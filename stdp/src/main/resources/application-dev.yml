flowable:
  #关闭定时任务JOB 
  async-executor-activate: false
  #将databaseSchemaUpdate设置为true。当Flowable发现库与数据库表结构不一致时，会自动将数据库表结构升级至新版本。
  database-schema-update: true
spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  datasource:
    url: *********************************************************************************************************************
    username: root
    password: ENC(DiuStr/wqEQSLSYqS4qrgh28YVGhSo+bELxvr6LVL7gNsJcm62NGpxflnLhsFlli)
    #driver-class-name: com.mysql.jdbc.Driver
    #其它配置
    #platform: mysql
    type: com.alibaba.druid.pool.DruidDataSource
    initialSize: 5
    minIdle: 5
    maxActive: 30
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 30000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    #目前有自定义的sql  wall 先不要
    filters: stat,slf4j
    #druid 用户名密码
    druidUsername: ipm
    druidPassword: ENC(kMRVt91sosHo1Du3S1pc99yeZX/3xuFnE23DccEu0lstmiOvgnenh56hfIDRMf3j)
    druidResetEnable: false
  #缓存配置
  redis:
    host: **************
    port: 6379
    password: ENC(x3rx1hPj7LmyEEJOMKQGEL80pf2sJxcdg48CovA0iZJrmXLwsMlpAJDGwSwoTnBR)
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms
mybatis:
  cacheDay: 2 #缓存有效时间 单位天
  configuration:
    cache-enabled: true #mybatis配置，开启二级缓存
    map-underscore-to-camel-case: true
  type-aliases-package: com.fd.stdp.beans
  mapper-locations: classpath:com/fd/stdp/dao/*/mapper/*.xml
mapper:
  mappers:
    - com.fd.stdp.common.mybatis.BaseInfoMapper
  not-empty: false
  identity: MYSQL
  IDENTITY: SELECT REPLACE(UUID(),"-","")
  # 写入之前
  ORDER: BEFORE
#project自定义配置
file:
  filePath: https://stdp.zjsis.com
  useFileType: LOCAL #文件上传模式 WEBDAV(webdav) LOCAL(本地)  ALIYUN(阿里云)
  temp:
    path: /home/<USER>/fd/files/backup/
  local:
    path: d:/localFile
    prefix: /statics
    urlPrefix: https://stdp.zjsis.com/api/file${file.local.prefix}
  aliyun:
    endpoint: xxx
    accessKeyId: xxx
    accessKeySecret: xxx
    bucketName: xxx
    domain: https://xxx
    savefolder:
  webdav:
    rootPath: http://**************:8001
    username: wjadmin
    password: ENC(VOKKQhJnX6dXqj/+aq/PrwLlRH1eWnCZ1aiVHnvtDbrJfKyxS8G5xcUO39R4Vi20)
token:
  expire:
    seconds: 28800
    app:
      seconds: 604800
  jwtSecret: (Fang:)_$^11244^%$_(Da:)_@@++--(Biao:)_++++_.sds_(Zhun:)
project:
  cors:
    list: #允许跨域访问域名列表
      - 'http://localhost:8080'
      - 'http://127.0.0.1:8080'
      - 'http://*************:8080'
      - 'http://*************:8081'
      - 'http://************:9999'
      - 'http://**************:8146'
      - 'http://**************:8146'
      - 'http://zjggjc.zjamr.zj.gov.cn:9090'
      - 'https://stdp.zjsis.com:443'
  log:
    #是否启用
    enabled: true
  #swagger程序文档功能
  swagger:
    #是否启用
    enabled: false
    #controller包位置
    base-package: com.fd.stdp.controller
    title: 浙江省方大标准信息技术有限公司模板项目
    description: 主要包含菜单字典权限日志文件等
    version: V1.0
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

stdp:
  mainPage:
    outTime: 3600000

interface:
  integratedPlatform:
    OSSLogin:
      #一体化平台 OSS登录成功后查询用户信息接口
      getUserInfoUrl: http://************:5012/sjzt/api/SSO/SSOLogin
      #一体化登录中转页
      transferPath: http://127.0.0.1:5506/supervison/pointPage/ssoCenter.html
      #一体化登录跳转页
      indexPath: http://127.0.0.1:5506/supervison/pointPage/itspSso.html
      appNameUnit: "jhzjzlzx"
      appName: ""


ythoauth:
  # 请求Api地址
  url: http://************:8066
  # 成功页面
  bindingPage: http://localhost:8081/ythLogin
  # 错误页面
  errPage: http://localhost:8081/ythLogin
  # appName
  appName: jhzjglxt

jasypt:
  encryptor:
    password: Stdp@2025!#$

# 脱敏配置
sensitive:
  decode:
    enabled: false
