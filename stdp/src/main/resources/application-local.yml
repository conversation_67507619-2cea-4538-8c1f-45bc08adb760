flowable:
  #关闭定时任务JOB
  async-executor-activate: false
  #将databaseSchemaUpdate设置为true。当Flowable发现库与数据库表结构不一致时，会自动将数据库表结构升级至新版本。
  database-schema-update: true
spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  datasource:
    url: *************************************************************************************************************************************************************************
    #    url: ********************************************************************************************************************************************************************
    username: develop
    password: E<PERSON>(T+1Ezhu/lJ/msC0ONf/Ep2vIwhaVCcQnjVLAiaT/MzWLCue2MGpiVOMlaY23tgG+)
    type: com.alibaba.druid.pool.DruidDataSource
    initialSize: 5
    minIdle: 5
    maxActive: 30
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 30000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    #目前有自定义的sql  wall 先不要
    filters: stat,slf4j
    #druid 用户名密码
    druidUsername: ipm
    druidPassword: ENC(57biUjAdl2robNG9qz0/EXS+p9EEI8yW4Qm9RKSB/iq/yVgju8OBtrWerATEWh1Z)
    druidResetEnable: false
  #缓存配置
  redis:
    host: **************
    port: 6378
    password: ENC(9URzKY3NQl5/Zrz/CTyImf1RQmIH1YBz6q1ZacRJ3KCKH7PAAQ/ncQPTP+k9ygPp)
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms
    database: 4
  jackson:
    time-zone: GMT+8
mybatis:
  cacheDay: 2 #缓存有效时间 单位天
  configuration:
    cache-enabled: true #mybatis配置，开启二级缓存
    map-underscore-to-camel-case: true
  type-aliases-package: com.fd.stdp.beans
  mapper-locations: classpath*:com/fd/stdp/dao/*/mapper/*.xml
mapper:
  mappers:
    - com.fd.stdp.common.mybatis.BaseInfoMapper
  not-empty: false
  identity: MYSQL
  IDENTITY: SELECT REPLACE(UUID(),"-","")
  # 写入之前
  ORDER: BEFORE

#project自定义配置
file:
  filePath: http://localhost:9007
  useFileType: ALIYUN #文件上传模式 WEBDAV(webdav) LOCAL(本地)  ALIYUN(阿里云)
  temp:
    path: d:/tempFile
  local:
    path: d:/localFile
    prefix: /statics
    urlPrefix: http://*************:9088/api/file${file.local.prefix}
  aliyun:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    accessKeyId: LTAI5t98gnEZtN2WZVFo5yjF
    accessKeySecret: ******************************
    bucketName: dev-oss-bucket-2025
    domain: https://jsb2022321.oss-cn-hangzhou.aliyuncs.com/
    savefolder: Kjsj/
  webdav:
    rootPath: http://**************:8001
    username: wjadmin
    password: ENC(CzMJDkGA9SjqyhZ+SFEScdqdd25LqXqqQivMOKCrTuyMmh6mmP4fIS+p/vBtXDet)
token:
  expire:
    seconds: 28800
    app:
      seconds: 604800
  jwtSecret: (Fang:)_$^11244^%$_(Da:)_@@++--(Biao:)_++++_.sds_(Zhun:)
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

project:
  cors:
    list: #允许跨域访问域名列表
      - 'http://localhost:8081'
      - 'http://127.0.0.1:8081'
      - 'http://127.0.0.1:8082'
      - 'http://*************:8081'
      - 'http://**************:8081'
      - 'http://**************:8084'
      - 'http://*************:8081'
      - 'http://*************:8081'
      - 'http://*************:8081'
      - 'http://*************:8081'
      - 'http://**************:8081'
      - 'http://*************:8081'
      - 'http://*************:8081'
      - 'http://*************:8081'
      - 'http://*************:8082'
      - 'http://*************:8081'
      - 'http://*************:8082'
      - 'http://*************:8081'
      - 'http://*************:8081'
  log:
    #是否启用
    enabled: true
  #swagger程序文档功能
  swagger:
    #是否启用
    enabled: false
    #controller包位置
    base-package: com.fd.stdp.controller
    title: 省市场监管科技管理数字化平台
    description: 主要包含菜单字典权限日志文件等
    version: V1.0

lims:
  openoffice:
    ip: **************
    port: 8100
jodconverter:
  enabled: true
  # 本地安装位置
  officeHome: C:\Program Files (x86)\OpenOffice 4
  #开启的端口 开的越多启动越慢，转换越稳定
  portNumbers: 8101 #, 8101, 8102, 8103, 8104, 8105, 8106, 8107, 8108, 8109
  #开启的连接池数
  maxTasksPerProcess: 10

stdp:
  mainPage:
    outTime: 3600000

interface:
  integratedPlatform:
    OSSLogin:
      #一体化平台 OSS登录成功后查询用户信息接口
      getUserInfoUrl: http://************:5012/sjzt/api/SSO/SSOLogin
      #一体化登录中转页
      transferPath: http://127.0.0.1:5506/supervison/pointPage/ssoCenter.html
      #一体化登录跳转页
      indexPath: http://127.0.0.1:5506/supervison/pointPage/itspSso.html
      appNameUnit: "jhzjzlzx"
      appName: ""

expert:
  url: http://*************:9090
#  url: http://**************:83
  #  url: https://exptest.pzcode.cn/api
  # url: https://zlzx.zjamr.zj.gov.cn/expmsapi
  sign:
    appId: KJC
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDk1qgE1FUGombwROjdtaXK1LDhOPCX2IYu+1jfMZUM3JsoBXz0rkM/NUMbmvSVtiN9JU53DRr0DXgxpaLDjmlmvf54hXrMi/lnBaiA8P89G0TSwKQzC1BwBTVKobzLmtNdXEMaMw44pV6uVQGXTLCJ9eYTqwl8FojeoD5cO0/+dwIDAQAB
    privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAOTWqATUVQaiZvBE6N21pcrUsOE48JfYhi77WN8xlQzcmygFfPSuQz81Qxua9JW2I30lTncNGvQNeDGlosOOaWa9/niFesyL+WcFqIDw/z0bRNLApDMLUHAFNUqhvMua011cQxozDjilXq5VAZdMsIn15hOrCXwWiN6gPlw7T/53AgMBAAECgYAggvrGDOCzm5kiY7ft3+PZKFSk+vD7sdCGlkEvHJ0/gYwtqHHzVVrKfSe2oVJEfucV3ZoDQgJlUBCWhSDVxcLliCwrIEc1gsvz9Adg6G4zJ/9nvjKKLYywRkF7P1jKi3FO159upk4P4M2UsUwEXXY7ICrrQXFm2EQEtCybVMDW6QJBAPMgZOWt1P1gqvJWEkbjmTcuT/3ULNg6hlJqu//OyaVbdKzD7jaTKsSOmUYhlwMIFee5XVFaUg1R/Mj3E2ijX/MCQQDw9JYZpX2sopytazTZXikRdzcwngd2OwHzMJMsgA+I3qXEIzIY4+mk8eoIZPIWZGN+KHBlDH/o955WRDDyrExtAkBM+j638dfz+X1miRX0V++U5xbwmRMxyyXoRl8L2V9C+bfxU3/Cy8nIvhWNfOkhTpgQ7EG9lklQalsNDh2ZDdE9AkEAoN0ZbIvQiDWT1i8xfEDhuBhYDywhnSwt1BbE2ruwI1dJYdWBd56I+KbM8Me723kv9yS2TK/4oSbXXY5vA0RGTQJAboZS7XmF9wmyIAGSABvR722jfbY3OjpDVBkaEFWdCxA77iLcmC98g0ZpC3V/avVi4brGhjms132PMv3q/UXNMw==
    deptCode: "03"
    domainCode: "06"
  # 个人用户默认配置
  default:
    password: ENC(tKxMVf6fIXcsf178iwC4zRkBstj/aleMsX3iqY0ysbd+aaPY6nsABAROuK6lIrun)
    roleCode: ROLE_PERSONAL
    userTypeCode: ROLE_PERSONAL
  # 企业用户默认配置
  org:
    password: ENC(tKxMVf6fIXcsf178iwC4zRkBstj/aleMsX3iqY0ysbd+aaPY6nsABAROuK6lIrun)
    roleCode: ORG_ADMIN
    userTypeCode: ORG_ADMIN
  # 监管用户默认配置
  supervise:
    password: ENC(tKxMVf6fIXcsf178iwC4zRkBstj/aleMsX3iqY0ysbd+aaPY6nsABAROuK6lIrun)
    roleCode: SUPERVISE
    userTypeCode: SUPERVISE
  # 专家用户默认配置
  zhuanjia:
    password: ENC(tKxMVf6fIXcsf178iwC4zRkBstj/aleMsX3iqY0ysbd+aaPY6nsABAROuK6lIrun)
    roleCode: EXPERT
    userTypeCode: EXPERT

ythoauth:
  # 请求Api地址
  #  url: http://************:8066
  url: https://zlzx.zjamr.zj.gov.cn/zsjapp/api/rest/ythAuth
  # 成功页面
  bindingPage: http://localhost:8081/ythSign
  # 错误页面
  errPage: http://localhost:8081/ythSign
  # appName
  #  appName: jhzjglxt
  appName: jhzjzlzx

# 通过质量在线进行政务网法人单点登录
zwwLogin:
  getLoginInfoUrl: https://api.zljweb.com/api/User/GetZwwUser

expertLogin:
  getLoginInfoUrl: http://**************:83/api/rest/sso/getUserInfo

jasypt:
  encryptor:
    password: Stdp@2025!#$

# 脱敏配置
sensitive:
  decode:
    enabled: true
