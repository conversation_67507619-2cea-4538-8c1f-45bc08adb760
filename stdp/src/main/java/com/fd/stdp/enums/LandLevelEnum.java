package com.fd.stdp.enums;

import org.springframework.util.StringUtils;

/**
 * 审批层级枚举
 * 省市县
 * 
 * <AUTHOR>
 * @date 2019年9月6日
 */
public enum LandLevelEnum {

    PROVINCE("PROVINCE", "省"),
    CITY("CITY", "市"),
    COUNTY("COUNTY", "县");

    String code;
    String name;
    
    public static String findCode(String code) {
        if (StringUtils.isEmpty(code)){
            return null;
        }
        for (LandLevelEnum enums : LandLevelEnum.values()) {
            if (enums.getCode().equals(code)) {
                return enums.getName();
            }
        }
        return null;
    }
    
    public String getCode() {
            return code;
    }
    public String getName() {
            return name;
    }
    
    LandLevelEnum(String code, String name) {
            
            this.code = code;
            this.name = name;
    }
}
