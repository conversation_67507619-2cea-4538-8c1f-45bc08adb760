package com.fd.stdp.enums;

/**
 * 项目申报流程状态枚举
 */
public enum ProjectApplyFlowStatus {

    WAIT_APPLY("项目待填报", "1"),
    ORG_AUDIT("申请单位审核", "2"),
    COUNTY_AUDIT("县局审核", "3"),
    CITY_AUDIT("市局审核", "4"),
    PROVINCE_AUDIT("省局审核", "5"),
    CHOOSE_EXPERTS("选择专家", "6"),
    EXPERTS_GRADE("专家打分", "7"),
    PROJECT_REVIEW("处室评议", "8"),
    PROJECT_RELEASE("项目下达", "9"),
    END("完成", "10");
    private String name;
    private String code;

    ProjectApplyFlowStatus(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }
}
