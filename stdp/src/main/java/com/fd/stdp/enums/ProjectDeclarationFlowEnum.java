package com.fd.stdp.enums;

import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.ProjectDeclarationFlowConstants;

/**
 * 项目申报流程枚举
 * <AUTHOR>
 *
 */
public enum ProjectDeclarationFlowEnum {

    APPLY_TO_ORG(ProjectDeclarationFlowConstants.APPLY_TASK_CODE, 1, "单位审核", 1),

    ORG_TO_COUNTY_CHECK(ProjectDeclarationFlowConstants.ORG_TASK_CODE, 1, "县级经办人审核", 1),
    ORG_TO_APPLY(ProjectDeclarationFlowConstants.ORG_TASK_CODE, 2, "退回申报人修改", 2),

    // 县级经办人审核
    COUNTY_CHECK_TO_COUNTY_APPROVAL(ProjectDeclarationFlowConstants.COUNTY_CHECK_TASK_CODE, 1, "县级分管领导审批", 1),
    COUNTY_CHECK_TO_ORG(ProjectDeclarationFlowConstants.COUNTY_CHECK_TASK_CODE, 2, "退回上单位审核", 2),
    COUNTY_CHECK_TO_APPLY(ProjectDeclarationFlowConstants.COUNTY_CHECK_TASK_CODE, 3, "退回申报人修改", 2),

    //县级分管领导审批
    COUNTY_APPROVAL_TO_CITY_CHECK(ProjectDeclarationFlowConstants.COUNTY_APPROVAL_TASK_CODE, 1, "市级经办人审核", 1),
    COUNTY_APPROVAL_TO_COUNTY_CHECK(ProjectDeclarationFlowConstants.COUNTY_APPROVAL_TASK_CODE, 2, "退回上一环节经办人", 2),
    COUNTY_APPROVAL_TO_APPLY(ProjectDeclarationFlowConstants.COUNTY_APPROVAL_TASK_CODE, 3, "退回申报人修改", 2),

    //市级经办人审核
    CITY_CHECK_TO_CITY_APPROVAL(ProjectDeclarationFlowConstants.CITY_CHECK_TASK_CODE, 1, "市级分管领导审批", 1),
    CITY_CHECK_TO_COUNTY_CHECK(ProjectDeclarationFlowConstants.CITY_CHECK_TASK_CODE, 2, "退回上一环节经办人", 2),
    CITY_CHECK_TO_APPLY(ProjectDeclarationFlowConstants.CITY_CHECK_TASK_CODE, 3, "退回申报人修改", 2),

    //市级分管领导审批
    CITY_APPROVAL_TO_PROVINCE_CHECK(ProjectDeclarationFlowConstants.CITY_APPROVAL_TASK_CODE, 1, "省级经办人审核", 1),
    CITY_APPROVAL_TO_CITY_CHECK(ProjectDeclarationFlowConstants.CITY_APPROVAL_TASK_CODE, 2, "退回上一环节经办人", 2),
    CITY_APPROVAL_TO_APPLY(ProjectDeclarationFlowConstants.CITY_APPROVAL_TASK_CODE, 3, "退回申报人修改", 2),

    // 省级经办人审核
    PROVINCE_CHECK_TO_PROVINCE_APPROVAL(ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_CODE, 1, "省级分管领导审批", 1),
    PROVINCE_CHECK_TO_CITY_CHECK(ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_CODE, 2, "退回上一环节经办人", 2),
    PROVINCE_CHECK_TO_APPLY(ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_CODE, 3, "退回申报人修改", 2),

    //处长审核
    DIRECTOR_AUDIT_TO_CHOOSE_EXPERT(ProjectDeclarationFlowConstants.DIRECTOR_AUDIT_TASK_CODE,1, "通过", 1),
    DIRECTOR_AUDIT_TO_PROVINCE_CHECK(ProjectDeclarationFlowConstants.DIRECTOR_AUDIT_TASK_CODE,2, "退回上一环节经办人", 2),
    DIRECTOR_AUDIT_TO_APPLY(ProjectDeclarationFlowConstants.DIRECTOR_AUDIT_TASK_CODE,3, "退回申报人修改", 2),

    //选择专家
    CHOOSE_EXPERT_TO_EXPERT_REVIEW(ProjectDeclarationFlowConstants.CHOOSE_EXPERT_TASK_CODE, 1, "专家评审", 1),

    //专家评审
    EXPERT_REVIEW_TO_DEPARTMENT_COMMENT(ProjectDeclarationFlowConstants.EXPERT_REVIEW_TASK_CODE, 1, "处室评议", 1),

    //处室评议
    DEPARTMENT_COMMENT_TO_DIRECTOR_AUDIT(ProjectDeclarationFlowConstants.DEPARTMENT_COMMENT_TASK_CODE, 1, "省级分管领导审批", 1),

    //省级分管领导审批
    PROVINCE_APPROVAL_TO_END(ProjectDeclarationFlowConstants.PROVINCE_APPROVAL_TASK_CODE, 1, "结束", 1),
    PROVINCE_APPROVAL_TO_DEPARTMENT_COMMENT(ProjectDeclarationFlowConstants.PROVINCE_APPROVAL_TASK_CODE, 2, "退回上一环节经办人", 2),
    PROVINCE_APPROVAL_TO_NOT_END(ProjectDeclarationFlowConstants.PROVINCE_APPROVAL_TASK_CODE, 3, "不通过", 2);




    /**
     * 节点名称
     */
    private final String node;

    /**
     * 操作类型
     */
    private final Integer code;
    /**
     * 操作类型名称
     */
    private final String value;
    /**
     * 意见类型：1 同意 2 驳回
     */
    private final Integer adviceType;

    public static ProjectDeclarationFlowEnum getFlowEnum(String node, int code) {
        for (ProjectDeclarationFlowEnum value : ProjectDeclarationFlowEnum.values()) {
            if (value.getNode().equals(node) && value.getCode().equals(code)) {
                return value;
            }
        }
        throw new ServiceException("操作类型不存在");
    }

    public String getNode() {
        return node;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public Integer getAdviceType() {
        return adviceType;
    }

    ProjectDeclarationFlowEnum(String node, Integer code, String value, Integer adviceType) {
        this.node = node;
        this.code = code;
        this.value = value;
        this.adviceType = adviceType;
    }

}
