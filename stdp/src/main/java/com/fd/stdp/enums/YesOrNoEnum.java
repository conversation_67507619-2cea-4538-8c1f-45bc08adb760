package com.fd.stdp.enums;

/**
 * <AUTHOR>
 * @date 2020/08/19
 */
public enum YesOrNoEnum {
    YES(1, "是"), NO(0, "否");

    private Integer key;
    private String value;

    public Integer getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    /**
     * @param key
     * @param value
     */
    private YesOrNoEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

}
