package com.fd.stdp.enums;

/**
 * 项目子类别
 */
public enum ProjectSecondTypeEnum {

    CY_01("01", "揭榜挂帅项目"),
    CY_02("02", "重大合作项目"),
    CY_03("03", "核心项目"),
    CY_04("04", "初级项目"),
    YB<PERSON><PERSON>KY_ZICHOUPROJECT("ZICHOUPROJECT", "自筹项目"),
    YBKYKY_QINGNIANKEJIPROJECT("QINGNIANKEJIPROJECT", "青年科技专项"),
    YBKYKY_CHENGGUOZHUANHUAPROJECT("CHENGG<PERSON><PERSON>ZHUANHUAPROJECT", "成果转化专项"),
    YBKYKY_NQIKEYANPROJECT("NQIKEYANPROJECT", "NQI科研项目"),
    ZCZB_JIANYANJIANCEPROJECT("JIANYANJIANCEPROJECT", "检验检测装备项目"),
    ZCZB_NQIZHUANGBEIPROJECT("NQIZHUANGBEIPROJECT", "NQI装备项目");

    String value;
    String code;

    ProjectSecondTypeEnum(String code, String value) {
        this.value = value;
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }
}
