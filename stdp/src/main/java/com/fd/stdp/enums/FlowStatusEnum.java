package com.fd.stdp.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/1/4 14:29
 */
public enum FlowStatusEnum {
    WAIT_APPLY("待填报", "1", "ORG_HEAD@ROLE", "项目填报"),
    ORG_AUDIT("待申请单位审核", "2", "ORG_ADMIN@ROLE", "单位审核"),
    COUNTY_AUDIT("待县局审核", "3", "DEPT_COUNTY@ROLE", "县局审核"),
    CITY_AUDIT("待市局审核", "4", "DEPT_CITY@ROLE", "市局审核"),
    PROVINCE_AUDIT("待省局审核", "5", "DEPT_PROVINCE@ROLE", "省局审核"),
    CHOOSE_EXPERTS("待选择专家", "6", "DEPT_PROVINCE@ROLE", "选择专家"),
    EXPERTS_GRADE("待专家打分", "7", "EXPERT@ROLE", "评审打分"),
    PROJECT_REVIEW("待处室评议", "8", "DEPT_PROVINCE@ROLE", "处室评议"),
    PROJECT_RELEASE("项目待下达", "9", "DEPT_PROVINCE@ROLE", "项目下达"),
    CONTRACT_T_RELEASE("任务书待下达", "10", "DEPT_PROVINCE@ROLE", "任务书下达"),
    CONTRACT_T_UPLOAD("任务书待上传", "11", "ORG_HEAD@ROLE", "任务书上传"),
    PROVINCE_TECH_AUDIT("待省局技术审核", "50", "TEC_ORG_AGENT@ROLE", "省局技术审核"),
    END("完成", "999", "", "结束"),
    JUST_END("完成并未进行下一步", "1000", "", "结束"),
    UPLOAD_END("任务书上传完成", "1001", "", "结束"),
    OUT_END("外部项目", "1002", "", "结束"),
    CANCEL("终止", "9999", "", "中止");
    private String code;
    private String name;
    private String role;
    private String flowName;

    FlowStatusEnum(String name, String code, String role, String flowName) {
        this.code = code;
        this.name = name;
        this.role = role;
        this.flowName = flowName;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getRole() {
        return role;
    }

    public String getFlowName() {
        return flowName;
    }
}
