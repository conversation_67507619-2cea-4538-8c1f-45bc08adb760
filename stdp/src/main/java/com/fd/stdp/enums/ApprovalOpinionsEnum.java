package com.fd.stdp.enums;

/**
 * 审批意见
 * 同意，拒绝
 * <AUTHOR>
 * @date 2019年9月6日
 */
public enum ApprovalOpinionsEnum {
    /**
     * 同意
     */
    AGREE("AGREE", "同意"),
    /**
     * 拒绝
     */
    REFUSE("REFUSE", "拒绝");

    String code;
    String name;
    
    public String getCode() {
            return code;
    }
    public String getName() {
            return name;
    }
    ApprovalOpinionsEnum(String code, String name) {
            
            this.code = code;
            this.name = name;
    }
}
