package com.fd.stdp.enums;

/**
 * 项目类别
 */
public enum ProjectTypeEnum {
    CY("CY", "雏鹰计划项目"),
    YBKYKY("YBKYKY", "一般科研项目"),
    ZCZB("ZCZB", "装备项目"),

    J<PERSON>("PROJECT_APP_NEW_MAJOR_JIANFEN", "尖峰项目"),
    J<PERSON>("PROJECT_APP_NEW_MAJOR_JIANBING", "尖兵项目"),
    LY("PROJECT_APP_NEW_GENERAL_KEYAN_LINYAN", "领雁项目");

    String value;
    String code;

    ProjectTypeEnum(String code, String value) {
        this.value = value;
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public String getCode() {
        return code;
    }
}
