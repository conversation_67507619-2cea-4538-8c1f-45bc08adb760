package com.fd.stdp.enums;

/** 
 * 名称：枚举类型
 * 创建人：linqiang
 * 更新时间：2018年1月31日
 * @version
 */
public enum BpmsActivityTypeEnum {

	START_EVENT("startEvent", "开始事件"),
	END_EVENT("endEvent", "结束事件"),
	USER_TASK("userTask", "用户任务"),
	EXCLUSIVE_GATEWAY("exclusiveGateway", "排他网关"),
	PARALLEL_GATEWAY("parallelGateway", "并行网关"),
	INCLUSIVE_GATEWAY("inclusiveGateway","包含网关");

	private String type;
	private String name;

	private BpmsActivityTypeEnum(String type, String name) {
		this.type = type;
		this.name = name;
	}

	public String getType() {
		return type;
	}
	public String getName() {
		return name;
	}
}