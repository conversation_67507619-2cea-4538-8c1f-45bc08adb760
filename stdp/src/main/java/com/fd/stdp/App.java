package com.fd.stdp;

import com.fd.stdp.util.expertExtract.EnableExpertSignClient;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import tk.mybatis.spring.annotation.MapperScan;

/**
 * Hello world!
 *
 */
@EnableFeignClients
@EnableExpertSignClient
@SpringBootApplication(exclude = {SecurityAutoConfiguration.class })
@EnableTransactionManagement
@MapperScan("com.fd.stdp.dao")
public class App {

	public static void main(String[] args) {
		SpringApplication.run(App.class, args);
		System.err.println("启动成功");
	}
}
