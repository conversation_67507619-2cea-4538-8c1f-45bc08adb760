package com.fd.stdp.process.projectApply;

import com.fd.stdp.beans.flow.FlowNodeVo;
import com.fd.stdp.beans.project.ProjectApplyInfo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.constant.ProjectDeclarationFlowConstants;
import com.fd.stdp.dao.project.ProjectApplyInfoMapper;
import com.fd.stdp.enums.ProjectDeclarationFlowEnum;
import com.fd.stdp.service.project.ProjectApplyInfoService;
import com.fd.stdp.util.AssertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description 项目申请流程
 * @Date 2025/7/8 19:31
 * <AUTHOR>
 **/
@Component(FlowableConstant.TECK_APPLY_FLOW + "task")
public class FlowNodeTask extends ParentNode {

    @Autowired
    ProjectApplyInfoMapper projectApplyInfoMapper;


    @Override
    public void beforeStartTask(ProjectApplyInfoVo apply) {
        //设备办件地区行政区划
        apply.setAcceptAreaCode(apply.getApplyUnitPostCode());
        apply.setAssignerUser(apply.getUserId());
    }

    @Override
    protected void endStartTask(ProjectApplyInfoVo apply) {
        System.err.println("流程受理完成·");
    }


    @Override
    protected void endNextTask(ProjectApplyInfoVo apply, FlowNodeVo object) {
        super.endNextTask(apply, object);
        System.err.println(apply.getActNodeName() + "审核完成·");
    }

    /**
     * @Description 项目申报
     * @Date 2025/7/9
     * <AUTHOR>
     **/
    @Component(FlowableConstant.TECK_APPLY_FLOW + ProjectDeclarationFlowConstants.APPLY_TASK_CODE)
    public class PrincipalTask extends ParentNode {

        @Override
        public void beforeNextTask(ProjectApplyInfoVo apply) {
            //单位审核用户ID
            apply.setAssignerGroup(ProjectDeclarationFlowConstants.ORG_TASK_ROLE);
        }
    }


    /**
     * @Description 单位审核
     * @Date 2025/7/9
     * <AUTHOR>
     **/
    @Component(FlowableConstant.TECK_APPLY_FLOW + ProjectDeclarationFlowConstants.ORG_TASK_CODE)
    public class OrganizationTask extends ParentNode {
        @Override
        public void beforeNextTask(ProjectApplyInfoVo apply) {
            ProjectApplyInfo projectApplyInfo = projectApplyInfoMapper.selectByPrimaryKey(apply.getId());
            AssertUtil.notNull(projectApplyInfo, "数据异常");

            //根据申报单位获取审批层级
            if (projectApplyInfo.getApplyUnitPostCode().endsWith("0000")) {
                apply.setAcceptAreaLevel("3");
                apply.setAssignerGroup(ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_ROLE);
            } else if (projectApplyInfo.getApplyUnitPostCode().endsWith("00")) {
                apply.setAcceptAreaLevel("2");
                apply.setAssignerGroup(ProjectDeclarationFlowConstants.CITY_APPROVAL_TASK_ROLE);
            } else {
                apply.setAcceptAreaLevel("1");
                apply.setAssignerGroup(ProjectDeclarationFlowConstants.COUNTY_CHECK_TASK_ROLE);
            }

            //退回申报人
            if (!"1".equals(apply.getFlowResult())) {
                apply.setTargetKey(ProjectDeclarationFlowConstants.APPLY_TASK_CODE);
                apply.setAssignerUser(projectApplyInfo.getUserId());
            }
            super.beforeNextTask(apply);
        }
    }

    /**
     * @Description 县级经办人审核
     * @Date 2025/7/9
     * <AUTHOR>
     **/
    @Component(FlowableConstant.TECK_APPLY_FLOW + ProjectDeclarationFlowConstants.COUNTY_CHECK_TASK_CODE)
    public class CountyCheckTask extends ParentNode {
        @Override
        public void beforeNextTask(ProjectApplyInfoVo apply) {
            ProjectApplyInfo projectApplyInfo = projectApplyInfoMapper.selectByPrimaryKey(apply.getId());
            AssertUtil.notNull(projectApplyInfo, "数据异常");

            apply.setAssignerGroup(ProjectDeclarationFlowConstants.COUNTY_APPROVAL_TASK_ROLE);

            if ("2".equals(apply.getFlowResult())) {
                //退回上一步（单位审核）
                apply.setTargetKey(ProjectDeclarationFlowConstants.ORG_TASK_CODE);
                apply.setAssignerGroup(ProjectDeclarationFlowConstants.ORG_TASK_ROLE);
            } else if ("3".equals(apply.getFlowResult())) {
                //退回申报人
                apply.setTargetKey(ProjectDeclarationFlowConstants.APPLY_TASK_CODE);
                apply.setAssignerUser(projectApplyInfo.getUserId());
            }
            super.beforeNextTask(apply);
        }
    }

    /**
     * @Description 县级分管领导审批
     * @Date 2025/7/9
     * <AUTHOR>
     **/
    @Component(FlowableConstant.TECK_APPLY_FLOW + ProjectDeclarationFlowConstants.COUNTY_APPROVAL_TASK_CODE)
    public class CountyApprovalTask extends ParentNode {
        @Override
        public void beforeNextTask(ProjectApplyInfoVo apply) {
            ProjectApplyInfo projectApplyInfo = projectApplyInfoMapper.selectByPrimaryKey(apply.getId());
            AssertUtil.notNull(projectApplyInfo, "数据异常");

            apply.setAssignerGroup(ProjectDeclarationFlowConstants.CITY_CHECK_TASK_ROLE);

            if ("2".equals(apply.getFlowResult())) {
                //退回上一步
                apply.setTargetKey(ProjectDeclarationFlowConstants.COUNTY_CHECK_TASK_CODE);
                apply.setAssignerGroup(ProjectDeclarationFlowConstants.COUNTY_CHECK_TASK_ROLE);
            } else if ("3".equals(apply.getFlowResult())) {
                //退回申报人
                apply.setTargetKey(ProjectDeclarationFlowConstants.APPLY_TASK_CODE);
                apply.setAssignerUser(projectApplyInfo.getUserId());
            }
            super.beforeNextTask(apply);
        }
    }


    /**
     * @Description 市级经办人审核
     * @Date 2025/7/9
     * <AUTHOR>
     **/
    @Component(FlowableConstant.TECK_APPLY_FLOW + ProjectDeclarationFlowConstants.CITY_CHECK_TASK_CODE)
    public class CityCheckTask extends ParentNode {
        @Override
        public void beforeNextTask(ProjectApplyInfoVo apply) {
            ProjectApplyInfo projectApplyInfo = projectApplyInfoMapper.selectByPrimaryKey(apply.getId());
            AssertUtil.notNull(projectApplyInfo, "数据异常");

            apply.setAssignerGroup(ProjectDeclarationFlowConstants.CITY_APPROVAL_TASK_ROLE);

            if ("2".equals(apply.getFlowResult())) {
                //市级项目退回单位审核
                if (projectApplyInfo.getApplyUnitPostCode().endsWith("00")) {
                    apply.setTargetKey(ProjectDeclarationFlowConstants.ORG_TASK_CODE);
                    apply.setAssignerGroup(ProjectDeclarationFlowConstants.ORG_TASK_ROLE);
                } else {
                    apply.setTargetKey(ProjectDeclarationFlowConstants.COUNTY_CHECK_TASK_CODE);
                    apply.setAssignerGroup(ProjectDeclarationFlowConstants.COUNTY_CHECK_TASK_ROLE);
                }

            } else if ("3".equals(apply.getFlowResult())) {
                //退回申报人
                apply.setTargetKey(ProjectDeclarationFlowConstants.APPLY_TASK_CODE);
                apply.setAssignerUser(projectApplyInfo.getUserId());
            }
            super.beforeNextTask(apply);
        }
    }


    /**
     * @Description 市级分管领导审批
     * @Date 2025/7/9
     * <AUTHOR>
     **/
    @Component(FlowableConstant.TECK_APPLY_FLOW + ProjectDeclarationFlowConstants.CITY_APPROVAL_TASK_CODE)
    public class CityApprovalTask extends ParentNode {
        @Override
        public void beforeNextTask(ProjectApplyInfoVo apply) {
            ProjectApplyInfo projectApplyInfo = projectApplyInfoMapper.selectByPrimaryKey(apply.getId());
            AssertUtil.notNull(projectApplyInfo, "数据异常");

            apply.setAssignerGroup(ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_ROLE);

            if ("2".equals(apply.getFlowResult())) {
                //退回上一步（市级经办人审核）
                apply.setTargetKey(ProjectDeclarationFlowConstants.CITY_CHECK_TASK_CODE);
                apply.setAssignerGroup(ProjectDeclarationFlowConstants.CITY_CHECK_TASK_ROLE);
            } else if ("3".equals(apply.getFlowResult())) {
                //退回申报人
                apply.setTargetKey(ProjectDeclarationFlowConstants.APPLY_TASK_CODE);
                apply.setAssignerUser(projectApplyInfo.getUserId());
            }
            super.beforeNextTask(apply);
        }
    }


    /**
     * @Description 省局经办人审核
     * @Date 2025/7/9
     * <AUTHOR>
     **/
    @Component(FlowableConstant.TECK_APPLY_FLOW + ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_CODE)
    public class ProvinceCheckTask extends ParentNode {
        @Override
        public void beforeNextTask(ProjectApplyInfoVo apply) {
            ProjectApplyInfo projectApplyInfo = projectApplyInfoMapper.selectByPrimaryKey(apply.getId());
            AssertUtil.notNull(projectApplyInfo, "数据异常");

            apply.setAssignerGroup(ProjectDeclarationFlowConstants.DIRECTOR_AUDIT_TASK_ROLE);

            if ("2".equals(apply.getFlowResult())) {
               //省级项目退回单位审核
                if (projectApplyInfo.getApplyUnitPostCode().endsWith("0000")) {
                    apply.setTargetKey(ProjectDeclarationFlowConstants.ORG_TASK_CODE);
                    apply.setAssignerGroup(ProjectDeclarationFlowConstants.ORG_TASK_ROLE);
                } else {
                    //退回上一环节经办人
                    apply.setTargetKey(ProjectDeclarationFlowConstants.CITY_CHECK_TASK_CODE);
                    apply.setAssignerGroup(ProjectDeclarationFlowConstants.CITY_CHECK_TASK_ROLE);
                }
            } else if ("3".equals(apply.getFlowResult())) {
                //退回申报人
                apply.setTargetKey(ProjectDeclarationFlowConstants.APPLY_TASK_CODE);
                apply.setAssignerUser(projectApplyInfo.getUserId());
            }
            super.beforeNextTask(apply);
        }
    }


    /**
     * @Description 处长审核
     * @Date 2025/7/9
     * <AUTHOR>
     **/
    @Component(FlowableConstant.TECK_APPLY_FLOW + ProjectDeclarationFlowConstants.DIRECTOR_AUDIT_TASK_CODE)
    public class directorAuditTask extends ParentNode {
        @Override
        public void beforeNextTask(ProjectApplyInfoVo apply) {
            ProjectApplyInfo projectApplyInfo = projectApplyInfoMapper.selectByPrimaryKey(apply.getId());
            AssertUtil.notNull(projectApplyInfo, "数据异常");

            //省局经办人选择专家
            apply.setAssignerGroup(ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_ROLE);

            if ("2".equals(apply.getFlowResult())) {
                //退回上一步（
                apply.setTargetKey(ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_CODE);
                apply.setAssignerGroup(ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_ROLE);
            } else if ("3".equals(apply.getFlowResult())) {
                //退回申报人
                apply.setTargetKey(ProjectDeclarationFlowConstants.APPLY_TASK_CODE);
                apply.setAssignerUser(projectApplyInfo.getUserId());
            }
            super.beforeNextTask(apply);
        }
    }


    /**
     * @Description 选择专家
     * @Date 2025/7/24
     * <AUTHOR>
     **/
    @Component(FlowableConstant.TECK_APPLY_FLOW + ProjectDeclarationFlowConstants.CHOOSE_EXPERT_TASK_CODE)
    public class chooseExpertTask extends ParentNode {
        @Override
        public void beforeNextTask(ProjectApplyInfoVo apply) {
            ProjectApplyInfo projectApplyInfo = projectApplyInfoMapper.selectByPrimaryKey(apply.getId());
            AssertUtil.notNull(projectApplyInfo, "数据异常");

            //专家评审
            apply.setAssignerGroup(ProjectDeclarationFlowConstants.EXPERT_REVIEW_TASK_ROLE);


            super.beforeNextTask(apply);
        }
    }

    /**
     * @Description 专家评审
     * @Date 2025/7/24
     * <AUTHOR>
     **/
    @Component(FlowableConstant.TECK_APPLY_FLOW + ProjectDeclarationFlowConstants.EXPERT_REVIEW_TASK_CODE)
    public class expertReviewTask extends ParentNode {
        @Override
        public void beforeNextTask(ProjectApplyInfoVo apply) {
            ProjectApplyInfo projectApplyInfo = projectApplyInfoMapper.selectByPrimaryKey(apply.getId());
            AssertUtil.notNull(projectApplyInfo, "数据异常");

            //处室评议（经办人处理该节点）
            apply.setAssignerGroup(ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_ROLE);

            super.beforeNextTask(apply);
        }
    }


    /**
     * @Description 处室评议
     * @Date 2025/7/24
     * <AUTHOR>
     **/
    @Component(FlowableConstant.TECK_APPLY_FLOW + ProjectDeclarationFlowConstants.DEPARTMENT_COMMENT_TASK_CODE)
    public class departmentCommentTask extends ParentNode {
        @Override
        public void beforeNextTask(ProjectApplyInfoVo apply) {
            ProjectApplyInfo projectApplyInfo = projectApplyInfoMapper.selectByPrimaryKey(apply.getId());
            AssertUtil.notNull(projectApplyInfo, "数据异常");

            //省局分管领导
            apply.setAssignerGroup(ProjectDeclarationFlowConstants.PROVINCE_APPROVAL_TASK_ROLE);

            super.beforeNextTask(apply);
        }
    }

}
