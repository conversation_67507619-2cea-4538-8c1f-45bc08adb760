package com.fd.stdp.process;

import com.fd.stdp.beans.flow.FlowNodeVo;
import com.fd.stdp.common.exception.ServiceException;

/**
 * @Description flowable 流程节点
 * @Date 2023-03-20 9:13
 * <AUTHOR>
 **/
public abstract class ProcessNode<T> {

    // 启动前
    protected abstract void beforeStartTask(T apply);

    // 启动
    protected abstract void startTask(T apply);

    // 启动后
    protected abstract void endStartTask(T apply);

    // 下一个节点前 这可以初始化对象
    protected abstract void beforeNextTask(T apply);

    // 下一个节点 执行flowable任务
    protected abstract FlowNodeVo nextTask(T apply) throws Exception;

    // 下一个节点后 这里可以做更新数据库
    protected abstract void endNextTask(T apply, FlowNodeVo object);

    /**
     * 启动节点
     *
     * @param apply
     */
    public void start(T apply) {
        beforeStartTask(apply);
        startTask(apply);
        endStartTask(apply);
    }

    /**
     * 下一个节点
     *
     * @param apply
     */
    public FlowNodeVo next(T apply) {
        beforeNextTask(apply);
        FlowNodeVo object = null;
        try {
            object = nextTask(apply);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        endNextTask(apply, object);
        return object;
    }

}
