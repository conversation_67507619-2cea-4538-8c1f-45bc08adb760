package com.fd.stdp.process.projectApply;

import com.fd.stdp.beans.flow.FlowNodeVo;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.beans.sys.SysRole;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.dao.project.ProjectApplyInfoMapper;
import com.fd.stdp.enums.ProjectDeclarationFlowEnum;
import com.fd.stdp.process.ProcessNode;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.flowable.FlowTaskService;
import com.fd.stdp.service.sys.RoleService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;


public abstract class ParentNode extends ProcessNode<ProjectApplyInfoVo> {

    private static final Logger logger = LoggerFactory.getLogger(ParentNode.class);

    @Autowired
    FlowApiService flowApiService;

    @Autowired
    FlowTaskService flowTaskService;

    @Autowired
    ProjectApplyInfoMapper projectApplyInfoMapper;

    @Autowired
    RoleService roleService;


    @Override
    public void beforeNextTask(ProjectApplyInfoVo apply) {

    }

    @Override
    protected void endNextTask(ProjectApplyInfoVo apply, FlowNodeVo object) {
        updateFlowStatus(apply, object);
    }


    @Override
    public void beforeStartTask(ProjectApplyInfoVo apply) {

    }

    @Override
    protected FlowNodeVo nextTask(ProjectApplyInfoVo apply) throws Exception {
        FlowTaskVo flowTaskVo = initFlowTaskVo(apply);
        return complete(flowTaskVo);
    }

    @Override
    public void startTask(ProjectApplyInfoVo apply) {
        apply.setAssigner(apply.getAssigner());
        FlowTaskVo taskVo = initFlowTaskVo(apply);
        try {
            // 开启 flowable 流程
            FlowNodeVo flowNodeVo = flowApiService.startProcessWithNext(taskVo, BaseController.getCurrentUserId());
            if (flowNodeVo != null) {
                // 更新流程
                apply.setAssigner(BaseController.getCurrentUserName());
                // 设置流程状态
                apply.setNextAssigner(flowNodeVo.getAssignee());
                apply.setActNodeKey(flowNodeVo.getTaskDefinitionKey());
                apply.setActNodeName(flowNodeVo.getName());
                apply.setFlowStatusTxt(flowNodeVo.getDescription());
                projectApplyInfoMapper.updateByPrimaryKeySelective(apply);
            }
        } catch (Throwable throwable) {
            throwable.printStackTrace();
            throw new ServiceException(throwable.getMessage());
        }
    }

    @Override
    protected void endStartTask(ProjectApplyInfoVo apply) {

    }

    /**
     * 初始化提交对象
     *
     * @param apply
     * @return
     */
    protected FlowTaskVo initFlowTaskVo(ProjectApplyInfoVo apply) {
        FlowTaskVo taskVo = new FlowTaskVo();
        taskVo.setBusinessKey(apply.getId());
        taskVo.setAssignee(apply.getAssigner());
        taskVo.setAcceptAreaCode(apply.getAcceptAreaCode());
        if (StringUtils.isNotBlank(apply.getTargetKey())) {
            taskVo.setTargetKey(apply.getTargetKey());
        }
        taskVo.setProcessInstanceKey(FlowableConstant.TECK_APPLY_FLOW);
        String advice = apply.getAdvice();
        if (StringUtils.isNotBlank(apply.getActNodeKey()) && StringUtils.isNotBlank(apply.getFlowResult())) {
            ProjectDeclarationFlowEnum flowEnum = ProjectDeclarationFlowEnum.getFlowEnum(apply.getActNodeKey(), Integer.parseInt(apply.getFlowResult()));
            advice = com.fd.stdp.util.StringUtils.format("【{}】{}", flowEnum.getValue(), StringUtils.stripToEmpty(advice));
            taskVo.setCommentType(flowEnum.getAdviceType());
        }
        if (StringUtils.isNotBlank(advice)) {
            taskVo.setComment(advice);
        }
        if (StringUtils.isNotBlank(apply.getAdvice())) {
            taskVo.setComment(apply.getAdvice());
        } else {
            taskVo.setComment("");
        }
        Map<String, Object> variables = initVariables(apply);
        if (StringUtils.isNotBlank(apply.getAssignerUser())) {
            variables.put("assignerUser", apply.getAssignerUser());
        }
        if (StringUtils.isNotBlank(apply.getAssignerGroup())) {
            SysRole roleByRoleCode = roleService.findRoleByRoleCode(apply.getAssignerGroup());
            if(roleByRoleCode == null){
                throw new ServiceException("角色不存在");
            }
            variables.put("assignerGroup", roleByRoleCode.getId());
        }
        if (StringUtils.isNotBlank(apply.getAcceptAreaLevel())) {
            variables.put("acceptAreaLevel", apply.getAcceptAreaLevel());
        }
        taskVo.setValues(variables);
        return taskVo;
    }

    /**
     * 初始化提交备注
     *
     * @param apply
     * @return
     */
    protected Map<String, Object> initVariables(ProjectApplyInfoVo apply) {
        Map<String, Object> variables = new HashMap<>();
        return variables;
    }


    /**
     * 提交
     *
     * @param flowTaskVo
     * @return
     */
    protected FlowNodeVo complete(FlowTaskVo flowTaskVo) {
        FlowNodeVo res = flowTaskService.complete2(flowTaskVo, BaseController.getCurrentUserId());
        return res;
    }

    /**
     * 更新FlowStatus
     */
    protected void updateFlowStatus(ProjectApplyInfoVo applyInfoVo, FlowNodeVo json) {
        ProjectApplyInfoVo record = new ProjectApplyInfoVo();
        record.setId(applyInfoVo.getId());
        if (StringUtils.isNotBlank(json.getAssignee())) {
            record.setNextAssigner(json.getAssignee());
        }
        record.setAssigner(BaseController.getCurrentUserName());
        // 设置流程状态
        record.setActNodeKey(json.getTaskDefinitionKey());
        record.setActNodeName(json.getName());
        record.setFlowStatusTxt(json.getDescription());
        projectApplyInfoMapper.updateByPrimaryKeySelective(record);

    }
}
