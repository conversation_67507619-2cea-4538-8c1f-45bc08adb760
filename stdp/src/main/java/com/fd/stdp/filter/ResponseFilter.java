package com.fd.stdp.filter;

import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.DecryptField;
import com.fd.stdp.util.DesTypeEnum;
import com.fd.stdp.util.DesensitizedUtils;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

/**
 * 响应数据脱敏过滤器
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@ControllerAdvice
public class ResponseFilter implements ResponseBodyAdvice<Object> {
    
    @Value("${spring.profiles.active:dev}")
    private String active;

    @Override
    public boolean supports(MethodParameter methodParameter, Class<? extends HttpMessageConverter<?>> aClass) {
        return true;
    }

    /**
     * @Description: 该方法是拦截到返回值（即response中的数据），然后操作返回值，并返回
     **/
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter methodParameter, MediaType mediaType, 
                                  Class<? extends HttpMessageConverter<?>> aClass, 
                                  ServerHttpRequest serverHttpRequest, 
                                  ServerHttpResponse serverHttpResponse) {

        // 生产环境可以添加额外的加密逻辑
        // if ("prod".equals(active) && body instanceof RestApiResponse) {
        //     // 加密逻辑
        // }

        if (body instanceof RestApiResponse) {
            RestApiResponse result = (RestApiResponse) body;
            try {
                deepProcess(result.getData());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return body;
    }

    /**
     * 数据脱敏方法
     * @param obj 需要处理的对象
     * @throws Exception 处理异常
     */
    public void deepProcess(Object obj) throws Exception {
        if (obj != null) {
            if (obj instanceof Collection) {
                for (Object o : (Collection) obj) {
                    tuoMing(o);
                }
            } else if (obj instanceof PageInfo) {
                for (Object o : ((PageInfo) obj).getList()) {
                    tuoMing(o);
                }
            } else {
                tuoMing(obj);
            }
        }
    }

    /**
     * 对单个对象进行脱敏处理
     * @param obj 需要处理的对象
     * @throws Exception 处理异常
     */
    private void tuoMing(Object obj) throws Exception {
        if (obj == null) {
            return;
        }
        
        // 跳过基本类型、包装类型和系统类
        if (isSkippableType(obj.getClass())) {
            return;
        }
        
        // 处理Map类型
        if (obj instanceof Map) {
            processMap((Map<?, ?>) obj);
            return;
        }
        
        // 取出输出对象的所有字段属性，并遍历
        List<Field> fieldList = getFieldList(obj.getClass());
        for (Field declaredField : fieldList) {
            // 判断字段属性上是否标记DecryptField注解
            if (declaredField.isAnnotationPresent(DecryptField.class)) {
                // 如果判断结果为真，则取出字段属性数据进行解密处理
                declaredField.setAccessible(true);
                Object valObj = declaredField.get(obj);
                if (valObj != null && valObj instanceof String) {
                    DecryptField annotation = declaredField.getAnnotation(DecryptField.class);
                    String value = valObj.toString();
                    
                    // 加密数据的解密处理
                    if (annotation.isEncryptField()) {
                        try {
                            value = decrypt(value);
                        } catch (Exception e) {
                            // 解密失败，保留原值
                            e.printStackTrace();
                        }
                    }
                    
                    // 如果需要脱敏处理
                    if (annotation.isDes()) {
                        value = desensitization(value, annotation.desType());
                    }
                    
                    // 把处理后的数据重新赋值
                    declaredField.set(obj, value);
                }
            }
        }
    }
    
    /**
     * 处理Map类型数据
     * @param map Map对象
     * @throws Exception 处理异常
     */
    private void processMap(Map<?, ?> map) throws Exception {
        // Map类型无法直接修改值，这里只是记录一下
        // 如果需要处理Map中的敏感数据，可以在这里实现
    }
    
    /**
     * 判断是否为需要跳过处理的类型
     * @param clazz 类型
     * @return 是否跳过
     */
    private boolean isSkippableType(Class<?> clazz) {
        if (clazz == null) {
            return true;
        }
        
        // 基本类型和包装类型
        if (clazz.isPrimitive() || 
            clazz == String.class ||
            clazz == Boolean.class ||
            clazz == Character.class ||
            clazz == Byte.class ||
            clazz == Short.class ||
            clazz == Integer.class ||
            clazz == Long.class ||
            clazz == Float.class ||
            clazz == Double.class) {
            return true;
        }
        
        // 系统类
        String packageName = clazz.getPackage() != null ? clazz.getPackage().getName() : "";
        return packageName.startsWith("java.") || 
               packageName.startsWith("javax.") || 
               packageName.startsWith("sun.") ||
               packageName.startsWith("com.sun.") ||
               packageName.startsWith("org.springframework.");
    }

    /**
     * 根据脱敏类型进行脱敏处理
     * @param value 需要脱敏的值
     * @param desTypeEnum 脱敏类型
     * @return 脱敏后的值
     */
    public String desensitization(String value, DesTypeEnum desTypeEnum) {
        String result = "";
        if (StringUtils.isNotBlank(value) && null != desTypeEnum) {
            switch (desTypeEnum) {
                case CHINESE_NAME:
                    result = DesensitizedUtils.chineseName(value);
                    break;
                case ID_CARD:
                    result = DesensitizedUtils.idCardNum(value);
                    break;
                case FIXED_PHONE:
                    result = DesensitizedUtils.fixedPhone(value);
                    break;
                case MOBILE_PHONE:
                    result = DesensitizedUtils.mobilePhone(value);
                    break;
                case ADDRESS:
                    result = DesensitizedUtils.address(value);
                    break;
                case EMAIL:
                    result = DesensitizedUtils.email(value);
                    break;
                case BANK_CARD:
                    result = DesensitizedUtils.bankCard(value);
                    break;
                case PASSWORD:
                    result = DesensitizedUtils.password(value);
                    break;
                case KEY:
                    result = DesensitizedUtils.key(value);
                    break;
                case COMPANY_NAME:
                    result = DesensitizedUtils.companyName(value);
                    break;
                case BANK_NAME:
                    result = DesensitizedUtils.bankName(value);
                    break;
                case NO:
                    result = value;
                    break;
                default:
                    throw new IllegalArgumentException("Unknown sensitive type enum " + desTypeEnum.name());
            }
        }
        return result;
    }

    /**
     * 解密方法
     * @param value 需要解密的值
     * @return 解密后的值
     * @throws Exception 解密异常
     */
    private String decrypt(String value) throws Exception {
        // 这里添加实际的解密逻辑，例如使用SM4算法
        // SymmetricCrypto sm4 = SmUtil.sm4(TokenConstants.SM4_DATA_SECRET.getBytes());
        // return sm4.decryptStr(value);
        
        // 暂时返回原值，实际使用时需要替换为真正的解密逻辑
        return value;
    }

    /**
     * 获取类的所有字段，包括父类字段
     * @param clazz 类
     * @return 所有字段列表
     */
    private List<Field> getFieldList(Class<?> clazz) {
        if (null == clazz || isSkippableType(clazz)) {
            return new ArrayList<>();
        }
        
        List<Field> fieldList = new ArrayList<>();
        // 递归查找
        while (clazz != null && !isSkippableType(clazz)) {
            Field[] declaredFields = clazz.getDeclaredFields();
            for (Field field : declaredFields) {
                if (field != null) {
                    try {
                        // 设置属性的可访问性
                        field.setAccessible(true);
                        // 过滤静态
                        if (Modifier.isStatic(field.getModifiers())) {
                            continue;
                        }
                        // 添加字段
                        fieldList.add(field);
                    } catch (Exception e) {
                        // 忽略无法访问的字段
                        System.out.println("无法访问字段: " + field.getName() + " 在类 " + clazz.getName());
                    }
                }
            }
            clazz = clazz.getSuperclass();
        }
        return fieldList;
    }
} 