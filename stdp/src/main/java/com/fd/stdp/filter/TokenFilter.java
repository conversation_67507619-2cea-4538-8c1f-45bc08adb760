package com.fd.stdp.filter;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.service.sys.TokenService;

/**
 * Token过滤器
 */
@Component
public class TokenFilter extends OncePerRequestFilter {

    @Autowired
    private TokenService tokenService;

    @Qualifier("userDetailsServiceImpl")
    @Autowired
    private UserDetailsService userDetailsService;

    private static final Long MINUTES_10 = 10 * 60 * 1000L;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
        throws ServletException, IOException {
        String token = getToken(request);
        if (StringUtils.isNotBlank(token)) {
            Object loginUser = tokenService.getLoginUser(token);
            if (loginUser != null) {

                loginUser = checkLoginTime(loginUser);
                if (loginUser instanceof LoginUser) {
                    if (request.getRequestURI().startsWith("/api")) {
                        throw new ServiceException("非法请求");
                    }
                    LoginUser user = (LoginUser)loginUser;
                    UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities());
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                }
            }
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 校验时间<br>
     * 过期时间与当前时间对比，临近过期10分钟内的话，自动刷新缓存
     * 
     * @param loginUser
     * @return
     */
    private Object checkLoginTime(Object loginUser) {
        if (loginUser instanceof LoginUser) {
            LoginUser user = (LoginUser)loginUser;
            long expireTime = user.getExpireTime();
            long currentTime = System.currentTimeMillis();
            if (expireTime - currentTime <= MINUTES_10) {
                String token = user.getToken();
                user = (LoginUser)userDetailsService.loadUserByUsername(user.getUsername());
                user.setToken(token);
                tokenService.refresh(user);
            }
            loginUser = user;
        }
        return loginUser;
    }

    /**
     * 根据参数或者header获取token
     * 
     * @param request
     * @return
     */
    public static String getToken(HttpServletRequest request) {

        // 头部的Authorization值以Bearer开头
        String auth = request.getHeader("Authorization");
        if (auth != null && !"".equals(auth)) {
            if (auth.startsWith(OAuth2AccessToken.BEARER_TYPE)) {
                auth = auth.replace(OAuth2AccessToken.BEARER_TYPE, "");
                return auth.trim();
            }
        }
        auth = request.getParameter("access_token");
        return auth;
    }

}
