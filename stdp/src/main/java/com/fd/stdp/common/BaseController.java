package com.fd.stdp.common;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.ModelAttribute;

import com.fd.stdp.beans.sys.SysRole;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.util.AppUserUtil;

public class BaseController {

    public static final Logger logger = LoggerFactory.getLogger(BaseController.class);

    protected HttpServletRequest request;
    protected HttpServletResponse response;
    protected HttpSession session;

    /**
     * spring ModelAttribute 放置在方法上面：表示请求该类的每个Action前都会首先执行它，也可以将一些准备数据的操作放置在该方法里面
     */
    @ModelAttribute
    public void setBaseController(HttpServletRequest request, HttpServletResponse response) {
        this.request = request;
        this.response = response;
        this.session = request.getSession();
    }

    public String getCurrentIp() {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    public static LoginUser getLoginUser() {
        Object user = AppUserUtil.getLoginAppUser();
        if (user instanceof LoginUser) {
            LoginUser appLoginUser = (LoginUser)user;
            return appLoginUser;
        }
        return null;
    }

    public static String getCurrentUserId() {
        Object user = AppUserUtil.getLoginAppUser();
        if (user instanceof LoginUser) {
            LoginUser appLoginUser = (LoginUser)user;
            return appLoginUser.getId();
        }
        return null;
    }

    public static String getCurrentAreaCode() {
        Object user = AppUserUtil.getLoginAppUser();
        if (user instanceof LoginUser) {
            LoginUser appLoginUser = (LoginUser)user;
            return appLoginUser.getAreaCode();
        }
        return null;
    }

    public static String getCurrentUscc() {
        Object user = AppUserUtil.getLoginAppUser();
        if (user instanceof LoginUser) {
            LoginUser appLoginUser = (LoginUser)user;
            return appLoginUser.getUscc();
        }
        return null;
    }

    public static String getCurrentUserName() {
        Object user = AppUserUtil.getLoginAppUser();
        if (user instanceof LoginUser) {
            LoginUser appLoginUser = (LoginUser)user;
            return appLoginUser.getUsername();
        }
        return null;
    }

    public static String getCurrentRealName() {
        Object user = AppUserUtil.getLoginAppUser();
        if (user instanceof LoginUser) {
            LoginUser appLoginUser = (LoginUser)user;
            return appLoginUser.getNickname();
        }
        return null;
    }

    /**
     * @Description:获取当前用户角色列表
     * @return List<SysRole>
     * @Author: szx
     */
    public static List<SysRole> getUserRoleList() {
        Object loginAppUser = AppUserUtil.getLoginAppUser();
        if (loginAppUser instanceof LoginUser) {
            LoginUser user = (LoginUser)loginAppUser;
            return user.getSysRoles();
        }
        return null;
    }

    /**
     * 返回当前用户的类型
     *
     * @return true 是管理员 false 非管理员
     */
    public static boolean isAdmin() {
        Object loginAppUser = AppUserUtil.getLoginAppUser();
        if (loginAppUser instanceof LoginUser) {
            LoginUser user = (LoginUser)loginAppUser;
            if (StringUtils.isEmpty(user.getType())) {
                return false;
            }
            return CommonConstant.TYPE_ADMIN.equals(user.getType());
        }
        return false;
    }

    public static String getCurrentOrgName() {
        Object user = AppUserUtil.getLoginAppUser();
        if (user instanceof LoginUser) {
            LoginUser appLoginUser = (LoginUser)user;
            return appLoginUser.getScienceOrgName()==null?appLoginUser.getManageOrgName():appLoginUser.getScienceOrgName();
        }
        return null;
    }

    public static String getCurrentScienceOrgName() {
        Object user = AppUserUtil.getLoginAppUser();
        if (user instanceof LoginUser) {
            LoginUser appLoginUser = (LoginUser)user;
            return appLoginUser.getScienceOrgName();
        }
        return null;
    }

    public static String getCurrentScienceOrgId() {
        Object user = AppUserUtil.getLoginAppUser();
        if (user instanceof LoginUser) {
            LoginUser appLoginUser = (LoginUser)user;
            return appLoginUser.getScienceOrgId();
        }
        return null;
    }

    public static String getCurrentManageOrgName() {
        Object user = AppUserUtil.getLoginAppUser();
        if (user instanceof LoginUser) {
            LoginUser appLoginUser = (LoginUser)user;
            return appLoginUser.getManageOrgName();
        }
        return null;
    }
}
