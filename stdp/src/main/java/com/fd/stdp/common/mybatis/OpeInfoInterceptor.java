package com.fd.stdp.common.mybatis;

import java.lang.reflect.Field;
import java.util.*;

import com.lzhpo.sensitive.annocation.Sensitive;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.util.AppUserUtil;

/**
 * <AUTHOR> @date
 * @desc MyBatis 新增和修改拦截-添加新增或修改用户信息及时间
 */
@Component
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
public class OpeInfoInterceptor implements Interceptor {

    private static Logger logger = LoggerFactory.getLogger(OpeInfoInterceptor.class);

    // @Autowired
    // private HttpServletRequest request;
    @Override
    @SuppressWarnings("unchecked")
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        SqlCommandType sqlCommandType = null;
        // 遍历处理所有参数，update方法有两个参数，参见Executor类中的update()方法。
        for (Object obj : args) {
            if (obj instanceof MappedStatement) { // 第一个参数处理。根据它判断是否给“操作属性”赋值。
                MappedStatement ms = (MappedStatement)obj;
                sqlCommandType = ms.getSqlCommandType();
                // 如果是“增加”或“更新”操作，则继续进行默认操作信息赋值。否则，则退出
                if (sqlCommandType == SqlCommandType.INSERT || sqlCommandType == SqlCommandType.UPDATE) {
                    continue;
                }
                break;
            }

            // 第二个参数处理。（只有第二个程序才能跑到这）
            if (obj instanceof Map) {// 如果是map，有两种情况：（1）使用@Param多参数传入，由Mybatis包装成map。（2）原始传入Map
                Map<Object, Object> map = (Map<Object, Object>)obj;
                for (Object o : map.values()) {
                    setProperty(o, sqlCommandType);
                }
            } else {// 原始参数传入
                setProperty(obj, sqlCommandType);
            }
        }

        return invocation.proceed();

    }

    /**
     * 为对象的操作属性赋值
     *
     * @param obj
     */
    private void setProperty(Object obj, SqlCommandType sqlCommandType) {
        try {
            String nikeName = null;
            String userId = null;
            try {
                userId = AppUserUtil.getCurrentUserName();
                nikeName = AppUserUtil.getCurrentRealName();
            } catch (Exception e) {
                logger.error("Exception:", e);
            }

            encryptionFiltering(obj);
            if (sqlCommandType == SqlCommandType.INSERT) {
                // 0407修改 如果存在createUser则不更新createUser 使得任务书和申报的createUser保持一致
                if(BeanUtils.getProperty(obj, "createUser") == null) {
                    if (!StringUtils.isEmpty(userId)) {
                        BeanUtils.setProperty(obj, "createUser", userId);
                    }
                    if (!StringUtils.isEmpty(nikeName)) {
                        BeanUtils.setProperty(obj, "createUserNickname", nikeName);
                    }
                }
                BeanUtils.setProperty(obj, "createTime", new Date());
                BeanUtils.setProperty(obj, "yn", CommonConstant.FLAG_YES);
                // 只有插入的时候才写入这些字段
            }

            BeanUtils.setProperty(obj, "updateUser", userId);
            BeanUtils.setProperty(obj, "updateUserNikename", nikeName);
            BeanUtils.setProperty(obj, "updateTime", new Date());
        } catch (Exception e) {
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }


    private void encryptionFiltering(Object parameter) {
        if (parameter == null) return;
        Class<?> aClass = parameter.getClass();
        List<Field> declaredFields = getAllFields(aClass);
        for (Field field : declaredFields) {
            Sensitive annotation = field.getAnnotation(Sensitive.class);
            if(annotation != null){
                field.setAccessible(true);
                try {
                    Object o = field.get(parameter);
                    if(o != null){
                        String string = o.toString();
                        boolean contains = string.contains("*");
                        if(contains){
                            field.set(parameter, null);
                        }
                    }
                } catch (IllegalAccessException e) {
                    // 过滤失败
                    logger.error("失败类，失败方法，失败原因：{}，{}，{}", aClass.getName(), field.getName(),  e.getMessage());
                    logger.error("过滤失败,{}", e);

                }
                field.setAccessible(false);
            }
        }
    }


    public static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        Field[] declaredFields1 = clazz.getDeclaredFields();
        if(declaredFields1.length > 0){
            for (Field field : declaredFields1) {
                fields.add(field);
            }
        }
        // 递归遍历所有父类
        for (Class<?> c = clazz; c != null; c = c.getSuperclass()) {
            // 获取当前类声明的所有字段
            Field[] declaredFields = c.getDeclaredFields();
            for (Field field : declaredFields) {
                fields.add(field);
            }
        }
        return fields;
    }

}
