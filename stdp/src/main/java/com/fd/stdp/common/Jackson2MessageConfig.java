package com.fd.stdp.common;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.core.JsonGenerator;

@Configuration
public class Jackson2MessageConfig {
	@Bean
    public MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter() {
		MappingJackson2HttpMessageConverter  htmlEscapingConverter = new MappingJackson2HttpMessageConverter();
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addDeserializer(String.class, new StringFWBXssDeserializer());
        htmlEscapingConverter.getObjectMapper().registerModule(simpleModule);
        
        // 配置JsonGenerator，防止数字被转换为科学计数法
        htmlEscapingConverter.getObjectMapper().getFactory().configure(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN, true);
        
        return htmlEscapingConverter;
    }
}
