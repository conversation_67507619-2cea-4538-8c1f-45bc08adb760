package com.fd.stdp.common.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

public class FastJson2JsonRedisSerializer<T> implements RedisSerializer<T> {

    public static final Charset DEFAULT_CHARSET = Charset.forName("UTF-8");

    private Class<T> clazz;

    public FastJson2JsonRedisSerializer(Class<T> clazz) {
        super();
        this.clazz = clazz;
    }

    @Override
    public byte[] serialize(T t) throws SerializationException {
        if (t == null) {
            return new byte[0];
        }
        return JSON.toJSONString(t, SerializerFeature.WriteClassName).getBytes(DEFAULT_CHARSET);
    }

    @Override
    public T deserialize(byte[] bytes) throws SerializationException {
        if (bytes == null || bytes.length <= 0) {
            return null;
        }
        String str = new String(bytes, DEFAULT_CHARSET);

        return (T) JSON.parseObject(str, clazz);
    }

    /**
     * 把Json转换为List
     */
    @SuppressWarnings("unchecked")
	public List<T> deserializeList(byte[] bytes) {
        List<T> result = new ArrayList<T>();

        if (bytes == null || bytes.length <= 0) {
            return null;
        }
        String str = new String(bytes, DEFAULT_CHARSET);

        JSONArray ja = JSON.parseArray(str);
        if (ja == null) {
            return result;
        }
        for (int i = 0; i < ja.size(); i++) {
            T vo = (T)ja.getJSONObject(i);
            result.add(vo);
        }
        return result;
    }

    /**
     * 将List转换为Json
     */
    public byte[] serializeJson(List<T> list) {
        byte[] bytes = JSON.toJSONString(list, SerializerFeature.WriteClassName).getBytes(DEFAULT_CHARSET);
        return bytes;
    }

}
