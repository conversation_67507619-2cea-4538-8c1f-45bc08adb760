package com.fd.stdp.common.redis;



import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;


/**
 * @Description:
 * @Author: linqiang
 * @Date: 2018/8/1
 * @Version: v1.0
 */
@Component
@SuppressWarnings({"rawtypes","unchecked"})
public class RedisUtil {

	private static Logger log=LoggerFactory.getLogger(RedisUtil.class);

    @Autowired
    private RedisTemplate redisTemplate;

    private FastJson2JsonRedisSerializer fastJson2JsonRedisSerializer = new FastJson2JsonRedisSerializer<>(Object.class);

    //处理乱
    @Autowired(required = false)
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        RedisSerializer stringSerializer = new StringRedisSerializer();
        redisTemplate.setKeySerializer(stringSerializer);
        this.redisTemplate = redisTemplate;
    }
    
    /**
     * 指定缓存失效时间
     * @param key 键
     * @param time 时间(秒)
     * @return
     */
    public boolean expire(String key,int time){
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
        	log.error("指定缓存失效时间 error", e);
            return false;
        }
    }

    /**
     * 根据key 获取过期时间
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public long getExpire(String key){
        return redisTemplate.getExpire(key,TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key){
        try {

            return redisTemplate.hasKey(key);

        } catch (Exception e) {
        	log.error("判断key是否存在 error", e);
            return false;
        }
    }

    /**
     * 删除缓存
     * @param key 可以传一个值 或多个
     */
    public void del(String ... key){
        if(key!=null&&key.length>0){
            if(key.length==1){
                redisTemplate.delete(key[0]);
            }else{
                redisTemplate.delete(CollectionUtils.arrayToList(key));
            }
        }
    }

    //============================String=============================
    /**
     * 普通缓存获取
     * @param key 键
     * @return 值
     */
    public Object get(String key){

        return key==null?null:redisTemplate.opsForValue().get(key);

    }

    /**
     * 普通缓存放入
     * @param key 键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean set(String key,Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
        	log.error("普通缓存放入 error", e);
            return false;
        }

    }

    /**
     * 普通缓存放入并设置时间
     * @param key 键
     * @param value 值
     * @param time 时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean set(String key,Object value,int time){
        try {

            if(time>0){
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            }else{
                set(key, value);
            }
            return true;

        } catch (Exception e) {
        	log.error("普通缓存放入并设置时间 error", e);
            return false;
        }
    }

    /**
     * 递增
     * @param key 键
     * @param by 要增加几(大于0)
     * @return
     */
    public long incr(String key, long delta){
        if(delta<0){
            throw new RuntimeException("递增因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 递减
     * @param key 键
     * @param by 要减少几(小于0)
     * @return
     */
    public long decr(String key, long delta){
        if(delta<0){
            throw new RuntimeException("递减因子必须大于0");
        }

        return redisTemplate.opsForValue().increment(key, -delta);

    }

/*    //================================Map=================================
    *//**
     * HashGet
     * @param key 键 不能为null
     * @param item 项 不能为null
     * @return 值
     *//*
    public Object hget(String key,String item){
        return unserialize(jedisCluster.hget(key, item).getBytes());
    }

    *//**
     * 获取hashKey对应的所有键值
     * @param key 键
     * @return 对应的多个键值
     *//*
    public Map<String, String> hmget(String key){
        return jedisCluster.hgetAll(key);
    }


    *//**
     * 集群HashSet
     * @param key 键
     * @param map 对应多个键值
     * @return true 成功 false 失败
     *//*
    public boolean jedishmset(String key, Map<String,String> map){
        try {

            jedisCluster.hmset(key,map);

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    *//**
     * 集群HashSet
     * @param key 键
     * @param map 对应多个键值
     * @return true 成功 false 失败
     *//*
    public boolean jedishmset(String key, Map<String,String> map, int time){
        try {
            jedisCluster.hmset(key,map);
            if(time>0){
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    *//**
     * 向一张hash表中放入数据,如果不存在将创建
     * @param key 键
     * @param item 项
     * @param value 值
     * @return true 成功 false失败
     *//*
    public boolean hset(String key,String item,Object value) {
        try {
            jedisCluster.hset(key,item,serialize(value));
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    *//**
     * 向一张hash表中放入数据,如果不存在将创建
     * @param key 键
     * @param item 项
     * @param value 值
     * @param time 时间(秒)  注意:如果已存在的hash表有时间,这里将会替换原有的时间
     * @return true 成功 false失败
     *//*
    public boolean hset(String key,String item,Object value,int time) {
        try {
            this.hset(key,item,serialize(value));
            if(time>0){
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    *//**
     * 删除hash表中的值
     * @param key 键 不能为null
     * @param item 项 可以使多个 不能为null
     *//*
    public void hdel(String key, String... item){
        jedisCluster.hdel(key,item);
    }

    *//**
     * 判断hash表中是否有该项的值
     * @param key 键 不能为null
     * @param item 项 不能为null
     * @return true 存在 false不存在
     *//*
    public boolean hHasKey(String key, String item){
        return jedisCluster.hexists(key,item);
    }

    *//**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     * @param key 键
     * @param item 项
     * @param by 要增加几(大于0)
     * @return
     *//*
    public double hincr(String key, String item,double by){
        return jedisCluster.hincrByFloat(key,item,by);
    }

    *//**
     * hash递减
     * @param key 键
     * @param item 项
     * @param by 要减少记(小于0)
     * @return
     *//*
    public double hdecr(String key, String item,double by){
        return jedisCluster.hincrByFloat(key,item,-by);
    }

    //============================set=============================
    *//**
     * 根据key获取Set中的所有值
     * @param key 键
     * @return
     *//*
    public Set<String> sGet(String key){
        try {
            return jedisCluster.smembers(key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    *//**
     * 根据value从一个set中查询,是否存在
     * @param key 键
     * @param value 值
     * @return true 存在 false不存在
     *//*
    public boolean sHasKey(String key,String value){
        try {
            return jedisCluster.sismember(key,value);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    *//**
     * 将数据放入set缓存
     * @param key 键
     * @param values 值 可以是多个
     * @return 成功个数
     *//*
    public long sSet(String key, String...values) {
        try {
            return jedisCluster.sadd(key,values);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    *//**
     * 将set数据放入缓存
     * @param key 键
     * @param time 时间(秒)
     * @param values 值 可以是多个
     * @return 成功个数
     *//*
    public long sSetAndTime(String key,int time,String...values) {
        try {
            Long count = this.sSet(key,values);
            if(time>0) expire(key, time);
            return count;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    *//**
     * 获取set缓存的长度
     * @param key 键
     * @return
     *//*
    public long sGetSetSize(String key){
        try {
            return jedisCluster.scard(key);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    *//**
     * 移除值为value的
     * @param key 键
     * @param values 值 可以是多个
     * @return 移除的个数
     *//*
    public long setRemove(String key, String ...values) {
        try {
            Long count = jedisCluster.srem(key,values);
            return count;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    //===============================list=================================

    *//**
     * 获取list缓存的内容
     * @param key 键
     * @param start 开始
     * @param end 结束  0 到 -1代表所有值
     * @return
     *//*
    public List<String> lGet(String key,long start, long end){
        try {
            return jedisCluster.lrange(key,start,end);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    *//**
     * 获取list缓存的长度
     * @param key 键
     * @return
     *//*
    public long lGetListSize(String key){
        try {
            return jedisCluster.llen(key);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    *//**
     * 通过索引 获取list中的值
     * @param key 键
     * @param index 索引  index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     * @return
     *//*
    public Object lGetIndex(String key,long index){
        try {
            String lindex = jedisCluster.lindex(key, index);
            return fastJson2JsonRedisSerializer.deserialize(lindex.getBytes());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    *//**
     * 将list放入缓存
     * @param key 键
     * @param value 值
     * @param time 时间(秒)
     * @return
     *//*
    public boolean lSet(String key, Object value) {
        try {
            jedisCluster.lpush(key,serialize(value));
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    *//**
     * 将list放入缓存
     * @param key 键
     * @param value 值
     * @param time 时间(秒)
     * @return
     *//*
    public boolean lSet(String key, String value, int time) {
        try {
            this.lSet(key,value);
            if (time > 0) expire(key, time);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    *//**
     * 将list放入缓存
     * @param key 键
     * @param value 值
     * @param time 时间(秒)
     * @return
     *//*
    public boolean lSet(String key, List<Object> value) {
        try {
            byte[] bytes = fastJson2JsonRedisSerializer.serializeJson(value);
            String data = new String(bytes);
            jedisCluster.lpush(data);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    *//**
     * 将list放入缓存
     * @param key 键
     * @param value 值
     * @param time 时间(秒)
     * @return
     *//*
    public boolean lSet(String key, List<Object> value, int time) {
        try {
            this.lSet(key,value);
            if (time > 0) expire(key, time);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    *//**
     * 移除N个值为value
     * @param key 键
     * @param count 移除多少个
     * @param value 值
     * @return 移除的个数
     *//*
    public long lRemove(String key,long count,Object value) {
        try {

            long remove = jedisCluster.lrem(key,count,serialize(value));
            return remove;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }*/


//    private Object unserialize(byte[] bytes) {
//        return fastJson2JsonRedisSerializer.deserialize(bytes);
//    }

    public String serialize(Object object) {
        byte[] bytes = fastJson2JsonRedisSerializer.serialize(object);
        return new String(bytes);
    }

}