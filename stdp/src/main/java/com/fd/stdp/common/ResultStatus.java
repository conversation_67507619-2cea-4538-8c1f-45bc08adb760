package com.fd.stdp.common;

/**
 * 
 *类描述:自定义请求状态码
 *
 *@Author:pzp
 *@date:2018年3月2日
 */
public enum ResultStatus {
    USERNAME_OR_PASSWORD_ERROR(-1001, "用户名或密码错误"),
    USER_NOT_FOUND(-1002, "用户不存在"),
    USER_NOT_LOGIN(-1003, "用户未登录"),
	
    /**************** 修改密码 (-8000) ***************************/
    MODIFY_PASSWORD_SUCCESS(-8001,"密码修改成功！"),
    MODIFY_PASSWORD_FAIL(-8002,"密码修改失败！"),
    MODIFY_PASSWORD_NULL(-8003,"旧密码为空或者旧密码填写不正确！"),
    
    /**************** 参数 (-10000) ***************************/
    PARAM_NULL(100001,"参数不能为空"),
    PARAM_INCORRECT(100002,"参数不正确"),
    
    PC_CURRENT_ACTIVITY_CODE(100003,"节点不能为空"),
    

    /**************** 异常(90001) ***************************/
    CODE_EXCEPTION(90001,"代码异常")
    ;

    /**
     * 返回码
     */
    private int code;

    /**
     * 返回结果描述
     */
    private String message;

    ResultStatus(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
