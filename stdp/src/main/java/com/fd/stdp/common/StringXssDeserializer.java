package com.fd.stdp.common;

import java.io.IOException;

import org.apache.commons.lang3.StringEscapeUtils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

public class StringXssDeserializer  extends JsonDeserializer<String> {
    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
    	if(p==null||p.getText()==null) {
    		return null;
    	}
    	
		String source = p.getText().trim();
		// 把字符串做XSS过滤
		source=StringEscapeUtils.escapeHtml4(source);
		return source;
    }
    
}