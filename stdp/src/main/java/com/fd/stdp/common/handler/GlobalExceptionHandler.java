package com.fd.stdp.common.handler;

import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fd.stdp.beans.sys.vo.ResponseInfo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.exception.BaseException;
import com.fd.stdp.common.exception.ServiceException;

import java.util.Objects;


@ControllerAdvice("com.fd.stdp")
@ResponseBody
public class GlobalExceptionHandler {
    private Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(BaseException.class)
    public ResponseInfo baseExceptionHandler(HttpServletResponse response, BaseException ex) {
        logger.error(ex.getMessage(), ex);
        response.setStatus(500);
        return new ResponseInfo(ex.getStatus() + "", ex.getMessage());
    }


    @ExceptionHandler(ServiceException.class)
    public RestApiResponse<?> baseExceptionHandler(HttpServletResponse response, ServiceException ex) {
        logger.error(ex.getMessage(), ex);
        response.setStatus(500); //ServiceException返回200
        return RestApiResponse.error(ex.getMessage());
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseInfo accessDeniedExceptionHandler(HttpServletResponse response, Exception ex) {
        response.setStatus(403);
        logger.error(ex.getMessage(), ex);
        return new ResponseInfo(403 + "", "api无权限");
    }

    @ExceptionHandler(Exception.class)
    public ResponseInfo otherExceptionHandler(HttpServletResponse response, Exception ex) {
        response.setStatus(500);
        logger.error(ex.getMessage(), ex);
        return new ResponseInfo(500 + "", "接口异常，请联系管理员");
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object handleMethodArgumentNotValidException(HttpServletResponse response, MethodArgumentNotValidException e) {
        logger.error(e.getMessage(), e);
        response.setStatus(500);
        String message = Objects.requireNonNull(e.getBindingResult().getFieldError()).getDefaultMessage();
        return RestApiResponse.error(message);
    }

}
