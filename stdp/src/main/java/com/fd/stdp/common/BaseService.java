package com.fd.stdp.common;

import java.util.List;

import com.github.pagehelper.PageInfo;

public interface BaseService<T> {

	/**
	 * 查询
	 *
	 * @param entity
	 * @return
	 */
	T selectOne(T entity);

	/**
	 * 通过Id查询
	 *
	 * @param id
	 * @return
	 */
	T selectById(Object id);

	/**
	 * 根据ID集合来查询
	 *
	 * @param ids
	 * @return
	 */
	// List<T> selectListByIds(List<Object> ids);

	/**
	 * 查询列表
	 *
	 * @param entity
	 * @return
	 */
	List<T> selectList(T entity);

	/**
	 * 获取所有对象
	 *
	 * @return
	 */
	List<T> selectListAll();

	// /**
	// * 查询总记录数
	// *
	// * @return
	// */
	// Long selectCountAll();

	/**
	 * 查询总记录数
	 *
	 * @param entity
	 * @return
	 */
	Long selectCount(T entity);

	/**
	 * 添加
	 *
	 * @param entity
	 */
	void insert(T entity);

	/**
	 * 插入 不插入null字段
	 *
	 * @param entity
	 */
	void insertSelective(T entity);

	/**
	 * 删除
	 *
	 * @param entity
	 */
	int delete(T entity);

	/**
	 * 根据Id删除
	 *
	 * @param id
	 */
	int deleteById(Object id);

	/**
	 * 通过对象删除
	 * 
	 * @param example
	 * @return
	 *
	 * @Author:pzp
	 * @date:2018年4月11日 上午11:03:27
	 */
	int deleteByExample(Object example);

	/**
	 * 根据id更新
	 *
	 * @param entity
	 */
	int updateById(T entity);

	/**
	 * 不update null
	 *
	 * @param entity
	 */
	int updateSelectiveById(T entity);

	// /**
	// * 根据ID集合批量删除
	// *
	// * @param ids
	// */
	// void deleteBatchByIds(List<Object> ids);
	//
	// /**
	// * 批量更新
	// *
	// * @param entitys
	// */
	// void updateBatch(List<T> entitys);

	List<T> selectByExample(Object example);

	int selectCountByExample(Object example);

	T selectOneByExample(Object example);

	/**
	 * 简单分页查询
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param queryEntity
	 * @return
	 */
	PageInfo<T> getBasePage(Integer pageNum, Integer pageSize, T queryEntity);

    void updateList(BaseEntity vo, List list, Object mapper, String linkIdparamSetMethodName);
}
