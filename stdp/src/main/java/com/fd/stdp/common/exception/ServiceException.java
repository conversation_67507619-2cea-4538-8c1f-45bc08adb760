package com.fd.stdp.common.exception;

/**
 * 
 *类描述: 业务层异常
 *
 *@date:2018年2月27日
 *@Version:1.1.0
 */
public class ServiceException extends BaseException {

    private static final long serialVersionUID = -1114219045834565634L;
    

    public ServiceException() {
    }

    public ServiceException(String message,int status) {
        super(message);
        this.setStatus("4001");
    }

    public ServiceException(String message) {
        super(message);
    }

    public ServiceException(String message, Throwable cause) {
        super(message, cause);
    }

    public ServiceException(Throwable cause) {
        super(cause);
    }

    public ServiceException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
    
}
