package com.fd.stdp.common.annotation;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.fd.stdp.beans.sys.SysLogOper;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.service.sys.SysLogOperService;
import com.fd.stdp.util.AppUserUtil;

/**
 * 后置带注解 OperationLogAnnotation的方法做aop
 * 
 * <AUTHOR>
 * @date 2020年7月6日 上午9:54:41
 */
@Component
@Aspect
public class OperationLogAop {

    @Autowired
    SysLogOperService sysLogOperService;

    private static final Logger logger = LoggerFactory.getLogger(OperationLogAop.class);

    /**
     * 后置带注解 OperationLogAnnotation的方法做aop
     */
    @AfterReturning(value = "@annotation(com.fd.stdp.common.annotation.OperationLogAnnotation)")
    public void logSave(JoinPoint joinPoint) throws Throwable {

        SysLogOper log = new SysLogOper();
        log.setCreateTime(new Date());
        // 获取方法参数
        Object[] args = joinPoint.getArgs();

        MethodSignature methodSignature = (MethodSignature)joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        // 获取注解类对象
        OperationLogAnnotation logAnnotation = method.getDeclaredAnnotation(OperationLogAnnotation.class);
        // 获取到方法的所有参数名称的字符串数组
        String[] parameterNames = methodSignature.getParameterNames();

        String template = logAnnotation.notes();
        String notes = regexMatcher(template, parameterNames, args);

        log.setOperDetails(notes);
        log.setOperOneModule(logAnnotation.module());
        log.setOperTwoModule(logAnnotation.submodule());
        log.setIp(getCurrentIp());
        try {
            // Object object = joinPoint.proceed(args); // 执行原方法
            LoginUser user = getCurrentUser(); // 获取用户信息
            if (user != null) {
                log.setCreateUser(user.getId());
                log.setCreateUserNickname(user.getNickname());
            }

            // 异步将Log对象发送到队列
            CompletableFuture.runAsync(() -> {
                try {
                    sysLogOperService.saveOrUpdateSysLogOper(log);
                    logger.info("发送操作日志到队列：{}", log.toString());
                } catch (Exception e2) {
                    logger.error("发送操作日志到队列：{}", e2);
                }
            });
            // return object;
        } catch (Exception ex) { // 方法执行失败
            logger.error("发送操作日志到队列：{}", ex);
            throw ex;
        }
    }

    /**
     * 获取登录用户信息
     * 
     * @return
     */
    private LoginUser getCurrentUser() {
        Object user = AppUserUtil.getLoginAppUser();
        if (user instanceof LoginUser) {
            LoginUser appLoginUser = (LoginUser)user;
            return appLoginUser;
        }
        return null;
    }

    private String getCurrentIp() {
        // 取用户信息和请求路径
        HttpServletRequest request =
            ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();

        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 正则获取指定字符串并替换："{A:b}"
     * 
     * @param str
     * @return
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    private static String regexMatcher(String str, String[] fields, Object[] args)
        throws IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        String res = str;
        String regex = "\\{([^}])*\\}";
        Matcher marcher = Pattern.compile(regex).matcher(str);
        while (marcher.find()) {
            String sub = marcher.group(0);
            String text = sub.substring(1, sub.lastIndexOf(":"));
            String key = sub.substring(sub.lastIndexOf(":") + 1, sub.length() - 1);
            String value = getFieldValue(key, fields, args);
            if (StringUtils.isEmpty(value)) {
                res = res.replace(sub, "");
            } else {
                res = res.replace(sub, text + "：" + value);
            }

        }
        return res;
    }

    /**
     * 从方法传递参数中取值
     * 
     * @param key
     * @param fields
     * @param args
     * @return
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     * @throws NoSuchMethodException
     */
    private static String getFieldValue(String key, String[] fields, Object[] args)
        throws IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        for (int i = 0; i < args.length; i++) {
            if (key.indexOf('.') > -1) { // 包含”.“说明为对象属性
                String prefix = key.substring(0, key.indexOf('.'));
                String tail = key.substring(key.indexOf('.') + 1);
                if (isObject(args[i]) && fields[i].equals(prefix)) {
                    return BeanUtils.getProperty(args[i], tail);
                }
            } else {
                if (isObject(args[i])) {
                    String v = BeanUtils.getProperty(args[i], key);
                    if (!StringUtils.isEmpty(v)) {
                        return v;
                    }
                } else if (args[i] instanceof List<?>) {
                    return StringUtils.join((List<?>)args[i], ',');
                } else if (fields[i].equals(key)) {
                    return args[i].toString();
                }
            }
        }
        return "";
    }

    /**
     * 判断参数是否是对象，除String、Integer、Date外就是对象
     * 
     * @param o
     * @return
     */
    private static boolean isObject(Object o) {
        if (o instanceof String) {
            return false;
        }
        if (o instanceof Integer) {
            return false;
        }
        if (o instanceof Date) {
            return false;
        }
        if (o instanceof BigDecimal) {
            return false;
        }

        if (o.getClass().isArray()) {
            return false;
        }

        if (o instanceof List<?>) {
            return false;
        }
        return true;
    }
}
