package com.fd.stdp.common.annotation;

import com.fd.stdp.util.DesTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 解密脱敏字段注解
 * 用于标记需要进行解密和脱敏处理的字段
 * 支持两个功能：
 * 1. 解密：对加密的字段进行解密处理
 * 2. 脱敏：对敏感信息进行脱敏处理
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@Target(value = {ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DecryptField {

    /**
     * 是否做脱敏处理
     * 默认为true，表示需要脱敏处理
     * 如果设置为false，则不进行脱敏处理，保留原始值
     *
     * @return 是否脱敏
     */
    boolean isDes() default true;

    /**
     * 脱敏类型
     * 默认是中文姓名脱敏
     * 可选值参考DesTypeEnum枚举类
     *
     * @return 脱敏类型
     */
    DesTypeEnum desType() default DesTypeEnum.CHINESE_NAME;

    /**
     * 是否对字段做解密处理
     * 默认为true，表示需要解密处理
     * 如果设置为false，则不进行解密处理，直接进行脱敏处理
     * 
     * 注意：解密处理在脱敏处理之前进行
     *
     * @return 是否解密
     */
    boolean isEncryptField() default true;
} 