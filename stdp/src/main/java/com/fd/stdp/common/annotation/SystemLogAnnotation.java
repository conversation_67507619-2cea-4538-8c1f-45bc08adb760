package com.fd.stdp.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义注解，方法调用 日志记录
 *@Description: TODO
 *@Author: linqiang
 *@Date: 2018年8月6日 下午7:59:46
 */
@Target({ ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
public @interface SystemLogAnnotation {

	/**
	 * 业务类型
	 *@Description: TODO
	 *@return String
	 *@Author: linqiang
	 */
	String type()default "";
	/**
	 * 业务操作
	 *@Description: TODO
	 *@return String
	 *@Author: linqiang
	 */
	String value()default "";
	/**
	 * 是否写入参数
	 *@Description: TODO
	 *@return boolean
	 *@Author: linqiang
	 */
	boolean recordParam()default true;
}
