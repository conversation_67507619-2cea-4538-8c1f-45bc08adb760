package com.fd.stdp.common;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.util.UUIDUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import tk.mybatis.mapper.common.Mapper;

public class BaseServiceImpl<M extends Mapper<T>, T> implements BaseService<T> {

    public final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    protected M mapper;

    @Override
    public T selectOne(T entity) {
        return mapper.selectOne(entity);
    }

    @Override
    public T selectById(Object id) {
        return mapper.selectByPrimaryKey(id);
    }

    // @Override
    // public List<T> selectListByIds(List<Object> ids) {
    // return mapper.selectByIds(ids);
    // }

    @Override
    public List<T> selectList(T entity) {
        return mapper.select(entity);
    }

    @Override
    public List<T> selectListAll() {
        return mapper.selectAll();
    }

    // @Override
    // public Long selectCountAll() {
    // return mapper.selectCount();
    // }

    @Override
    public Long selectCount(T entity) {
        return Long.valueOf(mapper.selectCount(entity));
    }

    @Override
    public void insert(T entity) {
        mapper.insert(entity);
    }

    @Override
    public void insertSelective(T entity) {
        mapper.insertSelective(entity);
    }

    @Override
    public int delete(T entity) {
        return mapper.delete(entity);
    }

    @Override
    public int deleteById(Object id) {
        return mapper.deleteByPrimaryKey(id);
    }

    @Override
    public int updateById(T entity) {
        return mapper.updateByPrimaryKey(entity);
    }

    @Override
    public int updateSelectiveById(T entity) {
        return mapper.updateByPrimaryKeySelective(entity);
    }

    // @Override
    // public void deleteBatchByIds(List<Object> ids) {
    // mapper.batchDeleteByIds(ids);
    // }
    //
    // @Override
    // public void updateBatch(List<T> entitys) {
    // mapper.batchUpdate(entitys);
    // }

    @Override
    public List<T> selectByExample(Object example) {
        return mapper.selectByExample(example);
    }

    @Override
    public int selectCountByExample(Object example) {
        return mapper.selectCountByExample(example);
    }

    @Override
    public T selectOneByExample(Object example) {
        return mapper.selectOneByExample(example);
    }

    /**
     * 简单分页查询
     * 
     * @param pageNum
     * @param pageSize
     * @param queryEntity
     * @return
     */
    @Override
    public PageInfo<T> getBasePage(Integer pageNum, Integer pageSize, T queryEntity) {
        // 分页开始
        PageHelper.startPage(pageNum, pageSize);
        List<T> list = mapper.select(queryEntity);
        return new PageInfo<T>(list);
    }

    /**
     * 根据对象删除
     */
    @Override
    public int deleteByExample(Object example) {
        return mapper.deleteByExample(example);
    }

    /**
     * @description 获取公共属性字段
     * @return String[]
     * <AUTHOR>
     * @date 2020-9-21 8:43:05
     */
    public String[] getCommonProperties() {
        // 更新时，忽略公共字段的更新
        String[] commonProperties = new String[] {"id"};
        Field[] baseFields = BaseEntity.class.getDeclaredFields();
        String[] ignoreProperties = new String[baseFields.length];
        for (int i = 0; i < baseFields.length; i++) {
            if (!StringUtils.isEmpty(baseFields[i].getName())) {
                ignoreProperties[i] = baseFields[i].getName();
            }
        }
        ignoreProperties = StringUtils.concatenateStringArrays(ignoreProperties, commonProperties);
        return ignoreProperties;
    }

    @Override
    public void updateList(BaseEntity vo, List list, Object mapper, String linkIdparamSetMethodName) {
        try {
            Method[] methods = mapper.getClass().getMethods();
            Method methodClearByFormId = mapper.getClass().getMethod("clearByFormId", String.class);
            Method methodUpdateByPrimaryKeySelective = mapper.getClass().getMethod("updateByPrimaryKeySelective", Object.class);
            Method methodInsertSelective = mapper.getClass().getMethod("insertSelective", Object.class);
            Method linkIdparamSetMethod = null;
            Method mSetYn = null;
            methodClearByFormId.invoke(mapper, vo.getId());
            for (Object p:list) {
                BaseEntity b = (BaseEntity) p;
                linkIdparamSetMethod = linkIdparamSetMethod==null?p.getClass().getMethod(linkIdparamSetMethodName, String.class):linkIdparamSetMethod;
                mSetYn = mSetYn==null?p.getClass().getMethod("setYn", Integer.class):mSetYn;
                linkIdparamSetMethod.invoke(b, vo.getId());
                mSetYn.invoke(b, CommonConstant.FLAG_YES);
                //if(b.getId() == null){
                b.setId(UUIDUtils.getUUID());
                methodInsertSelective.invoke(mapper, b);
//                } else {
//                    methodUpdateByPrimaryKeySelective.invoke(mapper, b);
//                }
            }
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
            throw new ServiceException("代码错误");
        }
    }

    /**
     * 拷贝指定的字段
     * @param source
     * @param target
     * @param fields  字段列表
     */
    public void beanCopyField(Object source, Object target, String[] fields) {
        Arrays.stream(fields).map(field->{
            if(field !=null && field.length() > 0) {
                return field.substring(0, 1).toUpperCase() + field.substring(1);
            }
            return field;
        }).forEach(field->{
            try {
                Method getMethod = source.getClass().getMethod("get" + field);
                Object val = getMethod.invoke(source);
                if(val != null) {
                    if(!(val instanceof String) || org.apache.commons.lang3.StringUtils.isNotBlank((String) val)){
                        Method setMethod = target.getClass().getMethod("set" + field, val.getClass());
                        setMethod.invoke(target, val);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    public static List startPage(List list, Integer pageNum, Integer pageSize) {
        if (list == null) {
            return Collections.emptyList();
        }
        if (list.isEmpty()) {
            return Collections.emptyList();
        }
        //记录总数
        Integer count = list.size();

        //开始索引
        int fromIndex = (pageNum - 1) * pageSize;
        //结束索引
        int toIndex = pageNum * pageSize;
        if (fromIndex + 1 > count) {
            return Collections.emptyList();
        }
        if (pageNum * pageSize > count) {
            toIndex = count;
        }
        return list.subList(fromIndex, toIndex);
    }

}
