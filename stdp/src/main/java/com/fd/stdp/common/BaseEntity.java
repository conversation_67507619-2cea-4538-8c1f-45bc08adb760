package com.fd.stdp.common;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;

import com.lzhpo.sensitive.SensitiveStrategy;
import com.lzhpo.sensitive.annocation.Sensitive;
import org.apache.ibatis.type.JdbcType;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fd.stdp.common.annotation.FieldTypeAnnotation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import tk.mybatis.mapper.annotation.ColumnType;

/**
 * 
 * 类描述:实体基类
 *
 * <AUTHOR>
 * @date:2017年5月4日
 * @Version:1.1.0
 */
@ApiModel(value = "com.fd.stdp.common", description = "实体基类")
public class BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@FieldTypeAnnotation(getName = "ID")
	@Id
	@ApiModelProperty(value="主键")
	//@GeneratedValue(strategy = GenerationType.IDENTITY)
	private String id;


	@Column(name = "CREATE_TIME")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@JSONField(format="yyyy-mm-dd HH:mm:ss")
	@ApiModelProperty(value="创建时间")
	private Date createTime;

	@Column(name = "UPDATE_TIME")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@JSONField(format="yyyy-mm-dd HH:mm:ss")
	@ApiModelProperty(value="更新时间")
	private Date updateTime;

	@Column(name = "CREATE_USER")
	@ApiModelProperty(value="创建用户ID")
	@ColumnType(jdbcType = JdbcType.VARCHAR)
	private String createUser;
	
	@Column(name = "CREATE_USER_NICKNAME")
	@ApiModelProperty(value="创建用户名称")
	@Sensitive(strategy = SensitiveStrategy.CHINESE_NAME)
	private String createUserNickname;

	@Column(name = "UPDATE_USER")
	@ApiModelProperty(value="更新用户")
	private String updateUser;

	@Column(name = "UPDATE_USER_NIKENAME")
	@ApiModelProperty(value="更新用户名称")
	@Sensitive(strategy = SensitiveStrategy.CHINESE_NAME)
	private String updateUserNikename;

	@Column(name = "ATTR1")
	@ApiModelProperty(value="备用字段1")
	private String attr1;

	@Column(name = "ATTR2")
	@ApiModelProperty(value="备用字段2")
	private String attr2;

	@Column(name = "ATTR3")
	@ApiModelProperty(value="备用字段3")
	private String attr3;

	@Column(name = "ATTR4")
	@ApiModelProperty(value="备用字段4")
	private String attr4;

	@Column(name = "ATTR5")
	@ApiModelProperty(value="备用字段5")
	private String attr5;

	@ApiModelProperty(value="是否生效 1：是 0：否")
	private Integer yn;
	
	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getYn() {
		return yn;
	}

	public void setYn(Integer yn) {
		this.yn = yn;
	}

	/**
	 * 获取主键
	 *
	 * @return id - 主键
	 */
	public String getId() {
		return id;
	}

	/**
	 * 设置主键
	 *
	 * @param id
	 *            主键
	 */
	public void setId(String id) {
		this.id = id == null ? null : id.trim();
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	public String getAttr1() {
		return attr1;
	}

	public void setAttr1(String attr1) {
		this.attr1 = attr1;
	}

	public String getAttr2() {
		return attr2;
	}

	public void setAttr2(String attr2) {
		this.attr2 = attr2;
	}

	public String getAttr3() {
		return attr3;
	}

	public void setAttr3(String attr3) {
		this.attr3 = attr3;
	}

	public String getAttr4() {
		return attr4;
	}

	public void setAttr4(String attr4) {
		this.attr4 = attr4;
	}

	public String getAttr5() {
		return attr5;
	}

	public void setAttr5(String attr5) {
		this.attr5 = attr5;
	}

	public String getCreateUserNickname() {
		return createUserNickname;
	}

	public void setCreateUserNickname(String createUserNickname) {
		this.createUserNickname = createUserNickname;
	}

	public String getUpdateUserNikename() {
		return updateUserNikename;
	}

	public void setUpdateUserNikename(String updateUserNikename) {
		this.updateUserNikename = updateUserNikename;
	}
}
