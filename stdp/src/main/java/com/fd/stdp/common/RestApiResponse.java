package com.fd.stdp.common;


import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;

/**
 * 
 * 类描述: 返回前端的消息封装
 *
 * @Author:pzp
 * @date:2018年3月2日
 */
public class RestApiResponse<T> implements Serializable {
	private static final long serialVersionUID = 3896336125845619148L;
	
	private static final String DEFAULT_MSG_SUC = "成功";

	/**
	 * 反馈数据
	 */
	private Object data;

	/**
	 * 反馈信息
	 */
	private String message;

	/**
	 * 状态值,成功200
	 */
	private int status;

	public Map<String, Object> toMap() {
		Map<String, Object> map = new HashMap<>();
		if (data != null)
			map.put("data", this.getData());
		if (message != null)
			map.put("message", this.getMessage());
		map.put("code", this.getStatus());
		return map;
	}
	
	protected RestApiResponse() {
		super();
	}

	protected RestApiResponse(Object data) {
		this.setStatus(200);
		this.data = data;
	}

	protected RestApiResponse(int code, Object data) {
		this.setStatus(code);
		this.message = DEFAULT_MSG_SUC;
		this.data = data;
	}

	public Object getData() {
		return data;
	}

	public RestApiResponse<T> setData(Object data) {
		this.data = data;
		return this;
	}

	@Override
	public String toString() {
		return JSON.toJSONStringWithDateFormat(this, "yyyy-MM-dd HH:mm:ss");
	}

	public static RestApiResponse<?> fromJson(String json) {
		return JSON.parseObject(json, RestApiResponse.class);
	}

	public String getMessage() {
		return message;
	}

	@SuppressWarnings("rawtypes")
	public RestApiResponse setMessage(String message) {
		this.message = message;
		return this;
	}
	@SuppressWarnings("rawtypes")
	public static RestApiResponse ok() {
		return ok(null);
	}
	@SuppressWarnings("rawtypes")
	public static RestApiResponse ok(Object data) {
		return new RestApiResponse(200, data);
	}
	@SuppressWarnings("rawtypes")
	public static RestApiResponse error(String message) {
		return new RestApiResponse(500, null).setMessage(message);
	}
	@SuppressWarnings("rawtypes")
	public static RestApiResponse error(String message, int status) {
		return new RestApiResponse().setStatus(status).setMessage(message);
	}
	@SuppressWarnings("rawtypes")
	public static RestApiResponse error(String message, int status, Object data) {
		return new RestApiResponse(data).setStatus(status).setMessage(message);
	}
	@SuppressWarnings("rawtypes")
	public static RestApiResponse error(ResultStatus resultStatus) {
		return new RestApiResponse().setStatus(resultStatus.getCode()).setMessage(resultStatus.getMessage());
	}

	public int getStatus() {
		return status;
	}
	@SuppressWarnings("rawtypes")
	public RestApiResponse setStatus(int status) {
		this.status = status;
		return this;
	}
}
