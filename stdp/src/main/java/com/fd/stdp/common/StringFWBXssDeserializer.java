package com.fd.stdp.common;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

public class StringFWBXssDeserializer  extends JsonDeserializer<String> {
    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
    	if(p==null||p.getText()==null) {
    		return null;
    	}
    	
		String source = p.getText().trim();
		// 把字符串做XSS过滤
		source = source.replaceAll("alert", "");
		source = source.replaceAll("[\\\"\\\'][\\s]*javascript:(.*)[\\\"\\\']", "\"\"");
		source = source.replaceAll("script", "");
		return source;
    }
    
}