package com.fd.stdp.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义注解 操作日志
 * 
 * <AUTHOR>
 * @date 2020年7月6日 上午9:39:51
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface OperationLogAnnotation {

    /**
     * 操作详情 使用占位符插值 例如："新增{用户：basicTestorg.name}，{流水号：basicTestorg.id}" 说明：流水号表示操作主表的主键
     */
    String notes() default "";

    /**
     * 操作一级模块
     */
    String module() default "";

    /**
     * 操作二级模块
     */
    String submodule() default "";

}
