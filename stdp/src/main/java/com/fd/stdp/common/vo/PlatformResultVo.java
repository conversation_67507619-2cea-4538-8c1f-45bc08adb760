package com.fd.stdp.common.vo;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description:第三方系统访问本系统返回数据格式
 * <AUTHOR> 
 * @date 2020年5月13日
 */
@ApiModel(value="com.fd.stdp.common.vo.PlatformResultVo",description = "第三方系统访问本系统返回数据格式")
public class PlatformResultVo implements Serializable {
	 
	private static final long serialVersionUID = -2394491656313949560L;
	
	@ApiModelProperty(value ="业务处理状态码")
	private String code;
	@ApiModelProperty(value ="提示信息")
	private String msg;
	@ApiModelProperty(value ="返回数据")
	private Object data;
	 

	public String getCode() {
		return code;
	}
	
	public void setCode(String code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}



	@Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }
}
