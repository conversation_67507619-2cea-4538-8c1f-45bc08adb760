package com.fd.stdp.common.exception;

public class BaseException extends RuntimeException {
	
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String status = "200";
    
    private String code;
    
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public BaseException() {
    }

    public BaseException(String message,String status) {
        super(message);
        this.status = status;
    }

    public BaseException(String message,String status,String code) {
        super(message);
        this.status = status;
        this.code = code;
    }
    
    public BaseException(String message) {
        super(message);
    }

    public BaseException(String message, Throwable cause) {
        super(message, cause);
    }

    public BaseException(Throwable cause) {
        super(cause);
    }

    public BaseException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
