package com.fd.stdp.controller.basic;

import com.fd.stdp.beans.basic.vo.BasicPersonVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonExpert;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.basic.BasicPersonExpertService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 *@Description: 专家库
 *@Author: wangsh
 *@Date: 2022-01-07 10:53:32
 */
@RestController
@RequestMapping("/basic/basicPersonExpert")
@Api(value="专家库", description="专家库")
public class BasicPersonExpertController  extends BaseController {

	@Autowired
	private BasicPersonExpertService basicPersonExpertService;
	
	private final String PER_PREFIX = "basic:but:expert:";
	
	/**
	 *@Description: 新增专家库
	 *@param basicPersonExpert 专家库数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增专家库", notes = "新增专家库")
	@SystemLogAnnotation(type = "专家库",value = "新增专家库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveBasicPersonExpert(@RequestBody BasicPersonExpertVo basicPersonExpert) {
		basicPersonExpert.setOrgName(getCurrentOrgName());
		basicPersonExpert.setOrgCode(getCurrentScienceOrgId());
		String id = basicPersonExpertService.saveOrUpdateBasicPersonExpert(basicPersonExpert);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改专家库
	 *@param basicPersonExpert 专家库数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改专家库", notes = "修改专家库")
	@SystemLogAnnotation(type = "专家库",value = "修改专家库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateBasicPersonExpert(@RequestBody BasicPersonExpertVo basicPersonExpert) {
		String id = basicPersonExpertService.saveOrUpdateBasicPersonExpert(basicPersonExpert);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除专家库(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除专家库", notes = "删除专家库")
	@SystemLogAnnotation(type = "专家库",value = "删除专家库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteBasicPersonExpert(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		basicPersonExpertService.deleteMultiBasicPersonExpert(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询专家库详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询专家库详情", notes = "查询专家库详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		BasicPersonExpert  basicPersonExpert=basicPersonExpertService.findById(id);
		return RestApiResponse.ok(basicPersonExpert);
	}
	
	/**
	 *@Description: 分页查询专家库
	 *@param basicPersonExpertVo 专家库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询专家库", notes = "分页查询专家库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody BasicPersonExpertVo basicPersonExpertVo) {
		PageInfo<BasicPersonExpert>  basicPersonExpert=basicPersonExpertService.findPageByQuery(basicPersonExpertVo);
		return RestApiResponse.ok(basicPersonExpert);
	}

	/**
	 *@Description: 专家筛选入库
	 *@param basicPersonExpert 专家库数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/filter")
	@ApiOperation(value = "专家筛选入库", notes = "专家筛选入库")
	@SystemLogAnnotation(type = "专家库",value = "专家筛选入库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"filter')")
	public RestApiResponse<?> filterBasicPersonExpert(@RequestBody BasicPersonExpertVo basicPersonExpert) {
		String id = basicPersonExpertService.filterBasicPersonExpert(basicPersonExpert);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 专家推荐入库
	 *@param basicPersonExpert 专家库数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "专家推荐入库", notes = "专家推荐入库")
	@SystemLogAnnotation(type = "专家库",value = "专家推荐入库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitBasicPersonExpert(@RequestBody BasicPersonExpertVo basicPersonExpert) {
		basicPersonExpert.setOrgName(getCurrentOrgName());
		basicPersonExpert.setOrgCode(getCurrentScienceOrgId());
		String id = basicPersonExpertService.submitBasicPersonExpert(basicPersonExpert);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 专家推荐入库审核
	 *@param basicPersonExpert 专家库数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "专家推荐入库审核", notes = "专家推荐入库审核")
	@SystemLogAnnotation(type = "专家库",value = "专家推荐入库审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditBasicPersonExpert(@RequestBody BasicPersonExpertVo basicPersonExpert) {
		String id = basicPersonExpertService.auditBasicPersonExpert(basicPersonExpert);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 专家推荐入库退回
	 *@param basicPersonExpert 专家库数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "专家推荐入库退回", notes = "专家推荐入库退回")
	@SystemLogAnnotation(type = "专家库",value = "专家推荐入库退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackBasicPersonExpert(@RequestBody BasicPersonExpertVo basicPersonExpert) {
		String id = basicPersonExpertService.sendBackBasicPersonExpert(basicPersonExpert);
		return RestApiResponse.ok(id);
	}


	/**
	 *@Description: 专家库统计
	 *@param basicPersonVo 专家库统计 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/statistics")
	@ApiOperation(value = "专家库统计", notes = "专家库统计")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"statistics')")
	public RestApiResponse<?> statisticsPageByQuery(@RequestBody BasicPersonVo basicPersonVo) {
		return RestApiResponse.ok(basicPersonExpertService.statistics(basicPersonVo));
	}

	/**
	 *@Description: 批量导入机构人员库
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/upload")
	@ApiOperation(value = "批量导入专家", notes = "批量导入专家")
	@SystemLogAnnotation(type = "专家库统计",value = "批量导入专家")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"upload')")
	public void upload(@RequestParam MultipartFile file){
		basicPersonExpertService.upload(file);
	}

	/**
	 *@Description: 专家库导出
	 *@param basicPersonVo 专家库导出 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/export")
	@ApiOperation(value = "专家库导出", notes = "专家库导出")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"export')")
	public RestApiResponse<?> exportPageByQuery(@RequestBody BasicPersonVo basicPersonVo, HttpServletResponse response) {
		return RestApiResponse.ok(basicPersonExpertService.export(basicPersonVo, response));
	}

	/**
	 *@Description: 专家待办
	 *@param basicPersonExpert 专家库数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/todo")
	@ApiOperation(value = "专家待办", notes = "专家待办")
	@SystemLogAnnotation(type = "专家库",value = "专家待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoBasicPersonExpert(@RequestBody BasicPersonExpertVo basicPersonExpert) {
		return RestApiResponse.ok(basicPersonExpertService.todoList(basicPersonExpert));
	}

	/**
	 *@Description: 专家待办
	 *@param basicPersonExpert 专家库数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/finished")
	@ApiOperation(value = "专家待办", notes = "专家待办")
	@SystemLogAnnotation(type = "专家库",value = "专家待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finishedBasicPersonExpert(@RequestBody BasicPersonExpertVo basicPersonExpert) {
		return RestApiResponse.ok(basicPersonExpertService.finishedList(basicPersonExpert));
	}
}
