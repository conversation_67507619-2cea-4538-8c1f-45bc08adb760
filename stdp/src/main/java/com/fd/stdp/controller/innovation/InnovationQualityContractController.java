package com.fd.stdp.controller.innovation;

import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityContract;
import com.fd.stdp.beans.innovation.vo.InnovationQualityContractVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.innovation.InnovationQualityContractService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 省质检中心筹建任务书
 *@Author: wangsh
 *@Date: 2022-02-11 13:56:23
 */
@RestController
@RequestMapping("/innovation/innovationQualityContract")
@Api(value="省质检中心筹建任务书", description="省质检中心筹建任务书")
public class InnovationQualityContractController  extends BaseController {

	@Autowired
	private InnovationQualityContractService innovationQualityContractService;
	
	private final String PER_PREFIX = "but:quality:contract:";
	
	/**
	 *@Description: 新增省质检中心筹建任务书
	 *@param vo 省质检中心筹建任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增省质检中心筹建任务书", notes = "新增省质检中心筹建任务书")
	@SystemLogAnnotation(type = "省质检中心筹建任务书",value = "新增省质检中心筹建任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInnovationQualityContract(@RequestBody InnovationQualityContractVo vo) {
		if(StringUtils.isBlank(vo.getOrgName())){
			vo.setOrgName(getCurrentOrgName());
			vo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityContractService.saveOrUpdateInnovationQualityContract(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改省质检中心筹建任务书
	 *@param innovationQualityContract 省质检中心筹建任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改省质检中心筹建任务书", notes = "修改省质检中心筹建任务书")
	@SystemLogAnnotation(type = "省质检中心筹建任务书",value = "修改省质检中心筹建任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInnovationQualityContract(@RequestBody InnovationQualityContractVo innovationQualityContract) {
		String id = innovationQualityContractService.saveOrUpdateInnovationQualityContract(innovationQualityContract);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除省质检中心筹建任务书(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除省质检中心筹建任务书", notes = "删除省质检中心筹建任务书")
	@SystemLogAnnotation(type = "省质检中心筹建任务书",value = "删除省质检中心筹建任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteInnovationQualityContract(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		innovationQualityContractService.deleteInnovationQualityContract(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除省质检中心筹建任务书(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除省质检中心筹建任务书", notes = "删除省质检中心筹建任务书")
	@SystemLogAnnotation(type = "省质检中心筹建任务书",value = "删除省质检中心筹建任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiInnovationQualityContract(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innovationQualityContractService.deleteMultiInnovationQualityContract(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询省质检中心筹建任务书详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询省质检中心筹建任务书详情", notes = "查询省质检中心筹建任务书详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InnovationQualityContract  innovationQualityContract=innovationQualityContractService.findById(id);
		return RestApiResponse.ok(innovationQualityContract);
	}
	
	/**
	 *@Description: 分页查询省质检中心筹建任务书
	 *@param innovationQualityContractVo 省质检中心筹建任务书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询省质检中心筹建任务书", notes = "分页查询省质检中心筹建任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnovationQualityContractVo innovationQualityContractVo) {
		PageInfo<InnovationQualityContract>  innovationQualityContract=innovationQualityContractService.findPageByQuery(innovationQualityContractVo);
		return RestApiResponse.ok(innovationQualityContract);
	}

	/**
	 *@Description: 省质检中心筹建任务书提交
	 *@param vo 省质检中心筹建任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "省质检中心筹建任务书提交", notes = "省质检中心筹建任务书提交")
	@SystemLogAnnotation(type = "省质检中心筹建任务书",value = "省质检中心筹建任务书提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitInnovationQualityContract(@RequestBody InnovationQualityContractVo vo) {
		if(StringUtils.isBlank(vo.getOrgName())){
			vo.setOrgName(getCurrentOrgName());
			vo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityContractService.submitInnovationQualityContract(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 省质检中心筹建任务书审核
	 *@param innovationQualityContractVo 省质检中心筹建任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "省质检中心筹建任务书审核", notes = "省质检中心筹建任务书审核")
	@SystemLogAnnotation(type = "省质检中心筹建任务书",value = "省质检中心筹建任务书审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditInnovationQualityContract(@RequestBody InnovationQualityContractVo innovationQualityContractVo) {
		String id = innovationQualityContractService.auditInnovationQualityContract(innovationQualityContractVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心筹建任务书退回
	 *@param innovationQualityContractVo 省质检中心筹建任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "省质检中心筹建任务书退回", notes = "省质检中心筹建任务书退回")
	@SystemLogAnnotation(type = "省质检中心筹建任务书",value = "省质检中心筹建任务书退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackInnovationQualityContract(@RequestBody InnovationQualityContractVo innovationQualityContractVo) {
		String id = innovationQualityContractService.sendBackInnovationQualityContract(innovationQualityContractVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心筹建任务书任务书下达
	 *@param innovationQualityContractVo 省质检中心筹建任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "省质检中心筹建任务书任务书下达", notes = "省质检中心筹建任务书任务书下达")
	@SystemLogAnnotation(type = "省质检中心筹建任务书",value = "省质检中心筹建任务书任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseInnovationQualityContract(@RequestBody InnovationQualityContractVo innovationQualityContractVo) {
		String id = innovationQualityContractService.releaseInnovationQualityContract(innovationQualityContractVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心筹建任务书待办
	 *@param vo 省质检中心筹建任务书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "省质检中心筹建任务书待办", notes = "省质检中心筹建任务书待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody InnovationQualityContractVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = innovationQualityContractService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = innovationQualityContractService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = innovationQualityContractService.endList(vo);
		} else {
			list = innovationQualityContractService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 省质检中心筹建任务书已办
	 *@param innovationQualityContractVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "省质检中心筹建任务书已办", notes = "省质检中心筹建任务书已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody InnovationQualityContractVo innovationQualityContractVo) {
		PageInfo<InnovationQualityContract>  innovationQualityContract=innovationQualityContractService.finishedList(innovationQualityContractVo);
		return RestApiResponse.ok(innovationQualityContract);
	}

	/**
	 *@Description: 省质检中心筹建任务书已完成
	 *@param innovationQualityContractVo 省质检中心筹建任务书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "省质检中心筹建任务书已完成", notes = "省质检中心筹建任务书已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody InnovationQualityContractVo innovationQualityContractVo) {
		PageInfo<InnovationQualityContract>  innovationQualityContract=innovationQualityContractService.endList(innovationQualityContractVo);
		return RestApiResponse.ok(innovationQualityContract);
	}

	/**
	 *@Description: 省质检中心筹建任务书可变更
	 *@param innovationQualityContractVo 省质检中心筹建任务书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/changeAble")
	@ApiOperation(value = "省质检中心筹建任务书可变更", notes = "省质检中心筹建任务书可变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> changeAbleList(@RequestBody InnovationQualityContractVo innovationQualityContractVo) {
		PageInfo<InnovationQualityContract> innovationQualityContract=innovationQualityContractService.changeAbleList(innovationQualityContractVo);
		return RestApiResponse.ok(innovationQualityContract);
	}


	/**
	 *@Description: 省质检中心筹建任务书已验收
	 *@param innovationQualityContractVo 省质检中心筹建任务书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/accepted")
	@ApiOperation(value = "省质检中心筹建任务书已验收", notes = "省质检中心筹建任务书已验收")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> acceptedList(@RequestBody InnovationQualityContractVo innovationQualityContractVo) {
		PageInfo<InnovationQualityContract> innovationQualityContract=innovationQualityContractService.acceptedList(innovationQualityContractVo);
		return RestApiResponse.ok(innovationQualityContract);
	}
}
