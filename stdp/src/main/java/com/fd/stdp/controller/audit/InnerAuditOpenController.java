package com.fd.stdp.controller.audit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.audit.InnerAuditOpen;
import com.fd.stdp.beans.audit.vo.InnerAuditOpenVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.audit.InnerAuditOpenService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 内审开放功能配置
 *@Author: wangsh
 *@Date: 2022-02-23 15:56:20
 */
@RestController
@RequestMapping("/audit/innerAuditOpen")
@Api(value="内审开放功能配置", description="内审开放功能配置")
public class InnerAuditOpenController  extends BaseController {

	@Autowired
	private InnerAuditOpenService innerAuditOpenService;
	
	private final String PER_PREFIX = "but:innerAudit:open:";
	
	/**
	 *@Description: 新增内审开放功能配置
	 *@param innerAuditOpenVo 内审开放功能配置数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增内审开放功能配置", notes = "新增内审开放功能配置")
	@SystemLogAnnotation(type = "内审开放功能配置",value = "新增内审开放功能配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInnerAuditOpen(@RequestBody InnerAuditOpenVo innerAuditOpenVo) {
		String id = innerAuditOpenService.saveOrUpdateInnerAuditOpen(innerAuditOpenVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改内审开放功能配置
	 *@param innerAuditOpen 内审开放功能配置数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改内审开放功能配置", notes = "修改内审开放功能配置")
	@SystemLogAnnotation(type = "内审开放功能配置",value = "修改内审开放功能配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInnerAuditOpen(@RequestBody InnerAuditOpenVo innerAuditOpen) {
		String id = innerAuditOpenService.saveOrUpdateInnerAuditOpen(innerAuditOpen);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除内审开放功能配置(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除内审开放功能配置", notes = "删除内审开放功能配置")
	@SystemLogAnnotation(type = "内审开放功能配置",value = "删除内审开放功能配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteInnerAuditOpen(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		innerAuditOpenService.deleteInnerAuditOpen(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除内审开放功能配置(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除内审开放功能配置", notes = "删除内审开放功能配置")
	@SystemLogAnnotation(type = "内审开放功能配置",value = "删除内审开放功能配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiInnerAuditOpen(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innerAuditOpenService.deleteMultiInnerAuditOpen(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询内审开放功能配置详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询内审开放功能配置详情", notes = "查询内审开放功能配置详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InnerAuditOpen  innerAuditOpen=innerAuditOpenService.findById(id);
		return RestApiResponse.ok(innerAuditOpen);
	}
	
	/**
	 *@Description: 分页查询内审开放功能配置
	 *@param innerAuditOpenVo 内审开放功能配置 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询内审开放功能配置", notes = "分页查询内审开放功能配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnerAuditOpenVo innerAuditOpenVo) {
		PageInfo<InnerAuditOpen>  innerAuditOpen=innerAuditOpenService.findPageByQuery(innerAuditOpenVo);
		return RestApiResponse.ok(innerAuditOpen);
	}

	/**
	 *@Description: 修改内审开放功能配置
	 *@param innerAuditOpen 内审开放功能配置数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/open")
	@ApiOperation(value = "修改内审开放功能配置", notes = "修改内审开放功能配置")
	@SystemLogAnnotation(type = "内审开放功能配置",value = "修改内审开放功能配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> openInnerAuditOpen(@RequestBody InnerAuditOpenVo innerAuditOpen) {
		String id = innerAuditOpenService.open(innerAuditOpen);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 修改内审开放功能配置
	 *@param innerAuditOpen 内审开放功能配置数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/close")
	@ApiOperation(value = "修改内审开放功能配置", notes = "修改内审开放功能配置")
	@SystemLogAnnotation(type = "内审开放功能配置",value = "修改内审开放功能配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> closeInnerAuditOpen(@RequestBody InnerAuditOpenVo innerAuditOpen) {
		String id = innerAuditOpenService.close(innerAuditOpen);
		return RestApiResponse.ok(id);
	}


	/**
	 *@Description: 查询内审开放功能
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/isOpen")
	@ApiOperation(value = "查询内审开放功能", notes = "查询内审开放功能")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> isOpen(@RequestParam("id") String id) {
		return RestApiResponse.ok(innerAuditOpenService.isOpen());
	}
}
