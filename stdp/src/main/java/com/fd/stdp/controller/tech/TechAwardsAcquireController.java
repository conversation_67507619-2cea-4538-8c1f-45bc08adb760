package com.fd.stdp.controller.tech;

import com.fd.stdp.beans.basic.BasicPersonExpertRecommend;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertRecommendVo;
import com.fd.stdp.beans.tech.vo.TechAchievementVo;
import com.fd.stdp.beans.tech.vo.TechAwardsAcquireVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.tech.TechAwardsAcquire;
import com.fd.stdp.beans.tech.vo.TechAwardsAcquireVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.tech.TechAwardsAcquireService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 奖项获取
 *@Author: wangsh
 *@Date: 2022-01-04 13:49:48
 */
@RestController
@RequestMapping("/tech/techAwardsAcquire")
@Api(value="奖项获取", description="奖项获取")
public class TechAwardsAcquireController  extends BaseController {

	@Autowired
	private TechAwardsAcquireService techAwardsAcquireService;
	
	private final String PER_PREFIX = "tech:but:acquire:";
	
	/**
	 *@Description: 新增奖项获取
	 *@param techAwardsAcquire 奖项获取数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增奖项获取", notes = "新增奖项获取")
	@SystemLogAnnotation(type = "奖项获取",value = "新增奖项获取")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTechAwardsAcquire(@RequestBody TechAwardsAcquireVo techAwardsAcquire) {
		techAwardsAcquire.setOrgName(getCurrentOrgName());
		techAwardsAcquire.setOrgCode(getCurrentScienceOrgId());
		String id = techAwardsAcquireService.saveOrUpdateTechAwardsAcquire(techAwardsAcquire);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改奖项获取
	 *@param techAwardsAcquire 奖项获取数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改奖项获取", notes = "修改奖项获取")
	@SystemLogAnnotation(type = "奖项获取",value = "修改奖项获取")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTechAwardsAcquire(@RequestBody TechAwardsAcquireVo techAwardsAcquire) {
		String id = techAwardsAcquireService.saveOrUpdateTechAwardsAcquire(techAwardsAcquire);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除奖项获取(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除奖项获取", notes = "删除奖项获取")
	@SystemLogAnnotation(type = "奖项获取",value = "删除奖项获取")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTechAwardsAcquire(@RequestBody List<String> ids) {
		techAwardsAcquireService.deleteMultiTechAwardsAcquire(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询奖项获取详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询奖项获取详情", notes = "查询奖项获取详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		TechAwardsAcquire  techAwardsAcquire=techAwardsAcquireService.findById(id);
		return RestApiResponse.ok(techAwardsAcquire);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			TechAwardsAcquireVo vo = new TechAwardsAcquireVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoPageByQuery(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<TechAwardsAcquire> pageInfo = (PageInfo<TechAwardsAcquire>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}

	/**
	 *@Description: 分页查询奖项获取
	 *@param techAwardsAcquireVo 奖项获取 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询奖项获取", notes = "分页查询奖项获取")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TechAwardsAcquireVo techAwardsAcquireVo) {
		PageInfo<TechAwardsAcquire>  techAwardsAcquire=techAwardsAcquireService.findPageByQuery(techAwardsAcquireVo);
		return RestApiResponse.ok(techAwardsAcquire);
	}

	/**
	 *@Description: 提交奖项获取
	 *@param techAwardsAcquireVo 奖项获取数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "提交奖项获取", notes = "提交奖项获取")
	@SystemLogAnnotation(type = "奖项获取",value = "提交奖项获取")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTechAchievement(@RequestBody TechAwardsAcquireVo techAwardsAcquireVo) {
		techAwardsAcquireVo.setOrgName(getCurrentOrgName());
		techAwardsAcquireVo.setOrgCode(getCurrentScienceOrgId());
		String id = techAwardsAcquireService.submitTechAwardsAcquire(techAwardsAcquireVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 审核奖项获取
	 *@param techAwardsAcquireVo 奖项获取数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "审核奖项获取", notes = "审核奖项获取")
	@SystemLogAnnotation(type = "奖项获取",value = "审核奖项获取")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTechAchievement(@RequestBody TechAwardsAcquireVo techAwardsAcquireVo) {
		String id = techAwardsAcquireService.auditTechAwardsAcquire(techAwardsAcquireVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 退回奖项获取
	 *@param techAwardsAcquireVo 奖项获取数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "退回奖项获取", notes = "退回奖项获取")
	@SystemLogAnnotation(type = "奖项获取",value = "退回奖项获取")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTechAchievement(@RequestBody TechAwardsAcquireVo techAwardsAcquireVo) {
		String id = techAwardsAcquireService.sendBackTechAwardsAcquire(techAwardsAcquireVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 奖项获取待办
	 *@param vo 奖项获取 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "奖项获取待办", notes = "奖项获取待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoPageByQuery(@RequestBody TechAwardsAcquireVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = techAwardsAcquireService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = techAwardsAcquireService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = techAwardsAcquireService.endList(vo);
		} else {
			list = techAwardsAcquireService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}

	/**
	 *@Description: 分页查询奖项获取
	 *@param techAwardsAcquireVo 奖项获取 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "奖项获取已办", notes = "奖项获取已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finishedPageByQuery(@RequestBody TechAwardsAcquireVo techAwardsAcquireVo) {
		PageInfo<TechAwardsAcquire> techAwardsAcquire=techAwardsAcquireService.finishedList(techAwardsAcquireVo);
		return RestApiResponse.ok(techAwardsAcquire);
	}

	/**
	 *@Description: 奖项获取导出
	 *@param vo 奖项获取数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/export")
	@ApiOperation(value = "奖项获取导出", notes = "奖项获取导出")
	@SystemLogAnnotation(type = "奖项获取",value = "奖项获取导出")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"todo')")
	public void exportTechAchievement(@RequestBody TechAwardsAcquireVo vo, HttpServletResponse response) {
		techAwardsAcquireService.exportTechAchievement(vo, response);
	}
}
