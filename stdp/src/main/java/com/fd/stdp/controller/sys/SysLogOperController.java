package com.fd.stdp.controller.sys;

import com.fd.stdp.beans.sys.SysLogOper;
import com.fd.stdp.beans.sys.vo.SysLogOperExportVo;
import com.fd.stdp.beans.sys.vo.SysLogOperVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.service.sys.SysLogOperService;
import com.fd.stdp.util.EasyExcelUtils;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: 用户操作日志表
 * @Author: szx
 * @Date: 2020-07-07 17:51:45
 */
@RestController
@RequestMapping("/sys/sysLogOper")
@Api(value = "用户操作日志表", description = "用户操作日志表")
public class SysLogOperController extends BaseController {

    @Autowired
    private SysLogOperService sysLogOperService;

    private final String PER_PREFIX = "btn:sys:operlog:";

    /**
     * @param sysLogOpervo 用户操作日志表 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询用户操作日志表
     * @Author: szx
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询用户操作日志表", notes = "分页查询用户操作日志表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody SysLogOperVo sysLogOperVo) {
        PageInfo<SysLogOper> sysLogOper = sysLogOperService.findPageByQuery(sysLogOperVo);
        return RestApiResponse.ok(sysLogOper);
    }

    /**
     * @param sysLogOperVo void
     * @Description:导出操作日志
     * @Author: szx
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出操作日志", notes = "导出操作日志")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "export')")
    public void export(SysLogOperVo sysLogOperVo) {
        List<SysLogOperExportVo> list = sysLogOperService.export(sysLogOperVo);
        EasyExcelUtils.exportExcel(list, "导出操作日志结果", SysLogOperExportVo.class, "导出操作日志结果", response);
    }

}
