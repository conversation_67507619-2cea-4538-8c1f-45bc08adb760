package com.fd.stdp.controller.work;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.work.WorkActivityWeek;
import com.fd.stdp.beans.work.vo.WorkActivityWeekVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.work.WorkActivityWeekService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 科技活动周开展情况Controller
 *
 * <AUTHOR>
 * @date 2021-11-12
 */
@RestController
@RequestMapping("/work/activityWeek")
@Api(value = "科技活动周开展情况", description = "科技活动周开展情况")
public class WorkActivityWeekController extends BaseController {
    @Autowired
    private WorkActivityWeekService workActivityWeekService;

    private final String PER_PREFIX = "btn:work:activityWeek:";

    /**
     * @param workActivityWeek 科技活动周开展情况数据 json
     * @return RestApiResponse<?>
     * @Description: 新增科技活动周开展情况
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增科技活动周开展情况", notes = "新增科技活动周开展情况")
    @SystemLogAnnotation(type = "科技活动周开展情况", value = "新增科技活动周开展情况")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveWorkActivityWeek(@RequestBody WorkActivityWeek workActivityWeek) {
        String id = workActivityWeekService.saveOrUpdateWorkActivityWeek(workActivityWeek);
        return RestApiResponse.ok(id);
    }

    /**
     * @param workActivityWeek 科技活动周开展情况数据 json
     * @return RestApiResponse<?>
     * @Description: 修改科技活动周开展情况
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改科技活动周开展情况", notes = "修改科技活动周开展情况")
    @SystemLogAnnotation(type = "科技活动周开展情况", value = "修改科技活动周开展情况")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateWorkActivityWeek(@RequestBody WorkActivityWeek workActivityWeek) {
        String id = workActivityWeekService.saveOrUpdateWorkActivityWeek(workActivityWeek);
        return RestApiResponse.ok(id);
    }

    /**
     * @param ids
     * @return RestApiResponse<?>
     * @Description: 批量删除科技活动周开展情况(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除科技活动周开展情况", notes = "批量删除科技活动周开展情况")
    @SystemLogAnnotation(type = "科技活动周开展情况", value = "批量删除科技活动周开展情况")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteWorkActivityWeek(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        workActivityWeekService.deleteWorkActivityWeek(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询科技活动周开展情况详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询科技活动周开展情况详情", notes = "查询科技活动周开展情况详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        WorkActivityWeek workActivityWeek = workActivityWeekService.findById(id);
        return RestApiResponse.ok(workActivityWeek);
    }

    /**
     * @param workActivityWeekVo 科技活动周开展情况 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询科技活动周开展情况
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询科技活动周开展情况", notes = "分页查询科技活动周开展情况")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody WorkActivityWeekVo workActivityWeekVo) {
        PageInfo<WorkActivityWeek> workActivityWeek = workActivityWeekService.findPageByQuery(workActivityWeekVo);
        return RestApiResponse.ok(workActivityWeek);
    }

    /**
     * @param workActivityWeekVo 科技活动周开展情况 查询条件
     * @return RestApiResponse<?>
     * @Description: 查询统计
     * @Author: yujianfei
     */
    @PostMapping("/findStatisticByQuery")
    @ApiOperation(value = "查询统计科技活动周开展情况", notes = "查询统计科技活动周开展情况")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findStatisticByQuery')")
    public RestApiResponse<?> findStatisticByQuery(@RequestBody WorkActivityWeekVo workActivityWeekVo) {
        PageInfo<WorkActivityWeekVo> workActivityWeek = workActivityWeekService.findStatisticByQuery(workActivityWeekVo);
        return RestApiResponse.ok(workActivityWeek);
    }

}
