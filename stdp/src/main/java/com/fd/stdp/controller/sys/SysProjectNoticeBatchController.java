package com.fd.stdp.controller.sys;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.sys.SysProjectNoticeBatch;
import com.fd.stdp.beans.sys.vo.SysProjectNoticeBatchVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.sys.SysProjectNoticeBatchService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 公告项目Controller
 *
 * <AUTHOR>
 * @date 2021-11-15
 */
@RestController
@RequestMapping("/sys/projectNoticeBatch")
@Api(value = "公告项目", description = "公告项目")
public class SysProjectNoticeBatchController extends BaseController {
    @Autowired
    private SysProjectNoticeBatchService sysProjectNoticeBatchService;

    private final String PER_PREFIX = "btn:sys:projectNoticeBatch:";

    /**
     * @param sysProjectNoticeBatch 公告项目数据 json
     * @return RestApiResponse<?>
     * @Description: 新增公告项目
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增公告项目", notes = "新增公告项目")
    @SystemLogAnnotation(type = "公告项目", value = "新增公告项目")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveSysProjectNoticeBatch(@RequestBody SysProjectNoticeBatch sysProjectNoticeBatch) {
        String id = sysProjectNoticeBatchService.saveOrUpdateSysProjectNoticeBatch(sysProjectNoticeBatch);
        return RestApiResponse.ok(id);
    }

    /**
     * @param sysProjectNoticeBatch 公告项目数据 json
     * @return RestApiResponse<?>
     * @Description: 修改公告项目
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改公告项目", notes = "修改公告项目")
    @SystemLogAnnotation(type = "公告项目", value = "修改公告项目")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateSysProjectNoticeBatch(@RequestBody SysProjectNoticeBatch sysProjectNoticeBatch) {
        String id = sysProjectNoticeBatchService.saveOrUpdateSysProjectNoticeBatch(sysProjectNoticeBatch);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除公告项目(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除公告项目", notes = "批量删除公告项目")
    @SystemLogAnnotation(type = "公告项目", value = "批量删除公告项目")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteSysProjectNoticeBatch(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        sysProjectNoticeBatchService.deleteSysProjectNoticeBatch(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询公告项目详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询公告项目详情", notes = "查询公告项目详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        SysProjectNoticeBatch sysProjectNoticeBatch = sysProjectNoticeBatchService.findById(id);
        return RestApiResponse.ok(sysProjectNoticeBatch);
    }

    /**
     * @param sysProjectNoticeBatchVo 公告项目 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询公告项目
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询公告项目", notes = "分页查询公告项目")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody SysProjectNoticeBatchVo sysProjectNoticeBatchVo) {
        PageInfo<SysProjectNoticeBatch> sysProjectNoticeBatch = sysProjectNoticeBatchService.findPageByQuery(sysProjectNoticeBatchVo);
        return RestApiResponse.ok(sysProjectNoticeBatch);
    }

}
