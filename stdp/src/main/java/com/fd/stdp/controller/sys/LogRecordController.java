package com.fd.stdp.controller.sys;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fd.stdp.beans.sys.SysLogRecord;
import com.fd.stdp.beans.sys.vo.SysLogRecordVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.service.sys.SysLogService;
import com.github.pagehelper.PageInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 
 * @Description: 日志记录
 * @Author: linqiang
 * @Date: 2018-08-07 13:54:34
 */
@RestController
@RequestMapping("/sys/logRecord")
@Api(value = "日志记录", description = "日志记录")
public class LogRecordController extends BaseController {

	@Autowired
	private SysLogService logRecordService;
	 private final String PER_PREFIX = "btn:sys:log:";
	/**
	 * 
	 * @Description: 新增日志记录
	 * @param logRecord 日志记录数据 json
	 * @return RestApiResponse<?>
	 * @Author: linqiang
	 */
	@PostMapping("/save")
	@ResponseBody
	@ApiOperation(value = "新增日志记录", notes = "新增日志记录")
	@PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
	public RestApiResponse<?> save(@RequestBody SysLogRecord logRecord) {
		String id = logRecordService.saveOrUpdateLogRecord(logRecord);
		return RestApiResponse.ok(id);
	}

	/**
	 * 
	 * @Description: 分页日志记录
	 * @param logRecordvo
	 * @return RestApiResponse<?>
	 * @Author: linqiang
	 */
	@PostMapping("/findAll")
	@ResponseBody
	@ApiOperation(value = "分页日志记录", notes = "分页日志记录")
	@PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
	public RestApiResponse<?> findAll(@RequestBody SysLogRecordVo logRecordvo) {
		PageInfo<SysLogRecord> loglist = logRecordService.findAllPage(logRecordvo);
		return RestApiResponse.ok(loglist);
	}

}
