package com.fd.stdp.controller.project;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectExtra;
import com.fd.stdp.beans.project.vo.ProjectExtraVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectExtraService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 外部项目
 *@Author: wangsh
 *@Date: 2022-03-28 19:32:44
 */
@RestController
@RequestMapping("/project/projectExtra")
@Api(value="外部项目", description="外部项目")
public class ProjectExtraController  extends BaseController {

	@Autowired
	private ProjectExtraService projectExtraService;
	
	private final String PER_PREFIX = "but:project:extra:";
	
	/**
	 *@Description: 新增外部项目
	 *@param projectExtraVo 外部项目数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增外部项目", notes = "新增外部项目")
	@SystemLogAnnotation(type = "外部项目",value = "新增外部项目")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveProjectExtra(@RequestBody ProjectExtraVo projectExtraVo) {
	    if(StringUtils.isBlank(projectExtraVo.getOrgName())) {
            projectExtraVo.setOrgName(getCurrentOrgName());
            projectExtraVo.setOrgCode(getLoginUser().getAreaCode());
		}
		String id = projectExtraService.saveOrUpdateProjectExtra(projectExtraVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改外部项目
	 *@param projectExtra 外部项目数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改外部项目", notes = "修改外部项目")
	@SystemLogAnnotation(type = "外部项目",value = "修改外部项目")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateProjectExtra(@RequestBody ProjectExtraVo projectExtra) {
		String id = projectExtraService.saveOrUpdateProjectExtra(projectExtra);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除外部项目(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除外部项目", notes = "删除外部项目")
	@SystemLogAnnotation(type = "外部项目",value = "删除外部项目")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteProjectExtra(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		projectExtraService.deleteProjectExtra(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除外部项目(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除外部项目", notes = "删除外部项目")
	@SystemLogAnnotation(type = "外部项目",value = "删除外部项目")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiProjectExtra(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		projectExtraService.deleteMultiProjectExtra(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询外部项目详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询外部项目详情", notes = "查询外部项目详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		ProjectExtra  projectExtra=projectExtraService.findById(id);
		return RestApiResponse.ok(projectExtra);
	}
	
	/**
	 *@Description: 分页查询外部项目
	 *@param projectExtraVo 外部项目 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询外部项目", notes = "分页查询外部项目")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody ProjectExtraVo projectExtraVo) {
		PageInfo<ProjectExtra>  projectExtra=projectExtraService.findPageByQuery(projectExtraVo);
		return RestApiResponse.ok(projectExtra);
	}
//
//	/**
//	 *@Description: 外部项目提交
//	 *@param projectExtraVo 外部项目数据 json
//	 *@return RestApiResponse<?>
//	 *@Author: wangsh
//	 */
//	@RepeatSubAnnotation
//	@PostMapping("/submit")
//	@ApiOperation(value = "外部项目提交", notes = "外部项目提交")
//	@SystemLogAnnotation(type = "外部项目",value = "外部项目提交")
//	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
//	public RestApiResponse<?> submitProjectExtra(@RequestBody ProjectExtraVo projectExtraVo) {
//	    if(StringUtils.isBlank(projectExtraVo.getOrgName())) {
//            projectExtraVo.setOrgName(getCurrentOrgName());
//            projectExtraVo.setOrgCode(getLoginUser().getAreaCode());
//		}
//		String id = projectExtraService.submitProjectExtra(projectExtraVo);
//		return RestApiResponse.ok(id);
//	}
//
//	/**
//	 *@Description: 外部项目审核
//	 *@param projectExtraVo 外部项目数据 json
//	 *@return RestApiResponse<?>
//	 *@Author: wangsh
//	 */
//	@RepeatSubAnnotation
//	@PostMapping("/audit")
//	@ApiOperation(value = "外部项目审核", notes = "外部项目审核")
//	@SystemLogAnnotation(type = "外部项目",value = "外部项目审核")
//	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
//	public RestApiResponse<?> auditProjectExtra(@RequestBody ProjectExtraVo projectExtraVo) {
//		String id = projectExtraService.auditProjectExtra(projectExtraVo);
//		return RestApiResponse.ok(id);
//	}
//
//	/**
//	 *@Description: 外部项目退回
//	 *@param projectExtraVo 外部项目数据 json
//	 *@return RestApiResponse<?>
//	 *@Author: wangsh
//	 */
//	@RepeatSubAnnotation
//	@PostMapping("/sendBack")
//	@ApiOperation(value = "外部项目退回", notes = "外部项目退回")
//	@SystemLogAnnotation(type = "外部项目",value = "外部项目退回")
//	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
//	public RestApiResponse<?> sendBackProjectExtra(@RequestBody ProjectExtraVo projectExtraVo) {
//		String id = projectExtraService.sendBackProjectExtra(projectExtraVo);
//		return RestApiResponse.ok(id);
//	}
//
//	/**
//	 *@Description: 外部项目任务书下达
//	 *@param projectExtraVo 外部项目数据 json
//	 *@return RestApiResponse<?>
//	 *@Author: wangsh
//	 */
//	@RepeatSubAnnotation
//	@PostMapping("/release")
//	@ApiOperation(value = "外部项目任务书下达", notes = "外部项目任务书下达")
//	@SystemLogAnnotation(type = "外部项目",value = "外部项目任务书下达")
//	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
//	public RestApiResponse<?> releaseProjectExtra(@RequestBody ProjectExtraVo projectExtraVo) {
//		String id = projectExtraService.releaseProjectExtra(projectExtraVo);
//		return RestApiResponse.ok(id);
//	}
//
//	/**
//	 *@Description: 外部项目待办
//	 *@param projectExtraVo 外部项目 查询条件
//	 *@return RestApiResponse<?>
//	 *@Author: wangsh
//	 */
//	@PostMapping("/todo")
//	@ApiOperation(value = "外部项目待办", notes = "外部项目待办")
//	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
//	public RestApiResponse<?> todoList(@RequestBody ProjectExtraVo projectExtraVo) {
//		PageInfo<ProjectExtra>  projectExtra=projectExtraService.todoList(projectExtraVo);
//		return RestApiResponse.ok(projectExtra);
//	}
//	/**
//	 *@Description: 外部项目已办
//	 *@param projectExtraVo 专家委员会库 查询条件
//	 *@return RestApiResponse<?>
//	 *@Author: wangsh
//	 */
//	@PostMapping("/finished")
//	@ApiOperation(value = "外部项目已办", notes = "外部项目已办")
//	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
//	public RestApiResponse<?> finished(@RequestBody ProjectExtraVo projectExtraVo) {
//		PageInfo<ProjectExtra>  projectExtra=projectExtraService.finishedList(projectExtraVo);
//		return RestApiResponse.ok(projectExtra);
//	}
//
//	/**
//	 *@Description: 外部项目已完成
//	 *@param projectExtraVo 外部项目 查询条件
//	 *@return RestApiResponse<?>
//	 *@Author: wangsh
//	 */
//	@PostMapping("/end")
//	@ApiOperation(value = "外部项目已完成", notes = "外部项目已完成")
//	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
//	public RestApiResponse<?> endList(@RequestBody ProjectExtraVo projectExtraVo) {
//		PageInfo<ProjectExtra>  projectExtra=projectExtraService.endList(projectExtraVo);
//		return RestApiResponse.ok(projectExtra);
//	}
}
