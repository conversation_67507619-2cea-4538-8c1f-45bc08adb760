package com.fd.stdp.controller.talent;

import com.fd.stdp.beans.innovation.InnovationOtherInnovationApply;
import com.fd.stdp.beans.innovation.vo.InnovationOtherInnovationApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectLeader;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectLeaderVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.talent.TalentLeaderSubjectLeaderService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 学科带头人申报书
 *@Author: wangsh
 *@Date: 2022-02-14 10:05:30
 */
@RestController
@RequestMapping("/talent/talentLeaderSubjectLeader")
@Api(value="学科带头人申报书", description="学科带头人申报书")
public class TalentLeaderSubjectLeaderController  extends BaseController {

	@Autowired
	private TalentLeaderSubjectLeaderService talentLeaderSubjectLeaderService;
	
	private final String PER_PREFIX = "talent:but:leader:";
	
	/**
	 *@Description: 新增学科带头人申报书
	 *@param vo 学科带头人申报书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增学科带头人申报书", notes = "新增学科带头人申报书")
	@SystemLogAnnotation(type = "学科带头人申报书",value = "新增学科带头人申报书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTalentLeaderSubjectLeader(@RequestBody TalentLeaderSubjectLeaderVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentLeaderSubjectLeaderService.saveOrUpdateTalentLeaderSubjectLeader(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改学科带头人申报书
	 *@param talentLeaderSubjectLeader 学科带头人申报书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改学科带头人申报书", notes = "修改学科带头人申报书")
	@SystemLogAnnotation(type = "学科带头人申报书",value = "修改学科带头人申报书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTalentLeaderSubjectLeader(@RequestBody TalentLeaderSubjectLeaderVo talentLeaderSubjectLeader) {
		String id = talentLeaderSubjectLeaderService.saveOrUpdateTalentLeaderSubjectLeader(talentLeaderSubjectLeader);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除学科带头人申报书(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除学科带头人申报书", notes = "删除学科带头人申报书")
	@SystemLogAnnotation(type = "学科带头人申报书",value = "删除学科带头人申报书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTalentLeaderSubjectLeader(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		talentLeaderSubjectLeaderService.deleteTalentLeaderSubjectLeader(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除学科带头人申报书(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除学科带头人申报书", notes = "删除学科带头人申报书")
	@SystemLogAnnotation(type = "学科带头人申报书",value = "删除学科带头人申报书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiTalentLeaderSubjectLeader(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		talentLeaderSubjectLeaderService.deleteMultiTalentLeaderSubjectLeader(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询学科带头人申报书详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询学科带头人申报书详情", notes = "查询学科带头人申报书详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		TalentLeaderSubjectLeader  talentLeaderSubjectLeader=talentLeaderSubjectLeaderService.findById(id);
		return RestApiResponse.ok(talentLeaderSubjectLeader);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			TalentLeaderSubjectLeaderVo vo = new TalentLeaderSubjectLeaderVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<TalentLeaderSubjectLeader> pageInfo = (PageInfo<TalentLeaderSubjectLeader>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}

	/**
	 *@Description: 分页查询学科带头人申报书
	 *@param talentLeaderSubjectLeaderVo 学科带头人申报书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询学科带头人申报书", notes = "分页查询学科带头人申报书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo) {
		PageInfo<TalentLeaderSubjectLeader>  talentLeaderSubjectLeader=talentLeaderSubjectLeaderService.findPageByQuery(talentLeaderSubjectLeaderVo);
		return RestApiResponse.ok(talentLeaderSubjectLeader);
	}

	/**
	 *@Description: 学科带头人申报书提交
	 *@param vo 学科带头人申报书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "学科带头人申报书提交", notes = "学科带头人申报书提交")
	@SystemLogAnnotation(type = "学科带头人申报书",value = "学科带头人申报书提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTalentLeaderSubjectLeader(@RequestBody TalentLeaderSubjectLeaderVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentLeaderSubjectLeaderService.submitTalentLeaderSubjectLeader(vo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 学科带头人申报书提交
	 *@param vo 学科带头人申报书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/expertSubmit")
	@ApiOperation(value = "学科带头人申报书提交", notes = "学科带头人申报书提交")
	@SystemLogAnnotation(type = "学科带头人申报书",value = "学科带头人申报书提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> expertSubmitTalentLeaderSubjectLeader(@RequestBody TalentLeaderSubjectLeaderVo vo) {
		String id = talentLeaderSubjectLeaderService.expertSubmitTalentLeaderSubjectLeader(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 学科带头人申报书审核
	 *@param talentLeaderSubjectLeaderVo 学科带头人申报书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "学科带头人申报书审核", notes = "学科带头人申报书审核")
	@SystemLogAnnotation(type = "学科带头人申报书",value = "学科带头人申报书审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTalentLeaderSubjectLeader(@RequestBody TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo) {
		String id = talentLeaderSubjectLeaderService.auditTalentLeaderSubjectLeader(talentLeaderSubjectLeaderVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 学科带头人申报书退回
	 *@param talentLeaderSubjectLeaderVo 学科带头人申报书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "学科带头人申报书退回", notes = "学科带头人申报书退回")
	@SystemLogAnnotation(type = "学科带头人申报书",value = "学科带头人申报书退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTalentLeaderSubjectLeader(@RequestBody TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo) {
		String id = talentLeaderSubjectLeaderService.sendBackTalentLeaderSubjectLeader(talentLeaderSubjectLeaderVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 学科带头人申报书任务书下达
	 *@param talentLeaderSubjectLeaderVo 学科带头人申报书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "学科带头人申报书任务书下达", notes = "学科带头人申报书任务书下达")
	@SystemLogAnnotation(type = "学科带头人申报书",value = "学科带头人申报书任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseTalentLeaderSubjectLeader(@RequestBody TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo) {
		String id = talentLeaderSubjectLeaderService.releaseTalentLeaderSubjectLeader(talentLeaderSubjectLeaderVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 学科带头人申报书待办
	 *@param vo 学科带头人申报书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "学科带头人申报书待办", notes = "学科带头人申报书待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody TalentLeaderSubjectLeaderVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = talentLeaderSubjectLeaderService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = talentLeaderSubjectLeaderService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = talentLeaderSubjectLeaderService.findPageByQuery(vo);
		} else {
			list = talentLeaderSubjectLeaderService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 学科带头人申报书已办
	 *@param talentLeaderSubjectLeaderVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "学科带头人申报书已办", notes = "学科带头人申报书已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo) {
		PageInfo<TalentLeaderSubjectLeader>  talentLeaderSubjectLeader=talentLeaderSubjectLeaderService.finishedList(talentLeaderSubjectLeaderVo);
		return RestApiResponse.ok(talentLeaderSubjectLeader);
	}
}
