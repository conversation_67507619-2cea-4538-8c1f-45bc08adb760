package com.fd.stdp.controller.sys;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.nio.charset.Charset;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.fd.stdp.common.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.alibaba.fastjson.JSONObject;
import com.fd.stdp.beans.sys.SysFileInfo;
import com.fd.stdp.config.FileServiceFactory;
import com.fd.stdp.service.sys.FileService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/sys/baidu/ueditor")
@Api(value = "百度富文本", description = "百度富文本")
public class BaiDuUeditorController extends BaseController {
	@Autowired
	private FileServiceFactory fileServiceFactory;

	@Value("${file.useFileType}")
	private String useFileType;
	@Value("${file.filePath}")
	private String filePath;
	/**
	 * getconfig
	 * 
	 * @return
	 */
	@RequestMapping(value = "/config", method = { RequestMethod.GET, RequestMethod.POST })
	@ApiOperation(value = "getconfig", notes = "getconfig")
	@ResponseBody
	public void config(HttpServletRequest request, HttpServletResponse response, String action) {
		try {
			if ("config".equals(action)) { // 如果是初始化
				ClassPathResource classPathResource = new ClassPathResource("config.json");
				response.setContentType("text/javascript");
				String callback = request.getParameter("callback");
				String exec = extracted(classPathResource)
						.toString();
				PrintWriter writer = response.getWriter();
				System.out.println(exec);
				writer.write(callback + "(" + exec + ");");
				writer.flush();
				writer.close();
			} else if ("uploadimage".equals(action) || "uploadvideo".equals(action) || "uploadfile".equals(action)) { // 如果是上传图片、视频、和其他文件
				response.setContentType("application/json;charset=utf-8");
				MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
				Map<String, MultipartFile> files = multipartRequest.getFileMap();// 得到文件map对象
				for (MultipartFile pic : files.values()) {
					JSONObject jo = new JSONObject();
					long size = pic.getSize(); // 文件大小
					String originalFilename = pic.getOriginalFilename(); // 原来的文件名
					FileService fileService = fileServiceFactory.getFileService(useFileType);
					SysFileInfo fileInfo = fileService.upload(pic);
					if (fileInfo != null && !"".equals(fileInfo.getUrl())) { // 如果上传成功
						jo.put("state", "SUCCESS");
						jo.put("original", originalFilename);// 原来的文件名
						jo.put("size", size); // 文件大小
						jo.put("title", fileInfo.getUrl()); // 随意，代表的是鼠标经过图片时显示的文字
						jo.put("type", originalFilename.substring(originalFilename.lastIndexOf(".") + 1)); // 文件后缀名
						jo.put("url", filePath+fileInfo.getUrl());// 这里的url字段表示的是上传后的图片在图片服务器的完整地址（http://ip:端口/***/***/***.jpg）
					} else { // 如果上传失败
					}
					PrintWriter writer = response.getWriter();
					writer.write(jo.toString());
					writer.flush();
					writer.close();
				}
			}
		} catch (Exception e) {
			logger.error("请求配置是出现问题{},请求路径是/config",e.getStackTrace());
		}

	}
	private Object extracted(ClassPathResource classPathResource) throws IOException {
		InputStream in=classPathResource.getInputStream();
		return JSONObject
				.parseObject(in, Charset.forName("UTF-8"), null);
	}
}
