package com.fd.stdp.controller.project;

import java.util.List;

import com.fd.stdp.beans.appraisal.vo.AppraisalApplyVo;
import com.fd.stdp.beans.flowable.FlowTaskDto;
import com.fd.stdp.beans.project.ProjectDemandCollectionGuid;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.project.ProjectDemandCollectionApply;
import com.fd.stdp.beans.project.vo.ProjectDemandCollectionApplyVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectDemandCollectionApplyService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 需求征集申请Controller
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
@RestController
@RequestMapping("/project/demandCollectionApply")
@Api(value = "需求征集申请", description = "需求征集申请")
public class ProjectDemandCollectionApplyController extends BaseController {
    @Autowired
    private ProjectDemandCollectionApplyService projectDemandCollectionApplyService;

    private final String PER_PREFIX = "btn:project:demandCollectionApply:";

    /**
     * @param projectDemandCollectionApplyVo 需求征集申请数据 json
     * @return RestApiResponse<?>
     * @Description: 新增需求征集申请
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增需求征集申请", notes = "新增需求征集申请")
    @SystemLogAnnotation(type = "需求征集申请", value = "新增需求征集申请")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveProjectDemandCollectionApply(@RequestBody ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        String id = projectDemandCollectionApplyService.saveOrUpdateProjectDemandCollectionApply(projectDemandCollectionApplyVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectDemandCollectionApplyVo 需求征集申请数据 json
     * @return RestApiResponse<?>
     * @Description: 修改需求征集申请
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改需求征集申请", notes = "修改需求征集申请")
    @SystemLogAnnotation(type = "需求征集申请", value = "修改需求征集申请")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateProjectDemandCollectionApply(@RequestBody ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        String id = projectDemandCollectionApplyService.saveOrUpdateProjectDemandCollectionApply(projectDemandCollectionApplyVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param ids
     * @return RestApiResponse<?>
     * @Description: 批量删除需求征集申请(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除需求征集申请", notes = "批量删除需求征集申请")
    @SystemLogAnnotation(type = "需求征集申请", value = "批量删除需求征集申请")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteProjectDemandCollectionApply(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        projectDemandCollectionApplyService.deleteProjectDemandCollectionApply(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询需求征集申请详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询需求征集申请详情", notes = "查询需求征集申请详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ProjectDemandCollectionApply projectDemandCollectionApply = projectDemandCollectionApplyService.findById(id);
        return RestApiResponse.ok(projectDemandCollectionApply);
    }

    /**
     * @param projectDemandCollectionApplyVo 需求征集申请 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询需求征集申请
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询需求征集申请", notes = "分页查询需求征集申请")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        PageInfo<ProjectDemandCollectionApply> projectDemandCollectionApply = projectDemandCollectionApplyService.findPageByQuery(projectDemandCollectionApplyVo);
        return RestApiResponse.ok(projectDemandCollectionApply);
    }


    /**
     * @param projectDemandCollectionApplyVo 需求征集数据 json
     * @return RestApiResponse<?>
     * @Description: 审核
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/audit")
    @ApiOperation(value = "审核", notes = "审核")
    @SystemLogAnnotation(type = "审核", value = "审核")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "audit')")
    public RestApiResponse<?> audit(@RequestBody ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        projectDemandCollectionApplyService.audit(projectDemandCollectionApplyVo);
        return RestApiResponse.ok();
    }

    /**
     * @param projectDemandCollectionApplyVo 需求征集数据 json
     * @return RestApiResponse<?>
     * @Description: 审核
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/toExpert")
    @ApiOperation(value = "指派专家", notes = "指派专家")
    @SystemLogAnnotation(type = "指派专家", value = "指派专家")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "audit')")
    public RestApiResponse<?> toExpert(@RequestBody ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        projectDemandCollectionApplyService.audit(projectDemandCollectionApplyVo);
        return RestApiResponse.ok();
    }

    /**
     * 整合
     * @param projectDemandCollectionApplyVo
     * @return
     */
    @RepeatSubAnnotation
    @PostMapping("/intergrate")
    @ApiOperation(value = "整合", notes = "整合")
    @SystemLogAnnotation(type = "整合", value = "整合")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "intergrate')")
    public RestApiResponse<?> intergrate(@RequestBody ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        projectDemandCollectionApplyService.intergrate(projectDemandCollectionApplyVo);
        return RestApiResponse.ok();
    }


    /**
     * @param projectDemandCollectionApplyVo 需求征集数据 json
     * @return RestApiResponse<?>
     * @Description: 退回
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/sendBack")
    @ApiOperation(value = "退回", notes = "退回")
    @SystemLogAnnotation(type = "退回", value = "退回")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "sendBack')")
    public RestApiResponse<?> sendBack(@RequestBody ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        projectDemandCollectionApplyService.sendBack(projectDemandCollectionApplyVo);
        return RestApiResponse.ok();
    }


    /**
     * 代办列表
     * @return
     */
    @RepeatSubAnnotation
    @PostMapping("/todoList")
    @ApiOperation(value = "代办列表", notes = "代办列表")
    @SystemLogAnnotation(type = "代办列表", value = "代办列表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "todoList')")
    public RestApiResponse<?> todoList(@RequestBody ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        return RestApiResponse.ok(projectDemandCollectionApplyService.todoList(projectDemandCollectionApplyVo, getCurrentUserId()));
        
//        PageInfo<ProjectDemandCollectionApply> projectDemandCollectionApply =projectDemandCollectionApplyService.todoList(projectDemandCollectionApplyVo);
//		return RestApiResponse.ok(projectDemandCollectionApply);
    }


    /**
     * 已办列表
     * @param projectDemandCollectionApplyVo
     * @return
     */
    @RepeatSubAnnotation
    @PostMapping("/finishedList")
    @ApiOperation(value = "已办列表", notes = "已办列表")
    @SystemLogAnnotation(type = "已办列表", value = "已办列表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "finishedList')")
    public RestApiResponse<?> finishedList(@RequestBody ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        return RestApiResponse.ok(projectDemandCollectionApplyService.finishedList(projectDemandCollectionApplyVo, getCurrentUserId()));
    }

    /**
     * @param ids
     * @return RestApiResponse<?>
     * @Description: 批量删除需求征集申请(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/intergrateData")
    @ApiOperation(value = "整合数据", notes = "整合数据")
    @SystemLogAnnotation(type = "整合数据", value = "整合数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "intergrate')")
    public RestApiResponse<?> intergrateData(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        ProjectDemandCollectionGuid guid = projectDemandCollectionApplyService.intergrateData(ids);
        return RestApiResponse.ok(guid);
    }

}
