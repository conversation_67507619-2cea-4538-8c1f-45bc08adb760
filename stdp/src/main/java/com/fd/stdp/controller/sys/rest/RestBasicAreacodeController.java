package com.fd.stdp.controller.sys.rest;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fd.stdp.beans.sys.AreaTree;
import com.fd.stdp.beans.sys.BasicAreacode;
import com.fd.stdp.beans.sys.vo.AreaCodeVo;
import com.fd.stdp.service.sys.BasicAreacodeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/sys/area")
@Api(value = "行政区划RESTAPI", description = "行政区划")
public class RestBasicAreacodeController {

    @Autowired
    private BasicAreacodeService basicAreacodeService;

    /**
     * @Description 根据省Code初始化行政区划树
     * @param code
     * @return List<DictTree>
     */
    @GetMapping("/rest/tree")
    @ResponseBody
    @ApiOperation(value = "根据省Code初始化行政区划树", notes = "根据省Code初始化行政区划树")
    public List<AreaTree> getTree(@RequestParam(value = "code", required = false) String code) {
        List<AreaTree> tree = basicAreacodeService.getTree(code);
        return tree;
    }

    /**
     * @Description:根据区划CODE得到下级下拉 （post）
     * @param code
     * @param level
     * @return List<Map<String,String>> * @throws
     * @linqiang
     */
    @PostMapping("/rest/listFindByCode")
    @ResponseBody
    @ApiOperation(value = "根据区划CODE得到下级下拉（post）", notes = "根据区划CODE得到下级下拉（post）")
    public List<Map<String, String>> listFindByCode(@RequestParam(value = "code") String code,
            @RequestParam(value = "level") String level) {
        return basicAreacodeService.findLevelByCode(code, level);
    }

    /**
     * 查找所有省行政区划
     */
    @GetMapping("/rest/findAllProvice")
    @ResponseBody
    @ApiOperation(value = "查找所有省行政区划", notes = "查找所有省行政区划")
    public List<Map<String, String>> findAllProvice() {
        return basicAreacodeService.findAllProvice();
    }

    /**
     * @Description 根据区划CODE得到NAME 
     * @param code
     * @return String * @throws
     */
    @GetMapping("/rest/findNameByCode")
    @ResponseBody
    @ApiOperation(value = "根据区划CODE得到NAME", notes = "根据区划CODE得到NAME")
    public String findNameByCode(@RequestParam(value = "code") String code) {
        return basicAreacodeService.findNameByCode(code);
    }

    /**
     * @Description 查询行政区划
     * @param code
     * @return String * @throws
     */
    @GetMapping("/rest/listArea")
    @ResponseBody
    @ApiOperation(value = "查询行政区划", notes = "查询行政区划")
    public List<AreaCodeVo> listArea(@RequestParam(value = "code", required = false) String code) {
        return basicAreacodeService.listArea(code);
    }
    
}
