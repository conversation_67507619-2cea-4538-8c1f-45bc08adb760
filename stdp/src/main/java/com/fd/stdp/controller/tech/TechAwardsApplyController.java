package com.fd.stdp.controller.tech;

import com.fd.stdp.beans.tech.TechAchievement;
import com.fd.stdp.beans.tech.vo.TechAchievementVo;
import com.fd.stdp.beans.tech.vo.TechAwardsApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.tech.TechAwardsApply;
import com.fd.stdp.beans.tech.vo.TechAwardsApplyVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.tech.TechAwardsApplyService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 奖项申报
 *@Author: wangsh
 *@Date: 2022-01-04 13:49:20
 */
@RestController
@RequestMapping("/tech/techAwardsApply")
@Api(value="奖项申报", description="奖项申报")
public class TechAwardsApplyController  extends BaseController {

	@Autowired
	private TechAwardsApplyService techAwardsApplyService;
	
	private final String PER_PREFIX = "tech:but:apply:";
	
	/**
	 *@Description: 新增奖项申报
	 *@param techAwardsApply 奖项申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增奖项申报", notes = "新增奖项申报")
	@SystemLogAnnotation(type = "奖项申报",value = "新增奖项申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTechAwardsApply(@RequestBody TechAwardsApplyVo techAwardsApply) {
		techAwardsApply.setOrgName(getCurrentOrgName());
		techAwardsApply.setOrgCode(getCurrentScienceOrgId());
		String id = techAwardsApplyService.saveOrUpdateTechAwardsApply(techAwardsApply);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改奖项申报
	 *@param techAwardsApply 奖项申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改奖项申报", notes = "修改奖项申报")
	@SystemLogAnnotation(type = "奖项申报",value = "修改奖项申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTechAwardsApply(@RequestBody TechAwardsApplyVo techAwardsApply) {
		String id = techAwardsApplyService.saveOrUpdateTechAwardsApply(techAwardsApply);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除奖项申报(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除奖项申报", notes = "删除奖项申报")
	@SystemLogAnnotation(type = "奖项申报",value = "删除奖项申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTechAwardsApply(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		techAwardsApplyService.deleteMultiTechAwardsApply(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询奖项申报详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询奖项申报详情", notes = "查询奖项申报详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		TechAwardsApply  techAwardsApply=techAwardsApplyService.findById(id);
		return RestApiResponse.ok(techAwardsApply);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			TechAwardsApplyVo vo = new TechAwardsApplyVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoPageByQuery(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<TechAwardsApply> pageInfo = (PageInfo<TechAwardsApply>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
	
	/**
	 *@Description: 分页查询奖项申报
	 *@param techAwardsApplyVo 奖项申报 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询奖项申报", notes = "分页查询奖项申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TechAwardsApplyVo techAwardsApplyVo) {
		PageInfo<TechAwardsApply>  techAwardsApply=techAwardsApplyService.findPageByQuery(techAwardsApplyVo);
		return RestApiResponse.ok(techAwardsApply);
	}

	/**
	 *@Description: 提交奖项申报
	 *@param vo 奖项申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "提交奖项申报", notes = "提交奖项申报")
	@SystemLogAnnotation(type = "奖项申报",value = "提交奖项申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTechAchievement(@RequestBody TechAwardsApplyVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = techAwardsApplyService.submitTechAwardsApply(vo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 审核奖项申报
	 *@param techAwardsApplyVo 奖项申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "审核奖项申报", notes = "审核奖项申报")
	@SystemLogAnnotation(type = "奖项申报",value = "审核奖项申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTechAchievement(@RequestBody TechAwardsApplyVo techAwardsApplyVo) {
		String id = techAwardsApplyService.auditTechAwardsApply(techAwardsApplyVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 退回奖项申报
	 *@param techAwardsApplyVo 奖项申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "退回奖项申报", notes = "退回奖项申报")
	@SystemLogAnnotation(type = "奖项申报",value = "退回奖项申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTechAchievement(@RequestBody TechAwardsApplyVo techAwardsApplyVo) {
		String id = techAwardsApplyService.sendBackTechAwardsApply(techAwardsApplyVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 分页查询奖项申报
	 *@param vo 奖项申报 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "奖项申报待办", notes = "奖项申报待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoPageByQuery(@RequestBody TechAwardsApplyVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = techAwardsApplyService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = techAwardsApplyService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = techAwardsApplyService.endList(vo);
		} else {
			list = techAwardsApplyService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}

	/**
	 *@Description: 分页查询奖项申报
	 *@param techAwardsApplyVo 奖项申报 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "奖项申报已办", notes = "奖项申报已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finishedPageByQuery(@RequestBody TechAwardsApplyVo techAwardsApplyVo) {
		PageInfo<TechAwardsApply>  techAwardsApply=techAwardsApplyService.finishedList(techAwardsApplyVo);
		return RestApiResponse.ok(techAwardsApply);
	}

	/**
	 *@Description: 分页查询奖项申报
	 *@param techAwardsApplyVo 奖项申报 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "奖项申报已完成", notes = "奖项申报已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endPageByQuery(@RequestBody TechAwardsApplyVo techAwardsApplyVo) {
		PageInfo<TechAwardsApply>  techAwardsApply=techAwardsApplyService.endList(techAwardsApplyVo);
		return RestApiResponse.ok(techAwardsApply);
	}

	/**
	 *@Description: 奖项申报导出
	 *@param vo 奖项申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/export")
	@ApiOperation(value = "奖项申报导出", notes = "奖项申报导出")
	@SystemLogAnnotation(type = "奖项申报",value = "奖项申报导出")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"todo')")
	public void exportTechAchievement(@RequestBody TechAwardsApplyVo vo, HttpServletResponse response) {
		techAwardsApplyService.exportTechAchievement(vo, response);
	}
}
