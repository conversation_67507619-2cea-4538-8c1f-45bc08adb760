package com.fd.stdp.controller.sys;

import java.util.List;
import java.util.stream.Collectors;

import com.fd.stdp.beans.sys.vo.RouterVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.fd.stdp.beans.sys.SysMenu;
import com.fd.stdp.beans.sys.vo.SysMenuVo;
import com.fd.stdp.beans.sys.vo.TreeSelect;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.service.sys.MenuService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 菜单管理
 *
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/sys/menu")
@Api(value = "菜单管理", description = "菜单接口")
public class MenuController extends BaseController {
	@Autowired
	private MenuService menuService;

	private final String prefix = "btn:sys:fun:menu:";

	@RepeatSubAnnotation
	@PostMapping("/add")
	@ApiOperation(value = "新增菜单", notes = "新增菜单")
	@ResponseBody
	@PreAuthorize("hasAuthority('" + prefix + "add')")
	public RestApiResponse<?> add(@RequestBody SysMenuVo vo) {
		vo.setAdmin(isAdmin());
		this.menuService.addSysMenu(vo);
		return RestApiResponse.ok(1);
	}

	@RepeatSubAnnotation
	@PutMapping(value = "/edit")
	@ApiOperation(value = "修改菜单", notes = "修改菜单")
	@ResponseBody
	@PreAuthorize("hasAuthority('" + prefix + "edit')")
	public RestApiResponse<?> update(@RequestBody SysMenuVo vo) {
		vo.setAdmin(isAdmin());

		if (vo.getId().equals(vo.getParentId()))
        {
            return RestApiResponse.error("修改菜单'" + vo.getMenuName() + "'失败，上级菜单不能选择自己");
        }
		this.menuService.updateById(vo);
		return RestApiResponse.ok(vo);
	}



	@GetMapping(value = "/{menuId}")
	@ApiOperation(value = "菜单单条查询", notes = "菜单单条查询")
	@ResponseBody
	@PreAuthorize("hasAuthority('" + prefix + "byid')")
	public RestApiResponse<?> getInfo(@PathVariable String menuId) {
		SysMenu sysMenu = this.menuService.selectById(menuId);
		return RestApiResponse.ok(sysMenu);
	}


	@RepeatSubAnnotation
	@DeleteMapping(value = "/{menuId}")
	@ApiOperation(value = "删除菜单", notes = "删除菜单")
	@ResponseBody
	@PreAuthorize("hasAuthority('" + prefix + "delete')")
	public RestApiResponse<?> remove(@PathVariable String menuId) {
		this.menuService.deleteMenuById(menuId);
		return RestApiResponse.ok();
	}


    /**
     * 获取菜单列表
     */
    @PreAuthorize("hasAuthority('"+prefix+"list')")
    @PostMapping("/list")
    @ResponseBody
    public RestApiResponse<?> list(@RequestBody SysMenuVo menu)
    {
    	String userId = getCurrentUserId();
    	menu.setAdmin(isAdmin());
        List<SysMenuVo> menus = menuService.selectMenuList(menu, userId);
        return RestApiResponse.ok(menus);
    }
    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("/getRouters")
    @ResponseBody
    public RestApiResponse<?> getRouters()
    {
    	String userId = getCurrentUserId();
    	SysMenuVo menu=new SysMenuVo();
    	menu.setAdmin(isAdmin());
        List<SysMenuVo> menus = menuService.selectMenuList(menu, userId);
        List<SysMenuVo> menusC=menuService.getChildPerms(menus, "0");
		List<RouterVo> routerVos = menuService.buildMenus(menusC);
		return RestApiResponse.ok(routerVos);
    }

	@GetMapping(value = "/roleMenuTreeselect/{roleId}")
	@ApiOperation(value = "角色查询菜单", notes = "角色查询菜单")
	@ResponseBody
	@PreAuthorize("hasAuthority('"+prefix+"menuByRoleId')")
	public RestApiResponse<?> roleMenuTreeselect(@PathVariable String roleId) {
		//角色对应的菜单
		List<SysMenu> sysMenuList = menuService.findRoleId(roleId);
		List<String> sysMenuStrs=sysMenuList.stream().map(menu->menu.getId()).collect(Collectors.toList());

		SysMenuVo menu=new SysMenuVo();
		String userId = getCurrentUserId();
    	menu.setAdmin(isAdmin());
        List<SysMenuVo> menus = menuService.selectMenuList(menu, userId);

        List<TreeSelect> treeSelect= menuService.buildMenuTreeSelect(menus);
		JSONObject json=new JSONObject();
		json.put("checkedKeys", sysMenuStrs);
		json.put("menus", treeSelect);
		return RestApiResponse.ok(json);
	}

	/**
     * 获取菜单下拉树列表
     */
    @GetMapping("/treeselect")
    public RestApiResponse<?> treeselect(SysMenuVo menu){
    	String userId = getCurrentUserId();
    	menu.setAdmin(isAdmin());
        List<SysMenuVo> menus = menuService.selectMenuList(menu, userId);
        return RestApiResponse.ok(menuService.buildMenuTreeSelect(menus));
    }
}
