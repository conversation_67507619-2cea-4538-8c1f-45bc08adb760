package com.fd.stdp.controller.talent;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectContribute;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectContributeVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.talent.TalentLeaderSubjectContributeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 学科带头人建设任务书
 *@Author: wangsh
 *@Date: 2022-02-14 10:05:19
 */
@RestController
@RequestMapping("/talent/talentLeaderSubjectContribute")
@Api(value="学科带头人建设任务书", description="学科带头人建设任务书")
public class TalentLeaderSubjectContributeController  extends BaseController {

	@Autowired
	private TalentLeaderSubjectContributeService talentLeaderSubjectContributeService;
	
	private final String PER_PREFIX = "talent:but:contribute:";
	
	/**
	 *@Description: 新增学科带头人建设任务书
	 *@param talentLeaderSubjectContribute 学科带头人建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增学科带头人建设任务书", notes = "新增学科带头人建设任务书")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "新增学科带头人建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTalentLeaderSubjectContribute(@RequestBody TalentLeaderSubjectContributeVo talentLeaderSubjectContribute) {
		String id = talentLeaderSubjectContributeService.saveOrUpdateTalentLeaderSubjectContribute(talentLeaderSubjectContribute);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改学科带头人建设任务书
	 *@param talentLeaderSubjectContribute 学科带头人建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改学科带头人建设任务书", notes = "修改学科带头人建设任务书")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "修改学科带头人建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTalentLeaderSubjectContribute(@RequestBody TalentLeaderSubjectContributeVo talentLeaderSubjectContribute) {
		String id = talentLeaderSubjectContributeService.saveOrUpdateTalentLeaderSubjectContribute(talentLeaderSubjectContribute);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除学科带头人建设任务书(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除学科带头人建设任务书", notes = "删除学科带头人建设任务书")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "删除学科带头人建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTalentLeaderSubjectContribute(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		talentLeaderSubjectContributeService.deleteTalentLeaderSubjectContribute(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除学科带头人建设任务书(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除学科带头人建设任务书", notes = "删除学科带头人建设任务书")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "删除学科带头人建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiTalentLeaderSubjectContribute(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		talentLeaderSubjectContributeService.deleteMultiTalentLeaderSubjectContribute(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询学科带头人建设任务书详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询学科带头人建设任务书详情", notes = "查询学科带头人建设任务书详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		TalentLeaderSubjectContribute  talentLeaderSubjectContribute=talentLeaderSubjectContributeService.findById(id);
		return RestApiResponse.ok(talentLeaderSubjectContribute);
	}
	
	/**
	 *@Description: 分页查询学科带头人建设任务书
	 *@param talentLeaderSubjectContributeVo 学科带头人建设任务书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询学科带头人建设任务书", notes = "分页查询学科带头人建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo) {
		PageInfo<TalentLeaderSubjectContribute>  talentLeaderSubjectContribute=talentLeaderSubjectContributeService.findPageByQuery(talentLeaderSubjectContributeVo);
		return RestApiResponse.ok(talentLeaderSubjectContribute);
	}

	/**
	 *@Description: 学科带头人建设任务书提交
	 *@param talentLeaderSubjectContributeVo 学科带头人建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "学科带头人建设任务书提交", notes = "学科带头人建设任务书提交")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "学科带头人建设任务书提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTalentLeaderSubjectContribute(@RequestBody TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo) {
		String id = talentLeaderSubjectContributeService.submitTalentLeaderSubjectContribute(talentLeaderSubjectContributeVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 学科带头人建设任务书审核
	 *@param talentLeaderSubjectContributeVo 学科带头人建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "学科带头人建设任务书审核", notes = "学科带头人建设任务书审核")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "学科带头人建设任务书审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTalentLeaderSubjectContribute(@RequestBody TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo) {
		String id = talentLeaderSubjectContributeService.auditTalentLeaderSubjectContribute(talentLeaderSubjectContributeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 学科带头人建设任务书退回
	 *@param talentLeaderSubjectContributeVo 学科带头人建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "学科带头人建设任务书退回", notes = "学科带头人建设任务书退回")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "学科带头人建设任务书退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTalentLeaderSubjectContribute(@RequestBody TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo) {
		String id = talentLeaderSubjectContributeService.sendBackTalentLeaderSubjectContribute(talentLeaderSubjectContributeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 学科带头人建设任务书任务书下达
	 *@param talentLeaderSubjectContributeVo 学科带头人建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "学科带头人建设任务书任务书下达", notes = "学科带头人建设任务书任务书下达")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "学科带头人建设任务书任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseTalentLeaderSubjectContribute(@RequestBody TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo) {
		String id = talentLeaderSubjectContributeService.releaseTalentLeaderSubjectContribute(talentLeaderSubjectContributeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 学科带头人建设任务书待办
	 *@param talentLeaderSubjectContributeVo 学科带头人建设任务书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "学科带头人建设任务书待办", notes = "学科带头人建设任务书待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo) {
		PageInfo<TalentLeaderSubjectContribute>  talentLeaderSubjectContribute=talentLeaderSubjectContributeService.todoList(talentLeaderSubjectContributeVo);
		return RestApiResponse.ok(talentLeaderSubjectContribute);
	}
	/**
	 *@Description: 学科带头人建设任务书已办
	 *@param talentLeaderSubjectContributeVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "学科带头人建设任务书已办", notes = "学科带头人建设任务书已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo) {
		PageInfo<TalentLeaderSubjectContribute>  talentLeaderSubjectContribute=talentLeaderSubjectContributeService.finishedList(talentLeaderSubjectContributeVo);
		return RestApiResponse.ok(talentLeaderSubjectContribute);
	}
}
