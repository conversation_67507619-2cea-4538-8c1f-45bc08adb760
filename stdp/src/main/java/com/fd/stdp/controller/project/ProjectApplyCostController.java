package com.fd.stdp.controller.project;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.project.ProjectApplyCost;
import com.fd.stdp.beans.project.vo.ProjectApplyCostVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectApplyCostService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 项目科研经费预算Controller
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
@RestController
@RequestMapping("/project/applyCost")
@Api(value = "项目科研经费预算", description = "项目科研经费预算")
public class ProjectApplyCostController extends BaseController {
    @Autowired
    private ProjectApplyCostService projectApplyCostService;

    private final String PER_PREFIX = "btn:project:applyCost:";

    /**
     * @param projectApplyCost 项目科研经费预算数据 json
     * @return RestApiResponse<?>
     * @Description: 新增项目科研经费预算
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增项目科研经费预算", notes = "新增项目科研经费预算")
    @SystemLogAnnotation(type = "项目科研经费预算", value = "新增项目科研经费预算")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveProjectApplyCost(@RequestBody ProjectApplyCost projectApplyCost) {
        String id = projectApplyCostService.saveOrUpdateProjectApplyCost(projectApplyCost);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectApplyCost 项目科研经费预算数据 json
     * @return RestApiResponse<?>
     * @Description: 修改项目科研经费预算
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改项目科研经费预算", notes = "修改项目科研经费预算")
    @SystemLogAnnotation(type = "项目科研经费预算", value = "修改项目科研经费预算")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateProjectApplyCost(@RequestBody ProjectApplyCost projectApplyCost) {
        String id = projectApplyCostService.saveOrUpdateProjectApplyCost(projectApplyCost);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除项目科研经费预算(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除项目科研经费预算", notes = "批量删除项目科研经费预算")
    @SystemLogAnnotation(type = "项目科研经费预算", value = "批量删除项目科研经费预算")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteProjectApplyCost(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        projectApplyCostService.deleteProjectApplyCost(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询项目科研经费预算详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询项目科研经费预算详情", notes = "查询项目科研经费预算详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ProjectApplyCost projectApplyCost = projectApplyCostService.findById(id);
        return RestApiResponse.ok(projectApplyCost);
    }

    /**
     * @param projectApplyCostVo 项目科研经费预算 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询项目科研经费预算
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询项目科研经费预算", notes = "分页查询项目科研经费预算")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ProjectApplyCostVo projectApplyCostVo) {
        PageInfo<ProjectApplyCost> projectApplyCost = projectApplyCostService.findPageByQuery(projectApplyCostVo);
        return RestApiResponse.ok(projectApplyCost);
    }

}
