package com.fd.stdp.controller.project;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.project.ProjectDemandCollectionExperts;
import com.fd.stdp.beans.project.vo.ProjectDemandCollectionExpertsVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectDemandCollectionExpertsService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 需求征集专家Controller
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
@RestController
@RequestMapping("/project/demandCollectionExperts")
@Api(value = "需求征集专家", description = "需求征集专家")
public class ProjectDemandCollectionExpertsController extends BaseController {
    @Autowired
    private ProjectDemandCollectionExpertsService projectDemandCollectionExpertsService;

    private final String PER_PREFIX = "btn:project:demandCollectionExperts:";

    /**
     * @param projectDemandCollectionExperts 需求征集专家数据 json
     * @return RestApiResponse<?>
     * @Description: 新增需求征集专家
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增需求征集专家", notes = "新增需求征集专家")
    @SystemLogAnnotation(type = "需求征集专家", value = "新增需求征集专家")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveProjectDemandCollectionExperts(@RequestBody ProjectDemandCollectionExperts projectDemandCollectionExperts) {
        String id = projectDemandCollectionExpertsService.saveOrUpdateProjectDemandCollectionExperts(projectDemandCollectionExperts);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectDemandCollectionExperts 需求征集专家数据 json
     * @return RestApiResponse<?>
     * @Description: 修改需求征集专家
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改需求征集专家", notes = "修改需求征集专家")
    @SystemLogAnnotation(type = "需求征集专家", value = "修改需求征集专家")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateProjectDemandCollectionExperts(@RequestBody ProjectDemandCollectionExperts projectDemandCollectionExperts) {
        String id = projectDemandCollectionExpertsService.saveOrUpdateProjectDemandCollectionExperts(projectDemandCollectionExperts);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除需求征集专家(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除需求征集专家", notes = "批量删除需求征集专家")
    @SystemLogAnnotation(type = "需求征集专家", value = "批量删除需求征集专家")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteProjectDemandCollectionExperts(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        projectDemandCollectionExpertsService.deleteProjectDemandCollectionExperts(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询需求征集专家详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询需求征集专家详情", notes = "查询需求征集专家详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ProjectDemandCollectionExperts projectDemandCollectionExperts = projectDemandCollectionExpertsService.findById(id);
        return RestApiResponse.ok(projectDemandCollectionExperts);
    }

    /**
     * @param projectDemandCollectionExpertsVo 需求征集专家 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询需求征集专家
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询需求征集专家", notes = "分页查询需求征集专家")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ProjectDemandCollectionExpertsVo projectDemandCollectionExpertsVo) {
        PageInfo<ProjectDemandCollectionExperts> projectDemandCollectionExperts = projectDemandCollectionExpertsService.findPageByQuery(projectDemandCollectionExpertsVo);
        return RestApiResponse.ok(projectDemandCollectionExperts);
    }

}
