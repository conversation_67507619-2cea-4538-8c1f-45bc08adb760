package com.fd.stdp.controller.sys.rest;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.fd.stdp.beans.sys.SysFileInfo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.config.FileServiceFactory;
import com.fd.stdp.dao.sys.SysFileInfoMapper;
import com.fd.stdp.service.sys.FileService;
import com.fd.stdp.util.Base64Converter;

import io.swagger.annotations.Api;
import tk.mybatis.mapper.entity.Example;

@RestController
@RequestMapping("/sys/file/rest")
@Api(value = "文件上传rest服务", description = "文件上传rest服务")
public class RestFileController extends BaseController {
    @Value("${file.useFileType}")
    private String fileSource;
    @Autowired
    private FileServiceFactory fileServiceFactory;
    @Autowired
    private SysFileInfoMapper fileInfoMapper;

    /**
     * @Description:文件上传
     * @param file
     * @return SysFileInfo * @throws
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public SysFileInfo upload(MultipartFile file) throws Exception {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }
        
        // 检查文件大小（限制为50MB）
        long maxSize = 50 * 1024 * 1024; // 50MB
        if (file.getSize() > maxSize) {
            throw new ServiceException("上传文件大小不能超过50MB");
        }
        
        // 检查文件名是否包含恶意字符
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            throw new ServiceException("文件名不能为空");
        }
        
        if (originalFilename.contains("../") || originalFilename.contains("..\\")) {
            throw new ServiceException("文件名包含非法字符");
        }
        
        FileService fileService = fileServiceFactory.getFileService(fileSource);
        SysFileInfo sysFileInfo = new SysFileInfo();
        return fileService.upload(file, sysFileInfo);
    }

    /**
     * @Description:文件下载
     * @param key 
     * @return void * @throws
     */
    @GetMapping("/down/{key}")
    public void down(@PathVariable("key") String key) {
        key = Base64Converter.decode(key);
        FileService fileService = fileServiceFactory.getFileService(fileSource);
        fileService.down(key, response);
    }

    /**
     * @Descripiton 文件删除
     * @param id  
     * @return void * @throws
     * <AUTHOR>
     */
    @GetMapping("/delete")
    public void delete(@RequestParam("id") String id) {
        SysFileInfo fileInfo = fileInfoMapper.selectByPrimaryKey(id);
        if (fileInfo != null) {
            FileService fileService = fileServiceFactory.getFileService(fileInfo.getSource());
            fileService.delete(fileInfo);
        }
    }

    /**
     *  
     * @Descripiton 根据url删除文件
     * @param url
     * <AUTHOR>
     */
    @GetMapping("/deleteByUrl")
    public void deleteByUrl(@RequestParam String fileUrl) {
        Example example = new Example(SysFileInfo.class);
        example.createCriteria().andEqualTo("url", fileUrl);
        List<SysFileInfo> fileInfo = fileInfoMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(fileInfo)) {
            for (SysFileInfo sysFileInfo : fileInfo) {
                FileService fileService = fileServiceFactory.getFileService(sysFileInfo.getSource());
                fileService.delete(sysFileInfo);
            }
        }
    }

}
