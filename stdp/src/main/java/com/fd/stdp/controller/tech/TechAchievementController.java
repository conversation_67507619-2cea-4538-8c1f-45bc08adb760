package com.fd.stdp.controller.tech;

import com.fd.stdp.beans.innovation.InnovationPrivinceLaboratoryApply;
import com.fd.stdp.beans.innovation.vo.InnovationPrivinceLaboratoryApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.tech.TechAchievement;
import com.fd.stdp.beans.tech.vo.TechAchievementVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.tech.TechAchievementService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 成果统计
 *@Author: wangsh
 *@Date: 2022-01-04 13:49:38
 */
@RestController
@RequestMapping("/tech/techAchievement")
@Api(value="成果统计", description="成果统计")
public class TechAchievementController  extends BaseController {

	@Autowired
	private TechAchievementService techAchievementService;
	
	private final String PER_PREFIX = "tech:but:achievement:";
	
	/**
	 *@Description: 新增成果统计
	 *@param techAchievement 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增成果统计", notes = "新增成果统计")
	@SystemLogAnnotation(type = "成果统计",value = "新增成果统计")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTechAchievement(@RequestBody TechAchievementVo techAchievement) {
		techAchievement.setOrgName(getCurrentOrgName());
		techAchievement.setOrgCode(getCurrentScienceOrgId());
		String id = techAchievementService.saveOrUpdateTechAchievement(techAchievement);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改成果统计
	 *@param techAchievement 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改成果统计", notes = "修改成果统计")
	@SystemLogAnnotation(type = "成果统计",value = "修改成果统计")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTechAchievement(@RequestBody TechAchievementVo
																techAchievement) {
		String id = techAchievementService.saveOrUpdateTechAchievement(techAchievement);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除成果统计(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除成果统计", notes = "删除成果统计")
	@SystemLogAnnotation(type = "成果统计",value = "删除成果统计")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTechAchievement(@RequestBody List<String> ids) {
		techAchievementService.deleteMultiTechAchievement(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询成果统计详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询成果统计详情", notes = "查询成果统计详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		TechAchievement  techAchievement=techAchievementService.findById(id);
		return RestApiResponse.ok(techAchievement);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			TechAchievementVo vo = new TechAchievementVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoTechAchievement(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<TechAchievement> pageInfo = (PageInfo<TechAchievement>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}

	/**
	 *@Description: 分页查询成果统计
	 *@param techAchievementVo 成果统计 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询成果统计", notes = "分页查询成果统计")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TechAchievementVo techAchievementVo) {
		PageInfo<TechAchievement>  techAchievement=techAchievementService.findPageByQuery(techAchievementVo);
		return RestApiResponse.ok(techAchievement);
	}

	/**
	 *@Description: 提交成果统计
	 *@param techAchievementVo 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "提交成果统计", notes = "提交成果统计")
	@SystemLogAnnotation(type = "成果统计",value = "提交成果统计")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTechAchievement(@RequestBody TechAchievementVo techAchievementVo) {
		techAchievementVo.setOrgName(getCurrentOrgName());
		techAchievementVo.setOrgCode(getCurrentScienceOrgId());
		String id = techAchievementService.submitTechAchievement(techAchievementVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 审核成果统计
	 *@param techAchievementVo 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "审核成果统计", notes = "审核成果统计")
	@SystemLogAnnotation(type = "成果统计",value = "审核成果统计")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTechAchievement(@RequestBody TechAchievementVo techAchievementVo) {
		String id = techAchievementService.auditTechAchievement(techAchievementVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 退回成果统计
	 *@param techAchievementVo 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "退回成果统计", notes = "退回成果统计")
	@SystemLogAnnotation(type = "成果统计",value = "退回成果统计")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTechAchievement(@RequestBody TechAchievementVo techAchievementVo) {
		String id = techAchievementService.sendBackTechAchievement(techAchievementVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 退回成果待办
	 *@param vo 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/todo")
	@ApiOperation(value = "成果统计待办", notes = "成果统计待办")
	@SystemLogAnnotation(type = "成果统计",value = "成果统计待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"todo')")
	public RestApiResponse<?> todoTechAchievement(@RequestBody TechAchievementVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = techAchievementService.todoTechAchievement(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = techAchievementService.finishedTechAchievement(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = techAchievementService.endList(vo);
		} else {
			list = techAchievementService.todoTechAchievement(vo);
		}
		return RestApiResponse.ok(list);
	}

	/**
	 *@Description: 退回成果待办
	 *@param techAchievementVo 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/finished")
	@ApiOperation(value = "成果统计已办", notes = "成果统计已办")
	@SystemLogAnnotation(type = "成果统计",value = "成果统计已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"finished')")
	public RestApiResponse<?> finishedTechAchievement(@RequestBody TechAchievementVo techAchievementVo) {
		return RestApiResponse.ok(techAchievementService.finishedTechAchievement(techAchievementVo));
	}

	/**
	 *@Description: 分页查询成果统计
	 *@param techAchievementVo 成果统计 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "分页查询成果统计", notes = "分页查询成果统计")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endPageByQuery(@RequestBody TechAchievementVo techAchievementVo) {
		PageInfo<TechAchievement>  techAchievement=techAchievementService.endList(techAchievementVo);
		return RestApiResponse.ok(techAchievement);
	}

	/**
	 *@Description: 成果统计导出
	 *@param techAchievementVo 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/export")
	@ApiOperation(value = "成果统计导出", notes = "成果统计导出")
	@SystemLogAnnotation(type = "成果统计",value = "成果统计导出")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"todo')")
	public void exportTechAchievement(@RequestBody TechAchievementVo techAchievementVo, HttpServletResponse response) {
		techAchievementService.exportTechAchievement(techAchievementVo, response);
	}
}
