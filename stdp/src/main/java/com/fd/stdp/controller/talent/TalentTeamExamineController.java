package com.fd.stdp.controller.talent;

import com.fd.stdp.beans.talent.TalentTeamContributeChange;
import com.fd.stdp.beans.talent.vo.TalentTeamContributeChangeVo;
import com.fd.stdp.enums.QueryTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamExamine;
import com.fd.stdp.beans.talent.vo.TalentTeamExamineVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.talent.TalentTeamExamineService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 创新团队考核
 *@Author: wangsh
 *@Date: 2022-02-14 10:22:36
 */
@RestController
@RequestMapping("/talent/talentTeamExamine")
@Api(value="创新团队考核", description="创新团队考核")
public class

TalentTeamExamineController  extends BaseController {

	@Autowired
	private TalentTeamExamineService talentTeamExamineService;
	
	private final String PER_PREFIX = "talent:but:teamExamine:";
	
	/**
	 *@Description: 新增创新团队考核
	 *@param vo 创新团队考核数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增创新团队考核", notes = "新增创新团队考核")
	@SystemLogAnnotation(type = "创新团队考核",value = "新增创新团队考核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTalentTeamExamine(@RequestBody TalentTeamExamineVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentTeamExamineService.saveOrUpdateTalentTeamExamine(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改创新团队考核
	 *@param talentTeamExamine 创新团队考核数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改创新团队考核", notes = "修改创新团队考核")
	@SystemLogAnnotation(type = "创新团队考核",value = "修改创新团队考核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTalentTeamExamine(@RequestBody TalentTeamExamineVo talentTeamExamine) {
		String id = talentTeamExamineService.saveOrUpdateTalentTeamExamine(talentTeamExamine);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除创新团队考核(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除创新团队考核", notes = "删除创新团队考核")
	@SystemLogAnnotation(type = "创新团队考核",value = "删除创新团队考核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTalentTeamExamine(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		talentTeamExamineService.deleteTalentTeamExamine(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除创新团队考核(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除创新团队考核", notes = "删除创新团队考核")
	@SystemLogAnnotation(type = "创新团队考核",value = "删除创新团队考核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiTalentTeamExamine(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		talentTeamExamineService.deleteMultiTalentTeamExamine(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询创新团队考核详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询创新团队考核详情", notes = "查询创新团队考核详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		TalentTeamExamine  talentTeamExamine=talentTeamExamineService.findById(id);
		return RestApiResponse.ok(talentTeamExamine);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			TalentTeamExamineVo vo = new TalentTeamExamineVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<TalentTeamExamine> pageInfo = (PageInfo<TalentTeamExamine>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
	
	/**
	 *@Description: 分页查询创新团队考核
	 *@param talentTeamExamineVo 创新团队考核 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询创新团队考核", notes = "分页查询创新团队考核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TalentTeamExamineVo talentTeamExamineVo) {
		PageInfo<TalentTeamExamine>  talentTeamExamine=talentTeamExamineService.findPageByQuery(talentTeamExamineVo);
		return RestApiResponse.ok(talentTeamExamine);
	}

	/**
	 *@Description: 创新团队考核提交
	 *@param vo 创新团队考核数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "创新团队考核提交", notes = "创新团队考核提交")
	@SystemLogAnnotation(type = "创新团队考核",value = "创新团队考核提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTalentTeamExamine(@RequestBody TalentTeamExamineVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentTeamExamineService.submitTalentTeamExamine(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 创新团队考核审核
	 *@param talentTeamExamineVo 创新团队考核数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "创新团队考核审核", notes = "创新团队考核审核")
	@SystemLogAnnotation(type = "创新团队考核",value = "创新团队考核审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTalentTeamExamine(@RequestBody TalentTeamExamineVo talentTeamExamineVo) {
		String id = talentTeamExamineService.auditTalentTeamExamine(talentTeamExamineVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 创新团队考核退回
	 *@param talentTeamExamineVo 创新团队考核数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "创新团队考核退回", notes = "创新团队考核退回")
	@SystemLogAnnotation(type = "创新团队考核",value = "创新团队考核退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTalentTeamExamine(@RequestBody TalentTeamExamineVo talentTeamExamineVo) {
		String id = talentTeamExamineService.sendBackTalentTeamExamine(talentTeamExamineVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 创新团队考核任务书下达
	 *@param talentTeamExamineVo 创新团队考核数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "创新团队考核任务书下达", notes = "创新团队考核任务书下达")
	@SystemLogAnnotation(type = "创新团队考核",value = "创新团队考核任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseTalentTeamExamine(@RequestBody TalentTeamExamineVo talentTeamExamineVo) {
		String id = talentTeamExamineService.releaseTalentTeamExamine(talentTeamExamineVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 创新团队考核待办
	 *@param vo 创新团队考核 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "创新团队考核待办", notes = "创新团队考核待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody TalentTeamExamineVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = talentTeamExamineService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = talentTeamExamineService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = talentTeamExamineService.findPageByQuery(vo);
		} else {
			list = talentTeamExamineService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 创新团队考核已办
	 *@param talentTeamExamineVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "创新团队考核已办", notes = "创新团队考核已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody TalentTeamExamineVo talentTeamExamineVo) {
		PageInfo<TalentTeamExamine>  talentTeamExamine=talentTeamExamineService.finishedList(talentTeamExamineVo);
		return RestApiResponse.ok(talentTeamExamine);
	}
}
