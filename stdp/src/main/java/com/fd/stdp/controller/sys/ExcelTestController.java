package com.fd.stdp.controller.sys;

import com.fd.stdp.beans.sys.vo.TestExcelVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.util.EasyExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.*;

/**
 * Excel测试
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sys/testExcel")
@Api(value = "Excel测试", description = "Excel测试")
public class ExcelTestController extends BaseController {

    /**
     * 导出Excel
     */
    @GetMapping(value = "/exportTemplExcel")
    @ApiOperation(value = "导出Excel 模板导出", notes = "导出Excel 模板导出")
    public void exportTemplExcel() {
        List<TestExcelVo> testList = new ArrayList<TestExcelVo>();
        for (int i = 0; i < 11; i++) {
            TestExcelVo test = new TestExcelVo();
            test.setDateTime(new Date());
            test.setId("" + (i + 1));
            test.setName("学生" + (i + 1));
            test.setPhone("1760719354" + (i + 1));
            test.setSex(i % 2 == 0 ? "男" : "女");
            testList.add(test);
        }
        ClassPathResource classPathResource = new ClassPathResource("template\\\\stu.xlsx");
        InputStream is = null;
        try {
            is = classPathResource.getInputStream();
        } catch (Exception e) {
            // TODO Auto-generated catch block
            logger.error("类路径获取输入流异常{}", e.getStackTrace());
        }
//        EasyExcelUtils.exportExcelByTemplate(new HashMap<>(), is, "学生信息", ".xlsx",
//                1, 2, 1, testList, null, TestExcelVo.class, response);

    }

    /**
     * 导出Excel
     */
    @GetMapping(value = "/exportExcel")
    @ApiOperation(value = "导出Excel 普通导出", notes = "导出Excel 普通导出")
    public void exportExcel() {
        List<TestExcelVo> testList = new ArrayList<TestExcelVo>();
        for (int i = 0; i < 11; i++) {
            TestExcelVo test = new TestExcelVo();
            test.setDateTime(new Date());
            test.setId("" + (i + 1));
            test.setName("学生" + (i + 1));
            test.setPhone("1760719354" + (i + 1));
            test.setSex(i % 2 == 0 ? "男" : "女");
            testList.add(test);
        }

        EasyExcelUtils.exportExcel(testList, "sheetName", TestExcelVo.class, "学生信息", response);

    }

    /**
     * 动态导出Excel
     */
    @GetMapping(value = "/dynamicExportExcel")
    @ApiOperation(value = "动态导出Excel", notes = "动态导出Excel")
    public void dynamicExportExcel() {
        List<TestExcelVo> testList = new ArrayList<TestExcelVo>();
        for (int i = 0; i < 11; i++) {
            TestExcelVo test = new TestExcelVo();
            test.setDateTime(new Date());
            test.setId("" + (i + 1));
            test.setName("学生" + (i + 1));
            test.setPhone("1760719354" + (i + 1));
            test.setSex(i % 2 == 0 ? "男" : "女");
            testList.add(test);
        }
        Map<String, String> headKey = new LinkedHashMap<String, String>();
        headKey.put("id", "序号");
        headKey.put("name", "姓名");
//        EasyExcelUtils.dynamicExportExcel(testList, "标题", "sheetName", "学生信息", true, response, headKey);

    }

    /**
     * 导入Excel
     */
    @PostMapping(value = "/importExcel")
    @ApiOperation(value = "导入Excel 普通导出的文件", notes = "导入Excel 普通导出的文件")
    public RestApiResponse<?> importExcel(@RequestParam("file") MultipartFile file) {
        //标题多少行，头多少行
//        List<TestExcelVo> testList = ExcelUtils.importExcel(file, 1, 1, TestExcelVo.class);
        return RestApiResponse.ok(null);
    }

    /**
     * 导入Excel
     */
    @PostMapping(value = "/importExcel2")
    @ApiOperation(value = "导入Excel2 模板导出的文件", notes = "导入Excel2模板导出的文件")
    public RestApiResponse<?> importExcel2(@RequestParam("file") MultipartFile file) {
        //标题多少行，头多少行
//        List<TestExcelVo> testList = ExcelUtils.importExcel(file, 2, 1, 1, TestExcelVo.class);
        return RestApiResponse.ok(null);
    }
}
