package com.fd.stdp.controller.sys;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fd.stdp.beans.sys.SysDictItem;
import com.fd.stdp.beans.sys.vo.DictTree;
import com.fd.stdp.beans.sys.vo.SysDictItemVo;
import com.fd.stdp.beans.sys.vo.SysDictVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.OperationLogAnnotation;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.service.sys.SysDictItemService;
import com.fd.stdp.util.TreeUtil;
import com.github.pagehelper.PageInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 *@Description: 系统数据字典分类
 *@Author: linqiang
 *@Date: 2020-07-05 15:14:51
 */
@RestController
@RequestMapping("/sys/dictItem")
@Api(value = "系统数据字典分类", description = "系统数据字典分类")
public class SysDictItemController extends BaseController {

    @Autowired
    private SysDictItemService basicDictItemService;
    private final String PREFIX = "btn:sys:dict:";
    /**
     * @Description 初始化字典数
     * @param name
     * @return List<DictTree>
     */
    @RequestMapping(value = "/tree", method = RequestMethod.GET)
    @ApiOperation(value = "字典管理", notes = "查询属性字典")
    @PreAuthorize("hasAuthority('"+PREFIX+"tree')")
    @ResponseBody
    public List<DictTree> getTree() {
        SysDictItem dictItem = new SysDictItem();
        // 获取字典树
        List<SysDictItem> dictItems = basicDictItemService.listItem(dictItem, isAdmin());
        // 生成树
        List<DictTree> dictTree = getDictTree(dictItems, CommonConstant.ROOT);
        return dictTree;
    }

    private List<DictTree> getDictTree(List<SysDictItem> dicts, String root) {
        List<DictTree> trees = new ArrayList<DictTree>();
        DictTree node = null;
        for (SysDictItem dict : dicts) {
            node = new DictTree();
            BeanUtils.copyProperties(dict, node);
            node.setItemValue(dict.getItemValue());
            node.setId(dict.getItemCode());
            trees.add(node);
        }
        if (StringUtils.isEmpty(root)) {
            root = "-1";
        }
        return TreeUtil.bulidNode(trees, root);
    }

    /**
     *@Description: 新增系统数据字典分类
     *@param basicDictItem 系统数据字典分类数据 json
     *@return RestApiResponse<?>
     *@Author: linqiang
     */

    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增系统数据字典分类", notes = "新增系统数据字典分类")
    @SystemLogAnnotation(type = "系统数据字典分类", value = "新增系统数据字典分类")
    @OperationLogAnnotation(notes = "新增系统数据字典分类{字典code:itemCode}", module = "基础信息管理", submodule = "字典管理")
    @PreAuthorize("hasAuthority('"+PREFIX+"add')")
    public RestApiResponse<?> addBasicDictItem(@RequestBody SysDictItem basicDictItem) {
        SysDictVo dictVo = new SysDictVo();
        dictVo.setWhetherAdmin(isAdmin());
        basicDictItemService.addBasicDictItem(basicDictItem, dictVo);
        return RestApiResponse.ok(basicDictItem);
    }

    /**
     *@Description: 修改系统数据字典分类
     *@param basicDictItem 系统数据字典分类数据 json
     *@return RestApiResponse<?>
     *@Author: linqiang
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改系统数据字典分类", notes = "修改系统数据字典分类")
    @SystemLogAnnotation(type = "系统数据字典分类", value = "修改系统数据字典分类")
    @OperationLogAnnotation(notes = "修改系统数据字典分类{字典code:itemCode},{字典id:id}", module = "基础信息管理", submodule = "字典管理")
    @PreAuthorize("hasAuthority('"+PREFIX+"update')")
    public RestApiResponse<?> update(@RequestBody SysDictItem basicDictItem) {
        basicDictItemService.updateItemById(basicDictItem, isAdmin());
        return RestApiResponse.ok(basicDictItem);
    }

    /**
     * @Description: 删除数据字典分类（新）
     * @param id 系统数据字典分类ID
     * @return void
     * @Author: linqiang
     */
    @PostMapping(value = "/deleteItem/{id}")
    @ApiOperation(value = "删除字典", notes = "删除字典")
    @ResponseBody
    @SystemLogAnnotation(type = "字典管理", value = "删除字典")
    @OperationLogAnnotation(notes = "删除字典{字典id:id}", module = "基础信息管理", submodule = "字典管理")
    @PreAuthorize("hasAuthority('"+PREFIX+"delete')")
    public RestApiResponse<?> removeItem(@PathVariable String id) {
        basicDictItemService.deleteDictById(id, isAdmin());
        return RestApiResponse.ok();
    }

    /**
     * @Description: 字典单条查询
     * @param basicDictItem 系统数据字典分类数据 json
     * @return RestApiResponse<?>
     * @Author: linqiang
     */
    @PostMapping(value = "/findId/{id}")
    @ApiOperation(value = "字典单条查询", notes = "字典单条查询")
    @ResponseBody
    @PreAuthorize("hasAuthority('"+PREFIX+"id')")
    public RestApiResponse<?> findId(@PathVariable String id) {
        SysDictItem dictItem = basicDictItemService.findById(id);
        return RestApiResponse.ok(dictItem);
    }

	/**
	 * @Description 查询字典
	 * @param name
	 * @return List<DictTree>
	 * <AUTHOR>
	 */
	@PostMapping("/findDictItemByCodes")
	@ResponseBody
	@ApiOperation(value = "根据字典类型获取字典条目", notes = "根据字典类型获取字典条目")
	public RestApiResponse<PageInfo<SysDictItem>> findDictItemByCodes(@RequestBody SysDictItemVo sysDictItem) {
		PageInfo<SysDictItem> dictItemList = basicDictItemService.findDictItemByCodes(sysDictItem);
		return RestApiResponse.ok(dictItemList);
	}
}
