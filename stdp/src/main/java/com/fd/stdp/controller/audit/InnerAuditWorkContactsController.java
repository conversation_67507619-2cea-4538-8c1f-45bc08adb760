package com.fd.stdp.controller.audit;

import com.fd.stdp.beans.audit.InnerAuditWorkContacts;
import com.fd.stdp.beans.audit.vo.InnerAuditEquippedInformationVo;
import com.fd.stdp.beans.audit.vo.InnerAuditWorkContactsVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.constant.InnerAuditBasicConstant;
import com.fd.stdp.service.audit.impl.InnerAuditUtil;
import com.fd.stdp.util.EasyExcelUtils;
import com.fd.stdp.util.ExportUtil;
import com.fd.stdp.util.UUIDUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.audit.InnerAuditWorkContactsService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description: 省市场监管局直属单位内审工作联系表
 * @Author: sef
 * @Date: 2022-06-06 13:54:35
 */
@RestController
@RequestMapping("/audit/innerAuditWorkContacts")
@Api(value = "省市场监管局直属单位内审工作联系表", description = "省市场监管局直属单位内审工作联系表")
public class InnerAuditWorkContactsController extends BaseController {

    @Autowired
    private InnerAuditWorkContactsService innerAuditWorkContactsService;

    //private final String PER_PREFIX = "这里写业务前缀命名:but:模块:功能:";

    private final String PER_PREFIX = "btn:audit:WorkContacts:";

    /**
     * @param innerAuditWorkContactsVoList 省市场监管局直属单位内审工作联系表数据 json
     * @return RestApiResponse<?>
     * @Description: 新增省市场监管局直属单位内审工作联系表
     * @Author: sef
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增省市场监管局直属单位内审工作联系表", notes = "新增省市场监管局直属单位内审工作联系表")
    @SystemLogAnnotation(type = "省市场监管局直属单位内审工作联系表", value = "新增省市场监管局直属单位内审工作联系表")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
    public RestApiResponse<?> saveInnerAuditWorkContacts(@RequestBody InnerAuditWorkContactsVo innerAuditWorkContactsVoList) {
        List<InnerAuditWorkContactsVo> contactList = innerAuditWorkContactsVoList.getContactList();
        innerAuditWorkContactsService.saveBatchInnerAuditWorkContacts(contactList);
        return RestApiResponse.ok();
    }

    /**
     * @param innerAuditWorkContacts 省市场监管局直属单位内审工作联系表数据 json
     * @return RestApiResponse<?>
     * @Description: 修改省市场监管局直属单位内审工作联系表
     * @Author: sef
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改省市场监管局直属单位内审工作联系表", notes = "修改省市场监管局直属单位内审工作联系表")
    @SystemLogAnnotation(type = "省市场监管局直属单位内审工作联系表", value = "修改省市场监管局直属单位内审工作联系表")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
    public RestApiResponse<?> updateInnerAuditWorkContacts(@RequestBody InnerAuditWorkContactsVo innerAuditWorkContacts) {
        String id = innerAuditWorkContactsService.saveOrUpdateInnerAuditWorkContacts(innerAuditWorkContacts);
        return RestApiResponse.ok(id);
    }

    /**
     * @param ids
     * @return RestApiResponse<?>
     * @Description: 批量删除省市场监管局直属单位内审工作联系表(判断 关联数据是否可以删除)
     * @Author: sef
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "删除省市场监管局直属单位内审工作联系表", notes = "删除省市场监管局直属单位内审工作联系表")
    @SystemLogAnnotation(type = "省市场监管局直属单位内审工作联系表", value = "删除省市场监管局直属单位内审工作联系表")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
    public RestApiResponse<?> deleteMultiInnerAuditWorkContacts(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        innerAuditWorkContactsService.deleteMultiInnerAuditWorkContacts(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询省市场监管局直属单位内审工作联系表详情
     * @Author: sef
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询省市场监管局直属单位内审工作联系表详情", notes = "查询省市场监管局直属单位内审工作联系表详情")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        InnerAuditWorkContacts innerAuditWorkContacts = innerAuditWorkContactsService.findById(id);
        return RestApiResponse.ok(innerAuditWorkContacts);
    }

    /**
     * @param innerAuditWorkContactsVo 省市场监管局直属单位内审工作联系表 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询省市场监管局直属单位内审工作联系表
     * @Author: sef
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询省市场监管局直属单位内审工作联系表", notes = "分页查询省市场监管局直属单位内审工作联系表")
    @PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody InnerAuditWorkContactsVo innerAuditWorkContactsVo) {
        PageInfo<InnerAuditWorkContacts> innerAuditWorkContacts = innerAuditWorkContactsService.findPageByQuery(innerAuditWorkContactsVo);
        return RestApiResponse.ok(innerAuditWorkContacts);
    }

    /**
     * @param innerAuditWorkContactsVo 省市场监管局直属单位内审工作联系表数据 json
     * @return RestApiResponse<?>
     * @Description: 省市场监管局直属单位内审工作联系表提交
     * @Author: sef
     */
    @RepeatSubAnnotation
    @PostMapping("/submit")
    @ApiOperation(value = "省市场监管局直属单位内审工作联系表提交", notes = "省市场监管局直属单位内审工作联系表提交")
    @SystemLogAnnotation(type = "省市场监管局直属单位内审工作联系表", value = "省市场监管局直属单位内审工作联系表提交")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
    public RestApiResponse<?> submitInnerAuditWorkContacts(@RequestBody InnerAuditWorkContactsVo innerAuditWorkContactsVo) {
        if (StringUtils.isBlank(innerAuditWorkContactsVo.getOrgName())) {
            innerAuditWorkContactsVo.setOrgName(getCurrentOrgName());
            innerAuditWorkContactsVo.setOrgCode(getLoginUser().getAreaCode());
        }
        String id = innerAuditWorkContactsService.submitInnerAuditWorkContacts(innerAuditWorkContactsVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param innerAuditWorkContactsVo 省市场监管局直属单位内审工作联系表数据 json
     * @return RestApiResponse<?>
     * @Description: 省市场监管局直属单位内审工作联系表审核
     * @Author: sef
     */
    @RepeatSubAnnotation
    @PostMapping("/audit")
    @ApiOperation(value = "省市场监管局直属单位内审工作联系表审核", notes = "省市场监管局直属单位内审工作联系表审核")
    @SystemLogAnnotation(type = "省市场监管局直属单位内审工作联系表", value = "省市场监管局直属单位内审工作联系表审核")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
    public RestApiResponse<?> auditInnerAuditWorkContacts(@RequestBody InnerAuditWorkContactsVo innerAuditWorkContactsVo) {
        String id = innerAuditWorkContactsService.auditInnerAuditWorkContacts(innerAuditWorkContactsVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param innerAuditWorkContactsVo 省市场监管局直属单位内审工作联系表数据 json
     * @return RestApiResponse<?>
     * @Description: 省市场监管局直属单位内审工作联系表退回
     * @Author: sef
     */
    @RepeatSubAnnotation
    @PostMapping("/sendBack")
    @ApiOperation(value = "省市场监管局直属单位内审工作联系表退回", notes = "省市场监管局直属单位内审工作联系表退回")
    @SystemLogAnnotation(type = "省市场监管局直属单位内审工作联系表", value = "省市场监管局直属单位内审工作联系表退回")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
    public RestApiResponse<?> sendBackInnerAuditWorkContacts(@RequestBody InnerAuditWorkContactsVo innerAuditWorkContactsVo) {
        String id = innerAuditWorkContactsService.sendBackInnerAuditWorkContacts(innerAuditWorkContactsVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param innerAuditWorkContactsVo 省市场监管局直属单位内审工作联系表数据 json
     * @return RestApiResponse<?>
     * @Description: 省市场监管局直属单位内审工作联系表任务书下达
     * @Author: sef
     */
    @RepeatSubAnnotation
    @PostMapping("/release")
    @ApiOperation(value = "省市场监管局直属单位内审工作联系表任务书下达", notes = "省市场监管局直属单位内审工作联系表任务书下达")
    @SystemLogAnnotation(type = "省市场监管局直属单位内审工作联系表", value = "省市场监管局直属单位内审工作联系表任务书下达")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
    public RestApiResponse<?> releaseInnerAuditWorkContacts(@RequestBody InnerAuditWorkContactsVo innerAuditWorkContactsVo) {
        String id = innerAuditWorkContactsService.releaseInnerAuditWorkContacts(innerAuditWorkContactsVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param innerAuditWorkContactsVo 省市场监管局直属单位内审工作联系表 查询条件
     * @return RestApiResponse<?>
     * @Description: 省市场监管局直属单位内审工作联系表待办
     * @Author: sef
     */
    @PostMapping("/todo")
    @ApiOperation(value = "省市场监管局直属单位内审工作联系表待办", notes = "省市场监管局直属单位内审工作联系表待办")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
    public RestApiResponse<?> todoList(@RequestBody InnerAuditWorkContactsVo innerAuditWorkContactsVo) {
        PageInfo<InnerAuditWorkContacts> innerAuditWorkContacts = innerAuditWorkContactsService.todoList(innerAuditWorkContactsVo);
        return RestApiResponse.ok(innerAuditWorkContacts);
    }

    /**
     * @param innerAuditWorkContactsVo 专家委员会库 查询条件
     * @return RestApiResponse<?>
     * @Description: 省市场监管局直属单位内审工作联系表已办
     * @Author: sef
     */
    @PostMapping("/finished")
    @ApiOperation(value = "省市场监管局直属单位内审工作联系表已办", notes = "省市场监管局直属单位内审工作联系表已办")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
    public RestApiResponse<?> finished(@RequestBody InnerAuditWorkContactsVo innerAuditWorkContactsVo) {
        PageInfo<InnerAuditWorkContacts> innerAuditWorkContacts = innerAuditWorkContactsService.finishedList(innerAuditWorkContactsVo);
        return RestApiResponse.ok(innerAuditWorkContacts);
    }

    /**
     * @param innerAuditWorkContactsVo 省市场监管局直属单位内审工作联系表 查询条件
     * @return RestApiResponse<?>
     * @Description: 省市场监管局直属单位内审工作联系表已完成
     * @Author: sef
     */
    @PostMapping("/end")
    @ApiOperation(value = "省市场监管局直属单位内审工作联系表已完成", notes = "省市场监管局直属单位内审工作联系表已完成")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
    public RestApiResponse<?> endList(@RequestBody InnerAuditWorkContactsVo innerAuditWorkContactsVo) {
        PageInfo<InnerAuditWorkContacts> innerAuditWorkContacts = innerAuditWorkContactsService.endList(innerAuditWorkContactsVo);
        return RestApiResponse.ok(innerAuditWorkContacts);
    }

    @PostMapping("/findList")
    @ApiOperation(value = "分页查询浙江省内部审计总审计师配备情况表", notes = "分页查询浙江省内部审计总审计师配备情况表")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
    public RestApiResponse<?> findList(@RequestBody InnerAuditWorkContactsVo vo) {
        return RestApiResponse.ok(innerAuditWorkContactsService.findList(vo));
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出浙江省内部审计总审计师配备情况表", notes = "导出浙江省内部审计总审计师配备情况表")
    public void export(@RequestBody InnerAuditWorkContactsVo vo) throws IOException {
        List list = new ArrayList<>();

        String roleCode = getUserRoleList().get(0).getRoleCode();
        if ("AUDIT_PROVINCE".equals(roleCode)) {
            list = innerAuditWorkContactsService.exportInnerAuditWorkContactsVoAllUnit(vo);
        } else if ("AUDIT_PROVINCE_DIRECT".equals(roleCode)){
            list = innerAuditWorkContactsService.exportInnerAuditWorkContactsVo(vo);
        }
        // 文件名
        String prefix = "/word/";
        InputStream inputStream = ExportUtil.getInputStream(prefix + "直属单位内审工作联系表导出模板.xlsx");
        EasyExcelUtils.exportTemplateFill(null, list, "Sheet1", "省市场监管局直属单位内审工作联系表.xlsx", response, inputStream, true);
    }

}
