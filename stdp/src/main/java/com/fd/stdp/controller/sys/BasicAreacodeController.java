package com.fd.stdp.controller.sys;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fd.stdp.beans.sys.AreaTree;
import com.fd.stdp.beans.sys.BasicAreacode;
import com.fd.stdp.beans.sys.vo.BasicAreacodeVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.OperationLogAnnotation;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.service.sys.BasicAreacodeService;
import com.github.pagehelper.PageInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 行政区划信息
 * @Author: linqiang
 * @Date: 2020-07-07 19:11:41
 */
@RestController
@RequestMapping("/sys/basicAreacode")
@Api(value = "行政区划信息", description = "行政区划信息")
public class BasicAreacodeController extends BaseController {

	@Autowired
	private BasicAreacodeService basicAreacodeService;

	private final String PER_PREFIX = "btn:sys:area:";
//
//	/**
//	 * @Description: 新增行政区划信息
//	 * @param basicAreacode 行政区划信息数据 json
//	 * @return RestApiResponse<?>
//	 * @Author: linqiang
//	 */
//	@RepeatSubAnnotation
//	@PostMapping("/save")
//	@ApiOperation(value = "新增行政区划信息", notes = "新增行政区划信息")
//	@SystemLogAnnotation(type = "行政区划信息", value = "新增行政区划信息")
//	@OperationLogAnnotation(notes = "新增行政区划信息{行政区划code:areaCode}", module = "基础信息管理", submodule = "行政区划")
//	@PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
//	public RestApiResponse<?> saveBasicAreacode(@RequestBody BasicAreacode basicAreacode) {
//		String id = basicAreacodeService.saveOrUpdateBasicAreacode(basicAreacode);
//		return RestApiResponse.ok(id);
//	}
//
//	/**
//	 * @Description: 修改行政区划信息
//	 * @param basicAreacode 行政区划信息数据 json
//	 * @return RestApiResponse<?>
//	 * @Author: linqiang
//	 */
//	@RepeatSubAnnotation
//	@PostMapping("/update")
//	@ApiOperation(value = "修改行政区划信息", notes = "修改行政区划信息")
//	@SystemLogAnnotation(type = "行政区划信息", value = "修改行政区划信息")
//	@PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
//	@OperationLogAnnotation(notes = "修改行政区划信息{行政区划code:areaCode}", module = "基础信息管理", submodule = "行政区划")
//	public RestApiResponse<?> updateBasicAreacode(@RequestBody BasicAreacode basicAreacode) {
//		String id = basicAreacodeService.saveOrUpdateBasicAreacode(basicAreacode);
//		return RestApiResponse.ok(id);
//	}
//
//	/**
//	 * @Description: 删除行政区划信息(判断 关联数据是否可以删除)
//	 * @param ids
//	 * @return RestApiResponse<?>
//	 * @Author: linqiang
//	 */
//	@RepeatSubAnnotation
//	@PostMapping("/deleteArea")
//	@ApiOperation(value = "批量删除行政区划信息", notes = "批量删除行政区划信息")
//	@SystemLogAnnotation(type = "批量删除行政区划信息", value = "批量删除删除行政区划信息")
//	@OperationLogAnnotation(notes = "批量删除行政区划信息{行政区划id集合:ids}", module = "基础信息管理", submodule = "行政区划")
//	@PreAuthorize("hasAuthority('" + PER_PREFIX + "batch:del')")
//	public RestApiResponse<?> deleteBatchAreacode(@RequestBody List<String> ids) {
//		// 判断 关联数据是否可以删除
//		basicAreacodeService.deleteBatchAreacode(ids);
//		return RestApiResponse.ok();
//	}
//
//	/**
//	 * @Description: 删除行政区划信息(判断 关联数据是否可以删除)
//	 * @param id
//	 * @return RestApiResponse<?>
//	 * @Author: linqiang
//	 */
//	@RepeatSubAnnotation
//	@GetMapping("/delete")
//	@ApiOperation(value = "删除行政区划信息", notes = "删除行政区划信息")
//	@SystemLogAnnotation(type = "行政区划信息", value = "删除行政区划信息")
//	@OperationLogAnnotation(notes = "删除行政区划信息{行政区划id:id}", module = "基础信息管理", submodule = "行政区划")
//	@PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
//	public RestApiResponse<?> deleteBasicAreacode(@RequestParam("id") String id) {
//		// 判断 关联数据是否可以删除
//		basicAreacodeService.deleteBasicAreacode(id);
//		return RestApiResponse.ok();
//	}
//
//	/**
//	 * @Description: 查询行政区划信息详情
//	 * @param id
//	 * @return RestApiResponse<?>
//	 * @Author: linqiang
//	 */
//	@GetMapping("/findById/{id}")
//	@ApiOperation(value = "查询行政区划信息详情", notes = "查询行政区划信息详情")
//	@PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
//	public RestApiResponse<?> findById(@PathVariable("id") String id) {
//		BasicAreacode basicAreacode = basicAreacodeService.findById(id);
//		return RestApiResponse.ok(basicAreacode);
//	}
//
//	/**
//	 * @Description: 分页查询行政区划信息
//	 * @param basicAreacodevo 行政区划信息 查询条件
//	 * @return RestApiResponse<?>
//	 * @Author: linqiang
//	 */
//	@PostMapping("/findPageByQuery")
//	@ApiOperation(value = "分页查询行政区划信息", notes = "分页查询行政区划信息")
//	@PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
//	public RestApiResponse<?> findPageByQuery(@RequestBody BasicAreacodeVo basicAreacodeVo) {
//		PageInfo<BasicAreacode> basicAreacode = basicAreacodeService.findPageByQuery(basicAreacodeVo);
//		return RestApiResponse.ok(basicAreacode);
//	}

	/**
	 * @Description 初始化行政区划树
	 * @param name
	 * @return List<DictTree>
	 */
	@RequestMapping(value = "/tree", method = RequestMethod.GET)
	@ApiOperation(value = "行政区划树", notes = "行政区划树")
	@ResponseBody
	public List<AreaTree> getTree(String code) {
		// 生成树
		List<AreaTree> areaTree = basicAreacodeService.getTree(code);
		return areaTree;
	}

	/**
	 * 获取行政区划
	 * @param code
	 * @return
	 */
	@RequestMapping(value = "/tree/{code}", method = RequestMethod.POST)
	@ApiOperation(value = "行政区划树", notes = "行政区划树")
	@ResponseBody
	public List<AreaTree> getTree1(@PathVariable String code) {
		// 生成树
		List<AreaTree> areaTree = basicAreacodeService.getTree(code);
		return areaTree;
	}

	/**
	 * @Description: 获取所在省份
	 * @param id
	 * @return RestApiResponse<?>
	 * @Author: yujianfei
//	 */
//	@GetMapping("/findProvince")
//	@ApiOperation(value = "查询行政区划信息详情", notes = "查询行政区划信息详情")
//	//@PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
//	public RestApiResponse<?> findProvince(@PathVariable("id") String id) {
//		BasicAreacode basicAreacode = basicAreacodeService.findById(id);
//		return RestApiResponse.ok(basicAreacode);
//	}

	/**
	 * @Description: 获取省份
	 * @return RestApiResponse<?>
	 * @Author: yujianfei
	 */
	@GetMapping("/getProvince")
	@ApiOperation(value = "查询行政区划信息详情", notes = "查询行政区划信息详情")
	//@PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
	public RestApiResponse<?> getProvince() {
		return RestApiResponse.ok(basicAreacodeService.getProvince());
	}

}
