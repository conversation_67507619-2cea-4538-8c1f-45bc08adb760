package com.fd.stdp.controller.talent;

import com.fd.stdp.beans.talent.TalentLeaderSubjectContractChange;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectContractChangeVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectExamine;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectExamineVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.talent.TalentLeaderSubjectExamineService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 学科带头人考核表
 *@Author: wangsh
 *@Date: 2022-02-14 10:05:09
 */
@RestController
@RequestMapping("/talent/talentLeaderSubjectExamine")
@Api(value="学科带头人考核表", description="学科带头人考核表")
public class TalentLeaderSubjectExamineController  extends BaseController {

	@Autowired
	private TalentLeaderSubjectExamineService talentLeaderSubjectExamineService;
	
	private final String PER_PREFIX = "talent:but:examine:";
	
	/**
	 *@Description: 新增学科带头人考核表
	 *@param vo 学科带头人考核表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增学科带头人考核表", notes = "新增学科带头人考核表")
	@SystemLogAnnotation(type = "学科带头人考核表",value = "新增学科带头人考核表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTalentLeaderSubjectExamine(@RequestBody TalentLeaderSubjectExamineVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentLeaderSubjectExamineService.saveOrUpdateTalentLeaderSubjectExamine(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改学科带头人考核表
	 *@param talentLeaderSubjectExamine 学科带头人考核表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改学科带头人考核表", notes = "修改学科带头人考核表")
	@SystemLogAnnotation(type = "学科带头人考核表",value = "修改学科带头人考核表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTalentLeaderSubjectExamine(@RequestBody TalentLeaderSubjectExamineVo talentLeaderSubjectExamine) {
		String id = talentLeaderSubjectExamineService.saveOrUpdateTalentLeaderSubjectExamine(talentLeaderSubjectExamine);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除学科带头人考核表(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除学科带头人考核表", notes = "删除学科带头人考核表")
	@SystemLogAnnotation(type = "学科带头人考核表",value = "删除学科带头人考核表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTalentLeaderSubjectExamine(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		talentLeaderSubjectExamineService.deleteTalentLeaderSubjectExamine(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除学科带头人考核表(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除学科带头人考核表", notes = "删除学科带头人考核表")
	@SystemLogAnnotation(type = "学科带头人考核表",value = "删除学科带头人考核表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiTalentLeaderSubjectExamine(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		talentLeaderSubjectExamineService.deleteMultiTalentLeaderSubjectExamine(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询学科带头人考核表详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询学科带头人考核表详情", notes = "查询学科带头人考核表详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		TalentLeaderSubjectExamine  talentLeaderSubjectExamine=talentLeaderSubjectExamineService.findById(id);
		return RestApiResponse.ok(talentLeaderSubjectExamine);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			TalentLeaderSubjectExamineVo vo = new TalentLeaderSubjectExamineVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<TalentLeaderSubjectExamine> pageInfo = (PageInfo<TalentLeaderSubjectExamine>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
	
	/**
	 *@Description: 分页查询学科带头人考核表
	 *@param talentLeaderSubjectExamineVo 学科带头人考核表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询学科带头人考核表", notes = "分页查询学科带头人考核表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo) {
		PageInfo<TalentLeaderSubjectExamine>  talentLeaderSubjectExamine=talentLeaderSubjectExamineService.findPageByQuery(talentLeaderSubjectExamineVo);
		return RestApiResponse.ok(talentLeaderSubjectExamine);
	}

	/**
	 *@Description: 学科带头人考核表提交
	 *@param talentLeaderSubjectExamineVo 学科带头人考核表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "学科带头人考核表提交", notes = "学科带头人考核表提交")
	@SystemLogAnnotation(type = "学科带头人考核表",value = "学科带头人考核表提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTalentLeaderSubjectExamine(@RequestBody TalentLeaderSubjectExamineVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentLeaderSubjectExamineService.submitTalentLeaderSubjectExamine(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 学科带头人考核表审核
	 *@param talentLeaderSubjectExamineVo 学科带头人考核表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "学科带头人考核表审核", notes = "学科带头人考核表审核")
	@SystemLogAnnotation(type = "学科带头人考核表",value = "学科带头人考核表审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTalentLeaderSubjectExamine(@RequestBody TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo) {
		String id = talentLeaderSubjectExamineService.auditTalentLeaderSubjectExamine(talentLeaderSubjectExamineVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 学科带头人考核表退回
	 *@param talentLeaderSubjectExamineVo 学科带头人考核表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "学科带头人考核表退回", notes = "学科带头人考核表退回")
	@SystemLogAnnotation(type = "学科带头人考核表",value = "学科带头人考核表退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTalentLeaderSubjectExamine(@RequestBody TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo) {
		String id = talentLeaderSubjectExamineService.sendBackTalentLeaderSubjectExamine(talentLeaderSubjectExamineVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 学科带头人考核表任务书下达
	 *@param talentLeaderSubjectExamineVo 学科带头人考核表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "学科带头人考核表任务书下达", notes = "学科带头人考核表任务书下达")
	@SystemLogAnnotation(type = "学科带头人考核表",value = "学科带头人考核表任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseTalentLeaderSubjectExamine(@RequestBody TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo) {
		String id = talentLeaderSubjectExamineService.releaseTalentLeaderSubjectExamine(talentLeaderSubjectExamineVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 学科带头人考核表待办
	 *@param vo 学科带头人考核表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "学科带头人考核表待办", notes = "学科带头人考核表待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody TalentLeaderSubjectExamineVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = talentLeaderSubjectExamineService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = talentLeaderSubjectExamineService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = talentLeaderSubjectExamineService.findPageByQuery(vo);
		} else {
			list = talentLeaderSubjectExamineService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 学科带头人考核表已办
	 *@param talentLeaderSubjectExamineVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "学科带头人考核表已办", notes = "学科带头人考核表已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo) {
		PageInfo<TalentLeaderSubjectExamine>  talentLeaderSubjectExamine=talentLeaderSubjectExamineService.finishedList(talentLeaderSubjectExamineVo);
		return RestApiResponse.ok(talentLeaderSubjectExamine);
	}
}
