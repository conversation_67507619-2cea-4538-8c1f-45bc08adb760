package com.fd.stdp.controller.sys;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fd.stdp.beans.sys.SysLogRecord;
import com.fd.stdp.beans.sys.vo.SysLogRecordExportVo;
import com.fd.stdp.beans.sys.vo.SysLogRecordVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.service.sys.SysLogService;
import com.fd.stdp.util.EasyExcelUtils;
import com.github.pagehelper.PageInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 系统日志
 * @Author: hzh
 * @Date: 2019-11-04 14:33:24
 */
@RestController
@RequestMapping("/sys/accesslog")
@Api(value = "系统日志", description = "系统日志")
public class SystemLogController extends BaseController {

	@Autowired
	private SysLogService sysLogService;

	private final String PER_PREFIX = "btn:sys:log:";

	// /**
	// *
	// * @Description: 新增日志记录
	// * @param logRecord
	// * 日志记录数据 json
	// * @return RestApiResponse<?>
	// * @Author: linqiang
	// */
	// @PostMapping("/save")
	// @ResponseBody
	// @ApiOperation(value = "新增日志记录", notes = "新增日志记录")
	// @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
	// public RestApiResponse<?> save(@RequestBody SysLogRecord sysLogRecord) {
	// String id = sysLogService.saveOrUpdateLogRecord(sysLogRecord);
	// return RestApiResponse.ok(id);
	// }

	/**
	 * @param sysLogRecord 用户操作日志表 查询条件
	 * @return RestApiResponse<?>
	 * @Description: 分页查询用户操作日志表
	 * @Author: szx
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询用户操作日志表", notes = "分页查询用户操作日志表")
	@PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody SysLogRecordVo sysLogRecordVo) {
		PageInfo<SysLogRecord> sysLogOper = sysLogService.findAllPage(sysLogRecordVo);
		return RestApiResponse.ok(sysLogOper);
	}

	/**
	 * @param sysLogRecord 用户操作日志表 查询条件
	 * @return RestApiResponse<?>
	 * @Description: 分页查询用户操作日志表
	 * @Author: szx
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询日志", notes = "查询日志")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
	public RestApiResponse<?> findById(@RequestParam(name = "id") String id) {
		SysLogRecord record = sysLogService.findById(id);
		return RestApiResponse.ok(record);
	}

	/**
	 * @param sysLogOperVo void
	 * @Description:导出访问日志
	 * @Author: szx
	 */
	@PostMapping("/export")
	@ApiOperation(value = "导出访问日志", notes = "导出访问日志")
	@PreAuthorize("hasAuthority('" + PER_PREFIX + "export')")
	public void export(SysLogRecordVo sysLogRecordVo) {
		List<SysLogRecordExportVo> list = sysLogService.export(sysLogRecordVo);
		EasyExcelUtils.exportExcel(list, "导出访问日志结果", SysLogRecordExportVo.class, "导出访问日志结果", response);
	}

}
