package com.fd.stdp.controller.audit;

import com.fd.stdp.beans.audit.InnerAuditProjectInformation;
import com.fd.stdp.beans.audit.vo.InnerAuditEquippedInformationVo;
import com.fd.stdp.beans.audit.vo.InnerAuditProjectInformationVo;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.constant.InnerAuditBasicConstant;
import com.fd.stdp.service.audit.InnerAuditProjectInformationsService;
import com.fd.stdp.util.EasyExcelUtils;
import com.fd.stdp.util.ExportUtil;
import com.fd.stdp.util.UUIDUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *@Description: 委托社会审计机构审计项目情况表
 *@Author: sef
 *@Date: 2022-06-06 13:56:38
 */
@RestController
@RequestMapping("/audit/innerAuditProjectInformations")
@Api(value="委托社会审计机构审计项目情况表", description="委托社会审计机构审计项目情况表")
public class InnerAuditProjectInformationController  extends BaseController {

	@Autowired
	private InnerAuditProjectInformationsService innerAuditProjectInformationervice;
	
	//private final String PER_PREFIX = "这里写业务前缀命名:but:模块:功能:";


	private final String PER_PREFIX = "btn:audit:Information:";
	
	/**
	 *@Description: 新增委托社会审计机构审计项目情况表
	 *@param innerAuditProjectInformationVo 委托社会审计机构审计项目情况表数据 json
	 *@return RestApiResponse<?>
	 *@Author: sef
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增委托社会审计机构审计项目情况表", notes = "新增委托社会审计机构审计项目情况表")
	@SystemLogAnnotation(type = "委托社会审计机构审计项目情况表",value = "新增委托社会审计机构审计项目情况表")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInnerAuditProjectInformation(@RequestBody InnerAuditProjectInformationVo innerAuditProjectInformationVo) {
		List<InnerAuditProjectInformationVo> contactList = innerAuditProjectInformationVo.getProjectList();
		innerAuditProjectInformationervice.saveBatchInnerAuditWorkContacts(contactList);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 修改委托社会审计机构审计项目情况表
	 *@param innerAuditProjectInformation 委托社会审计机构审计项目情况表数据 json
	 *@return RestApiResponse<?>
	 *@Author: sef
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改委托社会审计机构审计项目情况表", notes = "修改委托社会审计机构审计项目情况表")
	@SystemLogAnnotation(type = "委托社会审计机构审计项目情况表",value = "修改委托社会审计机构审计项目情况表")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInnerAuditProjectInformation(@RequestBody InnerAuditProjectInformationVo innerAuditProjectInformation) {
		String id =innerAuditProjectInformationervice.saveOrUpdateInnerAuditProjectInformation(innerAuditProjectInformation);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 批量删除委托社会审计机构审计项目情况表(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: sef
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除委托社会审计机构审计项目情况表", notes = "删除委托社会审计机构审计项目情况表")
	@SystemLogAnnotation(type = "委托社会审计机构审计项目情况表",value = "删除委托社会审计机构审计项目情况表")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiInnerAuditProjectInformation(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innerAuditProjectInformationervice.deleteMultiInnerAuditProjectInformation(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询委托社会审计机构审计项目情况表详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: sef
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询委托社会审计机构审计项目情况表详情", notes = "查询委托社会审计机构审计项目情况表详情")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InnerAuditProjectInformation innerAuditProjectInformation =innerAuditProjectInformationervice.findById(id);
		return RestApiResponse.ok(innerAuditProjectInformation);
	}
	
	/**
	 *@Description: 分页查询委托社会审计机构审计项目情况表
	 *@param innerAuditProjectInformationVo 委托社会审计机构审计项目情况表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: sef
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询委托社会审计机构审计项目情况表", notes = "分页查询委托社会审计机构审计项目情况表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnerAuditProjectInformationVo innerAuditProjectInformationVo) {
		PageInfo<InnerAuditProjectInformationVo>  innerAuditProjectInformation=innerAuditProjectInformationervice.findPageByQuery(innerAuditProjectInformationVo);
		return RestApiResponse.ok(innerAuditProjectInformation);
	}

	/**
	 *@Description: 委托社会审计机构审计项目情况表提交
	 *@param innerAuditProjectInformationVo 委托社会审计机构审计项目情况表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "委托社会审计机构审计项目情况表提交", notes = "委托社会审计机构审计项目情况表提交")
	@SystemLogAnnotation(type = "委托社会审计机构审计项目情况表",value = "委托社会审计机构审计项目情况表提交")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitInnerAuditProjectInformation(@RequestBody InnerAuditProjectInformationVo innerAuditProjectInformationVo) {
	    if(StringUtils.isBlank(innerAuditProjectInformationVo.getOrgName())) {
            innerAuditProjectInformationVo.setOrgName(getCurrentOrgName());
            innerAuditProjectInformationVo.setOrgCode(getLoginUser().getAreaCode());
		}
		String id = innerAuditProjectInformationervice.submitInnerAuditProjectInformation(innerAuditProjectInformationVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 委托社会审计机构审计项目情况表审核
	 *@param innerAuditProjectInformationVo 委托社会审计机构审计项目情况表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "委托社会审计机构审计项目情况表审核", notes = "委托社会审计机构审计项目情况表审核")
	@SystemLogAnnotation(type = "委托社会审计机构审计项目情况表",value = "委托社会审计机构审计项目情况表审核")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditInnerAuditProjectInformation(@RequestBody InnerAuditProjectInformationVo innerAuditProjectInformationVo) {
		String id = innerAuditProjectInformationervice.auditInnerAuditProjectInformation(innerAuditProjectInformationVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 委托社会审计机构审计项目情况表退回
	 *@param innerAuditProjectInformationVo 委托社会审计机构审计项目情况表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "委托社会审计机构审计项目情况表退回", notes = "委托社会审计机构审计项目情况表退回")
	@SystemLogAnnotation(type = "委托社会审计机构审计项目情况表",value = "委托社会审计机构审计项目情况表退回")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackInnerAuditProjectInformation(@RequestBody InnerAuditProjectInformationVo innerAuditProjectInformationVo) {
		String id =innerAuditProjectInformationervice.sendBackInnerAuditProjectInformation(innerAuditProjectInformationVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 委托社会审计机构审计项目情况表任务书下达
	 *@param innerAuditProjectInformationVo 委托社会审计机构审计项目情况表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "委托社会审计机构审计项目情况表任务书下达", notes = "委托社会审计机构审计项目情况表任务书下达")
	@SystemLogAnnotation(type = "委托社会审计机构审计项目情况表",value = "委托社会审计机构审计项目情况表任务书下达")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseInnerAuditProjectInformation(@RequestBody InnerAuditProjectInformationVo innerAuditProjectInformationVo) {
		String id = innerAuditProjectInformationervice.releaseInnerAuditProjectInformation(innerAuditProjectInformationVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 委托社会审计机构审计项目情况表待办
	 *@param innerAuditProjectInformationVo 委托社会审计机构审计项目情况表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "委托社会审计机构审计项目情况表待办", notes = "委托社会审计机构审计项目情况表待办")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody InnerAuditProjectInformationVo innerAuditProjectInformationVo) {
		PageInfo<InnerAuditProjectInformation>  innerAuditProjectInformation=innerAuditProjectInformationervice.todoList(innerAuditProjectInformationVo);
		return RestApiResponse.ok(innerAuditProjectInformation);
	}
	/**
	 *@Description: 委托社会审计机构审计项目情况表已办
	 *@param innerAuditProjectInformationVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "委托社会审计机构审计项目情况表已办", notes = "委托社会审计机构审计项目情况表已办")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody InnerAuditProjectInformationVo innerAuditProjectInformationVo) {
		PageInfo<InnerAuditProjectInformation>  innerAuditProjectInformation=innerAuditProjectInformationervice.finishedList(innerAuditProjectInformationVo);
		return RestApiResponse.ok(innerAuditProjectInformation);
	}
	
	/**
	 *@Description: 委托社会审计机构审计项目情况表已完成
	 *@param innerAuditProjectInformationVo 委托社会审计机构审计项目情况表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "委托社会审计机构审计项目情况表已完成", notes = "委托社会审计机构审计项目情况表已完成")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody InnerAuditProjectInformationVo innerAuditProjectInformationVo) {
		PageInfo<InnerAuditProjectInformation>  innerAuditProjectInformation=innerAuditProjectInformationervice.endList(innerAuditProjectInformationVo);
		return RestApiResponse.ok(innerAuditProjectInformation);
	}

	@PostMapping("/findList")
	@ApiOperation(value = "分页查询浙江省内部审计总审计师配备情况表", notes = "分页查询浙江省内部审计总审计师配备情况表")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findList(@RequestBody InnerAuditProjectInformationVo vo) {
		return RestApiResponse.ok(innerAuditProjectInformationervice.findList(vo));
	}

	@PostMapping("/export")
	@ApiOperation(value = "导出浙江省内部审计总审计师配备情况表", notes = "导出浙江省内部审计总审计师配备情况表")
	public void export(@RequestBody InnerAuditProjectInformationVo vo) throws IOException {
		List list = new ArrayList<>();

		String roleCode = getUserRoleList().get(0).getRoleCode();
		if ("AUDIT_PROVINCE".equals(roleCode)) {
			list = innerAuditProjectInformationervice.exportInnerAuditProjectInformationVoAllUnit(vo);
		} else if ("AUDIT_PROVINCE_DIRECT".equals(roleCode)){
			list = innerAuditProjectInformationervice.exportInnerAuditProjectInformationVo(vo);
		}

		// 文件名
		String prefix = "/word/";
		InputStream inputStream = ExportUtil.getInputStream(prefix + "委托社会审计机构审计项目情况表导出模板.xlsx");
		EasyExcelUtils.exportTemplateFill(null, list, "Sheet1", "委托社会审计机构审计项目情况表.xlsx", response, inputStream, true);
	}
}
