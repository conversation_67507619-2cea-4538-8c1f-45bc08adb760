package com.fd.stdp.controller.innovation;

import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryAcceptCommentVo;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryApplyCommentVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationLaboratoryApply;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryApplyVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.innovation.InnovationLaboratoryApplyService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 省局重点实验室培育申报
 *@Author: wangsh
 *@Date: 2022-02-09 16:45:56
 */
@RestController
@RequestMapping("/innovation/innovationLaboratoryApply")
@Api(value="省局重点实验室培育申报", description="省局重点实验室培育申报")
public class

InnovationLaboratoryApplyController  extends BaseController {

	@Autowired
	private InnovationLaboratoryApplyService innovationLaboratoryApplyService;
	
	private final String PER_PREFIX = "but:laboratory:apply:";
	
	/**
	 *@Description: 新增省局重点实验室培育申报
	 *@param vo 省局重点实验室培育申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增省局重点实验室培育申报", notes = "新增省局重点实验室培育申报")
	@SystemLogAnnotation(type = "省局重点实验室培育申报",value = "新增省局重点实验室培育申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInnovationLaboratoryApply(@RequestBody InnovationLaboratoryApplyVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = innovationLaboratoryApplyService.saveOrUpdateInnovationLaboratoryApply(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改省局重点实验室培育申报
	 *@param innovationLaboratoryApply 省局重点实验室培育申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改省局重点实验室培育申报", notes = "修改省局重点实验室培育申报")
	@SystemLogAnnotation(type = "省局重点实验室培育申报",value = "修改省局重点实验室培育申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInnovationLaboratoryApply(@RequestBody InnovationLaboratoryApplyVo innovationLaboratoryApply) {
		String id = innovationLaboratoryApplyService.saveOrUpdateInnovationLaboratoryApply(innovationLaboratoryApply);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除省局重点实验室培育申报(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除省局重点实验室培育申报", notes = "删除省局重点实验室培育申报")
	@SystemLogAnnotation(type = "省局重点实验室培育申报",value = "删除省局重点实验室培育申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteInnovationLaboratoryApply(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		innovationLaboratoryApplyService.deleteInnovationLaboratoryApply(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除省局重点实验室培育申报(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除省局重点实验室培育申报", notes = "删除省局重点实验室培育申报")
	@SystemLogAnnotation(type = "省局重点实验室培育申报",value = "删除省局重点实验室培育申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiInnovationLaboratoryApply(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innovationLaboratoryApplyService.deleteMultiInnovationLaboratoryApply(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询省局重点实验室培育申报详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询省局重点实验室培育申报详情", notes = "查询省局重点实验室培育申报详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该ID的数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}

		InnovationLaboratoryApply  innovationLaboratoryApply=innovationLaboratoryApplyService.findById(id);
		return RestApiResponse.ok(innovationLaboratoryApply);
	}
	
	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			InnovationLaboratoryApplyVo vo = new InnovationLaboratoryApplyVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据
			
			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();
			
			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};
			
			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<InnovationLaboratoryApply> pageInfo = (PageInfo<InnovationLaboratoryApply>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}
			
			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);
			
		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
	
	/**
	 *@Description: 分页查询省局重点实验室培育申报
	 *@param innovationLaboratoryApplyVo 省局重点实验室培育申报 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询省局重点实验室培育申报", notes = "分页查询省局重点实验室培育申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnovationLaboratoryApplyVo innovationLaboratoryApplyVo) {
		PageInfo<InnovationLaboratoryApply>  innovationLaboratoryApply=innovationLaboratoryApplyService.findPageByQuery(innovationLaboratoryApplyVo);
		return RestApiResponse.ok(innovationLaboratoryApply);
	}

	/**
	 *@Description: 省局重点实验室培育申报提交
	 *@param vo 省局重点实验室培育申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "省局重点实验室培育申报提交", notes = "省局重点实验室培育申报提交")
	@SystemLogAnnotation(type = "省局重点实验室培育申报",value = "省局重点实验室培育申报提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitInnovationLaboratoryApply(@RequestBody InnovationLaboratoryApplyVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = innovationLaboratoryApplyService.submitInnovationLaboratoryApply(vo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省局重点实验室培育申报审核
	 *@param innovationLaboratoryApply 省局重点实验室培育申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "省局重点实验室培育申报审核", notes = "省局重点实验室培育申报审核")
	@SystemLogAnnotation(type = "省局重点实验室培育申报",value = "省局重点实验室培育申报审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditInnovationLaboratoryApply(@RequestBody InnovationLaboratoryApplyVo innovationLaboratoryApply) {
		String id = innovationLaboratoryApplyService.auditInnovationLaboratoryApply(innovationLaboratoryApply);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省局重点实验室培育申报提交
	 *@param innovationLaboratoryApply 省局重点实验室培育申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "省局重点实验室培育申报提交", notes = "省局重点实验室培育申报提交")
	@SystemLogAnnotation(type = "省局重点实验室培育申报",value = "省局重点实验室培育申报提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackInnovationLaboratoryApply(@RequestBody InnovationLaboratoryApplyVo innovationLaboratoryApply) {
		String id = innovationLaboratoryApplyService.sendBackInnovationLaboratoryApply(innovationLaboratoryApply);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省局重点实验室培育申报选择专家
	 *@param innovationLaboratoryApply 省局重点实验室培育申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/choseExpert")
	@ApiOperation(value = "省局重点实验室培育申报选择专家", notes = "省局重点实验室培育申报选择专家")
	@SystemLogAnnotation(type = "省局重点实验室培育申报",value = "省局重点实验室培育申报选择专家")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> choseExpertInnovationLaboratoryApply(@RequestBody InnovationLaboratoryApplyVo innovationLaboratoryApply) {
		String id = innovationLaboratoryApplyService.sendBackInnovationLaboratoryApply(innovationLaboratoryApply);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 申报/直接命名申报专家评审提交
	 *@param innovationLaboratoryApplyCommentVo 省局重点实验室培育申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/expertSubmit")
	@ApiOperation(value = "专家评审提交", notes = "专家评审提交")
	@SystemLogAnnotation(type = "省局重点实验室培育申报",value = "专家评审提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> expoertSubmitInnovationLaboratoryApply(@RequestBody InnovationLaboratoryApplyCommentVo innovationLaboratoryApplyCommentVo) {
		String id = innovationLaboratoryApplyService.expoertSubmitInnovationLaboratoryApply(innovationLaboratoryApplyCommentVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 验收评审/变更评审/中期评估专家评审提交
	 *@param innovationLaboratoryAcceptCommentVo 省局重点实验室培育申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/expoertSubmitII")
	@ApiOperation(value = "专家评审提交", notes = "专家评审提交")
	@SystemLogAnnotation(type = "省局重点实验室培育申报",value = "专家评审提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> expoertSubmitInnovationLaboratoryApplyII(@RequestBody InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo) {
		String id = innovationLaboratoryApplyService.expoertSubmitIIInnovationLaboratoryApply(innovationLaboratoryAcceptCommentVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省局重点实验室培育申报任务书下达
	 *@param innovationLaboratoryApply 省局重点实验室培育申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "省局重点实验室培育申报任务书下达", notes = "省局重点实验室培育申报任务书下达")
	@SystemLogAnnotation(type = "省局重点实验室培育申报",value = "省局重点实验室培育申报任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseInnovationLaboratoryApply(@RequestBody InnovationLaboratoryApplyVo innovationLaboratoryApply) {
		String id = innovationLaboratoryApplyService.releaseInnovationLaboratoryApply(innovationLaboratoryApply);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省局重点实验室培育申报待办
	 *@param vo 省局重点实验室培育申报 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "省局重点实验室培育申报待办", notes = "省局重点实验室培育申报待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody InnovationLaboratoryApplyVo vo) {
		PageInfo<InnovationLaboratoryApply> list = null;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = innovationLaboratoryApplyService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = innovationLaboratoryApplyService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = innovationLaboratoryApplyService.endList(vo);
		} else {
			list = innovationLaboratoryApplyService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 省局重点实验室培育申报已办
	 *@param innovationLaboratoryApplyVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "省局重点实验室培育申报已办", notes = "省局重点实验室培育申报已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody InnovationLaboratoryApplyVo innovationLaboratoryApplyVo) {
		PageInfo<InnovationLaboratoryApply>  innovationLaboratoryApply=innovationLaboratoryApplyService.finishedList(innovationLaboratoryApplyVo);
		return RestApiResponse.ok(innovationLaboratoryApply);
	}

	/**
	 *@Description: 省局重点实验室培育申报已完成
	 *@param innovationLaboratoryApplyVo 省局重点实验室培育申报 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "省局重点实验室培育申报已完成", notes = "省局重点实验室培育申报已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody InnovationLaboratoryApplyVo innovationLaboratoryApplyVo) {
		PageInfo<InnovationLaboratoryApply>  innovationLaboratoryApply=innovationLaboratoryApplyService.endList(innovationLaboratoryApplyVo);
		return RestApiResponse.ok(innovationLaboratoryApply);
	}
}
