package com.fd.stdp.controller.innovation;

import com.fd.stdp.beans.innovation.InnovationQualityApply;
import com.fd.stdp.beans.innovation.vo.InnovationQualityApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityApply;
import com.fd.stdp.beans.innovation.vo.InnovationQualityApplyVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.innovation.InnovationQualityApplyService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 省质检中心申报
 *@Author: wangsh
 *@Date: 2022-02-11 11:18:37
 */
@RestController
@RequestMapping("/innovation/innovationQualityApply")
@Api(value="省质检中心申报", description="省质检中心申报")
public class InnovationQualityApplyController  extends BaseController {

	@Autowired
	private InnovationQualityApplyService innovationQualityApplyService;
	
	private final String PER_PREFIX = "but:quality:apply:";
	
	/**
	 *@Description: 新增省质检中心申报
	 *@param vo 省质检中心申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增省质检中心申报", notes = "新增省质检中心申报")
	@SystemLogAnnotation(type = "省质检中心申报",value = "新增省质检中心申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInnovationQualityApply(@RequestBody InnovationQualityApplyVo vo) {
		if(StringUtils.isBlank(vo.getOrgName())){
			vo.setOrgName(getCurrentOrgName());
			vo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityApplyService.saveOrUpdateInnovationQualityApply(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改省质检中心申报
	 *@param innovationQualityApply 省质检中心申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改省质检中心申报", notes = "修改省质检中心申报")
	@SystemLogAnnotation(type = "省质检中心申报",value = "修改省质检中心申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInnovationQualityApply(@RequestBody InnovationQualityApplyVo innovationQualityApply) {
		String id = innovationQualityApplyService.saveOrUpdateInnovationQualityApply(innovationQualityApply);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除省质检中心申报(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除省质检中心申报", notes = "删除省质检中心申报")
	@SystemLogAnnotation(type = "省质检中心申报",value = "删除省质检中心申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteInnovationQualityApply(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		innovationQualityApplyService.deleteInnovationQualityApply(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除省质检中心申报(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除省质检中心申报", notes = "删除省质检中心申报")
	@SystemLogAnnotation(type = "省质检中心申报",value = "删除省质检中心申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiInnovationQualityApply(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innovationQualityApplyService.deleteMultiInnovationQualityApply(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询省质检中心申报详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询省质检中心申报详情", notes = "查询省质检中心申报详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该ID的数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		
		InnovationQualityApply  innovationQualityApply=innovationQualityApplyService.findById(id);
		return RestApiResponse.ok(innovationQualityApply);
	}
	
	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			InnovationQualityApplyVo vo = new InnovationQualityApplyVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据
			
			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();
			
			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};
			
			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<InnovationQualityApply> pageInfo = (PageInfo<InnovationQualityApply>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}
			
			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);
			
		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
	
	/**
	 *@Description: 分页查询省质检中心申报
	 *@param innovationQualityApplyVo 省质检中心申报 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询省质检中心申报", notes = "分页查询省质检中心申报")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnovationQualityApplyVo innovationQualityApplyVo) {
		PageInfo<InnovationQualityApply>  innovationQualityApply=innovationQualityApplyService.findPageByQuery(innovationQualityApplyVo);
		return RestApiResponse.ok(innovationQualityApply);
	}

	/**
	 *@Description: 省质检中心申报提交
	 *@param vo 省质检中心申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "省质检中心申报提交", notes = "省质检中心申报提交")
	@SystemLogAnnotation(type = "省质检中心申报",value = "省质检中心申报提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitInnovationQualityApply(@RequestBody InnovationQualityApplyVo vo) {
		if(StringUtils.isBlank(vo.getOrgName())){
			vo.setOrgName(getCurrentOrgName());
			vo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityApplyService.submitInnovationQualityApply(vo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心申报审核
	 *@param innovationQualityApplyVo 省质检中心申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "省质检中心申报审核", notes = "省质检中心申报审核")
	@SystemLogAnnotation(type = "省质检中心申报",value = "省质检中心申报审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditInnovationQualityApply(@RequestBody InnovationQualityApplyVo innovationQualityApplyVo) {
		String id = innovationQualityApplyService.auditInnovationQualityApply(innovationQualityApplyVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心申报退回
	 *@param innovationQualityApplyVo 省质检中心申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "省质检中心申报退回", notes = "省质检中心申报退回")
	@SystemLogAnnotation(type = "省质检中心申报",value = "省质检中心申报退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackInnovationQualityApply(@RequestBody InnovationQualityApplyVo innovationQualityApplyVo) {
		String id = innovationQualityApplyService.sendBackInnovationQualityApply(innovationQualityApplyVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心申报选择专家
	 *@param innovationQualityApplyVo 省质检中心申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/choseExpert")
	@ApiOperation(value = "省质检中心申报选择专家", notes = "省质检中心申报选择专家")
	@SystemLogAnnotation(type = "省质检中心申报",value = "省质检中心申报选择专家")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> choseExpertInnovationQualityApply(@RequestBody InnovationQualityApplyVo innovationQualityApplyVo) {
		String id = innovationQualityApplyService.choseExpertInnovationQualityApply(innovationQualityApplyVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心申报专家评审提交
	 *@param innovationQualityApplyVo 省质检中心申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/expoertSubmit")
	@ApiOperation(value = "省质检中心申报选择专家", notes = "省质检中心申报选择专家")
	@SystemLogAnnotation(type = "省质检中心申报",value = "省质检中心申报选择专家")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> expoertSubmitInnovationQualityApply(@RequestBody InnovationQualityApplyVo innovationQualityApplyVo) {
		String id = innovationQualityApplyService.expoertSubmitInnovationQualityApply(innovationQualityApplyVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心申报任务书下达
	 *@param innovationQualityApplyVo 省质检中心申报数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "省质检中心申报任务书下达", notes = "省质检中心申报任务书下达")
	@SystemLogAnnotation(type = "省质检中心申报",value = "省质检中心申报任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseInnovationQualityApply(@RequestBody InnovationQualityApplyVo innovationQualityApplyVo) {
		String id = innovationQualityApplyService.releaseInnovationQualityApply(innovationQualityApplyVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心申报待办
	 *@param vo 省质检中心申报 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "省质检中心申报待办", notes = "省质检中心申报待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody InnovationQualityApplyVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = innovationQualityApplyService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = innovationQualityApplyService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = innovationQualityApplyService.endList(vo);
		} else {
			list = innovationQualityApplyService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 省质检中心申报已办
	 *@param innovationQualityApplyVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "省质检中心申报已办", notes = "省质检中心申报已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody InnovationQualityApplyVo innovationQualityApplyVo) {
		PageInfo<InnovationQualityApply>  innovationQualityApply=innovationQualityApplyService.finishedList(innovationQualityApplyVo);
		return RestApiResponse.ok(innovationQualityApply);
	}

	/**
	 *@Description: 省质检中心申报已完成
	 *@param innovationQualityApplyVo 省质检中心申报 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "省质检中心申报已完成", notes = "省质检中心申报已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody InnovationQualityApplyVo innovationQualityApplyVo) {
		PageInfo<InnovationQualityApply>  innovationQualityApply=innovationQualityApplyService.endList(innovationQualityApplyVo);
		return RestApiResponse.ok(innovationQualityApply);
	}
}
