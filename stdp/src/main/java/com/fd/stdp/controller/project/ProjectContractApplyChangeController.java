package com.fd.stdp.controller.project;

import com.fd.stdp.beans.project.ProjectApplyExpertMumber;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertMumberVo;
import com.fd.stdp.enums.QueryTypeEnum;
import com.fd.stdp.service.project.ProjectContractApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractApplyChange;
import com.fd.stdp.beans.project.vo.ProjectContractApplyChangeVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectContractApplyChangeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 任务书申报表变更
 *@Author: wangsh
 *@Date: 2022-02-15 15:33:35
 */
@RestController
@RequestMapping("/project/projectContractApplyChange")
@Api(value="任务书申报表变更", description="任务书申报表变更")
public class ProjectContractApplyChangeController  extends BaseController {

	@Autowired
	private ProjectContractApplyChangeService projectContractApplyChangeService;
	@Autowired
	private ProjectContractApplyService projectContractApplyService;
	
	private final String PER_PREFIX = "btn:project:change:";
	
	/**
	 *@Description: 新增任务书申报表变更
	 *@param vo 任务书申报表变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增任务书申报表变更", notes = "新增任务书申报表变更")
	@SystemLogAnnotation(type = "任务书申报表变更",value = "新增任务书申报表变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveProjectContractApplyChange(@RequestBody ProjectContractApplyChangeVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = projectContractApplyChangeService.saveOrUpdateProjectContractApplyChange(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改任务书申报表变更
	 *@param projectContractApplyChange 任务书申报表变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改任务书申报表变更", notes = "修改任务书申报表变更")
	@SystemLogAnnotation(type = "任务书申报表变更",value = "修改任务书申报表变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateProjectContractApplyChange(@RequestBody ProjectContractApplyChangeVo projectContractApplyChange) {
		String id = projectContractApplyChangeService.saveOrUpdateProjectContractApplyChange(projectContractApplyChange);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除任务书申报表变更(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除任务书申报表变更", notes = "删除任务书申报表变更")
	@SystemLogAnnotation(type = "任务书申报表变更",value = "删除任务书申报表变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteProjectContractApplyChange(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		projectContractApplyChangeService.deleteProjectContractApplyChange(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除任务书申报表变更(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除任务书申报表变更", notes = "删除任务书申报表变更")
	@SystemLogAnnotation(type = "任务书申报表变更",value = "删除任务书申报表变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiProjectContractApplyChange(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		projectContractApplyChangeService.deleteMultiProjectContractApplyChange(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询任务书申报表变更详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询任务书申报表变更详情", notes = "查询任务书申报表变更详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		ProjectContractApplyChange  projectContractApplyChange=projectContractApplyChangeService.findById(id);
		return RestApiResponse.ok(projectContractApplyChange);
	}

	/**
	 *@Description: 查询任务书申报表变更详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findOriginById")
	@ApiOperation(value = "查询任务书申报表变更原始记录详情", notes = "查询任务书申报表变更原始记录详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findOriginById(@RequestParam("id") String id) {
		ProjectContractApplyChange  projectContractApplyChange = projectContractApplyChangeService.findById(id);
		return RestApiResponse.ok(projectContractApplyService.findById(projectContractApplyChange.getOriginContractId()));
	}
	
	/**
	 *@Description: 分页查询任务书申报表变更
	 *@param projectContractApplyChangeVo 任务书申报表变更 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询任务书申报表变更", notes = "分页查询任务书申报表变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody ProjectContractApplyChangeVo projectContractApplyChangeVo) {
		PageInfo<ProjectContractApplyChange>  projectContractApplyChange=projectContractApplyChangeService.findPageByQuery(projectContractApplyChangeVo);
		return RestApiResponse.ok(projectContractApplyChange);
	}

	/**
	 *@Description: 任务书申报表变更提交
	 *@param vo 任务书申报表变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "任务书申报表变更提交", notes = "任务书申报表变更提交")
	@SystemLogAnnotation(type = "任务书申报表变更",value = "任务书申报表变更提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitProjectContractApplyChange(@RequestBody ProjectContractApplyChangeVo vo) {

		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = projectContractApplyChangeService.submitProjectContractApplyChange(vo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书申报表变更审核
	 *@param projectContractApplyChangeVo 任务书申报表变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "任务书申报表变更审核", notes = "任务书申报表变更审核")
	@SystemLogAnnotation(type = "任务书申报表变更",value = "任务书申报表变更审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditProjectContractApplyChange(@RequestBody ProjectContractApplyChangeVo projectContractApplyChangeVo) {
		String id = projectContractApplyChangeService.auditProjectContractApplyChange(projectContractApplyChangeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书申报表变更退回
	 *@param projectContractApplyChangeVo 任务书申报表变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "任务书申报表变更退回", notes = "任务书申报表变更退回")
	@SystemLogAnnotation(type = "任务书申报表变更",value = "任务书申报表变更退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackProjectContractApplyChange(@RequestBody ProjectContractApplyChangeVo projectContractApplyChangeVo) {
		String id = projectContractApplyChangeService.sendBackProjectContractApplyChange(projectContractApplyChangeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书申报表变更专家论证提交
	 *@param vo 任务书申报表变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/expertSubmit")
	@ApiOperation(value = "任务书申报表变更专家论证提交", notes = "任务书申报表变更专家论证提交")
	@SystemLogAnnotation(type = "任务书申报表变更",value = "任务书申报表变更专家论证提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> expertSubmitProjectContractApplyChange(@RequestBody ProjectApplyExpertMumberVo vo) {
		String id = projectContractApplyChangeService.expertSubmitProjectContractApplyChange(vo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书申报表变更任务书下达
	 *@param projectContractApplyChangeVo 任务书申报表变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "任务书申报表变更任务书下达", notes = "任务书申报表变更任务书下达")
	@SystemLogAnnotation(type = "任务书申报表变更",value = "任务书申报表变更任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseProjectContractApplyChange(@RequestBody ProjectContractApplyChangeVo projectContractApplyChangeVo) {
		String id = projectContractApplyChangeService.releaseProjectContractApplyChange(projectContractApplyChangeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书申报表变更待办
	 *@param vo 任务书申报表变更 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "任务书申报表变更待办", notes = "任务书申报表变更待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody ProjectContractApplyChangeVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = projectContractApplyChangeService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = projectContractApplyChangeService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = projectContractApplyChangeService.endList(vo);
		} else {
			list = projectContractApplyChangeService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 任务书申报表变更已办
	 *@param projectContractApplyChangeVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "任务书申报表变更已办", notes = "任务书申报表变更已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody ProjectContractApplyChangeVo projectContractApplyChangeVo) {
		PageInfo<ProjectContractApplyChange>  projectContractApplyChange=projectContractApplyChangeService.finishedList(projectContractApplyChangeVo);
		return RestApiResponse.ok(projectContractApplyChange);
	}
	
	/**
	 *@Description: 任务书申报表变更已完成
	 *@param projectContractApplyChangeVo 任务书申报表变更 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "任务书申报表变更已完成", notes = "任务书申报表变更已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody ProjectContractApplyChangeVo projectContractApplyChangeVo) {
		PageInfo<ProjectContractApplyChange>  projectContractApplyChange=projectContractApplyChangeService.endList(projectContractApplyChangeVo);
		return RestApiResponse.ok(projectContractApplyChange);
	}
}
