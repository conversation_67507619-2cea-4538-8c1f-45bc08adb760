package com.fd.stdp.controller.appraisal;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.appraisal.AppraisalBasicItems;
import com.fd.stdp.beans.appraisal.vo.AppraisalBasicItemsVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.appraisal.AppraisalBasicItemsService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 评价基础项Controller
 *
 * <AUTHOR>
 * @date 2021-11-18
 */
@RestController
@RequestMapping("/appraisal/basicItems")
@Api(value = "评价基础项", description = "评价基础项")
public class AppraisalBasicItemsController extends BaseController {
    @Autowired
    private AppraisalBasicItemsService appraisalBasicItemsService;

    private final String PER_PREFIX = "btn:appraisal:basicItems:";

    /**
     * @param appraisalBasicItems 评价基础项数据 json
     * @return RestApiResponse<?>
     * @Description: 新增评价基础项
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增评价基础项", notes = "新增评价基础项")
    @SystemLogAnnotation(type = "评价基础项", value = "新增评价基础项")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveAppraisalBasicItems(@RequestBody AppraisalBasicItems appraisalBasicItems) {
        String id = appraisalBasicItemsService.saveOrUpdateAppraisalBasicItems(appraisalBasicItems);
        return RestApiResponse.ok(id);
    }

    /**
     * @param appraisalBasicItems 评价基础项数据 json
     * @return RestApiResponse<?>
     * @Description: 修改评价基础项
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改评价基础项", notes = "修改评价基础项")
    @SystemLogAnnotation(type = "评价基础项", value = "修改评价基础项")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateAppraisalBasicItems(@RequestBody AppraisalBasicItems appraisalBasicItems) {
        String id = appraisalBasicItemsService.saveOrUpdateAppraisalBasicItems(appraisalBasicItems);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除评价基础项(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除评价基础项", notes = "批量删除评价基础项")
    @SystemLogAnnotation(type = "评价基础项", value = "批量删除评价基础项")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteAppraisalBasicItems(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        appraisalBasicItemsService.deleteAppraisalBasicItems(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询评价基础项详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询评价基础项详情", notes = "查询评价基础项详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        AppraisalBasicItems appraisalBasicItems = appraisalBasicItemsService.findById(id);
        return RestApiResponse.ok(appraisalBasicItems);
    }

    /**
     * @param appraisalBasicItemsVo 评价基础项 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询评价基础项
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询评价基础项", notes = "分页查询评价基础项")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody AppraisalBasicItemsVo appraisalBasicItemsVo) {
        PageInfo<AppraisalBasicItems> appraisalBasicItems = appraisalBasicItemsService.findPageByQuery(appraisalBasicItemsVo);
        return RestApiResponse.ok(appraisalBasicItems);
    }

}
