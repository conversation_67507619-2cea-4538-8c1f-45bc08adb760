package com.fd.stdp.controller.user;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.user.UserLeave;
import com.fd.stdp.beans.user.vo.UserLeaveVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.user.UserLeaveService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 员工请假Controller
 * 
 * <AUTHOR>
 * @date 2021-10-25
 */
@RestController
@RequestMapping("/user/leave")
@Api(value="员工请假", description="员工请假")
public class UserLeaveController extends BaseController{
    @Autowired
    private UserLeaveService userLeaveService;

	private final String PER_PREFIX = "btn:user:leave:";
	
	/**
	 *@Description: 新增员工请假
	 *@param userLeave 员工请假数据 json
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增员工请假", notes = "新增员工请假")
	@SystemLogAnnotation(type = "员工请假",value = "新增员工请假")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveUserLeave(@RequestBody UserLeave userLeave) {
		userLeave.setUserId(getCurrentUserId());
		userLeave.setUserName(getCurrentRealName());
		String id = userLeaveService.saveOrUpdateUserLeave(userLeave);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改员工请假
	 *@param userLeave 员工请假数据 json
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改员工请假", notes = "修改员工请假")
	@SystemLogAnnotation(type = "员工请假",value = "修改员工请假")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateUserLeave(@RequestBody UserLeave userLeave) {
		String id = userLeaveService.saveOrUpdateUserLeave(userLeave);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 批量删除员工请假(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "批量删除员工请假", notes = "批量删除员工请假")
	@SystemLogAnnotation(type = "员工请假",value = "批量删除员工请假")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteUserLeave(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		userLeaveService.deleteUserLeave(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询员工请假详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询员工请假详情", notes = "查询员工请假详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		UserLeave  userLeave=userLeaveService.findById(id);
		return RestApiResponse.ok(userLeave);
	}
	
	/**
	 *@Description: 分页查询员工请假
	 *@param userLeaveVo 员工请假 查询条件
	 *@return RestApiResponse<?>
	 *@Author: linqiang
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询员工请假", notes = "分页查询员工请假")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody UserLeaveVo userLeaveVo) {
		PageInfo<UserLeave>  userLeave=userLeaveService.findPageByQuery(userLeaveVo);
		return RestApiResponse.ok(userLeave);
	}
	
}
