package com.fd.stdp.controller.project;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.fd.stdp.beans.audit.vo.InnerAuditStatisticsVo;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertScoreVo;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertsVo;
import com.fd.stdp.beans.project.vo.ProjectInfoStatistics;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.constant.Constant;
import com.fd.stdp.constant.ProjectDeclarationFlowConstants;
import com.fd.stdp.enums.QueryTypeEnum;
import com.fd.stdp.service.project.ProjectApplyExpertsService;
import com.fd.stdp.util.AppUserUtil;
import com.lzhpo.sensitive.annocation.IgnoreSensitive;
import liquibase.pro.packaged.R;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.bcel.Const;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.project.ProjectApplyInfo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectApplyInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 项目基本信息Controller
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
@RestController
@RequestMapping("/project/applyInfo")
@Api(value = "项目基本信息", description = "项目基本信息")
public class ProjectApplyInfoController extends BaseController {
    @Autowired
    private ProjectApplyInfoService projectApplyInfoService;

    @Autowired
    private ProjectApplyExpertsService projectApplyExpertsService;

    private final String PER_PREFIX = "btn:project:applyInfo:";

    /**
     * @param vo 项目基本信息数据 json
     * @return RestApiResponse<?>
     * @Description: 新增项目基本信息
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增项目基本信息", notes = "新增项目基本信息")
    @SystemLogAnnotation(type = "项目基本信息", value = "新增项目基本信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveProjectApplyInfo(@RequestBody ProjectApplyInfoVo vo) {
        vo.setOrgName(getCurrentOrgName());
        vo.setOrgCode(getCurrentScienceOrgId());
        vo.setUserId(getCurrentUserId());
        String id = projectApplyInfoService.saveOrUpdateProjectApplyInfo(vo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param vo 项目基本信息数据 json
     * @return RestApiResponse<?>
     * @Description: 新增项目基本信息
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/historySave")
    @ApiOperation(value = "新增历史项目基本信息", notes = "新增历史项目基本信息")
    @SystemLogAnnotation(type = "新增历史项目基本信息", value = "新增历史项目基本信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveHistoryProjectApplyInfo(@RequestBody ProjectApplyInfoVo vo) {
        vo.setOrgName(getCurrentOrgName());
        vo.setOrgCode(getCurrentScienceOrgId());
        vo.setUserId(getCurrentUserId());
        String id = projectApplyInfoService.saveHistoryProjectApplyInfo(vo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param vo 外部项目基本信息数据 json
     * @return RestApiResponse<?>
     * @Description: 新增外部项目基本信息
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/saveOut")
    @ApiOperation(value = "新增外部项目基本信息", notes = "新增外部项目基本信息")
    @SystemLogAnnotation(type = "项目基本信息", value = "新增外部项目基本信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveOutProjectApplyInfo(@RequestBody ProjectApplyInfoVo vo) {
        vo.setOrgName(getCurrentOrgName());
        vo.setOrgCode(getCurrentScienceOrgId());
        String id = projectApplyInfoService.saveOutProjectApplyInfo(vo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectApplyInfoVo 项目基本信息数据 json
     * @return RestApiResponse<?>
     * @Description: 修改项目基本信息
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改项目基本信息", notes = "修改项目基本信息")
    @SystemLogAnnotation(type = "项目基本信息", value = "修改项目基本信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateProjectApplyInfo(@RequestBody ProjectApplyInfoVo projectApplyInfoVo) {
        String id = projectApplyInfoService.saveOrUpdateProjectApplyInfo(projectApplyInfoVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param ids
     * @return RestApiResponse<?>
     * @Description: 批量删除项目基本信息(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除项目基本信息", notes = "批量删除项目基本信息")
    @SystemLogAnnotation(type = "项目基本信息", value = "批量删除项目基本信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteProjectApplyInfo(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        projectApplyInfoService.deleteProjectApplyInfo(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询项目基本信息详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询项目基本信息详情", notes = "查询项目基本信息详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ProjectApplyInfo projectApplyInfo = projectApplyInfoService.findById(id);
        return RestApiResponse.ok(projectApplyInfo);
    }

    /**
     * @param projectApplyInfoVo 项目基本信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询项目基本信息
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询项目基本信息", notes = "分页查询项目基本信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ProjectApplyInfoVo projectApplyInfoVo) {
        PageInfo<ProjectApplyInfo> projectApplyInfo = projectApplyInfoService.findPageByQuery(projectApplyInfoVo);
        return RestApiResponse.ok(projectApplyInfo);
    }

    /**
     * @param vo 项目基本信息数据 json
     * @return RestApiResponse<?>
     * @Description: 项目信息提交
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/submit")
    @ApiOperation(value = "项目信息提交", notes = "项目信息提交")
    @SystemLogAnnotation(type = "项目信息提交", value = "项目信息提交")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "submit')")
    public RestApiResponse<?> submitProjectApplyInfo(@RequestBody ProjectApplyInfoVo vo) {
        vo.setOrgName(getCurrentOrgName());
        vo.setOrgCode(getCurrentScienceOrgId());
        vo.setUserId(getCurrentUserId());
        String id = projectApplyInfoService.submitProjectApplyInfo(vo);
        return RestApiResponse.ok(id);
    }


    /**
     * @param projectApplyInfoVo 项目基本信息数据 json
     * @return RestApiResponse<?>
     * @Description: 项目信息审核
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/audit")
    @ApiOperation(value = "项目信息审核", notes = "项目信息审核")
    @SystemLogAnnotation(type = "项目信息审核", value = "项目信息审核")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "audit')")
    public RestApiResponse<?> auditProjectApplyInfo(@RequestBody ProjectApplyInfoVo projectApplyInfoVo) {
        String id = projectApplyInfoService.auditProjectApplyInfo(projectApplyInfoVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectApplyInfoVo 项目基本信息数据 json
     * @return RestApiResponse<?>
     * @Description: 项目信息审核
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/multiProvinceAudit")
    @ApiOperation(value = "项目信息审核", notes = "项目信息审核")
    @SystemLogAnnotation(type = "项目信息审核", value = "项目信息审核")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "audit')")
    public RestApiResponse<?> multiProvinceAudit(@RequestBody ProjectApplyInfoVo projectApplyInfoVo) {
        String id = projectApplyInfoService.multiProvinceAudit(projectApplyInfoVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectApplyInfoVo 项目基本信息数据 json
     * @return RestApiResponse<?>
     * @Description: 项目信息退回
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/sendBack")
    @ApiOperation(value = "项目信息退回", notes = "项目信息退回")
    @SystemLogAnnotation(type = "项目信息退回", value = "项目信息退回")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "audit')")
    public RestApiResponse<?> sendBackProjectApplyInfo(@RequestBody ProjectApplyInfoVo projectApplyInfoVo) {
        String id = projectApplyInfoService.sendBackProjectApplyInfo(projectApplyInfoVo);
        return RestApiResponse.ok(id);
    }


//    /**
//     * @param projectApplyInfoVo 项目基本信息数据 json
//     * @return RestApiResponse<?>
//     * @Description: 选择专家
//     * @Author: wangsh
//     */
//    @RepeatSubAnnotation
//    @PostMapping("/toExperts")
//    @ApiOperation(value = "选择专家", notes = "选择专家")
//    @SystemLogAnnotation(type = "选择专家", value = "选择专家")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "audit')")
//    public RestApiResponse<?> toExpertsProjectApplyInfo(@RequestBody ProjectApplyInfoVo projectApplyInfoVo) {
//        String id = projectApplyInfoService.toExpertsProjectApplyInfo(projectApplyInfoVo);
//        return RestApiResponse.ok(id);
//    }

    /**
     * @param projectApplyExpertsVo 项目专家信息数据 json
     * @return RestApiResponse<?>
     * @Description: 专家提交评审
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/submitExperts")
    @ApiOperation(value = "专家提交评审", notes = "专家提交评审")
    @SystemLogAnnotation(type = "专家提交评审", value = "专家提交评审")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "submit')")
    public RestApiResponse<?> submitProjectApplyExperts(@RequestBody ProjectApplyExpertsVo projectApplyExpertsVo) {
        String id = projectApplyExpertsService.submitProjectApplyExperts(projectApplyExpertsVo);
        return RestApiResponse.ok(id);
    }

    @RepeatSubAnnotation
    @PostMapping("/submitExpertsMulti")
    @ApiOperation(value = "专家批量提交评审", notes = "专家批量提交评审")
    @SystemLogAnnotation(type = "专家提交评审", value = "专家批量提交评审")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "submit')")
    public RestApiResponse<?> submitProjectApplyExpertsMulti(@RequestBody List<String> ids) {
        projectApplyExpertsService.submitProjectApplyExpertsMulti(ids, getCurrentUserId());
        return RestApiResponse.ok();
    }

    /**
     * @param projectApplyInfoVo 项目信息数据 json
     * @return RestApiResponse<?>
     * @Description: 处室评议
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/review")
    @ApiOperation(value = "处室评议", notes = "处室评议")
    @SystemLogAnnotation(type = "处室评议", value = "处室评议")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "discuss')")
    public RestApiResponse<?> reviewProjectApplyInfo(@RequestBody ProjectApplyInfoVo projectApplyInfoVo) {
        String id = projectApplyInfoService.reviewProjectApplyInfo(projectApplyInfoVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectApplyInfoVo 项目信息数据 json
     * @return RestApiResponse<?>
     * @Description: 项目下达
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/release")
    @ApiOperation(value = "项目下达", notes = "项目下达")
    @SystemLogAnnotation(type = "项目下达", value = "项目下达")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "release')")
    public RestApiResponse<?> releaseProjectApplyInfo(@RequestBody ProjectApplyInfoVo projectApplyInfoVo) {
        String id = projectApplyInfoService.releaseProjectApplyInfo(projectApplyInfoVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param vo 项目基本信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 项目申报已办
     * @Author: wangsh
     */
    @PostMapping("/todo")
    @ApiOperation(value = "项目申报已办", notes = "项目申报已办")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> todoPageByQuery(@RequestBody ProjectApplyInfoVo vo) {
        PageInfo list;
        if (QueryTypeEnum.TODO.getType().equals(vo.getQueryType())) {
            list = projectApplyInfoService.todoList(vo);
        } else if (QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())) {
            list = projectApplyInfoService.finishedList(vo);
        } else if (QueryTypeEnum.END.getType().equals(vo.getQueryType())) {
            list = projectApplyInfoService.endList(vo);
        } else {
            list = projectApplyInfoService.todoList(vo);
        }
        return RestApiResponse.ok(list);
    }

    /**
     * @param vo 项目基本信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 项目申报代办查询New
     * @Author: wangsh
     */
    @PostMapping("/todoNew")
    @ApiOperation(value = "项目申报代办查询New", notes = "项目申报代办查询New")
    public RestApiResponse<?> todoNew(@RequestBody ProjectApplyInfoVo vo) {

        String currentAreaCode = getCurrentAreaCode();
        if (StringUtils.isEmpty(currentAreaCode)) {
            logger.info("用户行政区划为空");
            return RestApiResponse.ok(new PageInfo<ProjectApplyInfoVo>(new ArrayList<>()));
        }
        vo.setUserAreaCode(currentAreaCode);

        //获取用户角色，判断是否包含管理员
        boolean isAdmin = getUserRoleList().stream()
                .anyMatch(role -> role.getRoleCode().equals(CommonConstant.SUPER_ADMIN_ROLE));
        List<String> userRoleList = getUserRoleList().stream().map(role -> role.getRoleCode()).collect(Collectors.toList());
        if (!isAdmin) {
            //机构审核人角色
            if (userRoleList.size() == 1 && userRoleList.get(0).equals(ProjectDeclarationFlowConstants.ORG_TASK_ROLE)) {
                //查询当前登录机构用户的uscc
                String currentUserUscc = getCurrentUscc();
                if (StringUtils.isEmpty(currentUserUscc)) {
                    return RestApiResponse.ok(new PageInfo<ProjectApplyInfoVo>(new ArrayList<>()));
                }
                vo.setUserUscc(currentUserUscc);
            }
        }
        List<ProjectApplyInfoVo> voList = projectApplyInfoService.todoNew(vo);
        return RestApiResponse.ok(new PageInfo<ProjectApplyInfoVo>(voList));
    }

    /**
     * @param projectApplyInfoVo 项目基本信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 项目申报待办
     * @Author: wangsh
     */
    @PostMapping("/finished")
    @ApiOperation(value = "项目申报待办", notes = "项目申报待办")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> finishedPageByQuery(@RequestBody ProjectApplyInfoVo projectApplyInfoVo) {
        PageInfo<ProjectApplyInfo> projectApplyInfo = projectApplyInfoService.finishedList(projectApplyInfoVo);
        return RestApiResponse.ok(projectApplyInfo);
    }

    /**
     * @param projectApplyInfoVo 项目基本信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 项目申报待办
     * @Author: wangsh
     */
    @PostMapping("/end")
    @ApiOperation(value = "项目申报已完成", notes = "项目申报已完成")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> endList(@RequestBody ProjectApplyInfoVo projectApplyInfoVo) {
        PageInfo<ProjectApplyInfo> projectApplyInfo = projectApplyInfoService.endList(projectApplyInfoVo);
        return RestApiResponse.ok(projectApplyInfo);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 项目申报表单下载
     * @Author: wangsh
     */
    @GetMapping("/download")
    @ApiOperation(value = "下载", notes = "下载")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "upload')")
    public void upload(@RequestParam("id") String id, HttpServletResponse response) {
        projectApplyInfoService.download(id, response);
    }

    /**
     * @param projectApplyInfoVo 查询储备项目信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询项目基本信息
     * @Author: yujianfei
     */
    @PostMapping("/findReservePageByQuery")
    @ApiOperation(value = "查询储备项目信息", notes = "查询储备项目信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findReservePageByQuery(@RequestBody ProjectApplyInfoVo projectApplyInfoVo) {
        PageInfo<ProjectApplyInfo> projectApplyInfo = projectApplyInfoService.findReservePageByQuery(projectApplyInfoVo);
        return RestApiResponse.ok(projectApplyInfo);
    }


    /**
     * @param vo 项目负责人查询储备项目信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询项目基本信息
     * @Author: zhangYu
     */
    @PostMapping("/findSelfReservePro")
    @ApiOperation(value = "项目负责人查询储备项目信息", notes = "项目负责人查询储备项目信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findSelfReservePro')")
    public RestApiResponse<?> findSelfReservePro(@RequestBody ProjectApplyInfoVo vo) {
        vo.setUserId(getCurrentUserId());
        List<ProjectApplyInfo> voList = projectApplyInfoService.findSelfReservePro(vo);
        return RestApiResponse.ok(new PageInfo<ProjectApplyInfo>(voList));
    }

    /**
     * @param vo 历史申报项目查询 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询项目基本信息
     * @Author: zhangYu
     */
    @PostMapping("/findHistoryDeclarationPro")
    @ApiOperation(value = "历史申报项目查询", notes = "历史申报项目查询")
    public RestApiResponse<?> findHistoryDeclarationPro(@RequestBody ProjectApplyInfoVo vo) {
//        boolean isProvinceChecker = getUserRoleList().stream()
//                .anyMatch(role -> role.getRoleCode().equals(ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_ROLE));
//
//        if (!isAdmin() && !isProvinceChecker) {
//            //机构管理员
//            if (Constant.ORG_ADMIN.equals(getLoginUser().getType())) {
//                //查询当前登录机构用户的uscc
//                String currentUserUscc = getCurrentUscc();
//                if (StringUtils.isEmpty(currentUserUscc)) {
//                    throw new ServiceException("缺少单位统一社会信用代码");
//                }
//                vo.setUserUscc(currentUserUscc);
//            }else if (Constant.ROLE_PERSONAL.equals(getLoginUser().getType())){
//                vo.setUserId(getCurrentUserId());
//            }else{
//                throw new ServiceException("无权限查看数据");
//            }
//        }
        List<ProjectApplyInfoVo> voList = projectApplyInfoService.findHistoryDeclarationPro(vo);
        return RestApiResponse.ok(new PageInfo<ProjectApplyInfoVo>(voList));
    }

    /**
     * 统计已提交数量
     *
     * @return
     */
    @PostMapping("/countCommitNumber")
    @ApiOperation(value = "统计已提交数量", notes = "统计已提交数量")
    public RestApiResponse<?> countCommitNumber(@RequestBody ProjectApplyInfoVo vo) {
//        boolean isProvinceChecker = getUserRoleList().stream()
//                .anyMatch(role -> role.getRoleCode().equals(ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_ROLE));
//
//        if (!isAdmin() && !isProvinceChecker) {
//            //机构管理员
//            if (Constant.ORG_ADMIN.equals(getLoginUser().getType())) {
//                //查询当前登录机构用户的uscc
//                String currentUserUscc = getCurrentUscc();
//                if (StringUtils.isEmpty(currentUserUscc)) {
//                    throw new ServiceException("缺少单位统一社会信用代码");
//                }
//                vo.setUserUscc(currentUserUscc);
//            }else if (Constant.ROLE_PERSONAL.equals(getLoginUser().getType())){
//                vo.setUserId(getCurrentUserId());
//            }else{
//                throw new ServiceException("无权限查看数据");
//            }
//        }
        int count = projectApplyInfoService.countCommitNumber(vo);
        return RestApiResponse.ok(count);
    }

    /**
     * @param
     * @return RestApiResponse<?>
     * @Description: 获得推荐单位
     * @Author: wangsh
     */
    @GetMapping("/getRecommandUnit")
    @ApiOperation(value = "获得推荐单位", notes = "获得推荐单位")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> getRecommandUnit() {
        return RestApiResponse.ok(projectApplyInfoService.getRecommandUnit());
    }


    /**
     * @param statistics 查询储备项目信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询项目基本信息
     * @Author: yujianfei
     */
    @PostMapping("/statisticsProjectInfo")
    @ApiOperation(value = "查询储备项目信息", notes = "查询储备项目信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> statisticsProjectInfo(@RequestBody ProjectInfoStatistics statistics) {
        PageInfo pageInfo = projectApplyInfoService.statisticsProjectInfo(statistics);
        return RestApiResponse.ok(pageInfo);
    }

    /**
     * @param projectApplyInfoVo 项目基本信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 已报项目导出
     * @Author: yujianfei
     */
    @PostMapping("/exportPageByQuery")
    @ApiOperation(value = "已报项目导出", notes = "已报项目导出")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public void exportPageByQuery(@RequestBody ProjectApplyInfoVo projectApplyInfoVo, HttpServletResponse response) {
        projectApplyInfoService.exportPageByQuery(projectApplyInfoVo, response);
    }

    /**
     * @param projectApplyInfoVo 项目基本信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 处室评议导出
     * @Author: yujianfei
     */
    @PostMapping("/exportReviewPageByQuery")
    @ApiOperation(value = "处室评议导出", notes = "处室评议导出")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public void exportReviewPageByQuery(@RequestBody ProjectApplyInfoVo projectApplyInfoVo, HttpServletResponse response) {
        projectApplyInfoVo.setPageNum(1);
        projectApplyInfoVo.setPageSize(Integer.MAX_VALUE);
        projectApplyInfoService.exportReviewPageByQuery(projectApplyInfoVo, response);
    }


    /**
     * 审核操作 （流程）
     *
     * @param applyVo 审核操作 查询条件
     * @return RestApiResponse<?>
     */
    @RepeatSubAnnotation
    @PostMapping("/applyAudit")
    @ApiOperation(value = "审核操作", notes = "审核操作")
    @SystemLogAnnotation(type = "审核操作", value = "审核操作")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "applyAudit')")
    public RestApiResponse<?> applyAudit(@RequestBody ProjectApplyInfoVo applyVo) {
        projectApplyInfoService.applyAudit(applyVo, getCurrentUserName(), getCurrentRealName());
        return RestApiResponse.ok();
    }

    /**
     * @param vo 项目基本信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 项目申报代办列表查询
     * @Author: zhangYu
     */
    @PostMapping("/findTodoList")
    @ApiOperation(value = "项目申报代办列表查询", notes = "项目申报代办列表查询")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findTodoList')")
    public RestApiResponse<?> findTodoList(@RequestBody ProjectApplyInfoVo vo) {
        String currentAreaCode = getCurrentAreaCode();
        if (StringUtils.isEmpty(currentAreaCode)) {
            throw new ServiceException("用户行政区划错误");
        }
        vo.setUserAreaCode(currentAreaCode);

        //单位审核
        //获取用户角色，判断是否包含管理员
        boolean isAdmin = getUserRoleList().stream()
                .anyMatch(role -> role.getRoleCode().equals(CommonConstant.SUPER_ADMIN_ROLE));
        if (!isAdmin) {
            if (ProjectDeclarationFlowConstants.ORG_TASK_CODE.equals(vo.getActNodeKey())) {
                //查询当前登录机构用户的uscc
                String currentUserUscc = getCurrentUscc();
                if (StringUtils.isEmpty(currentUserUscc)) {
                    throw new ServiceException("缺少单位统一社会信用代码");
                }
                vo.setUserUscc(currentUserUscc);
            }
        }
        List<ProjectApplyInfoVo> voList = projectApplyInfoService.findTodoList(vo);
        return RestApiResponse.ok(new PageInfo<ProjectApplyInfoVo>(voList));
    }


    /**
     * 待专家评审数据查询
     *
     * @param applyVo
     * @return
     */
    @PostMapping("/awaitingExpertReview")
    @ApiOperation(value = "待专家评审数据查询", notes = "待专家评审数据查询")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "awaitingExpertReview')")
    public RestApiResponse<?> awaitingExpertReview(@RequestBody ProjectApplyInfoVo applyVo) {
        if (!isAdmin()) {
            if (!CommonConstant.EXPERT_ROLE.equals(getLoginUser().getType())) {
                return RestApiResponse.ok(new PageInfo<ProjectApplyInfoVo>(new ArrayList<>()));
            }
            applyVo.setExpertName(getLoginUser().getNickname());
            applyVo.setExpertPhone(getLoginUser().getPhone());
        }
        List<ProjectApplyInfoVo> voList = projectApplyInfoService.awaitingExpertReview(applyVo);
        return RestApiResponse.ok(new PageInfo<ProjectApplyInfoVo>(voList));
    }


    /**
     * 专家评审过的项目列表
     *
     * @param vo
     * @return
     */
    @PostMapping("/expertReviewedList")
    @ApiOperation(value = "专家评审过的项目列表", notes = "专家评审过的项目列表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findReviewPro')")
    public RestApiResponse<?> expertReviewedList(@RequestBody ProjectApplyInfoVo vo) {
        vo.setUserId(getCurrentUserId());
        vo.setExpertName(getLoginUser().getNickname());
        List<ProjectApplyInfoVo> projectApplyInfoVoList = projectApplyInfoService.expertReviewedList(vo);
        return RestApiResponse.ok(new PageInfo<ProjectApplyInfoVo>(projectApplyInfoVoList));
    }


    /**
     *@Description: 导出储备项目信息
     *@param vo 项目基本信息查询条件
     *@return void
     *@Author:
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出储备项目信息", notes = "导出储备项目信息")
    @PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
    public void export(@RequestBody ProjectApplyInfoVo vo, HttpServletResponse response) {
        // 设置当前用户ID，确保只导出当前用户的储备项目
        vo.setUserId(getCurrentUserId());
        // 调用服务层的导出方法
        projectApplyInfoService.exportSelfReservePro(vo, response);
    }

}
