package com.fd.stdp.controller.project;

import com.fd.stdp.beans.project.ProjectApplyInfo;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertMumberVo;
import com.fd.stdp.enums.QueryTypeEnum;
import com.fd.stdp.service.project.ProjectApplyInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractAccept;
import com.fd.stdp.beans.project.vo.ProjectContractAcceptVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectContractAcceptService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 任务书验收表单
 *@Author: wangsh
 *@Date: 2022-03-03 10:16:28
 */
@RestController
@RequestMapping("/project/projectContractAccept")
@Api(value="任务书验收表单", description="任务书验收表单")
public class ProjectContractAcceptController  extends BaseController {

	@Autowired
	private ProjectContractAcceptService projectContractAcceptService;
	@Autowired
	private ProjectApplyInfoService projectApplyInfoService;
	
	private final String PER_PREFIX = "project:but:accept:";
	
	/**
	 *@Description: 新增任务书验收表单
	 *@param projectContractAcceptVo 任务书验收表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增任务书验收表单", notes = "新增任务书验收表单")
	@SystemLogAnnotation(type = "任务书验收表单",value = "新增任务书验收表单")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveProjectContractAccept(@RequestBody ProjectContractAcceptVo projectContractAcceptVo) {
		projectContractAcceptVo.setOrgName(getCurrentOrgName());
		projectContractAcceptVo.setOrgCode(getCurrentScienceOrgId());
		String id = projectContractAcceptService.saveOrUpdateProjectContractAccept(projectContractAcceptVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改任务书验收表单
	 *@param projectContractAccept 任务书验收表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改任务书验收表单", notes = "修改任务书验收表单")
	@SystemLogAnnotation(type = "任务书验收表单",value = "修改任务书验收表单")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateProjectContractAccept(@RequestBody ProjectContractAcceptVo projectContractAccept) {
		String id = projectContractAcceptService.saveOrUpdateProjectContractAccept(projectContractAccept);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除任务书验收表单(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除任务书验收表单", notes = "删除任务书验收表单")
	@SystemLogAnnotation(type = "任务书验收表单",value = "删除任务书验收表单")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteProjectContractAccept(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		projectContractAcceptService.deleteProjectContractAccept(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除任务书验收表单(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除任务书验收表单", notes = "删除任务书验收表单")
	@SystemLogAnnotation(type = "任务书验收表单",value = "删除任务书验收表单")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiProjectContractAccept(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		projectContractAcceptService.deleteMultiProjectContractAccept(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询任务书验收表单详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询任务书验收表单详情", notes = "查询任务书验收表单详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		ProjectContractAccept  projectContractAccept=projectContractAcceptService.findById(id);
		return RestApiResponse.ok(projectContractAccept);
	}

	/**
	 *@Description: 查询任务书验收表单详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findApplyById")
	@ApiOperation(value = "查询任务书验收表单详情", notes = "查询任务书验收表单详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findApplyById(@RequestParam("id") String id) {
		ProjectContractAccept projectContractAccept=projectContractAcceptService.findById(id);
		return RestApiResponse.ok(projectApplyInfoService.findById(projectContractAccept.getApplyId()));
	}
	
	/**
	 *@Description: 分页查询任务书验收表单
	 *@param projectContractAcceptVo 任务书验收表单 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询任务书验收表单", notes = "分页查询任务书验收表单")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody ProjectContractAcceptVo projectContractAcceptVo) {
		PageInfo<ProjectContractAccept>  projectContractAccept=projectContractAcceptService.findPageByQuery(projectContractAcceptVo);
		return RestApiResponse.ok(projectContractAccept);
	}

	/**
	 *@Description: 任务书验收表单提交
	 *@param projectContractAcceptVo 任务书验收表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "任务书验收表单提交", notes = "任务书验收表单提交")
	@SystemLogAnnotation(type = "任务书验收表单",value = "任务书验收表单提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitProjectContractAccept(@RequestBody ProjectContractAcceptVo projectContractAcceptVo) {
		projectContractAcceptVo.setOrgName(getCurrentOrgName());
		projectContractAcceptVo.setOrgCode(getCurrentScienceOrgId());
		String id = projectContractAcceptService.submitProjectContractAccept(projectContractAcceptVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书验收表单提交
	 *@param vo 任务书验收表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/expertSubmit")
	@ApiOperation(value = "任务书验收表单提交", notes = "任务书验收表单提交")
	@SystemLogAnnotation(type = "任务书验收表单",value = "任务书验收表单提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> expertSubmitProjectContractAccept(@RequestBody ProjectApplyExpertMumberVo vo) {
		String id = projectContractAcceptService.expertSubmitProjectContractAccept(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 任务书验收表单审核
	 *@param projectContractAcceptVo 任务书验收表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "任务书验收表单审核", notes = "任务书验收表单审核")
	@SystemLogAnnotation(type = "任务书验收表单",value = "任务书验收表单审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditProjectContractAccept(@RequestBody ProjectContractAcceptVo projectContractAcceptVo) {
		String id = projectContractAcceptService.auditProjectContractAccept(projectContractAcceptVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书验收表单退回
	 *@param projectContractAcceptVo 任务书验收表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "任务书验收表单退回", notes = "任务书验收表单退回")
	@SystemLogAnnotation(type = "任务书验收表单",value = "任务书验收表单退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackProjectContractAccept(@RequestBody ProjectContractAcceptVo projectContractAcceptVo) {
		String id = projectContractAcceptService.sendBackProjectContractAccept(projectContractAcceptVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书验收表单任务书下达
	 *@param projectContractAcceptVo 任务书验收表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "任务书验收表单任务书下达", notes = "任务书验收表单任务书下达")
	@SystemLogAnnotation(type = "任务书验收表单",value = "任务书验收表单任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseProjectContractAccept(@RequestBody ProjectContractAcceptVo projectContractAcceptVo) {
		String id = projectContractAcceptService.releaseProjectContractAccept(projectContractAcceptVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书验收表单待办
	 *@param vo 任务书验收表单 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "任务书验收表单待办", notes = "任务书验收表单待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody ProjectContractAcceptVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = projectContractAcceptService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = projectContractAcceptService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = projectContractAcceptService.endList(vo);
		} else {
			list = projectContractAcceptService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 任务书验收表单已办
	 *@param projectContractAcceptVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "任务书验收表单已办", notes = "任务书验收表单已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody ProjectContractAcceptVo projectContractAcceptVo) {
		PageInfo<ProjectContractAccept>  projectContractAccept=projectContractAcceptService.finishedList(projectContractAcceptVo);
		return RestApiResponse.ok(projectContractAccept);
	}
	
	/**
	 *@Description: 任务书验收表单已完成
	 *@param projectContractAcceptVo 任务书验收表单 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "任务书验收表单已完成", notes = "任务书验收表单已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody ProjectContractAcceptVo projectContractAcceptVo) {
		PageInfo<ProjectContractAccept>  projectContractAccept=projectContractAcceptService.endList(projectContractAcceptVo);
		return RestApiResponse.ok(projectContractAccept);
	}
}
