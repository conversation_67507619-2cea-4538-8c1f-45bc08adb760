package com.fd.stdp.controller.sys;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.sys.SysFunctionRole;
import com.fd.stdp.beans.sys.vo.SysFunctionRoleVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.sys.SysFunctionRoleService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 
 *@Author: qyj
 *@Date: 2020-07-12 15:59:28
 */
@RestController
@RequestMapping("/sys/sysFunctionRole")
@Api(value="", description="")
public class SysFunctionRoleController  extends BaseController {

	@Autowired
	private SysFunctionRoleService sysFunctionRoleService;
	
	private final String PER_PREFIX = "";
	

	/**
	 *@Description: 查询详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: qyj
	 */
	@GetMapping("/findAll")
	@ApiOperation(value = "查询详情", notes = "查询详情")
	public RestApiResponse<?> findById() {
		List<SysFunctionRole> all = sysFunctionRoleService.findAll();
		return RestApiResponse.ok(all);
	}
}
