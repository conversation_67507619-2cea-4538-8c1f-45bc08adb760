package com.fd.stdp.controller.work;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.work.WorkFileInfo;
import com.fd.stdp.beans.work.vo.WorkFileInfoVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.work.WorkFileInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 工作附件信息Controller
 *
 * <AUTHOR>
 * @date 2021-11-12
 */
@RestController
@RequestMapping("/work/fileInfo")
@Api(value = "工作附件信息", description = "工作附件信息")
public class WorkFileInfoController extends BaseController {
    @Autowired
    private WorkFileInfoService workFileInfoService;

    private final String PER_PREFIX = "btn:work:fileInfo:";

    /**
     * @param workFileInfo 工作附件信息数据 json
     * @return RestApiResponse<?>
     * @Description: 新增工作附件信息
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增工作附件信息", notes = "新增工作附件信息")
    @SystemLogAnnotation(type = "工作附件信息", value = "新增工作附件信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveWorkFileInfo(@RequestBody WorkFileInfo workFileInfo) {
        String id = workFileInfoService.saveOrUpdateWorkFileInfo(workFileInfo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param workFileInfo 工作附件信息数据 json
     * @return RestApiResponse<?>
     * @Description: 修改工作附件信息
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改工作附件信息", notes = "修改工作附件信息")
    @SystemLogAnnotation(type = "工作附件信息", value = "修改工作附件信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateWorkFileInfo(@RequestBody WorkFileInfo workFileInfo) {
        String id = workFileInfoService.saveOrUpdateWorkFileInfo(workFileInfo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除工作附件信息(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除工作附件信息", notes = "批量删除工作附件信息")
    @SystemLogAnnotation(type = "工作附件信息", value = "批量删除工作附件信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteWorkFileInfo(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        workFileInfoService.deleteWorkFileInfo(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询工作附件信息详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询工作附件信息详情", notes = "查询工作附件信息详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        WorkFileInfo workFileInfo = workFileInfoService.findById(id);
        return RestApiResponse.ok(workFileInfo);
    }

    /**
     * @param workFileInfoVo 工作附件信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询工作附件信息
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询工作附件信息", notes = "分页查询工作附件信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody WorkFileInfoVo workFileInfoVo) {
        PageInfo<WorkFileInfo> workFileInfo = workFileInfoService.findPageByQuery(workFileInfoVo);
        return RestApiResponse.ok(workFileInfo);
    }

}
