package com.fd.stdp.controller.talent;

import com.fd.stdp.beans.talent.TalentLeaderSubjectLeader;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectLeaderVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamContributeChange;
import com.fd.stdp.beans.talent.vo.TalentTeamContributeChangeVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.talent.TalentTeamContributeChangeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 创新团队建设任务书变更
 *@Author: wangsh
 *@Date: 2022-02-14 19:56:46
 */
@RestController
@RequestMapping("/talent/talentTeamContributeChange")
@Api(value="创新团队建设任务书变更", description="创新团队建设任务书变更")
public class TalentTeamContributeChangeController  extends BaseController {

	@Autowired
	private TalentTeamContributeChangeService talentTeamContributeChangeService;
	
	private final String PER_PREFIX = "talent:but:teamChange:";
	
	/**
	 *@Description: 新增创新团队建设任务书变更
	 *@param vo 创新团队建设任务书变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增创新团队建设任务书变更", notes = "新增创新团队建设任务书变更")
	@SystemLogAnnotation(type = "创新团队建设任务书变更",value = "新增创新团队建设任务书变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTalentTeamContributeChange(@RequestBody TalentTeamContributeChangeVo vo) {
		if(StringUtils.isBlank(vo.getOrgName())) {
			vo.setOrgName(getCurrentOrgName());
			vo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = talentTeamContributeChangeService.saveOrUpdateTalentTeamContributeChange(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改创新团队建设任务书变更
	 *@param talentTeamContributeChange 创新团队建设任务书变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改创新团队建设任务书变更", notes = "修改创新团队建设任务书变更")
	@SystemLogAnnotation(type = "创新团队建设任务书变更",value = "修改创新团队建设任务书变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTalentTeamContributeChange(@RequestBody TalentTeamContributeChangeVo talentTeamContributeChange) {
		String id = talentTeamContributeChangeService.saveOrUpdateTalentTeamContributeChange(talentTeamContributeChange);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除创新团队建设任务书变更(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除创新团队建设任务书变更", notes = "删除创新团队建设任务书变更")
	@SystemLogAnnotation(type = "创新团队建设任务书变更",value = "删除创新团队建设任务书变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTalentTeamContributeChange(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		talentTeamContributeChangeService.deleteTalentTeamContributeChange(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除创新团队建设任务书变更(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除创新团队建设任务书变更", notes = "删除创新团队建设任务书变更")
	@SystemLogAnnotation(type = "创新团队建设任务书变更",value = "删除创新团队建设任务书变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiTalentTeamContributeChange(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		talentTeamContributeChangeService.deleteMultiTalentTeamContributeChange(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询创新团队建设任务书变更详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询创新团队建设任务书变更详情", notes = "查询创新团队建设任务书变更详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		TalentTeamContributeChange  talentTeamContributeChange=talentTeamContributeChangeService.findById(id);
		return RestApiResponse.ok(talentTeamContributeChange);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			TalentTeamContributeChangeVo vo = new TalentTeamContributeChangeVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<TalentTeamContributeChange> pageInfo = (PageInfo<TalentTeamContributeChange>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
	
	/**
	 *@Description: 分页查询创新团队建设任务书变更
	 *@param talentTeamContributeChangeVo 创新团队建设任务书变更 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询创新团队建设任务书变更", notes = "分页查询创新团队建设任务书变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TalentTeamContributeChangeVo talentTeamContributeChangeVo) {
		PageInfo<TalentTeamContributeChange>  talentTeamContributeChange=talentTeamContributeChangeService.findPageByQuery(talentTeamContributeChangeVo);
		return RestApiResponse.ok(talentTeamContributeChange);
	}

	/**
	 *@Description: 创新团队建设任务书变更提交
	 *@param vo 创新团队建设任务书变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "创新团队建设任务书变更提交", notes = "创新团队建设任务书变更提交")
	@SystemLogAnnotation(type = "创新团队建设任务书变更",value = "创新团队建设任务书变更提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTalentTeamContributeChange(@RequestBody TalentTeamContributeChangeVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentTeamContributeChangeService.submitTalentTeamContributeChange(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 创新团队建设任务书变更审核
	 *@param talentTeamContributeChangeVo 创新团队建设任务书变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "创新团队建设任务书变更审核", notes = "创新团队建设任务书变更审核")
	@SystemLogAnnotation(type = "创新团队建设任务书变更",value = "创新团队建设任务书变更审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTalentTeamContributeChange(@RequestBody TalentTeamContributeChangeVo talentTeamContributeChangeVo) {
		String id = talentTeamContributeChangeService.auditTalentTeamContributeChange(talentTeamContributeChangeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 创新团队建设任务书变更退回
	 *@param talentTeamContributeChangeVo 创新团队建设任务书变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "创新团队建设任务书变更退回", notes = "创新团队建设任务书变更退回")
	@SystemLogAnnotation(type = "创新团队建设任务书变更",value = "创新团队建设任务书变更退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTalentTeamContributeChange(@RequestBody TalentTeamContributeChangeVo talentTeamContributeChangeVo) {
		String id = talentTeamContributeChangeService.sendBackTalentTeamContributeChange(talentTeamContributeChangeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 创新团队建设任务书变更任务书下达
	 *@param talentTeamContributeChangeVo 创新团队建设任务书变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "创新团队建设任务书变更任务书下达", notes = "创新团队建设任务书变更任务书下达")
	@SystemLogAnnotation(type = "创新团队建设任务书变更",value = "创新团队建设任务书变更任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseTalentTeamContributeChange(@RequestBody TalentTeamContributeChangeVo talentTeamContributeChangeVo) {
		String id = talentTeamContributeChangeService.releaseTalentTeamContributeChange(talentTeamContributeChangeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 创新团队建设任务书变更待办
	 *@param vo 创新团队建设任务书变更 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "创新团队建设任务书变更待办", notes = "创新团队建设任务书变更待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody TalentTeamContributeChangeVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = talentTeamContributeChangeService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = talentTeamContributeChangeService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = talentTeamContributeChangeService.endList(vo);
		} else {
			list = talentTeamContributeChangeService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 创新团队建设任务书变更已办
	 *@param talentTeamContributeChangeVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "创新团队建设任务书变更已办", notes = "创新团队建设任务书变更已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody TalentTeamContributeChangeVo talentTeamContributeChangeVo) {
		PageInfo<TalentTeamContributeChange>  talentTeamContributeChange=talentTeamContributeChangeService.finishedList(talentTeamContributeChangeVo);
		return RestApiResponse.ok(talentTeamContributeChange);
	}
	
	/**
	 *@Description: 创新团队建设任务书变更已完成
	 *@param talentTeamContributeChangeVo 创新团队建设任务书变更 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "创新团队建设任务书变更已完成", notes = "创新团队建设任务书变更已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody TalentTeamContributeChangeVo talentTeamContributeChangeVo) {
		PageInfo<TalentTeamContributeChange>  talentTeamContributeChange=talentTeamContributeChangeService.endList(talentTeamContributeChangeVo);
		return RestApiResponse.ok(talentTeamContributeChange);
	}
}
