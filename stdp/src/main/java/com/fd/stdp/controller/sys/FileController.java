package com.fd.stdp.controller.sys;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.fd.stdp.beans.sys.SysFileInfo;
import com.fd.stdp.beans.sys.vo.SystemFileinfoVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.OperationLogAnnotation;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.config.FileServiceFactory;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.dao.sys.SysFileInfoMapper;
import com.fd.stdp.service.sys.FileService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import io.swagger.annotations.ApiOperation;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@RestController
@RequestMapping("/sys/file")
public class FileController extends BaseController {

    @Autowired
    private FileServiceFactory fileServiceFactory;

    @Autowired
    private SysFileInfoMapper fileInfoMapper;

    
    @Value("${file.useFileType}")
    private String fileSource;
    
    private final String PER_PREFIX = "btn:sys:file:";

    /**
     * 文件上传<br>
     * 根据fileSource选择上传方式，目前仅实现了上传到本地<br>
     * 如有需要可上传到第三方，如阿里云、七牛等
     * @param file
     * @param fileSource FileSource
     * @return
     * @throws Exception
     */
    @PostMapping("/upload")
    //@SystemLogAnnotation(type = "文件查询", value = "上传文件")
    //@OperationLogAnnotation(notes = "上传{文件名:file},{文件源:fileSource}", module = "基础信息管理", submodule = "文件查询")
    //@PreAuthorize("hasAuthority('" + PER_PREFIX + "upload')")
    public SysFileInfo upload(@RequestParam("file") MultipartFile file)
            throws Exception {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }
        
        // 检查文件大小（限制为50MB）
        long maxSize = 50 * 1024 * 1024; // 50MB
        if (file.getSize() > maxSize) {
            throw new ServiceException("上传文件大小不能超过50MB");
        }
        
        FileService fileService = fileServiceFactory.getFileService(fileSource);
        SysFileInfo fileInfo = new SysFileInfo();
        return fileService.upload(file, fileInfo);
    }

    /**
     * 文件上传<br>
     * 根据fileSource选择上传方式，目前仅实现了上传到本地<br>
     * 如有需要可上传到第三方，如阿里云、七牛等
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/uploadLocal")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "uploadlocal')")
    public SysFileInfo uploadLocal(@RequestParam("file") MultipartFile file) throws Exception {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }
        
        // 检查文件大小（限制为50MB）
        long maxSize = 50 * 1024 * 1024; // 50MB
        if (file.getSize() > maxSize) {
            throw new ServiceException("上传文件大小不能超过50MB");
        }
        
        FileService fileService = fileServiceFactory.getFileService(fileSource);
        SysFileInfo fileInfo = new SysFileInfo();
        return fileService.upload(file, fileInfo);
    }

    /**
     * layui富文本文件自定义上传
     * @param file
     * @param fileSource
     * @return
     * @throws Exception
     */
    @PostMapping("/layui")
    public Map<String, Object> uploadLayui(@RequestParam("file") MultipartFile file)
            throws Exception {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }
        
        // 检查文件大小（限制为50MB）
        long maxSize = 50 * 1024 * 1024; // 50MB
        if (file.getSize() > maxSize) {
            throw new ServiceException("上传文件大小不能超过50MB");
        }
        
        SysFileInfo fileInfo = upload(file);
        Map<String, Object> map = new HashMap<>();
        map.put("code", 0);
        Map<String, Object> data = new HashMap<>();
        data.put("src", fileInfo.getUrl());
        map.put("data", data);
        return map;
    }

    /**
     * @Description 文件批量删除
     * @param ids 
     * <AUTHOR>
     */
    @RepeatSubAnnotation
    @PostMapping(value = "/deleteFile")
    @ApiOperation(value = "批量删除", notes = "批量删除文件")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "batch:del')")
    @SystemLogAnnotation(type = "文件查询", value = "批量删除文件")
    //    @OperationLogAnnotation(notes = "批量删除文件{文件id集合:ids}", module = "基础信息管理", submodule = "文件查询")
    public RestApiResponse<?> deleteRole(@RequestBody List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ServiceException("非法请求");
        }
        SysFileInfo fileInfo = null;
        for (String id : ids) {
            fileInfo = fileInfoMapper.selectByPrimaryKey(id);
            if (fileInfo != null) {
                FileService fileService = fileServiceFactory.getFileService(fileInfo.getSource());
                fileService.delete(fileInfo);
            }
        }
        return RestApiResponse.ok();
    }

    /**
     * @Description 文件删除
     * @param id 
     * @return void * @throws
     */
    @RepeatSubAnnotation
    @GetMapping("/delete/{id}")
    @ApiOperation(value = "文件查询", notes = "删除单个文件")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "del')")
    @SystemLogAnnotation(type = "文件查询", value = "删除单个文件")
    @OperationLogAnnotation(notes = "批量删除文件{文件id:id}", module = "基础信息管理", submodule = "文件查询")
    public void delete(@PathVariable String id) {
        SysFileInfo fileInfo = fileInfoMapper.selectByPrimaryKey(id);
        if (fileInfo != null) {
            FileService fileService = fileServiceFactory.getFileService(fileInfo.getSource());
            fileService.delete(fileInfo);
        }
    }

    /**
     * 文件查询
     * @param params
     * @return
     */
    @PostMapping("/findPage")
    @ApiOperation(value = "文件查询", notes = "查询文件列表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findFiles(@RequestBody SystemFileinfoVo fileInfo) {
        PageHelper.startPage(fileInfo.getPageNum(), fileInfo.getPageSize());
        Example example = new Example(SysFileInfo.class);
        Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(fileInfo.getName())) {
            criteria.andLike("name", "%" + fileInfo.getName() + "%");
        }
        if (!StringUtils.isEmpty(fileInfo.getSource())) {
            criteria.andEqualTo("source", fileInfo.getSource());
        }
        if (!StringUtils.isEmpty(fileInfo.getBeginDate())) {
            criteria.andGreaterThanOrEqualTo("createTime", fileInfo.getBeginDate());
        }
        if (!StringUtils.isEmpty(fileInfo.getEndDate())) {
            criteria.andLessThanOrEqualTo("createTime", fileInfo.getEndDate());
        }
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //默认排序
        if (StringUtils.isEmpty(fileInfo.getSortField())) {
            example.orderBy("createTime").desc();
        } else {
            if (!StringUtils.isEmpty(fileInfo.getSortType())) {
                example.setOrderByClause(fileInfo.getSortField() + " " + fileInfo.getSortType());
            }
        }
        return RestApiResponse.ok(new PageInfo<>(fileInfoMapper.selectByExample(example)));
    }
}
