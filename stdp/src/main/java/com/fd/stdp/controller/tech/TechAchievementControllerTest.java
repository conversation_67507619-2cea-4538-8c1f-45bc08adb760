package com.fd.stdp.controller.tech;

import com.alibaba.fastjson.JSONObject;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.beans.tech.TechAchievement;
import com.fd.stdp.beans.tech.vo.TechAchievementVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.tech.TechAchievementService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 *@Description: 成果统计
 *@Author: wangsh
 *@Date: 2022-01-04 13:49:38
 */
@RestController
@RequestMapping("/rest/tech/techAchievement")
@Api(value="成果统计", description="成果统计")
public class TechAchievementControllerTest extends BaseController {

	@Autowired
	private TechAchievementService techAchievementService;

	@Autowired
	private FlowApiService flowApiService;

	@Resource
	protected TaskService taskService;

	private final String PER_PREFIX = "tech:but:achievement:";
	
	/**
	 *@Description: 新增成果统计
	 *@param techAchievement 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增成果统计", notes = "新增成果统计")
	@SystemLogAnnotation(type = "成果统计",value = "新增成果统计")
	public RestApiResponse<?> saveTechAchievement(@RequestBody TechAchievementVo techAchievement) {
		String id = techAchievementService.saveOrUpdateTechAchievement(techAchievement);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改成果统计
	 *@param techAchievement 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改成果统计", notes = "修改成果统计")
	@SystemLogAnnotation(type = "成果统计",value = "修改成果统计")
	public RestApiResponse<?> updateTechAchievement(@RequestBody TechAchievementVo techAchievement) {
		String id = techAchievementService.saveOrUpdateTechAchievement(techAchievement);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除成果统计(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除成果统计", notes = "删除成果统计")
	@SystemLogAnnotation(type = "成果统计",value = "删除成果统计")
	public RestApiResponse<?> deleteTechAchievement(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		techAchievementService.deleteTechAchievement(id);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询成果统计详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询成果统计详情", notes = "查询成果统计详情")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		TechAchievement  techAchievement=techAchievementService.findById(id);
		return RestApiResponse.ok(techAchievement);
	}
	
	/**
	 *@Description: 分页查询成果统计
	 *@param techAchievementVo 成果统计 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询成果统计", notes = "分页查询成果统计")
	public RestApiResponse<?> findPageByQuery(@RequestBody TechAchievementVo techAchievementVo) {
		PageInfo<TechAchievement>  techAchievement=techAchievementService.findPageByQuery(techAchievementVo);
		return RestApiResponse.ok(techAchievement);
	}

	/**
	 *@Description: 提交成果统计
	 *@param techAchievementVo 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "提交成果统计", notes = "提交成果统计")
	@SystemLogAnnotation(type = "成果统计",value = "提交成果统计")
	public RestApiResponse<?> submitTechAchievement(@RequestBody TechAchievementVo techAchievementVo) {
		String id = techAchievementService.submitTechAchievement(techAchievementVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 提交成果统计
	 *@param techAchievementVo 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "审核成果统计", notes = "审核成果统计")
	@SystemLogAnnotation(type = "成果统计",value = "审核成果统计")
	public RestApiResponse<?> auditTechAchievement(@RequestBody TechAchievementVo techAchievementVo) {
		String id = techAchievementService.auditTechAchievement(techAchievementVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 提交成果统计
	 *@param techAchievementVo 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "退回成果统计", notes = "退回成果统计")
	@SystemLogAnnotation(type = "成果统计",value = "退回成果统计")
	public RestApiResponse<?> sendBackTechAchievement(@RequestBody TechAchievementVo techAchievementVo) {
		String id = techAchievementService.sendBackTechAchievement(techAchievementVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 提交成果统计
	 *@param techAchievementVo 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/reject")
	@ApiOperation(value = "退回成果统计", notes = "退回成果统计")
	@SystemLogAnnotation(type = "成果统计",value = "退回成果统计")
	public RestApiResponse<?> rejectTechAchievement(@RequestBody TechAchievementVo techAchievementVo) {
		String id = techAchievementService.rejectTechAchievement(techAchievementVo);
		return RestApiResponse.ok(id);
	}


	/**
	 * 查询任务进度
	 * @param projectApplyInfoVo
	 * @return
	 */
	@RepeatSubAnnotation
	@PostMapping("/task")
	@ApiOperation(value = "项目下达", notes = "项目下达")
	@SystemLogAnnotation(type = "项目下达", value = "项目下达")
	public RestApiResponse<?> taskProjectApplyInfo(@RequestBody ProjectApplyInfoVo projectApplyInfoVo) {
		String taskId = flowApiService.getTaskId(projectApplyInfoVo.getId());
		Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("name", task.getName());
		jsonObject.put("step", task.getTaskDefinitionKey());
		return RestApiResponse.ok(jsonObject);
	}

	/**
	 *@Description: 退回成果待办
	 *@param techAchievementVo 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/todo")
	@ApiOperation(value = "退回成果待办", notes = "退回成果待办")
	@SystemLogAnnotation(type = "成果统计",value = "退回成果待办")
	public RestApiResponse<?> todoTechAchievement(@RequestBody TechAchievementVo techAchievementVo) {
		return RestApiResponse.ok(techAchievementService.todoTechAchievement(techAchievementVo));
	}

	/**
	 *@Description: 退回成果待办
	 *@param techAchievementVo 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/finishedList")
	@ApiOperation(value = "退回成果已办", notes = "退回成果已办")
	@SystemLogAnnotation(type = "成果统计",value = "退回成果已办")
	public RestApiResponse<?> finishedTechAchievement(@RequestBody TechAchievementVo techAchievementVo) {
		return RestApiResponse.ok(techAchievementService.finishedTechAchievement(techAchievementVo));
	}
}
