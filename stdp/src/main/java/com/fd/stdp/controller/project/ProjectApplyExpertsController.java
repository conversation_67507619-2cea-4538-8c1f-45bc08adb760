package com.fd.stdp.controller.project;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.project.ProjectApplyExperts;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertsVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectApplyExpertsService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 项目专家信息Controller
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
@RestController
@RequestMapping("/project/applyExperts")
@Api(value = "项目专家信息", description = "项目专家信息")
public class ProjectApplyExpertsController extends BaseController {
    @Autowired
    private ProjectApplyExpertsService projectApplyExpertsService;

    private final String PER_PREFIX = "btn:project:applyExperts:";

    /**
     * @param projectApplyExperts 项目专家信息数据 json
     * @return RestApiResponse<?>
     * @Description: 新增项目专家信息
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增项目专家信息", notes = "新增项目专家信息")
    @SystemLogAnnotation(type = "项目专家信息", value = "新增项目专家信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveProjectApplyExperts(@RequestBody ProjectApplyExperts projectApplyExperts) {
        String id = projectApplyExpertsService.saveOrUpdateProjectApplyExperts(projectApplyExperts);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectApplyExperts 项目专家信息数据 json
     * @return RestApiResponse<?>
     * @Description: 修改项目专家信息
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改项目专家信息", notes = "修改项目专家信息")
    @SystemLogAnnotation(type = "项目专家信息", value = "修改项目专家信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateProjectApplyExperts(@RequestBody ProjectApplyExperts projectApplyExperts) {
        String id = projectApplyExpertsService.saveOrUpdateProjectApplyExperts(projectApplyExperts);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除项目专家信息(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除项目专家信息", notes = "批量删除项目专家信息")
    @SystemLogAnnotation(type = "项目专家信息", value = "批量删除项目专家信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteProjectApplyExperts(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        projectApplyExpertsService.deleteProjectApplyExperts(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询项目专家信息详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询项目专家信息详情", notes = "查询项目专家信息详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ProjectApplyExperts projectApplyExperts = projectApplyExpertsService.findById(id);
        return RestApiResponse.ok(projectApplyExperts);
    }

    /**
     * @param projectApplyExpertsVo 项目专家信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询项目专家信息
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询项目专家信息", notes = "分页查询项目专家信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ProjectApplyExpertsVo projectApplyExpertsVo) {
        PageInfo<ProjectApplyExperts> projectApplyExperts = projectApplyExpertsService.findPageByQuery(projectApplyExpertsVo);
        return RestApiResponse.ok(projectApplyExperts);
    }

}
