package com.fd.stdp.controller.project;

import com.fd.stdp.beans.project.ProjectApplyExperts;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertsVo;
import com.fd.stdp.enums.QueryTypeEnum;
import com.fd.stdp.service.project.ProjectContractApplyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractInterimReport;
import com.fd.stdp.beans.project.vo.ProjectContractInterimReportVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectContractInterimReportService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 任务书中期检查表单
 *@Author: wangsh
 *@Date: 2022-03-03 14:34:43
 */
@RestController
@RequestMapping("/project/projectContractInterimReport")
@Api(value="任务书中期检查表单", description="任务书中期检查表单")
public class ProjectContractInterimReportController  extends BaseController {

	@Autowired
	private ProjectContractInterimReportService projectContractInterimReportService;
	@Autowired
	private ProjectContractApplyService projectContractApplyService;

	private final String PER_PREFIX = "but:project:interim:";
	
	/**
	 *@Description: 新增任务书中期检查表单
	 *@param projectContractInterimReportVo 任务书中期检查表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增任务书中期检查表单", notes = "新增任务书中期检查表单")
	@SystemLogAnnotation(type = "任务书中期检查表单",value = "新增任务书中期检查表单")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveProjectContractInterimReport(@RequestBody ProjectContractInterimReportVo projectContractInterimReportVo) {
		projectContractInterimReportVo.setOrgName(getCurrentOrgName());
		projectContractInterimReportVo.setOrgCode(getCurrentScienceOrgId());
		String id = projectContractInterimReportService.saveOrUpdateProjectContractInterimReport(projectContractInterimReportVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改任务书中期检查表单
	 *@param projectContractInterimReport 任务书中期检查表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改任务书中期检查表单", notes = "修改任务书中期检查表单")
	@SystemLogAnnotation(type = "任务书中期检查表单",value = "修改任务书中期检查表单")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateProjectContractInterimReport(@RequestBody ProjectContractInterimReportVo projectContractInterimReport) {
		String id = projectContractInterimReportService.saveOrUpdateProjectContractInterimReport(projectContractInterimReport);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除任务书中期检查表单(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除任务书中期检查表单", notes = "删除任务书中期检查表单")
	@SystemLogAnnotation(type = "任务书中期检查表单",value = "删除任务书中期检查表单")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteProjectContractInterimReport(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		projectContractInterimReportService.deleteProjectContractInterimReport(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除任务书中期检查表单(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除任务书中期检查表单", notes = "删除任务书中期检查表单")
	@SystemLogAnnotation(type = "任务书中期检查表单",value = "删除任务书中期检查表单")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiProjectContractInterimReport(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		projectContractInterimReportService.deleteMultiProjectContractInterimReport(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询任务书中期检查表单详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询任务书中期检查表单详情", notes = "查询任务书中期检查表单详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		ProjectContractInterimReport  projectContractInterimReport=projectContractInterimReportService.findById(id);
		return RestApiResponse.ok(projectContractInterimReport);
	}
	
	/**
	 *@Description: 查询任务书中期检查表单详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findOriginById")
	@ApiOperation(value = "查询任务书中期检查表单详情", notes = "查询任务书中期检查表单详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findOriginById(@RequestParam("id") String id) {
		ProjectContractInterimReport  projectContractInterimReport=projectContractInterimReportService.findById(id);
		return RestApiResponse.ok(projectContractApplyService.findById(projectContractInterimReport.getContractId()));
	}

	/**
	 *@Description: 分页查询任务书中期检查表单
	 *@param projectContractInterimReportVo 任务书中期检查表单 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询任务书中期检查表单", notes = "分页查询任务书中期检查表单")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody ProjectContractInterimReportVo projectContractInterimReportVo) {
		PageInfo<ProjectContractInterimReport>  projectContractInterimReport=projectContractInterimReportService.findPageByQuery(projectContractInterimReportVo);
		return RestApiResponse.ok(projectContractInterimReport);
	}

	/**
	 *@Description: 任务书中期检查表单提交
	 *@param projectContractInterimReportVo 任务书中期检查表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "任务书中期检查表单提交", notes = "任务书中期检查表单提交")
	@SystemLogAnnotation(type = "任务书中期检查表单",value = "任务书中期检查表单提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitProjectContractInterimReport(@RequestBody ProjectContractInterimReportVo projectContractInterimReportVo) {
		projectContractInterimReportVo.setOrgName(getCurrentOrgName());
		projectContractInterimReportVo.setOrgCode(getCurrentScienceOrgId());
		String id = projectContractInterimReportService.submitProjectContractInterimReport(projectContractInterimReportVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书中期检查表单提交
	 *@param projectApplyExpertsVo 任务书中期检查表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/expertSubmit")
	@ApiOperation(value = "任务书中期检查表单提交", notes = "任务书中期检查表单提交")
	@SystemLogAnnotation(type = "任务书中期检查表单",value = "任务书中期检查表单提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> expertSubmitProjectContractInterimReport(@RequestBody ProjectApplyExpertsVo projectApplyExpertsVo) {
		String id = projectContractInterimReportService.expertSubmitProjectContractInterimReport(projectApplyExpertsVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 任务书中期检查表单审核
	 *@param projectContractInterimReportVo 任务书中期检查表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "任务书中期检查表单审核", notes = "任务书中期检查表单审核")
	@SystemLogAnnotation(type = "任务书中期检查表单",value = "任务书中期检查表单审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditProjectContractInterimReport(@RequestBody ProjectContractInterimReportVo projectContractInterimReportVo) {
		String id = projectContractInterimReportService.auditProjectContractInterimReport(projectContractInterimReportVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书中期检查表单退回
	 *@param projectContractInterimReportVo 任务书中期检查表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "任务书中期检查表单退回", notes = "任务书中期检查表单退回")
	@SystemLogAnnotation(type = "任务书中期检查表单",value = "任务书中期检查表单退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackProjectContractInterimReport(@RequestBody ProjectContractInterimReportVo projectContractInterimReportVo) {
		String id = projectContractInterimReportService.sendBackProjectContractInterimReport(projectContractInterimReportVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书中期检查表单任务书下达
	 *@param projectContractInterimReportVo 任务书中期检查表单数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "任务书中期检查表单任务书下达", notes = "任务书中期检查表单任务书下达")
	@SystemLogAnnotation(type = "任务书中期检查表单",value = "任务书中期检查表单任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseProjectContractInterimReport(@RequestBody ProjectContractInterimReportVo projectContractInterimReportVo) {
		String id = projectContractInterimReportService.releaseProjectContractInterimReport(projectContractInterimReportVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书中期检查表单待办
	 *@param vo 任务书中期检查表单 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "任务书中期检查表单待办", notes = "任务书中期检查表单待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody ProjectContractInterimReportVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = projectContractInterimReportService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = projectContractInterimReportService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = projectContractInterimReportService.endList(vo);
		} else {
			list = projectContractInterimReportService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 任务书中期检查表单已办
	 *@param projectContractInterimReportVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "任务书中期检查表单已办", notes = "任务书中期检查表单已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody ProjectContractInterimReportVo projectContractInterimReportVo) {
		PageInfo<ProjectContractInterimReport>  projectContractInterimReport=projectContractInterimReportService.finishedList(projectContractInterimReportVo);
		return RestApiResponse.ok(projectContractInterimReport);
	}
	
	/**
	 *@Description: 任务书中期检查表单已完成
	 *@param projectContractInterimReportVo 任务书中期检查表单 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "任务书中期检查表单已完成", notes = "任务书中期检查表单已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody ProjectContractInterimReportVo projectContractInterimReportVo) {
		PageInfo<ProjectContractInterimReport>  projectContractInterimReport=projectContractInterimReportService.endList(projectContractInterimReportVo);
		return RestApiResponse.ok(projectContractInterimReport);
	}
}
