package com.fd.stdp.controller.basic;

import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonExpertRecommend;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertRecommendVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.basic.BasicPersonExpertRecommendService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 专家推荐表
 *@Author: wangsh
 *@Date: 2022-01-11 19:13:41
 */
@RestController
@RequestMapping("/basic/basicPersonExpertRecommend")
@Api(value="专家推荐表", description="专家推荐表")
public class BasicPersonExpertRecommendController  extends BaseController {

	@Autowired
	private BasicPersonExpertRecommendService basicPersonExpertRecommendService;
	
	private final String PER_PREFIX = "basic:but:expert:";

	/**
	 *@Description: 新增专家推荐表
	 *@param basicPersonExpertRecommendVo 专家推荐表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增专家推荐表", notes = "新增专家推荐表")
	@SystemLogAnnotation(type = "专家推荐表",value = "新增专家推荐表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveBasicPersonExpertRecommend(@RequestBody BasicPersonExpertRecommendVo basicPersonExpertRecommendVo) {
		if(StringUtils.isBlank(basicPersonExpertRecommendVo.getOrgName())) {
			basicPersonExpertRecommendVo.setOrgName(getCurrentOrgName());
			basicPersonExpertRecommendVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = basicPersonExpertRecommendService.saveOrUpdateBasicPersonExpertRecommend(basicPersonExpertRecommendVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 修改专家推荐表
	 *@param basicPersonExpertRecommend 专家推荐表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改专家推荐表", notes = "修改专家推荐表")
	@SystemLogAnnotation(type = "专家推荐表",value = "修改专家推荐表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateBasicPersonExpertRecommend(@RequestBody BasicPersonExpertRecommendVo basicPersonExpertRecommend) {
		String id = basicPersonExpertRecommendService.saveOrUpdateBasicPersonExpertRecommend(basicPersonExpertRecommend);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 删除专家推荐表(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除专家推荐表", notes = "删除专家推荐表")
	@SystemLogAnnotation(type = "专家推荐表",value = "删除专家推荐表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteBasicPersonExpertRecommend(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		basicPersonExpertRecommendService.deleteBasicPersonExpertRecommend(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除专家推荐表(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除专家推荐表", notes = "删除专家推荐表")
	@SystemLogAnnotation(type = "专家推荐表",value = "删除专家推荐表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiBasicPersonExpertRecommend(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		basicPersonExpertRecommendService.deleteMultiBasicPersonExpertRecommend(ids);
		return RestApiResponse.ok();
	}

	/**
	 *@Description: 查询专家推荐表详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询专家推荐表详情", notes = "查询专家推荐表详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		BasicPersonExpertRecommend  basicPersonExpertRecommend=basicPersonExpertRecommendService.findById(id);
		return RestApiResponse.ok(basicPersonExpertRecommend);
	}

	/**
	 *@Description: 分页查询专家推荐表
	 *@param basicPersonExpertRecommendVo 专家推荐表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询专家推荐表", notes = "分页查询专家推荐表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody BasicPersonExpertRecommendVo basicPersonExpertRecommendVo) {
		PageInfo<BasicPersonExpertRecommend>  basicPersonExpertRecommend=basicPersonExpertRecommendService.findPageByQuery(basicPersonExpertRecommendVo);
		return RestApiResponse.ok(basicPersonExpertRecommend);
	}

	/**
	 *@Description: 专家推荐表提交
	 *@param basicPersonExpertRecommendVo 专家推荐表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "专家推荐表提交", notes = "专家推荐表提交")
	@SystemLogAnnotation(type = "专家推荐表",value = "专家推荐表提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitBasicPersonExpertRecommend(@RequestBody BasicPersonExpertRecommendVo basicPersonExpertRecommendVo) {
		if(StringUtils.isBlank(basicPersonExpertRecommendVo.getOrgName())) {
			basicPersonExpertRecommendVo.setOrgName(getCurrentOrgName());
			basicPersonExpertRecommendVo.setOrgCode(getLoginUser().getAreaCode());
		}
		String id = basicPersonExpertRecommendService.submitBasicPersonExpertRecommend(basicPersonExpertRecommendVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 专家推荐表审核
	 *@param basicPersonExpertRecommendVo 专家推荐表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "专家推荐表审核", notes = "专家推荐表审核")
	@SystemLogAnnotation(type = "专家推荐表",value = "专家推荐表审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditBasicPersonExpertRecommend(@RequestBody BasicPersonExpertRecommendVo basicPersonExpertRecommendVo) {
		String id = basicPersonExpertRecommendService.auditBasicPersonExpertRecommend(basicPersonExpertRecommendVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 专家推荐表退回
	 *@param basicPersonExpertRecommendVo 专家推荐表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "专家推荐表退回", notes = "专家推荐表退回")
	@SystemLogAnnotation(type = "专家推荐表",value = "专家推荐表退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackBasicPersonExpertRecommend(@RequestBody BasicPersonExpertRecommendVo basicPersonExpertRecommendVo) {
		String id = basicPersonExpertRecommendService.sendBackBasicPersonExpertRecommend(basicPersonExpertRecommendVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 专家推荐表任务书下达
	 *@param basicPersonExpertRecommendVo 专家推荐表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "专家推荐表任务书下达", notes = "专家推荐表任务书下达")
	@SystemLogAnnotation(type = "专家推荐表",value = "专家推荐表任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseBasicPersonExpertRecommend(@RequestBody BasicPersonExpertRecommendVo basicPersonExpertRecommendVo) {
		String id = basicPersonExpertRecommendService.releaseBasicPersonExpertRecommend(basicPersonExpertRecommendVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 专家推荐表待办
	 *@param vo 专家推荐表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "专家推荐表待办", notes = "专家推荐表待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody BasicPersonExpertRecommendVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = basicPersonExpertRecommendService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = basicPersonExpertRecommendService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = basicPersonExpertRecommendService.endList(vo);
		} else {
			list = basicPersonExpertRecommendService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 专家推荐表已办
	 *@param basicPersonExpertRecommendVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "专家推荐表已办", notes = "专家推荐表已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody BasicPersonExpertRecommendVo basicPersonExpertRecommendVo) {
		PageInfo<BasicPersonExpertRecommend>  basicPersonExpertRecommend=basicPersonExpertRecommendService.finishedList(basicPersonExpertRecommendVo);
		return RestApiResponse.ok(basicPersonExpertRecommend);
	}

	/**
	 *@Description: 专家推荐表已完成
	 *@param basicPersonExpertRecommendVo 专家推荐表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "专家推荐表已完成", notes = "专家推荐表已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody BasicPersonExpertRecommendVo basicPersonExpertRecommendVo) {
		PageInfo<BasicPersonExpertRecommend>  basicPersonExpertRecommend=basicPersonExpertRecommendService.endList(basicPersonExpertRecommendVo);
		return RestApiResponse.ok(basicPersonExpertRecommend);
	}

	/**
	 *@Description: 专家推荐列表导出
	 *@param basicPersonExpertRecommendVo 专家推荐列表导出 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/export")
	@ApiOperation(value = "专家推荐列表导出", notes = "专家推荐列表导出")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> exportPageByQuery(@RequestBody BasicPersonExpertRecommendVo basicPersonExpertRecommendVo, HttpServletResponse response) {
		basicPersonExpertRecommendService.exportPageByQuery(basicPersonExpertRecommendVo, response);
		return RestApiResponse.ok();
	}

	/**
	 *@Description: 导出专家推荐表单
	 *@param basicPersonExpertRecommendVo
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/exportOne")
	@ApiOperation(value = "导出专家推荐表单", notes = "导出专家推荐表单")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public void exportOne(@RequestBody BasicPersonExpertRecommendVo basicPersonExpertRecommendVo, HttpServletResponse response) {
		basicPersonExpertRecommendService.exportOne(basicPersonExpertRecommendVo, response);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			BasicPersonExpertRecommendVo vo = new BasicPersonExpertRecommendVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据
			
			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();
			
			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};
			
			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<BasicPersonExpertRecommend> pageInfo = (PageInfo<BasicPersonExpertRecommend>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}
			
			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);
			
		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
}
