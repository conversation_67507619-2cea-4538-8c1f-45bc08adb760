package com.fd.stdp.controller.project;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.project.ProjectDemandCollectionGuid;
import com.fd.stdp.beans.project.vo.ProjectDemandCollectionGuidVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectDemandCollectionGuidService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 需求征集指南Controller
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
@RestController
@RequestMapping("/project/demandCollectionGuid")
@Api(value = "需求征集指南", description = "需求征集指南")
public class ProjectDemandCollectionGuidController extends BaseController {
    @Autowired
    private ProjectDemandCollectionGuidService projectDemandCollectionGuidService;

    private final String PER_PREFIX = "btn:project:demandCollectionGuid:";

    /**
     * @param projectDemandCollectionGuid 需求征集指南数据 json
     * @return RestApiResponse<?>
     * @Description: 新增需求征集指南
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增需求征集指南", notes = "新增需求征集指南")
    @SystemLogAnnotation(type = "需求征集指南", value = "新增需求征集指南")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveProjectDemandCollectionGuid(@RequestBody ProjectDemandCollectionGuid projectDemandCollectionGuid) {
        String id = projectDemandCollectionGuidService.saveOrUpdateProjectDemandCollectionGuid(projectDemandCollectionGuid);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectDemandCollectionGuid 需求征集指南数据 json
     * @return RestApiResponse<?>
     * @Description: 修改需求征集指南
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改需求征集指南", notes = "修改需求征集指南")
    @SystemLogAnnotation(type = "需求征集指南", value = "修改需求征集指南")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateProjectDemandCollectionGuid(@RequestBody ProjectDemandCollectionGuid projectDemandCollectionGuid) {
        String id = projectDemandCollectionGuidService.saveOrUpdateProjectDemandCollectionGuid(projectDemandCollectionGuid);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除需求征集指南(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除需求征集指南", notes = "批量删除需求征集指南")
    @SystemLogAnnotation(type = "需求征集指南", value = "批量删除需求征集指南")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteProjectDemandCollectionGuid(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        projectDemandCollectionGuidService.deleteProjectDemandCollectionGuid(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询需求征集指南详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询需求征集指南详情", notes = "查询需求征集指南详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ProjectDemandCollectionGuid projectDemandCollectionGuid = projectDemandCollectionGuidService.findById(id);
        return RestApiResponse.ok(projectDemandCollectionGuid);
    }

    /**
     * @param projectDemandCollectionGuidVo 需求征集指南 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询需求征集指南
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询需求征集指南", notes = "分页查询需求征集指南")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ProjectDemandCollectionGuidVo projectDemandCollectionGuidVo) {
        PageInfo<ProjectDemandCollectionGuid> projectDemandCollectionGuid = projectDemandCollectionGuidService.findPageByQuery(projectDemandCollectionGuidVo);
        return RestApiResponse.ok(projectDemandCollectionGuid);
    }



    /**
     * @param ids 需求征集指南数据 json
     * @return RestApiResponse<?>
     * @Description: 修改需求征集指南发布
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/open")
    @ApiOperation(value = "发布", notes = "发布")
    @SystemLogAnnotation(type = "发布", value = "发布")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "open')")
    public RestApiResponse<?> openProjectDemandCollectionGuid(@RequestBody ProjectDemandCollectionGuidVo projectDemandCollectionGuidVo) {
        String id = projectDemandCollectionGuidService.open(projectDemandCollectionGuidVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectDemandCollectionGuidVo 需求征集指南 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询需求征集指南
     * @Author: yujianfei
     */
    @PostMapping("/todo")
    @ApiOperation(value = "分页查询需求征集指南", notes = "分页查询需求征集指南")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> todoList(@RequestBody ProjectDemandCollectionGuidVo projectDemandCollectionGuidVo) {
        PageInfo<ProjectDemandCollectionGuid> projectDemandCollectionGuid = projectDemandCollectionGuidService.todoList(projectDemandCollectionGuidVo);
        return RestApiResponse.ok(projectDemandCollectionGuid);
    }

    /**
     * @param projectDemandCollectionGuidVo 需求征集指南 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询需求征集指南
     * @Author: yujianfei
     */
    @PostMapping("/finished")
    @ApiOperation(value = "分页查询需求征集指南", notes = "分页查询需求征集指南")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> finishedList(@RequestBody ProjectDemandCollectionGuidVo projectDemandCollectionGuidVo) {
        PageInfo<ProjectDemandCollectionGuid> projectDemandCollectionGuid = projectDemandCollectionGuidService.finishedList(projectDemandCollectionGuidVo);
        return RestApiResponse.ok(projectDemandCollectionGuid);
    }
}
