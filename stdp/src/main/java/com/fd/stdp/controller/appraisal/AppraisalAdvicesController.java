package com.fd.stdp.controller.appraisal;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.appraisal.AppraisalAdvices;
import com.fd.stdp.beans.appraisal.vo.AppraisalAdvicesVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.appraisal.AppraisalAdvicesService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 评价退回Controller
 *
 * <AUTHOR>
 * @date 2021-11-19
 */
@RestController
@RequestMapping("/appraisal/advices")
@Api(value = "评价退回", description = "评价退回")
public class AppraisalAdvicesController extends BaseController {
    @Autowired
    private AppraisalAdvicesService appraisalAdvicesService;

    private final String PER_PREFIX = "btn:appraisal:advices:";

    /**
     * @param appraisalAdvices 评价退回数据 json
     * @return RestApiResponse<?>
     * @Description: 新增评价退回
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增评价退回", notes = "新增评价退回")
    @SystemLogAnnotation(type = "评价退回", value = "新增评价退回")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveAppraisalAdvices(@RequestBody AppraisalAdvices appraisalAdvices) {
        String id = appraisalAdvicesService.saveOrUpdateAppraisalAdvices(appraisalAdvices);
        return RestApiResponse.ok(id);
    }

    /**
     * @param appraisalAdvices 评价退回数据 json
     * @return RestApiResponse<?>
     * @Description: 修改评价退回
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改评价退回", notes = "修改评价退回")
    @SystemLogAnnotation(type = "评价退回", value = "修改评价退回")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateAppraisalAdvices(@RequestBody AppraisalAdvices appraisalAdvices) {
        String id = appraisalAdvicesService.saveOrUpdateAppraisalAdvices(appraisalAdvices);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除评价退回(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除评价退回", notes = "批量删除评价退回")
    @SystemLogAnnotation(type = "评价退回", value = "批量删除评价退回")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteAppraisalAdvices(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        appraisalAdvicesService.deleteAppraisalAdvices(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询评价退回详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询评价退回详情", notes = "查询评价退回详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        AppraisalAdvices appraisalAdvices = appraisalAdvicesService.findById(id);
        return RestApiResponse.ok(appraisalAdvices);
    }

    /**
     * @param appraisalAdvicesVo 评价退回 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询评价退回
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询评价退回", notes = "分页查询评价退回")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody AppraisalAdvicesVo appraisalAdvicesVo) {
        PageInfo<AppraisalAdvices> appraisalAdvices = appraisalAdvicesService.findPageByQuery(appraisalAdvicesVo);
        return RestApiResponse.ok(appraisalAdvices);
    }

}
