package com.fd.stdp.controller.audit;

import com.fd.stdp.beans.audit.InnerAuditInformationCollection;
import com.fd.stdp.beans.audit.vo.InnerAuditInformationCollectionExportVo;
import com.fd.stdp.beans.audit.vo.InnerAuditInformationCollectionVo;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.constant.InnerAuditBasicConstant;
import com.fd.stdp.util.EasyExcelUtils;
import com.fd.stdp.util.ExportUtil;
import com.fd.stdp.util.UUIDUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.audit.InnerAuditInformationCollectionsService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 *@Description: 浙江省内部审计人员信息采集表
 *@Author: sef
 *@Date: 2022-06-06 13:55:59
 */
@RestController
@RequestMapping("/audit/innerAuditInformationCollections")
@Api(value="浙江省内部审计人员信息采集表", description="浙江省内部审计人员信息采集表")
public class InnerAuditInformationCollectionController  extends BaseController {

	@Autowired
	private InnerAuditInformationCollectionsService innerAuditInformationCollectionsService;

	//private final String PER_PREFIX = "这里写业务前缀命名:but:模块:功能:";

	private final String PER_PREFIX = "btn:audit:Collections:";

	/**
	 *@Description: 新增浙江省内部审计人员信息采集表
	 *@param innerAuditInformationCollectionVo 浙江省内部审计人员信息采集表数据 json
	 *@return RestApiResponse<?>
	 *@Author: sef
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增浙江省内部审计人员信息采集表", notes = "新增浙江省内部审计人员信息采集表")
	@SystemLogAnnotation(type = "浙江省内部审计人员信息采集表",value = "新增浙江省内部审计人员信息采集表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInnerAuditInformationCollection(@RequestBody InnerAuditInformationCollectionVo innerAuditInformationCollectionVo) {
		List<InnerAuditInformationCollectionVo> contactList = innerAuditInformationCollectionVo.getInfomationList();
		innerAuditInformationCollectionsService.saveBatchInnerAuditWorkContacts(contactList);
		return RestApiResponse.ok();
	}

	/**
	 *@Description: 修改浙江省内部审计人员信息采集表
	 *@param innerAuditInformationCollection 浙江省内部审计人员信息采集表数据 json
	 *@return RestApiResponse<?>
	 *@Author: sef
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改浙江省内部审计人员信息采集表", notes = "修改浙江省内部审计人员信息采集表")
	@SystemLogAnnotation(type = "浙江省内部审计人员信息采集表",value = "修改浙江省内部审计人员信息采集表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInnerAuditInformationCollection(@RequestBody InnerAuditInformationCollectionVo innerAuditInformationCollection) {
		String id = innerAuditInformationCollectionsService.saveOrUpdateInnerAuditInformationCollection(innerAuditInformationCollection);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 批量删除浙江省内部审计人员信息采集表(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: sef
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除浙江省内部审计人员信息采集表", notes = "删除浙江省内部审计人员信息采集表")
	@SystemLogAnnotation(type = "浙江省内部审计人员信息采集表",value = "删除浙江省内部审计人员信息采集表")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiInnerAuditInformationCollection(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innerAuditInformationCollectionsService.deleteMultiInnerAuditInformationCollection(ids);
		return RestApiResponse.ok();
	}

	/**
	 *@Description: 查询浙江省内部审计人员信息采集表详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: sef
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询浙江省内部审计人员信息采集表详情", notes = "查询浙江省内部审计人员信息采集表详情")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InnerAuditInformationCollection innerAuditInformationCollection = innerAuditInformationCollectionsService.findById(id);
		return RestApiResponse.ok(innerAuditInformationCollection);
	}

	/**
	 *@Description: 分页查询浙江省内部审计人员信息采集表
	 *@param innerAuditInformationCollectionVo 浙江省内部审计人员信息采集表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: sef
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询浙江省内部审计人员信息采集表", notes = "分页查询浙江省内部审计人员信息采集表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnerAuditInformationCollectionVo innerAuditInformationCollectionVo) {
		PageInfo<InnerAuditInformationCollection>  innerAuditInformationCollection= innerAuditInformationCollectionsService.findPageByQuery(innerAuditInformationCollectionVo);
		return RestApiResponse.ok(innerAuditInformationCollection);
	}

	/**
	 *@Description: 浙江省内部审计人员信息采集表提交
	 *@param innerAuditInformationCollectionVo 浙江省内部审计人员信息采集表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "浙江省内部审计人员信息采集表提交", notes = "浙江省内部审计人员信息采集表提交")
	@SystemLogAnnotation(type = "浙江省内部审计人员信息采集表",value = "浙江省内部审计人员信息采集表提交")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitInnerAuditInformationCollection(@RequestBody InnerAuditInformationCollectionVo innerAuditInformationCollectionVo) {
	    if(StringUtils.isBlank(innerAuditInformationCollectionVo.getOrgName())) {
            innerAuditInformationCollectionVo.setOrgName(getCurrentOrgName());
            innerAuditInformationCollectionVo.setOrgCode(getLoginUser().getAreaCode());
		}
		String id = innerAuditInformationCollectionsService.submitInnerAuditInformationCollection(innerAuditInformationCollectionVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 浙江省内部审计人员信息采集表审核
	 *@param innerAuditInformationCollectionVo 浙江省内部审计人员信息采集表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "浙江省内部审计人员信息采集表审核", notes = "浙江省内部审计人员信息采集表审核")
	@SystemLogAnnotation(type = "浙江省内部审计人员信息采集表",value = "浙江省内部审计人员信息采集表审核")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditInnerAuditInformationCollection(@RequestBody InnerAuditInformationCollectionVo innerAuditInformationCollectionVo) {
		String id = innerAuditInformationCollectionsService.auditInnerAuditInformationCollection(innerAuditInformationCollectionVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 浙江省内部审计人员信息采集表退回
	 *@param innerAuditInformationCollectionVo 浙江省内部审计人员信息采集表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "浙江省内部审计人员信息采集表退回", notes = "浙江省内部审计人员信息采集表退回")
	@SystemLogAnnotation(type = "浙江省内部审计人员信息采集表",value = "浙江省内部审计人员信息采集表退回")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackInnerAuditInformationCollection(@RequestBody InnerAuditInformationCollectionVo innerAuditInformationCollectionVo) {
		String id = innerAuditInformationCollectionsService.sendBackInnerAuditInformationCollection(innerAuditInformationCollectionVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 浙江省内部审计人员信息采集表任务书下达
	 *@param innerAuditInformationCollectionVo 浙江省内部审计人员信息采集表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "浙江省内部审计人员信息采集表任务书下达", notes = "浙江省内部审计人员信息采集表任务书下达")
	@SystemLogAnnotation(type = "浙江省内部审计人员信息采集表",value = "浙江省内部审计人员信息采集表任务书下达")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseInnerAuditInformationCollection(@RequestBody InnerAuditInformationCollectionVo innerAuditInformationCollectionVo) {
		String id = innerAuditInformationCollectionsService.releaseInnerAuditInformationCollection(innerAuditInformationCollectionVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 浙江省内部审计人员信息采集表待办
	 *@param innerAuditInformationCollectionVo 浙江省内部审计人员信息采集表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "浙江省内部审计人员信息采集表待办", notes = "浙江省内部审计人员信息采集表待办")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody InnerAuditInformationCollectionVo innerAuditInformationCollectionVo) {
		PageInfo<InnerAuditInformationCollection>  innerAuditInformationCollection= innerAuditInformationCollectionsService.todoList(innerAuditInformationCollectionVo);
		return RestApiResponse.ok(innerAuditInformationCollection);
	}
	/**
	 *@Description: 浙江省内部审计人员信息采集表已办
	 *@param innerAuditInformationCollectionVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "浙江省内部审计人员信息采集表已办", notes = "浙江省内部审计人员信息采集表已办")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody InnerAuditInformationCollectionVo innerAuditInformationCollectionVo) {
		PageInfo<InnerAuditInformationCollection>  innerAuditInformationCollection= innerAuditInformationCollectionsService.finishedList(innerAuditInformationCollectionVo);
		return RestApiResponse.ok(innerAuditInformationCollection);
	}

	/**
	 *@Description: 浙江省内部审计人员信息采集表已完成
	 *@param innerAuditInformationCollectionVo 浙江省内部审计人员信息采集表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "浙江省内部审计人员信息采集表已完成", notes = "浙江省内部审计人员信息采集表已完成")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody InnerAuditInformationCollectionVo innerAuditInformationCollectionVo) {
		PageInfo<InnerAuditInformationCollection>  innerAuditInformationCollection= innerAuditInformationCollectionsService.endList(innerAuditInformationCollectionVo);
		return RestApiResponse.ok(innerAuditInformationCollection);
	}


	/**
	 *@Description: 批量删除浙江省内部审计人员信息采集表(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: sef
	 */
	@RepeatSubAnnotation
	@PostMapping("/remove")
	@ApiOperation(value = "移除本单位", notes = "移除本单位")
	@SystemLogAnnotation(type = "移除本单位",value = "移除本单位")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> removeMultiInnerAuditInformationCollection(@RequestParam("id") String id) {
		innerAuditInformationCollectionsService.removeInnerAuditInformationCollection(id);
		return RestApiResponse.ok();
	}

	@PostMapping("/findList")
	@ApiOperation(value = "分页查询浙江省内部审计总审计师配备情况表", notes = "分页查询浙江省内部审计总审计师配备情况表")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findList(@RequestBody InnerAuditInformationCollectionVo vo) {
		return RestApiResponse.ok(innerAuditInformationCollectionsService.findList(vo));
	}


	@PostMapping("/export")
	@ApiOperation(value = "导出浙江省内部审计总审计师配备情况表", notes = "导出浙江省内部审计总审计师配备情况表")
	public void export(@RequestBody InnerAuditInformationCollectionVo vo) throws IOException {
		List list = new ArrayList<>();

		String roleCode = getUserRoleList().get(0).getRoleCode();
		if ("AUDIT_PROVINCE".equals(roleCode)) {
			list = innerAuditInformationCollectionsService.exportInnerAuditInformationCollectionVoAllUnit(vo);
		} else if ("AUDIT_PROVINCE_DIRECT".equals(roleCode)){
			list = innerAuditInformationCollectionsService.exportInnerAuditInformationCollectionVo(vo);
		}

		// 文件名
		String prefix = "/word/";
		InputStream inputStream = ExportUtil.getInputStream(prefix + "浙江省内部审计人员信息采集表导出模板.xlsx");
		/*InnerAuditInformationCollectionExportVo exportVo = new InnerAuditInformationCollectionExportVo();
		if (!list.isEmpty()) {
			exportVo.setFillTimeLastCn(((InnerAuditInformationCollectionExportVo) list.get(0)).getFillTimeLast());
		}*/
		EasyExcelUtils.exportTemplateFill(null, list, "Sheet1", "浙江省内部审计人员信息采集表.xlsx", response, inputStream, true);
	}
}
