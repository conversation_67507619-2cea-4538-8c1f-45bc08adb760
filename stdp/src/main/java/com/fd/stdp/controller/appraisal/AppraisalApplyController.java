package com.fd.stdp.controller.appraisal;

import java.util.List;

import com.fd.stdp.beans.appraisal.vo.*;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.constant.Constant;
import com.fd.stdp.util.AppUserUtil;
import com.fd.stdp.util.EasyExcelUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.appraisal.AppraisalApply;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.appraisal.AppraisalApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 评价申请Controller
 *
 * <AUTHOR>
 * @date 2021-11-18
 */
@RestController
@RequestMapping("/appraisal/apply")
@Api(value = "评价申请", description = "评价申请")
public class AppraisalApplyController extends BaseController {

    @Autowired
    private AppraisalApplyService appraisalApplyService;

    private final String PER_PREFIX = "btn:appraisal:apply:";

    /**
     * @param appraisalApplyVo 评价申请数据 json
     * @return RestApiResponse<?>
     * @Description: 新增评价申请
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增评价申请", notes = "新增评价申请")
    @SystemLogAnnotation(type = "评价申请", value = "新增评价申请")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveAppraisalApply(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        String id = appraisalApplyService.saveOrUpdateAppraisalApply(appraisalApplyVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param appraisalApplyVo 评价申请数据 json
     * @return RestApiResponse<?>
     * @Description: 修改评价申请
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改评价申请", notes = "修改评价申请")
    @SystemLogAnnotation(type = "评价申请", value = "修改评价申请")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateAppraisalApply(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        String id = appraisalApplyService.saveOrUpdateAppraisalApply(appraisalApplyVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除评价申请(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除评价申请", notes = "批量删除评价申请")
    @SystemLogAnnotation(type = "评价申请", value = "批量删除评价申请")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteAppraisalApply(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        appraisalApplyService.deleteAppraisalApply(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询评价申请详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询评价申请详情", notes = "查询评价申请详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        AppraisalApplyVo appraisalApplyVo = appraisalApplyService.findById(id);
        return RestApiResponse.ok(appraisalApplyVo);
    }

    /**
     * @param appraisalApplyVo 评价申请 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询评价申请
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询评价申请", notes = "分页查询评价申请")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        PageInfo<AppraisalApplyVo> appraisalApply = appraisalApplyService.findPageByQuery(appraisalApplyVo);
        return RestApiResponse.ok(appraisalApply);
    }

    /**
     * @param appraisalApplyVo 评价申请数据 json
     * @return RestApiResponse<?>
     * @Description: 省局审核评价申请
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/auditByProvince")
    @ApiOperation(value = "省局审核评价申请", notes = "省局审核评价申请")
    @SystemLogAnnotation(type = "省局审核评价申请", value = "省局审核评价申请")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "auditByProvince')")
    public RestApiResponse<?> auditByProvince(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        appraisalApplyService.auditByProvince(appraisalApplyVo);
        return RestApiResponse.ok();
    }

    /**
     * @param appraisalApplyVo 评价申请数据 json
     * @return RestApiResponse<?>
     * @Description: 市局审核评价申请
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/auditByCity")
    @ApiOperation(value = "市局审核评价申请", notes = "市局审核评价申请")
    @SystemLogAnnotation(type = "市局审核评价申请", value = "市局审核评价申请")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "auditByCity')")
    public RestApiResponse<?> auditByCity(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        appraisalApplyService.auditByCity(appraisalApplyVo);
        return RestApiResponse.ok();
    }

    /**
     * @param appraisalApplyVo 评价申请数据 json
     * @return RestApiResponse<?>
     * @Description: 退回评价申请
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/sendBack")
    @ApiOperation(value = "退回评价申请", notes = "退回评价申请")
    @SystemLogAnnotation(type = "退回评价申请", value = "退回评价申请")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "sendBack')")
    public RestApiResponse<?> sendBack(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        appraisalApplyService.sendBack(appraisalApplyVo);
        return RestApiResponse.ok();
    }

    /**
     * @param appraisalApplyVo 查询条件 json
     * @return RestApiResponse<?>
     * @Description: 初始化考评单位
     * @Author: liuwei
     */
    @RepeatSubAnnotation
    @PostMapping("/initApply")
    @ApiOperation(value = "初始化考评单位", notes = "初始化考评单位")
    @SystemLogAnnotation(type = "初始化考评单位", value = "初始化考评单位")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "initApply')")
    public RestApiResponse<?> initApply(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        appraisalApplyService.initApply(appraisalApplyVo);
        return RestApiResponse.ok();
    }


    /**
     * @return RestApiResponse<?>
     * @Description: 待办数据
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @GetMapping("/toDoCount")
    @ApiOperation(value = "待办数据", notes = "待办数据")
    @SystemLogAnnotation(type = "待办数据", value = "待办数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "toDoCount')")
    public RestApiResponse<?> toDoCount() {
        SysUser user = (SysUser) AppUserUtil.getLoginAppUser();
        int count = appraisalApplyService.toDoCount(user);
        return RestApiResponse.ok(count);
    }

    /**
     * @param appraisalApplyVo 评价申请 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询已处理评价申请
     * @Author: yujianfei
     */
    @PostMapping("/findFinish")
    @ApiOperation(value = "分页查询已处理评价申请", notes = "分页查询已处理评价申请")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findFinish')")
    public RestApiResponse<?> findFinish(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        PageInfo<AppraisalApply> appraisalApply = appraisalApplyService.findFinish(appraisalApplyVo);
        return RestApiResponse.ok(appraisalApply);
    }

    /**
     * @param appraisalApplyVo 评价申请 查询条件
     * @return RestApiResponse<?>
     * @Description: 搜索
     * @Author: yujianfei
     */
    @PostMapping("/search")
    @ApiOperation(value = "搜索", notes = "搜索")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "search')")
    public RestApiResponse<?> search(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        PageInfo<AppraisalApply> appraisalApply = appraisalApplyService.search(appraisalApplyVo);
        return RestApiResponse.ok(appraisalApply);
    }

    /**
     * @return RestApiResponse<?>
     * @Description: 是否存在下属技术机构
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/noOrgName")
    @ApiOperation(value = "是否存在下属技术机构", notes = "是否存在下属技术机构")
    @SystemLogAnnotation(type = "是否存在下属技术机构", value = "是否存在下属技术机构")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "noOrgName')")
    public RestApiResponse<?> noOrgName(@RequestBody String id) {
        appraisalApplyService.noOrgName(id);
        return RestApiResponse.ok();
    }


    /**
     * @param appraisalApplyVo 评价申请 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询考核统计表（县（市、区））
     * @Author: yujianfei
     */
    @PostMapping("/findCountyStatistic")
    @ApiOperation(value = "考核统计表（县（市、区））", notes = "考核统计表（县（市、区））")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findCountyStatistic')")
    public RestApiResponse<?> findCountyStatistic(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        PageInfo<AppraisalApplyVo> appraisalApply = appraisalApplyService.findCountyStatistic(appraisalApplyVo);
        return RestApiResponse.ok(appraisalApply);
    }

    /**
     * @param appraisalApplyVo 评价申请 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询考核统计表【市】
     * @Author: yujianfei
     */
    @PostMapping("/findCityStatistic")
    @ApiOperation(value = "分页查询考核统计表【市】", notes = "分页查询考核统计表【市】")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findCityStatistic')")
    public RestApiResponse<?> findCityStatistic(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        PageInfo<AppraisalApplyVo> appraisalApply = appraisalApplyService.findCityStatistic(appraisalApplyVo);
        return RestApiResponse.ok(appraisalApply);
    }

    /**
     * @param appraisalApplyVo void
     * @Description:导出考核统计表
     * @Author: yujianfei
     * @Date: 2021/12/02
     */
    @PostMapping("/exportApply")
    @ApiOperation(value = "导出考核统计表", notes = "导出考核统计表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "exportApply')")
    public void export(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        if (Constant.INTERGRATE_COUNTY.equals(appraisalApplyVo.getOrgType())) {
            List<AppraisalApplyCountyExportVo> list = appraisalApplyService.exportCounty(appraisalApplyVo);
            EasyExcelUtils.exportExcel(list, "地方局技术能力建设专项考核统计表（市局）", AppraisalApplyCountyExportVo.class, "地方局技术能力建设专项考核统计表（市局）.xlsx", response);
        } else if (Constant.INTERGRATE_CITY.equals(appraisalApplyVo.getOrgType())) {
            List<AppraisalApplyCityExportVo> list = appraisalApplyService.exportCity(appraisalApplyVo);
            EasyExcelUtils.exportExcel(list, "地方局技术能力建设专项考核统计表（县（市、区））", AppraisalApplyCityExportVo.class, "地方局技术能力建设专项考核统计表（县（市、区））.xlsx", response);

        }
    }


    /**
     * @return RestApiResponse<?>
     * @Description: 撤回到上一步
     * @Author: yujianfei
     * @Date: 2021/12/03
     */
    @RepeatSubAnnotation
    @PostMapping("/recall")
    @ApiOperation(value = "撤回到上一步", notes = "撤回到上一步")
    @SystemLogAnnotation(type = "撤回到上一步", value = "撤回到上一步")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "recall')")
    public RestApiResponse<?> recall(@RequestBody String id) {
        appraisalApplyService.recall(id);
        return RestApiResponse.ok();
    }


    /**
     * @Description: 考核进度
     * @Author: yujianfei
     * @Date: 2021-12-10
     */
    @PostMapping("/assessmentProgress")
    @ApiOperation(value = "考核进度", notes = "考核进度")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "assessmentProgress')")
    public RestApiResponse<?> assessmentProgress(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        PageInfo<AppraisalApplyAssessmentExportVo> appraisalApply = appraisalApplyService.assessmentProgress(appraisalApplyVo);
        return RestApiResponse.ok(appraisalApply);
    }

    /**
     * @param appraisalApplyVo void
     * @Description:导出考核进度表
     * @Author: yujianfei
     * @Date: 2021/12/10
     */
    @PostMapping("/exportAssessment")
    @ApiOperation(value = "导出考核进度", notes = "导出考核进度")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "exportAssessment')")
    public void exportAssessment(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        List<AppraisalApplyAssessmentExportVo> list = appraisalApplyService.exportAssessment(appraisalApplyVo);
        EasyExcelUtils.exportExcel(list, "考核进度", AppraisalApplyAssessmentExportVo.class, "考核进度.xlsx", response);
    }


    /**
     * @param appraisalApplyVo void
     * @Description:导出已处理的单条
     * @Author: yujianfei
     * @Date: 2021/12/13
     */
    @PostMapping("/exportOneFinish")
    @ApiOperation(value = "导出已处理的单条", notes = "导出已处理的单条")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "exportOneFinish')")
    public void exportOneFinish(@RequestBody AppraisalApplyVo appraisalApplyVo) {
        List<AppraisalApplyFinishExportVo> list = appraisalApplyService.exportOneFinish(appraisalApplyVo);

        String templateFile = "";
        String sheetName = "";
        if (Constant.INTERGRATE_COUNTY.equals(appraisalApplyVo.getOrgType())) {
            templateFile = "src/main/resources/static/template/countyAppraisal.xlsx";
            sheetName = "县（市、区）局“科技强局”战略目标考评";
        } else if (Constant.INTERGRATE_CITY.equals(appraisalApplyVo.getOrgType())) {
            templateFile ="src/main/resources/static/template/cityAppraisal.xlsx";
            sheetName = "市局“科技强局”战略目标考评";
        }
        EasyExcelUtils.exportExcelByModel(list, sheetName, AppraisalApplyFinishExportVo.class, sheetName + ".xlsx", response, templateFile);
    }

}
