package com.fd.stdp.controller.appraisal;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.appraisal.AppraisalApplyItem;
import com.fd.stdp.beans.appraisal.vo.AppraisalApplyItemVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.appraisal.AppraisalApplyItemService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 评价申请项Controller
 *
 * <AUTHOR>
 * @date 2021-11-18
 */
@RestController
@RequestMapping("/appraisal/applyItem")
@Api(value = "评价申请项", description = "评价申请项")
public class AppraisalApplyItemController extends BaseController {
    @Autowired
    private AppraisalApplyItemService appraisalApplyItemService;

    private final String PER_PREFIX = "btn:appraisal:applyItem:";

    /**
     * @param appraisalApplyItem 评价申请项数据 json
     * @return RestApiResponse<?>
     * @Description: 新增评价申请项
     * @Author: linqiang
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增评价申请项", notes = "新增评价申请项")
    @SystemLogAnnotation(type = "评价申请项", value = "新增评价申请项")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveAppraisalApplyItem(@RequestBody AppraisalApplyItem appraisalApplyItem) {
        String id = appraisalApplyItemService.saveOrUpdateAppraisalApplyItem(appraisalApplyItem);
        return RestApiResponse.ok(id);
    }

    /**
     * @param appraisalApplyItem 评价申请项数据 json
     * @return RestApiResponse<?>
     * @Description: 修改评价申请项
     * @Author: linqiang
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改评价申请项", notes = "修改评价申请项")
    @SystemLogAnnotation(type = "评价申请项", value = "修改评价申请项")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateAppraisalApplyItem(@RequestBody AppraisalApplyItem appraisalApplyItem) {
        String id = appraisalApplyItemService.saveOrUpdateAppraisalApplyItem(appraisalApplyItem);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除评价申请项(判断 关联数据是否可以删除)
     * @Author: linqiang
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除评价申请项", notes = "批量删除评价申请项")
    @SystemLogAnnotation(type = "评价申请项", value = "批量删除评价申请项")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteAppraisalApplyItem(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        appraisalApplyItemService.deleteAppraisalApplyItem(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询评价申请项详情
     * @Author: linqiang
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询评价申请项详情", notes = "查询评价申请项详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        AppraisalApplyItem appraisalApplyItem = appraisalApplyItemService.findById(id);
        return RestApiResponse.ok(appraisalApplyItem);
    }

    /**
     * @param appraisalApplyItemVo 评价申请项 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询评价申请项
     * @Author: linqiang
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询评价申请项", notes = "分页查询评价申请项")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody AppraisalApplyItemVo appraisalApplyItemVo) {
        PageInfo<AppraisalApplyItem> appraisalApplyItem = appraisalApplyItemService.findPageByQuery(appraisalApplyItemVo);
        return RestApiResponse.ok(appraisalApplyItem);
    }

}
