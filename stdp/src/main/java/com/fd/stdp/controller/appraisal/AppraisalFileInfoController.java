package com.fd.stdp.controller.appraisal;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.appraisal.AppraisalFileInfo;
import com.fd.stdp.beans.appraisal.vo.AppraisalFileInfoVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.appraisal.AppraisalFileInfoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 评价佐证材料Controller
 *
 * <AUTHOR>
 * @date 2021-11-18
 */
@RestController
@RequestMapping("/appraisal/fileInfo")
@Api(value = "评价佐证材料", description = "评价佐证材料")
public class AppraisalFileInfoController extends BaseController {
    @Autowired
    private AppraisalFileInfoService appraisalFileInfoService;

    private final String PER_PREFIX = "btn:appraisal:fileInfo:";

    /**
     * @param appraisalFileInfo 评价佐证材料数据 json
     * @return RestApiResponse<?>
     * @Description: 新增评价佐证材料
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增评价佐证材料", notes = "新增评价佐证材料")
    @SystemLogAnnotation(type = "评价佐证材料", value = "新增评价佐证材料")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveAppraisalFileInfo(@RequestBody AppraisalFileInfo appraisalFileInfo) {
        String id = appraisalFileInfoService.saveOrUpdateAppraisalFileInfo(appraisalFileInfo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param appraisalFileInfo 评价佐证材料数据 json
     * @return RestApiResponse<?>
     * @Description: 修改评价佐证材料
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改评价佐证材料", notes = "修改评价佐证材料")
    @SystemLogAnnotation(type = "评价佐证材料", value = "修改评价佐证材料")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateAppraisalFileInfo(@RequestBody AppraisalFileInfo appraisalFileInfo) {
        String id = appraisalFileInfoService.saveOrUpdateAppraisalFileInfo(appraisalFileInfo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除评价佐证材料(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除评价佐证材料", notes = "批量删除评价佐证材料")
    @SystemLogAnnotation(type = "评价佐证材料", value = "批量删除评价佐证材料")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteAppraisalFileInfo(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        appraisalFileInfoService.deleteAppraisalFileInfo(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询评价佐证材料详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询评价佐证材料详情", notes = "查询评价佐证材料详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        AppraisalFileInfo appraisalFileInfo = appraisalFileInfoService.findById(id);
        return RestApiResponse.ok(appraisalFileInfo);
    }

    /**
     * @param appraisalFileInfoVo 评价佐证材料 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询评价佐证材料
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询评价佐证材料", notes = "分页查询评价佐证材料")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody AppraisalFileInfoVo appraisalFileInfoVo) {
        PageInfo<AppraisalFileInfo> appraisalFileInfo = appraisalFileInfoService.findPageByQuery(appraisalFileInfoVo);
        return RestApiResponse.ok(appraisalFileInfo);
    }

}
