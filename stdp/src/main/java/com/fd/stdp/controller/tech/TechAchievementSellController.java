package com.fd.stdp.controller.tech;

import com.fd.stdp.beans.tech.TechAwardsAcquire;
import com.fd.stdp.beans.tech.vo.TechAchievementVo;
import com.fd.stdp.beans.tech.vo.TechAwardsAcquireVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.tech.TechAchievementSell;
import com.fd.stdp.beans.tech.vo.TechAchievementSellVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.tech.TechAchievementSellService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 表名
 *@Author: wangsh
 *@Date: 2022-01-04 13:49:55
 */
@RestController
@RequestMapping("/tech/techAchievementSell")
@Api(value="表名", description="表名")
public class TechAchievementSellController  extends BaseController {

	@Autowired
	private TechAchievementSellService techAchievementSellService;
	
	private final String PER_PREFIX = "tech:but:sell:";
	
	/**
	 *@Description: 新增表名
	 *@param techAchievementSell 表名数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增表名", notes = "新增表名")
	@SystemLogAnnotation(type = "表名",value = "新增表名")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTechAchievementSell(@RequestBody TechAchievementSellVo techAchievementSell) {
		techAchievementSell.setOrgName(getCurrentOrgName());
		techAchievementSell.setOrgCode(getCurrentScienceOrgId());
		String id = techAchievementSellService.saveOrUpdateTechAchievementSell(techAchievementSell);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改表名
	 *@param techAchievementSell 表名数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改表名", notes = "修改表名")
	@SystemLogAnnotation(type = "表名",value = "修改表名")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTechAchievementSell(@RequestBody TechAchievementSellVo techAchievementSell) {
		String id = techAchievementSellService.saveOrUpdateTechAchievementSell(techAchievementSell);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除表名(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除表名", notes = "删除表名")
	@SystemLogAnnotation(type = "表名",value = "删除表名")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTechAchievementSell(@RequestBody List<String> ids) {
		techAchievementSellService.deleteMultiTechAchievementSell(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询表名详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询表名详情", notes = "查询表名详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		TechAchievementSell  techAchievementSell=techAchievementSellService.findById(id);
		return RestApiResponse.ok(techAchievementSell);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			TechAchievementSellVo vo = new TechAchievementSellVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoTechAchievement(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<TechAchievementSell> pageInfo = (PageInfo<TechAchievementSell>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
	
	/**
	 *@Description: 分页查询表名
	 *@param techAchievementSellVo 表名 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询表名", notes = "分页查询表名")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TechAchievementSellVo techAchievementSellVo) {
		PageInfo<TechAchievementSell>  techAchievementSell=techAchievementSellService.findPageByQuery(techAchievementSellVo);
		return RestApiResponse.ok(techAchievementSell);
	}

	/**
	 *@Description: 成果转化信息上传
	 *@param file
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/upload")
	@ApiOperation(value = "导入", notes = "导入")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"upload')")
	public RestApiResponse<?> upload(@RequestParam MultipartFile file, HttpServletRequest request) {
		return RestApiResponse.ok(techAchievementSellService.doImport(file));
	}

	/**
	 *@Description: 提交成果转化
	 *@param techAchievementSellVo 成果转化数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "提交成果转化", notes = "提交成果转化")
	@SystemLogAnnotation(type = "成果转化",value = "提交成果转化")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTechAchievement(@RequestBody TechAchievementSellVo techAchievementSellVo) {
		techAchievementSellVo.setOrgName(getCurrentOrgName());
		techAchievementSellVo.setOrgCode(getCurrentScienceOrgId());
		String id = techAchievementSellService.submitTechAchievementSell(techAchievementSellVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 审核成果转化
	 *@param techAchievementSellVo 成果转化数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "审核成果转化", notes = "审核成果转化")
	@SystemLogAnnotation(type = "成果转化",value = "审核成果转化")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTechAchievement(@RequestBody TechAchievementSellVo techAchievementSellVo) {
		String id = techAchievementSellService.auditTechAchievementSell(techAchievementSellVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 退回成果转化
	 *@param techAchievementSellVo 成果转化数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "退回成果转化", notes = "退回成果转化")
	@SystemLogAnnotation(type = "成果转化",value = "退回成果转化")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTechAchievement(@RequestBody TechAchievementSellVo techAchievementSellVo) {
		String id = techAchievementSellService.sendBackTechAchievementSell(techAchievementSellVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 成果转化待办
	 *@param vo 成果转化数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/todo")
	@ApiOperation(value = "成果转化待办", notes = "成果转化待办")
	@SystemLogAnnotation(type = "成果转化",value = "成果转化待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoTechAchievement(@RequestBody TechAchievementSellVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = techAchievementSellService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = techAchievementSellService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = techAchievementSellService.endList(vo);
		} else {
			list = techAchievementSellService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 退回成果转化
	 *@param techAchievementSellVo 成果转化数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/finished")
	@ApiOperation(value = "成果转化已办", notes = "成果转化已办")
	@SystemLogAnnotation(type = "成果转化",value = "成果转化已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finishedTechAchievement(@RequestBody TechAchievementSellVo techAchievementSellVo) {
		return RestApiResponse.ok(techAchievementSellService.finishedList(techAchievementSellVo));
	}
	/**
	 *@Description: 成果转化导出
	 *@param vo 成果统计数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/export")
	@ApiOperation(value = "成果转化导出", notes = "成果转化导出")
	@SystemLogAnnotation(type = "成果转化",value = "成果转化导出")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"todo')")
	public void exportTechAchievement(@RequestBody TechAchievementSellVo vo, HttpServletResponse response) {
		techAchievementSellService.exportTechAchievement(vo, response);
	}
}
