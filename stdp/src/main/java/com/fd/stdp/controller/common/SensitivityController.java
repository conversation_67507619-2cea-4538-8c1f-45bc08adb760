package com.fd.stdp.controller.common;



import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.redis.RedisUtil;
import com.lzhpo.sensitive.cache.SensitivityPointer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/sensitivity")
@Api(value = "公共方法", tags = "公共方法")
public class SensitivityController extends BaseController {

    @Autowired
    SensitivityPointer sensitivityPointer;
    
//    @Autowired
//    RedisUtil redisUtil;

    @ApiOperation(value = "脱敏查看", notes = "脱敏查看")
    @GetMapping("/getDecipher")
    public RestApiResponse<?> getDecipher(String key) {
        Object o = sensitivityPointer.getCleartext(key);
        return RestApiResponse.ok(o);
    }
//
//    @ApiOperation(value = "脱敏查看详细信息", notes = "脱敏查看详细信息，包含调试信息")
//    @GetMapping("/getDecipherDetail")
//    public RestApiResponse<?> getDecipherDetail(String key) {
//        try {
//            // 获取脱敏数据
//            Object result = sensitivityPointer.getCleartext(key);
//
//            // 构建详细信息
//            java.util.Map<String, Object> detailInfo = new java.util.HashMap<>();
//            detailInfo.put("inputKey", key);
//            detailInfo.put("result", result);
//            detailInfo.put("isOriginalKey", key != null && key.equals(result));
//
//            // 检查Redis中的完整key
//             String redisKey = "stdp:tuomin:SENSITIVE:" + key;
//             detailInfo.put("redisKey", redisKey);
//
//             // 检查Redis中是否存在该key
//             boolean keyExists = redisUtil.hasKey(redisKey);
//             detailInfo.put("keyExistsInRedis", keyExists);
//
//             // 如果key存在，获取Redis中的原始值
//             if (keyExists) {
//                 Object redisValue = redisUtil.get(redisKey);
//                 detailInfo.put("redisValue", redisValue);
//             } else {
//                 detailInfo.put("redisValue", "Key不存在或已过期");
//             }
//
//            return RestApiResponse.ok(detailInfo);
//        } catch (Exception e) {
//            java.util.Map<String, Object> errorInfo = new java.util.HashMap<>();
//            errorInfo.put("inputKey", key);
//            errorInfo.put("error", e.getMessage());
//            errorInfo.put("errorType", e.getClass().getSimpleName());
//            return RestApiResponse.error("脱敏查看失败: " + e.getMessage(), errorInfo.size());
//         }
//     }
//
//     @ApiOperation(value = "测试脱敏功能", notes = "测试脱敏数据的存储和检索")
//     @GetMapping("/testSensitive")
//     public RestApiResponse<?> testSensitive(String testValue) {
//         try {
//             if (testValue == null || testValue.trim().isEmpty()) {
//                 testValue = "测试敏感数据_" + System.currentTimeMillis();
//             }
//
//             // 模拟脱敏过程：存储数据并返回UUID
//             String uuid = sensitivityPointer.desensitize(null, "test", testValue);
//
//             // 立即尝试检索数据
//             String retrieved = sensitivityPointer.getCleartext(uuid);
//
//             java.util.Map<String, Object> testResult = new java.util.HashMap<>();
//             testResult.put("originalValue", testValue);
//             testResult.put("generatedUuid", uuid);
//             testResult.put("retrievedValue", retrieved);
//             testResult.put("testSuccess", testValue.equals(retrieved));
//             testResult.put("redisKey", "stdp:tuomin:SENSITIVE:" + uuid);
//
//             // 检查Redis中是否真的存储了数据
//             String redisKey = "stdp:tuomin:SENSITIVE:" + uuid;
//             boolean keyExists = redisUtil.hasKey(redisKey);
//             testResult.put("keyExistsInRedis", keyExists);
//
//             if (keyExists) {
//                 Object redisValue = redisUtil.get(redisKey);
//                 testResult.put("redisValue", redisValue);
//             }
//
//             return RestApiResponse.ok(testResult);
//         } catch (Exception e) {
//             java.util.Map<String, Object> errorInfo = new java.util.HashMap<>();
//             errorInfo.put("testValue", testValue);
//             errorInfo.put("error", e.getMessage());
//             errorInfo.put("errorType", e.getClass().getSimpleName());
//             return RestApiResponse.error("脱敏功能测试失败: " + e.getMessage(), errorInfo.size());
//         }
//     }
 }
