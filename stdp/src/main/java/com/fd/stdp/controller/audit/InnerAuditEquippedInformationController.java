package com.fd.stdp.controller.audit;

import com.fd.stdp.beans.appraisal.vo.AppraisalApplyCountyExportVo;
import com.fd.stdp.beans.appraisal.vo.AppraisalApplyVo;
import com.fd.stdp.beans.audit.InnerAuditEquippedInformation;
import com.fd.stdp.beans.audit.vo.InnerAuditEquippedInformationExportVo;
import com.fd.stdp.beans.audit.vo.InnerAuditEquippedInformationVo;
import com.fd.stdp.beans.audit.vo.InnerAuditProjectInformationVo;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.constant.InnerAuditBasicConstant;
import com.fd.stdp.util.EasyExcelUtils;
import com.fd.stdp.util.ExcelUtils;
import com.fd.stdp.util.ExportUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.audit.InnerAuditEquippedInformationsService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *@Description: 浙江省内部审计总审计师配备情况表
 *@Author: sef
 *@Date: 2022-06-06 13:56:29
 */
@RestController
@RequestMapping("/audit/innerAuditEquippedInformations")
@Api(value="浙江省内部审计总审计师配备情况表", description="浙江省内部审计总审计师配备情况表")
public class InnerAuditEquippedInformationController  extends BaseController {

	@Autowired
	private InnerAuditEquippedInformationsService innerAuditEquippedInformationService;


	private final String PER_PREFIX = "btn:audit:inner:";

	/**
	 *@Description: 新增浙江省内部审计总审计师配备情况表
	 *@param innerAuditEquippedInformationVo 浙江省内部审计总审计师配备情况表数据 json
	 *@return RestApiResponse<?>
	 *@Author: sef
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增浙江省内部审计总审计师配备情况表", notes = "新增浙江省内部审计总审计师配备情况表")
	@SystemLogAnnotation(type = "浙江省内部审计总审计师配备情况表",value = "新增浙江省内部审计总审计师配备情况表")
	public RestApiResponse<?> saveInnerAuditEquippedInformation(@RequestBody InnerAuditEquippedInformationVo innerAuditEquippedInformationVo) {
		List<InnerAuditEquippedInformationVo> contactList = innerAuditEquippedInformationVo.getEquippedList();
		innerAuditEquippedInformationService.saveBatchInnerAuditWorkContacts(contactList);
		return RestApiResponse.ok();
	}

	/**
	 *@Description: 修改浙江省内部审计总审计师配备情况表
	 *@param innerAuditEquippedInformation 浙江省内部审计总审计师配备情况表数据 json
	 *@return RestApiResponse<?>
	 *@Author: sef
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改浙江省内部审计总审计师配备情况表", notes = "修改浙江省内部审计总审计师配备情况表")
	@SystemLogAnnotation(type = "浙江省内部审计总审计师配备情况表",value = "修改浙江省内部审计总审计师配备情况表")
	public RestApiResponse<?> updateInnerAuditEquippedInformation(@RequestBody InnerAuditEquippedInformationVo innerAuditEquippedInformation) {
		String id = innerAuditEquippedInformationService.saveOrUpdateInnerAuditEquippedInformation(innerAuditEquippedInformation);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 批量删除浙江省内部审计总审计师配备情况表(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除浙江省内部审计总审计师配备情况表", notes = "删除浙江省内部审计总审计师配备情况表")
	@SystemLogAnnotation(type = "浙江省内部审计总审计师配备情况表",value = "删除浙江省内部审计总审计师配备情况表")
	public RestApiResponse<?> deleteMultiInnerAuditEquippedInformation(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innerAuditEquippedInformationService.deleteMultiInnerAuditEquippedInformation(ids);
		return RestApiResponse.ok();
	}

	/**
	 *@Description: 查询浙江省内部审计总审计师配备情况表详情
	 *@param id
	 *@return RestApiResponse<?>
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询浙江省内部审计总审计师配备情况表详情", notes = "查询浙江省内部审计总审计师配备情况表详情")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InnerAuditEquippedInformation innerAuditEquippedInformation =innerAuditEquippedInformationService.findById(id);
		return RestApiResponse.ok(innerAuditEquippedInformation);
	}

	/**
	 *@Description: 分页查询浙江省内部审计总审计师配备情况表
	 *@param innerAuditEquippedInformationVo 浙江省内部审计总审计师配备情况表 查询条件
	 *@return RestApiResponse<?>
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询浙江省内部审计总审计师配备情况表", notes = "分页查询浙江省内部审计总审计师配备情况表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnerAuditEquippedInformationVo innerAuditEquippedInformationVo) {
		return RestApiResponse.ok(innerAuditEquippedInformationService.findPageByQuery(innerAuditEquippedInformationVo));
	}

	/**
	 *@Description: 浙江省内部审计总审计师配备情况表提交
	 *@param innerAuditEquippedInformationVo 浙江省内部审计总审计师配备情况表数据 json
	 *@return RestApiResponse<?>
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "浙江省内部审计总审计师配备情况表提交", notes = "浙江省内部审计总审计师配备情况表提交")
	@SystemLogAnnotation(type = "浙江省内部审计总审计师配备情况表",value = "浙江省内部审计总审计师配备情况表提交")
	public RestApiResponse<?> submitInnerAuditEquippedInformation(@RequestBody InnerAuditEquippedInformationVo innerAuditEquippedInformationVo) {
	    if(StringUtils.isBlank(innerAuditEquippedInformationVo.getOrgName())) {
            innerAuditEquippedInformationVo.setOrgName(getCurrentOrgName());
            innerAuditEquippedInformationVo.setOrgCode(getLoginUser().getAreaCode());
		}
		String id = innerAuditEquippedInformationService.submitInnerAuditEquippedInformation(innerAuditEquippedInformationVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 浙江省内部审计总审计师配备情况表审核
	 *@param innerAuditEquippedInformationVo 浙江省内部审计总审计师配备情况表数据 json
	 *@return RestApiResponse<?>
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "浙江省内部审计总审计师配备情况表审核", notes = "浙江省内部审计总审计师配备情况表审核")
	@SystemLogAnnotation(type = "浙江省内部审计总审计师配备情况表",value = "浙江省内部审计总审计师配备情况表审核")
	public RestApiResponse<?> auditInnerAuditEquippedInformation(@RequestBody InnerAuditEquippedInformationVo innerAuditEquippedInformationVo) {
		String id = innerAuditEquippedInformationService.auditInnerAuditEquippedInformation(innerAuditEquippedInformationVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 浙江省内部审计总审计师配备情况表退回
	 *@param innerAuditEquippedInformationVo 浙江省内部审计总审计师配备情况表数据 json
	 *@return RestApiResponse<?>
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "浙江省内部审计总审计师配备情况表退回", notes = "浙江省内部审计总审计师配备情况表退回")
	@SystemLogAnnotation(type = "浙江省内部审计总审计师配备情况表",value = "浙江省内部审计总审计师配备情况表退回")
	public RestApiResponse<?> sendBackInnerAuditEquippedInformation(@RequestBody InnerAuditEquippedInformationVo innerAuditEquippedInformationVo) {
		String id = innerAuditEquippedInformationService.sendBackInnerAuditEquippedInformation(innerAuditEquippedInformationVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 浙江省内部审计总审计师配备情况表任务书下达
	 *@param innerAuditEquippedInformationVo 浙江省内部审计总审计师配备情况表数据 json
	 *@return RestApiResponse<?>
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "浙江省内部审计总审计师配备情况表任务书下达", notes = "浙江省内部审计总审计师配备情况表任务书下达")
	@SystemLogAnnotation(type = "浙江省内部审计总审计师配备情况表",value = "浙江省内部审计总审计师配备情况表任务书下达")
	public RestApiResponse<?> releaseInnerAuditEquippedInformation(@RequestBody InnerAuditEquippedInformationVo innerAuditEquippedInformationVo) {
		String id = innerAuditEquippedInformationService.releaseInnerAuditEquippedInformation(innerAuditEquippedInformationVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 浙江省内部审计总审计师配备情况表待办
	 *@param innerAuditEquippedInformationVo 浙江省内部审计总审计师配备情况表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "浙江省内部审计总审计师配备情况表待办", notes = "浙江省内部审计总审计师配备情况表待办")
	public RestApiResponse<?> todoList(@RequestBody InnerAuditEquippedInformationVo innerAuditEquippedInformationVo) {
		PageInfo<InnerAuditEquippedInformation>  innerAuditEquippedInformation=innerAuditEquippedInformationService.todoList(innerAuditEquippedInformationVo);
		return RestApiResponse.ok(innerAuditEquippedInformation);
	}
	/**
	 *@Description: 浙江省内部审计总审计师配备情况表已办
	 *@param innerAuditEquippedInformationVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "浙江省内部审计总审计师配备情况表已办", notes = "浙江省内部审计总审计师配备情况表已办")
	public RestApiResponse<?> finished(@RequestBody InnerAuditEquippedInformationVo innerAuditEquippedInformationVo) {
		PageInfo<InnerAuditEquippedInformation>  innerAuditEquippedInformation=innerAuditEquippedInformationService.finishedList(innerAuditEquippedInformationVo);
		return RestApiResponse.ok(innerAuditEquippedInformation);
	}

	/**
	 *@Description: 浙江省内部审计总审计师配备情况表已完成
	 *@param innerAuditEquippedInformationVo 浙江省内部审计总审计师配备情况表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "浙江省内部审计总审计师配备情况表已完成", notes = "浙江省内部审计总审计师配备情况表已完成")
	public RestApiResponse<?> endList(@RequestBody InnerAuditEquippedInformationVo innerAuditEquippedInformationVo) {
		PageInfo<InnerAuditEquippedInformation>  innerAuditEquippedInformation=innerAuditEquippedInformationService.endList(innerAuditEquippedInformationVo);
		return RestApiResponse.ok(innerAuditEquippedInformation);
	}


	@PostMapping("/findList")
	@ApiOperation(value = "分页查询浙江省内部审计总审计师配备情况表", notes = "分页查询浙江省内部审计总审计师配备情况表")
	public RestApiResponse<?> findList(@RequestBody InnerAuditEquippedInformationVo vo) {
		return RestApiResponse.ok(innerAuditEquippedInformationService.findList(vo));
	}


	@PostMapping("/export")
	@ApiOperation(value = "导出浙江省内部审计总审计师配备情况表", notes = "导出浙江省内部审计总审计师配备情况表")
	public void export(@RequestBody InnerAuditEquippedInformationVo vo) throws IOException {
		List list = new ArrayList<>();

		String roleCode = getUserRoleList().get(0).getRoleCode();
		if ("AUDIT_PROVINCE".equals(roleCode)) {
			list = innerAuditEquippedInformationService.exportInnerAuditEquippedInformationAllUnit(vo);
		} else if ("AUDIT_PROVINCE_DIRECT".equals(roleCode)){
			list = innerAuditEquippedInformationService.exportInnerAuditEquippedInformation(vo);
		}

		// 文件名
		String prefix = "/word/";
		InputStream inputStream = ExportUtil.getInputStream(prefix + "总审计师配备情况导出模板.xlsx");

		EasyExcelUtils.exportTemplateFill(null, list, "Sheet1", "浙江省内部审计总审计师配备情况表.xlsx", response, inputStream, true);
	}
}
