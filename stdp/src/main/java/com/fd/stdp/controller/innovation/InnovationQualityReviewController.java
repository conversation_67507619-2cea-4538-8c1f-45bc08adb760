package com.fd.stdp.controller.innovation;

import com.fd.stdp.beans.innovation.InnovationQualityReport;
import com.fd.stdp.enums.QueryTypeEnum;
import com.fd.stdp.service.innovation.InnovationQualityContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityReview;
import com.fd.stdp.beans.innovation.vo.InnovationQualityReviewVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.innovation.InnovationQualityReviewService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 省质检中心年度总结
 *@Author: wangsh
 *@Date: 2022-03-04 09:23:43
 */
@RestController
@RequestMapping("/innovation/innovationQualityReview")
@Api(value="省质检中心年度总结", description="省质检中心年度总结")
public class InnovationQualityReviewController  extends BaseController {

	@Autowired
	private InnovationQualityReviewService innovationQualityReviewService;
	@Autowired
	private InnovationQualityContractService innovationQualityContractService;
	
	private final String PER_PREFIX = "but:quality:summary:";
	
	/**
	 *@Description: 新增省质检中心年度总结
	 *@param innovationQualityReviewVo 省质检中心年度总结数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增省质检中心年度总结", notes = "新增省质检中心年度总结")
	@SystemLogAnnotation(type = "省质检中心年度总结",value = "新增省质检中心年度总结")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInnovationQualityReview(@RequestBody InnovationQualityReviewVo innovationQualityReviewVo) {
	    if(StringUtils.isBlank(innovationQualityReviewVo.getOrgName())) {
            innovationQualityReviewVo.setOrgName(getCurrentOrgName());
            innovationQualityReviewVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityReviewService.saveOrUpdateInnovationQualityReview(innovationQualityReviewVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改省质检中心年度总结
	 *@param innovationQualityReview 省质检中心年度总结数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改省质检中心年度总结", notes = "修改省质检中心年度总结")
	@SystemLogAnnotation(type = "省质检中心年度总结",value = "修改省质检中心年度总结")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInnovationQualityReview(@RequestBody InnovationQualityReviewVo innovationQualityReview) {
		String id = innovationQualityReviewService.saveOrUpdateInnovationQualityReview(innovationQualityReview);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除省质检中心年度总结(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除省质检中心年度总结", notes = "删除省质检中心年度总结")
	@SystemLogAnnotation(type = "省质检中心年度总结",value = "删除省质检中心年度总结")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteInnovationQualityReview(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		innovationQualityReviewService.deleteInnovationQualityReview(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除省质检中心年度总结(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除省质检中心年度总结", notes = "删除省质检中心年度总结")
	@SystemLogAnnotation(type = "省质检中心年度总结",value = "删除省质检中心年度总结")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiInnovationQualityReview(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innovationQualityReviewService.deleteMultiInnovationQualityReview(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询省质检中心年度总结详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询省质检中心年度总结详情", notes = "查询省质检中心年度总结详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InnovationQualityReview  innovationQualityReview=innovationQualityReviewService.findById(id);
		return RestApiResponse.ok(innovationQualityReview);
	}

	/**
	 *@Description: 查询省质检中心任务书详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findContractById")
	@ApiOperation(value = "查询省质检中心变更详情", notes = "查询省质检中心变更详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findContractById(@RequestParam("id") String id) {
		InnovationQualityReview innovationQualityReview=innovationQualityReviewService.findById(id);
		return RestApiResponse.ok(innovationQualityContractService.findById(innovationQualityReview.getContractId()));
	}

	/**
	 *@Description: 分页查询省质检中心年度总结
	 *@param innovationQualityReviewVo 省质检中心年度总结 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询省质检中心年度总结", notes = "分页查询省质检中心年度总结")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnovationQualityReviewVo innovationQualityReviewVo) {
		PageInfo<InnovationQualityReview>  innovationQualityReview=innovationQualityReviewService.findPageByQuery(innovationQualityReviewVo);
		return RestApiResponse.ok(innovationQualityReview);
	}

	/**
	 *@Description: 省质检中心年度总结提交
	 *@param innovationQualityReviewVo 省质检中心年度总结数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "省质检中心年度总结提交", notes = "省质检中心年度总结提交")
	@SystemLogAnnotation(type = "省质检中心年度总结",value = "省质检中心年度总结提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitInnovationQualityReview(@RequestBody InnovationQualityReviewVo innovationQualityReviewVo) {
	    if(StringUtils.isBlank(innovationQualityReviewVo.getOrgName())) {
            innovationQualityReviewVo.setOrgName(getCurrentOrgName());
            innovationQualityReviewVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityReviewService.submitInnovationQualityReview(innovationQualityReviewVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 省质检中心年度总结审核
	 *@param innovationQualityReviewVo 省质检中心年度总结数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "省质检中心年度总结审核", notes = "省质检中心年度总结审核")
	@SystemLogAnnotation(type = "省质检中心年度总结",value = "省质检中心年度总结审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditInnovationQualityReview(@RequestBody InnovationQualityReviewVo innovationQualityReviewVo) {
		String id = innovationQualityReviewService.auditInnovationQualityReview(innovationQualityReviewVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心年度总结退回
	 *@param innovationQualityReviewVo 省质检中心年度总结数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "省质检中心年度总结退回", notes = "省质检中心年度总结退回")
	@SystemLogAnnotation(type = "省质检中心年度总结",value = "省质检中心年度总结退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackInnovationQualityReview(@RequestBody InnovationQualityReviewVo innovationQualityReviewVo) {
		String id = innovationQualityReviewService.sendBackInnovationQualityReview(innovationQualityReviewVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心年度总结任务书下达
	 *@param innovationQualityReviewVo 省质检中心年度总结数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "省质检中心年度总结任务书下达", notes = "省质检中心年度总结任务书下达")
	@SystemLogAnnotation(type = "省质检中心年度总结",value = "省质检中心年度总结任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseInnovationQualityReview(@RequestBody InnovationQualityReviewVo innovationQualityReviewVo) {
		String id = innovationQualityReviewService.releaseInnovationQualityReview(innovationQualityReviewVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心年度总结待办
	 *@param vo 省质检中心年度总结 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "省质检中心年度总结待办", notes = "省质检中心年度总结待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody InnovationQualityReviewVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = innovationQualityReviewService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = innovationQualityReviewService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = innovationQualityReviewService.endList(vo);
		} else {
			list = innovationQualityReviewService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 省质检中心年度总结已办
	 *@param innovationQualityReviewVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "省质检中心年度总结已办", notes = "省质检中心年度总结已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody InnovationQualityReviewVo innovationQualityReviewVo) {
		PageInfo<InnovationQualityReview>  innovationQualityReview=innovationQualityReviewService.finishedList(innovationQualityReviewVo);
		return RestApiResponse.ok(innovationQualityReview);
	}
	
	/**
	 *@Description: 省质检中心年度总结已完成
	 *@param innovationQualityReviewVo 省质检中心年度总结 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "省质检中心年度总结已完成", notes = "省质检中心年度总结已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody InnovationQualityReviewVo innovationQualityReviewVo) {
		PageInfo<InnovationQualityReview>  innovationQualityReview=innovationQualityReviewService.endList(innovationQualityReviewVo);
		return RestApiResponse.ok(innovationQualityReview);
	}
}
