package com.fd.stdp.controller.sys.rest;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.ServiceException;
import com.fd.stdp.beans.sys.*;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.beans.sys.vo.Token;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.redis.RedisUtil;
import com.fd.stdp.constant.RedisConstant;
import com.fd.stdp.dao.sys.SysUserMapper;
import com.fd.stdp.service.sys.SysUserService;
import com.fd.stdp.service.sys.TokenService;
import com.fd.stdp.util.UUIDUtils;
import io.swagger.annotations.Api;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/rest/point")
@Api(value = "单点登录", description = "单点登录")
public class RestOssLoginController {

    public static final Logger logger = LoggerFactory.getLogger(RestOssLoginController.class);

    @Value("${interface.integratedPlatform.OSSLogin.appName}")
    private String appName;
    @Value("${interface.integratedPlatform.OSSLogin.appNameUnit}")
    private String appNameUnit;
    @Value("${interface.integratedPlatform.OSSLogin.getUserInfoUrl}")
    private String platformOssLoginUrl;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SysUserMapper sysUserMapper;

    @GetMapping("/ythLogin")
    public RestApiResponse ythLogin(@RequestParam("est") String est) {
        if (StringUtils.isBlank(est)) {
            return RestApiResponse.ok();
        }
        try {
            // 调用老胡的接口消费est并获得用户信息
            OssUser ossUser = ythOssGetLonginUser(est,appNameUnit);
            logger.info("登录反馈：结果-{},描述-{},手机号-{}，中台ID-{}",ossUser.getSuccess(),ossUser.getData(),ossUser.getMobile(),ossUser.getUserId());
            if (true || "true".equals(ossUser.getSuccess())) {
                // 按手机号查询库中的用户
                List<SysUser> sysUsers = sysUserService.getUserListByPhone(ossUser.getMobile());
                logger.info("getUserByPhone" + ossUser.getMobile());
                // 没有匹配的用户则刷新至登录处理页面
                if (CollectionUtils.isEmpty(sysUsers)) {
                    throw new ServiceException("该账号未关联用户 请联系管理员");
                }
                List<SysUser> resUsers = new ArrayList<>();
                sysUsers.forEach(user->{
                    SysUser u = new SysUser();
                    u.setId(user.getId());
                    u.setUsername(user.getUsername());
                    u.setNickname(user.getNickname());
                    resUsers.add(u);
                });
                String guid = UUIDUtils.getUUID();
                redisUtil.set(RedisConstant.SSOTICKENT + ":" + guid, guid,
                        RedisConstant.REDIS_EXPIRE_TEN_MIN);
                OssResponse ossResponse = new OssResponse();
                ossResponse.setRandom(guid);
                ossResponse.setUserList(resUsers);
                return RestApiResponse.ok(ossResponse);
            }
        } catch (Exception e) {
            // 登录失败
            logger.error("一体化登录失败", e);
        }
        throw new ServiceException("登录失败");
    }

    @GetMapping("/ythLoginForUser")
    public RestApiResponse ythLoginForUser(@RequestParam("random") String random, @RequestParam("id") String id) {
        if (StringUtils.isBlank(random) || StringUtils.isBlank(id)) {
            return RestApiResponse.error("参数异常");
        }
        try {
            Object guid = redisUtil.get(RedisConstant.SSOTICKENT + ":" + random);
            if(guid==null || !guid.toString().equals(random)) {
                return RestApiResponse.error("认证已过期，请重新登陆");
            }
            // 用户登录
            SysUser sysUser = sysUserMapper.selectByPrimaryKey(id);
            LoginUser loginUser = new LoginUser();
            BeanUtils.copyProperties(sysUser, loginUser);
            Set<String> permissions = sysUserService.listElementByUserId(sysUser.getId());
            List<SysRole> sysRoles = sysUserService.listRolesByUserId(sysUser.getId());
            loginUser.setPermissions(permissions);
            loginUser.setSysRoles(sysRoles);
            // 设置token
            loginUser.setPassword(null);
            Token token = tokenService.saveToken(loginUser);
            loginUser.setToken(token.getToken());
            // 设置登录状态
            SysUser user = new SysUser();
            user.setId(loginUser.getId());
            user.setLastLogin(new Date());
            sysUserMapper.updateByPrimaryKeySelective(user);
            return RestApiResponse.ok(token.getToken());
        } catch (Exception e) {
            // 登录失败
            logger.error("一体化登录失败", e);
        }
        throw new ServiceException("登录失败");
    }

    /**
     *
     * @param est 一体化的令牌
     * @param appName 一体化那边分配的app-name
     * @return 登录结果
     */
    private OssUser ythOssGetLonginUser(String est,String appName) {
        OssParam param = new OssParam();
        param.setEst(est);
        param.setAppName(appName);
        RestTemplate rest = new RestTemplate();
        OssUser ossUser = rest.postForObject(platformOssLoginUrl, param, OssUser.class);
        logger.info("调用一体化平台接口返回结果{}", ossUser);
        redisUtil.set(est, JSON.toJSON(ossUser), 3600);// 把查询的平台用户信息存道redis
        return ossUser;
    }

}
