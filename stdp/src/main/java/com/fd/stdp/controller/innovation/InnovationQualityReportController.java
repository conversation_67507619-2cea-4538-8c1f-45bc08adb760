package com.fd.stdp.controller.innovation;

import com.fd.stdp.beans.innovation.InnovationQualityInterim;
import com.fd.stdp.enums.QueryTypeEnum;
import com.fd.stdp.service.innovation.InnovationQualityContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityReport;
import com.fd.stdp.beans.innovation.vo.InnovationQualityReportVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.innovation.InnovationQualityReportService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 省质检中心事项报告
 *@Author: wangsh
 *@Date: 2022-03-04 09:23:48
 */
@RestController
@RequestMapping("/innovation/innovationQualityReport")
@Api(value="省质检中心事项报告", description="省质检中心事项报告")
public class InnovationQualityReportController  extends BaseController {

	@Autowired
	private InnovationQualityReportService innovationQualityReportService;
	@Autowired
	private InnovationQualityContractService innovationQualityContractService;
	
	private final String PER_PREFIX = "but:quality:report:";
	
	/**
	 *@Description: 新增省质检中心事项报告
	 *@param innovationQualityReportVo 省质检中心事项报告数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增省质检中心事项报告", notes = "新增省质检中心事项报告")
	@SystemLogAnnotation(type = "省质检中心事项报告",value = "新增省质检中心事项报告")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInnovationQualityReport(@RequestBody InnovationQualityReportVo innovationQualityReportVo) {
	    if(StringUtils.isBlank(innovationQualityReportVo.getOrgName())) {
            innovationQualityReportVo.setOrgName(getCurrentOrgName());
            innovationQualityReportVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityReportService.saveOrUpdateInnovationQualityReport(innovationQualityReportVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改省质检中心事项报告
	 *@param innovationQualityReport 省质检中心事项报告数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改省质检中心事项报告", notes = "修改省质检中心事项报告")
	@SystemLogAnnotation(type = "省质检中心事项报告",value = "修改省质检中心事项报告")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInnovationQualityReport(@RequestBody InnovationQualityReportVo innovationQualityReport) {
		String id = innovationQualityReportService.saveOrUpdateInnovationQualityReport(innovationQualityReport);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除省质检中心事项报告(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除省质检中心事项报告", notes = "删除省质检中心事项报告")
	@SystemLogAnnotation(type = "省质检中心事项报告",value = "删除省质检中心事项报告")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteInnovationQualityReport(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		innovationQualityReportService.deleteInnovationQualityReport(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除省质检中心事项报告(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除省质检中心事项报告", notes = "删除省质检中心事项报告")
	@SystemLogAnnotation(type = "省质检中心事项报告",value = "删除省质检中心事项报告")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiInnovationQualityReport(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innovationQualityReportService.deleteMultiInnovationQualityReport(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询省质检中心事项报告详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询省质检中心事项报告详情", notes = "查询省质检中心事项报告详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InnovationQualityReport  innovationQualityReport=innovationQualityReportService.findById(id);
		return RestApiResponse.ok(innovationQualityReport);
	}

	/**
	 *@Description: 查询省质检中心任务书详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findContractById")
	@ApiOperation(value = "查询省质检中心变更详情", notes = "查询省质检中心变更详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findContractById(@RequestParam("id") String id) {
		InnovationQualityReport innovationQualityReport=innovationQualityReportService.findById(id);
		return RestApiResponse.ok(innovationQualityContractService.findById(innovationQualityReport.getContractId()));
	}
	
	/**
	 *@Description: 分页查询省质检中心事项报告
	 *@param innovationQualityReportVo 省质检中心事项报告 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询省质检中心事项报告", notes = "分页查询省质检中心事项报告")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnovationQualityReportVo innovationQualityReportVo) {
		PageInfo<InnovationQualityReport>  innovationQualityReport=innovationQualityReportService.findPageByQuery(innovationQualityReportVo);
		return RestApiResponse.ok(innovationQualityReport);
	}

	/**
	 *@Description: 省质检中心事项报告提交
	 *@param innovationQualityReportVo 省质检中心事项报告数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "省质检中心事项报告提交", notes = "省质检中心事项报告提交")
	@SystemLogAnnotation(type = "省质检中心事项报告",value = "省质检中心事项报告提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitInnovationQualityReport(@RequestBody InnovationQualityReportVo innovationQualityReportVo) {
	    if(StringUtils.isBlank(innovationQualityReportVo.getOrgName())) {
            innovationQualityReportVo.setOrgName(getCurrentOrgName());
            innovationQualityReportVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityReportService.submitInnovationQualityReport(innovationQualityReportVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 省质检中心事项报告审核
	 *@param innovationQualityReportVo 省质检中心事项报告数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "省质检中心事项报告审核", notes = "省质检中心事项报告审核")
	@SystemLogAnnotation(type = "省质检中心事项报告",value = "省质检中心事项报告审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditInnovationQualityReport(@RequestBody InnovationQualityReportVo innovationQualityReportVo) {
		String id = innovationQualityReportService.auditInnovationQualityReport(innovationQualityReportVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心事项报告退回
	 *@param innovationQualityReportVo 省质检中心事项报告数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "省质检中心事项报告退回", notes = "省质检中心事项报告退回")
	@SystemLogAnnotation(type = "省质检中心事项报告",value = "省质检中心事项报告退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackInnovationQualityReport(@RequestBody InnovationQualityReportVo innovationQualityReportVo) {
		String id = innovationQualityReportService.sendBackInnovationQualityReport(innovationQualityReportVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心事项报告任务书下达
	 *@param innovationQualityReportVo 省质检中心事项报告数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "省质检中心事项报告任务书下达", notes = "省质检中心事项报告任务书下达")
	@SystemLogAnnotation(type = "省质检中心事项报告",value = "省质检中心事项报告任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseInnovationQualityReport(@RequestBody InnovationQualityReportVo innovationQualityReportVo) {
		String id = innovationQualityReportService.releaseInnovationQualityReport(innovationQualityReportVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心事项报告待办
	 *@param vo 省质检中心事项报告 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "省质检中心事项报告待办", notes = "省质检中心事项报告待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody InnovationQualityReportVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = innovationQualityReportService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = innovationQualityReportService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = innovationQualityReportService.endList(vo);
		} else {
			list = innovationQualityReportService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 省质检中心事项报告已办
	 *@param innovationQualityReportVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "省质检中心事项报告已办", notes = "省质检中心事项报告已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody InnovationQualityReportVo innovationQualityReportVo) {
		PageInfo<InnovationQualityReport>  innovationQualityReport=innovationQualityReportService.finishedList(innovationQualityReportVo);
		return RestApiResponse.ok(innovationQualityReport);
	}
	
	/**
	 *@Description: 省质检中心事项报告已完成
	 *@param innovationQualityReportVo 省质检中心事项报告 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "省质检中心事项报告已完成", notes = "省质检中心事项报告已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody InnovationQualityReportVo innovationQualityReportVo) {
		PageInfo<InnovationQualityReport>  innovationQualityReport=innovationQualityReportService.endList(innovationQualityReportVo);
		return RestApiResponse.ok(innovationQualityReport);
	}
}
