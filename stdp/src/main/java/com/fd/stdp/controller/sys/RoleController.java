package com.fd.stdp.controller.sys;

import com.fd.stdp.beans.sys.SysRole;
import com.fd.stdp.beans.sys.vo.*;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.service.sys.RoleService;
import com.fd.stdp.util.EasyExcelUtils;
import com.fd.stdp.util.TreeUtil;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * 角色controller
 *
 * <AUTHOR>
 * @date 2018/03/05
 */
@Controller
@RequestMapping("/sys/role")
@Api(value = "角色管理", description = "角色接口")
public class RoleController extends BaseController {

    @Autowired
    private RoleService roleService;

    private final String prefix = "btn:sys:role:";

    /**
     * 查询角色列表
     *
     * @param roleName
     * @return
     */
    @GetMapping(value = "/list")
    @ResponseBody
    @ApiOperation(value = "查询角色", notes = "角色名称模糊查询角色")
    public RestApiResponse<?> listRole(String roleName) {
        Example example = new Example(SysRole.class);
        if (StringUtils.isNotBlank(roleName)) {
            example.createCriteria().andLike("roleName", "%" + roleName + "%");
        }
        List<SysRole> listRole = roleService.selectByExample(example);
        return RestApiResponse.ok(listRole);
    }

    /**
     * 批量删除角色
     *
     * @param roleIds
     * @return
     */
    @RepeatSubAnnotation
    @GetMapping(value = "/deleteRole/{roleIds}")
    @ApiOperation(value = "批量删除角色", notes = "批量删除角色")
    @ResponseBody
    @PreAuthorize("hasAuthority('" + prefix + "delete')")
    public RestApiResponse<?> deleteRole(@PathVariable("roleIds") String roleIds) {
        if (StringUtils.isEmpty(roleIds)) {
            throw new ServiceException("参数异常");
        }

        roleService.deleteRole(roleIds);
        return RestApiResponse.ok();
    }

    /**
     * 查询角色列表
     *
     * @param roleName
     * @return
     */
    @PostMapping(value = "/listPage")
    @ResponseBody
    @ApiOperation(value = "查询角色(分页)", notes = "角色名称模糊查询角色(分页)")
    @PreAuthorize("hasAuthority('" + prefix + "query')")
    public RestApiResponse<?> listRolePage(@RequestBody SysRoleVo sysRoleVo) {
        sysRoleVo.setAdmin(this.isAdmin());
        PageInfo<RoleAndRoleTypesVO> roleTypeVo = this.roleService.listPageRoleAndRoleTypeVo(sysRoleVo);
        return RestApiResponse.ok(roleTypeVo);
    }

    /**
     * 查询当前用户添加的角色列表
     *
     * @param roleName
     * @return
     */
    @PostMapping(value = "/listRoleByLoginUser")
    @ResponseBody
    @ApiOperation(value = "查询当前用户添加的角色列表", notes = "查询当前用户添加的角色列表")
//	@PreAuthorize("hasAuthority('" + prefix + "queryByUserId')")
    public RestApiResponse<?> listRoleByLoginUser() {
        SysRoleVo sysRoleVo = new SysRoleVo();
        sysRoleVo.setAdmin(this.isAdmin());
        List<SysRole> roleList = this.roleService.listRoleByLoginUser(sysRoleVo);
        return RestApiResponse.ok(roleList);
    }

    /**
     * 根据角色编码查询角色
     *
     * @param roleCode
     * @return
     */
    @GetMapping(value = "/findByCode")
    @ResponseBody
    @ApiOperation(value = "根据角色编码查询角色", notes = "根据角色编码查询角色")
    public RestApiResponse<?> findByCode(String roleCode) {
        SysRole querySysRole = new SysRole();
        querySysRole.setRoleCode(roleCode);
        SysRole sysRole = this.roleService.selectOne(querySysRole);
        return RestApiResponse.ok(sysRole);
    }

    /**
     * 根据角色ID查询角色
     *
     * @param roleCode
     * @return
     */
    @GetMapping(value = "/findById/{id}")
    @ResponseBody
    @ApiOperation(value = "根据角色ID查询角色", notes = "根据角色ID查询角色")
    @PreAuthorize("hasAuthority('" + prefix + "byid')")
    public RestApiResponse<?> findById(@PathVariable("id") String id) {
    	SysRoleVo sysRole = roleService.findRoleById(id);
        return RestApiResponse.ok(sysRole);
    }

    @RepeatSubAnnotation
    @PostMapping(value = "/addRole")
    @ApiOperation(value = "保存角色", notes = "保存角色")
    @SystemLogAnnotation(type = "保存角色", value = "保存角色")
    @PreAuthorize("hasAuthority('" + prefix + "add')")
    public @ResponseBody
    RestApiResponse<SysRole> addRole(@RequestBody SysRoleVo sysRole) throws Exception {
        return RestApiResponse.ok(this.roleService.saveOrUpdateRole(sysRole));
    }

    @RepeatSubAnnotation
    @PostMapping(value = "/updateRole")
    @ApiOperation(value = "更新角色", notes = "更新角色")
    @SystemLogAnnotation(type = "更新角色", value = "更新角色")
    @PreAuthorize("hasAuthority('" + prefix + "edit')")
    public @ResponseBody
    RestApiResponse<SysRole> updateRole(@RequestBody SysRoleVo sysRole) throws Exception {
        return RestApiResponse.ok(this.roleService.saveOrUpdateRole(sysRole));
    }

    @RepeatSubAnnotation
    @GetMapping(value = "/enable/{roleIds}/{status}")
    @ApiOperation(value = "改变角色状态-启用", notes = "改变角色状态-启用")
    @PreAuthorize("hasAuthority('" + prefix + "enable')")
    public @ResponseBody
    RestApiResponse<?> enable(@PathVariable("roleIds") String roleIds,
                              @PathVariable("status") Integer status) {
        this.roleService.changeStatus(roleIds, status);
        return RestApiResponse.ok();

    }

    @RepeatSubAnnotation
    @GetMapping(value = "/disable/{roleIds}/{status}")
    @ApiOperation(value = "改变角色状态-禁用", notes = "改变角色状态-禁用")
    @PreAuthorize("hasAuthority('" + prefix + "disable')")
    public @ResponseBody
    RestApiResponse<?> disable(@PathVariable("roleIds") String roleIds,
                               @PathVariable("status") Integer status) {
        this.roleService.changeStatus(roleIds, status);
        return RestApiResponse.ok();

    }

    /**
     * @return RestApiResponse<SysRole>
     * @Description: 角色(附带用户)列表
     * @Author: linqiang
     */
    @GetMapping(value = "/roleUserPage")
    @ApiOperation(value = "分页查询角色(附带用户)", notes = "分页查询角色(附带用户)")
    public @ResponseBody
    RestApiResponse<?> roleUserPage(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize, String userName) {
        PageInfo<RoleUserVO> result = roleService.roleUserPage(pageNum, pageSize, userName);
        return RestApiResponse.ok(result);
    }

    /**
     * 获取组织架构
     *
     * @return
     */
    @RequestMapping(value = "/tree", method = RequestMethod.GET)
    @ResponseBody
    public List<OrganizationalTree> getOrgTree() {
        // 获取组织架构
        List<OrganizationalTree> orgMenus = roleService.getOrgTree();
        // 生成树
        String root = CommonConstant.ROOT;
        if (StringUtils.isEmpty(root)) {
            root = "-1";
        }
        return TreeUtil.bulid(orgMenus, root);
    }

    /**
     * 导出
     *
     * @param expertList
     */
    @PostMapping(value = "/export")
    @ApiOperation(value = "导出角色", notes = "导出角色")
    @PreAuthorize("hasAuthority('" + prefix + "export')")
    public void export(SysRoleVo sysRoleVo) {
        sysRoleVo.setAdmin(this.isAdmin());
        List<SysRoleExportVo> roleList = roleService.export(sysRoleVo);
        EasyExcelUtils.exportExcel(roleList, "导出角色结果", SysRoleExportVo.class, "导出角色结果.xlsx", response);
    }

}
