package com.fd.stdp.controller.talent;

import com.fd.stdp.beans.talent.TalentTeamContributeChange;
import com.fd.stdp.beans.talent.vo.TalentTeamContributeChangeVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTechPioneersApply;
import com.fd.stdp.beans.talent.vo.TalentTechPioneersApplyVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.talent.TalentTechPioneersApplyService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 科技尖兵申请书
 *@Author: wangsh
 *@Date: 2022-01-11 10:48:36
 */
@RestController
@RequestMapping("/talent/talentTechPioneersApply")
@Api(value="科技尖兵申请书", description="科技尖兵申请书")
public class TalentTechPioneersApplyController  extends BaseController {

	@Autowired
	private TalentTechPioneersApplyService talentTechPioneersApplyService;
	
	private final String PER_PREFIX = "talent:but:pioneer:";
	
	/**
	 *@Description: 新增科技尖兵申请书
	 *@param vo 科技尖兵申请书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增科技尖兵申请书", notes = "新增科技尖兵申请书")
	@SystemLogAnnotation(type = "科技尖兵申请书",value = "新增科技尖兵申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTalentTechPioneersApply(@RequestBody TalentTechPioneersApplyVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentTechPioneersApplyService.saveOrUpdateTalentTechPioneersApply(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改科技尖兵申请书
	 *@param vo 科技尖兵申请书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改科技尖兵申请书", notes = "修改科技尖兵申请书")
	@SystemLogAnnotation(type = "科技尖兵申请书",value = "修改科技尖兵申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTalentTechPioneersApply(@RequestBody TalentTechPioneersApplyVo vo) {
		String id = talentTechPioneersApplyService.saveOrUpdateTalentTechPioneersApply(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除科技尖兵申请书(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除科技尖兵申请书", notes = "删除科技尖兵申请书")
	@SystemLogAnnotation(type = "科技尖兵申请书",value = "删除科技尖兵申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTalentTechPioneersApply(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		talentTechPioneersApplyService.deleteTalentTechPioneersApply(id);
		return RestApiResponse.ok();
	}

	/**
	 *@Description: 批量删除科技尖兵申请书(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除科技尖兵申请书", notes = "删除科技尖兵申请书")
	@SystemLogAnnotation(type = "科技尖兵申请书",value = "删除科技尖兵申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiTalentTechPioneersApply(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		talentTechPioneersApplyService.deleteMultiTalentTechPioneersApply(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询科技尖兵申请书详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询科技尖兵申请书详情", notes = "查询科技尖兵申请书详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		TalentTechPioneersApply  talentTechPioneersApply=talentTechPioneersApplyService.findById(id);
		return RestApiResponse.ok(talentTechPioneersApply);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			TalentTechPioneersApplyVo vo = new TalentTechPioneersApplyVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<TalentTechPioneersApply> pageInfo = (PageInfo<TalentTechPioneersApply>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
	
	/**
	 *@Description: 分页查询科技尖兵申请书
	 *@param talentTechPioneersApplyVo 科技尖兵申请书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询科技尖兵申请书", notes = "分页查询科技尖兵申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TalentTechPioneersApplyVo talentTechPioneersApplyVo) {
		PageInfo<TalentTechPioneersApply>  talentTechPioneersApply=talentTechPioneersApplyService.findPageByQuery(talentTechPioneersApplyVo);
		return RestApiResponse.ok(talentTechPioneersApply);
	}


	/**
	 *@Description: 提交科技尖兵申请书
	 *@param vo 科技尖兵申请书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "提交科技尖兵申请书", notes = "提交科技尖兵申请书")
	@SystemLogAnnotation(type = "科技尖兵申请书",value = "提交科技尖兵申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTalentTechPioneersApply(@RequestBody TalentTechPioneersApplyVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentTechPioneersApplyService.submitTalentTechPioneersApply(vo);
		return RestApiResponse.ok(id);
	}


	/**
	 *@Description: 提交科技尖兵申请书
	 *@param talentTechPioneersApply 科技尖兵申请书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "审核科技尖兵申请书", notes = "审核科技尖兵申请书")
	@SystemLogAnnotation(type = "科技尖兵申请书",value = "审核科技尖兵申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTalentTechPioneersApply(@RequestBody TalentTechPioneersApplyVo talentTechPioneersApply) {
		String id = talentTechPioneersApplyService.auditTalentTechPioneersApply(talentTechPioneersApply);
		return RestApiResponse.ok(id);
	}


	/**
	 *@Description: 提交科技尖兵申请书
	 *@param talentTechPioneersApply 科技尖兵申请书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "退回科技尖兵申请书", notes = "退回科技尖兵申请书")
	@SystemLogAnnotation(type = "科技尖兵申请书",value = "退回科技尖兵申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTalentTechPioneersApply(@RequestBody TalentTechPioneersApplyVo talentTechPioneersApply) {
		String id = talentTechPioneersApplyService.sendBackTalentTechPioneersApply(talentTechPioneersApply);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 科技尖兵申请书待办
	 *@param vo 科技尖兵申请书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "科技尖兵申请书待办", notes = "科技尖兵申请书待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody TalentTechPioneersApplyVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = talentTechPioneersApplyService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = talentTechPioneersApplyService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = talentTechPioneersApplyService.endList(vo);
		} else {
			list = talentTechPioneersApplyService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}

	/**
	 *@Description: 科技尖兵申请书已办
	 *@param talentTechPioneersApplyVo 科技尖兵申请书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "科技尖兵申请书已办", notes = "科技尖兵申请书已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finishedList(@RequestBody TalentTechPioneersApplyVo talentTechPioneersApplyVo) {
		PageInfo<TalentTechPioneersApply>  talentTechPioneersApply=talentTechPioneersApplyService.finishedList(talentTechPioneersApplyVo);
		return RestApiResponse.ok(talentTechPioneersApply);
	}


	/**
	 *@Description: 科技尖兵申请书已完成
	 *@param talentTechPioneersApplyVo 科技尖兵申请书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "科技尖兵申请书已完成", notes = "科技尖兵申请书已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody TalentTechPioneersApplyVo talentTechPioneersApplyVo) {
		PageInfo<TalentTechPioneersApply>  talentTechPioneersApply=talentTechPioneersApplyService.endList(talentTechPioneersApplyVo);
		return RestApiResponse.ok(talentTechPioneersApply);
	}
}
