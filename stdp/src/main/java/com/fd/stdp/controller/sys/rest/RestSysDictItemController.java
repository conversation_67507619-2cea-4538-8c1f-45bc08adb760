package com.fd.stdp.controller.sys.rest;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fd.stdp.beans.sys.vo.TreeDropVo;
import com.fd.stdp.beans.sys.SysDictItem;
import com.fd.stdp.beans.sys.vo.DictTree;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.service.sys.SysDictItemService;
import com.fd.stdp.util.TreeUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/sys/dict")
@Api(value = "字典REST", description = "字典RESTAPI")
public class RestSysDictItemController extends BaseController {

	@Autowired
	private SysDictItemService basicDictItemService;

	/**
	 * @Description 初始化字典数
	 * @param name
	 * @return List<DictTree>
	 */
	@RequestMapping(value = "/tree", method = RequestMethod.GET)
	@ApiOperation(value = "字典管理", notes = "查询属性字典")
	@ResponseBody
	public List<DictTree> getTree() {
		SysDictItem dictItem = new SysDictItem();
		// 获取字典树
		List<SysDictItem> dictItems = basicDictItemService.listItem(dictItem, isAdmin());
		// 生成树
		List<DictTree> dictTree = getDictTree(dictItems, CommonConstant.ROOT);
		return dictTree;
	}
	
	/**
	 * @Description 字典树 treeDrop
	 * @param name
	 * @return List<DictTree>
	 */
	@RequestMapping(value = "/getTreeDrop", method = RequestMethod.GET)
	@ApiOperation(value = "字典树 treeDrop", notes = "字典树 treeDrop")
	@ResponseBody
	public List<TreeDropVo> getTreeDrop(@RequestParam(value = "typeCode", required = true) String typeCode) {
		SysDictItem dictItem = new SysDictItem();
		dictItem.setItemCode(typeCode);
		// 获取字典树
		List<TreeDropVo> dictItems = basicDictItemService.getTreeDrop(dictItem);
		return dictItems;
	}
	
	

	private List<DictTree> getDictTree(List<SysDictItem> dicts, String root) {
		List<DictTree> trees = new ArrayList<DictTree>();
		DictTree node = null;
		for (SysDictItem dict : dicts) {
			node = new DictTree();
			BeanUtils.copyProperties(dict, node);
			node.setItemValue(dict.getItemValue());
			node.setId(dict.getItemCode());
			trees.add(node);
		}
		if (StringUtils.isEmpty(root)) {
			root = "-1";
		}
		return TreeUtil.bulidNode(trees, root);
	}

	/**
	 * 根据字典CODE得到下级结点列表一级
	 * 
	 * @param typeCode
	 * @return List<IpmSysDictItem> @throws
	 */
	@GetMapping("/rest/findDictItemByDictType")
	@ResponseBody
	@ApiOperation(value = "根据字典类型获取字典条目", notes = "根据字典类型获取字典条目")
	public List<SysDictItem> findDictItemByDictType(
			@RequestParam(value = "typeCode", required = true) String typeCode) {
		return basicDictItemService.findDictItemByDictType(typeCode);
	}

	/**
	 * 根据字典条目 code 得到值
	 * 
	 * @param code
	 * @return IpmSysDictItem @throws
	 */
	@GetMapping("/rest/findDictItem")
	@ResponseBody
	@ApiOperation(value = "根据字典条目 code 得到值", notes = "根据字典条目 code 得到值")
	public SysDictItem findDictItem(@RequestParam(value = "code", required = true) String code) {
		return basicDictItemService.findDictItem(code);
	}
}
