package com.fd.stdp.controller.talent;

import com.fd.stdp.beans.basic.BasicGradeLinked;
import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectExamine;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectExamineVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamApply;
import com.fd.stdp.beans.talent.vo.TalentTeamApplyVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.talent.TalentTeamApplyService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 科技创新团队申请书
 *@Author: wangsh
 *@Date: 2022-02-14 10:21:55
 */
@RestController
@RequestMapping("/talent/talentTeamApply")
@Api(value="科技创新团队申请书", description="科技创新团队申请书")
public class TalentTeamApplyController  extends BaseController {

	@Autowired
	private TalentTeamApplyService talentTeamApplyService;
	
	private final String PER_PREFIX = "talent:but:teamApply:";
	
	/**
	 *@Description: 新增科技创新团队申请书
	 *@param vo 科技创新团队申请书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增科技创新团队申请书", notes = "新增科技创新团队申请书")
	@SystemLogAnnotation(type = "科技创新团队申请书",value = "新增科技创新团队申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTalentTeamApply(@RequestBody TalentTeamApplyVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentTeamApplyService.saveOrUpdateTalentTeamApply(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改科技创新团队申请书
	 *@param talentTeamApply 科技创新团队申请书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改科技创新团队申请书", notes = "修改科技创新团队申请书")
	@SystemLogAnnotation(type = "科技创新团队申请书",value = "修改科技创新团队申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTalentTeamApply(@RequestBody TalentTeamApplyVo talentTeamApply) {
		String id = talentTeamApplyService.saveOrUpdateTalentTeamApply(talentTeamApply);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除科技创新团队申请书(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除科技创新团队申请书", notes = "删除科技创新团队申请书")
	@SystemLogAnnotation(type = "科技创新团队申请书",value = "删除科技创新团队申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTalentTeamApply(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		talentTeamApplyService.deleteTalentTeamApply(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除科技创新团队申请书(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除科技创新团队申请书", notes = "删除科技创新团队申请书")
	@SystemLogAnnotation(type = "科技创新团队申请书",value = "删除科技创新团队申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiTalentTeamApply(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		talentTeamApplyService.deleteMultiTalentTeamApply(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询科技创新团队申请书详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询科技创新团队申请书详情", notes = "查询科技创新团队申请书详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		TalentTeamApply  talentTeamApply=talentTeamApplyService.findById(id);
		return RestApiResponse.ok(talentTeamApply);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			TalentTeamApplyVo vo = new TalentTeamApplyVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<TalentTeamApply> pageInfo = (PageInfo<TalentTeamApply>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
	
	/**
	 *@Description: 分页查询科技创新团队申请书
	 *@param talentTeamApplyVo 科技创新团队申请书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询科技创新团队申请书", notes = "分页查询科技创新团队申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TalentTeamApplyVo talentTeamApplyVo) {
		PageInfo<TalentTeamApply>  talentTeamApply=talentTeamApplyService.findPageByQuery(talentTeamApplyVo);
		return RestApiResponse.ok(talentTeamApply);
	}

	/**
	 *@Description: 科技创新团队申请书提交
	 *@param vo 科技创新团队申请书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "科技创新团队申请书提交", notes = "科技创新团队申请书提交")
	@SystemLogAnnotation(type = "科技创新团队申请书",value = "科技创新团队申请书提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTalentTeamApply(@RequestBody TalentTeamApplyVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentTeamApplyService.submitTalentTeamApply(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 科技创新团队申请书审核
	 *@param talentTeamApplyVo 科技创新团队申请书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "科技创新团队申请书审核", notes = "科技创新团队申请书审核")
	@SystemLogAnnotation(type = "科技创新团队申请书",value = "科技创新团队申请书审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTalentTeamApply(@RequestBody TalentTeamApplyVo talentTeamApplyVo) {
		String id = talentTeamApplyService.auditTalentTeamApply(talentTeamApplyVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 科技创新团队申请书退回
	 *@param talentTeamApplyVo 科技创新团队申请书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "科技创新团队申请书退回", notes = "科技创新团队申请书退回")
	@SystemLogAnnotation(type = "科技创新团队申请书",value = "科技创新团队申请书退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTalentTeamApply(@RequestBody TalentTeamApplyVo talentTeamApplyVo) {
		String id = talentTeamApplyService.sendBackTalentTeamApply(talentTeamApplyVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 科技创新团队申请书任务书下达
	 *@param talentTeamApplyVo 科技创新团队申请书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "科技创新团队申请书任务书下达", notes = "科技创新团队申请书任务书下达")
	@SystemLogAnnotation(type = "科技创新团队申请书",value = "科技创新团队申请书任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseTalentTeamApply(@RequestBody TalentTeamApplyVo talentTeamApplyVo) {
		String id = talentTeamApplyService.releaseTalentTeamApply(talentTeamApplyVo);
		return RestApiResponse.ok(id);
	}


	/**
	 *@Description: 专家评审提交
	 *@param talentTeamApplyVo 科技创新团队申请书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/expertSubmit")
	@ApiOperation(value = "专家评审提交", notes = "专家评审提交")
	@SystemLogAnnotation(type = "科技创新团队申请书",value = "专家评审提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> expertSubmit(@RequestBody TalentTeamApplyVo talentTeamApplyVo) {
		String id = talentTeamApplyService.expertSubmit(talentTeamApplyVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 科技创新团队申请书待办
	 *@param vo 科技创新团队申请书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "科技创新团队申请书待办", notes = "科技创新团队申请书待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody TalentTeamApplyVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = talentTeamApplyService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = talentTeamApplyService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = talentTeamApplyService.findPageByQuery(vo);
		} else {
			list = talentTeamApplyService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 科技创新团队申请书已办
	 *@param talentTeamApplyVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "科技创新团队申请书已办", notes = "科技创新团队申请书已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody TalentTeamApplyVo talentTeamApplyVo) {
		PageInfo<TalentTeamApply>  talentTeamApply=talentTeamApplyService.finishedList(talentTeamApplyVo);
		return RestApiResponse.ok(talentTeamApply);
	}

	/**
	 *@Description: 科技创新团队申请书已办
	 *@param talentTeamApplyVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/toContract")
	@ApiOperation(value = "科技创新团队申请书已办", notes = "科技创新团队申请书已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> toContract(@RequestBody TalentTeamApplyVo talentTeamApplyVo) {
		PageInfo<TalentTeamApply>  talentTeamApply=talentTeamApplyService.toContractList(talentTeamApplyVo);
		return RestApiResponse.ok(talentTeamApply);
	}

	/**
	 *@Description: 批量删除科技创新团队申请书(判断 关联数据是否可以删除)
	 *@param talentTeamApplyVo
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/createContract")
	@ApiOperation(value = "删除科技创新团队申请书", notes = "删除科技创新团队申请书")
	@SystemLogAnnotation(type = "科技创新团队申请书",value = "删除科技创新团队申请书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> createContract(@RequestBody TalentTeamApplyVo talentTeamApplyVo) {
		//判断 关联数据是否可以删除
		talentTeamApplyService.createContractTalentTeamApply(talentTeamApplyVo);
		return RestApiResponse.ok();
	}
}
