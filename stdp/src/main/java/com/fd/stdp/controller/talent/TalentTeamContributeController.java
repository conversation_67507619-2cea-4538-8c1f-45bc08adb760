package com.fd.stdp.controller.talent;

import com.fd.stdp.beans.talent.TalentTeamApply;
import com.fd.stdp.beans.talent.vo.TalentTeamApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamContribute;
import com.fd.stdp.beans.talent.vo.TalentTeamContributeVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.talent.TalentTeamContributeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 创新团队建设任务书
 *@Author: wangsh
 *@Date: 2022-02-14 10:22:12
 */
@RestController
@RequestMapping("/talent/talentTeamContribute")
@Api(value="创新团队建设任务书", description="创新团队建设任务书")
public class TalentTeamContributeController  extends BaseController {

	@Autowired
	private TalentTeamContributeService talentTeamContributeService;
	
	private final String PER_PREFIX = "talent:but:teamContribute:";
	
	/**
	 *@Description: 新增创新团队建设任务书
	 *@param vo 创新团队建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增创新团队建设任务书", notes = "新增创新团队建设任务书")
	@SystemLogAnnotation(type = "创新团队建设任务书",value = "新增创新团队建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTalentTeamContribute(@RequestBody TalentTeamContributeVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentTeamContributeService.saveOrUpdateTalentTeamContribute(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改创新团队建设任务书
	 *@param talentTeamContribute 创新团队建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改创新团队建设任务书", notes = "修改创新团队建设任务书")
	@SystemLogAnnotation(type = "创新团队建设任务书",value = "修改创新团队建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTalentTeamContribute(@RequestBody TalentTeamContributeVo talentTeamContribute) {
		String id = talentTeamContributeService.saveOrUpdateTalentTeamContribute(talentTeamContribute);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除创新团队建设任务书(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除创新团队建设任务书", notes = "删除创新团队建设任务书")
	@SystemLogAnnotation(type = "创新团队建设任务书",value = "删除创新团队建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTalentTeamContribute(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		talentTeamContributeService.deleteTalentTeamContribute(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除创新团队建设任务书(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除创新团队建设任务书", notes = "删除创新团队建设任务书")
	@SystemLogAnnotation(type = "创新团队建设任务书",value = "删除创新团队建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiTalentTeamContribute(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		talentTeamContributeService.deleteMultiTalentTeamContribute(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询创新团队建设任务书详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询创新团队建设任务书详情", notes = "查询创新团队建设任务书详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		TalentTeamContribute  talentTeamContribute=talentTeamContributeService.findById(id);
		return RestApiResponse.ok(talentTeamContribute);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			TalentTeamContributeVo vo = new TalentTeamContributeVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<TalentTeamContribute> pageInfo = (PageInfo<TalentTeamContribute>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
	
	/**
	 *@Description: 分页查询创新团队建设任务书
	 *@param talentTeamContributeVo 创新团队建设任务书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询创新团队建设任务书", notes = "分页查询创新团队建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TalentTeamContributeVo talentTeamContributeVo) {
		PageInfo<TalentTeamContribute>  talentTeamContribute=talentTeamContributeService.findPageByQuery(talentTeamContributeVo);
		return RestApiResponse.ok(talentTeamContribute);
	}

	/**
	 *@Description: 创新团队建设任务书提交
	 *@param vo 创新团队建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "创新团队建设任务书提交", notes = "创新团队建设任务书提交")
	@SystemLogAnnotation(type = "创新团队建设任务书",value = "创新团队建设任务书提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTalentTeamContribute(@RequestBody TalentTeamContributeVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentTeamContributeService.submitTalentTeamContribute(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 创新团队建设任务书审核
	 *@param talentTeamContributeVo 创新团队建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "创新团队建设任务书审核", notes = "创新团队建设任务书审核")
	@SystemLogAnnotation(type = "创新团队建设任务书",value = "创新团队建设任务书审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTalentTeamContribute(@RequestBody TalentTeamContributeVo talentTeamContributeVo) {
		String id = talentTeamContributeService.auditTalentTeamContribute(talentTeamContributeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 创新团队建设任务书退回
	 *@param talentTeamContributeVo 创新团队建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "创新团队建设任务书退回", notes = "创新团队建设任务书退回")
	@SystemLogAnnotation(type = "创新团队建设任务书",value = "创新团队建设任务书退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTalentTeamContribute(@RequestBody TalentTeamContributeVo talentTeamContributeVo) {
		String id = talentTeamContributeService.sendBackTalentTeamContribute(talentTeamContributeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 创新团队建设任务书任务书下达
	 *@param talentTeamContributeVo 创新团队建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "创新团队建设任务书任务书下达", notes = "创新团队建设任务书任务书下达")
	@SystemLogAnnotation(type = "创新团队建设任务书",value = "创新团队建设任务书任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseTalentTeamContribute(@RequestBody TalentTeamContributeVo talentTeamContributeVo) {
		String id = talentTeamContributeService.releaseTalentTeamContribute(talentTeamContributeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 创新团队建设任务书待办
	 *@param vo 创新团队建设任务书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "创新团队建设任务书待办", notes = "创新团队建设任务书待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody TalentTeamContributeVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = talentTeamContributeService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = talentTeamContributeService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = talentTeamContributeService.findPageByQuery(vo);
		} else {
			list = talentTeamContributeService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 创新团队建设任务书已办
	 *@param talentTeamContributeVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "创新团队建设任务书已办", notes = "创新团队建设任务书已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody TalentTeamContributeVo talentTeamContributeVo) {
		PageInfo<TalentTeamContribute>  talentTeamContribute=talentTeamContributeService.finishedList(talentTeamContributeVo);
		return RestApiResponse.ok(talentTeamContribute);
	}
}
