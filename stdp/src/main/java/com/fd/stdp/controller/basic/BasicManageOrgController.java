package com.fd.stdp.controller.basic;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.basic.BasicManageOrg;
import com.fd.stdp.beans.basic.vo.BasicManageOrgVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.basic.BasicManageOrgService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 监管单位Controller
 *
 * <AUTHOR>
 * @date 2021-11-08
 */
@RestController
@RequestMapping("/basic/manageOrg")
@Api(value = "监管单位", description = "监管单位")
public class BasicManageOrgController extends BaseController {
    @Autowired
    private BasicManageOrgService basicManageOrgService;

    private final String PER_PREFIX = "btn:basic:manageOrg:";

    /**
     * @param basicManageOrgVo 监管单位数据 json
     * @return RestApiResponse<?>
     * @Description: 新增监管单位
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增监管单位", notes = "新增监管单位")
    @SystemLogAnnotation(type = "监管单位", value = "新增监管单位")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveBasicManageOrg(@RequestBody BasicManageOrgVo basicManageOrgVo) {
        String id = basicManageOrgService.saveOrUpdateBasicManageOrg(basicManageOrgVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param basicManageOrgVo 监管单位数据 json
     * @return RestApiResponse<?>
     * @Description: 修改监管单位
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改监管单位", notes = "修改监管单位")
    @SystemLogAnnotation(type = "监管单位", value = "修改监管单位")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateBasicManageOrg(@RequestBody BasicManageOrgVo basicManageOrgVo) {
        String id = basicManageOrgService.saveOrUpdateBasicManageOrg(basicManageOrgVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除监管单位(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除监管单位", notes = "批量删除监管单位")
    @SystemLogAnnotation(type = "监管单位", value = "批量删除监管单位")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteBasicManageOrg(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        basicManageOrgService.deleteBasicManageOrg(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询监管单位详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询监管单位详情", notes = "查询监管单位详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        // 权限验证：检查用户是否有权限查看该监管单位信息
        BasicManageOrgVo queryVo = new BasicManageOrgVo();
        queryVo.setPageNum(1);
        queryVo.setPageSize(Integer.MAX_VALUE); // 获取所有有权限的数据
        
        PageInfo<BasicManageOrgVo> authorizedOrgs = basicManageOrgService.findPageByQuery(queryVo);
        List<String> authorizedIds = authorizedOrgs.getList().stream()
                .map(BasicManageOrg::getId)
                .collect(Collectors.toList());
        
        if (!authorizedIds.contains(id)) {
            return RestApiResponse.error("无权限查看该监管单位信息");
        }

        BasicManageOrg basicManageOrg = basicManageOrgService.findById(id);
        return RestApiResponse.ok(basicManageOrg);
    }

    /**
     * @param basicManageOrgVo 监管单位 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询监管单位
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询监管单位", notes = "分页查询监管单位")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody BasicManageOrgVo basicManageOrgVo) {
        PageInfo<BasicManageOrgVo> basicManageOrg = basicManageOrgService.findPageByQuery(basicManageOrgVo);
        return RestApiResponse.ok(basicManageOrg);
    }

    /**
     * @return RestApiResponse<?>
     * @Description: 查询所有市级监管单位
     * @Author: yujianfei
     */
    @PostMapping("/findAllCity")
    @ApiOperation(value = "查询所有市级监管单位", notes = "查询所有市级监管单位")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findAllCity')")
    public RestApiResponse<?> findAllCity() {
        List<BasicManageOrg> basicManageOrg = basicManageOrgService.findAllCity();
        return RestApiResponse.ok(basicManageOrg);
    }


    /**
     * @return RestApiResponse<?>
     * @Description: 查询所有县级监管单位
     * @Author: yujianfei
     */
    @PostMapping("/findAllCounty")
    @ApiOperation(value = "查询所有县级监管单位", notes = "查询所有县级监管单位")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findAllCounty')")
    public RestApiResponse<?> findAllCounty() {
        List<BasicManageOrg> basicManageOrg = basicManageOrgService.findAllCounty();
        return RestApiResponse.ok(basicManageOrg);
    }


}
