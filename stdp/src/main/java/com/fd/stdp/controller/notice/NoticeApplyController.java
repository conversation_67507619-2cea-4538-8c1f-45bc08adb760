package com.fd.stdp.controller.notice;

import com.fd.stdp.constant.AssigneeConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.notice.NoticeApply;
import com.fd.stdp.beans.notice.vo.NoticeApplyVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.notice.NoticeApplyService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.util.stream.Collectors;

/**
 *@Description: 
 *@Author: wangsh
 *@Date: 2022-03-15 09:45:58
 */
@RestController
@RequestMapping("/notice/noticeApply")
@Api(value="", description="")
public class NoticeApplyController  extends BaseController {

	@Autowired
	private NoticeApplyService noticeApplyService;
	
	private final String PER_PREFIX = "but:notice:notice:";
	
	/**
	 *@Description: 新增
	 *@param noticeApplyVo 数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增", notes = "新增")
	@SystemLogAnnotation(type = "",value = "新增")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveNoticeApply(@RequestBody NoticeApplyVo noticeApplyVo) {
		String id = noticeApplyService.saveOrUpdateNoticeApply(noticeApplyVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改
	 *@param noticeApply 数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改", notes = "修改")
	@SystemLogAnnotation(type = "",value = "修改")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateNoticeApply(@RequestBody NoticeApplyVo noticeApply) {
		String id = noticeApplyService.saveOrUpdateNoticeApply(noticeApply);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除", notes = "删除")
	@SystemLogAnnotation(type = "",value = "删除")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteNoticeApply(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		noticeApplyService.deleteNoticeApply(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除", notes = "删除")
	@SystemLogAnnotation(type = "",value = "删除")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiNoticeApply(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		noticeApplyService.deleteMultiNoticeApply(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询详情", notes = "查询详情")
//	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		NoticeApply  noticeApply=noticeApplyService.findById(id);
		return RestApiResponse.ok(noticeApply);
	}
	
	/**
	 *@Description: 分页查询
	 *@param noticeApplyVo  查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询", notes = "分页查询")
	//@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody NoticeApplyVo noticeApplyVo) {
		PageInfo<NoticeApply>  noticeApply=noticeApplyService.findPageByQuery(noticeApplyVo);
		return RestApiResponse.ok(noticeApply);
	}

	/**
	 *@Description: 开启
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/open")
	@ApiOperation(value = "开启", notes = "开启")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> open(@RequestParam("id") String id) {
		noticeApplyService.open(id);
		return RestApiResponse.ok();
	}

	/**
	 *@Description: 关闭
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/close")
	@ApiOperation(value = "关闭", notes = "关闭")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> close(@RequestParam("id") String id) {
		noticeApplyService.close(id);
		return RestApiResponse.ok();
	}
}
