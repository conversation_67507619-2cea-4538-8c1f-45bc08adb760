package com.fd.stdp.controller.basic;

import com.fd.stdp.beans.innovation.InnovationGeneralLaboratoryApply;
import com.fd.stdp.beans.innovation.vo.InnovationGeneralLaboratoryApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonExpertCommittee;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertCommitteeVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.basic.BasicPersonExpertCommitteeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Description: 专家委员会库
 * @Author: wangsh
 * @Date: 2022-01-07 13:20:37
 */
@RestController
@RequestMapping("/basic/basicPersonExpertCommittee")
@Api(value = "专家委员会库", description = "专家委员会库")
public class BasicPersonExpertCommitteeController extends BaseController {

    @Autowired
    private BasicPersonExpertCommitteeService basicPersonExpertCommitteeService;

    private final String PER_PREFIX = "basic:but:expertCommittee:";

    /**
     * @param basicPersonExpertCommittee 专家委员会库数据 json
     * @return RestApiResponse<?>
     * @Description: 新增专家委员会库
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增专家委员会库", notes = "新增专家委员会库")
    @SystemLogAnnotation(type = "专家委员会库", value = "新增专家委员会库")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveBasicPersonExpertCommittee(@RequestBody BasicPersonExpertCommitteeVo basicPersonExpertCommittee) {
        basicPersonExpertCommittee.setOrgName(getCurrentOrgName());
        basicPersonExpertCommittee.setOrgCode(getCurrentScienceOrgId());
        String id = basicPersonExpertCommitteeService.saveOrUpdateBasicPersonExpertCommittee(basicPersonExpertCommittee);
        return RestApiResponse.ok(id);
    }

    /**
     * @param basicPersonExpertCommittee 专家委员会库数据 json
     * @return RestApiResponse<?>
     * @Description: 修改专家委员会库
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改专家委员会库", notes = "修改专家委员会库")
    @SystemLogAnnotation(type = "专家委员会库", value = "修改专家委员会库")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateBasicPersonExpertCommittee(@RequestBody BasicPersonExpertCommitteeVo basicPersonExpertCommittee) {
        String id = basicPersonExpertCommitteeService.saveOrUpdateBasicPersonExpertCommittee(basicPersonExpertCommittee);
        return RestApiResponse.ok(id);
    }

    /**
     * @param ids
     * @return RestApiResponse<?>
     * @Description: 删除专家委员会库(判断 关联数据是否可以删除)
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "删除专家委员会库", notes = "删除专家委员会库")
    @SystemLogAnnotation(type = "专家委员会库", value = "删除专家委员会库")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteBasicPersonExpertCommittee(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        basicPersonExpertCommitteeService.deleteMultiBasicPersonExpertCommittee(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询专家委员会库详情
     * @Author: wangsh
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询专家委员会库详情", notes = "查询专家委员会库详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        // 权限验证：检查用户是否有权限查看该数据
        if (!hasPermissionToViewData(id)) {
            throw new ServiceException("无权限访问该数据");
        }
        BasicPersonExpertCommittee basicPersonExpertCommittee = basicPersonExpertCommitteeService.findById(id);
        return RestApiResponse.ok(basicPersonExpertCommittee);
    }

    /**
     * @param basicPersonExpertCommitteeVo 专家委员会库 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询专家委员会库
     * @Author: wangsh
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询专家委员会库", notes = "分页查询专家委员会库")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody BasicPersonExpertCommitteeVo basicPersonExpertCommitteeVo) {
        PageInfo<BasicPersonExpertCommittee> basicPersonExpertCommittee = basicPersonExpertCommitteeService.findPageByQuery(basicPersonExpertCommitteeVo);
        return RestApiResponse.ok(basicPersonExpertCommittee);
    }


    /**
     * @param basicPersonExpertCommittee 专家委员会库数据 json
     * @return RestApiResponse<?>
     * @Description: 推荐专家委员会库
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/submit")
    @ApiOperation(value = "推荐专家委员会库", notes = "推荐专家委员会库")
    @SystemLogAnnotation(type = "专家委员会库", value = "推荐专家委员会库")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> submitBasicPersonExpertCommittee(@RequestBody BasicPersonExpertCommitteeVo basicPersonExpertCommittee) {
        basicPersonExpertCommittee.setOrgName(getCurrentOrgName());
        basicPersonExpertCommittee.setOrgCode(getCurrentScienceOrgId());
        String id = basicPersonExpertCommitteeService.submitBasicPersonExpertCommittee(basicPersonExpertCommittee);
        return RestApiResponse.ok(id);
    }


    /**
     * @param basicPersonExpertCommittee 审核专家委员会库 json
     * @return RestApiResponse<?>
     * @Description: 审核专家委员会库
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/audit")
    @ApiOperation(value = "审核专家委员会库", notes = "审核专家委员会库")
    @SystemLogAnnotation(type = "专家委员会库", value = "审核专家委员会库")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "audit')")
    public RestApiResponse<?> auditBasicPersonExpertCommittee(@RequestBody BasicPersonExpertCommitteeVo basicPersonExpertCommittee) {
        String id = basicPersonExpertCommitteeService.auditBasicPersonExpertCommittee(basicPersonExpertCommittee);
        return RestApiResponse.ok(id);
    }


    /**
     * @param basicPersonExpertCommittee 专家委员会库数据 json
     * @return RestApiResponse<?>
     * @Description: 退回专家委员会库
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/sendBack")
    @ApiOperation(value = "退回专家委员会库", notes = "退回专家委员会库")
    @SystemLogAnnotation(type = "专家委员会库", value = "退回专家委员会库")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "audit')")
    public RestApiResponse<?> sendBackBasicPersonExpertCommittee(@RequestBody BasicPersonExpertCommitteeVo basicPersonExpertCommittee) {
        String id = basicPersonExpertCommitteeService.sendBackBasicPersonExpertCommittee(basicPersonExpertCommittee);
        return RestApiResponse.ok(id);
    }


    /**
     * @param basicPersonExpertCommittee 专家委员会库数据 json
     * @return RestApiResponse<?>
     * @Description: 拒绝专家委员会库
     * @Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/reject")
    @ApiOperation(value = "拒绝专家委员会库", notes = "拒绝专家委员会库")
    @SystemLogAnnotation(type = "专家委员会库", value = "拒绝专家委员会库")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "audit')")
    public RestApiResponse<?> rejectBasicPersonExpertCommittee(@RequestBody BasicPersonExpertCommitteeVo basicPersonExpertCommittee) {
        String id = basicPersonExpertCommitteeService.rejectBasicPersonExpertCommittee(basicPersonExpertCommittee);
        return RestApiResponse.ok(id);
    }

    /**
     * @param basicPersonExpertCommitteeVo 专家委员会库 查询条件
     * @return RestApiResponse<?>
     * @Description: 待办专家委员会库
     * @Author: wangsh
     */
    @PostMapping("/todo")
    @ApiOperation(value = "待办专家委员会库", notes = "待办专家委员会库")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> todo(@RequestBody BasicPersonExpertCommitteeVo basicPersonExpertCommitteeVo) {
        PageInfo<BasicPersonExpertCommittee> basicPersonExpertCommittee = basicPersonExpertCommitteeService.todoList(basicPersonExpertCommitteeVo);
        return RestApiResponse.ok(basicPersonExpertCommittee);
    }

    /**
     * @param basicPersonExpertCommitteeVo 专家委员会库 查询条件
     * @return RestApiResponse<?>
     * @Description: 已办专家委员会库
     * @Author: wangsh
     */
    @PostMapping("/finished")
    @ApiOperation(value = "已办专家委员会库", notes = "已办专家委员会库")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> finished(@RequestBody BasicPersonExpertCommitteeVo basicPersonExpertCommitteeVo) {
        PageInfo<BasicPersonExpertCommittee> basicPersonExpertCommittee = basicPersonExpertCommitteeService.finishedList(basicPersonExpertCommitteeVo);
        return RestApiResponse.ok(basicPersonExpertCommittee);
    }

    /**
     * @return RestApiResponse<?>
     * @Description: 查询是否在推荐期间内
     * @Author: wangsh
     */
    @GetMapping("/inRecommendTime")
    @ApiOperation(value = "查询是否在推荐期间内", notes = "查询是否在推荐期间内")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> inRecommendTime() {
        return RestApiResponse.ok(basicPersonExpertCommitteeService.inRecommendTime());
    }

//	/**
//	 * 检查用户是否有权限查看指定数据
//	 * @param id 数据ID
//	 * @return 是否有权限
//	 */
//	private boolean hasPermissionToViewData(String id) {
//		try {
//			Set<String> accessibleIds = new HashSet<>();
//
//			// 聚合待办和已办数据的ID
//			BasicPersonExpertCommitteeVo queryVo = new BasicPersonExpertCommitteeVo();
//
//			// 获取待办数据ID
//			PageInfo<BasicPersonExpertCommittee> todoData = basicPersonExpertCommitteeService.todoList(queryVo);
//			if (todoData != null && todoData.getList() != null) {
//				todoData.getList().forEach(item -> {
//					if (item.getId() != null) {
//						accessibleIds.add(item.getId());
//					}
//				});
//			}
//
//			// 获取已办数据ID
//			PageInfo<BasicPersonExpertCommittee> finishedData = basicPersonExpertCommitteeService.finishedList(queryVo);
//			if (finishedData != null && finishedData.getList() != null) {
//				finishedData.getList().forEach(item -> {
//					if (item.getId() != null) {
//						accessibleIds.add(item.getId());
//					}
//				});
//			}
//
//			return accessibleIds.contains(id);
//		} catch (Exception e) {
//			// 发生异常时，为安全起见返回false
//			return false;
//		}
//	}

    /**
     * 检查用户是否有权限查看指定ID的数据
     *
     * @param targetId 要查询的数据ID
     * @return 是否有权限
     */
    private boolean hasPermissionToViewData(String targetId) {
        try {
            // 创建查询条件对象
            BasicPersonExpertCommitteeVo vo = new BasicPersonExpertCommitteeVo();
            vo.setPageNum(1);
            vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据
            // 获取用户可访问的所有数据ID集合
            Set<String> accessibleIds = new HashSet<>();
            List<BasicPersonExpertCommittee> list = basicPersonExpertCommitteeService.todoList(vo).getList();
            if (list != null) {
                list.forEach(item -> accessibleIds.add(item.getId()));
            }
            // 检查目标ID是否在用户可访问的数据集合中
            return accessibleIds.contains(targetId);
        } catch (Exception e) {
            // 发生异常时，为了安全起见，拒绝访问
            return false;
        }
    }

}
