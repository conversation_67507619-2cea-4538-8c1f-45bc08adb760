package com.fd.stdp.controller.project;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.project.ProjectApplyCooperationUnit;
import com.fd.stdp.beans.project.vo.ProjectApplyCooperationUnitVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectApplyCooperationUnitService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 项目合作单位Controller
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
@RestController
@RequestMapping("/project/applyCooperationUnit")
@Api(value = "项目合作单位", description = "项目合作单位")
public class ProjectApplyCooperationUnitController extends BaseController {
    @Autowired
    private ProjectApplyCooperationUnitService projectApplyCooperationUnitService;

    private final String PER_PREFIX = "btn:project:applyCooperationUnit:";

    /**
     * @param projectApplyCooperationUnit 项目合作单位数据 json
     * @return RestApiResponse<?>
     * @Description: 新增项目合作单位
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增项目合作单位", notes = "新增项目合作单位")
    @SystemLogAnnotation(type = "项目合作单位", value = "新增项目合作单位")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveProjectApplyCooperationUnit(@RequestBody ProjectApplyCooperationUnit projectApplyCooperationUnit) {
        String id = projectApplyCooperationUnitService.saveOrUpdateProjectApplyCooperationUnit(projectApplyCooperationUnit);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectApplyCooperationUnit 项目合作单位数据 json
     * @return RestApiResponse<?>
     * @Description: 修改项目合作单位
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改项目合作单位", notes = "修改项目合作单位")
    @SystemLogAnnotation(type = "项目合作单位", value = "修改项目合作单位")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateProjectApplyCooperationUnit(@RequestBody ProjectApplyCooperationUnit projectApplyCooperationUnit) {
        String id = projectApplyCooperationUnitService.saveOrUpdateProjectApplyCooperationUnit(projectApplyCooperationUnit);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除项目合作单位(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除项目合作单位", notes = "批量删除项目合作单位")
    @SystemLogAnnotation(type = "项目合作单位", value = "批量删除项目合作单位")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteProjectApplyCooperationUnit(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        projectApplyCooperationUnitService.deleteProjectApplyCooperationUnit(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询项目合作单位详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询项目合作单位详情", notes = "查询项目合作单位详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ProjectApplyCooperationUnit projectApplyCooperationUnit = projectApplyCooperationUnitService.findById(id);
        return RestApiResponse.ok(projectApplyCooperationUnit);
    }

    /**
     * @param projectApplyCooperationUnitVo 项目合作单位 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询项目合作单位
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询项目合作单位", notes = "分页查询项目合作单位")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ProjectApplyCooperationUnitVo projectApplyCooperationUnitVo) {
        PageInfo<ProjectApplyCooperationUnit> projectApplyCooperationUnit = projectApplyCooperationUnitService.findPageByQuery(projectApplyCooperationUnitVo);
        return RestApiResponse.ok(projectApplyCooperationUnit);
    }

}
