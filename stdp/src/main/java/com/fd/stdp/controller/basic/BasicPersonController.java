package com.fd.stdp.controller.basic;

import com.fd.stdp.beans.basic.BasicPersonReport;
import com.fd.stdp.beans.basic.vo.BasicPersonReportVo;
import com.fd.stdp.beans.sys.SysRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPerson;
import com.fd.stdp.beans.basic.vo.BasicPersonVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.basic.BasicPersonService;
import com.fd.stdp.service.main.MainPageService;
import com.github.pagehelper.PageInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 *@Description: 机构人员库
 *@Author: wangsh
 *@Date: 2022-01-07 10:53:24
 */
@RestController
@RequestMapping("/basic/basicPerson")
@Api(value="机构人员库", description="机构人员库")
public class BasicPersonController  extends BaseController {

	@Autowired
	private BasicPersonService basicPersonService;
	
	@Autowired
	private MainPageService mainPageService;
	
	private final String PER_PREFIX = "basic:but:person:";
	
	/**
	 *@Description: 新增机构人员库
	 *@param basicPerson 机构人员库数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增机构人员库", notes = "新增机构人员库")
	@SystemLogAnnotation(type = "机构人员库",value = "新增机构人员库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveBasicPerson(@RequestBody BasicPersonVo basicPerson) {
		basicPerson.setOrgName(getCurrentOrgName());
		basicPerson.setOrgId(getCurrentScienceOrgId());
		String id = basicPersonService.saveOrUpdateBasicPerson(basicPerson);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改机构人员库
	 *@param basicPerson 机构人员库数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改机构人员库", notes = "修改机构人员库")
	@SystemLogAnnotation(type = "机构人员库",value = "修改机构人员库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateBasicPerson(@RequestBody BasicPersonVo basicPerson) {
		String id = basicPersonService.saveOrUpdateBasicPerson(basicPerson);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除机构人员库(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除机构人员库", notes = "删除机构人员库")
	@SystemLogAnnotation(type = "机构人员库",value = "删除机构人员库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteBasicPerson(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		basicPersonService.deleteMultiBasicPerson(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询机构人员库详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询机构人员库详情", notes = "查询机构人员库详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
//		// 权限验证：检查用户是否有权限查看该人员信息
//		BasicPersonVo queryVo = new BasicPersonVo();
//		queryVo.setPageNum(1);
//		queryVo.setPageSize(Integer.MAX_VALUE);
//		PageInfo<BasicPerson> authorizedPersons = (PageInfo<BasicPerson>) mainPageService.queryPerson(queryVo);
//
//		List<String> authorizedIds = authorizedPersons.getList().stream()
//				.map(BasicPerson::getId)
//				.collect(Collectors.toList());
//
//		if (!authorizedIds.contains(id)) {
//			return RestApiResponse.error("无权限查看该人员信息");
//		}
		
		BasicPerson  basicPerson=basicPersonService.findById(id);
		return RestApiResponse.ok(basicPerson);
	}

	/**
	 *@Description: 查询机构人员库详情
	 *@param id 身份证号
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findByIdentityId")
	@ApiOperation(value = "查询机构人员库详情", notes = "查询机构人员库详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findByIdentityId(@RequestParam("id") String id) {
		return RestApiResponse.ok(basicPersonService.findByIdentityId(id));
	}
	
	/**
	 *@Description: 分页查询机构人员库 (自己单位)
	 *@param basicPersonVo 机构人员库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询机构人员库", notes = "分页查询机构人员库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody BasicPersonVo basicPersonVo) {
		PageInfo<BasicPerson>  basicPerson=basicPersonService.findPageByQuery(basicPersonVo);
		return RestApiResponse.ok(basicPerson);
	}

	/**
	 *@Description: 分页查询机构人员库 (全库)
	 *@param basicPersonVo 机构人员库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQueryGlobal")
	@ApiOperation(value = "分页查询机构人员库", notes = "分页查询机构人员库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQueryGlobal(@RequestBody BasicPersonVo basicPersonVo) {
		PageInfo<BasicPerson>  basicPerson=basicPersonService.findPageByQueryGlobal(basicPersonVo);
		return RestApiResponse.ok(basicPerson);
	}

	/**
	 *@Description: 分页查询机构人员库 (主库)
	 *@param basicPersonVo 机构人员库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQueryMain")
	@ApiOperation(value = "分页查询机构人员库", notes = "分页查询机构人员库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQueryMain(@RequestBody BasicPersonVo basicPersonVo) {
		PageInfo<BasicPerson>  basicPerson=basicPersonService.findPageByQueryMain(basicPersonVo);
		return RestApiResponse.ok(basicPerson);
	}

	/**
	 *@Description: 批量导入机构人员库
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/upload")
	@ApiOperation(value = "批量导入机构人员库", notes = "批量导入机构人员库")
	@SystemLogAnnotation(type = "机构人员库",value = "批量导入机构人员库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"upload')")
	public void upload(@RequestParam MultipartFile file){
		basicPersonService.upload(file);
	}


	/**
	 *@Description: 批量导入机构人员库
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/uploadSystem")
	@ApiOperation(value = "批量导入机构人员库", notes = "批量导入机构人员库")
	@SystemLogAnnotation(type = "机构人员库",value = "批量导入机构人员库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"upload')")
	public void uploadSystem(@RequestParam MultipartFile file){
		basicPersonService.uploadSystem(file);
	}

	/**
	 *@Description: 提交审核机构人员库
	 *@param vo 机构人员库数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "提交审核机构人员库", notes = "提交审核机构人员库")
	@SystemLogAnnotation(type = "机构人员库",value = "提交审核机构人员库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitBasicPerson(@RequestBody BasicPersonVo vo) {
		String id = basicPersonService.submitBasicPerson(vo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 审核机构人员库
	 *@param vo 机构人员库数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "审核机构人员库", notes = "审核机构人员库")
	@SystemLogAnnotation(type = "机构人员库",value = "审核机构人员库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditBasicPerson(@RequestBody BasicPersonVo vo) {
		String id = basicPersonService.auditBasicPerson(vo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 退回机构人员库
	 *@param vo 机构人员库数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "退回机构人员库", notes = "退回机构人员库")
	@SystemLogAnnotation(type = "机构人员库",value = "退回机构人员库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackBasicPerson(@RequestBody BasicPersonVo vo) {
		String id = basicPersonService.sendBackBasicPerson(vo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 机构人员库统计
	 *@param basicPersonVo 机构人员库统计 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/statistics")
	@ApiOperation(value = "机构人员库统计", notes = "机构人员库统计")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"statistics')")
	public RestApiResponse<?> statisticsPageByQuery(@RequestBody BasicPersonVo basicPersonVo) {
		return RestApiResponse.ok(basicPersonService.statistics(basicPersonVo));
	}

	/**
	 *@Description: 机构人员库导出
	 *@param basicPersonVo 机构人员库导出 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/export")
	@ApiOperation(value = "机构人员库导出", notes = "机构人员库导出")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"export')")
	public void exportPageByQuery(@RequestBody BasicPersonVo basicPersonVo, HttpServletResponse response) {
		basicPersonService.export(basicPersonVo, response);
	}



	/**
	 *@Description: 分页查询机构人员库
	 *@param basicPersonVo 机构人员库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "机构人员库待办", notes = "机构人员库待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody BasicPersonVo basicPersonVo) {
		PageInfo<BasicPerson>  basicPerson=basicPersonService.todoList(basicPersonVo);
		return RestApiResponse.ok(basicPerson);
	}


	/**
	 *@Description: 分页查询机构人员库
	 *@param basicPersonVo 机构人员库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "机构人员库已办", notes = "机构人员库已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finishedList(@RequestBody BasicPersonVo basicPersonVo) {
		PageInfo<BasicPerson>  basicPerson=basicPersonService.finishedList(basicPersonVo);
		return RestApiResponse.ok(basicPerson);
	}



	/**
	 *@Description: 提交备案
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/record")
	@ApiOperation(value = "提交备案", notes = "提交备案")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> record() {
		basicPersonService.record();
		return RestApiResponse.ok();
	}

	/**
	 *@Description: 分页查询机构人员库
	 *@param vo 机构人员库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findRecordPageByQuery")
	@ApiOperation(value = "分页查询机构人员库", notes = "分页查询机构人员库")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findRecordPageByQuery(@RequestBody BasicPersonReportVo vo) {
		PageInfo<BasicPersonReport> basicPerson=basicPersonService.findRecordPageByQuery(vo);
		return RestApiResponse.ok(basicPerson);
	}

	/**
	 *@Description: 查询机构人员库详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findByRecordId")
	@ApiOperation(value = "查询机构人员库详情", notes = "查询机构人员库详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findByRecordId(@RequestParam("id") String id) {
		BasicPersonReport basicPersonReport=basicPersonService.findByRecordId(id);
		return RestApiResponse.ok(basicPersonReport);
	}
}
