package com.fd.stdp.controller.talent;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamExamineRegister;
import com.fd.stdp.beans.talent.vo.TalentTeamExamineRegisterVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.talent.TalentTeamExamineRegisterService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 创新团队考核登记
 *@Author: wangsh
 *@Date: 2022-02-14 10:22:40
 */
@RestController
@RequestMapping("/talent/talentTeamExamineRegister")
@Api(value="创新团队考核登记", description="创新团队考核登记")
public class TalentTeamExamineRegisterController  extends BaseController {

	@Autowired
	private TalentTeamExamineRegisterService talentTeamExamineRegisterService;
	
	private final String PER_PREFIX = "talent:but:teamRegister:";
	
	/**
	 *@Description: 新增创新团队考核登记
	 *@param vo 创新团队考核登记数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增创新团队考核登记", notes = "新增创新团队考核登记")
	@SystemLogAnnotation(type = "创新团队考核登记",value = "新增创新团队考核登记")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTalentTeamExamineRegister(@RequestBody TalentTeamExamineRegisterVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentTeamExamineRegisterService.saveOrUpdateTalentTeamExamineRegister(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改创新团队考核登记
	 *@param talentTeamExamineRegister 创新团队考核登记数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改创新团队考核登记", notes = "修改创新团队考核登记")
	@SystemLogAnnotation(type = "创新团队考核登记",value = "修改创新团队考核登记")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTalentTeamExamineRegister(@RequestBody TalentTeamExamineRegisterVo talentTeamExamineRegister) {
		String id = talentTeamExamineRegisterService.saveOrUpdateTalentTeamExamineRegister(talentTeamExamineRegister);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除创新团队考核登记(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除创新团队考核登记", notes = "删除创新团队考核登记")
	@SystemLogAnnotation(type = "创新团队考核登记",value = "删除创新团队考核登记")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTalentTeamExamineRegister(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		talentTeamExamineRegisterService.deleteTalentTeamExamineRegister(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除创新团队考核登记(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除创新团队考核登记", notes = "删除创新团队考核登记")
	@SystemLogAnnotation(type = "创新团队考核登记",value = "删除创新团队考核登记")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiTalentTeamExamineRegister(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		talentTeamExamineRegisterService.deleteMultiTalentTeamExamineRegister(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询创新团队考核登记详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询创新团队考核登记详情", notes = "查询创新团队考核登记详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		TalentTeamExamineRegister  talentTeamExamineRegister=talentTeamExamineRegisterService.findById(id);
		return RestApiResponse.ok(talentTeamExamineRegister);
	}
	
	/**
	 *@Description: 分页查询创新团队考核登记
	 *@param talentTeamExamineRegisterVo 创新团队考核登记 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询创新团队考核登记", notes = "分页查询创新团队考核登记")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TalentTeamExamineRegisterVo talentTeamExamineRegisterVo) {
		PageInfo<TalentTeamExamineRegister>  talentTeamExamineRegister=talentTeamExamineRegisterService.findPageByQuery(talentTeamExamineRegisterVo);
		return RestApiResponse.ok(talentTeamExamineRegister);
	}

	/**
	 *@Description: 创新团队考核登记提交
	 *@param vo 创新团队考核登记数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "创新团队考核登记提交", notes = "创新团队考核登记提交")
	@SystemLogAnnotation(type = "创新团队考核登记",value = "创新团队考核登记提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTalentTeamExamineRegister(@RequestBody TalentTeamExamineRegisterVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentTeamExamineRegisterService.submitTalentTeamExamineRegister(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 创新团队考核登记审核
	 *@param talentTeamExamineRegisterVo 创新团队考核登记数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "创新团队考核登记审核", notes = "创新团队考核登记审核")
	@SystemLogAnnotation(type = "创新团队考核登记",value = "创新团队考核登记审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTalentTeamExamineRegister(@RequestBody TalentTeamExamineRegisterVo talentTeamExamineRegisterVo) {
		String id = talentTeamExamineRegisterService.auditTalentTeamExamineRegister(talentTeamExamineRegisterVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 创新团队考核登记退回
	 *@param talentTeamExamineRegisterVo 创新团队考核登记数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "创新团队考核登记退回", notes = "创新团队考核登记退回")
	@SystemLogAnnotation(type = "创新团队考核登记",value = "创新团队考核登记退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTalentTeamExamineRegister(@RequestBody TalentTeamExamineRegisterVo talentTeamExamineRegisterVo) {
		String id = talentTeamExamineRegisterService.sendBackTalentTeamExamineRegister(talentTeamExamineRegisterVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 创新团队考核登记任务书下达
	 *@param talentTeamExamineRegisterVo 创新团队考核登记数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "创新团队考核登记任务书下达", notes = "创新团队考核登记任务书下达")
	@SystemLogAnnotation(type = "创新团队考核登记",value = "创新团队考核登记任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseTalentTeamExamineRegister(@RequestBody TalentTeamExamineRegisterVo talentTeamExamineRegisterVo) {
		String id = talentTeamExamineRegisterService.releaseTalentTeamExamineRegister(talentTeamExamineRegisterVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 创新团队考核登记待办
	 *@param talentTeamExamineRegisterVo 创新团队考核登记 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "创新团队考核登记待办", notes = "创新团队考核登记待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody TalentTeamExamineRegisterVo talentTeamExamineRegisterVo) {
		PageInfo<TalentTeamExamineRegister>  talentTeamExamineRegister=talentTeamExamineRegisterService.todoList(talentTeamExamineRegisterVo);
		return RestApiResponse.ok(talentTeamExamineRegister);
	}
	/**
	 *@Description: 创新团队考核登记已办
	 *@param talentTeamExamineRegisterVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "创新团队考核登记已办", notes = "创新团队考核登记已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody TalentTeamExamineRegisterVo talentTeamExamineRegisterVo) {
		PageInfo<TalentTeamExamineRegister>  talentTeamExamineRegister=talentTeamExamineRegisterService.finishedList(talentTeamExamineRegisterVo);
		return RestApiResponse.ok(talentTeamExamineRegister);
	}
}
