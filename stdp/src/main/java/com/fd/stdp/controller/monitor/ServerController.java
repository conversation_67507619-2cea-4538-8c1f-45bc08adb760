package com.fd.stdp.controller.monitor;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fd.stdp.beans.monitor.Server;
import com.fd.stdp.common.RestApiResponse;

/**
 * 服务器监控
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/server")
public class ServerController
{
    @PreAuthorize("hasAuthority('monitor:server:list')")
    @GetMapping()
    public RestApiResponse<?> getInfo() throws Exception
    {
        Server server = new Server();
        server.copyTo();
        return RestApiResponse.ok(server);
    }
}
