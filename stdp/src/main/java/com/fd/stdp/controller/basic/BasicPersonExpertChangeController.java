package com.fd.stdp.controller.basic;

import com.fd.stdp.beans.tech.TechAchievementSell;
import com.fd.stdp.beans.tech.vo.TechAchievementSellVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonExpertChange;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertChangeVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.basic.BasicPersonExpertChangeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 专家变更表
 *@Author: wangsh
 *@Date: 2022-06-23 15:07:54
 */
@RestController
@RequestMapping("/basic/basicPersonExpertChange")
@Api(value="专家变更表", description="专家变更表")
public class BasicPersonExpertChangeController  extends BaseController {

	@Autowired
	private BasicPersonExpertChangeService basicPersonExpertChangeService;
	
	private final String PER_PREFIX = "basic:but:expert:";
	
	/**
	 *@Description: 新增专家变更表
	 *@param basicPersonExpertChangeVo 专家变更表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增专家变更表", notes = "新增专家变更表")
	@SystemLogAnnotation(type = "专家变更表",value = "新增专家变更表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveBasicPersonExpertChange(@RequestBody BasicPersonExpertChangeVo basicPersonExpertChangeVo) {
	    if(StringUtils.isBlank(basicPersonExpertChangeVo.getOrgName())) {
            basicPersonExpertChangeVo.setOrgName(getCurrentOrgName());
            basicPersonExpertChangeVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = basicPersonExpertChangeService.saveOrUpdateBasicPersonExpertChange(basicPersonExpertChangeVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改专家变更表
	 *@param basicPersonExpertChange 专家变更表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改专家变更表", notes = "修改专家变更表")
	@SystemLogAnnotation(type = "专家变更表",value = "修改专家变更表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateBasicPersonExpertChange(@RequestBody BasicPersonExpertChangeVo basicPersonExpertChange) {
		String id = basicPersonExpertChangeService.saveOrUpdateBasicPersonExpertChange(basicPersonExpertChange);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除专家变更表(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除专家变更表", notes = "删除专家变更表")
	@SystemLogAnnotation(type = "专家变更表",value = "删除专家变更表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteBasicPersonExpertChange(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		basicPersonExpertChangeService.deleteBasicPersonExpertChange(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除专家变更表(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除专家变更表", notes = "删除专家变更表")
	@SystemLogAnnotation(type = "专家变更表",value = "删除专家变更表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiBasicPersonExpertChange(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		basicPersonExpertChangeService.deleteMultiBasicPersonExpertChange(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询专家变更表详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询专家变更表详情", notes = "查询专家变更表详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		BasicPersonExpertChange  basicPersonExpertChange=basicPersonExpertChangeService.findById(id);
		return RestApiResponse.ok(basicPersonExpertChange);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			BasicPersonExpertChangeVo vo = new BasicPersonExpertChangeVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<BasicPersonExpertChange> pageInfo = (PageInfo<BasicPersonExpertChange>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
	
	/**
	 *@Description: 分页查询专家变更表
	 *@param basicPersonExpertChangeVo 专家变更表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询专家变更表", notes = "分页查询专家变更表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody BasicPersonExpertChangeVo basicPersonExpertChangeVo) {
		PageInfo<BasicPersonExpertChange>  basicPersonExpertChange=basicPersonExpertChangeService.findPageByQuery(basicPersonExpertChangeVo);
		return RestApiResponse.ok(basicPersonExpertChange);
	}

	/**
	 *@Description: 专家变更表提交
	 *@param basicPersonExpertChangeVo 专家变更表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "专家变更表提交", notes = "专家变更表提交")
	@SystemLogAnnotation(type = "专家变更表",value = "专家变更表提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitBasicPersonExpertChange(@RequestBody BasicPersonExpertChangeVo basicPersonExpertChangeVo) {
	    if(StringUtils.isBlank(basicPersonExpertChangeVo.getOrgName())) {
            basicPersonExpertChangeVo.setOrgName(getCurrentOrgName());
            basicPersonExpertChangeVo.setOrgCode(getLoginUser().getAreaCode());
		}
		String id = basicPersonExpertChangeService.submitBasicPersonExpertChange(basicPersonExpertChangeVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 专家变更表审核
	 *@param basicPersonExpertChangeVo 专家变更表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "专家变更表审核", notes = "专家变更表审核")
	@SystemLogAnnotation(type = "专家变更表",value = "专家变更表审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditBasicPersonExpertChange(@RequestBody BasicPersonExpertChangeVo basicPersonExpertChangeVo) {
		String id = basicPersonExpertChangeService.auditBasicPersonExpertChange(basicPersonExpertChangeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 专家变更表退回
	 *@param basicPersonExpertChangeVo 专家变更表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "专家变更表退回", notes = "专家变更表退回")
	@SystemLogAnnotation(type = "专家变更表",value = "专家变更表退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackBasicPersonExpertChange(@RequestBody BasicPersonExpertChangeVo basicPersonExpertChangeVo) {
		String id = basicPersonExpertChangeService.sendBackBasicPersonExpertChange(basicPersonExpertChangeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 专家变更表任务书下达
	 *@param basicPersonExpertChangeVo 专家变更表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "专家变更表任务书下达", notes = "专家变更表任务书下达")
	@SystemLogAnnotation(type = "专家变更表",value = "专家变更表任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseBasicPersonExpertChange(@RequestBody BasicPersonExpertChangeVo basicPersonExpertChangeVo) {
		String id = basicPersonExpertChangeService.releaseBasicPersonExpertChange(basicPersonExpertChangeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 专家变更表待办
	 *@param vo 专家变更表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "专家变更表待办", notes = "专家变更表待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody BasicPersonExpertChangeVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = basicPersonExpertChangeService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = basicPersonExpertChangeService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = basicPersonExpertChangeService.endList(vo);
		} else {
			list = basicPersonExpertChangeService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 专家变更表已办
	 *@param basicPersonExpertChangeVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "专家变更表已办", notes = "专家变更表已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody BasicPersonExpertChangeVo basicPersonExpertChangeVo) {
		PageInfo<BasicPersonExpertChange>  basicPersonExpertChange=basicPersonExpertChangeService.finishedList(basicPersonExpertChangeVo);
		return RestApiResponse.ok(basicPersonExpertChange);
	}
	
	/**
	 *@Description: 专家变更表已完成
	 *@param basicPersonExpertChangeVo 专家变更表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "专家变更表已完成", notes = "专家变更表已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody BasicPersonExpertChangeVo basicPersonExpertChangeVo) {
		PageInfo<BasicPersonExpertChange>  basicPersonExpertChange=basicPersonExpertChangeService.endList(basicPersonExpertChangeVo);
		return RestApiResponse.ok(basicPersonExpertChange);
	}
}
