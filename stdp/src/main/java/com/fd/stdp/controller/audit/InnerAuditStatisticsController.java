package com.fd.stdp.controller.audit;

import com.fd.stdp.beans.basic.BasicManageOrg;
import com.fd.stdp.dao.basic.BasicManageOrgMapper;
import com.fd.stdp.dao.basic.BasicScienceOrgMapper;
import com.fd.stdp.enums.QueryTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.audit.InnerAuditStatistics;
import com.fd.stdp.beans.audit.vo.InnerAuditStatisticsVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.audit.InnerAuditStatisticsService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 *@Description: 内审统计表2
 *@Author: wangsh
 *@Date: 2022-02-22 17:30:52
 */
@RestController
@RequestMapping("/audit/innerAuditStatistics")
@Api(value="内审数据采集表", description="内审数据采集表")
public class InnerAuditStatisticsController  extends BaseController {

	@Autowired
	private InnerAuditStatisticsService innerAuditStatisticsService;
	
	private final String PER_PREFIX = "but:innerAudit:statistics:";
	
	/**
	 *@Description: 新增内审统计表2
	 *@param innerAuditStatisticsVo 内审统计表2数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增内审统计表2", notes = "新增内审统计表2")
	@SystemLogAnnotation(type = "内审统计表2",value = "新增内审统计表2")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInnerAuditStatistics(@RequestBody InnerAuditStatisticsVo innerAuditStatisticsVo) {
		if(innerAuditStatisticsVo.getOrgName() == null) {
			innerAuditStatisticsVo.setOrgName(getCurrentOrgName());
			innerAuditStatisticsVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innerAuditStatisticsService.saveOrUpdateInnerAuditStatistics(innerAuditStatisticsVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改内审统计表2
	 *@param innerAuditStatistics 内审统计表2数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改内审统计表2", notes = "修改内审统计表2")
	@SystemLogAnnotation(type = "内审统计表2",value = "修改内审统计表2")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInnerAuditStatistics(@RequestBody InnerAuditStatisticsVo innerAuditStatistics) {
		String id = innerAuditStatisticsService.saveOrUpdateInnerAuditStatistics(innerAuditStatistics);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除内审统计表2(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除内审统计表2", notes = "删除内审统计表2")
	@SystemLogAnnotation(type = "内审统计表2",value = "删除内审统计表2")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteInnerAuditStatistics(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		innerAuditStatisticsService.deleteInnerAuditStatistics(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除内审统计表2(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除内审统计表2", notes = "删除内审统计表2")
	@SystemLogAnnotation(type = "内审统计表2",value = "删除内审统计表2")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiInnerAuditStatistics(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innerAuditStatisticsService.deleteMultiInnerAuditStatistics(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询内审统计表2详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询内审统计表2详情", notes = "查询内审统计表2详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InnerAuditStatistics  innerAuditStatistics=innerAuditStatisticsService.findById(id);
		return RestApiResponse.ok(innerAuditStatistics);
	}
	
	/**
	 *@Description: 分页查询内审统计表2
	 *@param innerAuditStatisticsVo 内审统计表2 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询内审统计表2", notes = "分页查询内审统计表2")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnerAuditStatisticsVo innerAuditStatisticsVo) {
		PageInfo<InnerAuditStatistics>  innerAuditStatistics=innerAuditStatisticsService.findPageByQuery(innerAuditStatisticsVo);
		return RestApiResponse.ok(innerAuditStatistics);
	}

	/**
	 *@Description: 分页查询内审统计表  处室统计用
	 *@param innerAuditStatisticsVo 内审统计表2 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findOfficePageByQuery")
	@ApiOperation(value = "分页查询内审统计表2", notes = "分页查询内审统计表2")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findOfficePageByQuery(@RequestBody InnerAuditStatisticsVo innerAuditStatisticsVo) {
		PageInfo<InnerAuditStatistics>  innerAuditStatistics=innerAuditStatisticsService.findOfficePageByQuery(innerAuditStatisticsVo);
		return RestApiResponse.ok(innerAuditStatistics);
	}

	/**
	 *@Description: 内审统计表2提交
	 *@param innerAuditStatisticsVo 内审统计表2数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "内审统计表2提交", notes = "内审统计表2提交")
	@SystemLogAnnotation(type = "内审统计表2",value = "内审统计表2提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitInnerAuditStatistics(@RequestBody InnerAuditStatisticsVo innerAuditStatisticsVo) {
		if(innerAuditStatisticsVo.getOrgName() == null) {
			innerAuditStatisticsVo.setOrgName(getCurrentOrgName());
			innerAuditStatisticsVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innerAuditStatisticsService.submitInnerAuditStatistics(innerAuditStatisticsVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 内审统计表2审核
	 *@param innerAuditStatisticsVo 内审统计表2数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "内审统计表2审核", notes = "内审统计表2审核")
	@SystemLogAnnotation(type = "内审统计表2",value = "内审统计表2审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditInnerAuditStatistics(@RequestBody InnerAuditStatisticsVo innerAuditStatisticsVo) {
		String id = innerAuditStatisticsService.auditInnerAuditStatistics(innerAuditStatisticsVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 内审统计表2退回
	 *@param id 内审统计表2数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "内审统计表2退回", notes = "内审统计表2退回")
	@SystemLogAnnotation(type = "内审统计表2",value = "内审统计表2退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackInnerAuditStatistics(@RequestBody String id) {
		InnerAuditStatisticsVo vo = new InnerAuditStatisticsVo();
		vo.setId(id);
		innerAuditStatisticsService.sendBackInnerAuditStatistics(vo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 内审统计表2任务书下达
	 *@param innerAuditStatisticsVo 内审统计表2数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "内审统计表2任务书下达", notes = "内审统计表2任务书下达")
	@SystemLogAnnotation(type = "内审统计表2",value = "内审统计表2任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseInnerAuditStatistics(@RequestBody InnerAuditStatisticsVo innerAuditStatisticsVo) {
		String id = innerAuditStatisticsService.releaseInnerAuditStatistics(innerAuditStatisticsVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 内审统计表2待办
	 *@param vo 内审统计表2 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "内审统计表2待办", notes = "内审统计表2待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody InnerAuditStatisticsVo vo) {
		//innerAuditStatisticsVo.setOrgName(getCurrentOrgName());
		//innerAuditStatisticsVo.setOrgCode(getCurrentScienceOrgId());
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = innerAuditStatisticsService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = innerAuditStatisticsService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = innerAuditStatisticsService.endList(vo);
		} else {
			list = innerAuditStatisticsService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 内审统计表2已办
	 *@param innerAuditStatisticsVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "内审统计表2已办", notes = "内审统计表2已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody InnerAuditStatisticsVo innerAuditStatisticsVo) {
		//innerAuditStatisticsVo.setOrgName(getCurrentOrgName());
		//innerAuditStatisticsVo.setOrgCode(getCurrentScienceOrgId());
		PageInfo<InnerAuditStatistics>  innerAuditStatistics=innerAuditStatisticsService.finishedList(innerAuditStatisticsVo);
		return RestApiResponse.ok(innerAuditStatistics);
	}
	
	/**
	 *@Description: 内审统计表2已完成
	 *@param innerAuditStatisticsVo 内审统计表2 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "内审统计表2已完成", notes = "内审统计表2已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody InnerAuditStatisticsVo innerAuditStatisticsVo) {
		PageInfo<InnerAuditStatistics>  innerAuditStatistics=innerAuditStatisticsService.endList(innerAuditStatisticsVo);
		return RestApiResponse.ok(innerAuditStatistics);
	}

	/**
	 *@Description: 获取可填报的机构列表
	 *@param innerAuditStatisticsVo 内审统计表2 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/applyOrg")
	@ApiOperation(value = "采集表可选择单位", notes = "采集表可选择单位")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> applyOrg(@RequestBody InnerAuditStatisticsVo innerAuditStatisticsVo) {
		PageInfo<BasicManageOrg>  innerAuditStatistics=innerAuditStatisticsService.applyOrg(innerAuditStatisticsVo);
		return RestApiResponse.ok(innerAuditStatistics);
	}

	/**
	 *@Description: 获取可填报的处室列表
	 *@param innerAuditStatisticsVo 内审统计表2 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/applyDepartment")
	@ApiOperation(value = "获取可填报的处室列表", notes = "获取可填报的处室列表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> applyDepartment(@RequestBody InnerAuditStatisticsVo innerAuditStatisticsVo) {
		PageInfo<BasicManageOrg>  innerAuditStatistics=innerAuditStatisticsService.applyDepartment(innerAuditStatisticsVo);
		return RestApiResponse.ok(innerAuditStatistics);
	}

	/**
	 *@Description: 采集表导出
	 *@param innerAuditStatisticsVo 内审统计表2 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/export")
	@ApiOperation(value = "采集表导出", notes = "采集表导出")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public void export(@RequestBody InnerAuditStatisticsVo innerAuditStatisticsVo, HttpServletResponse response) {
		innerAuditStatisticsService.export(innerAuditStatisticsVo, response);
	}
}
