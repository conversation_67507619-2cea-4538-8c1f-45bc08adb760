package com.fd.stdp.controller.project;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectNumberSetting;
import com.fd.stdp.beans.project.vo.ProjectNumberSettingVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectNumberSettingService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 申报编号配置
 *@Author: wangsh
 *@Date: 2022-01-06 10:06:23
 */
@RestController
@RequestMapping("/project/projectNumberSetting")
@Api(value="申报编号配置", description="申报编号配置")
public class ProjectNumberSettingController  extends BaseController {

	@Autowired
	private ProjectNumberSettingService projectNumberSettingService;
	
	private final String PER_PREFIX = "but:project:numberSetting:";
	
	/**
	 *@Description: 新增申报编号配置
	 *@param projectNumberSetting 申报编号配置数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增申报编号配置", notes = "新增申报编号配置")
	@SystemLogAnnotation(type = "申报编号配置",value = "新增申报编号配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveProjectNumberSetting(@RequestBody ProjectNumberSetting projectNumberSetting) {
		String id = projectNumberSettingService.saveOrUpdateProjectNumberSetting(projectNumberSetting);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改申报编号配置
	 *@param projectNumberSetting 申报编号配置数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改申报编号配置", notes = "修改申报编号配置")
	@SystemLogAnnotation(type = "申报编号配置",value = "修改申报编号配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateProjectNumberSetting(@RequestBody ProjectNumberSetting projectNumberSetting) {
		String id = projectNumberSettingService.saveOrUpdateProjectNumberSetting(projectNumberSetting);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除申报编号配置(判断 关联数据是否可以删除)
	 *@param vo
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除申报编号配置", notes = "删除申报编号配置")
	@SystemLogAnnotation(type = "申报编号配置",value = "删除申报编号配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteProjectNumberSetting(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		projectNumberSettingService.deleteMultiProjectNumberSetting(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询申报编号配置详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询申报编号配置详情", notes = "查询申报编号配置详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		ProjectNumberSetting  projectNumberSetting=projectNumberSettingService.findById(id);
		return RestApiResponse.ok(projectNumberSetting);
	}
	
	/**
	 *@Description: 分页查询申报编号配置
	 *@param projectNumberSettingVo 申报编号配置 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询申报编号配置", notes = "分页查询申报编号配置")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody ProjectNumberSettingVo projectNumberSettingVo) {
		PageInfo<ProjectNumberSetting>  projectNumberSetting=projectNumberSettingService.findPageByQuery(projectNumberSettingVo);
		return RestApiResponse.ok(projectNumberSetting);
	}
	
}
