package com.fd.stdp.controller.basic;

//import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.fd.stdp.dao.basic.BasicScienceOrgMapper;
import com.fd.stdp.util.ExcelUtils;
//import com.fd.stdp.util.LocalImportUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.basic.vo.BasicScienceOrgVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.basic.BasicScienceOrgService;
import com.fd.stdp.service.sys.SysUserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import tk.mybatis.mapper.entity.Example;

/**
 * 机构基础信息Controller
 *
 * <AUTHOR>
 * @date 2021-11-09
 */
@RestController
@RequestMapping("/basic/scienceOrg")
@Api(value = "机构基础信息", description = "机构基础信息")
public class BasicScienceOrgController extends BaseController {
    @Autowired
    private BasicScienceOrgService basicScienceOrgService;
    @Autowired
    private BasicScienceOrgMapper basicScienceOrgMapper;
    @Autowired
    private SysUserService sysUserService;

    private final String PER_PREFIX = "btn:basic:scienceOrg:";

    /**
     * @param basicScienceOrgVo 机构基础信息数据 json
     * @return RestApiResponse<?>
     * @Description: 新增机构基础信息
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增机构基础信息", notes = "新增机构基础信息")
    @SystemLogAnnotation(type = "机构基础信息", value = "新增机构基础信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveBasicScienceOrg(@RequestBody BasicScienceOrgVo basicScienceOrgVo) {
        String id = basicScienceOrgService.saveOrUpdateBasicScienceOrg(basicScienceOrgVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param basicScienceOrgVo 机构基础信息数据 json
     * @return RestApiResponse<?>
     * @Description: 修改机构基础信息
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改机构基础信息", notes = "修改机构基础信息")
    @SystemLogAnnotation(type = "机构基础信息", value = "修改机构基础信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateBasicScienceOrg(@RequestBody BasicScienceOrgVo basicScienceOrgVo) {
        String id = basicScienceOrgService.saveOrUpdateBasicScienceOrg(basicScienceOrgVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param ids
     * @return RestApiResponse<?>
     * @Description: 批量删除机构基础信息(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除机构基础信息", notes = "批量删除机构基础信息")
    @SystemLogAnnotation(type = "机构基础信息", value = "批量删除机构基础信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteBasicScienceOrg(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        basicScienceOrgService.deleteBasicScienceOrg(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询机构基础信息详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询机构基础信息详情", notes = "查询机构基础信息详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        BasicScienceOrgVo basicScienceOrgVo = basicScienceOrgService.findById(id);
        return RestApiResponse.ok(basicScienceOrgVo);
    }

    /**
     * @param basicScienceOrgVo 机构基础信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询机构基础信息
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询机构基础信息", notes = "分页查询机构基础信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody BasicScienceOrgVo basicScienceOrgVo) {
        PageInfo<BasicScienceOrg> basicScienceOrg = basicScienceOrgService.findPageByQuery(basicScienceOrgVo);
        return RestApiResponse.ok(basicScienceOrg);
    }

    @GetMapping("/rest/localImport")
    public void localImport(){
//        String filepath = "D:\\doc\\1-4全省质量技术基础支撑机构基础情况表-全省汇总-2022导入表.xlsx";
//        try {
//            List<BasicScienceOrgVo> list = LocalImportUtil.parseFileT(filepath, new BasicScienceOrgVo(), 2);
//            list.forEach(b->{
//                b.setOrgCode(b.getOrgUscc());
//                Example example = new Example(BasicScienceOrg.class);
//                example.createCriteria().andEqualTo("orgName", b.getOrgName());
//                List<BasicScienceOrg> list1 = basicScienceOrgMapper.selectByExample(example);
//                if(CollectionUtils.isEmpty(list1)){
//                    basicScienceOrgService.saveOrUpdateBasicScienceOrg(b);
//                }
//                else if (list1.size() == 1) {
//                    BasicScienceOrg b1 = list1.get(0);
//                    // BeanUtils.copyProperties(b1, b);
//                    b.setId(b1.getId());
//                    basicScienceOrgService.saveOrUpdateBasicScienceOrg(b);
//                } else {
//                    logger.error("库中存在重复数据" + b.getOrgName());
//                }
//            });
//            System.out.println(JSONObject.toJSONString(list));
//        } catch (InstantiationException e) {
//            e.printStackTrace();
//        } catch (IllegalAccessException e) {
//            e.printStackTrace();
//        } catch (InvocationTargetException e) {
//            e.printStackTrace();
//        }
    }

    /**
     * @param basicScienceOrgVo 机构查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询机构列表（只返回机构名称和机构代码）
     * @Author: zgx
     */
    @PostMapping("/findAllOrgList")
    @ApiOperation(value = "分页查询机构列表", notes = "分页查询机构列表（只返回机构名称和机构代码）")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findAllOrgList')")
    public RestApiResponse<?> findAllOrgList(@RequestBody BasicScienceOrgVo basicScienceOrgVo) {
        PageInfo<BasicScienceOrg> orgList = basicScienceOrgService.findOrgListByPage(basicScienceOrgVo);
        return RestApiResponse.ok(orgList);
    }

    /**
     * @return RestApiResponse<?>
     * @Description: 用户绑定机构
     * @Author: zgx
     */
    @PostMapping("/bindUserToOrg")
    @ApiOperation(value = "用户绑定机构", notes = "用户绑定机构")
    @SystemLogAnnotation(type = "机构基础信息", value = "用户绑定机构")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "bindUserToOrg')")
    public RestApiResponse<?> bindUserToOrg(@RequestBody BasicScienceOrgVo basicScienceOrgVo) {
        sysUserService.bindUserToOrg(basicScienceOrgVo);
        return RestApiResponse.ok("绑定成功");
    }

}
