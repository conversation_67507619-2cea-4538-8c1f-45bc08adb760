package com.fd.stdp.controller.project;

import java.util.List;
import java.util.Map;

import com.fd.stdp.beans.project.ProjectApplyExpertExtract;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertExtractVo;
import com.fd.stdp.service.project.ProjectApplyExpertExtractService;
import com.fd.stdp.util.expertExtract.beans.fegn.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 专家抽取任务Controller
 *
 * <AUTHOR>
 * @date 2025-03-18
 */
@RestController
@RequestMapping("/project/expert")
@Api(value = "专家抽取任务", description = "专家抽取任务")
public class ProjectApplyExpertExtractController extends BaseController {
    @Autowired
    private ProjectApplyExpertExtractService projectApplyExpertExtractService;

    private final String PER_PREFIX = "btn:stdp:extract:";

    /**
     * @param projectApplyExpertExtract 专家抽取任务数据 json
     * @return RestApiResponse<?>
     * @Description: 新增专家抽取任务
     * @Author: zhangYu
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增专家抽取任务", notes = "新增专家抽取任务")
    @SystemLogAnnotation(type = "专家抽取任务", value = "新增专家抽取任务")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveProjectApplyExpertExtract(@RequestBody ProjectApplyExpertExtract projectApplyExpertExtract) {
        String id = projectApplyExpertExtractService.saveOrUpdateProjectApplyExpertExtract(projectApplyExpertExtract);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectApplyExpertExtract 专家抽取任务数据 json
     * @return RestApiResponse<?>
     * @Description: 修改专家抽取任务
     * @Author: zhangYu
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改专家抽取任务", notes = "修改专家抽取任务")
    @SystemLogAnnotation(type = "专家抽取任务", value = "修改专家抽取任务")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateProjectApplyExpertExtract(@RequestBody ProjectApplyExpertExtract projectApplyExpertExtract) {
        String id = projectApplyExpertExtractService.saveOrUpdateProjectApplyExpertExtract(projectApplyExpertExtract);
        return RestApiResponse.ok(id);
    }

    /**
     * @param ids
     * @return RestApiResponse<?>
     * @Description: 批量删除专家抽取任务(判断 关联数据是否可以删除)
     * @Author: zhangYu
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除专家抽取任务", notes = "批量删除专家抽取任务")
    @SystemLogAnnotation(type = "专家抽取任务", value = "批量删除专家抽取任务")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteProjectApplyExpertExtract(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        projectApplyExpertExtractService.deleteProjectApplyExpertExtract(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询专家抽取任务详情
     * @Author: zhangYu
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询专家抽取任务详情", notes = "查询专家抽取任务详情")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ProjectApplyExpertExtractVo vo = projectApplyExpertExtractService.findById(id);
        return RestApiResponse.ok(vo);
    }

    /**
     * @param projectApplyExpertExtractVo 专家抽取任务 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询专家抽取任务
     * @Author: zhangYu
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询专家抽取任务", notes = "分页查询专家抽取任务")
    public RestApiResponse<?> findPageByQuery(@RequestBody ProjectApplyExpertExtractVo projectApplyExpertExtractVo) {
        List<ProjectApplyExpertExtractVo> voList = projectApplyExpertExtractService.findPageByQuery(projectApplyExpertExtractVo);
        return RestApiResponse.ok(new PageInfo<ProjectApplyExpertExtractVo>(voList));
    }


    /**
     * @param restBusiRandom 抽取专家参数
     * @return RestApiResponse<?>
     * @Description: 抽取专家
     * @Author: zhangYu
     */
    @PostMapping("/extract")
    @ApiOperation(value = "抽取专家", notes = "抽取专家")
    public RestApiResponse<?> extract(@RequestBody RestBusiRandom restBusiRandom) {
        ProjectApplyExpertExtract projectApplyExpertExtract = projectApplyExpertExtractService.extract(restBusiRandom);
        return RestApiResponse.ok(projectApplyExpertExtract);
    }


    /**
     * @param applyId
     * @return ProjectApplyExpertExtract
     * @Description: 根据applyId查询专家抽取任务
     * @Author: zhangYu
     */
    @GetMapping("/findByApplyId")
    @ApiOperation(value = "根据ApplyId查询抽取任务详情", notes = "根据AppId查询抽取任务详情")
    public RestApiResponse<?> findByApplyId(@RequestParam("applyId") String applyId) {
        ProjectApplyExpertExtract projectApplyExpertExtract = projectApplyExpertExtractService.findByApplyId(applyId);
        return RestApiResponse.ok(projectApplyExpertExtract);
    }

    @PostMapping("/addCondition")
    @ApiOperation(value = "追加抽取专家", notes = "追加抽取专家")
    public RestApiResponse<?> addCondition(@RequestBody RestBusiRandom restBusiRandom) {
        BusiRandomVo vo = projectApplyExpertExtractService.addCondition(restBusiRandom);
        return RestApiResponse.ok(vo);
    }


    /**
     * 查询专家库字典
     *
     * @param vo
     * @return
     */
    @PostMapping("/findDictItemByDictType")
    @ApiOperation(value = "查询专家库字典", notes = "查询专家库字典")
    public RestApiResponse<?> findDictItemByDictType(@RequestBody BusiRandomExpert vo) {
        List<SysDictItem> list = projectApplyExpertExtractService.findDictItemByDictType(vo);
        return RestApiResponse.ok(list);
    }


    /**
     * 查询工作单位（回避条件）
     *
     * @param vo
     * @return
     */
    @PostMapping("/findRecusalUnitListByQuery")
    @ApiOperation(value = "查询工作单位（回避条件）", notes = "查询工作单位（回避条件）")
    public RestApiResponse<?> findRecusalUnitListByQuery(@RequestBody BusiRandomVo vo) {
        List<String> list = projectApplyExpertExtractService.findRecusalUnitListByQuery(vo);
        return RestApiResponse.ok(list);
    }

    /**
     * 查询专家（回避条件）
     *
     * @param vo
     * @return
     */
    @PostMapping("/findRecusalExportListByQuery")
    @ApiOperation(value = "查询专家（回避条件）", notes = "查询专家（回避条件）")
    public RestApiResponse<?> findRecusalExportListByQuery(@RequestBody BusiRandomVo vo) {
        List<String> list = projectApplyExpertExtractService.findRecusalExportListByQuery(vo);
        return RestApiResponse.ok(list);
    }


    /**
     * 查询专家（回避条件）
     *
     * @param vo
     * @return
     */
    @PostMapping("/selectDictItemByDictType")
    @ApiOperation(value = "查询领域标签", notes = "查询领域标签")
    public RestApiResponse<?> selectDictItemByDictType(@RequestBody BusiRandomExpert vo) {
        List<TreeSelect> list = projectApplyExpertExtractService.selectDictItemByDictType(vo);
        return RestApiResponse.ok(list);
    }

    @PostMapping("/changeExpertStatus")
    @ApiOperation(value = "删除任务抽中的专家", notes = "删除任务抽中的专家")
    public RestApiResponse<?> changeExpertStatus(@RequestBody BusiRandomExpert vo) {
        Boolean aBoolean = projectApplyExpertExtractService.changeExpertStatus(vo);
        return RestApiResponse.ok(aBoolean);
    }

    /**
     * @param id 任务ID
     * @return RestApiResponse<?>
     * @Description: 获取抽取信息
     * @Author: zhangYu
     */
    @GetMapping("/findRandomInfoById")
    @ApiOperation(value = "获取抽取信息", notes = "获取抽取信息")
    public RestApiResponse<?> extract(@RequestParam("id") String id) {
        BusiRandomVo vo = projectApplyExpertExtractService.findRandomInfoById(id);
        return RestApiResponse.ok(vo);
    }


    /**
     * 确认选择专家
     *
     * @param id
     * @return
     */
    @GetMapping("/confirmExpert")
    @ApiOperation(value = "确认选择专家", notes = "确认选择专家")
    public RestApiResponse<?> confirmExpert(@RequestParam("id")  String id) {
        projectApplyExpertExtractService.confirmExpert(id , getCurrentUserName(), getCurrentRealName());
        return RestApiResponse.ok();
    }
}
