package com.fd.stdp.controller.talent;

import com.fd.stdp.beans.talent.TalentLeaderSubjectLeader;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectLeaderVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectContract;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectContractVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.talent.TalentLeaderSubjectContractService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 学科带头人建设任务书
 *@Author: wangsh
 *@Date: 2022-02-16 09:00:47
 */
@RestController
@RequestMapping("/talent/talentLeaderSubjectContract")
@Api(value="学科带头人建设任务书", description="学科带头人建设任务书")
public class TalentLeaderSubjectContractController  extends BaseController {

	@Autowired
	private TalentLeaderSubjectContractService talentLeaderSubjectContractService;
	
	private final String PER_PREFIX = "talent:but:contribute:";
	
	/**
	 *@Description: 新增学科带头人建设任务书
	 *@param vo 学科带头人建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增学科带头人建设任务书", notes = "新增学科带头人建设任务书")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "新增学科带头人建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveTalentLeaderSubjectContract(@RequestBody TalentLeaderSubjectContractVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentLeaderSubjectContractService.saveOrUpdateTalentLeaderSubjectContract(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改学科带头人建设任务书
	 *@param talentLeaderSubjectContract 学科带头人建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改学科带头人建设任务书", notes = "修改学科带头人建设任务书")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "修改学科带头人建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateTalentLeaderSubjectContract(@RequestBody TalentLeaderSubjectContractVo talentLeaderSubjectContract) {
		String id = talentLeaderSubjectContractService.saveOrUpdateTalentLeaderSubjectContract(talentLeaderSubjectContract);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除学科带头人建设任务书(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除学科带头人建设任务书", notes = "删除学科带头人建设任务书")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "删除学科带头人建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteTalentLeaderSubjectContract(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		talentLeaderSubjectContractService.deleteTalentLeaderSubjectContract(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除学科带头人建设任务书(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除学科带头人建设任务书", notes = "删除学科带头人建设任务书")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "删除学科带头人建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiTalentLeaderSubjectContract(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		talentLeaderSubjectContractService.deleteMultiTalentLeaderSubjectContract(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询学科带头人建设任务书详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询学科带头人建设任务书详情", notes = "查询学科带头人建设任务书详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		TalentLeaderSubjectContract  talentLeaderSubjectContract=talentLeaderSubjectContractService.findById(id);
		return RestApiResponse.ok(talentLeaderSubjectContract);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			TalentLeaderSubjectContractVo vo = new TalentLeaderSubjectContractVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<TalentLeaderSubjectContract> pageInfo = (PageInfo<TalentLeaderSubjectContract>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}


	/**
	 *@Description: 分页查询学科带头人建设任务书
	 *@param talentLeaderSubjectContractVo 学科带头人建设任务书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询学科带头人建设任务书", notes = "分页查询学科带头人建设任务书")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody TalentLeaderSubjectContractVo talentLeaderSubjectContractVo) {
		PageInfo<TalentLeaderSubjectContract>  talentLeaderSubjectContract=talentLeaderSubjectContractService.findPageByQuery(talentLeaderSubjectContractVo);
		return RestApiResponse.ok(talentLeaderSubjectContract);
	}

	/**
	 *@Description: 学科带头人建设任务书提交
	 *@param talentLeaderSubjectContractVo 学科带头人建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "学科带头人建设任务书提交", notes = "学科带头人建设任务书提交")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "学科带头人建设任务书提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitTalentLeaderSubjectContract(@RequestBody TalentLeaderSubjectContractVo vo) {
		vo.setOrgName(getCurrentOrgName());
		vo.setOrgCode(getCurrentScienceOrgId());
		String id = talentLeaderSubjectContractService.submitTalentLeaderSubjectContract(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 学科带头人建设任务书审核
	 *@param talentLeaderSubjectContractVo 学科带头人建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "学科带头人建设任务书审核", notes = "学科带头人建设任务书审核")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "学科带头人建设任务书审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditTalentLeaderSubjectContract(@RequestBody TalentLeaderSubjectContractVo talentLeaderSubjectContractVo) {
		String id = talentLeaderSubjectContractService.auditTalentLeaderSubjectContract(talentLeaderSubjectContractVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 学科带头人建设任务书退回
	 *@param talentLeaderSubjectContractVo 学科带头人建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "学科带头人建设任务书退回", notes = "学科带头人建设任务书退回")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "学科带头人建设任务书退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackTalentLeaderSubjectContract(@RequestBody TalentLeaderSubjectContractVo talentLeaderSubjectContractVo) {
		String id = talentLeaderSubjectContractService.sendBackTalentLeaderSubjectContract(talentLeaderSubjectContractVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 学科带头人建设任务书任务书下达
	 *@param talentLeaderSubjectContractVo 学科带头人建设任务书数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "学科带头人建设任务书任务书下达", notes = "学科带头人建设任务书任务书下达")
	@SystemLogAnnotation(type = "学科带头人建设任务书",value = "学科带头人建设任务书任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseTalentLeaderSubjectContract(@RequestBody TalentLeaderSubjectContractVo talentLeaderSubjectContractVo) {
		String id = talentLeaderSubjectContractService.releaseTalentLeaderSubjectContract(talentLeaderSubjectContractVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 学科带头人建设任务书待办
	 *@param vo 学科带头人建设任务书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "学科带头人建设任务书待办", notes = "学科带头人建设任务书待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody TalentLeaderSubjectContractVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = talentLeaderSubjectContractService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = talentLeaderSubjectContractService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = talentLeaderSubjectContractService.endList(vo);
		} else {
			list = talentLeaderSubjectContractService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 学科带头人建设任务书已办
	 *@param talentLeaderSubjectContractVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "学科带头人建设任务书已办", notes = "学科带头人建设任务书已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody TalentLeaderSubjectContractVo talentLeaderSubjectContractVo) {
		PageInfo<TalentLeaderSubjectContract>  talentLeaderSubjectContract=talentLeaderSubjectContractService.finishedList(talentLeaderSubjectContractVo);
		return RestApiResponse.ok(talentLeaderSubjectContract);
	}
	
	/**
	 *@Description: 学科带头人建设任务书已完成
	 *@param talentLeaderSubjectContractVo 学科带头人建设任务书 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "学科带头人建设任务书已完成", notes = "学科带头人建设任务书已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody TalentLeaderSubjectContractVo talentLeaderSubjectContractVo) {
		PageInfo<TalentLeaderSubjectContract>  talentLeaderSubjectContract=talentLeaderSubjectContractService.endList(talentLeaderSubjectContractVo);
		return RestApiResponse.ok(talentLeaderSubjectContract);
	}
}
