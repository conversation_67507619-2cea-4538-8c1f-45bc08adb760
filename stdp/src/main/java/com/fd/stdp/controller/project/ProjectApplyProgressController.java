package com.fd.stdp.controller.project;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.project.ProjectApplyProgress;
import com.fd.stdp.beans.project.vo.ProjectApplyProgressVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectApplyProgressService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 项目计划进度安排Controller
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
@RestController
@RequestMapping("/project/applyProgress")
@Api(value = "项目计划进度安排", description = "项目计划进度安排")
public class ProjectApplyProgressController extends BaseController {
    @Autowired
    private ProjectApplyProgressService projectApplyProgressService;

    private final String PER_PREFIX = "btn:project:applyProgress:";

    /**
     * @param projectApplyProgress 项目计划进度安排数据 json
     * @return RestApiResponse<?>
     * @Description: 新增项目计划进度安排
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增项目计划进度安排", notes = "新增项目计划进度安排")
    @SystemLogAnnotation(type = "项目计划进度安排", value = "新增项目计划进度安排")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveProjectApplyProgress(@RequestBody ProjectApplyProgress projectApplyProgress) {
        String id = projectApplyProgressService.saveOrUpdateProjectApplyProgress(projectApplyProgress);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectApplyProgress 项目计划进度安排数据 json
     * @return RestApiResponse<?>
     * @Description: 修改项目计划进度安排
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改项目计划进度安排", notes = "修改项目计划进度安排")
    @SystemLogAnnotation(type = "项目计划进度安排", value = "修改项目计划进度安排")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateProjectApplyProgress(@RequestBody ProjectApplyProgress projectApplyProgress) {
        String id = projectApplyProgressService.saveOrUpdateProjectApplyProgress(projectApplyProgress);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除项目计划进度安排(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除项目计划进度安排", notes = "批量删除项目计划进度安排")
    @SystemLogAnnotation(type = "项目计划进度安排", value = "批量删除项目计划进度安排")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteProjectApplyProgress(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        projectApplyProgressService.deleteProjectApplyProgress(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询项目计划进度安排详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询项目计划进度安排详情", notes = "查询项目计划进度安排详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ProjectApplyProgress projectApplyProgress = projectApplyProgressService.findById(id);
        return RestApiResponse.ok(projectApplyProgress);
    }

    /**
     * @param projectApplyProgressVo 项目计划进度安排 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询项目计划进度安排
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询项目计划进度安排", notes = "分页查询项目计划进度安排")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ProjectApplyProgressVo projectApplyProgressVo) {
        PageInfo<ProjectApplyProgress> projectApplyProgress = projectApplyProgressService.findPageByQuery(projectApplyProgressVo);
        return RestApiResponse.ok(projectApplyProgress);
    }

}
