package com.fd.stdp.controller.flowable;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fd.stdp.beans.flowable.FlwDeployForm;
import com.fd.stdp.beans.flowable.FlwForm;
import com.fd.stdp.beans.flowable.vo.FlwFormVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.service.flowable.FlwDeployFormService;
import com.fd.stdp.service.flowable.FlwFormService;
import com.fd.stdp.util.EasyExcelUtils;
import com.github.pagehelper.PageInfo;

/**
 * 流程表单Controller
 *
 * <AUTHOR>
 * @date 2021-04-03
 */
@RestController
@RequestMapping("/flowable/form")
public class FlwFormController extends BaseController {
	

    @Autowired
    private FlwFormService flwFormService;

    @Autowired
    private FlwDeployFormService flwDeployFormService;

    private final String prefix = "btn:flowable:form:";
    /**
     * 查询流程表单列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasAnyRole('SUPERADMIN')")
    public RestApiResponse<?> list(FlwFormVo flwFormVo) {
        PageInfo<FlwForm> list=flwFormService.findPageByQuery(flwFormVo);
        return RestApiResponse.ok(list);
    }

    /**
     * 导出流程表单列表
     */
    @SystemLogAnnotation(type = "流程表单",value = "导出流程表单")
    @GetMapping("/export")
    public void export(FlwForm flwForm) {
        List<FlwForm> list = flwFormService.findFlwFormList(flwForm);
        EasyExcelUtils.exportExcel(list, "流程表单", FlwForm.class,"流程表单.xlsx", response);
    }

    /**
     * 获取流程表单详细信息
     */
    @GetMapping(value = "/{formId}")
    public RestApiResponse<?> getInfo(@PathVariable("formId") String formId) {
        return RestApiResponse.ok(flwFormService.findById(formId));
    }

    /**
     * 新增流程表单
     */
    @PreAuthorize("hasAuthority('"+prefix+"add')")
    @SystemLogAnnotation(type = "流程表单",value = "新增流程表单")
    @PostMapping
    public RestApiResponse<?> add(@RequestBody FlwForm flwForm) {
    	flwFormService.saveOrUpdateFlwForm(flwForm);
        return RestApiResponse.ok();
    }

    /**
     * 修改流程表单
     */
    @PreAuthorize("hasAuthority('"+prefix+"edit')")
    @SystemLogAnnotation(type = "流程表单",value = "修改流程表单")
    @PutMapping
    public RestApiResponse<?> edit(@RequestBody FlwForm flwForm) {
    	flwFormService.saveOrUpdateFlwForm(flwForm);
        return RestApiResponse.ok();
    }

    /**
     * 删除流程表单
     */
    @SystemLogAnnotation(type = "流程表单",value = "批量删除流程表单")
    @DeleteMapping("/{formIds}")
    public RestApiResponse<?> remove(@PathVariable String[] formIds) {
        flwFormService.deleteFlwFormByIds(formIds);
        return RestApiResponse.ok();
    }


    /**
     * 挂载流程表单
     */
    @SystemLogAnnotation(type = "流程表单",value = "挂载流程表单")
    @PostMapping("/addDeployForm")
    public RestApiResponse<?> addDeployForm(@RequestBody FlwDeployForm flwDeployForm) {
    	flwDeployFormService.saveOrUpdateFlwDeployForm(flwDeployForm);
        return RestApiResponse.ok();
    }
}
