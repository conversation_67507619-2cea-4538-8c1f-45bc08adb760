package com.fd.stdp.controller.basic;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.basic.BasicScienceOrgAssets;
import com.fd.stdp.beans.basic.vo.BasicScienceOrgAssetsVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.basic.BasicScienceOrgAssetsService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 机构资产Controller
 *
 * <AUTHOR>
 * @date 2021-11-09
 */
@RestController
@RequestMapping("/basic/scienceOrgAssets")
@Api(value = "机构资产", description = "机构资产")
public class BasicScienceOrgAssetsController extends BaseController {
    @Autowired
    private BasicScienceOrgAssetsService basicScienceOrgAssetsService;

    private final String PER_PREFIX = "btn:basic:scienceOrgAssets:";

    /**
     * @param basicScienceOrgAssets 机构资产数据 json
     * @return RestApiResponse<?>
     * @Description: 新增机构资产
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增机构资产", notes = "新增机构资产")
    @SystemLogAnnotation(type = "机构资产", value = "新增机构资产")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveBasicScienceOrgAssets(@RequestBody BasicScienceOrgAssets basicScienceOrgAssets) {
        String id = basicScienceOrgAssetsService.saveOrUpdateBasicScienceOrgAssets(basicScienceOrgAssets);
        return RestApiResponse.ok(id);
    }

    /**
     * @param basicScienceOrgAssets 机构资产数据 json
     * @return RestApiResponse<?>
     * @Description: 修改机构资产
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改机构资产", notes = "修改机构资产")
    @SystemLogAnnotation(type = "机构资产", value = "修改机构资产")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateBasicScienceOrgAssets(@RequestBody BasicScienceOrgAssets basicScienceOrgAssets) {
        String id = basicScienceOrgAssetsService.saveOrUpdateBasicScienceOrgAssets(basicScienceOrgAssets);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除机构资产(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除机构资产", notes = "批量删除机构资产")
    @SystemLogAnnotation(type = "机构资产", value = "批量删除机构资产")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteBasicScienceOrgAssets(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        basicScienceOrgAssetsService.deleteBasicScienceOrgAssets(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询机构资产详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询机构资产详情", notes = "查询机构资产详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        BasicScienceOrgAssets basicScienceOrgAssets = basicScienceOrgAssetsService.findById(id);
        return RestApiResponse.ok(basicScienceOrgAssets);
    }

    /**
     * @param basicScienceOrgAssetsVo 机构资产 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询机构资产
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询机构资产", notes = "分页查询机构资产")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody BasicScienceOrgAssetsVo basicScienceOrgAssetsVo) {
        PageInfo<BasicScienceOrgAssets> basicScienceOrgAssets = basicScienceOrgAssetsService.findPageByQuery(basicScienceOrgAssetsVo);
        return RestApiResponse.ok(basicScienceOrgAssets);
    }

}
