package com.fd.stdp.controller.project;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.project.ProjectApplyDevices;
import com.fd.stdp.beans.project.vo.ProjectApplyDevicesVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectApplyDevicesService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 项目设备购置预算明细Controller
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
@RestController
@RequestMapping("/project/applyDevices")
@Api(value = "项目设备购置预算明细", description = "项目设备购置预算明细")
public class ProjectApplyDevicesController extends BaseController {
    @Autowired
    private ProjectApplyDevicesService projectApplyDevicesService;

    private final String PER_PREFIX = "btn:project:applyDevices:";

    /**
     * @param projectApplyDevices 项目设备购置预算明细数据 json
     * @return RestApiResponse<?>
     * @Description: 新增项目设备购置预算明细
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增项目设备购置预算明细", notes = "新增项目设备购置预算明细")
    @SystemLogAnnotation(type = "项目设备购置预算明细", value = "新增项目设备购置预算明细")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveProjectApplyDevices(@RequestBody ProjectApplyDevices projectApplyDevices) {
        String id = projectApplyDevicesService.saveOrUpdateProjectApplyDevices(projectApplyDevices);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectApplyDevices 项目设备购置预算明细数据 json
     * @return RestApiResponse<?>
     * @Description: 修改项目设备购置预算明细
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改项目设备购置预算明细", notes = "修改项目设备购置预算明细")
    @SystemLogAnnotation(type = "项目设备购置预算明细", value = "修改项目设备购置预算明细")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateProjectApplyDevices(@RequestBody ProjectApplyDevices projectApplyDevices) {
        String id = projectApplyDevicesService.saveOrUpdateProjectApplyDevices(projectApplyDevices);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除项目设备购置预算明细(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除项目设备购置预算明细", notes = "批量删除项目设备购置预算明细")
    @SystemLogAnnotation(type = "项目设备购置预算明细", value = "批量删除项目设备购置预算明细")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteProjectApplyDevices(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        projectApplyDevicesService.deleteProjectApplyDevices(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询项目设备购置预算明细详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询项目设备购置预算明细详情", notes = "查询项目设备购置预算明细详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ProjectApplyDevices projectApplyDevices = projectApplyDevicesService.findById(id);
        return RestApiResponse.ok(projectApplyDevices);
    }

    /**
     * @param projectApplyDevicesVo 项目设备购置预算明细 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询项目设备购置预算明细
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询项目设备购置预算明细", notes = "分页查询项目设备购置预算明细")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ProjectApplyDevicesVo projectApplyDevicesVo) {
        PageInfo<ProjectApplyDevices> projectApplyDevices = projectApplyDevicesService.findPageByQuery(projectApplyDevicesVo);
        return RestApiResponse.ok(projectApplyDevices);
    }

}
