package com.fd.stdp.controller.audit;

import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.audit.InnerAuditFirst;
import com.fd.stdp.beans.audit.vo.InnerAuditFirstVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.audit.InnerAuditFirstService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 *@Description: 内审统计表1
 *@Author: wangsh
 *@Date: 2022-02-22 16:49:05
 */
@RestController
@RequestMapping("/audit/innerAuditFirst")
@Api(value="内审统计表1", description="内审统计表1")
public class InnerAuditFirstController  extends BaseController {

	@Autowired
	private InnerAuditFirstService innerAuditFirstService;
	
	private final String PER_PREFIX = "but:innerAudit:first:";
	
	/**
	 *@Description: 新增内审统计表1
	 *@param innerAuditFirstVo 内审统计表1数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增内审统计表1", notes = "新增内审统计表1")
	@SystemLogAnnotation(type = "内审统计表1",value = "新增内审统计表1")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInnerAuditFirst(@RequestBody InnerAuditFirstVo innerAuditFirstVo) {
		if(StringUtils.isBlank(innerAuditFirstVo.getOrgName())) {
			innerAuditFirstVo.setOrgName(getCurrentOrgName());
			innerAuditFirstVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innerAuditFirstService.saveOrUpdateInnerAuditFirst(innerAuditFirstVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改内审统计表1
	 *@param innerAuditFirst 内审统计表1数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改内审统计表1", notes = "修改内审统计表1")
	@SystemLogAnnotation(type = "内审统计表1",value = "修改内审统计表1")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInnerAuditFirst(@RequestBody InnerAuditFirstVo innerAuditFirst) {
		String id = innerAuditFirstService.saveOrUpdateInnerAuditFirst(innerAuditFirst);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除内审统计表1(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除内审统计表1", notes = "删除内审统计表1")
	@SystemLogAnnotation(type = "内审统计表1",value = "删除内审统计表1")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteInnerAuditFirst(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		innerAuditFirstService.deleteInnerAuditFirst(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除内审统计表1(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除内审统计表1", notes = "删除内审统计表1")
	@SystemLogAnnotation(type = "内审统计表1",value = "删除内审统计表1")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiInnerAuditFirst(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innerAuditFirstService.deleteMultiInnerAuditFirst(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询内审统计表1详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询内审统计表1详情", notes = "查询内审统计表1详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InnerAuditFirst  innerAuditFirst=innerAuditFirstService.findById(id);
		return RestApiResponse.ok(innerAuditFirst);
	}
	
	/**
	 *@Description: 分页查询内审统计表1
	 *@param innerAuditFirstVo 内审统计表1 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询内审统计表1", notes = "分页查询内审统计表1")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnerAuditFirstVo innerAuditFirstVo) {
		PageInfo<InnerAuditFirst>  innerAuditFirst=innerAuditFirstService.findPageByQuery(innerAuditFirstVo);
		return RestApiResponse.ok(innerAuditFirst);
	}

	/**
	 *@Description: 修改内审统计表1
	 *@param innerAuditFirstVo 内审统计表1数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "修改内审统计表1", notes = "修改内审统计表1")
	@SystemLogAnnotation(type = "内审统计表1",value = "修改内审统计表1")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> submitInnerAuditFirst(@RequestBody InnerAuditFirstVo innerAuditFirstVo) {
		if(StringUtils.isBlank(innerAuditFirstVo.getOrgName())) {
			innerAuditFirstVo.setOrgName(getCurrentOrgName());
			innerAuditFirstVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innerAuditFirstService.submitInnerAuditFirst(innerAuditFirstVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 修改内审统计表1
	 *@param innerAuditFirstVo 内审统计表1数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "修改内审统计表1", notes = "修改内审统计表1")
	@SystemLogAnnotation(type = "内审统计表1",value = "修改内审统计表1")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> sendBackInnerAuditFirst(@RequestBody InnerAuditFirstVo innerAuditFirstVo) {
		String id = innerAuditFirstService.sendBackInnerAuditFirst(innerAuditFirstVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 内审统计表1待办
	 *@param vo 内审统计表1 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "内审统计表1待办", notes = "内审统计表1待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody InnerAuditFirstVo vo) {
//		if(StringUtils.isBlank(innerAuditFirstVo.getOrgName())) {
//			innerAuditFirstVo.setOrgName(getCurrentOrgName());
//			innerAuditFirstVo.setOrgCode(getCurrentScienceOrgId());
//		}
//		PageInfo<InnerAuditFirst>  innerAuditFirst=innerAuditFirstService.todoList(innerAuditFirstVo);
//		return RestApiResponse.ok(innerAuditFirst);
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = innerAuditFirstService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = innerAuditFirstService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = innerAuditFirstService.endList(vo);
		} else {
			list = innerAuditFirstService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 内审统计表1已办
	 *@param innerAuditFirstVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "内审统计表1已办", notes = "内审统计表1已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody InnerAuditFirstVo innerAuditFirstVo) {
		if(StringUtils.isBlank(innerAuditFirstVo.getOrgName())) {
			innerAuditFirstVo.setOrgName(getCurrentOrgName());
			innerAuditFirstVo.setOrgCode(getCurrentScienceOrgId());
		}
		PageInfo<InnerAuditFirst>  innerAuditFirst=innerAuditFirstService.finishedList(innerAuditFirstVo);
		return RestApiResponse.ok(innerAuditFirst);
	}


	/**
	 *@Description: 查询机构填报权限
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/auditOpen")
	@ApiOperation(value = "查询机构填报权限", notes = "查询机构填报权限")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> isAuditOpen() {
		return RestApiResponse.ok(innerAuditFirstService.isAuditOpen(getCurrentOrgName()));
	}

	/**
	 *@Description: 获取统计数据
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/recordStatistics")
	@ApiOperation(value = "获取统计数据", notes = "获取统计数据")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"statistics')")
	public RestApiResponse<?> statistics(@RequestBody InnerAuditFirstVo innerAuditFirstVo) {
		return RestApiResponse.ok(innerAuditFirstService.statistics(innerAuditFirstVo));
	}

	/**
	 *@Description: 导出统计数据
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/recordExport")
	@ApiOperation(value = "导出统计数据", notes = "导出统计数据")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"statistics')")
	public void export(@RequestBody InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response) {
		innerAuditFirstService.export(innerAuditFirstVo, response);
	}


	/**
	 *@Description: 获取统计数据
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/recordStatistics11")
	@ApiOperation(value = "获取统计数据", notes = "获取统计数据")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"statistics')")
	public RestApiResponse<?> statistics1(@RequestBody InnerAuditFirstVo innerAuditFirstVo) {
		return RestApiResponse.ok(innerAuditFirstService.statistics11(innerAuditFirstVo));
	}


	/**
	 *@Description: 获取统计数据
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/recordStatistics12")
	@ApiOperation(value = "获取统计数据", notes = "获取统计数据")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"statistics')")
	public RestApiResponse<?> statistics12(@RequestBody InnerAuditFirstVo innerAuditFirstVo) {
		return RestApiResponse.ok(innerAuditFirstService.statistics12(innerAuditFirstVo));
	}

	/**
	 *@Description: 导出统计数据
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/recordExport11")
	@ApiOperation(value = "导出统计数据", notes = "导出统计数据")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"statistics')")
	public void export11(@RequestBody InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response) {
		innerAuditFirstService.export11(innerAuditFirstVo, response);
	}
	/**
	 *@Description: 导出统计数据
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/recordExport12")
	@ApiOperation(value = "导出统计数据", notes = "导出统计数据")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"statistics')")
	public void export12(@RequestBody InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response) {
		innerAuditFirstService.export12(innerAuditFirstVo, response);
	}

	/**
	 *@Description: 获取统计数据
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/recordStatistics21")
	@ApiOperation(value = "获取统计数据", notes = "获取统计数据")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"statistics')")
	public RestApiResponse<?> statistics21(@RequestBody InnerAuditFirstVo innerAuditFirstVo) {
		return RestApiResponse.ok(innerAuditFirstService.statistics21(innerAuditFirstVo));
	}


	/**
	 *@Description: 获取统计数据
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/recordStatistics22")
	@ApiOperation(value = "获取统计数据", notes = "获取统计数据")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"statistics')")
	public RestApiResponse<?> statistics22(@RequestBody InnerAuditFirstVo innerAuditFirstVo) {
		return RestApiResponse.ok(innerAuditFirstService.statistics22(innerAuditFirstVo));
	}


	/**
	 *@Description: 导出统计数据
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/recordExport21")
	@ApiOperation(value = "导出统计数据", notes = "导出统计数据")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"statistics')")
	public void export21(@RequestBody InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response) {
		innerAuditFirstService.export21(innerAuditFirstVo, response);
	}
	/**
	 *@Description: 导出统计数据
	 *@param
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/recordExport22")
	@ApiOperation(value = "导出统计数据", notes = "导出统计数据")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"statistics')")
	public void export22(@RequestBody InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response) {
		innerAuditFirstService.export22(innerAuditFirstVo, response);
	}


	/**
	 *@Description: 获取历史内容
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findOldVal")
	@ApiOperation(value = "获取历史内容", notes = "获取历史内容")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findOldVal() {
		InnerAuditFirst innerAuditFirst=innerAuditFirstService.findOldVal();
		return RestApiResponse.ok(innerAuditFirst);
	}

}
