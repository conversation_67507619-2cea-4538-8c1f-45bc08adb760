package com.fd.stdp.controller.sys;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fd.stdp.beans.sys.SysRolePermission;
import com.fd.stdp.beans.sys.vo.PermissionVO;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.service.sys.RolePermissionService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Controller
@RequestMapping("/sys/userRole")
@Api(value = "角色权限管理", description = "角色权限接口")
public class RolePermissionController extends BaseController {
	@Autowired
	private RolePermissionService rolePermissionService;

	private final String prefix = "btn:sys:role:";

	@RepeatSubAnnotation
	@PostMapping(value = "/savePermissionByRoleId")
	@ResponseBody
	@ApiOperation(value = "保存角色权限", notes = "查询角色权限")
	@PreAuthorize("hasAuthority('" + prefix + "setPermission')")
	public RestApiResponse<?> savePermissionByRoleId(String roleId, String menuId,
			@RequestBody List<String> permissions) {
		this.rolePermissionService.savePermissions(roleId, menuId, permissions, PermissionVO.TYPE_BTN);
		return RestApiResponse.ok();

	}

	@RepeatSubAnnotation
	@PostMapping(value = "/savePermissionMenuByRoleId")
	@ResponseBody
	@ApiOperation(value = "保存角色菜单权限", notes = "查询角色菜单权限")
	@PreAuthorize("hasAuthority('" + prefix + "setMenu')")
	public RestApiResponse<?> savePermissionMenuByRoleId(String roleId, @RequestBody List<String> permissions) {
		this.rolePermissionService.savePermissions(roleId, null, permissions, PermissionVO.TYPE_MENU);
		return RestApiResponse.ok();

	}

	@GetMapping(value = "/listPermissionByRoleId")
	@ResponseBody
	@ApiOperation(value = "查询角色权限", notes = "查询角色权限")
	@PreAuthorize("hasAuthority('" + prefix + "permissionByRoleId')")
	public RestApiResponse<?> listPermissionByRoleId(String roleId) {
		List<SysRolePermission> dataList = this.rolePermissionService.listPermissions(roleId);
		return RestApiResponse.ok(dataList);
	}

}
