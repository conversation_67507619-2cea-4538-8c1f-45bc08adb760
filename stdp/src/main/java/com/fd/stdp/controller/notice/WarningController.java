package com.fd.stdp.controller.notice;

import com.fd.stdp.beans.notice.WarningBean;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.service.notice.WarningService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *@Description: 
 *@Author: wangsh
 *@Date: 2022-03-15 09:45:58
 */
@RestController
@RequestMapping("/notice/warning")
@Api(value="", description="")
public class WarningController extends BaseController {

	@Autowired
	private WarningService warningService;
	
	private final String PER_PREFIX = "but:notice:notice:";
		
	/**
	 *@Description: 分页查询
	 *@param warning  查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/projectWarning")
	@ApiOperation(value = "分页查询", notes = "分页查询")
	public RestApiResponse<?> projectWarning(@RequestBody WarningBean warning) {
		PageInfo<WarningBean> warnings = warningService.findProjectWarning(warning);
		return RestApiResponse.ok(warnings);
	}

	/**
	 *@Description: 分页查询
	 *@param warning  查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/innovationWarning")
	@ApiOperation(value = "分页查询", notes = "分页查询")
	public RestApiResponse<?> innovationWarning(@RequestBody WarningBean warning) {
		PageInfo<WarningBean> warnings = warningService.findInnovationWarning(warning);
		return RestApiResponse.ok(warnings);
	}

	/**
	 *@Description: 分页查询
	 *@param warning  查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/talentWarning")
	@ApiOperation(value = "分页查询", notes = "分页查询")
	public RestApiResponse<?> talentWarning(@RequestBody WarningBean warning) {
		PageInfo<WarningBean> warnings = warningService.findTalentWarning(warning);
		return RestApiResponse.ok(warnings);
	}
}
