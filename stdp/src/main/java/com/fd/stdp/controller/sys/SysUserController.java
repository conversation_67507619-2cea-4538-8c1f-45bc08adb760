package com.fd.stdp.controller.sys;

import com.fd.stdp.beans.appraisal.vo.AppraisalApplyVo;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.beans.sys.vo.SysUserVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.OperationLogAnnotation;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.redis.RedisUtil;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.constant.RedisConstant;
import com.fd.stdp.enums.UserTypeEnum;
import com.fd.stdp.service.sys.SysUserService;
import com.fd.stdp.util.EasyExcelUtils;
import com.fd.stdp.util.HttpUtils;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 系统用户表
 * @Author: qyj
 * @Date: 2020-07-05 16:25:12
 */
@RestController
@RequestMapping("/sys/sysUser")
@Api(value = "系统用户表", description = "系统用户表")
public class SysUserController extends BaseController {

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private RedisUtil redisUtil;
    
    private final String PER_PREFIX = "btn:sys:user:";


    /**
     * 
     * @param sysUser 系统用户表数据 json
     * @return RestApiResponse<?>
     * @Description: 新增系统用户表
     * @Author: qyj
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增系统用户表", notes = "新增系统用户表")
    @SystemLogAnnotation(type = "系统用户表", value = "新增系统用户表")
    @OperationLogAnnotation(notes = "添加{系统用户:username}", module = "系统用户", submodule = "系统用户管理")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    @PreAuthorize("hasAnyRole('SUPERADMIN')")
    public RestApiResponse<String> saveSysUser(@RequestBody SysUserVo sysUser) {
        if (!isAdmin()) {
            sysUser.setType(UserTypeEnum.ORDINARY.toString());
        }
        String id = sysUserService.saveOrUpdateSysUser(sysUser);
        return RestApiResponse.ok(id);
    }

    /**
     * @param sysUser 系统用户表数据 json
     * @return RestApiResponse<?>
     * @Description: 修改系统用户表
     * @Author: qyj
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改系统用户表", notes = "修改系统用户表")
    @SystemLogAnnotation(type = "系统用户表", value = "修改系统用户表")
    @OperationLogAnnotation(notes = "更新{系统用户:id}", module = "系统用户", submodule = "系统用户管理")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    @PreAuthorize("hasAnyRole('SUPERADMIN')")
    public RestApiResponse<String> updateSysUser(@RequestBody SysUserVo sysUser) {
        if (!isAdmin()) {
            sysUser.setType(UserTypeEnum.ORDINARY.toString());
        }
        String id = sysUserService.saveOrUpdateSysUser(sysUser);
        return RestApiResponse.ok(id);
    }

    /**
     * @param ids
     * @return RestApiResponse<?>
     * @Description: 删除系统用户表(判断 关联数据是否可以删除)
     * @Author: qyj
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "删除系统用户表", notes = "删除系统用户表")
    @SystemLogAnnotation(type = "系统用户表", value = "删除系统用户表")
//	@OperationLogAnnotation(notes = "删除{系统用户:ids}", module = "系统用户", submodule = "系统用户管理")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    @PreAuthorize("hasAnyRole('SUPERADMIN')")
    public RestApiResponse<?> deleteSysUser(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        sysUserService.deleteSysUser(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询系统用户表详情
     * @Author: qyj
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询系统用户表详情", notes = "查询系统用户表详情")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    @PreAuthorize("hasAnyRole('SUPERADMIN')")
    public RestApiResponse<SysUser> findById(@RequestParam("id") String id) {
        SysUser sysUser = sysUserService.findById(id);
        return RestApiResponse.ok(sysUser);
    }

    /**
     * @param sysUserVo 系统用户表 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询系统用户表
     * @Author: qyj
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询系统用户表", notes = "分页查询系统用户表")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    @PreAuthorize("hasAnyRole('SUPERADMIN')")
    public RestApiResponse<PageInfo<SysUserVo>> findPageByQuery(@RequestBody SysUserVo sysUserVo) {
    	LoginUser loginUser = getLoginUser();
    	if(AssigneeConstant.ORG_ADMIN_ROLE.equals(loginUser.getSysRoles().get(0).getRoleCode() + "@ROLE")) {
    		sysUserVo.setScienceOrgId(loginUser.getScienceOrgId());
    	}
    	
        PageInfo<SysUserVo> sysUser = sysUserService.findPageByQuery(sysUserVo);
        return RestApiResponse.ok(sysUser);
    }

    /**
     * @param sysUserVo 系统用户表 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询系统用户表
     * @Author: qyj
     */
    @PostMapping("/findExpertPageByQuery")
    @ApiOperation(value = "分页查询系统用户表内的专家", notes = "分页查询系统用户表内的专家")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<PageInfo<SysUserVo>> findExpertPageByQuery(@RequestBody SysUserVo sysUserVo) {
        PageInfo<SysUserVo> sysUser = sysUserService.findExpertPageByQuery(sysUserVo);
        return RestApiResponse.ok(sysUser);
    }

//    /**
//     * @param sysUserVo 修改密码
//     * @return RestApiResponse<?>
//     * @Description: 修改密码
//     */
//    @PostMapping("/updatePwd")
//    @ApiOperation(value = "修改密码系统用户", notes = "修改密码系统用户")
//    @PreAuthorize("hasAuthority('" + PER_PREFIX + "updatePwd')")
//    public RestApiResponse<?> updatePwd(@RequestBody SysUserVo sysUserVo) {
//
//        sysUserService.resetPwd(sysUserVo);
//        return RestApiResponse.ok();
//    }

    /**
     * @param sysUserVo 查询条件
     * @return RestApiResponse<?>
     * @Description: 导出系统用户表
     */
    @PostMapping("/exportExcel")
    @ApiOperation(value = "导出查询得系统用户", notes = "导出查询得系统用户")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "export')")
    public void exportExcel(SysUserVo sysUserVo) {
        PageInfo<SysUserVo> sysUser = sysUserService.findPageByQuery(sysUserVo);
        EasyExcelUtils.exportExcel(sysUser.getList(), "系统用户", SysUserVo.class, "系统用户.xlsx", response);
    }


    /**
     * @return RestApiResponse<?>
     * @Description: 当前用户的组织机构id
     */
    @GetMapping("/getAdmin")
    @ApiOperation(value = "查询当前用户的组织机构id", notes = "查询当前用户的组织机构id")
    public RestApiResponse<?> getAdmin() {
        boolean admin = isAdmin();
        return RestApiResponse.ok(admin);
    }

    /**
     * @return RestApiResponse<?>
     * @Description: 注册系统用户时  用户名是否重复
     */
    @GetMapping("/getUserNameIsRepeatable")
    @ApiOperation(value = "用户名是否重复", notes = "用户名是否重复")
    @PreAuthorize("hasAnyRole('SUPERADMIN')")
    public RestApiResponse<?> getUserNameIsRepeatable(@RequestParam("username") String username) {
        SysUser user = sysUserService.getUser(username);
        if (null != user) {
            throw new ServiceException("用户名重复");
        }
        return RestApiResponse.ok();
    }

//    /**
//     * @return RestApiResponse<?>
//     * @Description: 注册系统用户时  用户名是否重复
//     */
//    @PostMapping("/updatePasswordById")
//    @ApiOperation(value = "通过主键修改对应用户得密码", notes = "通过主键修改对应用户得密码")
////    @PreAuthorize("hasAuthority('" + PER_PREFIX + "updatePrimaryPassword')")
//    @PreAuthorize("hasAnyRole('SUPERADMIN')")
//    public RestApiResponse<?> updatePasswordById(@RequestBody SysUserVo sysUserVo) {
//        int i = sysUserService.updatePasswordById(sysUserVo);
//        if (i == 0) {
//            throw new ServiceException("密码修改失败，请联系管理员");
//        }
//        return RestApiResponse.ok();
//    }

    /**
     * @return RestApiResponse<?>
     * @Description: 初始化用户
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/initUser")
    @ApiOperation(value = "初始化用户", notes = "初始化用户")
    @SystemLogAnnotation(type = "初始化用户", value = "初始化用户")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "initUser')")
    public RestApiResponse<?> initUser() {
        sysUserService.initUser();
        return RestApiResponse.ok();
    }

    /**
     * @param areaCode 角色CODE
     * @return RestApiResponse<?>
     * @Description: 根据角色CODE查询出所有系统用户表
     * @Author: yujianfei
     */
    @PostMapping("/findPageByRoleCode")
    @ApiOperation(value = "根据角色CODE查询出所有系统用户表", notes = "根据角色CODE查询出所有系统用户表")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "findPageByRoleCode')")
    public RestApiResponse<PageInfo<SysUser>> findPageByRoleCode(@RequestBody String areaCode) {
        PageInfo<SysUser> sysUser = sysUserService.findPageByRoleCode(areaCode);
        return RestApiResponse.ok(sysUser);
    }

    /**
     * @param areaCode 角色CODE
     * @return RestApiResponse<?>
     * @Description: 根据角色CODE查询出所有系统用户表
     * @Author: liwuei
     */
    @PostMapping("/getZLJInfo")
    @ApiOperation(value = "浙里检数据", notes = "查询浙里检数据")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "getZLJInfo')")
    public RestApiResponse<?> getZLJInfo() {
    	Object reportTj = redisUtil.get(RedisConstant.ZJL_INFO);
        if (reportTj != null) {
          return (RestApiResponse.ok(reportTj));
        }
        return RestApiResponse.ok(sysUserService.getZLJInfo());
    }
}
