package com.fd.stdp.controller.basic;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonRecord;
import com.fd.stdp.beans.basic.vo.BasicPersonRecordVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.basic.BasicPersonRecordService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 机构人员报备
 *@Author: wangsh
 *@Date: 2022-02-15 16:58:00
 */
@RestController
@RequestMapping("/basic/basicPersonRecord")
@Api(value="机构人员报备", description="机构人员报备")
public class BasicPersonRecordController  extends BaseController {

	@Autowired
	private BasicPersonRecordService basicPersonRecordService;
	
	private final String PER_PREFIX = "这里写业务前缀命名:but:模块:功能:";
	
	/**
	 *@Description: 新增机构人员报备
	 *@param basicPersonRecord 机构人员报备数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增机构人员报备", notes = "新增机构人员报备")
	@SystemLogAnnotation(type = "机构人员报备",value = "新增机构人员报备")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveBasicPersonRecord(@RequestBody BasicPersonRecordVo basicPersonRecord) {
		String id = basicPersonRecordService.saveOrUpdateBasicPersonRecord(basicPersonRecord);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改机构人员报备
	 *@param basicPersonRecord 机构人员报备数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改机构人员报备", notes = "修改机构人员报备")
	@SystemLogAnnotation(type = "机构人员报备",value = "修改机构人员报备")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateBasicPersonRecord(@RequestBody BasicPersonRecordVo basicPersonRecord) {
		String id = basicPersonRecordService.saveOrUpdateBasicPersonRecord(basicPersonRecord);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除机构人员报备(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除机构人员报备", notes = "删除机构人员报备")
	@SystemLogAnnotation(type = "机构人员报备",value = "删除机构人员报备")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteBasicPersonRecord(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		basicPersonRecordService.deleteBasicPersonRecord(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除机构人员报备(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除机构人员报备", notes = "删除机构人员报备")
	@SystemLogAnnotation(type = "机构人员报备",value = "删除机构人员报备")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiBasicPersonRecord(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		basicPersonRecordService.deleteMultiBasicPersonRecord(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询机构人员报备详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询机构人员报备详情", notes = "查询机构人员报备详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		BasicPersonRecord  basicPersonRecord=basicPersonRecordService.findById(id);
		return RestApiResponse.ok(basicPersonRecord);
	}
	
	/**
	 *@Description: 分页查询机构人员报备
	 *@param basicPersonRecordVo 机构人员报备 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询机构人员报备", notes = "分页查询机构人员报备")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody BasicPersonRecordVo basicPersonRecordVo) {
		PageInfo<BasicPersonRecord>  basicPersonRecord=basicPersonRecordService.findPageByQuery(basicPersonRecordVo);
		return RestApiResponse.ok(basicPersonRecord);
	}

	/**
	 *@Description: 查询机构是否报备
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findIsRecord")
	@ApiOperation(value = "查询机构人员报备详情", notes = "查询机构人员报备详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findIsRecord() {
		return RestApiResponse.ok(basicPersonRecordService.findIsRecord(getCurrentOrgName()));
	}
}
