package com.fd.stdp.controller.project;

import com.fd.stdp.enums.QueryTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractApply;
import com.fd.stdp.beans.project.vo.ProjectContractApplyVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectContractApplyService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 *@Description: 任务书申报表
 *@Author: wangsh
 *@Date: 2022-01-12 14:37:32
 */
@RestController
@RequestMapping("/project/projectContractApply")
@Api(value="任务书申报表", description="任务书申报表")
public class ProjectContractApplyController  extends BaseController {

	@Autowired
	private ProjectContractApplyService projectContractApplyService;
	
	private final String PER_PREFIX = "project:but:contract:";
	
	/**
	 *@Description: 新增任务书申报表
	 *@param projectContractApply 任务书申报表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增任务书申报表", notes = "新增任务书申报表")
	@SystemLogAnnotation(type = "任务书申报表",value = "新增任务书申报表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveProjectContractApply(@RequestBody ProjectContractApplyVo projectContractApply) {
		projectContractApply.setOrgName(getCurrentOrgName());
		projectContractApply.setOrgCode(getCurrentScienceOrgId());
		String id = projectContractApplyService.saveOrUpdateProjectContractApply(projectContractApply);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改任务书申报表
	 *@param projectContractApply 任务书申报表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改任务书申报表", notes = "修改任务书申报表")
	@SystemLogAnnotation(type = "任务书申报表",value = "修改任务书申报表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateProjectContractApply(@RequestBody ProjectContractApplyVo projectContractApply) {
		String id = projectContractApplyService.saveOrUpdateProjectContractApply(projectContractApply);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除任务书申报表(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除任务书申报表", notes = "删除任务书申报表")
	@SystemLogAnnotation(type = "任务书申报表",value = "删除任务书申报表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteProjectContractApply(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		projectContractApplyService.deleteProjectContractApply(id);
		return RestApiResponse.ok();
	}

	/**
	 *@Description: 批量删除任务书申报表(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除任务书申报表", notes = "删除任务书申报表")
	@SystemLogAnnotation(type = "任务书申报表",value = "删除任务书申报表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiProjectContractApply(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		projectContractApplyService.deleteMultiProjectContractApply(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询任务书申报表详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询任务书申报表详情", notes = "查询任务书申报表详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		ProjectContractApply  projectContractApply=projectContractApplyService.findById(id);
		return RestApiResponse.ok(projectContractApply);
	}
	
	/**
	 *@Description: 分页查询任务书申报表
	 *@param projectContractApplyVo 任务书申报表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询任务书申报表", notes = "分页查询任务书申报表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody ProjectContractApplyVo projectContractApplyVo) {
		PageInfo<ProjectContractApply>  projectContractApply=projectContractApplyService.findPageByQuery(projectContractApplyVo);
		return RestApiResponse.ok(projectContractApply);
	}

	/**
	 *@Description: 提交任务书申报表
	 *@param projectContractApply 任务书申报表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "提交任务书申报表", notes = "提交任务书申报表")
	@SystemLogAnnotation(type = "任务书申报表",value = "提交任务书申报表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitProjectContractApply(@RequestBody ProjectContractApplyVo projectContractApply) {
		projectContractApply.setOrgName(getCurrentOrgName());
		projectContractApply.setOrgCode(getCurrentScienceOrgId());
		String id = projectContractApplyService.submitProjectContractApply(projectContractApply);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 审核任务书申报表
	 *@param projectContractApply 任务书申报表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "审核任务书申报表", notes = "审核任务书申报表")
	@SystemLogAnnotation(type = "任务书申报表",value = "审核任务书申报表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditProjectContractApply(@RequestBody ProjectContractApplyVo projectContractApply) {
		String id = projectContractApplyService.auditProjectContractApply(projectContractApply);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 退回任务书申报表
	 *@param projectContractApply 任务书申报表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "退回任务书申报表", notes = "退回任务书申报表")
	@SystemLogAnnotation(type = "任务书申报表",value = "退回任务书申报表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"sendBack')")
	public RestApiResponse<?> sendBackProjectContractApply(@RequestBody ProjectContractApplyVo projectContractApply) {
		String id = projectContractApplyService.sendBackProjectContractApply(projectContractApply);
		return RestApiResponse.ok(id);
	}


	/**
	 *@Description: 任务书下达
	 *@param projectContractApply 任务书申报表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "任务书下达", notes = "任务书下达")
	@SystemLogAnnotation(type = "任务书申报表",value = "任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseProjectContractApply(@RequestBody ProjectContractApplyVo projectContractApply) {
		String id = projectContractApplyService.releaseProjectContractApply(projectContractApply);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书完成上传
	 *@param projectContractApply 任务书申报表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/upload")
	@ApiOperation(value = "任务书完成上传", notes = "任务书完成上传")
	@SystemLogAnnotation(type = "任务书申报表",value = "任务书完成上传")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> uploadProjectContractApply(@RequestBody ProjectContractApplyVo projectContractApply) {
		String id = projectContractApplyService.uploadProjectContractApply(projectContractApply);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 任务书完成上传
	 *@param vo 任务书申报表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/todo")
	@ApiOperation(value = "任务书申请待办", notes = "任务书申请待办")
	@SystemLogAnnotation(type = "任务书申报表",value = "任务书申请待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoProjectContractApply(@RequestBody ProjectContractApplyVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = projectContractApplyService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = projectContractApplyService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = projectContractApplyService.endList(vo);
		} else {
			list = projectContractApplyService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}

	/**
	 *@Description: 任务书完成上传
	 *@param projectContractApply 任务书申报表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/finished")
	@ApiOperation(value = "任务书申请已办", notes = "任务书申请已办")
	@SystemLogAnnotation(type = "任务书申报表",value = "任务书申请已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finishedProjectContractApply(@RequestBody ProjectContractApplyVo projectContractApply) {
		return RestApiResponse.ok(projectContractApplyService.finishedList(projectContractApply));
	}


	/**
	 * @param vo 项目基本信息 查询条件
	 * @return RestApiResponse<?>
	 * @Description: 任务书已完成列表
	 * @Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "任务书已完成列表", notes = "任务书已完成列表")
	@PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
	public RestApiResponse<?> endList(@RequestBody ProjectContractApplyVo vo) {
		PageInfo pageInfo = projectContractApplyService.endList(vo);
		return RestApiResponse.ok(pageInfo);
	}

	/**
	 *@Description: 项目申报表单下载
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/download")
	@ApiOperation(value = "下载", notes = "下载")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"download')")
	public void upload(@RequestParam("id") String id, HttpServletResponse response) {
		projectContractApplyService.download(id, response);
	}

	/**
	 * 临时申报导中期
	 */
	@GetMapping("/rest/tempApplyToInterim")
	@ApiOperation(value = "临时申报导中期", notes = "临时申报导中期")
	public void tempApplyToInterim() {
		projectContractApplyService.tempApplyToInterim();
	}
}
