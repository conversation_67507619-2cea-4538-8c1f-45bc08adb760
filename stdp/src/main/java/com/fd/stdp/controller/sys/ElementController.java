package com.fd.stdp.controller.sys;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fd.stdp.beans.sys.SysElement;
import com.fd.stdp.beans.sys.vo.SysElementVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.service.sys.ElementService;
import com.github.pagehelper.PageInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import tk.mybatis.mapper.entity.Example;

/**
 * 元素管理
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/sys/element")
@Api(value = "元素管理", description = "元素接口")
public class ElementController extends BaseController {

	@Autowired
	private ElementService elementService;

	private final String prefix = "btn:sys:fun:permission:";

	@PostMapping("/save")
	@ApiOperation(value = "新增元素", notes = "新增元素")
	@ResponseBody
	@RepeatSubAnnotation
	@PreAuthorize("hasAuthority('" + prefix + "add')")
	public RestApiResponse<?> add(@RequestBody SysElementVo vo) {
		vo.setAdmin(isAdmin());
		this.elementService.addSysElement(vo);
		return RestApiResponse.ok(vo);
	}

	@RepeatSubAnnotation
	@PostMapping(value = "/update/{id}")
	@ApiOperation(value = "修改元素", notes = "修改元素")
	@ResponseBody
	@PreAuthorize("hasAuthority('" + prefix + "edit')")
	public RestApiResponse<?> update(@RequestBody SysElementVo vo) {
		vo.setAdmin(isAdmin());
		this.elementService.updateSelectiveById(vo);
		return RestApiResponse.ok(vo);
	}

	@RepeatSubAnnotation
	@PostMapping(value = "/delete/{id}")
	@ApiOperation(value = "删除元素", notes = "删除元素")
	@ResponseBody
	@PreAuthorize("hasAuthority('" + prefix + "delete')")
	public RestApiResponse<?> remove(@PathVariable String id) {
		this.elementService.delete(id, isAdmin());
		return RestApiResponse.ok();
	}

	/**
	 * 元素分页查询
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param name
	 * @param code
	 * @return
	 */
	@RequestMapping(value = "/findPageList", method = RequestMethod.GET)
	@ResponseBody
	public RestApiResponse<?> findPageList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize, String name, String code) {
		PageInfo<SysElement> elements = elementService.findPageList(pageNum, pageSize, name, code);
		return RestApiResponse.ok(elements);
	}

	/**
	 * 分页查询菜单下所有元素
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param name
	 * @param menuId
	 * @return
	 */
	@RequestMapping(value = "/list", method = RequestMethod.POST)
	@ResponseBody
	@ApiOperation(value = "查询菜单下所有元素(分页)", notes = "查询菜单下所有元素(分页)")
	@PreAuthorize("hasAuthority('" + prefix + "query')")
	public RestApiResponse<?> findElementByMenuId(@RequestBody SysElementVo sysElementVo) {
		PageInfo<SysElement> elementList = this.elementService.findElementByMenuId(sysElementVo);
		return RestApiResponse.ok(elementList);
	}

	/**
	 * 列表菜单下所有元素
	 * 
	 * @param menuId
	 * @return
	 */
	@RequestMapping(value = "/listAllByMenuId", method = RequestMethod.GET)
	@ResponseBody
	@PreAuthorize("hasAuthority('btn:sys:role:permissionByMenuId')")
	public RestApiResponse<?> list(String menuId) {
		if (StringUtils.isEmpty(menuId)) {
			throw new ServiceException("请选择菜单");
		}
		Example example = new Example(SysElement.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("menuId", menuId);
		List<SysElement> elementList = elementService.selectByExample(example);
		return RestApiResponse.ok(elementList);
	}

	/**
	 * 当前登录者所有权限
	 * 
	 * @param parentId
	 * @return
	 */
	@RequestMapping(value = "/user/authorityElement", method = RequestMethod.GET)
	@ApiOperation(value = "查询用户按钮权限", notes = "查询用户按钮权限")
	@ResponseBody
	public List<SysElement> listUserAuthorityElement(String parentId) {
		String userId = getCurrentUserId();
		if (userId == null) {
			return new ArrayList<SysElement>();
		}
		try {
			if (parentId == null) {
				parentId = CommonConstant.ROOT;// this.getSystem().get(0).getId();
			}
		} catch (Exception e) {
			return new ArrayList<SysElement>();
		}
		return elementService.getAuthorityElementByUserId(userId, parentId, 1, null);
	}

}
