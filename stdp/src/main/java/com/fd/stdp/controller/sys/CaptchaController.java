package com.fd.stdp.controller.sys;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import javax.imageio.ImageIO;

import com.fd.stdp.beans.basic.BasicManageOrg;
import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.sys.vo.LoginUserVo;
import com.fd.stdp.service.basic.BasicManageOrgService;
import com.fd.stdp.service.basic.BasicScienceOrgService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.redis.RedisUtil;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.constant.RedisConstant;
import com.fd.stdp.constant.SysConstant;
import com.fd.stdp.util.AppUserUtil;
import com.fd.stdp.util.UUIDUtils;
import com.google.code.kaptcha.impl.DefaultKaptcha;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 生成验证码Controller
 * @Author: linqiang
 * @Date: 2018/8/18
 * @Version: v1.0
 */
@RestController
@RequestMapping("/sys/captcha")
@Api(value = "图形验证码", description = "图形验证码")
public class CaptchaController extends BaseController {

	private static final String REDIS_MAN_ORG_K = "REDIS_MAN_ORG_K";
	private static final String REDIS_SCI_ORG_K = "REDIS_SCI_ORG_K";

	@Autowired
	private DefaultKaptcha captchaProducer;

	@Autowired
	private BasicManageOrgService basicManageOrgService;
	@Autowired
	private BasicScienceOrgService basicScienceOrgService;

	@Autowired
	private RedisUtil redisUtil;

	@ApiOperation(value = "测试获取当前用户信息")
	@GetMapping("/getCurrentUser")
	public RestApiResponse<?> getCurrentUser() {
		//return RestApiResponse.ok(AppUserUtil.getLoginAppUser());
		return getCurrentUserFull();
	}

	@ApiOperation(value = "测试获取当前用户信息")
	@GetMapping("/getCurrentUserFull")
	public RestApiResponse<?> getCurrentUserFull() {
		LoginUserVo loginUserVo = new LoginUserVo();
		BeanUtils.copyProperties(AppUserUtil.getLoginAppUser(), loginUserVo);
		BasicManageOrg basicManageOrg = null;
		BasicScienceOrg basicScienceOrg = null;
		/*try{
			basicManageOrg = (BasicManageOrg) redisUtil.get(REDIS_MAN_ORG_K + loginUserVo.getToken());
			basicScienceOrg = (BasicScienceOrg) redisUtil.get(REDIS_SCI_ORG_K + loginUserVo.getToken());
		}catch (Exception e){
			e.printStackTrace();
		}*/
		if(basicManageOrg == null && StringUtils.isNotBlank(loginUserVo.getManageOrgId())){
			basicManageOrg = basicManageOrgService.findById(loginUserVo.getManageOrgId());
		}
		if(basicScienceOrg == null && StringUtils.isNotBlank(loginUserVo.getScienceOrgId())){
			basicScienceOrg = basicScienceOrgService.findById(loginUserVo.getScienceOrgId());
		}
		/*if(basicManageOrg == null){
			redisUtil.set(REDIS_MAN_ORG_K + loginUserVo.getToken(), basicManageOrg, 60 * 5);
		}
		if(basicScienceOrg == null){
			redisUtil.set(REDIS_SCI_ORG_K + loginUserVo.getToken(), basicScienceOrg, 60 * 5);
		}*/
		loginUserVo.setBasicManageOrg(basicManageOrg);
		loginUserVo.setBasicScienceOrg(basicScienceOrg);
		return RestApiResponse.ok(loginUserVo);
	}

	@GetMapping("/loginkaptcha")
	@ApiOperation(value = "获取图形验证码")
	public RestApiResponse<?> loginkaptcha() {
		Map<String, String> base64img = handleRequest(SysConstant.LOGIN_VALID_CODE_FLAG);
		return RestApiResponse.ok(base64img);
	}

	private Map<String, String> handleRequest(String kaptType) {
		Map<String, String> result = new HashMap<>();
		ByteArrayOutputStream jpegOutputStream = new ByteArrayOutputStream();
		try {
			// 生产验证码字符串
			String createText = captchaProducer.createText();
			// 使用生产的验证码字符串返回一个BufferedImage对象并转为byte写入到byte数组中
			BufferedImage challenge = captchaProducer.createImage(createText);
			ImageIO.write(challenge, "jpg", jpegOutputStream);
			// 把验证码和随机字符串保存到redis中然后把随机字符串和验证码图片以base64传到前台
			String guid = UUIDUtils.getUUID();
			redisUtil.set(RedisConstant.VALID_CODE_KEY + kaptType + ":" + guid, createText,
					RedisConstant.REDIS_EXPIRE_ONE_MIN);
			result.put("random", guid);
		} catch (Exception e) {
			throw new ServiceException("验证码生成失败，请刷新");
		}
		String img = Base64.getEncoder().encodeToString(jpegOutputStream.toByteArray());
		result.put("img", img);
		return result;
	}

	@GetMapping("/checkLoginKaptcha")
	@ApiOperation(value = "校验登录图形验证码")
	public RestApiResponse<?> checkLoginKaptcha(@RequestParam(value = "kaptcha", required = true) String kaptcha,
			@RequestParam(value = "random", required = true) String random) {
		return checkKaptcha(SysConstant.LOGIN_VALID_CODE_FLAG, kaptcha, random);
	}

	private RestApiResponse<?> checkKaptcha(String kaptType, String kaptcha, String random) {
		if (StringUtils.isEmpty(random)) {
			return RestApiResponse.error("非法请求！");
		}
		// 页面输入的验证码
		if (StringUtils.isEmpty(kaptcha)) {
			return RestApiResponse.error("验证码为空!");
		}
		// 正确的图形验证码
		String validateCode = (String) redisUtil.get(RedisConstant.VALID_CODE_KEY + kaptType + ":" + random);
		if (StringUtils.isEmpty(validateCode)) {
			return RestApiResponse.error("验证码过期请刷新!");
		}
		logger.info("服务器图形验证码:" + validateCode);
		logger.info("用户输入图形验证码:" + kaptcha);
		if (StringUtils.isEmpty(validateCode)) {
			return RestApiResponse.error("验证码错误，请刷新后重试!");
		}
		if (validateCode.toLowerCase().equals(kaptcha.toLowerCase())) {
			redisUtil.del(RedisConstant.VALID_CODE_KEY + kaptType + ":" + random);
			String guid = UUIDUtils.getUUID();
			redisUtil.set(RedisConstant.VALID_CODE_KEY + guid, CommonConstant.FLAG_YES,
					RedisConstant.REDIS_EXPIRE_VALID);
			return RestApiResponse.ok(guid);
		} else {
			return RestApiResponse.error("验证码错误!");
		}
	}

}