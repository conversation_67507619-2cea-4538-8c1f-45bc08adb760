package com.fd.stdp.controller.sys;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.sys.SysProjectNotice;
import com.fd.stdp.beans.sys.vo.SysProjectNoticeVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.sys.SysProjectNoticeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 公告信息Controller
 *
 * <AUTHOR>
 * @date 2021-11-15
 */
@RestController
@RequestMapping("/sys/projectNotice")
@Api(value = "公告信息", description = "公告信息")
public class SysProjectNoticeController extends BaseController {
    @Autowired
    private SysProjectNoticeService sysProjectNoticeService;

    private final String PER_PREFIX = "btn:sys:projectNotice:";

    /**
     * @param sysProjectNoticeVo 公告信息数据 json
     * @return RestApiResponse<?>
     * @Description: 新增公告信息
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增公告信息", notes = "新增公告信息")
    @SystemLogAnnotation(type = "公告信息", value = "新增公告信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveSysProjectNotice(@RequestBody SysProjectNoticeVo sysProjectNoticeVo) {
        String id = sysProjectNoticeService.saveOrUpdateSysProjectNotice(sysProjectNoticeVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param sysProjectNoticeVo 公告信息数据 json
     * @return RestApiResponse<?>
     * @Description: 修改公告信息
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改公告信息", notes = "修改公告信息")
    @SystemLogAnnotation(type = "公告信息", value = "修改公告信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateSysProjectNotice(@RequestBody SysProjectNoticeVo sysProjectNoticeVo) {
        String id = sysProjectNoticeService.saveOrUpdateSysProjectNotice(sysProjectNoticeVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除公告信息(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除公告信息", notes = "批量删除公告信息")
    @SystemLogAnnotation(type = "公告信息", value = "批量删除公告信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteSysProjectNotice(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        sysProjectNoticeService.deleteSysProjectNotice(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询公告信息详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询公告信息详情", notes = "查询公告信息详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        SysProjectNoticeVo sysProjectNoticeVo = sysProjectNoticeService.findById(id);
        return RestApiResponse.ok(sysProjectNoticeVo);
    }

    /**
     * @param sysProjectNoticeVo 公告信息 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询公告信息
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询公告信息", notes = "分页查询公告信息")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody SysProjectNoticeVo sysProjectNoticeVo) {
        PageInfo<SysProjectNotice> sysProjectNotice = sysProjectNoticeService.findPageByQuery(sysProjectNoticeVo);
        return RestApiResponse.ok(sysProjectNotice);
    }

}
