package com.fd.stdp.controller.gen;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.fd.stdp.beans.gen.GenTable;
import com.fd.stdp.beans.gen.GenTableColumn;
import com.fd.stdp.beans.gen.vo.GenTableVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.service.gen.GenTableColumnService;
import com.fd.stdp.service.gen.GenTableService;
import com.fd.stdp.util.txt.Convert;
import com.github.pagehelper.PageInfo;

import io.swagger.annotations.ApiOperation;

/**
 * 代码生成 操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/gen/code")
public class GenController extends BaseController
{
    @Autowired
    private GenTableService genTableService;

    @Autowired
    private GenTableColumnService genTableColumnService;
    private final String prefix = "btn:gen:code:";
    /**
     * 查询代码生成列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @PreAuthorize("hasAuthority('"+prefix+"list')")
    public RestApiResponse<?> genList(@RequestBody GenTableVo genTable)
    {
        PageInfo<GenTable> list = genTableService.findPageByQuery(genTable);
        return RestApiResponse.ok(list);
    }

    /**
     * 修改代码生成业务
     */
    @PreAuthorize("hasAuthority('"+prefix+"query')")
    @GetMapping(value = "/find/{talbleId}")
    public RestApiResponse<?> getInfo(@PathVariable String talbleId)
    {
        GenTable table = genTableService.findById(talbleId);
        List<GenTable> tables = genTableService.findAll();
        List<GenTableColumn> list = genTableColumnService.findGenTableColumnListByTableId(talbleId);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("info", table);
        map.put("rows", list);
        map.put("tables", tables);
        return RestApiResponse.ok(map);
    }

    /**
     * 查询数据库列表
     */
    @PreAuthorize("hasAuthority('"+prefix+"dblist')")
    @PostMapping("/db/list")
    public RestApiResponse<?> dataList(@RequestBody GenTableVo genTable){
        PageInfo<GenTable> list = genTableService.findDbTableList(genTable);
        return RestApiResponse.ok(list);
    }

    /**
     * 查询数据表字段列表
     */
//    @PreAuthorize("hasAuthority('"+prefix+"list')")
//    @GetMapping(value = "/column/{talbleId}")
//    public TableDataInfo columnList(String tableId)
//    {
//        TableDataInfo dataInfo = new TableDataInfo();
//        List<GenTableColumn> list = genTableColumnService.selectGenTableColumnListByTableId(tableId);
//        dataInfo.setRows(list);
//        dataInfo.setTotal(list.size());
//        return dataInfo;
//    }

    /**
     * 导入表结构（保存）
     */
    @PreAuthorize("hasAuthority('"+prefix+"importTable')")
    @SystemLogAnnotation(type = "代码生成",value = "导入表结构（保存）")
    @PostMapping("/importTable")
    public RestApiResponse<?> importTableSave(String tables)
    {
        String[] tableNames = Convert.toStrArray(tables);
        // 查询表信息
        List<GenTable> tableList = genTableService.findDbTableListByNames(tableNames);
        genTableService.importGenTable(tableList,getCurrentUserName());
        return RestApiResponse.ok();
    }

    /**
     * 修改保存代码生成业务
     */
    @PreAuthorize("hasAuthority('"+prefix+"edit')")
    @SystemLogAnnotation(type = "代码生成",value = "修改保存代码生成业务")
    @PutMapping
    public RestApiResponse<?> editSave(@Validated @RequestBody GenTableVo genTable)
    {
    	String options = JSONObject.toJSONString(genTable.getParams());
        genTable.setOptions(options);
        genTableService.validateEdit(genTable);
        genTableService.saveOrUpdateGenTable(genTable);
        return RestApiResponse.ok();
    }

    /**
     * 删除代码生成
     */
    @PreAuthorize("hasAuthority('"+prefix+"remove')")
    @SystemLogAnnotation(type = "代码生成",value = "删除代码生成")
    @DeleteMapping("/{tableIds}")
    public RestApiResponse<?> remove(@PathVariable String[] tableIds)
    {
        genTableService.deleteGenTableByIds(tableIds);
        return RestApiResponse.ok();
    }

    /**
     * 预览代码
     */
    @PreAuthorize("hasAuthority('"+prefix+"preview')")
    @GetMapping("/preview/{tableId}")
    public RestApiResponse<?> preview(@PathVariable("tableId") String tableId) throws IOException
    {
        Map<String, String> dataMap = genTableService.previewCode(tableId);
        return RestApiResponse.ok(dataMap);
    }

    /**
     * 生成代码（下载方式）
     */
    @PreAuthorize("hasAuthority('"+prefix+"code')")
    @SystemLogAnnotation(type = "代码生成",value = "生成代码（下载方式）")
    @GetMapping("/download/{tableName}")
    public void download(HttpServletResponse response, @PathVariable("tableName") String tableName) throws IOException
    {
        byte[] data = genTableService.downloadCode(tableName);
        genCode(response, data);
    }

    /**
     * 生成代码（自定义路径）
     */
    @PreAuthorize("hasAuthority('"+prefix+"gencode')")
    @SystemLogAnnotation(type = "代码生成",value = "生成代码（自定义路径）")
    @GetMapping("/genCode/{tableName}")
    public RestApiResponse<?> genCode(@PathVariable("tableName") String tableName)
    {
        genTableService.generatorCode(tableName);
        return RestApiResponse.ok();
    }

    /**
     * 同步数据库
     */
    @PreAuthorize("hasAuthority('"+prefix+"synchDb')")
    @SystemLogAnnotation(type = "代码生成",value = "同步数据库")
    @GetMapping("/synchDb/{tableName}")
    public RestApiResponse<?> synchDb(@PathVariable("tableName") String tableName)
    {
        genTableService.synchDb(tableName);
        return RestApiResponse.ok();
    }

    /**
     * 批量生成代码
     */
    @PreAuthorize("hasAuthority('"+prefix+"batchGen')")
    @SystemLogAnnotation(type = "代码生成",value = "批量生成代码")
    @GetMapping("/batchGenCode")
    public void batchGenCode(@RequestParam("tables") String tables) throws IOException
    {
        String[] tableNames = Convert.toStrArray(tables);
        byte[] data = genTableService.downloadCode(tableNames);
        genCode(response, data);
    }

    /**
     * 生成zip文件
     */
    private void genCode(HttpServletResponse response, byte[] data) throws IOException
    {
        response.reset();
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment; filename=\"linqiang.zip\"");
        response.addHeader("Content-Length", "" + data.length);
        response.setContentType("application/octet-stream; charset=UTF-8");
        IOUtils.write(data, response.getOutputStream());
    }
}