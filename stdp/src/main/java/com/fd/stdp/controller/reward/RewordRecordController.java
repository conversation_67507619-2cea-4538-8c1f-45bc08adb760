package com.fd.stdp.controller.reward;

import com.fd.stdp.beans.innovation.InnovationNationalCenterApply;
import com.fd.stdp.beans.innovation.vo.InnovationNationalCenterApplyVo;
import com.fd.stdp.beans.reward.RewordKeepOn;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.reward.RewordRecord;
import com.fd.stdp.beans.reward.vo.RewordRecordVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.reward.RewordRecordService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 科技成果登记
 *@Author: wangsh
 *@Date: 2022-07-05 16:58:41
 */
@RestController
@RequestMapping("/reward/rewordRecord")
@Api(value="科技成果登记", description="科技成果登记")
public class RewordRecordController  extends BaseController {

	@Autowired
	private RewordRecordService rewordRecordService;
	
	private final String PER_PREFIX = "reward:but:record:";
	
	/**
	 *@Description: 新增科技成果登记
	 *@param rewordRecordVo 科技成果登记数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增科技成果登记", notes = "新增科技成果登记")
	@SystemLogAnnotation(type = "科技成果登记",value = "新增科技成果登记")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveRewordRecord(@RequestBody RewordRecordVo rewordRecordVo) {
	    if(StringUtils.isBlank(rewordRecordVo.getOrgName())) {
            rewordRecordVo.setOrgName(getCurrentOrgName());
            rewordRecordVo.setOrgCode(getCurrentScienceOrgId());
			rewordRecordVo.setOrgId(getCurrentScienceOrgId());
		}
		String id = rewordRecordService.saveOrUpdateRewordRecord(rewordRecordVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改科技成果登记
	 *@param rewordRecord 科技成果登记数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改科技成果登记", notes = "修改科技成果登记")
	@SystemLogAnnotation(type = "科技成果登记",value = "修改科技成果登记")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateRewordRecord(@RequestBody RewordRecordVo rewordRecord) {
		String id = rewordRecordService.saveOrUpdateRewordRecord(rewordRecord);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除科技成果登记(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除科技成果登记", notes = "删除科技成果登记")
	@SystemLogAnnotation(type = "科技成果登记",value = "删除科技成果登记")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteRewordRecord(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		rewordRecordService.deleteRewordRecord(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除科技成果登记(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除科技成果登记", notes = "删除科技成果登记")
	@SystemLogAnnotation(type = "科技成果登记",value = "删除科技成果登记")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiRewordRecord(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		rewordRecordService.deleteMultiRewordRecord(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询科技成果登记详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询科技成果登记详情", notes = "查询科技成果登记详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			return RestApiResponse.error("无权限访问该数据");
		}
		RewordRecord  rewordRecord=rewordRecordService.findById(id);
		return RestApiResponse.ok(rewordRecord);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			RewordRecordVo vo = new RewordRecordVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<RewordRecord> pageInfo = (PageInfo<RewordRecord>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
	
	/**
	 *@Description: 分页查询科技成果登记
	 *@param rewordRecordVo 科技成果登记 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询科技成果登记", notes = "分页查询科技成果登记")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody RewordRecordVo rewordRecordVo) {
		PageInfo<RewordRecord>  rewordRecord=rewordRecordService.findPageByQuery(rewordRecordVo);
		return RestApiResponse.ok(rewordRecord);
	}

	/**
	 *@Description: 科技成果登记提交
	 *@param rewordRecordVo 科技成果登记数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "科技成果登记提交", notes = "科技成果登记提交")
	@SystemLogAnnotation(type = "科技成果登记",value = "科技成果登记提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitRewordRecord(@RequestBody RewordRecordVo rewordRecordVo) {
	    if(StringUtils.isBlank(rewordRecordVo.getOrgName())) {
            rewordRecordVo.setOrgName(getCurrentOrgName());
            rewordRecordVo.setOrgCode(getLoginUser().getAreaCode());
		}
		String id = rewordRecordService.submitRewordRecord(rewordRecordVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 科技成果登记审核
	 *@param rewordRecordVo 科技成果登记数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "科技成果登记审核", notes = "科技成果登记审核")
	@SystemLogAnnotation(type = "科技成果登记",value = "科技成果登记审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditRewordRecord(@RequestBody RewordRecordVo rewordRecordVo) {
		String id = rewordRecordService.auditRewordRecord(rewordRecordVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 科技成果登记退回
	 *@param rewordRecordVo 科技成果登记数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "科技成果登记退回", notes = "科技成果登记退回")
	@SystemLogAnnotation(type = "科技成果登记",value = "科技成果登记退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackRewordRecord(@RequestBody RewordRecordVo rewordRecordVo) {
		String id = rewordRecordService.sendBackRewordRecord(rewordRecordVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 科技成果登记任务书下达
	 *@param rewordRecordVo 科技成果登记数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "科技成果登记任务书下达", notes = "科技成果登记任务书下达")
	@SystemLogAnnotation(type = "科技成果登记",value = "科技成果登记任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseRewordRecord(@RequestBody RewordRecordVo rewordRecordVo) {
		String id = rewordRecordService.releaseRewordRecord(rewordRecordVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 科技成果登记待办
	 *@param vo 科技成果登记 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "科技成果登记待办", notes = "科技成果登记待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody RewordRecordVo vo) {
		PageInfo<RewordRecord> list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = rewordRecordService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = rewordRecordService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = rewordRecordService.endList(vo);
		} else {
			list = rewordRecordService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 科技成果登记已办
	 *@param rewordRecordVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "科技成果登记已办", notes = "科技成果登记已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody RewordRecordVo rewordRecordVo) {
		PageInfo<RewordRecord>  rewordRecord=rewordRecordService.finishedList(rewordRecordVo);
		return RestApiResponse.ok(rewordRecord);
	}
	
	/**
	 *@Description: 科技成果登记已完成
	 *@param rewordRecordVo 科技成果登记 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "科技成果登记已完成", notes = "科技成果登记已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody RewordRecordVo rewordRecordVo) {
		PageInfo<RewordRecord>  rewordRecord=rewordRecordService.endList(rewordRecordVo);
		return RestApiResponse.ok(rewordRecord);
	}
}
