package com.fd.stdp.controller.project;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.project.ProjectApplyTeams;
import com.fd.stdp.beans.project.vo.ProjectApplyTeamsVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.project.ProjectApplyTeamsService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 项目团队Controller
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
@RestController
@RequestMapping("/project/applyTeams")
@Api(value = "项目团队", description = "项目团队")
public class ProjectApplyTeamsController extends BaseController {
    @Autowired
    private ProjectApplyTeamsService projectApplyTeamsService;

    private final String PER_PREFIX = "btn:project:applyTeams:";

    /**
     * @param projectApplyTeams 项目团队数据 json
     * @return RestApiResponse<?>
     * @Description: 新增项目团队
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增项目团队", notes = "新增项目团队")
    @SystemLogAnnotation(type = "项目团队", value = "新增项目团队")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveProjectApplyTeams(@RequestBody ProjectApplyTeams projectApplyTeams) {
        String id = projectApplyTeamsService.saveOrUpdateProjectApplyTeams(projectApplyTeams);
        return RestApiResponse.ok(id);
    }

    /**
     * @param projectApplyTeams 项目团队数据 json
     * @return RestApiResponse<?>
     * @Description: 修改项目团队
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改项目团队", notes = "修改项目团队")
    @SystemLogAnnotation(type = "项目团队", value = "修改项目团队")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateProjectApplyTeams(@RequestBody ProjectApplyTeams projectApplyTeams) {
        String id = projectApplyTeamsService.saveOrUpdateProjectApplyTeams(projectApplyTeams);
        return RestApiResponse.ok(id);
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 批量删除项目团队(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除项目团队", notes = "批量删除项目团队")
    @SystemLogAnnotation(type = "项目团队", value = "批量删除项目团队")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteProjectApplyTeams(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        projectApplyTeamsService.deleteProjectApplyTeams(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询项目团队详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询项目团队详情", notes = "查询项目团队详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        ProjectApplyTeams projectApplyTeams = projectApplyTeamsService.findById(id);
        return RestApiResponse.ok(projectApplyTeams);
    }

    /**
     * @param projectApplyTeamsVo 项目团队 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询项目团队
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询项目团队", notes = "分页查询项目团队")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody ProjectApplyTeamsVo projectApplyTeamsVo) {
        PageInfo<ProjectApplyTeams> projectApplyTeams = projectApplyTeamsService.findPageByQuery(projectApplyTeamsVo);
        return RestApiResponse.ok(projectApplyTeams);
    }

}
