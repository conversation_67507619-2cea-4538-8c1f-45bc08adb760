package com.fd.stdp.controller.project;


import com.fd.stdp.beans.project.ProjectApplyInfo;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertScoreVo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.service.project.ProjectApplyExpertScoreService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * 项目申请专家评审意见Controller
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/expert/score")
@Api(value = "项目申请专家评审意见", description = "项目申请专家评审意见")
public class ProjectApplyExpertScoreController extends BaseController {
    @Autowired
    private ProjectApplyExpertScoreService projectApplyExpertScoreService;

    private final String PER_PREFIX = "btn:stdp:score:";

    /**
     * @param vo 项目申请专家评审意见数据 json
     * @return RestApiResponse<?>
     * @Description: 新增项目申请专家评审意见
     * @Author: zhangYu
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增项目申请专家评审意见", notes = "新增项目申请专家评审意见")
    @SystemLogAnnotation(type = "项目申请专家评审意见", value = "新增项目申请专家评审意见")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveProjectApplyExpertScore(@RequestBody ProjectApplyExpertScoreVo vo) {
        vo.setUserId(getCurrentUserId());
        vo.setExpertName(getLoginUser().getNickname());
        String id = projectApplyExpertScoreService.save(vo, getCurrentUserName(), getCurrentRealName());
        return RestApiResponse.ok(id);
    }

}
