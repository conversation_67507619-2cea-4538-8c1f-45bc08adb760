package com.fd.stdp.controller.sys.rest;

import com.alibaba.fastjson2.JSONObject;
import com.fd.stdp.beans.rest.ExportSsoBody;
import com.fd.stdp.beans.rest.LoginBody;
import com.fd.stdp.beans.rest.SysUserYthoauth;
import com.fd.stdp.beans.rest.ZwwSsoBody;
import com.fd.stdp.beans.rest.vo.YthBindingVo;
import com.fd.stdp.beans.sys.OssUser;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.beans.sys.vo.SysUserVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.redis.RedisUtil;
import com.fd.stdp.config.SecurityHandlerConfig;
import com.fd.stdp.config.properties.SuperviseProperties;
import com.fd.stdp.config.properties.YthoauthProperties;
import com.fd.stdp.feign.ReviewApi;
import com.fd.stdp.feign.YthoauthApi;
import com.fd.stdp.service.sys.SysUserService;
import com.fd.stdp.service.sys.TokenService;
import com.fd.stdp.service.sys.impl.UserDetailsServiceImpl;
import com.fd.stdp.util.ResponseUtil;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;

/**
 * @Description 单点登录
 * @Date 2023/9/13 10:00
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/rest/sso")
@Api(value = "单点登录", description = "单点登录")
public class SsoLoginController extends BaseController {

    public static final Logger logger = LoggerFactory.getLogger(SsoLoginController.class);

    @Autowired
    private SecurityHandlerConfig securityHandlerConfig;


    private static final String EST_REDIS_PREFIX = "EST_STDP_REDIS_PREFIX:";

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    YthoauthProperties ythoauthProperties;

    @Autowired
    YthoauthApi ythoauthApi;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SuperviseProperties superviseProperties;

    /**
     * @return void * @throws
     * @description: 一体化登录
     */
    @GetMapping("/ythLogin")
    public void ythLogin(@RequestParam("est") String est) {
        if (StringUtils.isEmpty(est)) {
            throw new ServiceException("est参数不能为空");
        }
        // 调用老胡的接口消费est并获得用户信息
        OssUser ossUser = null;
        try {
            HashMap<String, String> map = new HashMap<>();
            map.put("appName", ythoauthProperties.getAppName());
            map.put("est", est);
            logger.error("map: {} ", JSONObject.toJSONString(map));
            ossUser = ythoauthApi.ythOssPostLoginUser(map);
            logger.error("ossUser: {} ", JSONObject.toJSONString(ossUser));
            logger.info("登录反馈：结果-{},描述-{}，中台ID-{}", ossUser.getSuccess(), ossUser.getData(), ossUser.getUserId());
        } catch (Exception e) {
            // throw new ServiceException("一体化服务请求失败");
            e.printStackTrace();
            errRedirect("一体化登录失败");
            return;
        }
        try {
            if (ossUser != null) {
                redisUtil.set(EST_REDIS_PREFIX + est, ossUser.getUserId(), (60 * 60 * 24));
                // 按手机号查询库中的用户
                SysUserYthoauth sysUserYthoauth = new SysUserYthoauth();
                sysUserYthoauth.setYthuserId(ossUser.getUserId());
                sysUserYthoauth.setPhone(ossUser.getMobile());
                LoginUser loginUser = sysUserService.ythLogin(sysUserYthoauth);
                // 没有匹配的用户就创建用户
                if (loginUser == null) {
                    SysUserVo sysUserVo = new SysUserVo();
                    sysUserVo.setPhone(ossUser.getMobile());
                    sysUserVo.setUsername(ossUser.getMobile() + "_" + superviseProperties.getUserTypeCode());
                    sysUserVo.setNickname(ossUser.getMobile());
                    sysUserVo.setYthUserId(ossUser.getUserId());
                    sysUserService.createSuperviseUsers(sysUserVo);
                    loginUser = sysUserService.ythLogin(sysUserYthoauth);
//                    user = sysUserService.getUserById(user.getId());
                }
                securityHandlerConfig.loginResponse(loginUser, response);
            } else {
                throw new ServiceException("一体化服务请求失败");
            }
        } catch (Exception e) {
            // 登录失败
            logger.error("一体化登录失败:{}", e);
            errRedirect("一体化登录失败");
            return;
            // throw new ServiceException("一体化登录失败");
        }
        return;
    }

    //不用绑定了
//    @PostMapping("/ythBinding")
//    public void ythBinding(@RequestBody YthBindingVo ythBindingVo) {
//        if (StringUtils.isBlank(ythBindingVo.getEst())) {
//            logger.error("签名不能为空,{}", JSONObject.toJSONString(ythBindingVo));
//            throw new ServiceException("签名不能为空");
//        }
//        String ythUserId = null;
//        try {
//            ythUserId = (String) redisUtil.get(EST_REDIS_PREFIX + ythBindingVo.getEst());
//        } catch (Exception e) {
//            throw new ServiceException("一体化服务请求失败");
//        }
//        if (ythUserId == null) {
//            throw new ServiceException("签名不正确");
//        }
//        LoginBody loginBody = new LoginBody();
//        loginBody.setUsername(ythBindingVo.getUsername());
//        loginBody.setPassword(ythBindingVo.getPassword());
//        // 用户登录
//        LoginUser userInfo = null;
//        try {
//            userInfo = sysUserService.getLoginUser(loginBody);
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("账号或密码不正确,{}", JSONObject.toJSONString(ythBindingVo));
//            throw new ServiceException("账号或密码不正确");
//        }
//        if (StringUtils.isBlank(ythBindingVo.getEst())) {
//            logger.error("账号或密码不正确,{}", JSONObject.toJSONString(ythBindingVo));
//            throw new ServiceException("账号或密码不正确");
//        }
//        // 绑定用户
//        SysUserYthoauth sysUserYthoauth = new SysUserYthoauth();
//        sysUserYthoauth.setUserId(userInfo.getId());
//        sysUserYthoauth.setYthuserId(ythUserId);
//        LoginUser loginUser = sysUserService.ythLogin(sysUserYthoauth);
//        if (loginUser == null) {
//            sysUserService.ythBinding(sysUserYthoauth);
//        } else {
//            logger.error("用户重复,{}", JSONObject.toJSONString(ythBindingVo));
//        }
//        // 返回前端token
//        securityHandlerConfig.loginResponse(userInfo, response);
//    }

    protected void errRedirect(String msg) {
        // 把页面交给一体化登录页面
        HashMap<String, Object> map = new HashMap<>();
        map.put("isRedirect", true);
        RestApiResponse rest = RestApiResponse.error(msg);
        rest.setData(map);
        ResponseUtil.responseJson(response, HttpStatus.OK.value(), rest);
        return;
    }


    @Autowired
    UserDetailsServiceImpl userDetailsServiceImpl;

    /**
     * @return void * @throws
     * @description: 政务网登录 - 个人
     */
    @GetMapping("/zwwLoginApi")
    public void zwwLoginApi(@RequestParam("zt") String zt) {
        if (StringUtils.isEmpty(zt)) {
            throw new ServiceException("zt参数不能为空");
        }
        ZwwSsoBody zwwSsoBody = new ZwwSsoBody();
        zwwSsoBody.setToken(zt);
        LoginUser loginUser = sysUserService.zwwPersonLogin(zwwSsoBody);
        securityHandlerConfig.loginResponse(loginUser, response);
    }

    /**
     * @return void * @throws
     * @description: 政务网登录 - 企业机构
     */
    @GetMapping("/zwwOrgLoginApi")
    public void zwwOrgLoginApi(@RequestParam("zt") String zt) {
        if (StringUtils.isEmpty(zt)) {
            throw new ServiceException("zt参数不能为空");
        }
        ZwwSsoBody zwwSsoBody = new ZwwSsoBody();
        zwwSsoBody.setToken(zt);
        LoginUser loginUser = sysUserService.zwwOrgLoginApi(zwwSsoBody);
        securityHandlerConfig.loginResponse(loginUser, response);
    }

    /**
     * @return void * @throws
     * @description: 专家单点登录
     */
    @GetMapping("/expertLogin")
    public void expertLogin(HttpServletRequest request) {
        String queryString = request.getQueryString();
        if (StringUtils.isEmpty(queryString)) {
            throw new ServiceException("token参数不能为空");
        }
        String token = null;
        if (queryString.startsWith("token=")) {
            token = queryString.substring(6);
        } else {
            throw new ServiceException("token参数格式不正确");
        }
        if (StringUtils.isEmpty(token)) {
            throw new ServiceException("token参数不能为空");
        }
        ExportSsoBody exportSsoBody = new ExportSsoBody();
        exportSsoBody.setToken(token);
        LoginUser loginUser = sysUserService.expertLogin(exportSsoBody);
        securityHandlerConfig.loginResponse(loginUser, response);
    }

}
