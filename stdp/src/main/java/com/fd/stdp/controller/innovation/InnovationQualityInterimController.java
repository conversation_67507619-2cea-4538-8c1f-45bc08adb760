package com.fd.stdp.controller.innovation;

import com.fd.stdp.beans.innovation.InnovationQualityChange;
import com.fd.stdp.enums.QueryTypeEnum;
import com.fd.stdp.service.innovation.InnovationQualityContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityInterim;
import com.fd.stdp.beans.innovation.vo.InnovationQualityInterimVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.innovation.InnovationQualityInterimService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 省质检中心中期检查
 *@Author: wangsh
 *@Date: 2022-03-04 09:23:38
 */
@RestController
@RequestMapping("/innovation/innovationQualityInterim")
@Api(value="省质检中心中期检查", description="省质检中心中期检查")
public class InnovationQualityInterimController  extends BaseController {

	@Autowired
	private InnovationQualityInterimService innovationQualityInterimService;
	@Autowired
	private InnovationQualityContractService innovationQualityContractService;
	
	private final String PER_PREFIX = "but:quality:interim:";
	
	/**
	 *@Description: 新增省质检中心中期检查
	 *@param innovationQualityInterimVo 省质检中心中期检查数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增省质检中心中期检查", notes = "新增省质检中心中期检查")
	@SystemLogAnnotation(type = "省质检中心中期检查",value = "新增省质检中心中期检查")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInnovationQualityInterim(@RequestBody InnovationQualityInterimVo innovationQualityInterimVo) {
	    if(StringUtils.isBlank(innovationQualityInterimVo.getOrgName())) {
            innovationQualityInterimVo.setOrgName(getCurrentOrgName());
            innovationQualityInterimVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityInterimService.saveOrUpdateInnovationQualityInterim(innovationQualityInterimVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改省质检中心中期检查
	 *@param innovationQualityInterim 省质检中心中期检查数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改省质检中心中期检查", notes = "修改省质检中心中期检查")
	@SystemLogAnnotation(type = "省质检中心中期检查",value = "修改省质检中心中期检查")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInnovationQualityInterim(@RequestBody InnovationQualityInterimVo innovationQualityInterim) {
		String id = innovationQualityInterimService.saveOrUpdateInnovationQualityInterim(innovationQualityInterim);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除省质检中心中期检查(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除省质检中心中期检查", notes = "删除省质检中心中期检查")
	@SystemLogAnnotation(type = "省质检中心中期检查",value = "删除省质检中心中期检查")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteInnovationQualityInterim(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		innovationQualityInterimService.deleteInnovationQualityInterim(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除省质检中心中期检查(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除省质检中心中期检查", notes = "删除省质检中心中期检查")
	@SystemLogAnnotation(type = "省质检中心中期检查",value = "删除省质检中心中期检查")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiInnovationQualityInterim(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innovationQualityInterimService.deleteMultiInnovationQualityInterim(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询省质检中心中期检查详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询省质检中心中期检查详情", notes = "查询省质检中心中期检查详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InnovationQualityInterim  innovationQualityInterim=innovationQualityInterimService.findById(id);
		return RestApiResponse.ok(innovationQualityInterim);
	}


	/**
	 *@Description: 查询省质检中心任务书详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findContractById")
	@ApiOperation(value = "查询省质检中心变更详情", notes = "查询省质检中心变更详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findContractById(@RequestParam("id") String id) {
		InnovationQualityInterim innovationQualityInterim=innovationQualityInterimService.findById(id);
		return RestApiResponse.ok(innovationQualityContractService.findById(innovationQualityInterim.getContractId()));
	}
	
	/**
	 *@Description: 分页查询省质检中心中期检查
	 *@param innovationQualityInterimVo 省质检中心中期检查 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询省质检中心中期检查", notes = "分页查询省质检中心中期检查")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnovationQualityInterimVo innovationQualityInterimVo) {
		PageInfo<InnovationQualityInterim>  innovationQualityInterim=innovationQualityInterimService.findPageByQuery(innovationQualityInterimVo);
		return RestApiResponse.ok(innovationQualityInterim);
	}

	/**
	 *@Description: 省质检中心中期检查提交
	 *@param innovationQualityInterimVo 省质检中心中期检查数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "省质检中心中期检查提交", notes = "省质检中心中期检查提交")
	@SystemLogAnnotation(type = "省质检中心中期检查",value = "省质检中心中期检查提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitInnovationQualityInterim(@RequestBody InnovationQualityInterimVo innovationQualityInterimVo) {
	    if(StringUtils.isBlank(innovationQualityInterimVo.getOrgName())) {
            innovationQualityInterimVo.setOrgName(getCurrentOrgName());
            innovationQualityInterimVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityInterimService.submitInnovationQualityInterim(innovationQualityInterimVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 省质检中心中期检查审核
	 *@param innovationQualityInterimVo 省质检中心中期检查数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "省质检中心中期检查审核", notes = "省质检中心中期检查审核")
	@SystemLogAnnotation(type = "省质检中心中期检查",value = "省质检中心中期检查审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditInnovationQualityInterim(@RequestBody InnovationQualityInterimVo innovationQualityInterimVo) {
		String id = innovationQualityInterimService.auditInnovationQualityInterim(innovationQualityInterimVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心中期检查退回
	 *@param innovationQualityInterimVo 省质检中心中期检查数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "省质检中心中期检查退回", notes = "省质检中心中期检查退回")
	@SystemLogAnnotation(type = "省质检中心中期检查",value = "省质检中心中期检查退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackInnovationQualityInterim(@RequestBody InnovationQualityInterimVo innovationQualityInterimVo) {
		String id = innovationQualityInterimService.sendBackInnovationQualityInterim(innovationQualityInterimVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心中期检查任务书下达
	 *@param innovationQualityInterimVo 省质检中心中期检查数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "省质检中心中期检查任务书下达", notes = "省质检中心中期检查任务书下达")
	@SystemLogAnnotation(type = "省质检中心中期检查",value = "省质检中心中期检查任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseInnovationQualityInterim(@RequestBody InnovationQualityInterimVo innovationQualityInterimVo) {
		String id = innovationQualityInterimService.releaseInnovationQualityInterim(innovationQualityInterimVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心中期检查待办
	 *@param vo 省质检中心中期检查 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "省质检中心中期检查待办", notes = "省质检中心中期检查待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody InnovationQualityInterimVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = innovationQualityInterimService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = innovationQualityInterimService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = innovationQualityInterimService.endList(vo);
		} else {
			list = innovationQualityInterimService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 省质检中心中期检查已办
	 *@param innovationQualityInterimVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "省质检中心中期检查已办", notes = "省质检中心中期检查已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody InnovationQualityInterimVo innovationQualityInterimVo) {
		PageInfo<InnovationQualityInterim>  innovationQualityInterim=innovationQualityInterimService.finishedList(innovationQualityInterimVo);
		return RestApiResponse.ok(innovationQualityInterim);
	}
	
	/**
	 *@Description: 省质检中心中期检查已完成
	 *@param innovationQualityInterimVo 省质检中心中期检查 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "省质检中心中期检查已完成", notes = "省质检中心中期检查已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody InnovationQualityInterimVo innovationQualityInterimVo) {
		PageInfo<InnovationQualityInterim>  innovationQualityInterim=innovationQualityInterimService.endList(innovationQualityInterimVo);
		return RestApiResponse.ok(innovationQualityInterim);
	}
}
