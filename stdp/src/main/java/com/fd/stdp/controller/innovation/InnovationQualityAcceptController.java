package com.fd.stdp.controller.innovation;

import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityAcceptApply;
import com.fd.stdp.beans.innovation.vo.InnovationQualityAcceptApplyVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.innovation.InnovationQualityAcceptService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 省质检中心验收申请
 *@Author: wangsh
 *@Date: 2022-02-11 16:02:11
 */
@RestController
@RequestMapping("/innovation/innovationQualityAcceptApply")
@Api(value="省质检中心验收申请", description="省质检中心验收申请")
public class InnovationQualityAcceptController extends BaseController {

	@Autowired
	private InnovationQualityAcceptService innovationQualityAcceptApplyService;
	
	private final String PER_PREFIX = "but:quality:accept:";
	
	/**
	 *@Description: 新增省质检中心验收申请
	 *@param vo 省质检中心验收申请数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增省质检中心验收申请", notes = "新增省质检中心验收申请")
	@SystemLogAnnotation(type = "省质检中心验收申请",value = "新增省质检中心验收申请")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInnovationQualityAcceptApply(@RequestBody InnovationQualityAcceptApplyVo vo) {
		if(StringUtils.isBlank(vo.getOrgName())) {
			vo.setOrgName(getCurrentOrgName());
			vo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityAcceptApplyService.saveOrUpdateInnovationQualityAcceptApply(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改省质检中心验收申请
	 *@param innovationQualityAcceptApply 省质检中心验收申请数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改省质检中心验收申请", notes = "修改省质检中心验收申请")
	@SystemLogAnnotation(type = "省质检中心验收申请",value = "修改省质检中心验收申请")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInnovationQualityAcceptApply(@RequestBody InnovationQualityAcceptApplyVo innovationQualityAcceptApply) {
		String id = innovationQualityAcceptApplyService.saveOrUpdateInnovationQualityAcceptApply(innovationQualityAcceptApply);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除省质检中心验收申请(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除省质检中心验收申请", notes = "删除省质检中心验收申请")
	@SystemLogAnnotation(type = "省质检中心验收申请",value = "删除省质检中心验收申请")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteInnovationQualityAcceptApply(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		innovationQualityAcceptApplyService.deleteInnovationQualityAcceptApply(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除省质检中心验收申请(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除省质检中心验收申请", notes = "删除省质检中心验收申请")
	@SystemLogAnnotation(type = "省质检中心验收申请",value = "删除省质检中心验收申请")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiInnovationQualityAcceptApply(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innovationQualityAcceptApplyService.deleteMultiInnovationQualityAcceptApply(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询省质检中心验收申请详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询省质检中心验收申请详情", notes = "查询省质检中心验收申请详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InnovationQualityAcceptApply  innovationQualityAcceptApply=innovationQualityAcceptApplyService.findById(id);
		return RestApiResponse.ok(innovationQualityAcceptApply);
	}
	
	/**
	 *@Description: 分页查询省质检中心验收申请
	 *@param innovationQualityAcceptApplyVo 省质检中心验收申请 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询省质检中心验收申请", notes = "分页查询省质检中心验收申请")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo) {
		PageInfo<InnovationQualityAcceptApply>  innovationQualityAcceptApply=innovationQualityAcceptApplyService.findPageByQuery(innovationQualityAcceptApplyVo);
		return RestApiResponse.ok(innovationQualityAcceptApply);
	}

	/**
	 *@Description: 省质检中心验收申请提交
	 *@param vo 省质检中心验收申请数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "省质检中心验收申请提交", notes = "省质检中心验收申请提交")
	@SystemLogAnnotation(type = "省质检中心验收申请",value = "省质检中心验收申请提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitInnovationQualityAcceptApply(@RequestBody InnovationQualityAcceptApplyVo vo) {
		if(StringUtils.isBlank(vo.getOrgName())) {
			vo.setOrgName(getCurrentOrgName());
			vo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityAcceptApplyService.submitInnovationQualityAcceptApply(vo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心验收申请提交
	 *@param vo 省质检中心验收申请数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/expertSubmit")
	@ApiOperation(value = "省质检中心验收申请提交", notes = "省质检中心验收申请提交")
	@SystemLogAnnotation(type = "省质检中心验收申请",value = "省质检中心验收申请提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> expertSubmitInnovationQualityAcceptApply(@RequestBody InnovationQualityAcceptApplyVo vo) {
		String id = innovationQualityAcceptApplyService.expertSubmitInnovationQualityAcceptApply(vo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 省质检中心验收申请审核
	 *@param innovationQualityAcceptApplyVo 省质检中心验收申请数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "省质检中心验收申请审核", notes = "省质检中心验收申请审核")
	@SystemLogAnnotation(type = "省质检中心验收申请",value = "省质检中心验收申请审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditInnovationQualityAcceptApply(@RequestBody InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo) {
		String id = innovationQualityAcceptApplyService.auditInnovationQualityAcceptApply(innovationQualityAcceptApplyVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心验收申请退回
	 *@param innovationQualityAcceptApplyVo 省质检中心验收申请数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "省质检中心验收申请退回", notes = "省质检中心验收申请退回")
	@SystemLogAnnotation(type = "省质检中心验收申请",value = "省质检中心验收申请退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackInnovationQualityAcceptApply(@RequestBody InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo) {
		String id = innovationQualityAcceptApplyService.sendBackInnovationQualityAcceptApply(innovationQualityAcceptApplyVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心验收申请任务书下达
	 *@param innovationQualityAcceptApplyVo 省质检中心验收申请数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "省质检中心验收申请任务书下达", notes = "省质检中心验收申请任务书下达")
	@SystemLogAnnotation(type = "省质检中心验收申请",value = "省质检中心验收申请任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseInnovationQualityAcceptApply(@RequestBody InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo) {
		String id = innovationQualityAcceptApplyService.releaseInnovationQualityAcceptApply(innovationQualityAcceptApplyVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心验收申请待办
	 *@param vo 省质检中心验收申请 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "省质检中心验收申请待办", notes = "省质检中心验收申请待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody InnovationQualityAcceptApplyVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = innovationQualityAcceptApplyService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = innovationQualityAcceptApplyService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = innovationQualityAcceptApplyService.endList(vo);
		} else {
			list = innovationQualityAcceptApplyService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 省质检中心验收申请已办
	 *@param innovationQualityAcceptApplyVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "省质检中心验收申请已办", notes = "省质检中心验收申请已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo) {
		PageInfo<InnovationQualityAcceptApply>  innovationQualityAcceptApply=innovationQualityAcceptApplyService.finishedList(innovationQualityAcceptApplyVo);
		return RestApiResponse.ok(innovationQualityAcceptApply);
	}

	/**
	 *@Description: 省质检中心验收申请已完成
	 *@param innovationQualityAcceptApplyVo 省质检中心验收申请 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "省质检中心验收申请已完成", notes = "省质检中心验收申请已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo) {
		PageInfo<InnovationQualityAcceptApply>  innovationQualityAcceptApply=innovationQualityAcceptApplyService.endList(innovationQualityAcceptApplyVo);
		return RestApiResponse.ok(innovationQualityAcceptApply);
	}
}
