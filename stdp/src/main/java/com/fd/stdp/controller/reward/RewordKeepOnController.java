package com.fd.stdp.controller.reward;

import com.fd.stdp.beans.innovation.InnovationGeneralInnovationApply;
import com.fd.stdp.beans.tech.TechAchievementSell;
import com.fd.stdp.beans.tech.vo.TechAchievementSellVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.reward.RewordKeepOn;
import com.fd.stdp.beans.reward.vo.RewordKeepOnVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.reward.RewordKeepOnService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *@Description: 科技成果备案
 *@Author: wangsh
 *@Date: 2022-07-05 16:59:11
 */
@RestController
@RequestMapping("/reward/rewordKeepOn")
@Api(value="科技成果备案", description="科技成果备案")
public class RewordKeepOnController  extends BaseController {

	@Autowired
	private RewordKeepOnService rewordKeepOnService;
	
	private final String PER_PREFIX = "reward:but:keepon:";
	
	/**
	 *@Description: 新增科技成果备案
	 *@param rewordKeepOnVo 科技成果备案数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增科技成果备案", notes = "新增科技成果备案")
	@SystemLogAnnotation(type = "科技成果备案",value = "新增科技成果备案")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveRewordKeepOn(@RequestBody RewordKeepOnVo rewordKeepOnVo) {
	    if(StringUtils.isBlank(rewordKeepOnVo.getOrgName())) {
            rewordKeepOnVo.setOrgName(getCurrentOrgName());
            rewordKeepOnVo.setOrgCode(getCurrentScienceOrgId());
			rewordKeepOnVo.setOrgId(getCurrentScienceOrgId());
		}
		String id = rewordKeepOnService.saveOrUpdateRewordKeepOn(rewordKeepOnVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改科技成果备案
	 *@param rewordKeepOn 科技成果备案数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改科技成果备案", notes = "修改科技成果备案")
	@SystemLogAnnotation(type = "科技成果备案",value = "修改科技成果备案")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateRewordKeepOn(@RequestBody RewordKeepOnVo rewordKeepOn) {
		String id = rewordKeepOnService.saveOrUpdateRewordKeepOn(rewordKeepOn);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除科技成果备案(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除科技成果备案", notes = "删除科技成果备案")
	@SystemLogAnnotation(type = "科技成果备案",value = "删除科技成果备案")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteRewordKeepOn(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		rewordKeepOnService.deleteRewordKeepOn(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除科技成果备案(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除科技成果备案", notes = "删除科技成果备案")
	@SystemLogAnnotation(type = "科技成果备案",value = "删除科技成果备案")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiRewordKeepOn(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		rewordKeepOnService.deleteMultiRewordKeepOn(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询科技成果备案详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询科技成果备案详情", notes = "查询科技成果备案详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		// 权限验证：检查用户是否有权限查看该数据
		if (!hasPermissionToViewData(id)) {
			throw new ServiceException("无权限访问该数据");
		}
		RewordKeepOn  rewordKeepOn=rewordKeepOnService.findById(id);
		return RestApiResponse.ok(rewordKeepOn);
	}

	/**
	 * 检查用户是否有权限查看指定ID的数据
	 * @param targetId 要查询的数据ID
	 * @return 是否有权限
	 */
	private boolean hasPermissionToViewData(String targetId) {
		try {
			// 创建查询条件对象
			RewordKeepOnVo vo = new RewordKeepOnVo();
			vo.setPageNum(1);
			vo.setPageSize(Integer.MAX_VALUE); // 获取所有数据

			// 获取用户可访问的所有数据ID集合
			Set<String> accessibleIds = new HashSet<>();

			// 定义需要查询的所有类型
			String[] queryTypes = {QueryTypeEnum.TODO.getType(),QueryTypeEnum.FINISHED.getType(),QueryTypeEnum.END.getType()};

			// 循环调用todoList方法获取各种类型的数据
			for (String queryType : queryTypes) {
				vo.setQueryType(queryType);
				RestApiResponse<?> response = todoList(vo);
				if (response != null && response.getData() != null) {
					@SuppressWarnings("unchecked")
					PageInfo<RewordKeepOn> pageInfo = (PageInfo<RewordKeepOn>) response.getData();
					if (pageInfo.getList() != null) {
						pageInfo.getList().forEach(item -> accessibleIds.add(item.getId()));
					}
				}
			}

			// 检查目标ID是否在用户可访问的数据集合中
			return accessibleIds.contains(targetId);

		} catch (Exception e) {
			// 发生异常时，为了安全起见，拒绝访问
			return false;
		}
	}
	
	/**
	 *@Description: 分页查询科技成果备案
	 *@param rewordKeepOnVo 科技成果备案 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询科技成果备案", notes = "分页查询科技成果备案")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody RewordKeepOnVo rewordKeepOnVo) {
		PageInfo<RewordKeepOn>  rewordKeepOn=rewordKeepOnService.findPageByQuery(rewordKeepOnVo);
		return RestApiResponse.ok(rewordKeepOn);
	}

	/**
	 *@Description: 科技成果备案提交
	 *@param rewordKeepOnVo 科技成果备案数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "科技成果备案提交", notes = "科技成果备案提交")
	@SystemLogAnnotation(type = "科技成果备案",value = "科技成果备案提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitRewordKeepOn(@RequestBody RewordKeepOnVo rewordKeepOnVo) {
	    if(StringUtils.isBlank(rewordKeepOnVo.getOrgName())) {
            rewordKeepOnVo.setOrgName(getCurrentOrgName());
            rewordKeepOnVo.setOrgCode(getLoginUser().getAreaCode());
		}
		String id = rewordKeepOnService.submitRewordKeepOn(rewordKeepOnVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 科技成果备案审核
	 *@param rewordKeepOnVo 科技成果备案数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "科技成果备案审核", notes = "科技成果备案审核")
	@SystemLogAnnotation(type = "科技成果备案",value = "科技成果备案审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditRewordKeepOn(@RequestBody RewordKeepOnVo rewordKeepOnVo) {
		String id = rewordKeepOnService.auditRewordKeepOn(rewordKeepOnVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 科技成果备案退回
	 *@param rewordKeepOnVo 科技成果备案数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "科技成果备案退回", notes = "科技成果备案退回")
	@SystemLogAnnotation(type = "科技成果备案",value = "科技成果备案退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackRewordKeepOn(@RequestBody RewordKeepOnVo rewordKeepOnVo) {
		String id = rewordKeepOnService.sendBackRewordKeepOn(rewordKeepOnVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 科技成果备案任务书下达
	 *@param rewordKeepOnVo 科技成果备案数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "科技成果备案任务书下达", notes = "科技成果备案任务书下达")
	@SystemLogAnnotation(type = "科技成果备案",value = "科技成果备案任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseRewordKeepOn(@RequestBody RewordKeepOnVo rewordKeepOnVo) {
		String id = rewordKeepOnService.releaseRewordKeepOn(rewordKeepOnVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 科技成果备案待办
	 *@param vo 科技成果备案 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "科技成果备案待办", notes = "科技成果备案待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody RewordKeepOnVo vo) {
		PageInfo<RewordKeepOn> list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = rewordKeepOnService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = rewordKeepOnService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = rewordKeepOnService.endList(vo);
		} else {
			list = rewordKeepOnService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 科技成果备案已办
	 *@param rewordKeepOnVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "科技成果备案已办", notes = "科技成果备案已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody RewordKeepOnVo rewordKeepOnVo) {
		PageInfo<RewordKeepOn>  rewordKeepOn=rewordKeepOnService.finishedList(rewordKeepOnVo);
		return RestApiResponse.ok(rewordKeepOn);
	}
	
	/**
	 *@Description: 科技成果备案已完成
	 *@param rewordKeepOnVo 科技成果备案 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "科技成果备案已完成", notes = "科技成果备案已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody RewordKeepOnVo rewordKeepOnVo) {
		PageInfo<RewordKeepOn>  rewordKeepOn=rewordKeepOnService.endList(rewordKeepOnVo);
		return RestApiResponse.ok(rewordKeepOn);
	}
}
