package com.fd.stdp.controller.flowable;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;

import com.fd.stdp.beans.project.ProjectApplyInfo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.beans.project.vo.ProjectContractApplyVo;
import com.fd.stdp.beans.sys.SysRole;
import com.fd.stdp.beans.tech.vo.TechAchievementSellVo;
import com.fd.stdp.beans.tech.vo.TechAchievementVo;
import com.fd.stdp.beans.tech.vo.TechAwardsAcquireVo;
import com.fd.stdp.beans.tech.vo.TechAwardsApplyVo;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.dao.project.ProjectApplyInfoMapper;
import com.fd.stdp.dao.project.ProjectContractApplyMapper;
import com.fd.stdp.dao.tech.TechAchievementMapper;
import com.fd.stdp.dao.tech.TechAchievementSellMapper;
import com.fd.stdp.dao.tech.TechAwardsAcquireMapper;
import com.fd.stdp.dao.tech.TechAwardsApplyMapper;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.flowable.FlowCommonService;
import liquibase.pro.packaged.S;
import org.apache.velocity.runtime.directive.Foreach;
import org.flowable.task.api.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fd.stdp.beans.flowable.FlowTaskDto;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.service.flowable.FlowTaskService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import tk.mybatis.mapper.entity.Example;

/**
 * <p>工作流任务管理<p>
 *
 * <AUTHOR>
 * @date 2021-04-03
 */
@Api(tags = "工作流流程任务管理")
@RestController
@RequestMapping("/flowable/task")
public class FlowTaskController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(FlowTaskController.class);

    @Autowired
    private FlowTaskService flowTaskService;
    @Autowired
    private FlowApiService flowApiService;
    @Autowired
    private FlowCommonService flowCommonService;

    @Autowired
    private ProjectApplyInfoMapper projectApplyInfoMapper;
    @Autowired
    private ProjectContractApplyMapper projectContractApplyMapper;
    @Autowired
    private TechAchievementMapper techAchievementMapper;
    @Autowired
    private TechAchievementSellMapper techAchievementSellMapper;
    @Autowired
    private TechAwardsApplyMapper techAwardsApplyMapper;
    @Autowired
    private TechAwardsAcquireMapper techAwardsAcquireMapper;

    @ApiOperation(value = "我发起的流程", response = FlowTaskDto.class)
    @GetMapping(value = "/myProcess")
    public RestApiResponse<?> myProcess(@ApiParam(value = "当前页码", required = true) @RequestParam Integer pageNum,
                                        @ApiParam(value = "每页条数", required = true) @RequestParam Integer pageSize) {

        return flowTaskService.myProcess(pageNum, pageSize, getCurrentUserId());
    }

    @ApiOperation(value = "取消申请", response = FlowTaskDto.class)
    @PostMapping(value = "/stopProcess")
    public RestApiResponse<?> stopProcess(@RequestBody FlowTaskVo flowTaskVo) {
        return flowTaskService.stopProcess(flowTaskVo, getCurrentUserId());
    }

    @ApiOperation(value = "撤回流程", response = FlowTaskDto.class)
    @PostMapping(value = "/revokeProcess")
    public RestApiResponse<?> revokeProcess(@RequestBody FlowTaskVo flowTaskVo) {
        return flowTaskService.revokeProcess(flowTaskVo, getCurrentUserId());
    }

    @ApiOperation(value = "获取待办列表", response = FlowTaskDto.class)
    @GetMapping(value = "/todoList")
    public RestApiResponse<?> todoList(@ApiParam(value = "当前页码", required = true) @RequestParam Integer pageNum,
                                       @ApiParam(value = "每页条数", required = true) @RequestParam Integer pageSize,
                                       @ApiParam(value = "待办类型", required = true) @RequestParam(required = false) String procDefName) {
        if(procDefName != null) {
            Example example = null;
            Example.Criteria criteria = null;
            switch (procDefName) {
                //项目申请
                case "projectApply":
                    ProjectApplyInfoVo projectApplyInfoVo = new ProjectApplyInfoVo();
                    example = new Example(ProjectApplyInfo.class);
                    criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andLessThan("flowStatus", 999);
                    return RestApiResponse.ok(flowCommonService.todoTaskList(projectApplyInfoVo, projectApplyInfoMapper, example, criteria, pageNum, pageSize));
                //任务书
                case "projectContractApply":
                    ProjectContractApplyVo projectContractApplyVo = new ProjectContractApplyVo();
                    example = new Example(ProjectApplyInfo.class);
                    criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andLessThan("flowStatus", 999);
                    return RestApiResponse.ok(flowCommonService.todoTaskList(projectContractApplyVo, projectContractApplyMapper, example, criteria, pageNum, pageSize));
                //成果统计
                case "techAchievement":
                    TechAchievementVo techAchievementVo = new TechAchievementVo();
                    example = new Example(ProjectApplyInfo.class);
                    criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andLessThan("flowStatus", 999);
                    return RestApiResponse.ok(flowCommonService.todoTaskList(techAchievementVo, techAchievementMapper, example, criteria, pageNum, pageSize));
                //成果转化
                case "techAchievementSell":
                    TechAchievementSellVo techAchievementSellVo = new TechAchievementSellVo();
                    example = new Example(ProjectApplyInfo.class);
                    criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andLessThan("flowStatus", 999);
                    return RestApiResponse.ok(flowCommonService.todoTaskList(techAchievementSellVo, techAchievementSellMapper, example, criteria, pageNum, pageSize));
                //奖项申请
                case "techAwardsApply":
                    TechAwardsApplyVo techAwardsApplyVo = new TechAwardsApplyVo();
                    example = new Example(ProjectApplyInfo.class);
                    criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andLessThan("flowStatus", 999);
                    return RestApiResponse.ok(flowCommonService.todoTaskList(techAwardsApplyVo, techAwardsApplyMapper, example, criteria, pageNum, pageSize));
                //奖项获取
                case "techAwardsAcquire":
                    TechAwardsAcquireVo techAwardsAcquire = new TechAwardsAcquireVo();
                    example = new Example(TechAwardsAcquireVo.class);
                    criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andLessThan("flowStatus", 999);
                    return RestApiResponse.ok(flowCommonService.todoTaskList(techAwardsAcquire, techAwardsAcquireMapper, example, criteria, pageNum, pageSize));
                default:
            }
        }
        return flowTaskService.todoList(pageNum, pageSize, getCurrentUserId());
    }

    @ApiOperation(value = "获取已办任务", response = FlowTaskDto.class)
    @GetMapping(value = "/finishedList")
    public RestApiResponse<?> finishedList(@ApiParam(value = "当前页码", required = true) @RequestParam Integer pageNum,
                                           @ApiParam(value = "每页条数", required = true) @RequestParam Integer pageSize) {
        return flowTaskService.finishedList(pageNum, pageSize, getCurrentUserId());
    }


    @ApiOperation(value = "流程历史流转记录", response = FlowTaskDto.class)
    @GetMapping(value = "/flowRecord")
    public RestApiResponse<?> flowRecord(String procInsId, String deployId) {
        return flowTaskService.flowRecord(procInsId, deployId);
    }

    @ApiOperation(value = "获取流程变量", response = FlowTaskDto.class)
    @GetMapping(value = "/processVariables/{taskId}")
    public RestApiResponse<?> processVariables(@ApiParam(value = "流程任务Id") @PathVariable(value = "taskId") String taskId) {
        return flowTaskService.processVariables(taskId);
    }

    @ApiOperation(value = "审批任务")
    @PostMapping(value = "/complete")
    public RestApiResponse<?> complete(@RequestBody FlowTaskVo flowTaskVo) {
        return flowTaskService.complete(flowTaskVo, getCurrentUserId());
    }

    @ApiOperation(value = "驳回任务")
    @PostMapping(value = "/reject")
    public RestApiResponse<?> taskReject(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.taskReject(flowTaskVo);
        return RestApiResponse.ok();
    }

    @ApiOperation(value = "退回任务")
    @PostMapping(value = "/return")
    public RestApiResponse<?> taskReturn(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.taskReturn(flowTaskVo);
        return RestApiResponse.ok();
    }

    @ApiOperation(value = "获取所有可回退的节点")
    @PostMapping(value = "/returnList")
    public RestApiResponse<?> findReturnTaskList(@RequestBody FlowTaskVo flowTaskVo) {
        return flowTaskService.findReturnTaskList(flowTaskVo);
    }

    @ApiOperation(value = "删除任务")
    @DeleteMapping(value = "/delete")
    public RestApiResponse<?> delete(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.deleteTask(flowTaskVo);
        return RestApiResponse.ok();
    }

    @ApiOperation(value = "认领/签收任务")
    @PostMapping(value = "/claim")
    public RestApiResponse<?> claim(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.claim(flowTaskVo);
        return RestApiResponse.ok();
    }

    @ApiOperation(value = "取消认领/签收任务")
    @PostMapping(value = "/unClaim")
    public RestApiResponse<?> unClaim(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.unClaim(flowTaskVo);
        return RestApiResponse.ok();
    }

    @ApiOperation(value = "委派任务")
    @PostMapping(value = "/delegate")
    public RestApiResponse<?> delegate(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.delegateTask(flowTaskVo);
        return RestApiResponse.ok();
    }

    @ApiOperation(value = "转办任务")
    @PostMapping(value = "/assign")
    public RestApiResponse<?> assign(@RequestBody FlowTaskVo flowTaskVo) {
        flowTaskService.assignTask(flowTaskVo);
        return RestApiResponse.ok();
    }

    @ApiOperation(value = "获取下一节点")
    @PostMapping(value = "/nextFlowNode")
    public RestApiResponse<?> getNextFlowNode(@RequestBody FlowTaskVo flowTaskVo) {
        return flowTaskService.getNextFlowNode(flowTaskVo);
    }

    /**
     * 生成流程图
     *
     * @param processId 任务ID
     */
    @RequestMapping("/diagram/{processId}")
    public void genProcessDiagram(HttpServletResponse response,
                                  @PathVariable("processId") String processId) {
        InputStream inputStream = flowTaskService.diagram(processId);
        OutputStream os = null;
        BufferedImage image = null;
        try {
            image = ImageIO.read(inputStream);
            response.setContentType("image/png");
            os = response.getOutputStream();
            if (image != null) {
                ImageIO.write(image, "png", os);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.flush();
                    os.close();
                }
            } catch (IOException e) {
                log.error("生成流程图-关闭流错误", e);
            }
        }
    }

    /**
     * 生成流程图
     *
     * @param procInsId 任务ID
     */
    @RequestMapping("/flowViewer/{procInsId}")
    public RestApiResponse<?> getFlowViewer(@PathVariable("procInsId") String procInsId) {
        return flowTaskService.getFlowViewer(procInsId);
    }

    /**
     * 获取 formKey
     *
     * @param deployId 部署ID
     */
    @GetMapping(value = "/getFormKey")
    public RestApiResponse<?> getFormKey(String deployId) {
        Map<String, Object> formKey = flowApiService.getFormKey(deployId);
        return RestApiResponse.ok(formKey);
    }
}
