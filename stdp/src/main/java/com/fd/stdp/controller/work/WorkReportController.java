package com.fd.stdp.controller.work;

import java.util.List;

import com.fd.stdp.beans.innovation.InnovationLaboratoryApply;
import com.fd.stdp.enums.QueryTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;

import com.fd.stdp.beans.work.WorkReport;
import com.fd.stdp.beans.work.vo.WorkReportVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.work.WorkReportService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 工作交流Controller
 *
 * <AUTHOR>
 * @date 2021-11-04
 */
@RestController
@RequestMapping("/work/report")
@Api(value = "工作交流", description = "工作交流")
public class WorkReportController extends BaseController {
    @Autowired
    private WorkReportService workReportService;

    private final String PER_PREFIX = "btn:work:report:";

    /**
     * @param workReportVo 工作交流数据 json
     * @return RestApiResponse<?>
     * @Description: 新增工作交流
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/save")
    @ApiOperation(value = "新增工作交流", notes = "新增工作交流")
    @SystemLogAnnotation(type = "工作交流", value = "新增工作交流")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "save')")
    public RestApiResponse<?> saveWorkReport(@RequestBody WorkReportVo workReportVo) {
        if(StringUtils.isBlank(workReportVo.getOrgName())) {
            workReportVo.setOrgName(getCurrentOrgName());
            workReportVo.setOrgCode(getCurrentScienceOrgId());
            workReportVo.setAttr1(getLoginUser().getAreaName());
        }
        if(StringUtils.isBlank(workReportVo.getOrgCode())) {
            workReportVo.setOrgCode(getCurrentScienceOrgId());
        }
        String id = workReportService.saveOrUpdateWorkReport(workReportVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param workReportVo 工作交流数据 json
     * @return RestApiResponse<?>
     * @Description: 修改工作交流
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/update")
    @ApiOperation(value = "修改工作交流", notes = "修改工作交流")
    @SystemLogAnnotation(type = "工作交流", value = "修改工作交流")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "update')")
    public RestApiResponse<?> updateWorkReport(@RequestBody WorkReportVo workReportVo) {
        if(StringUtils.isBlank(workReportVo.getOrgName())) {
            workReportVo.setOrgName(getCurrentOrgName());
            workReportVo.setOrgCode(getCurrentScienceOrgId());
            workReportVo.setAttr1(getLoginUser().getAreaName());
        }
        if(StringUtils.isBlank(workReportVo.getOrgCode())) {
            workReportVo.setOrgCode(getCurrentScienceOrgId());
        }
        String id = workReportService.saveOrUpdateWorkReport(workReportVo);
        return RestApiResponse.ok(id);
    }

    /**
     * @param ids
     * @return RestApiResponse<?>
     * @Description: 批量删除工作交流(判断 关联数据是否可以删除)
     * @Author: yujianfei
     */
    @RepeatSubAnnotation
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除工作交流", notes = "批量删除工作交流")
    @SystemLogAnnotation(type = "工作交流", value = "批量删除工作交流")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "delete')")
    public RestApiResponse<?> deleteWorkReport(@RequestBody List<String> ids) {
        //判断 关联数据是否可以删除
        workReportService.deleteWorkReport(ids);
        return RestApiResponse.ok();
    }

    /**
     * @param id
     * @return RestApiResponse<?>
     * @Description: 查询工作交流详情
     * @Author: yujianfei
     */
    @GetMapping("/findById")
    @ApiOperation(value = "查询工作交流详情", notes = "查询工作交流详情")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "find')")
    public RestApiResponse<?> findById(@RequestParam("id") String id) {
        WorkReportVo workReportVo = workReportService.findById(id);
        return RestApiResponse.ok(workReportVo);
    }

    /**
     * @param workReportVo 工作交流 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询工作交流
     * @Author: yujianfei
     */
    @PostMapping("/findPageByQuery")
    @ApiOperation(value = "分页查询工作交流", notes = "分页查询工作交流")
    @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public RestApiResponse<?> findPageByQuery(@RequestBody WorkReportVo workReportVo) {
        PageInfo<WorkReport> workReport = workReportService.findPageByQuery(workReportVo);
        return RestApiResponse.ok(workReport);
    }

    /**
     *@Description: 提交
     *@param workReportVo 数据 json
     *@return RestApiResponse<?>
     *@Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/submit")
    @ApiOperation(value = "提交", notes = "提交")
    @SystemLogAnnotation(type = "",value = "提交")
    @PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
    public RestApiResponse<?> submitWorkReport(@RequestBody WorkReportVo workReportVo) {
        if(StringUtils.isBlank(workReportVo.getOrgName())) {
            workReportVo.setOrgName(getCurrentOrgName());
            workReportVo.setOrgCode(getCurrentScienceOrgId());
            workReportVo.setAttr1(getLoginUser().getAreaName());
        }
        if(StringUtils.isBlank(workReportVo.getOrgCode())) {
            workReportVo.setOrgCode(getCurrentScienceOrgId());
        }
        String id = workReportService.submitWorkReport(workReportVo);
        return RestApiResponse.ok(id);
    }

    /**
     *@Description: 审核
     *@param workReportVo 数据 json
     *@return RestApiResponse<?>
     *@Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/audit")
    @ApiOperation(value = "审核", notes = "审核")
    @SystemLogAnnotation(type = "",value = "审核")
    @PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
    public RestApiResponse<?> auditWorkReport(@RequestBody WorkReportVo workReportVo) {
        String id = workReportService.auditWorkReport(workReportVo);
        return RestApiResponse.ok(id);
    }

    /**
     *@Description: 退回
     *@param workReportVo 数据 json
     *@return RestApiResponse<?>
     *@Author: wangsh
     */
    @RepeatSubAnnotation
    @PostMapping("/sendBack")
    @ApiOperation(value = "退回", notes = "退回")
    @SystemLogAnnotation(type = "",value = "退回")
    @PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
    public RestApiResponse<?> sendBackWorkReport(@RequestBody WorkReportVo workReportVo) {
        String id = workReportService.sendBackWorkReport(workReportVo);
        return RestApiResponse.ok(id);
    }

    /**
     *@Description: 待办
     *@param vo  查询条件
     *@return RestApiResponse<?>
     *@Author: wangsh
     */
    @PostMapping("/todo")
    @ApiOperation(value = "待办", notes = "待办")
    @PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
    public RestApiResponse<?> todoList(@RequestBody WorkReportVo vo) {
        PageInfo<WorkReport> list = null;
        if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
            list = workReportService.todoList(vo);
        } else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
            list = workReportService.finishedList(vo);
        } else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
            list = workReportService.endList(vo);
        } else {
            list = workReportService.todoList(vo);
        }
        return RestApiResponse.ok(list);
//        PageInfo<WorkReport>  workReport=workReportService.todoList(workReportVo);
//        return RestApiResponse.ok(workReport);
    }
    /**
     *@Description: 已办
     *@param workReportVo 专家委员会库 查询条件
     *@return RestApiResponse<?>
     *@Author: wangsh
     */
    @PostMapping("/finished")
    @ApiOperation(value = "已办", notes = "已办")
    @PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
    public RestApiResponse<?> finished(@RequestBody WorkReportVo workReportVo) {
        PageInfo<WorkReport>  workReport=workReportService.finishedList(workReportVo);
        return RestApiResponse.ok(workReport);
    }

    /**
     *@Description: 已完成
     *@param workReportVo  查询条件
     *@return RestApiResponse<?>
     *@Author: wangsh
     */
    @PostMapping("/end")
    @ApiOperation(value = "已完成", notes = "已完成")
    @PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
    public RestApiResponse<?> endList(@RequestBody WorkReportVo workReportVo) {
        PageInfo<WorkReport>  workReport=workReportService.endList(workReportVo);
        return RestApiResponse.ok(workReport);
    }
}
