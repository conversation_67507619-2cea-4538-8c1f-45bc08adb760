package com.fd.stdp.controller.innovation;

import com.fd.stdp.enums.QueryTypeEnum;
import com.fd.stdp.service.innovation.InnovationLaboratoryContractService;
import com.fd.stdp.service.innovation.InnovationQualityContractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityChange;
import com.fd.stdp.beans.innovation.vo.InnovationQualityChangeVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.innovation.InnovationQualityChangeService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 省质检中心变更
 *@Author: wangsh
 *@Date: 2022-03-04 09:23:32
 */
@RestController
@RequestMapping("/innovation/innovationQualityChange")
@Api(value="省质检中心变更", description="省质检中心变更")
public class InnovationQualityChangeController  extends BaseController {

	@Autowired
	private InnovationQualityChangeService innovationQualityChangeService;
	@Autowired
	private InnovationQualityContractService innovationQualityContractService;
	@Autowired
	private InnovationLaboratoryContractService innovationLaboratoryContractService;
	
	private final String PER_PREFIX = "but:quality:change:";
	
	/**
	 *@Description: 新增省质检中心变更
	 *@param innovationQualityChangeVo 省质检中心变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增省质检中心变更", notes = "新增省质检中心变更")
	@SystemLogAnnotation(type = "省质检中心变更",value = "新增省质检中心变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveInnovationQualityChange(@RequestBody InnovationQualityChangeVo innovationQualityChangeVo) {
	    if(StringUtils.isBlank(innovationQualityChangeVo.getOrgName())) {
            innovationQualityChangeVo.setOrgName(getCurrentOrgName());
            innovationQualityChangeVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityChangeService.saveOrUpdateInnovationQualityChange(innovationQualityChangeVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改省质检中心变更
	 *@param innovationQualityChange 省质检中心变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改省质检中心变更", notes = "修改省质检中心变更")
	@SystemLogAnnotation(type = "省质检中心变更",value = "修改省质检中心变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateInnovationQualityChange(@RequestBody InnovationQualityChangeVo innovationQualityChange) {
		String id = innovationQualityChangeService.saveOrUpdateInnovationQualityChange(innovationQualityChange);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除省质检中心变更(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除省质检中心变更", notes = "删除省质检中心变更")
	@SystemLogAnnotation(type = "省质检中心变更",value = "删除省质检中心变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteInnovationQualityChange(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		innovationQualityChangeService.deleteInnovationQualityChange(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除省质检中心变更(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除省质检中心变更", notes = "删除省质检中心变更")
	@SystemLogAnnotation(type = "省质检中心变更",value = "删除省质检中心变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiInnovationQualityChange(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		innovationQualityChangeService.deleteMultiInnovationQualityChange(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询省质检中心变更详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询省质检中心变更详情", notes = "查询省质检中心变更详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		InnovationQualityChange  innovationQualityChange=innovationQualityChangeService.findById(id);
		return RestApiResponse.ok(innovationQualityChange);
	}

	/**
	 *@Description: 查询省质检中心任务书详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findContractById")
	@ApiOperation(value = "查询省质检中心变更详情", notes = "查询省质检中心变更详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findContractById(@RequestParam("id") String id) {
		InnovationQualityChange innovationQualityChange=innovationQualityChangeService.findById(id);
		try {
			return RestApiResponse.ok(innovationQualityContractService.findById(innovationQualityChange.getContractId()));
		} catch (Exception e){
			return RestApiResponse.ok(innovationLaboratoryContractService.findById(innovationQualityChange.getContractId()));
		}
	}
	
	/**
	 *@Description: 分页查询省质检中心变更
	 *@param innovationQualityChangeVo 省质检中心变更 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询省质检中心变更", notes = "分页查询省质检中心变更")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody InnovationQualityChangeVo innovationQualityChangeVo) {
		PageInfo<InnovationQualityChange>  innovationQualityChange=innovationQualityChangeService.findPageByQuery(innovationQualityChangeVo);
		return RestApiResponse.ok(innovationQualityChange);
	}

	/**
	 *@Description: 省质检中心变更提交
	 *@param innovationQualityChangeVo 省质检中心变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/submit")
	@ApiOperation(value = "省质检中心变更提交", notes = "省质检中心变更提交")
	@SystemLogAnnotation(type = "省质检中心变更",value = "省质检中心变更提交")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"submit')")
	public RestApiResponse<?> submitInnovationQualityChange(@RequestBody InnovationQualityChangeVo innovationQualityChangeVo) {
	    if(StringUtils.isBlank(innovationQualityChangeVo.getOrgName())) {
            innovationQualityChangeVo.setOrgName(getCurrentOrgName());
            innovationQualityChangeVo.setOrgCode(getCurrentScienceOrgId());
		}
		String id = innovationQualityChangeService.submitInnovationQualityChange(innovationQualityChangeVo);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 省质检中心变更审核
	 *@param innovationQualityChangeVo 省质检中心变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/audit")
	@ApiOperation(value = "省质检中心变更审核", notes = "省质检中心变更审核")
	@SystemLogAnnotation(type = "省质检中心变更",value = "省质检中心变更审核")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> auditInnovationQualityChange(@RequestBody InnovationQualityChangeVo innovationQualityChangeVo) {
		String id = innovationQualityChangeService.auditInnovationQualityChange(innovationQualityChangeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心变更退回
	 *@param innovationQualityChangeVo 省质检中心变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/sendBack")
	@ApiOperation(value = "省质检中心变更退回", notes = "省质检中心变更退回")
	@SystemLogAnnotation(type = "省质检中心变更",value = "省质检中心变更退回")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"audit')")
	public RestApiResponse<?> sendBackInnovationQualityChange(@RequestBody InnovationQualityChangeVo innovationQualityChangeVo) {
		String id = innovationQualityChangeService.sendBackInnovationQualityChange(innovationQualityChangeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心变更任务书下达
	 *@param innovationQualityChangeVo 省质检中心变更数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/release")
	@ApiOperation(value = "省质检中心变更任务书下达", notes = "省质检中心变更任务书下达")
	@SystemLogAnnotation(type = "省质检中心变更",value = "省质检中心变更任务书下达")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"release')")
	public RestApiResponse<?> releaseInnovationQualityChange(@RequestBody InnovationQualityChangeVo innovationQualityChangeVo) {
		String id = innovationQualityChangeService.releaseInnovationQualityChange(innovationQualityChangeVo);
		return RestApiResponse.ok(id);
	}

	/**
	 *@Description: 省质检中心变更待办
	 *@param vo 省质检中心变更 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/todo")
	@ApiOperation(value = "省质检中心变更待办", notes = "省质检中心变更待办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> todoList(@RequestBody InnovationQualityChangeVo vo) {
		PageInfo list;
		if(QueryTypeEnum.TODO.getType().equals(vo.getQueryType())){
			list = innovationQualityChangeService.todoList(vo);
		} else if(QueryTypeEnum.FINISHED.getType().equals(vo.getQueryType())){
			list = innovationQualityChangeService.finishedList(vo);
		} else if(QueryTypeEnum.END.getType().equals(vo.getQueryType())){
			list = innovationQualityChangeService.endList(vo);
		} else {
			list = innovationQualityChangeService.todoList(vo);
		}
		return RestApiResponse.ok(list);
	}
	/**
	 *@Description: 省质检中心变更已办
	 *@param innovationQualityChangeVo 专家委员会库 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/finished")
	@ApiOperation(value = "省质检中心变更已办", notes = "省质检中心变更已办")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> finished(@RequestBody InnovationQualityChangeVo innovationQualityChangeVo) {
		PageInfo<InnovationQualityChange>  innovationQualityChange=innovationQualityChangeService.finishedList(innovationQualityChangeVo);
		return RestApiResponse.ok(innovationQualityChange);
	}
	
	/**
	 *@Description: 省质检中心变更已完成
	 *@param innovationQualityChangeVo 省质检中心变更 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/end")
	@ApiOperation(value = "省质检中心变更已完成", notes = "省质检中心变更已完成")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> endList(@RequestBody InnovationQualityChangeVo innovationQualityChangeVo) {
		PageInfo<InnovationQualityChange>  innovationQualityChange=innovationQualityChangeService.endList(innovationQualityChangeVo);
		return RestApiResponse.ok(innovationQualityChange);
	}
}
