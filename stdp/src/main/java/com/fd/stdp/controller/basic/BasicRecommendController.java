package com.fd.stdp.controller.basic;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicRecommend;
import com.fd.stdp.beans.basic.vo.BasicRecommendVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.annotation.SystemLogAnnotation;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.service.basic.BasicRecommendService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 *@Description: 窗口表
 *@Author: wangsh
 *@Date: 2022-02-15 15:20:33
 */
@RestController
@RequestMapping("/basic/basicRecommend")
@Api(value="窗口表", description="窗口表")
public class BasicRecommendController  extends BaseController {

	@Autowired
	private BasicRecommendService basicRecommendService;
	
	private final String PER_PREFIX = "basic:but:recommand:";
	
	/**
	 *@Description: 新增窗口表
	 *@param basicRecommend 窗口表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/save")
	@ApiOperation(value = "新增窗口表", notes = "新增窗口表")
	@SystemLogAnnotation(type = "窗口表",value = "新增窗口表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"save')")
	public RestApiResponse<?> saveBasicRecommend(@RequestBody BasicRecommendVo basicRecommend) {
		String id = basicRecommendService.saveOrUpdateBasicRecommend(basicRecommend);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 修改窗口表
	 *@param basicRecommend 窗口表数据 json
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/update")
	@ApiOperation(value = "修改窗口表", notes = "修改窗口表")
	@SystemLogAnnotation(type = "窗口表",value = "修改窗口表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"update')")
	public RestApiResponse<?> updateBasicRecommend(@RequestBody BasicRecommendVo basicRecommend) {
		String id = basicRecommendService.saveOrUpdateBasicRecommend(basicRecommend);
		return RestApiResponse.ok(id);
	}
	
	/**
	 *@Description: 删除窗口表(判断 关联数据是否可以删除)
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	/*@RepeatSubAnnotation
	@GetMapping("/delete")
	@ApiOperation(value = "删除窗口表", notes = "删除窗口表")
	@SystemLogAnnotation(type = "窗口表",value = "删除窗口表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteBasicRecommend(@RequestParam("id") String id) {
		//判断 关联数据是否可以删除
		basicRecommendService.deleteBasicRecommend(id);
		return RestApiResponse.ok();
	}*/

	/**
	 *@Description: 批量删除窗口表(判断 关联数据是否可以删除)
	 *@param ids
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@RepeatSubAnnotation
	@PostMapping("/delete")
	@ApiOperation(value = "删除窗口表", notes = "删除窗口表")
	@SystemLogAnnotation(type = "窗口表",value = "删除窗口表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"delete')")
	public RestApiResponse<?> deleteMultiBasicRecommend(@RequestBody List<String> ids) {
		//判断 关联数据是否可以删除
		basicRecommendService.deleteMultiBasicRecommend(ids);
		return RestApiResponse.ok();
	}
	
	/**
	 *@Description: 查询窗口表详情
	 *@param id
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findById")
	@ApiOperation(value = "查询窗口表详情", notes = "查询窗口表详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findById(@RequestParam("id") String id) {
		BasicRecommend  basicRecommend=basicRecommendService.findById(id);
		return RestApiResponse.ok(basicRecommend);
	}



	/**
	 *@Description: 查询窗口表详情
	 *@param type
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@GetMapping("/findByRecommandType")
	@ApiOperation(value = "查询窗口表详情", notes = "查询窗口表详情")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"find')")
	public RestApiResponse<?> findByRecommandType(@RequestParam("type") String type) {
		BasicRecommend  basicRecommend=basicRecommendService.findByRecommandType(type);
		return RestApiResponse.ok(basicRecommend);
	}
	
	/**
	 *@Description: 分页查询窗口表
	 *@param basicRecommendVo 窗口表 查询条件
	 *@return RestApiResponse<?>
	 *@Author: wangsh
	 */
	@PostMapping("/findPageByQuery")
	@ApiOperation(value = "分页查询窗口表", notes = "分页查询窗口表")
	@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
	public RestApiResponse<?> findPageByQuery(@RequestBody BasicRecommendVo basicRecommendVo) {
		PageInfo<BasicRecommend>  basicRecommend=basicRecommendService.findPageByQuery(basicRecommendVo);
		return RestApiResponse.ok(basicRecommend);
	}

}
