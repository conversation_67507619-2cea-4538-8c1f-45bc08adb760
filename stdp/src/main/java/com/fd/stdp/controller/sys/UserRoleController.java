package com.fd.stdp.controller.sys;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fd.stdp.beans.sys.SysRole;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.beans.sys.SysUserRole;
import com.fd.stdp.beans.sys.vo.SysUserInfoVO;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.annotation.RepeatSubAnnotation;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.service.sys.RoleService;
import com.fd.stdp.service.sys.SysUserService;
import com.fd.stdp.service.sys.UserRoleService;
import com.github.pagehelper.PageInfo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 用户角色controller
 * 
 * <AUTHOR>
 * @date 2018/03/05
 */
@Controller
@RequestMapping("/sys/userRole")
@Api(value = "用户角色管理", description = "用户角色接口")
public class UserRoleController extends BaseController {

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private SysUserService userService;

    @Autowired
    private RoleService roleService;

    /**
     * 查询角色列表
     * 
     * @param userId
     * @return
     */
    @GetMapping(value = "/list")
    @ResponseBody
    @ApiOperation(value = "查询用户角色", notes = "查询用户角色")
    public RestApiResponse<?> listUserRole(String userId) {
        // String userName = this.getCurrentUserName();
        SysUser user = this.userService.findById(userId);
        if (user == null) {
            return RestApiResponse.error("未查找到当前用户信息");
        }
        List<SysUserRole> dataList = this.userRoleService.list(user.getId());
        return RestApiResponse.ok(dataList);
    }

    // /**
    // * 查询角色列表
    // *
    // * @return
    // */
    // @GetMapping(value = "/listAll")
    // @ResponseBody
    // @ApiOperation(value = "查询用户角色", notes = "查询用户角色")
    // public RestApiResponse<?> listAll() {
    // // String userName = this.getCurrentUserName();
    // List<SysRole> list = this.roleService.listAll();
    // return RestApiResponse.ok(list);
    // }

    /**
     * 根据角色编码查询角色列表
     * 
     * @param roleCode
     * @return
     */
    @GetMapping(value = "/listByRoleCode")
    @ResponseBody
    @ApiOperation(value = "根据角色编码查询角色列表", notes = "根据角色编码查询角色列表")
    public RestApiResponse<?>
        listUserRoleByRoleCode(@RequestParam(name = "roleCode", required = true) String roleCode) {
        // String userName = this.getCurrentUserName();
        List<SysUserRole> userRoleList = this.userRoleService.listByRoleCode(roleCode);
        return RestApiResponse.ok(userRoleList);
    }

    /**
     * 根据角色编码查询 用户列表
     * 
     * @param roleCode
     * @return
     */
    @GetMapping(value = "/listUserByRoleCode")
    @ResponseBody
    @ApiOperation(value = "根据角色编码获取用户列表", notes = "根据角色编码获取用户列表")
    public RestApiResponse<?> listUserRoleByRoleCode(@RequestParam(name = "roleCode", required = true) String roleCode,
        @RequestParam(name = "userName", required = false) String userName) {
        List<SysUserRole> userRoleList = this.userRoleService.listByRoleCode(roleCode);
        List<SysUserInfoVO> userList = new ArrayList<>();
        for (int i = 0; i < userRoleList.size(); i++) {
            SysUser user = this.userService.findById(userRoleList.get(i).getUserId());
            SysUserInfoVO userInfoVO = new SysUserInfoVO();
            if (user != null) {
                BeanUtils.copyProperties(user, userInfoVO);
                // userInfoVO.setUserRoleStatus(userRoleList.get(i).getStatus());
                if (StringUtils.isEmpty(userName)) {
                    userList.add(userInfoVO);
                } else {
                    if (user.getUsername().contains(userName)) {
                        userList.add(userInfoVO);
                    }
                }
            }

        }
        return RestApiResponse.ok(userList);
    }

    /**
     * 用户保存角色权限
     * 
     * @param userId
     * @param roleIds
     * @return
     */
    @RepeatSubAnnotation
    @PostMapping(value = "/saveOrUpdateRole")
    @ResponseBody
    @ApiOperation(value = "用户保存角色权限", notes = "用户保存角色权限")
    public RestApiResponse<?> saveOrUpdateRole(@RequestParam("userId") String userId,
        @RequestBody List<String> roleIds) {
        userRoleService.saveOrUpdateRole(userId, roleIds);
        return RestApiResponse.ok();
    }

    /**
     * 查询角色列表
     * 
     * @param userIdStr
     * @return
     */
    @RepeatSubAnnotation
    @PostMapping(value = "/saveOrUpdate")
    @ResponseBody
    @ApiOperation(value = "新增或保存用户角色", notes = "新增或保存用户角色")
    public RestApiResponse<?> saveOrUpdate(@RequestParam("userIdStr") String userIdStr,
        @RequestParam("roleIds") Long roleId) {
        // String userName = this.getCurrentUserName();
        // SysUser user = this.userService.getUserByUsername(userName);
        SysRole role = this.roleService.selectById(roleId);
        if (role == null) {
            throw new ServiceException("记录不存在");
        }
        String[] userIds = null;
        if (StringUtils.isEmpty(userIdStr)) {
            userIds = new String[0];
        } else {
            String str[] = userIdStr.split(",");
            userIds = new String[str.length];
            for (int i = 0; i < str.length; i++) {
                userIds[i] = str[i];
            }
        }
        this.userRoleService.saveOrUpdateUserRole(role.getId(), userIds);
        return RestApiResponse.ok();
    }

    /**
     * 根据角色编码查询 用户列表
     *
     * @param roleCode
     * @return
     */
    @GetMapping(value = "/pageUserByRoleCode")
    @ResponseBody
    @ApiOperation(value = "根据角色编码获取用户分页", notes = "根据角色编码获取用户分页")
    public RestApiResponse<?> pageUserByRoleCode(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
        @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
        @RequestParam(name = "roleCode", required = true) String roleCode,
        @RequestParam(name = "userName", required = false) String userName) {
        PageInfo<SysUserInfoVO> sysUserRolePageInfo =
            this.userRoleService.pageByRoleCodeAndName(pageNum, pageSize, roleCode, userName);
        return RestApiResponse.ok(sysUserRolePageInfo);
    }

    /**
     * 清空角色下用户
     *
     * @param roleId
     * @return
     */
    @RepeatSubAnnotation
    @PostMapping(value = "/deleteByRoleId")
    @ResponseBody
    @ApiOperation(value = "清空角色下用户", notes = "清空角色下用户")
    public RestApiResponse<?> deleteByRoleId(String roleId) {
        // String userName = this.getCurrentUserName();
        // SysUser user = this.userService.getUserByUsername(userName);
        SysRole role = this.roleService.selectById(roleId);
        if (role == null) {
            throw new ServiceException("记录不存在");
        }
        this.userRoleService.deleteByRoleId(roleId);
        return RestApiResponse.ok();
    }

    /**
     * 清空角色下用户
     *
     * @param roleId
     * @return
     */
    @RepeatSubAnnotation
    @GetMapping(value = "/deleteByRoleIdAndUserId")
    @ResponseBody
    @ApiOperation(value = "删除角色下用户", notes = "删除角色下用户")
    public RestApiResponse<?> deleteByRoleIdAndUserId(@RequestParam("roleId") String roleId,
        @RequestParam("userId") String userId) {
        // String userName = this.getCurrentUserName();
        // SysUser user = this.userService.getUserByUsername(userName);
        this.userRoleService.deleteByRoleIdAndUserId(roleId, userId);
        return RestApiResponse.ok();
    }

    @RepeatSubAnnotation
    @GetMapping(value = "/swtichStatus")
    @ResponseBody
    @ApiOperation(value = "暂停开启", notes = "暂停开启")
    public RestApiResponse<?> swtichStatus(@RequestParam("roleId") String roleId, @RequestParam("userId") String userId,
        @RequestParam("status") String status) {
        // String userName = this.getCurrentUserName();
        // SysUser user = this.userService.getUserByUsername(userName);
        this.userRoleService.swtichStatus(roleId, userId, status);
        return RestApiResponse.ok();
    }

}
