package com.fd.stdp.controller.main;

import com.fd.stdp.beans.basic.BasicPerson;
import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.basic.vo.BasicPersonVo;
import com.fd.stdp.beans.basic.vo.BasicScienceOrgVo;
import com.fd.stdp.beans.innovation.InnovationQualityChange;
import com.fd.stdp.beans.innovation.InnovationQualityContract;
import com.fd.stdp.beans.innovation.vo.InnovationGeneralInnovationApplyVo;
import com.fd.stdp.beans.innovation.vo.InnovationQualityChangeVo;
import com.fd.stdp.beans.notice.NoticeApply;
import com.fd.stdp.beans.notice.WarningBean;
import com.fd.stdp.beans.notice.vo.NoticeApplyVo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.beans.project.vo.ProjectInfoStatistics;
import com.fd.stdp.beans.tech.vo.TechAwardsApplyVo;
import com.fd.stdp.beans.work.WorkReport;
import com.fd.stdp.beans.work.vo.WorkReportVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.controller.notice.NoticeApplyController;
import com.fd.stdp.controller.notice.WarningController;
import com.fd.stdp.controller.project.ProjectApplyInfoController;
import com.fd.stdp.controller.work.WorkReportController;
import com.fd.stdp.enums.QueryTypeEnum;
import com.fd.stdp.service.main.MainPageService;
import com.fd.stdp.service.project.ProjectApplyInfoService;
import com.fd.stdp.service.work.WorkReportService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

import static com.fd.stdp.common.BaseController.getCurrentUserId;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/4/1 16:25
 */
@RestController
@RequestMapping("/main/page")
@Api(value = "首页接口", description = "首页接口")
public class MainPageController {

    @Autowired
    private WorkReportController workReportController;

    @Autowired
    private ProjectApplyInfoController projectApplyInfoController;

    @Autowired
    private NoticeApplyController noticeApplyController;

    @Autowired
    private WarningController warningController;

    @Autowired
    private MainPageService mainPageService;

    private final String PER_PREFIX = "btn:main:page:";

    // 缓存超时时间
    @Value("${stdp.mainPage.outTime}")
    private Long OUT_TIME = 3600 * 1000L;

    private Map<String, MainPageFlash> flashMap = new HashMap<>();

    private Object getFlash(String key){
        key = key + getCurrentUserId();
        if(flashMap.containsKey(key)) {
            MainPageFlash flash = flashMap.get(key);
            if(System.currentTimeMillis() < flash.getFlashTime()) {
                return flash.getValue();
            }
        }
        return null;
    }
    
    private synchronized Object setFlashAndReturn(String key, Object value){
        if(flashMap == null) {
            flashMap = new HashMap<>();
        }
        // 清理过期缓存
        List<String> removeList = null;
        for(String k:flashMap.keySet()){
            MainPageFlash tmp = flashMap.get(k);
            if(System.currentTimeMillis() > tmp.getFlashTime()) {
                if(removeList == null){
                    removeList = new ArrayList<>();
                }
                removeList.add(k);
            }
        }
        if(removeList != null) {
            removeList.forEach(k -> flashMap.remove(k));
        }
        // 设置缓存
        MainPageFlash flash;
        if(!flashMap.containsKey(key)) {
            flash = new MainPageFlash();
            flashMap.put(key, flash);
        } 
        flash = flashMap.get(key);
        flash.setValue(value);
        flash.setFlashTime(System.currentTimeMillis() + OUT_TIME);
        return value;
    }

    @GetMapping("/clearFlash")
    @ApiOperation(value = "分页查询工作交流", notes = "分页查询工作交流")
    public Object findPageByQuery() {
        flashMap = new HashMap<>();
        return RestApiResponse.ok();
    }

    /**
     * @param workReportVo 工作交流 查询条件
     * @return Object
     * @Description: 分页查询工作交流
     * @Author: yujianfei
     */
    @PostMapping("/work/report/findPageByQuery")
    @ApiOperation(value = "分页查询工作交流", notes = "分页查询工作交流")
    // @PreAuthorize("hasAuthority('" + PER_PREFIX + "query')")
    public Object findPageByQuery(@RequestBody WorkReportVo workReportVo) {
        String key = "work report";
        Object res = getFlash(key);
        return res!=null?res:setFlashAndReturn(key, workReportController.findPageByQuery(workReportVo));
    }

    /**
     * @param vo 项目基本信息 查询条件
     * @return Object
     * @Description: 项目申报已办
     * @Author: wangsh
     */
    @PostMapping("/project/applyInfo/todo")
    @ApiOperation(value = "项目申报已办", notes = "项目申报已办")
    public Object todoPageByQuery(@RequestBody ProjectApplyInfoVo vo) {
        String key = "project applyInfo";
        Object res = getFlash(key);
        return res!=null?res:setFlashAndReturn(key, projectApplyInfoController.todoPageByQuery(vo));
    }


    /**
     *@Description: 分页查询
     *@param noticeApplyVo  查询条件
     *@return Object
     *@Author: wangsh
     */
    @PostMapping("/notice/noticeApply/findPageByQuery")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    //@PreAuthorize("hasAuthority('"+PER_PREFIX+"query')")
    public Object findPageByQuery(@RequestBody NoticeApplyVo noticeApplyVo) {
        String key = "notice noticeApply";
        Object res = getFlash(key);
        return res!=null?res:setFlashAndReturn(key, noticeApplyController.findPageByQuery(noticeApplyVo));
    }

    /**
     *@Description: 分页查询
     *@param warning  查询条件
     *@return Object
     *@Author: wangsh
     */
    @PostMapping("/notice/warning/projectWarning")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    public Object projectWarning(@RequestBody WarningBean warning) {
        String key = "notice warning projectWarning";
        Object res = getFlash(key);
        return res!=null?res:setFlashAndReturn(key, warningController.projectWarning(warning));
    }

    /**
     *@Description: 分页查询
     *@param warning  查询条件
     *@return Object
     *@Author: wangsh
     */
    @PostMapping("/notice/warning/innovationWarning")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    public Object innovationWarning(@RequestBody WarningBean warning) {
        String key = "notice warning innovationWarning";
        Object res = getFlash(key);
        return res!=null?res:setFlashAndReturn(key, warningController.innovationWarning(warning));
    }

    /**
     *@Description: 分页查询
     *@param warning  查询条件
     *@return Object
     *@Author: wangsh
     */
    @PostMapping("/notice/warning/talentWarning")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    public Object talentWarning(@RequestBody WarningBean warning) {
        String key = "notice warning talentWarning";
        Object res = getFlash(key);
        return res!=null?res:setFlashAndReturn(key, warningController.talentWarning(warning));
    }


    /**
     *@Description: 查询机构
     *@param basicScienceOrg  查询条件
     *@return Object
     *@Author: wangsh
     */
    @PostMapping("/query/scienceOrg")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    public Object queryScienceOrg(@RequestBody BasicScienceOrgVo basicScienceOrg) {
        String key = "query scienceOrg";
        Object res = getFlash(key);
        return res!=null?res:setFlashAndReturn(key, mainPageService.queryScienceOrg(basicScienceOrg));
    }

    /**
     *@Description: 查询创新载体
     *@param vo  查询条件
     *@return Object
     *@Author: wangsh
     */
    @PostMapping("/query/innovation")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    public Object queryInnovation(@RequestBody InnovationGeneralInnovationApplyVo vo) {
        String key = "query innovation";
        Object res = getFlash(key);
        return res!=null?res:setFlashAndReturn(key, mainPageService.queryInnovation(vo));
    }

    /**
     *@Description: 查询项目
     *@param statistics  查询条件
     *@return Object
     *@Author: wangsh
     */
    @PostMapping("/query/project")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    public Object queryProject(@RequestBody ProjectInfoStatistics statistics) {
        String key = "query project";
        Object res = getFlash(key);
        return res!=null?res:setFlashAndReturn(key, mainPageService.queryProject(statistics));
    }

    /**
     *@Description: 查询机构人员
     *@param vo  查询条件
     *@return Object
     *@Author: wangsh
     */
    @PostMapping("/query/person")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @PreAuthorize("hasAuthority('"+PER_PREFIX+"queryPerson')")
    public Object queryPerson(@RequestBody BasicPersonVo vo) {
        String key = "query person";
        Object res = getFlash(key);
        return res!=null?res:setFlashAndReturn(key, mainPageService.queryPerson(vo));
    }

    /**
     *@Description: 查询机构人员
     *@param vo  查询条件
     *@return Object
     *@Author: wangsh
     */
    @PostMapping("/query/tech")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    public Object queryTech(@RequestBody TechAwardsApplyVo vo) {
        String key = "query tech";
        Object res = getFlash(key);
        return res!=null?res:setFlashAndReturn(key, mainPageService.queryTech(vo));
    }
}
