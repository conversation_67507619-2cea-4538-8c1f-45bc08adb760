package com.fd.stdp.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Description 描述
 * @Date 2023/9/13 15:51
 * <AUTHOR>
 **/

@Data
@Component
@ConfigurationProperties(prefix = ZhuanjiaProperties.PREFIX)
public class ZhuanjiaProperties {

    public final static String PREFIX = "expert.zhuanjia";

    /**
     * 默认密码
     */
    private String password;

    /**
     * 默认角色
     */
    private String roleCode;

    private String userTypeCode;

}
