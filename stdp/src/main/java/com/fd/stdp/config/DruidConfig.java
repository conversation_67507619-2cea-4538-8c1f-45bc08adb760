//package com.fd.stdp.config;
//
//import java.sql.SQLException;
//
//import javax.sql.DataSource;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.boot.web.servlet.ServletRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//
//import com.alibaba.druid.pool.DruidDataSource;
//import com.alibaba.druid.support.http.StatViewServlet;
//import com.alibaba.druid.support.http.WebStatFilter;
//
//
///**
// * <AUTHOR> * @date
// * @desc 数据库连接池配置文件.
// */
//@Configuration
//public class DruidConfig {
//	public static final Logger log = LoggerFactory.getLogger(DruidConfig.class);
//	@Value("${spring.datasource.druidUsername}")
//	private String druidUserName;
//	@Value("${spring.datasource.druidPassword}")
//	private String druidPassword;
//	@Value("${spring.datasource.druidResetEnable}")
//	private String druidResetEnable;
//
//	@Bean
//	public ServletRegistrationBean<StatViewServlet> druidServlet() {
//		ServletRegistrationBean<StatViewServlet> servletRegistrationBean = new ServletRegistrationBean<StatViewServlet>(
//				new StatViewServlet(), "/druid/*");
//		// IP白名单/IP黑名单 name:allow/deny value:************,127.0.0.1
//		servletRegistrationBean.addInitParameter("loginUsername", druidUserName);
//		servletRegistrationBean.addInitParameter("loginPassword", druidPassword);
//		servletRegistrationBean.addInitParameter("resetEnable", druidResetEnable);
//		return servletRegistrationBean;
//	}
//
//	@Bean
//	public FilterRegistrationBean<WebStatFilter> filterRegistrationBean() {
//		FilterRegistrationBean<WebStatFilter> filterRegistrationBean = new FilterRegistrationBean<WebStatFilter>(
//				new WebStatFilter());
//		filterRegistrationBean.addUrlPatterns("/*");
//		filterRegistrationBean.addInitParameter("exclusions", "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*");
//		return filterRegistrationBean;
//	}
//
//	/**
//	 * 连接池属性配置
//	 */
//	@Configuration
//	@ConfigurationProperties(prefix = "spring.datasource")
//	class DruidProperties {
//
//		private String url;
//
//		private String username;
//
//		private String password;
//
//		private String driverClassName;
//
//		private int initialSize;
//
//		private int minIdle;
//
//		private int maxActive;
//
//		private int maxWait;
//
//		private int timeBetweenEvictionRunsMillis;
//
//		private int minEvictableIdleTimeMillis;
//
//		private String validationQuery;
//
//		private boolean testWhileIdle;
//
//		private boolean testOnBorrow;
//
//		private boolean testOnReturn;
//
//		private boolean poolPreparedStatements;
//
//		private int maxPoolPreparedStatementPerConnectionSize;
//
//		private String filters;
//
//		private String connectionProperties;
//
//		public String getUrl() {
//			return url;
//		}
//
//		public void setUrl(String url) {
//			this.url = url;
//		}
//
//		public String getUsername() {
//			return username;
//		}
//
//		public void setUsername(String username) {
//			this.username = username;
//		}
//
//		public String getPassword() {
//			return password;
//		}
//
//		public void setPassword(String password) {
//			this.password = password;
//		}
//
//		public String getDriverClassName() {
//			return driverClassName;
//		}
//
//		public void setDriverClassName(String driverClassName) {
//			this.driverClassName = driverClassName;
//		}
//
//		public int getInitialSize() {
//			return initialSize;
//		}
//
//		public void setInitialSize(int initialSize) {
//			this.initialSize = initialSize;
//		}
//
//		public int getMinIdle() {
//			return minIdle;
//		}
//
//		public void setMinIdle(int minIdle) {
//			this.minIdle = minIdle;
//		}
//
//		public int getMaxActive() {
//			return maxActive;
//		}
//
//		public void setMaxActive(int maxActive) {
//			this.maxActive = maxActive;
//		}
//
//		public int getMaxWait() {
//			return maxWait;
//		}
//
//		public void setMaxWait(int maxWait) {
//			this.maxWait = maxWait;
//		}
//
//		public int getTimeBetweenEvictionRunsMillis() {
//			return timeBetweenEvictionRunsMillis;
//		}
//
//		public void setTimeBetweenEvictionRunsMillis(int timeBetweenEvictionRunsMillis) {
//			this.timeBetweenEvictionRunsMillis = timeBetweenEvictionRunsMillis;
//		}
//
//		public int getMinEvictableIdleTimeMillis() {
//			return minEvictableIdleTimeMillis;
//		}
//
//		public void setMinEvictableIdleTimeMillis(int minEvictableIdleTimeMillis) {
//			this.minEvictableIdleTimeMillis = minEvictableIdleTimeMillis;
//		}
//
//		public String getValidationQuery() {
//			return validationQuery;
//		}
//
//		public void setValidationQuery(String validationQuery) {
//			this.validationQuery = validationQuery;
//		}
//
//		public boolean isTestWhileIdle() {
//			return testWhileIdle;
//		}
//
//		public void setTestWhileIdle(boolean testWhileIdle) {
//			this.testWhileIdle = testWhileIdle;
//		}
//
//		public boolean isTestOnBorrow() {
//			return testOnBorrow;
//		}
//
//		public void setTestOnBorrow(boolean testOnBorrow) {
//			this.testOnBorrow = testOnBorrow;
//		}
//
//		public boolean isTestOnReturn() {
//			return testOnReturn;
//		}
//
//		public void setTestOnReturn(boolean testOnReturn) {
//			this.testOnReturn = testOnReturn;
//		}
//
//		public boolean isPoolPreparedStatements() {
//			return poolPreparedStatements;
//		}
//
//		public void setPoolPreparedStatements(boolean poolPreparedStatements) {
//			this.poolPreparedStatements = poolPreparedStatements;
//		}
//
//		public int getMaxPoolPreparedStatementPerConnectionSize() {
//			return maxPoolPreparedStatementPerConnectionSize;
//		}
//
//		public void setMaxPoolPreparedStatementPerConnectionSize(int maxPoolPreparedStatementPerConnectionSize) {
//			this.maxPoolPreparedStatementPerConnectionSize = maxPoolPreparedStatementPerConnectionSize;
//		}
//
//		public String getFilters() {
//			return filters;
//		}
//
//		public void setFilters(String filters) {
//			this.filters = filters;
//		}
//
//		public String getConnectionProperties() {
//			return connectionProperties;
//		}
//
//		public void setConnectionProperties(String connectionProperties) {
//			this.connectionProperties = connectionProperties;
//		}
//
//		@Bean
//		@Primary
//		public DataSource dataSource() throws SQLException {
//			DruidDataSource datasource = new DruidDataSource();
//			datasource.setUrl(url);
//			datasource.setUsername(username);
//			datasource.setPassword(password);
//			datasource.setDriverClassName(driverClassName);
//
//			// configuration
//			datasource.setInitialSize(initialSize);
//			datasource.setMinIdle(minIdle);
//			datasource.setMaxActive(maxActive);
//			datasource.setMaxWait(maxWait);
//			datasource.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
//			datasource.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
//			datasource.setValidationQuery(validationQuery);
//			datasource.setTestWhileIdle(testWhileIdle);
//			datasource.setTestOnBorrow(testOnBorrow);
//			datasource.setTestOnReturn(testOnReturn);
//			datasource.setPoolPreparedStatements(poolPreparedStatements);
//			datasource.setMaxPoolPreparedStatementPerConnectionSize(maxPoolPreparedStatementPerConnectionSize);
//			datasource.setConnectionProperties(connectionProperties);
//			datasource.setFilters(filters);
//			return datasource;
//		}
//	}
//}
