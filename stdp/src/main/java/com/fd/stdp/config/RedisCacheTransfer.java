package com.fd.stdp.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 写入redisTemplate 为mybaits二级缓存提供支持
 * <AUTHOR>
 *
 */
@Component
public class RedisCacheTransfer {
 
	@Value("${mybatis.cacheDay}")
	private Integer cacheDay;
    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        MybatisRedisCache.setRedisTemplate(redisTemplate,cacheDay);
    }
}
