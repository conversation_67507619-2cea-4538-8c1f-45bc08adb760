package com.fd.stdp.config;



import com.fd.stdp.common.redis.RedisUtil;
import com.fd.stdp.constant.RedisConstant;
import com.fd.stdp.util.UUIDUtils;
import com.lzhpo.sensitive.cache.SensitivityPointer;
import com.lzhpo.sensitive.cache.TemplateSensitivityPointer;
import com.lzhpo.sensitive.interceptor.SensitiveInterceptor;
import liquibase.pro.packaged.B;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.HandlerMethod;

@Configuration
public class SensitivityConfig {

    @Autowired
    RedisUtil redisUtil;

    @Bean
    public SensitiveInterceptor sensitiveInterceptor() {
        return new SensitiveInterceptor() {
            @Override
            public boolean intercept(HandlerMethod handlerMethod, Object obj, String name, String value) {
                return true;
            }
        };
    }

    @Bean
    public SensitivityPointer getSensitivityPointer() {
        return new TemplateSensitivityPointer((o, k, v) ->{
            if(StringUtils.isBlank(v)){
                return v;
            }
            String uuid = UUIDUtils.getUUID();
            redisUtil.set(getKey(uuid), v, 60 * 60 * 8);
            return uuid;
        }, k -> {
            if(StringUtils.isBlank(k)){
                return k;
            }
            Object o = redisUtil.get(getKey(k));
            if(o != null){
                return o.toString();
            }
            return k;
        });
    }

    private String getKey(String uuid) {
        String key = RedisConstant.SYS_REDIS + "SENSITIVE:" + uuid;
        return key;
    }

}
