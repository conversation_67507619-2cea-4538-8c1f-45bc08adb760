package com.fd.stdp.config;

import org.apache.ibatis.cache.Cache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
 
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
 
/**
 *  使用Redis来做Mybatis的二级缓存
 * 实现Mybatis的Cache接口
 * <AUTHOR>
 *
 */
public class MybatisRedisCache implements Cache {
 
    private static final Logger logger = LoggerFactory.getLogger(MybatisRedisCache.class);
 
    // 读写锁
    private final ReadWriteLock readWriteLock = new ReentrantReadWriteLock(true);
 
    private static RedisTemplate<String, Object> redisTemplate;
 
    private static Integer cacheDay;
    private String id;
    
    private final static String REDIS_PREFIX="ipmothingsql:";
 
    public MybatisRedisCache(final String id) {
        if (id == null) {
            throw new IllegalArgumentException("Cache instances require an ID");
        }
        logger.info("Redis Cache id " + id);
        this.id = id;
    }
 
    @Override
    public String getId() {
        return this.id;
    }
 
    @Override
    public void putObject(Object key, Object value) {
        if (value != null) {
            // 向Redis中添加数据，有效时间是2天
            redisTemplate.opsForValue().set(REDIS_PREFIX+key.toString(), value,cacheDay, TimeUnit.DAYS);
        }
    }
 
    @Override
    public Object getObject(Object key) {
        try {
            if (key != null) {
                Object obj = redisTemplate.opsForValue().get(REDIS_PREFIX+key.toString());
                return obj;
            }
        } catch (Exception e) {
            logger.error("mybatis redis cache getObject",e);
        }
        return null;
    }
 
    @Override
    public Object removeObject(Object key) {
        try {
            if (key != null) {
                redisTemplate.delete(REDIS_PREFIX+key.toString());
            }
        } catch (Exception e) {
        	 logger.error("mybatis redis cache removeObject",e);
        }
        return null;
    }
 
    @Override
    public void clear() {
        logger.debug("清空缓存");
        try {
            Set<String> keys = redisTemplate.keys(REDIS_PREFIX+"*:" + this.id + "*");
            if (!CollectionUtils.isEmpty(keys)) {
                redisTemplate.delete(keys);
            }
        } catch (Exception e) {
        	logger.error("mybatis redis cache clear",e);
        }
    }
 
    @Override
    public int getSize() {
        Long size = (Long) redisTemplate.execute(new RedisCallback<Long>() {
            @Override
            public Long doInRedis(RedisConnection connection) throws DataAccessException {
                return connection.dbSize();
            }
        });
        return size.intValue();
    }
 
    @Override
    public ReadWriteLock getReadWriteLock() {
        return this.readWriteLock;
    }
    public static void setRedisTemplate(RedisTemplate<String, Object> redisTemplate,Integer cacheDay) {
        MybatisRedisCache.redisTemplate = redisTemplate;
        MybatisRedisCache.cacheDay=cacheDay;
    }
}