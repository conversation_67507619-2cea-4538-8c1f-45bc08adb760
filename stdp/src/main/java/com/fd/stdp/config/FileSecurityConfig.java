package com.fd.stdp.config;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 文件安全配置类
 * 统一管理文件上传的安全策略
 * 
 * <AUTHOR>
 */
public class FileSecurityConfig {
    
    /**
     * 允许上传的文件扩展名（不含点号）
     */
    public static final String[] ALLOWED_EXTENSIONS = {
        "jpg", "jpeg", "png", "gif", "bmp", "tif",
        "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
        "txt", "md", "xml",
        "zip", "rar", "7z", "tar", "gz", "bz2",
        "mp4", "avi", "mov", "wmv", "mpg", "mpeg", "3gp", "rm", "rmvb", "asf", "dat",
        "mp3", "wav", "mid", "ogg", "webm",
        "flv", "swf", "mkv", "ogv",
        "iso", "cab", "cebx"
    };
    
    /**
     * 允许上传的文件扩展名（含点号）
     */
    public static final String[] ALLOWED_EXTENSIONS_WITH_DOT = {
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tif",
        ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
        ".txt", ".md", ".xml",
        ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2",
        ".mp4", ".avi", ".mov", ".wmv", ".mpg", ".mpeg", ".3gp", ".rm", ".rmvb", ".asf", ".dat",
        ".mp3", ".wav", ".mid", ".ogg", ".webm",
        ".flv", ".swf", ".mkv", ".ogv",
        ".iso", ".cab", ".cebx"
    };
    
    /**
     * 危险的文件扩展名（绝对禁止上传）
     */
    public static final String[] DANGEROUS_EXTENSIONS = {
        "exe", "bat", "cmd", "com", "pif", "scr", "vbs", "js", "jar",
        "sh", "php", "asp", "aspx", "jsp", "py", "pl", "rb",
        "class", "dll", "so", "dylib",
        "msi", "deb", "rpm", "dmg",
        "htaccess", "htpasswd"
    };
    
    /**
     * 最大文件大小（50MB）
     */
    public static final long MAX_FILE_SIZE = 50 * 1024 * 1024;
    
    /**
     * 危险文件头特征（十六进制）
     */
    public static final String[] DANGEROUS_FILE_HEADERS = {
        "4D5A",           // EXE, DLL (MZ header)
        "CAFEBABE",       // Java class文件
        "3C3F7068",       // PHP文件 <?ph
        "23212F62",       // Shell脚本 #!/b
        "3C68746D",       // HTML文件 <htm
        "3C736372",       // JavaScript <scr
        "3C25",           // JSP <%
        "7F454C46"        // ELF文件(Linux可执行文件)
    };
    
    private static final Set<String> ALLOWED_EXTENSION_SET = new HashSet<>(Arrays.asList(ALLOWED_EXTENSIONS));
    private static final Set<String> DANGEROUS_EXTENSION_SET = new HashSet<>(Arrays.asList(DANGEROUS_EXTENSIONS));
    
    /**
     * 检查文件扩展名是否被允许
     * @param extension 文件扩展名（不含点号）
     * @return true表示允许，false表示不允许
     */
    public static boolean isAllowedExtension(String extension) {
        if (extension == null || extension.isEmpty()) {
            return false;
        }
        return ALLOWED_EXTENSION_SET.contains(extension.toLowerCase());
    }
    
    /**
     * 检查文件扩展名是否为危险类型
     * @param extension 文件扩展名（不含点号）
     * @return true表示危险，false表示安全
     */
    public static boolean isDangerousExtension(String extension) {
        if (extension == null || extension.isEmpty()) {
            return false;
        }
        return DANGEROUS_EXTENSION_SET.contains(extension.toLowerCase());
    }
    
    /**
     * 检查文件大小是否超过限制
     * @param fileSize 文件大小（字节）
     * @return true表示超过限制，false表示未超过
     */
    public static boolean isFileSizeExceeded(long fileSize) {
        return fileSize > MAX_FILE_SIZE;
    }
    
    /**
     * 检查文件头是否为危险类型
     * @param fileHeader 文件头（十六进制字符串）
     * @return true表示危险，false表示安全
     */
    public static boolean isDangerousFileHeader(String fileHeader) {
        if (fileHeader == null || fileHeader.isEmpty()) {
            return false;
        }
        
        for (String dangerousHeader : DANGEROUS_FILE_HEADERS) {
            if (fileHeader.startsWith(dangerousHeader)) {
                return true;
            }
        }
        
        // 检查ZIP文件是否包含META-INF（可能是JAR文件）
        if (fileHeader.startsWith("504B0304") && fileHeader.contains("4D4554412D494E46")) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 验证文件名是否安全
     * @param filename 文件名
     * @return true表示安全，false表示不安全
     */
    public static boolean isSecureFilename(String filename) {
        if (filename == null || filename.isEmpty()) {
            return false;
        }
        
        // 检查路径遍历攻击
        if (filename.contains("../") || filename.contains("..\\")) {
            return false;
        }
        
        // 检查特殊字符
        if (filename.contains("<") || filename.contains(">") || 
            filename.contains("|") || filename.contains("&") ||
            filename.contains(";") || filename.contains("$") ||
            filename.contains("`")) {
            return false;
        }
        
        return true;
    }
}