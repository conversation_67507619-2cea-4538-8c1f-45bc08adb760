//package com.fd.stdp.config;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import springfox.documentation.builders.ApiInfoBuilder;
//import springfox.documentation.builders.ParameterBuilder;
//import springfox.documentation.builders.PathSelectors;
//import springfox.documentation.builders.RequestHandlerSelectors;
//import springfox.documentation.schema.ModelRef;
//import springfox.documentation.service.ApiInfo;
//import springfox.documentation.service.Parameter;
//import springfox.documentation.spi.DocumentationType;
//import springfox.documentation.spring.web.plugins.Docket;
//import springfox.documentation.swagger2.annotations.EnableSwagger2;
//import springfox.documentation.swagger2.web.Swagger2Controller;
//
///**
// * <AUTHOR>
// * @date
// * @desc Swagger配置.
// */
//@Configuration
//@EnableSwagger2
//@ConfigurationProperties(prefix = "project.swagger")
//@ConditionalOnProperty(name = "project.swagger.enabled", havingValue = "true")
//@ConditionalOnClass(value = Swagger2Controller.class)
//public class SwaggerConfig {
//
//    private String basePackage = "com.fd.stdp.controller";
//
//    private String title = "xxxx服务平台";
//
//    private String description = "xxxx服务平台";
//
//    private String version = "V1.0";
//
//    @Bean
//    public Docket createRestApi() {
//    	ParameterBuilder tokenPar = new ParameterBuilder();
//        List<Parameter> pars = new ArrayList<Parameter>();
//        tokenPar.name("Authorization").description("令牌").modelRef(new ModelRef("string")).parameterType("header").required(false).build();
//        pars.add(tokenPar.build());
//        return new Docket(DocumentationType.SWAGGER_2)
//                .apiInfo(apiInfo())
//                .select()
//				.apis(RequestHandlerSelectors.basePackage(basePackage))
//                .paths(PathSelectors.any())
//                .build()
//                .globalOperationParameters(pars) ;
//    }
//
//    private ApiInfo apiInfo() {
//        return new ApiInfoBuilder()
//                .title(title)
//                .description(description)
//                .version(version)
//                .build();
//    }
//
//	public String getBasePackage() {
//		return basePackage;
//	}
//
//	public void setBasePackage(String basePackage) {
//		this.basePackage = basePackage;
//	}
//
//	public String getTitle() {
//		return title;
//	}
//
//	public void setTitle(String title) {
//		this.title = title;
//	}
//
//	public String getDescription() {
//		return description;
//	}
//
//	public void setDescription(String description) {
//		this.description = description;
//	}
//
//	public String getVersion() {
//		return version;
//	}
//
//	public void setVersion(String version) {
//		this.version = version;
//	}
//
//}
