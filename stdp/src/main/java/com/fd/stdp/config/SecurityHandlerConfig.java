package com.fd.stdp.config;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson2.JSONObject;
import com.fd.stdp.constant.TokenConstants;
import com.fd.stdp.util.JwtUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.beans.sys.vo.ResponseInfo;
import com.fd.stdp.beans.sys.vo.Token;
import com.fd.stdp.common.annotation.OperationLogAnnotation;
import com.fd.stdp.dao.sys.SysUserMapper;
import com.fd.stdp.filter.TokenFilter;
import com.fd.stdp.service.sys.LoginAttemptService;
import com.fd.stdp.service.sys.TokenService;
import com.fd.stdp.util.ResponseUtil;

/**
 * spring security处理器
 */
@Configuration
public class SecurityHandlerConfig {

    @Autowired
    private TokenService tokenService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private LoginAttemptService loginAttemptService;

    public String getCurrentIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 登陆成功，返回Token
     *
     * @return
     */
    @Bean
    public AuthenticationSuccessHandler loginSuccessHandler() {
        return new AuthenticationSuccessHandler() {

            @Override
            @OperationLogAnnotation(notes = "登录成功", module = "用户登录", submodule = "用户登录")
            public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                                Authentication authentication) throws IOException, ServletException {
                loginAttemptService.loginSucceeded(getCurrentIp(request));
                LoginUser loginUser = (LoginUser) authentication.getPrincipal();
                loginUser.setPassword(null);
                Token token = tokenService.saveToken(loginUser);
                loginUser.setToken(token.getToken());

                SysUser user = new SysUser();
                user.setId(loginUser.getId());
                user.setLastLogin(new Date());
                sysUserMapper.updateByPrimaryKeySelective(user);

                ResponseUtil.responseJson(response, HttpStatus.OK.value(), token);
            }
        };
    }

    /**
     * 登陆失败
     *
     * @return
     */
    @Bean
    public AuthenticationFailureHandler loginFailureHandler() {
        return new AuthenticationFailureHandler() {

            @Override
            public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                                                AuthenticationException exception) throws IOException, ServletException {
                loginAttemptService.loginFailed(getCurrentIp(request));
                String msg = null;
                if (exception instanceof BadCredentialsException) {
                    msg = "用户名或密码错误";
                } else {
                    msg = exception.getMessage();
                }
                ResponseInfo info = new ResponseInfo(HttpStatus.UNAUTHORIZED.value() + "", msg);
                ResponseUtil.responseJson(response, HttpStatus.UNAUTHORIZED.value(), info);
            }
        };

    }

    /**
     * 未登录，返回401
     *
     * @return
     */
    @Bean
    public AuthenticationEntryPoint authenticationEntryPoint() {
        return new AuthenticationEntryPoint() {

            @Override
            public void commence(HttpServletRequest request, HttpServletResponse response,
                                 AuthenticationException authException) throws IOException, ServletException {
                ResponseInfo info = new ResponseInfo(HttpStatus.UNAUTHORIZED.value() + "", "请先登录");
                ResponseUtil.responseJson(response, HttpStatus.UNAUTHORIZED.value(), info);
            }
        };
    }

    /**
     * 退出处理
     *
     * @return
     */
    @Bean
    public LogoutSuccessHandler logoutSussHandler() {
        return new LogoutSuccessHandler() {

            @Override
            public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException, ServletException {
                ResponseInfo info = new ResponseInfo(HttpStatus.OK.value() + "", "退出成功");

                String token = TokenFilter.getToken(request);
                tokenService.deleteToken(token);

                ResponseUtil.responseJson(response, HttpStatus.OK.value(), info);
            }
        };

    }

    public void loginResponse(LoginUser loginUser, HttpServletResponse response) {
        // 使用与正常登录相同的token生成方式
        loginUser.setPassword(null);
        Token token = tokenService.saveToken(loginUser);
        loginUser.setToken(token.getToken());

        SysUser user = new SysUser();
        user.setId(loginUser.getId());
        user.setLastLogin(new Date());
        sysUserMapper.updateByPrimaryKeySelective(user);

        ResponseUtil.responseJson(response, HttpStatus.OK.value(), token);
    }

    public JSONObject generateToken(LoginUser loginUser) {
        loginUser.setPassword(null);
        Token token = tokenService.saveToken(loginUser);
        loginUser.setToken(token.getToken());

        SysUser user = new SysUser();
        user.setId(loginUser.getId());
        user.setLastLogin(new Date());
        sysUserMapper.updateByPrimaryKeySelective(user);
        JSONObject tokenJson = JSONObject.parseObject(JSONObject.toJSONString(token));

        // Jwt存储信息
        Map<String, Object> claimsMap = new HashMap<String, Object>();
        claimsMap.put(TokenConstants.USER_KEY, token.getToken());
        claimsMap.put(TokenConstants.DETAILS_USER_ID, loginUser.getId());
        claimsMap.put(TokenConstants.DETAILS_USERNAME, loginUser.getUsername());
        claimsMap.put(TokenConstants.DETAILS_USERNICKNAME, loginUser.getNickname());
        claimsMap.put(TokenConstants.DETAILS_USERTYPE, loginUser.getType());
        tokenJson.put("token", JwtUtils.createToken(claimsMap));
        return tokenJson;
    }

}
