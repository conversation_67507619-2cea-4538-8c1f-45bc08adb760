package com.fd.stdp.config.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 企业用户配置属性
 */
@Component
@ConfigurationProperties(prefix = OrgProperties.PREFIX)
public class OrgProperties {

    public static final String PREFIX = "expert.org";

    /**
     * 默认密码
     */
    private String password;

    /**
     * 角色代码
     */
    private String roleCode;

    /**
     * 用户类型代码
     */
    private String userTypeCode;

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getUserTypeCode() {
        return userTypeCode;
    }

    public void setUserTypeCode(String userTypeCode) {
        this.userTypeCode = userTypeCode;
    }
}