//package com.fd.stdp.config;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.task.TaskExecutor;
//import org.springframework.scheduling.annotation.EnableAsync;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//
///**
// * 线程池配置、启用异步
// * 
// */
//@EnableAsync(proxyTargetClass = true)
//@Configuration
//public class AsycTaskExecutorConfig {
//
//	@Bean
//	public TaskExecutor taskExecutor() {
//		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
//		taskExecutor.setCorePoolSize(50);
//		taskExecutor.setMaxPoolSize(100);
//
//		return taskExecutor;
//	}
//}
