package com.fd.stdp.config;

import java.util.Base64;

import com.fd.stdp.util.RSAEncrypt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

public class MyPasswordEncoder implements PasswordEncoder{
	public static final Logger logger = LoggerFactory.getLogger(MyPasswordEncoder.class);
	private BCryptPasswordEncoder bp=new BCryptPasswordEncoder();
	@Override
	public String encode(CharSequence rawPassword) {
		return bp.encode(rawPassword);
	}

	@Override
	public boolean matches(CharSequence rawPassword, String encodedPassword) {
		try {
			String raw = RSAEncrypt.privateKeyDecrypt(rawPassword.toString(), RSAEncrypt.PRIVATE_KEY);
			return bp.matches(raw, encodedPassword);
		} catch (Exception e) {
			return false;
		}
	}
}
