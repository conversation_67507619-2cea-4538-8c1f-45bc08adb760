package com.fd.stdp.service.basic.impl;

import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.util.ArrayList;
import java.util.List;

import com.fd.stdp.beans.basic.BasicPerson;
import com.fd.stdp.beans.basic.vo.BasicPersonVo;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.dao.basic.BasicPersonMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.sys.QueryConditionService;
import com.fd.stdp.service.user.SysUserUtilService;
import com.fd.stdp.util.ExcelUtils;
import com.fd.stdp.util.LocalImportUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonExpert;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicPersonExpertMapper;
import com.fd.stdp.service.basic.BasicPersonExpertService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.servlet.http.HttpServletResponse;

import static com.fd.stdp.common.BaseController.*;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 专家库
 *@Author: wangsh
 *@Date: 2022-01-07 10:53:32
 */
public class BasicPersonExpertServiceImpl extends BaseServiceImpl<BasicPersonExpertMapper, BasicPersonExpert> implements BasicPersonExpertService{

	public static final Logger logger = LoggerFactory.getLogger(BasicPersonExpertServiceImpl.class);
	
	@Autowired
	private BasicPersonExpertMapper basicPersonExpertMapper;

	@Autowired
	private BasicPersonMapper basicPersonMapper;

	@Autowired
	SysUserUtilService sysUserUtilService;
	@Autowired
	private FlowCommonService flowCommonService;

	@Autowired
	private QueryConditionService queryConditionService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新专家库
	 *@param basicPersonExpert 专家库对象
	 *@return String 专家库ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateBasicPersonExpert(BasicPersonExpert basicPersonExpert) {
		if(basicPersonExpert==null){
			throw new ServiceException("数据异常");
		}
		// 系统内外校验
		Example example = new Example(BasicPerson.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("id", basicPersonExpert.getPersonId())
				.andEqualTo("isSystem", basicPersonExpert.getIsSystem());
		if(CollectionUtils.isEmpty(basicPersonMapper.selectByExample(example))){
			throw new ServiceException("选择的专家类型与所选的人员不匹配");
		}

		if(basicPersonExpert.getPersonId() == null){
			throw new ServiceException("请选择人员");
		}

		// 用人员id进行去重过滤
		example = new Example(BasicPersonExpert.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("personId", basicPersonExpert.getPersonId());
		BasicPersonExpert old = this.mapper.selectOneByExample(example);
		if(old != null){
			basicPersonExpert.setId(old.getId());
		}

		// 传来的新增数据可能会带id， 需要去除id
		if(StringUtils.isNotBlank(basicPersonExpert.getId())){
			old = this.mapper.selectByPrimaryKey(basicPersonExpert.getId());
			if(old == null){
				basicPersonExpert.setPersonId(basicPersonExpert.getId());
				basicPersonExpert.setId(null);
				basicPersonExpert.setFlowStatus(null);
			}
		}

		if(StringUtils.isEmpty(basicPersonExpert.getId())){
			//新增
			basicPersonExpert.setId(UUIDUtils.getUUID());
			basicPersonExpert.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			basicPersonExpert.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			basicPersonExpertMapper.insertSelective(basicPersonExpert);
		}else{
			//避免页面传入修改
			basicPersonExpert.setYn(null);
			basicPersonExpertMapper.updateByPrimaryKeySelective(basicPersonExpert);
		}
		// flowCommonService.doFlowStart(FlowableConstant.FLOW_TECH_ACHIEVEMENT, basicPersonExpert, this.mapper, "开始专家申请流程");

		return basicPersonExpert.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除专家库
	 *@param id void 专家库ID
	 *@Author: wangsh
	 */
	public void deleteBasicPersonExpert(String id) {
		//TODO 做判断后方能执行删除
		BasicPersonExpert basicPersonExpert=basicPersonExpertMapper.selectByPrimaryKey(id);
		if(basicPersonExpert==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		BasicPersonExpert tembasicPersonExpert=new BasicPersonExpert();
		tembasicPersonExpert.setYn(CommonConstant.FLAG_NO);
		tembasicPersonExpert.setId(basicPersonExpert.getId());
		basicPersonExpertMapper.updateByPrimaryKeySelective(tembasicPersonExpert);
	}

	@Override
	/**
	 *@Description: 查询专家库详情
	 *@param id
	 *@return BasicPersonExpert
	 *@Author: wangsh
	 */
	public BasicPersonExpert findById(String id) {
		BasicPersonExpert basicPersonExpert = basicPersonExpertMapper.selectByPrimaryKey(id);
		BasicPersonExpertVo vo = new BasicPersonExpertVo();
		BeanUtils.copyProperties(basicPersonExpert, vo);
		/**
		 * 流程
		 */
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		/**
		 * 人员信息
		 */
		vo.setBasicPerson(basicPersonMapper.selectByPrimaryKey(vo.getPersonId()));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询专家库
	 *@param basicPersonExpertVo
	 *@return PageInfo<BasicPersonExpert>
	 *@Author: wangsh
	 */
	public PageInfo<BasicPersonExpert> findPageByQuery(BasicPersonExpertVo basicPersonExpertVo) {
		PageHelper.startPage(basicPersonExpertVo.getPageNum(),basicPersonExpertVo.getPageSize());
		Example example=new Example(BasicPersonExpert.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		//查询条件
		if(!StringUtils.isEmpty(basicPersonExpertVo.getName())){
			criteria.andLike("name", "%" + basicPersonExpertVo.getName() + "%");
		}
		if(!StringUtils.isEmpty(basicPersonExpertVo.getUnitName())){
			criteria.andLike("unitName", "%" + basicPersonExpertVo.getUnitName() + "%");
		}

		if(basicPersonExpertVo.getIsSystem() != null){
			criteria.andEqualTo("isSystem", basicPersonExpertVo.getIsSystem());
		}
		queryConditionService.querySelfDataOlny(criteria, "orgCode");
		List<BasicPersonExpert> basicPersonExpertList=basicPersonExpertMapper.selectByExample(example);
		return new PageInfo<BasicPersonExpert>(basicPersonExpertList);
	}

	/**
	 * 筛选入库
	 * @param basicPersonExpert
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public String filterBasicPersonExpert(BasicPersonExpertVo basicPersonExpert) {
		if(!CollectionUtils.isEmpty(basicPersonExpert.getBasicPersonList())){
			basicPersonExpert.getBasicPersonList().stream().forEach(basicPerson -> {
				BasicPersonExpertVo bv = new BasicPersonExpertVo();
				bv.setPersonId(basicPerson.getUserId());
				bv.setFlowStatus(FlowStatusEnum.END.getCode());
				this.saveOrUpdateBasicPersonExpert(bv);
			});
		}
		return "ok";
	}

	/**
	 * 推荐
	 * @param vo
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public String submitBasicPersonExpert(BasicPersonExpertVo vo) {
		this.saveOrUpdateBasicPersonExpert(vo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, vo, this.mapper,
				org.apache.commons.lang3.StringUtils.isNotBlank(vo.getAuditAdvice())?vo.getAuditAdvice():"提交专家推荐申请");

		return vo.getId();
	}

	/**
	 * 审核
	 * @param
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public String auditBasicPersonExpert(BasicPersonExpertVo vo) {

		String id = vo.getId();
		flowCommonService.doFlowStepAudit(vo, this.mapper
				, org.apache.commons.lang3.StringUtils.isNotBlank(vo.getAuditAdvice())?vo.getAuditAdvice():"通过专家推荐申请"
				, FlowStatusEnum.END.getCode());
		return id;
	}

	/**
	 * 退回
	 * @param
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public String sendBackBasicPersonExpert(BasicPersonExpertVo vo) {
		String id = vo.getId();
		flowCommonService.doFlowStepSendBack(vo, this.mapper
				, org.apache.commons.lang3.StringUtils.isNotBlank(vo.getAuditAdvice())?vo.getAuditAdvice():"专家推荐申请退回"
				, false
		);
		return  id;
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteMultiBasicPersonExpert(List<String> ids) {
		ids.stream().forEach(id->this.deleteBasicPersonExpert(id));
	}

	@Override
	public Object statistics(BasicPersonVo basicPersonVo) {
		return null;
	}

	@Override
	public Object export(BasicPersonVo basicPersonVo, HttpServletResponse response) {
		List list = basicPersonMapper.selectExportByVo(basicPersonVo);
		ExcelUtils.exportExcel(list, "机构人员信息", "机构人员信息", BasicPerson.class, "机构人员信息"
				, true, true, response);
		return null;
	}

	@Override
	public PageInfo<BasicPersonExpertVo> todoList(BasicPersonExpertVo vo) {
		Example example = new Example(BasicPersonExpert.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<BasicPersonExpertVo> finishedList(BasicPersonExpertVo vo) {
		Example example = new Example(BasicPersonExpert.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<BasicPersonExpert> endList(BasicPersonExpertVo vo) {
		Example example = new Example(BasicPersonExpertVo.class);
		Criteria criteria = getCriteria(vo, example);
		criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		example.orderBy("createTime").desc();
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}

	@Override
	@Transactional(readOnly = false)
	public void upload(MultipartFile file) {
		List<List<String>> fileValues = ExcelUtils.praseExcelToList(file);
		List<BasicPersonExpert> list = new ArrayList<>();
		try {
			LocalImportUtil.instanceObject(new BasicPersonExpert(), new DateFormat[0], list, fileValues);
		} catch (InstantiationException e) {
			e.printStackTrace();
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		} catch (InvocationTargetException e) {
			e.printStackTrace();
		}
		list.stream().forEach(basicPersonExpert -> {
			basicPersonExpert.setId(UUIDUtils.getUUID());
			basicPersonExpert.setFlowStatus("999");
			basicPersonExpert.setOrgCode("81b27090ee374dc9a016f6a44fe6f086");
			basicPersonExpert.setOrgName("浙江省市场监督管理局科技处");
			basicPersonExpertMapper.insertSelective(basicPersonExpert);
		});
	}

	private Criteria getCriteria(BasicPersonExpertVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getUnitName())){
			criteria.andLike("unitName", "%" + vo.getUnitName()+ "%");
		}
		return criteria;
	}
}
