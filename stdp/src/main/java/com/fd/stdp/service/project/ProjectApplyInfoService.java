package com.fd.stdp.service.project;

import java.util.List;

import com.fd.stdp.beans.project.vo.ProjectInfoStatistics;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectApplyInfo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;

import javax.servlet.http.HttpServletResponse;

/**
 * 项目基本信息Service接口
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
public interface ProjectApplyInfoService {
    /**
     * @param projectApplyInfoVo 项目基本信息对象
     * @return String 项目基本信息ID
     * @Description: 保存或更新项目基本信息
     * @Author: yujianfei
     */
    String saveOrUpdateProjectApplyInfo(ProjectApplyInfoVo projectApplyInfoVo);

    /**
     * 保存外部项目
     * @param vo
     * @return
     */
    String saveOutProjectApplyInfo(ProjectApplyInfoVo vo);

    String createProjectNumber(ProjectApplyInfoVo projectApplyInfoVo);

    /**
     * @param ids void 项目基本信息ID
     * @Description: 删除项目基本信息
     * @Author: yujianfei
     */
    void deleteProjectApplyInfo(List<String> ids);

    /**
     * @param id
     * @return ProjectApplyInfo
     * @Description: 查询项目基本信息详情
     * @Author: yujianfei
     */
    ProjectApplyInfo findById(String id);

    /**
     * @param projectApplyInfoVo
     * @return PageInfo<ProjectApplyInfo>
     * @Description: 分页查询项目基本信息
     * @Author: yujianfei
     */
    PageInfo<ProjectApplyInfo> findPageByQuery(ProjectApplyInfoVo projectApplyInfoVo);

    /**
     * 项目信息提交
     * @param projectApplyInfoVo
     * @return
     */
    String submitProjectApplyInfo(ProjectApplyInfoVo projectApplyInfoVo);

    /**
     * 项目信息审核
     * @param projectApplyInfoVo
     * @return
     */
    String auditProjectApplyInfo(ProjectApplyInfoVo projectApplyInfoVo);

    /**
     * 项目信息退回
     * @param projectApplyInfoVo
     * @return
     */
    String sendBackProjectApplyInfo(ProjectApplyInfoVo projectApplyInfoVo);

    /**
     * 分配专家
     * @param projectApplyInfoVo
     * @return
     */
//    String toExpertsProjectApplyInfo(ProjectApplyInfoVo projectApplyInfoVo);

    /**
     * 专家完成评审
     */
    void expertsAuditFinished(ProjectApplyInfoVo projectApplyInfoVo);

    /**
     * 处室评议
     * @param projectApplyInfoVo
     * @return
     */
    String reviewProjectApplyInfo(ProjectApplyInfoVo projectApplyInfoVo);

    /**
     * 项目下达
     * @param projectApplyInfoVo
     * @return
     */
    String releaseProjectApplyInfo(ProjectApplyInfoVo projectApplyInfoVo);

    /**
     * 已办
     * @param projectApplyInfoVo
     * @return
     */
    PageInfo<ProjectApplyInfo> todoList(ProjectApplyInfoVo projectApplyInfoVo);

    /**
     * 待办
     * @param projectApplyInfoVo
     * @return
     */
    PageInfo<ProjectApplyInfo> finishedList(ProjectApplyInfoVo projectApplyInfoVo);

    PageInfo<ProjectApplyInfo> endList(ProjectApplyInfoVo projectApplyInfoVo);

    Object download(String id, HttpServletResponse request);

    /***
     * 获取项目储备列表
     * @param projectApplyInfoVo
     * @return
     */
    PageInfo<ProjectApplyInfo> findReservePageByQuery(ProjectApplyInfoVo projectApplyInfoVo);

    /**
     * 获得申报单位的推荐单位
     * @return
     */
    Object getRecommandUnit();

    PageInfo statisticsProjectInfo(ProjectInfoStatistics statistics);

    /**
     * 省局批量审核
     * @param projectApplyInfoVo
     * @return
     */
    String multiProvinceAudit(ProjectApplyInfoVo projectApplyInfoVo);

    void exportPageByQuery(ProjectApplyInfoVo projectApplyInfoVo, HttpServletResponse response);

    /**
     * 处室评议导出
     * @param projectApplyInfoVo
     * @param response
     */
    void exportReviewPageByQuery(ProjectApplyInfoVo projectApplyInfoVo, HttpServletResponse response);

    /**
     * 审核操作 （流程）
     *
     * @param applyVo         审核操作 参数
     * @param currentUserName 当前用户名
     * @param currentRealName 当前用户姓名
     */
    void applyAudit(ProjectApplyInfoVo applyVo, String currentUserName, String currentRealName);

    /**
     * 项目申报代办列表查询
     *
     * @param vo
     * @return
     */
    List<ProjectApplyInfoVo> findTodoList(ProjectApplyInfoVo vo);

    /**
     * 项目负责人查询储备项目信息
     *
     * @param projectApplyInfoVo
     * @return
     */
    List<ProjectApplyInfo> findSelfReservePro(ProjectApplyInfoVo projectApplyInfoVo);

    /**
     * 项目待办信息
     *
     * @param vo
     * @return
     */
    List<ProjectApplyInfoVo> todoNew(ProjectApplyInfoVo vo);

    /**
     * 待专家评审数据
     *
     * @param applyVo
     * @return
     */
    List<ProjectApplyInfoVo> awaitingExpertReview(ProjectApplyInfoVo applyVo);

    String saveHistoryProjectApplyInfo(ProjectApplyInfoVo vo);

    /**
     * 专家评审过的项目列表
     *
     * @param vo
     * @param currentUserName
     * @param currentRealName
     * @return
     */
    List<ProjectApplyInfoVo> expertReviewedList(ProjectApplyInfoVo vo);

    /**
     * 历史申报项目查询
     *
     * @param vo
     * @return
     */
    List<ProjectApplyInfoVo> findHistoryDeclarationPro(ProjectApplyInfoVo vo);

    /**
     * 统计已提交数量
     *
     * @return
     */
    int countCommitNumber(ProjectApplyInfoVo vo);
}
