package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationProvinceCenterApply;
import com.fd.stdp.beans.innovation.vo.InnovationProvinceCenterApplyVo;
/**
 *@Description: 省质检中心
 *@Author: wangsh
 *@Date: 2022-03-11 16:42:02
 */
public interface InnovationProvinceCenterApplyService {

	/**
	 *@Description: 保存或更新省质检中心
	 *@param innovationProvinceCenterApply 省质检中心对象
	 *@return String 省质检中心ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationProvinceCenterApply(InnovationProvinceCenterApplyVo innovationProvinceCenterApply);
	
	/**
	 *@Description: 删除省质检中心
	 *@param id void 省质检中心ID
	 *@Author: wangsh
	 */
	void deleteInnovationProvinceCenterApply(String id);

	/**
	 * @Description: 批量删除省质检中心
	 * @param ids
	 */
    void deleteMultiInnovationProvinceCenterApply(List<String> ids);

	/**
	 *@Description: 查询省质检中心详情
	 *@param id
	 *@return InnovationProvinceCenterApply
	 *@Author: wangsh
	 */
	InnovationProvinceCenterApply findById(String id);

	/**
	 *@Description: 分页查询省质检中心
	 *@param innovationProvinceCenterApplyVo
	 *@return PageInfo<InnovationProvinceCenterApply>
	 *@Author: wangsh
	 */
	PageInfo<InnovationProvinceCenterApply> findPageByQuery(InnovationProvinceCenterApplyVo innovationProvinceCenterApplyVo);
	
	
	/**
	 * 提交
	 * @param innovationProvinceCenterApplyVo
	 * @return
	 */
    String submitInnovationProvinceCenterApply(InnovationProvinceCenterApplyVo innovationProvinceCenterApplyVo);

	/**
	 * 审核
	 * @param innovationProvinceCenterApplyVo
	 * @return
	 */
	String auditInnovationProvinceCenterApply(InnovationProvinceCenterApplyVo innovationProvinceCenterApplyVo);

	/**
	 * 退回
	 * @param innovationProvinceCenterApplyVo
	 * @return
	 */
	String sendBackInnovationProvinceCenterApply(InnovationProvinceCenterApplyVo innovationProvinceCenterApplyVo);

	/**
	 * 任务书下达
	 * @param innovationProvinceCenterApplyVo
	 * @return
	 */
	String releaseInnovationProvinceCenterApply(InnovationProvinceCenterApplyVo innovationProvinceCenterApplyVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnovationProvinceCenterApply> todoList(InnovationProvinceCenterApplyVo innovationProvinceCenterApplyVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationProvinceCenterApply> finishedList(InnovationProvinceCenterApplyVo innovationProvinceCenterApplyVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationProvinceCenterApply> endList(InnovationProvinceCenterApplyVo innovationProvinceCenterApplyVo);
}
