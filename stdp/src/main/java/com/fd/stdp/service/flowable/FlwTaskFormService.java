package com.fd.stdp.service.flowable;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.flowable.FlwTaskForm;
import com.fd.stdp.beans.flowable.vo.FlwTaskFormVo;
/**
 *@Description: 流程任务关联表单
 *@Author: linqiang
 *@Date: 2021-10-15 09:22:00
 */
public interface FlwTaskFormService {

	/**
	 *@Description: 保存或更新流程任务关联表单
	 *@param flwTaskForm 流程任务关联表单对象
	 *@return String 流程任务关联表单ID
	 *@Author: linqiang
	 */
	String saveOrUpdateFlwTaskForm(FlwTaskForm flwTaskForm);
	
	/**
	 *@Description: 删除流程任务关联表单
	 *@param id void 流程任务关联表单ID
	 *@Author: linqiang
	 */
	void deleteFlwTaskForm(String id);

	/**
	 *@Description: 查询流程任务关联表单详情
	 *@param id
	 *@return FlwTaskForm
	 *@Author: linqiang
	 */
	FlwTaskForm findById(String id);

	/**
	 *@Description: 分页查询流程任务关联表单
	 *@param flwTaskFormVo
	 *@return PageInfo<FlwTaskForm>
	 *@Author: linqiang
	 */
	PageInfo<FlwTaskForm> findPageByQuery(FlwTaskFormVo flwTaskFormVo);
	
	 /**
     * 查询流程任务关联单列表
     *
     * @param flwTaskForm 流程任务关联单
     * @return 流程任务关联单集合
     */
    public List<FlwTaskForm> findFlwTaskFormList(FlwTaskForm flwTaskForm);
	
	/**
     * 批量删除流程任务关联单
     *
     * @param ids 需要删除的流程任务关联单ID
     * @return 结果
     */
    public int deleteFlwTaskFormByIds(String[] ids);
}
