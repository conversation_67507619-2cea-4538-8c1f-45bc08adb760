package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractRelevantUnits;
import com.fd.stdp.beans.project.vo.ProjectContractRelevantUnitsVo;
/**
 *@Description: 参与单位
 *@Author: wangsh
 *@Date: 2022-01-12 14:18:16
 */
public interface ProjectContractRelevantUnitsService {

	/**
	 *@Description: 保存或更新参与单位
	 *@param projectContractRelevantUnits 参与单位对象
	 *@return String 参与单位ID
	 *@Author: wangsh
	 */
	String saveOrUpdateProjectContractRelevantUnits(ProjectContractRelevantUnits projectContractRelevantUnits);
	
	/**
	 *@Description: 删除参与单位
	 *@param id void 参与单位ID
	 *@Author: wangsh
	 */
	void deleteProjectContractRelevantUnits(String id);

	/**
	 * @Description: 批量删除参与单位
	 * @param projectContractRelevantUnitsVo
	 */
    void deleteMultiProjectContractRelevantUnits(ProjectContractRelevantUnitsVo projectContractRelevantUnitsVo);

	/**
	 *@Description: 查询参与单位详情
	 *@param id
	 *@return ProjectContractRelevantUnits
	 *@Author: wangsh
	 */
	ProjectContractRelevantUnits findById(String id);

	/**
	 *@Description: 分页查询参与单位
	 *@param projectContractRelevantUnitsVo
	 *@return PageInfo<ProjectContractRelevantUnits>
	 *@Author: wangsh
	 */
	PageInfo<ProjectContractRelevantUnits> findPageByQuery(ProjectContractRelevantUnitsVo projectContractRelevantUnitsVo);
}
