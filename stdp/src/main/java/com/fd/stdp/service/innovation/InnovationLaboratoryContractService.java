package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationLaboratoryContract;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryContractVo;
/**
 *@Description: 重点实验室培育申报任务书
 *@Author: wangsh
 *@Date: 2022-02-09 16:46:10
 */
public interface InnovationLaboratoryContractService {

	/**
	 *@Description: 保存或更新重点实验室培育申报任务书
	 *@param innovationLaboratoryContract 重点实验室培育申报任务书对象
	 *@return String 重点实验室培育申报任务书ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationLaboratoryContract(InnovationLaboratoryContractVo innovationLaboratoryContract);
	
	/**
	 *@Description: 删除重点实验室培育申报任务书
	 *@param id void 重点实验室培育申报任务书ID
	 *@Author: wangsh
	 */
	void deleteInnovationLaboratoryContract(String id);

	/**
	 * @Description: 批量删除重点实验室培育申报任务书
	 * @param ids
	 */
    void deleteMultiInnovationLaboratoryContract(List<String> ids);

	/**
	 *@Description: 查询重点实验室培育申报任务书详情
	 *@param id
	 *@return InnovationLaboratoryContract
	 *@Author: wangsh
	 */
	InnovationLaboratoryContract findById(String id);

	/**
	 *@Description: 分页查询重点实验室培育申报任务书
	 *@param innovationLaboratoryContractVo
	 *@return PageInfo<InnovationLaboratoryContract>
	 *@Author: wangsh
	 */
	PageInfo<InnovationLaboratoryContract> findPageByQuery(InnovationLaboratoryContractVo innovationLaboratoryContractVo);

	/**
	 * 提交
	 * @param innovationLaboratoryContract
	 * @return
	 */
    String submitInnovationLaboratoryContract(InnovationLaboratoryContractVo innovationLaboratoryContract);

	/**
	 * 审核
	 * @param innovationLaboratoryContract
	 * @return
	 */
	String auditInnovationLaboratoryContract(InnovationLaboratoryContractVo innovationLaboratoryContract);

	/**
	 * 退回
	 * @param innovationLaboratoryContract
	 * @return
	 */
	String sendBackInnovationLaboratoryContract(InnovationLaboratoryContractVo innovationLaboratoryContract);

	/**
	 * 待办列表
	 */
	PageInfo<InnovationLaboratoryContract> todoList(InnovationLaboratoryContractVo innovationLaboratoryContractVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationLaboratoryContract> finishedList(InnovationLaboratoryContractVo innovationLaboratoryContractVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationLaboratoryContract> endList(InnovationLaboratoryContractVo innovationLaboratoryContractVo);
}
