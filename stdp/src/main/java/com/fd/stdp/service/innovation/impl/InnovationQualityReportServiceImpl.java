package com.fd.stdp.service.innovation.impl;

import java.util.List;

import com.fd.stdp.beans.innovation.InnovationQualityInterim;
import com.fd.stdp.beans.innovation.vo.InnovationQualityInterimVo;
import com.fd.stdp.dao.innovation.InnovationQualityContractMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityReport;
import com.fd.stdp.beans.innovation.vo.InnovationQualityReportVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationQualityReportMapper;
import com.fd.stdp.service.innovation.InnovationQualityReportService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 省质检中心事项报告
 *@Author: wangsh
 *@Date: 2022-03-04 09:23:48
 */
public class InnovationQualityReportServiceImpl extends BaseServiceImpl<InnovationQualityReportMapper, InnovationQualityReport> implements InnovationQualityReportService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationQualityReportServiceImpl.class);
	
	@Autowired
	private InnovationQualityReportMapper innovationQualityReportMapper;
	@Autowired
	private InnovationQualityContractMapper innovationQualityContractMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新省质检中心事项报告
	 *@param innovationQualityReport 省质检中心事项报告对象
	 *@return String 省质检中心事项报告ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationQualityReport(InnovationQualityReportVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if (vo.getContract() != null){
			vo.setApplyUnitName(vo.getContract().getApplyUnitName());
			vo.setApplyCenterName(vo.getContract().getApplyCenterName());
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationQualityReportMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationQualityReportMapper.updateByPrimaryKeySelective(vo);
		}
		/**
		 * 附件
		 */
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除省质检中心事项报告
	 *@param id void 省质检中心事项报告ID
	 *@Author: wangsh
	 */
	public void deleteInnovationQualityReport(String id) {
		//TODO 做判断后方能执行删除
		InnovationQualityReport innovationQualityReport=innovationQualityReportMapper.selectByPrimaryKey(id);
		if(innovationQualityReport==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationQualityReport teminnovationQualityReport=new InnovationQualityReport();
		teminnovationQualityReport.setYn(CommonConstant.FLAG_NO);
		teminnovationQualityReport.setId(innovationQualityReport.getId());
		innovationQualityReportMapper.updateByPrimaryKeySelective(teminnovationQualityReport);
	}

    /**
     * @Description: 批量删除省质检中心事项报告
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationQualityReport(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationQualityReport(id));
	}

	@Override
	/**
	 *@Description: 查询省质检中心事项报告详情
	 *@param id
	 *@return InnovationQualityReport
	 *@Author: wangsh
	 */
	public InnovationQualityReport findById(String id) {
		InnovationQualityReport innovationQualityReport = innovationQualityReportMapper.selectByPrimaryKey(id);
		InnovationQualityReportVo vo = new InnovationQualityReportVo();
		BeanUtils.copyProperties(innovationQualityReport, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		vo.setContract(innovationQualityContractMapper.selectByPrimaryKey(vo.getContractId()));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询省质检中心事项报告
	 *@param innovationQualityReportVo
	 *@return PageInfo<InnovationQualityReport>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationQualityReport> findPageByQuery(InnovationQualityReportVo innovationQualityReportVo) {
		PageHelper.startPage(innovationQualityReportVo.getPageNum(),innovationQualityReportVo.getPageSize());
		Example example=new Example(InnovationQualityReport.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationQualityReportVo.getName())){
		//	criteria.andEqualTo(innovationQualityReportVo.getName());
		//}
		List<InnovationQualityReport> innovationQualityReportList=innovationQualityReportMapper.selectByExample(example);
		return new PageInfo<InnovationQualityReport>(innovationQualityReportList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationQualityReport(InnovationQualityReportVo innovationQualityReportVo) {
		String id = this.saveOrUpdateInnovationQualityReport(innovationQualityReportVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innovationQualityReportVo, this.mapper,
				StringUtils.isNotBlank(innovationQualityReportVo.getAuditAdvice())?innovationQualityReportVo.getAuditAdvice():"提交省质检中心事项报告");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationQualityReport(InnovationQualityReportVo innovationQualityReportVo) {
		flowCommonService.doFlowStepAudit(innovationQualityReportVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityReportVo.getAuditAdvice()) ? innovationQualityReportVo.getAuditAdvice() : "省质检中心事项报告审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationQualityReportVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationQualityReport(InnovationQualityReportVo innovationQualityReportVo) {
		flowCommonService.doFlowStepSendBack(innovationQualityReportVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityReportVo.getAuditAdvice())?innovationQualityReportVo.getAuditAdvice():"省质检中心事项报告退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationQualityReport(InnovationQualityReportVo innovationQualityReportVo) {
		flowCommonService.doCompleteTask(innovationQualityReportVo, this.mapper
				, "省质检中心事项报告任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnovationQualityReport> todoList(InnovationQualityReportVo vo) {

		Example example = new Example(InnovationQualityReport.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationQualityReport> finishedList(InnovationQualityReportVo vo) {
		Example example = new Example(InnovationQualityReport.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnovationQualityReport> endList(InnovationQualityReportVo vo) {
        Example example = new Example(InnovationQualityReport.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(InnovationQualityReport vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getApplyCenterName())){
			criteria.andLike("applyCenterName", "%" + vo.getApplyCenterName()+ "%");
		}
		return criteria;
	}
}
