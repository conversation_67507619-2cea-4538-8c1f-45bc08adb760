package com.fd.stdp.service.work;

import java.util.List;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.work.WorkReport;
import com.fd.stdp.beans.work.vo.WorkReportVo;
/**
 * 工作交流Service接口
 * 
 * <AUTHOR>
 * @date 2021-11-04
 */
public interface WorkReportService{
	/**
	 *@Description: 保存或更新工作交流
	 *@param workReportVo 工作交流对象
	 *@return String 工作交流ID
	 *@Author: yujianfei
	 */
	String saveOrUpdateWorkReport(WorkReportVo workReportVo);
	
	/**
	 *@Description: 删除工作交流
	 *@param ids void 工作交流ID
	 *@Author: yujianfei
	 */
	void deleteWorkReport(List<String> ids);

	/**
	 *@Description: 查询工作交流详情
	 *@param id
	 *@return WorkReport
	 *@Author: yujianfei
	 */
	WorkReportVo findById(String id);

	/**
	 *@Description: 分页查询工作交流
	 *@param workReportVo
	 *@return PageInfo<WorkReport>
	 *@Author: yujianfei
	 */
	PageInfo<WorkReport> findPageByQuery(WorkReportVo workReportVo);

    String submitWorkReport(WorkReportVo workReportVo);

	String auditWorkReport(WorkReportVo workReportVo);

	String sendBackWorkReport(WorkReportVo workReportVo);

	PageInfo<WorkReport> todoList(WorkReportVo workReportVo);

	PageInfo<WorkReport> finishedList(WorkReportVo workReportVo);

	PageInfo<WorkReport> endList(WorkReportVo workReportVo);
}
