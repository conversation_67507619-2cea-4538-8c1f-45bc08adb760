package com.fd.stdp.service.audit;

import java.util.List;

import com.fd.stdp.beans.audit.InnerAuditOpen;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.audit.InnerAuditFirst;
import com.fd.stdp.beans.audit.vo.InnerAuditFirstVo;

import javax.servlet.http.HttpServletResponse;

/**
 *@Description: 内审统计表1
 *@Author: wangsh
 *@Date: 2022-02-22 16:49:05
 */
public interface InnerAuditFirstService {

	/**
	 *@Description: 保存或更新内审统计表1
	 *@param innerAuditFirst 内审统计表1对象
	 *@return String 内审统计表1ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnerAuditFirst(InnerAuditFirstVo innerAuditFirst);
	
	/**
	 *@Description: 删除内审统计表1
	 *@param id void 内审统计表1ID
	 *@Author: wangsh
	 */
	void deleteInnerAuditFirst(String id);

	/**
	 * @Description: 批量删除内审统计表1
	 * @param ids
	 */
    void deleteMultiInnerAuditFirst(List<String> ids);

	/**
	 *@Description: 查询内审统计表1详情
	 *@param id
	 *@return InnerAuditFirst
	 *@Author: wangsh
	 */
	InnerAuditFirst findById(String id);

	/**
	 *@Description: 分页查询内审统计表1
	 *@param innerAuditFirstVo
	 *@return PageInfo<InnerAuditFirst>
	 *@Author: wangsh
	 */
	PageInfo<InnerAuditFirst> findPageByQuery(InnerAuditFirstVo innerAuditFirstVo);
	
	
	/**
	 * 提交
	 * @param innerAuditFirstVo
	 * @return
	 */
    String submitInnerAuditFirst(InnerAuditFirstVo innerAuditFirstVo);

	/**
	 * 审核
	 * @param innerAuditFirstVo
	 * @return
	 */
	String auditInnerAuditFirst(InnerAuditFirstVo innerAuditFirstVo);

	/**
	 * 退回
	 * @param innerAuditFirstVo
	 * @return
	 */
	String sendBackInnerAuditFirst(InnerAuditFirstVo innerAuditFirstVo);

	/**
	 * 任务书下达
	 * @param innerAuditFirstVo
	 * @return
	 */
	String releaseInnerAuditFirst(InnerAuditFirstVo innerAuditFirstVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnerAuditFirst> todoList(InnerAuditFirstVo innerAuditFirstVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnerAuditFirst> finishedList(InnerAuditFirstVo innerAuditFirstVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnerAuditFirst> endList(InnerAuditFirstVo innerAuditFirstVo);

	/**
	 * 当前登录用户是否有填报功能
	 * @param s
	 * @return
	 */
	InnerAuditOpen isAuditOpen(String s);

	Object statistics(InnerAuditFirstVo innerAuditFirstVo);

	void export(InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response);

	List statistics11(InnerAuditFirstVo innerAuditFirstVo);

	List statistics12(InnerAuditFirstVo innerAuditFirstVo);

	List statistics21(InnerAuditFirstVo innerAuditFirstVo);

	List statistics22(InnerAuditFirstVo innerAuditFirstVo);

	void export11(InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response);

	void export12(InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response);

	void export21(InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response);

	void export22(InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response);

    InnerAuditFirst findOldVal();
}
