package com.fd.stdp.service.basic;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonExpertRecommend;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertRecommendVo;

import javax.servlet.http.HttpServletResponse;

/**
 *@Description: 专家推荐表
 *@Author: wangsh
 *@Date: 2022-06-23 15:48:02
 */
public interface BasicPersonExpertRecommendService {

	/**
	 *@Description: 保存或更新专家推荐表
	 *@param basicPersonExpertRecommend 专家推荐表对象
	 *@return String 专家推荐表ID
	 *@Author: wangsh
	 */
	String saveOrUpdateBasicPersonExpertRecommend(BasicPersonExpertRecommendVo basicPersonExpertRecommend);
	
	/**
	 *@Description: 删除专家推荐表
	 *@param id void 专家推荐表ID
	 *@Author: wangsh
	 */
	void deleteBasicPersonExpertRecommend(String id);

	/**
	 * @Description: 批量删除专家推荐表
	 * @param ids
	 */
    void deleteMultiBasicPersonExpertRecommend(List<String> ids);

	/**
	 *@Description: 查询专家推荐表详情
	 *@param id
	 *@return BasicPersonExpertRecommend
	 *@Author: wangsh
	 */
	BasicPersonExpertRecommend findById(String id);

	/**
	 *@Description: 分页查询专家推荐表
	 *@param basicPersonExpertRecommendVo
	 *@return PageInfo<BasicPersonExpertRecommend>
	 *@Author: wangsh
	 */
	PageInfo<BasicPersonExpertRecommend> findPageByQuery(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo);
	
	
	/**
	 * 提交
	 * @param basicPersonExpertRecommendVo
	 * @return
	 */
    String submitBasicPersonExpertRecommend(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo);

	/**
	 * 审核
	 * @param basicPersonExpertRecommendVo
	 * @return
	 */
	String auditBasicPersonExpertRecommend(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo);

	/**
	 * 退回
	 * @param basicPersonExpertRecommendVo
	 * @return
	 */
	String sendBackBasicPersonExpertRecommend(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo);

	/**
	 * 任务书下达
	 * @param basicPersonExpertRecommendVo
	 * @return
	 */
	String releaseBasicPersonExpertRecommend(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<BasicPersonExpertRecommend> todoList(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo);

	/**
	 * 已办列表
	 */
	PageInfo<BasicPersonExpertRecommend> finishedList(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo);

	/**
	 * 已完成列表
	 */
	PageInfo<BasicPersonExpertRecommend> endList(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo);

	/**
	 * 导出单条专家推荐表单
	 * @param basicPersonExpertRecommendVo
	 * @param response
	 */
	void exportOne(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo, HttpServletResponse response);

	/**
	 * 导出专家推荐列表
	 * @param basicPersonExpertRecommendVo
	 * @param response
	 */
	void exportPageByQuery(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo, HttpServletResponse response);

}
