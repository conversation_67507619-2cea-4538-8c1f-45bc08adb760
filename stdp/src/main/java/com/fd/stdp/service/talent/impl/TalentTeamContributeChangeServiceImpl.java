package com.fd.stdp.service.talent.impl;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import com.fd.stdp.beans.basic.vo.BasicPersonLinkedVo;
import com.fd.stdp.beans.talent.TalentTeamContribute;
import com.fd.stdp.beans.talent.TalentTeamContributePlan;
import com.fd.stdp.beans.talent.TalentTeamContributeTarget;
import com.fd.stdp.beans.talent.vo.TalentTeamContributeVo;
import com.fd.stdp.common.BaseEntity;
import com.fd.stdp.dao.talent.TalentTeamContributeMapper;
import com.fd.stdp.dao.talent.TalentTeamContributePlanMapper;
import com.fd.stdp.dao.talent.TalentTeamContributeTargetMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.basic.BasicGradeLinkedService;
import com.fd.stdp.service.basic.BasicPersonLinkedService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamContributeChange;
import com.fd.stdp.beans.talent.vo.TalentTeamContributeChangeVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.talent.TalentTeamContributeChangeMapper;
import com.fd.stdp.service.talent.TalentTeamContributeChangeService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 创新团队建设任务书变更
 *@Author: wangsh
 *@Date: 2022-02-14 19:56:46
 */
public class TalentTeamContributeChangeServiceImpl extends BaseServiceImpl<TalentTeamContributeChangeMapper, TalentTeamContributeChange> implements TalentTeamContributeChangeService{

	public static final Logger logger = LoggerFactory.getLogger(TalentTeamContributeChangeServiceImpl.class);
	private static final String NUMBER = "NUMBER";
	private static final String EXPERT = "EXPERT";
	
	@Autowired
	private TalentTeamContributeChangeMapper talentTeamContributeChangeMapper;
	@Autowired
	private TalentTeamContributeMapper talentTeamContributeMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicPersonLinkedService basicPersonLinkedService;

	@Autowired
	private TalentTeamContributePlanMapper talentTeamContributePlanMapper;
	@Autowired
	private TalentTeamContributeTargetMapper talentTeamContributeTargetMapper;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	@Autowired
	private BasicGradeLinkedService basicGradeLinkedService;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新创新团队建设任务书变更
	 *@param talentTeamContributeChange 创新团队建设任务书变更对象
	 *@return String 创新团队建设任务书变更ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTalentTeamContributeChange(TalentTeamContributeChangeVo vo) {
		saveOrUpdateTalentTeamContributeChangeNoFlow(vo);
		// 开启流程
		// flowCommonService.doFlowStart(FlowableConstant.FLOW_TALENT_TEAM_CONTRACT_CHANGE, vo, this.mapper, "开始创新团队任务书变更流程");

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String saveOrUpdateTalentTeamContributeChangeNoFlow(TalentTeamContributeChangeVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			talentTeamContributeChangeMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			talentTeamContributeChangeMapper.updateByPrimaryKeySelective(vo);
		}

		// 团队成员
		List<BasicPersonLinkedVo> list = new ArrayList();
		if(!CollectionUtils.isEmpty(vo.getTeamNumbers())){
			vo.getTeamNumbers().stream().forEach(p->p.setPersonType(NUMBER));
			basicPersonLinkedService.clearAndUpdateBasicPersonLinked(vo.getId(), vo.getTeamNumbers(), NUMBER);
		}
		// 合作专家
		if(!CollectionUtils.isEmpty(vo.getTeamExperts())){
			vo.getTeamExperts().stream().forEach(p->p.setPersonType(EXPERT));
			basicPersonLinkedService.clearAndUpdateBasicPersonLinked(vo.getId(), vo.getTeamExperts(), EXPERT);
		}

		/**
		 * 计划分工
		 */
		if(!CollectionUtils.isEmpty(vo.getContractPlans())){
			updateList(vo, vo.getContractPlans(), talentTeamContributePlanMapper,"setContractId");
		}
		/**
		 * 考核指标
		 */
		if(!CollectionUtils.isEmpty(vo.getContractTargets())){
			updateList(vo, vo.getContractTargets(), talentTeamContributeTargetMapper,"setContractId");
		}
		/**
		 * 附件
		 */
		if(!CollectionUtils.isEmpty(vo.getFiles())) {
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除创新团队建设任务书变更
	 *@param id void 创新团队建设任务书变更ID
	 *@Author: wangsh
	 */
	public void deleteTalentTeamContributeChange(String id) {
		//TODO 做判断后方能执行删除
		TalentTeamContributeChange talentTeamContributeChange=talentTeamContributeChangeMapper.selectByPrimaryKey(id);
		if(talentTeamContributeChange==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TalentTeamContributeChange temtalentTeamContributeChange=new TalentTeamContributeChange();
		temtalentTeamContributeChange.setYn(CommonConstant.FLAG_NO);
		temtalentTeamContributeChange.setId(talentTeamContributeChange.getId());
		talentTeamContributeChangeMapper.updateByPrimaryKeySelective(temtalentTeamContributeChange);
	}

    /**
     * @Description: 批量删除创新团队建设任务书变更
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTalentTeamContributeChange(List<String> ids) {
		ids.stream().forEach(id-> this.deleteTalentTeamContributeChange(id));
	}

	@Override
	/**
	 *@Description: 查询创新团队建设任务书变更详情
	 *@param id
	 *@return TalentTeamContributeChange
	 *@Author: wangsh
	 */
	public TalentTeamContributeChange findById(String id) {
		TalentTeamContributeChange talentTeamContributeChange = talentTeamContributeChangeMapper.selectByPrimaryKey(id);
		TalentTeamContributeChangeVo vo = new TalentTeamContributeChangeVo();
		BeanUtils.copyProperties(talentTeamContributeChange, vo);

		Example example = new Example(TalentTeamContributePlan.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("contractId", id);
		vo.setContractPlans(talentTeamContributePlanMapper.selectByExample(example));

		example = new Example(TalentTeamContributeTarget.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("contractId", id);
		vo.setContractTargets(talentTeamContributeTargetMapper.selectByExample(example));

		List<BasicPersonLinkedVo> bv = basicPersonLinkedService.findByFormId(id);
		vo.setTeamExperts(bv.stream().filter(b->EXPERT.equals(b.getPersonType())).collect(Collectors.toList()));
		vo.setTeamNumbers(bv.stream().filter(b->NUMBER.equals(b.getPersonType())).collect(Collectors.toList()));

		vo.setFiles(basicFileAppendixService.findByFormId(id));

		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询创新团队建设任务书变更
	 *@param talentTeamContributeChangeVo
	 *@return PageInfo<TalentTeamContributeChange>
	 *@Author: wangsh
	 */
	public PageInfo<TalentTeamContributeChange> findPageByQuery(TalentTeamContributeChangeVo talentTeamContributeChangeVo) {
		PageHelper.startPage(talentTeamContributeChangeVo.getPageNum(),talentTeamContributeChangeVo.getPageSize());
		Example example=new Example(TalentTeamContributeChange.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(talentTeamContributeChangeVo.getName())){
		//	criteria.andEqualTo(talentTeamContributeChangeVo.getName());
		//}
		List<TalentTeamContributeChange> talentTeamContributeChangeList=talentTeamContributeChangeMapper.selectByExample(example);
		return new PageInfo<TalentTeamContributeChange>(talentTeamContributeChangeList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitTalentTeamContributeChange(TalentTeamContributeChangeVo talentTeamContributeChangeVo) {
		String id = this.saveOrUpdateTalentTeamContributeChange(talentTeamContributeChangeVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TALENT_TEAM_CONTRACT_CHANGE, talentTeamContributeChangeVo, this.mapper,
				StringUtils.isNotBlank(talentTeamContributeChangeVo.getAuditAdvice())?talentTeamContributeChangeVo.getAuditAdvice():"提交创新团队建设任务书变更");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditTalentTeamContributeChange(TalentTeamContributeChangeVo talentTeamContributeChangeVo) {
		if(StringUtils.equals(talentTeamContributeChangeVo.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())
				&& CollectionUtils.isEmpty(talentTeamContributeChangeVo.getGradeExperts())){
			throw new ServiceException("评审专家不得为空");
		}
		// 省局审核会选择专家
		if(!CollectionUtils.isEmpty(talentTeamContributeChangeVo.getGradeExperts())){
			List<BasicGradeLinkedVo> basicGradeLinkedVoList = new ArrayList<>();
			talentTeamContributeChangeVo.getGradeExperts().stream().forEach(basicPersonLinkedVo -> {
				BasicGradeLinkedVo b = new BasicGradeLinkedVo();
				b.setUserId(basicPersonLinkedVo.getPersonId());
				basicGradeLinkedVoList.add(b);
			});
			basicGradeLinkedService.clearAndUpdateBasicGradeLinked(talentTeamContributeChangeVo.getId(), basicGradeLinkedVoList);
		}
		flowCommonService.doFlowStepAudit(talentTeamContributeChangeVo, this.mapper
				, StringUtils.isNotBlank(talentTeamContributeChangeVo.getAuditAdvice()) ? talentTeamContributeChangeVo.getAuditAdvice() : "创新团队建设任务书变更审核通过"
				, FlowStatusEnum.EXPERTS_GRADE.getCode(), AssigneeConstant.EXPERT_ROLE);
		return talentTeamContributeChangeVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackTalentTeamContributeChange(TalentTeamContributeChangeVo talentTeamContributeChangeVo) {
		flowCommonService.doFlowStepSendBack(talentTeamContributeChangeVo, this.mapper
				, StringUtils.isNotBlank(talentTeamContributeChangeVo.getAuditAdvice())?talentTeamContributeChangeVo.getAuditAdvice():"创新团队建设任务书变更退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseTalentTeamContributeChange(TalentTeamContributeChangeVo talentTeamContributeChangeVo) {
		flowCommonService.doCompleteTask(talentTeamContributeChangeVo, this.mapper
				, "创新团队建设任务书变更任务书完成"
				, FlowStatusEnum.PROJECT_RELEASE.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<TalentTeamContributeChange> todoList(TalentTeamContributeChangeVo vo) {

		Example example = new Example(TalentTeamContributeChange.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TalentTeamContributeChange> finishedList(TalentTeamContributeChangeVo vo) {
		Example example = new Example(TalentTeamContributeChange.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<TalentTeamContributeChange> endList(TalentTeamContributeChangeVo vo) {
        Example example = new Example(TalentTeamContributeChange.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(TalentTeamContributeChangeVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("teamLeader", "%" + vo.getName()+ "%");
		}
		return criteria;
	}
}
