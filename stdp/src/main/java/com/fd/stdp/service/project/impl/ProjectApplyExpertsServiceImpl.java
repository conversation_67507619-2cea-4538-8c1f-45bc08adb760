package com.fd.stdp.service.project.impl;

import java.util.List;

import com.fd.stdp.beans.project.ProjectApplyInfo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.project.ProjectApplyInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectApplyExperts;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertsVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectApplyExpertsMapper;
import com.fd.stdp.service.project.ProjectApplyExpertsService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.fd.stdp.common.BaseController.getCurrentUserId;

/**
 * <AUTHOR>
 * @Description: 项目专家信息Service业务层处理
 * @date 2021-11-26
 */
@Service
@Transactional(readOnly = true)
public class ProjectApplyExpertsServiceImpl extends BaseServiceImpl<ProjectApplyExpertsMapper, ProjectApplyExperts> implements ProjectApplyExpertsService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectApplyExpertsServiceImpl.class);
    @Autowired
    private ProjectApplyExpertsMapper projectApplyExpertsMapper;

    @Autowired
    private FlowCommonService flowCommonService;

    @Autowired
    private ProjectApplyInfoService projectApplyInfoService;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新项目专家信息
     *@param projectApplyExperts 项目专家信息对象
     *@return String 项目专家信息ID
     *@Author: yujianfei
     */
    public String saveOrUpdateProjectApplyExperts(ProjectApplyExperts projectApplyExperts) {
        if (projectApplyExperts == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(projectApplyExperts.getId())) {
            //新增
            projectApplyExperts.setId(UUIDUtils.getUUID());
            projectApplyExpertsMapper.insertSelective(projectApplyExperts);
        } else {
            //避免页面传入修改
            projectApplyExperts.setYn(null);
            projectApplyExpertsMapper.updateByPrimaryKeySelective(projectApplyExperts);
        }
        return projectApplyExperts.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除项目专家信息
     *@param id void 项目专家信息ID
     *@Author: yujianfei
     */
    public void deleteProjectApplyExperts(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            ProjectApplyExperts projectApplyExperts = projectApplyExpertsMapper.selectByPrimaryKey(id);
            if (projectApplyExperts == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            ProjectApplyExperts temprojectApplyExperts = new ProjectApplyExperts();
            temprojectApplyExperts.setYn(CommonConstant.FLAG_NO);
            temprojectApplyExperts.setId(projectApplyExperts.getId());
            projectApplyExpertsMapper.updateByPrimaryKeySelective(temprojectApplyExperts);
        }
    }

    /**
     * @param id
     * @return ProjectApplyExperts
     * @Description: 查询项目专家信息详情
     * @Author: yujianfei
     */
    @Override
    public ProjectApplyExperts findById(String id) {
        return projectApplyExpertsMapper.selectByPrimaryKey(id);
    }


    /**
     * @param projectApplyExpertsVo
     * @return PageInfo<ProjectApplyExperts>
     * @Description: 分页查询项目专家信息
     * @Author: yujianfei
     */
    @Override
    public PageInfo<ProjectApplyExperts> findPageByQuery(ProjectApplyExpertsVo projectApplyExpertsVo) {
        PageHelper.startPage(projectApplyExpertsVo.getPageNum(), projectApplyExpertsVo.getPageSize());
        Example example = new Example(ProjectApplyExperts.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(projectApplyExpertsVo.getName())){
        //	criteria.andEqualTo(projectApplyExpertsVo.getName());
        //}
        List<ProjectApplyExperts> projectApplyExpertsList = projectApplyExpertsMapper.selectByExample(example);
        return new PageInfo<ProjectApplyExperts>(projectApplyExpertsList);
    }

    @Override
    @Transactional(readOnly = false)
    public String submitProjectApplyExperts(ProjectApplyExpertsVo projectApplyExpertsVo) {
        String applyId = projectApplyExpertsVo.getId();
        // 找到并更新打分记录
        Example example = new Example(ProjectApplyExperts.class);
        example.createCriteria().andEqualTo("yn", 1).andEqualTo("applyId", applyId)
                        .andEqualTo("userId", getCurrentUserId());
        ProjectApplyExperts projectApplyExperts = this.mapper.selectOneByExample(example);

        projectApplyExpertsVo.setId(projectApplyExperts.getId());
//        projectApplyExpertsVo.setIsSubmit(1);
        this.saveOrUpdateProjectApplyExperts(projectApplyExpertsVo);

        // 查询是否全部完成打分
        example = new Example(ProjectApplyExperts.class);
        example.createCriteria().andEqualTo("yn", 1)
                .andEqualTo("applyId", applyId)
                .andEqualTo("isSubmit", 0);
        List list = this.mapper.selectByExample(example);
        if(CollectionUtils.isEmpty(list)){
            // 全完成 则进行下一个流程
            ProjectApplyInfo projectApplyInfo = projectApplyInfoService.findById(applyId);
            ProjectApplyInfoVo projectApplyInfoVo = new ProjectApplyInfoVo();

            BeanUtils.copyProperties(projectApplyInfo, projectApplyInfoVo);
            projectApplyInfoService.expertsAuditFinished(projectApplyInfoVo);
        }

        return projectApplyExpertsVo.getId();
    }

    @Override
    public void submitProjectApplyExpertsMulti(List<String> ids, String currentUserId) {
        ids.forEach(id->{
            /**
             * 专家评审结果
             */
            Example example = new Example(ProjectApplyExperts.class);
            example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
                    .andEqualTo("applyId", id).andEqualTo("userId", currentUserId);
            example.orderBy("createTime").desc();
            List<ProjectApplyExperts> projectApplyExperts = projectApplyExpertsMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(projectApplyExperts)) {
                throw new ServiceException("未找到评审记录");
            }
            projectApplyExperts.forEach(projectApplyExpert->{
                if(projectApplyExpert.getNecessity() == null || projectApplyExpert.getFeasibility() == null
                        || projectApplyExpert.getResearchResults() == null || projectApplyExpert.getFundding() == null
                        || projectApplyExpert.getResearchPeriod() == null || projectApplyExpert.getAdvice() == null){
                    throw new ServiceException("未完成评审的记录无法提交");
                }
                projectApplyExpert.setIsSubmit(1);
                projectApplyExpertsMapper.updateByPrimaryKeySelective(projectApplyExpert);
            });
        });
    }
}
