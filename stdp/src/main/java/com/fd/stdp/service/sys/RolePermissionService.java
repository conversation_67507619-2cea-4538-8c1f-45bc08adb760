package com.fd.stdp.service.sys;

import java.util.List;

import com.fd.stdp.beans.sys.SysRolePermission;
import com.fd.stdp.beans.sys.vo.RolePermissionVO;
import com.fd.stdp.beans.sys.vo.RoleTypePermissionVO;
import com.fd.stdp.common.BaseService;

/**
 * 角色service
 * 
 * <AUTHOR>
 * @date 2018/03/1
 */
public interface RolePermissionService extends BaseService<SysRolePermission> {

	/**
	 * 给角色授权
	 * 
	 * @param roleCode
	 * @param permissions
	 */
	void savePermissions(String roleId, String menuId, List<String> permissions, String type);

	/**
	 * 查询角色权限
	 * 
	 * @param roleId
	 * @return
	 */
	List<SysRolePermission> listPermissions(String roleId);

	/**
	 * 取出用户的角色类型和
	 * 
	 * @param userId
	 * @return
	 */
	List<RoleTypePermissionVO> getUserAllPermission(String userId);

	/**
	 * 取出用户的角色类型和
	 * 
	 * @param userId
	 * @return
	 */
	public List<RolePermissionVO> getUserRoleAllPermission(String userId);
}
