package com.fd.stdp.service.reward;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.reward.RewordCooperUnit;
import com.fd.stdp.beans.reward.vo.RewordCooperUnitVo;
/**
 *@Description: 科技成果关联合作单位
 *@Author: wangsh
 *@Date: 2022-07-05 16:58:57
 */
public interface RewordCooperUnitService {

	/**
	 *@Description: 保存或更新科技成果关联合作单位
	 *@param rewordCooperUnit 科技成果关联合作单位对象
	 *@return String 科技成果关联合作单位ID
	 *@Author: wangsh
	 */
	String saveOrUpdateRewordCooperUnit(RewordCooperUnitVo rewordCooperUnit);
	
	/**
	 *@Description: 删除科技成果关联合作单位
	 *@param id void 科技成果关联合作单位ID
	 *@Author: wangsh
	 */
	void deleteRewordCooperUnit(String id);

	/**
	 * @Description: 批量删除科技成果关联合作单位
	 * @param ids
	 */
    void deleteMultiRewordCooperUnit(List<String> ids);

	/**
	 *@Description: 查询科技成果关联合作单位详情
	 *@param id
	 *@return RewordCooperUnit
	 *@Author: wangsh
	 */
	RewordCooperUnit findById(String id);

	/**
	 *@Description: 分页查询科技成果关联合作单位
	 *@param rewordCooperUnitVo
	 *@return PageInfo<RewordCooperUnit>
	 *@Author: wangsh
	 */
	PageInfo<RewordCooperUnit> findPageByQuery(RewordCooperUnitVo rewordCooperUnitVo);
	
	
	/**
	 * 提交
	 * @param rewordCooperUnitVo
	 * @return
	 */
    String submitRewordCooperUnit(RewordCooperUnitVo rewordCooperUnitVo);

	/**
	 * 审核
	 * @param rewordCooperUnitVo
	 * @return
	 */
	String auditRewordCooperUnit(RewordCooperUnitVo rewordCooperUnitVo);

	/**
	 * 退回
	 * @param rewordCooperUnitVo
	 * @return
	 */
	String sendBackRewordCooperUnit(RewordCooperUnitVo rewordCooperUnitVo);

	/**
	 * 任务书下达
	 * @param rewordCooperUnitVo
	 * @return
	 */
	String releaseRewordCooperUnit(RewordCooperUnitVo rewordCooperUnitVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<RewordCooperUnit> todoList(RewordCooperUnitVo rewordCooperUnitVo);

	/**
	 * 已办列表
	 */
	PageInfo<RewordCooperUnit> finishedList(RewordCooperUnitVo rewordCooperUnitVo);

	/**
	 * 已完成列表
	 */
	PageInfo<RewordCooperUnit> endList(RewordCooperUnitVo rewordCooperUnitVo);
}
