package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationPrivinceLaboratoryApply;
import com.fd.stdp.beans.innovation.vo.InnovationPrivinceLaboratoryApplyVo;
/**
 *@Description: 省重点实验室
 *@Author: wangsh
 *@Date: 2022-03-07 19:46:13
 */
public interface InnovationPrivinceLaboratoryApplyService {

	/**
	 *@Description: 保存或更新省重点实验室
	 *@param innovationPrivinceLaboratoryApply 省重点实验室对象
	 *@return String 省重点实验室ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationPrivinceLaboratoryApply(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApply);
	
	/**
	 *@Description: 删除省重点实验室
	 *@param id void 省重点实验室ID
	 *@Author: wangsh
	 */
	void deleteInnovationPrivinceLaboratoryApply(String id);

	/**
	 * @Description: 批量删除省重点实验室
	 * @param ids
	 */
    void deleteMultiInnovationPrivinceLaboratoryApply(List<String> ids);

	/**
	 *@Description: 查询省重点实验室详情
	 *@param id
	 *@return InnovationPrivinceLaboratoryApply
	 *@Author: wangsh
	 */
	InnovationPrivinceLaboratoryApply findById(String id);

	/**
	 *@Description: 分页查询省重点实验室
	 *@param innovationPrivinceLaboratoryApplyVo
	 *@return PageInfo<InnovationPrivinceLaboratoryApply>
	 *@Author: wangsh
	 */
	PageInfo<InnovationPrivinceLaboratoryApply> findPageByQuery(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApplyVo);
	
	
	/**
	 * 提交
	 * @param innovationPrivinceLaboratoryApplyVo
	 * @return
	 */
    String submitInnovationPrivinceLaboratoryApply(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApplyVo);

	/**
	 * 审核
	 * @param innovationPrivinceLaboratoryApplyVo
	 * @return
	 */
	String auditInnovationPrivinceLaboratoryApply(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApplyVo);

	/**
	 * 退回
	 * @param innovationPrivinceLaboratoryApplyVo
	 * @return
	 */
	String sendBackInnovationPrivinceLaboratoryApply(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApplyVo);

	/**
	 * 任务书下达
	 * @param innovationPrivinceLaboratoryApplyVo
	 * @return
	 */
	String releaseInnovationPrivinceLaboratoryApply(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApplyVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnovationPrivinceLaboratoryApply> todoList(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApplyVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationPrivinceLaboratoryApply> finishedList(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApplyVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationPrivinceLaboratoryApply> endList(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApplyVo);
}
