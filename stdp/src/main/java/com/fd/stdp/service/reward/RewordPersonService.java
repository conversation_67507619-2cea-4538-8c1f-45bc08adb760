package com.fd.stdp.service.reward;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.reward.RewordPerson;
import com.fd.stdp.beans.reward.vo.RewordPersonVo;
/**
 *@Description: 科技成果关联完成人员
 *@Author: wangsh
 *@Date: 2022-07-05 16:59:18
 */
public interface RewordPersonService {

	/**
	 *@Description: 保存或更新科技成果关联完成人员
	 *@param rewordPerson 科技成果关联完成人员对象
	 *@return String 科技成果关联完成人员ID
	 *@Author: wangsh
	 */
	String saveOrUpdateRewordPerson(RewordPersonVo rewordPerson);
	
	/**
	 *@Description: 删除科技成果关联完成人员
	 *@param id void 科技成果关联完成人员ID
	 *@Author: wangsh
	 */
	void deleteRewordPerson(String id);

	/**
	 * @Description: 批量删除科技成果关联完成人员
	 * @param ids
	 */
    void deleteMultiRewordPerson(List<String> ids);

	/**
	 *@Description: 查询科技成果关联完成人员详情
	 *@param id
	 *@return RewordPerson
	 *@Author: wangsh
	 */
	RewordPerson findById(String id);

	/**
	 *@Description: 分页查询科技成果关联完成人员
	 *@param rewordPersonVo
	 *@return PageInfo<RewordPerson>
	 *@Author: wangsh
	 */
	PageInfo<RewordPerson> findPageByQuery(RewordPersonVo rewordPersonVo);
	
	
	/**
	 * 提交
	 * @param rewordPersonVo
	 * @return
	 */
    String submitRewordPerson(RewordPersonVo rewordPersonVo);

	/**
	 * 审核
	 * @param rewordPersonVo
	 * @return
	 */
	String auditRewordPerson(RewordPersonVo rewordPersonVo);

	/**
	 * 退回
	 * @param rewordPersonVo
	 * @return
	 */
	String sendBackRewordPerson(RewordPersonVo rewordPersonVo);

	/**
	 * 任务书下达
	 * @param rewordPersonVo
	 * @return
	 */
	String releaseRewordPerson(RewordPersonVo rewordPersonVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<RewordPerson> todoList(RewordPersonVo rewordPersonVo);

	/**
	 * 已办列表
	 */
	PageInfo<RewordPerson> finishedList(RewordPersonVo rewordPersonVo);

	/**
	 * 已完成列表
	 */
	PageInfo<RewordPerson> endList(RewordPersonVo rewordPersonVo);
}
