package com.fd.stdp.service.talent;

import java.util.List;

import com.fd.stdp.beans.basic.BasicGradeLinked;
import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamApply;
import com.fd.stdp.beans.talent.vo.TalentTeamApplyVo;
/**
 *@Description: 科技创新团队申请书
 *@Author: wangsh
 *@Date: 2022-02-14 10:21:55
 */
public interface TalentTeamApplyService {

	/**
	 *@Description: 保存或更新科技创新团队申请书
	 *@param talentTeamApply 科技创新团队申请书对象
	 *@return String 科技创新团队申请书ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTalentTeamApply(TalentTeamApplyVo talentTeamApply);
	
	/**
	 *@Description: 删除科技创新团队申请书
	 *@param id void 科技创新团队申请书ID
	 *@Author: wangsh
	 */
	void deleteTalentTeamApply(String id);

	/**
	 * @Description: 批量删除科技创新团队申请书
	 * @param ids
	 */
    void deleteMultiTalentTeamApply(List<String> ids);

	/**
	 *@Description: 查询科技创新团队申请书详情
	 *@param id
	 *@return TalentTeamApply
	 *@Author: wangsh
	 */
	TalentTeamApply findById(String id);

	/**
	 *@Description: 分页查询科技创新团队申请书
	 *@param talentTeamApplyVo
	 *@return PageInfo<TalentTeamApply>
	 *@Author: wangsh
	 */
	PageInfo<TalentTeamApply> findPageByQuery(TalentTeamApplyVo talentTeamApplyVo);
	
	
	/**
	 * 提交
	 * @param talentTeamApplyVo
	 * @return
	 */
    String submitTalentTeamApply(TalentTeamApplyVo talentTeamApplyVo);

	/**
	 * 审核
	 * @param talentTeamApplyVo
	 * @return
	 */
	String auditTalentTeamApply(TalentTeamApplyVo talentTeamApplyVo);

	/**
	 * 退回
	 * @param talentTeamApplyVo
	 * @return
	 */
	String sendBackTalentTeamApply(TalentTeamApplyVo talentTeamApplyVo);

	/**
	 * 任务书下达
	 * @param talentTeamApplyVo
	 * @return
	 */
	String releaseTalentTeamApply(TalentTeamApplyVo talentTeamApplyVo);
	
	/**
	 * 待办列表
	*/
    PageInfo<TalentTeamApply> todoList(TalentTeamApplyVo talentTeamApplyVo);

	/**
	 * 已办列表
	*/
	PageInfo<TalentTeamApply> finishedList(TalentTeamApplyVo talentTeamApplyVo);

	/**
	 * 专家评审提交
	 * @param vo
	 * @return
	 */
    String expertSubmit(TalentTeamApplyVo vo);

	/**
	 * 获取尚未开始填报的任务书列表
	 * @param vo
	 * @return
	 */
	PageInfo<TalentTeamApply> toContractList(TalentTeamApplyVo vo);

	/**
	 * 选取的列表生成任务书
	 * @param vo
	 */
	void createContractTalentTeamApply(TalentTeamApplyVo vo);
}
