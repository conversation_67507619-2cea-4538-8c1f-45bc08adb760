package com.fd.stdp.service.appraisal.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.appraisal.AppraisalFileInfo;
import com.fd.stdp.beans.appraisal.vo.AppraisalFileInfoVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.appraisal.AppraisalFileInfoMapper;
import com.fd.stdp.service.appraisal.AppraisalFileInfoService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 评价佐证材料Service业务层处理
 * @date 2021-11-18
 */
@Service
@Transactional(readOnly = true)
public class AppraisalFileInfoServiceImpl extends BaseServiceImpl<AppraisalFileInfoMapper, AppraisalFileInfo> implements AppraisalFileInfoService {

    private static final Logger logger = LoggerFactory.getLogger(AppraisalFileInfoServiceImpl.class);
    @Autowired
    private AppraisalFileInfoMapper appraisalFileInfoMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新评价佐证材料
     *@param appraisalFileInfo 评价佐证材料对象
     *@return String 评价佐证材料ID
     *@Author: yujianfei
     */
    public String saveOrUpdateAppraisalFileInfo(AppraisalFileInfo appraisalFileInfo) {
        if (appraisalFileInfo == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(appraisalFileInfo.getId())) {
            //新增
            appraisalFileInfo.setId(UUIDUtils.getUUID());
            appraisalFileInfoMapper.insertSelective(appraisalFileInfo);
        } else {
            //避免页面传入修改
            appraisalFileInfo.setYn(null);
            appraisalFileInfoMapper.updateByPrimaryKeySelective(appraisalFileInfo);
        }
        return appraisalFileInfo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除评价佐证材料
     *@param id void 评价佐证材料ID
     *@Author: yujianfei
     */
    public void deleteAppraisalFileInfo(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            AppraisalFileInfo appraisalFileInfo = appraisalFileInfoMapper.selectByPrimaryKey(id);
            if (appraisalFileInfo == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            AppraisalFileInfo temappraisalFileInfo = new AppraisalFileInfo();
            temappraisalFileInfo.setYn(CommonConstant.FLAG_NO);
            temappraisalFileInfo.setId(appraisalFileInfo.getId());
            appraisalFileInfoMapper.updateByPrimaryKeySelective(temappraisalFileInfo);
        }
    }

    /**
     * @param id
     * @return AppraisalFileInfo
     * @Description: 查询评价佐证材料详情
     * @Author: yujianfei
     */
    @Override
    public AppraisalFileInfo findById(String id) {
        return appraisalFileInfoMapper.selectByPrimaryKey(id);
    }


    /**
     * @param appraisalFileInfoVo
     * @return PageInfo<AppraisalFileInfo>
     * @Description: 分页查询评价佐证材料
     * @Author: yujianfei
     */
    @Override
    public PageInfo<AppraisalFileInfo> findPageByQuery(AppraisalFileInfoVo appraisalFileInfoVo) {
        PageHelper.startPage(appraisalFileInfoVo.getPageNum(), appraisalFileInfoVo.getPageSize());
        Example example = new Example(AppraisalFileInfo.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(appraisalFileInfoVo.getName())){
        //	criteria.andEqualTo(appraisalFileInfoVo.getName());
        //}
        List<AppraisalFileInfo> appraisalFileInfoList = appraisalFileInfoMapper.selectByExample(example);
        return new PageInfo<AppraisalFileInfo>(appraisalFileInfoList);
    }
}
