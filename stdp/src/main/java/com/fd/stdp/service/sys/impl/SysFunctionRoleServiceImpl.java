package com.fd.stdp.service.sys.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.fd.stdp.beans.sys.SysFunctionRole;
import com.fd.stdp.beans.sys.vo.SysFunctionRoleVo;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.dao.sys.SysFunctionRoleMapper;
import com.fd.stdp.service.sys.SysFunctionRoleService;
import com.fd.stdp.util.UUIDUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description:
 *@Author: qyj
 *@Date: 2020-07-12 15:59:28
 */
public class SysFunctionRoleServiceImpl extends BaseServiceImpl<SysFunctionRoleMapper, SysFunctionRole> implements SysFunctionRoleService {

    public static final Logger logger = LoggerFactory.getLogger(SysFunctionRoleServiceImpl.class);

    @Autowired
    private SysFunctionRoleMapper sysFunctionRoleMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新
     *@param sysFunctionRole 对象
     *@return String ID
     *@Author: qyj
     */
    public String saveOrUpdateSysFunctionRole(SysFunctionRole sysFunctionRole) {
        if (sysFunctionRole == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(sysFunctionRole.getId())) {
            //新增
            sysFunctionRole.setId(UUIDUtils.getUUID());
            sysFunctionRoleMapper.insertSelective(sysFunctionRole);
        } else {
            //避免页面传入修改
            sysFunctionRole.setYn(null);
            sysFunctionRoleMapper.updateByPrimaryKeySelective(sysFunctionRole);
        }
        return sysFunctionRole.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除
     *@param id void ID
     *@Author: qyj
     */
    public void deleteSysFunctionRole(String id) {
        //TODO 做判断后方能执行删除
        SysFunctionRole sysFunctionRole = sysFunctionRoleMapper.selectByPrimaryKey(id);
        if (sysFunctionRole == null) {
            throw new ServiceException("非法请求");
        }
        //逻辑删除
        SysFunctionRole temsysFunctionRole = new SysFunctionRole();
        temsysFunctionRole.setYn(CommonConstant.FLAG_NO);
        temsysFunctionRole.setId(sysFunctionRole.getId());
        sysFunctionRoleMapper.updateByPrimaryKeySelective(temsysFunctionRole);
    }

    @Override
    /**
     *@Description: 查询详情
     *@param id
     *@return SysFunctionRole
     *@Author: qyj
     */
    public SysFunctionRole findById(String id) {
        return sysFunctionRoleMapper.selectByPrimaryKey(id);
    }

    @Override
    /**
     *@Description: 分页查询
     *@param sysFunctionRoleVo
     *@return PageInfo<SysFunctionRole>
     *@Author: qyj
     */
    public PageInfo<SysFunctionRole> findPageByQuery(SysFunctionRoleVo sysFunctionRoleVo) {
        PageHelper.startPage(sysFunctionRoleVo.getPageNum(), sysFunctionRoleVo.getPageSize());
        Example example = new Example(SysFunctionRole.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(sysFunctionRoleVo.getName())){
        //	criteria.andEqualTo(sysFunctionRoleVo.getName());
        //}
        List<SysFunctionRole> sysFunctionRoleList = sysFunctionRoleMapper.selectByExample(example);
        return new PageInfo<SysFunctionRole>(sysFunctionRoleList);
    }

    /**
     * 查找所有的functionRole
     *
     * @return
     */
    @Override
    public List<SysFunctionRole> findAll() {
        Example example = new Example(SysFunctionRole.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        example.orderBy("orderVal").asc();
        return sysFunctionRoleMapper.selectByExample(example);
    }

}
