package com.fd.stdp.service.innovation.impl;

import java.util.List;

import com.fd.stdp.beans.innovation.vo.InnovationPrivinceEngineApplyVo;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationPrivinceLaboratoryApply;
import com.fd.stdp.beans.innovation.vo.InnovationPrivinceLaboratoryApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationPrivinceLaboratoryApplyMapper;
import com.fd.stdp.service.innovation.InnovationPrivinceLaboratoryApplyService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 省重点实验室
 *@Author: wangsh
 *@Date: 2022-03-07 19:46:13
 */
public class InnovationPrivinceLaboratoryApplyServiceImpl extends BaseServiceImpl<InnovationPrivinceLaboratoryApplyMapper, InnovationPrivinceLaboratoryApply> implements InnovationPrivinceLaboratoryApplyService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationPrivinceLaboratoryApplyServiceImpl.class);
	
	@Autowired
	private InnovationPrivinceLaboratoryApplyMapper innovationPrivinceLaboratoryApplyMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新省重点实验室
	 *@param innovationPrivinceLaboratoryApply 省重点实验室对象
	 *@return String 省重点实验室ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationPrivinceLaboratoryApply(InnovationPrivinceLaboratoryApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationPrivinceLaboratoryApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationPrivinceLaboratoryApplyMapper.updateByPrimaryKeySelective(vo);
		}
		/**
		 * 附件
		 */
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除省重点实验室
	 *@param id void 省重点实验室ID
	 *@Author: wangsh
	 */
	public void deleteInnovationPrivinceLaboratoryApply(String id) {
		//TODO 做判断后方能执行删除
		InnovationPrivinceLaboratoryApply innovationPrivinceLaboratoryApply=innovationPrivinceLaboratoryApplyMapper.selectByPrimaryKey(id);
		if(innovationPrivinceLaboratoryApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationPrivinceLaboratoryApply teminnovationPrivinceLaboratoryApply=new InnovationPrivinceLaboratoryApply();
		teminnovationPrivinceLaboratoryApply.setYn(CommonConstant.FLAG_NO);
		teminnovationPrivinceLaboratoryApply.setId(innovationPrivinceLaboratoryApply.getId());
		innovationPrivinceLaboratoryApplyMapper.updateByPrimaryKeySelective(teminnovationPrivinceLaboratoryApply);
	}

    /**
     * @Description: 批量删除省重点实验室
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationPrivinceLaboratoryApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationPrivinceLaboratoryApply(id));
	}

	@Override
	/**
	 *@Description: 查询省重点实验室详情
	 *@param id
	 *@return InnovationPrivinceLaboratoryApply
	 *@Author: wangsh
	 */
	public InnovationPrivinceLaboratoryApply findById(String id) {
		InnovationPrivinceLaboratoryApply apply = innovationPrivinceLaboratoryApplyMapper.selectByPrimaryKey(id);

		InnovationPrivinceLaboratoryApplyVo vo = new InnovationPrivinceLaboratoryApplyVo();
		BeanUtils.copyProperties(apply, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询省重点实验室
	 *@param innovationPrivinceLaboratoryApplyVo
	 *@return PageInfo<InnovationPrivinceLaboratoryApply>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationPrivinceLaboratoryApply> findPageByQuery(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApplyVo) {
		PageHelper.startPage(innovationPrivinceLaboratoryApplyVo.getPageNum(),innovationPrivinceLaboratoryApplyVo.getPageSize());
		Example example=new Example(InnovationPrivinceLaboratoryApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationPrivinceLaboratoryApplyVo.getName())){
		//	criteria.andEqualTo(innovationPrivinceLaboratoryApplyVo.getName());
		//}
		List<InnovationPrivinceLaboratoryApply> innovationPrivinceLaboratoryApplyList=innovationPrivinceLaboratoryApplyMapper.selectByExample(example);
		return new PageInfo<InnovationPrivinceLaboratoryApply>(innovationPrivinceLaboratoryApplyList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationPrivinceLaboratoryApply(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApplyVo) {
		String id = this.saveOrUpdateInnovationPrivinceLaboratoryApply(innovationPrivinceLaboratoryApplyVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innovationPrivinceLaboratoryApplyVo, this.mapper,
				StringUtils.isNotBlank(innovationPrivinceLaboratoryApplyVo.getAuditAdvice())?innovationPrivinceLaboratoryApplyVo.getAuditAdvice():"提交省重点实验室");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationPrivinceLaboratoryApply(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApplyVo) {
		flowCommonService.doFlowStepAudit(innovationPrivinceLaboratoryApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationPrivinceLaboratoryApplyVo.getAuditAdvice()) ? innovationPrivinceLaboratoryApplyVo.getAuditAdvice() : "省重点实验室审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationPrivinceLaboratoryApplyVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationPrivinceLaboratoryApply(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApplyVo) {
		flowCommonService.doFlowStepSendBack(innovationPrivinceLaboratoryApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationPrivinceLaboratoryApplyVo.getAuditAdvice())?innovationPrivinceLaboratoryApplyVo.getAuditAdvice():"省重点实验室退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationPrivinceLaboratoryApply(InnovationPrivinceLaboratoryApplyVo innovationPrivinceLaboratoryApplyVo) {
		flowCommonService.doCompleteTask(innovationPrivinceLaboratoryApplyVo, this.mapper
				, "省重点实验室任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnovationPrivinceLaboratoryApply> todoList(InnovationPrivinceLaboratoryApplyVo vo) {

		Example example = new Example(InnovationPrivinceLaboratoryApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationPrivinceLaboratoryApply> finishedList(InnovationPrivinceLaboratoryApplyVo vo) {
		Example example = new Example(InnovationPrivinceLaboratoryApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnovationPrivinceLaboratoryApply> endList(InnovationPrivinceLaboratoryApplyVo vo) {
        Example example = new Example(InnovationPrivinceLaboratoryApply.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(InnovationPrivinceLaboratoryApply vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getLaboratory())){
			criteria.andLike("laboratory", "%" + vo.getLaboratory()+ "%");
		}
		return criteria;
	}
}
