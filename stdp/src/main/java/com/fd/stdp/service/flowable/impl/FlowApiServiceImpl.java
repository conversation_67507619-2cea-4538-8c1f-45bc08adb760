package com.fd.stdp.service.flowable.impl;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

import com.fd.stdp.beans.flow.FlowNodeVo;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.service.flowable.FlowTaskService;
import org.apache.commons.io.IOUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.EndEvent;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.bpmn.model.UserTask;
import org.flowable.common.engine.api.FlowableException;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.common.engine.impl.identity.Authentication;
import org.flowable.engine.form.TaskFormData;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.ProcessInstanceHistoryLogQuery;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.task.Comment;
import org.flowable.identitylink.api.history.HistoricIdentityLink;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.variable.api.persistence.entity.VariableInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.fd.stdp.beans.flowable.FlowCommentDto;
import com.fd.stdp.beans.flowable.FlowTaskDto;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.flowable.FlowViewerDto;
import com.fd.stdp.beans.flowable.vo.FlwTaskBukeyVo;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.config.FlowServiceFactory;
import com.fd.stdp.constant.ProcessConstants;
import com.fd.stdp.enums.FlowComment;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.flowable.util.FlowableUtils;
import com.fd.stdp.service.sys.SysUserService;
import com.fd.stdp.util.StringUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;

/**
 * 工作人流操作 对外调用api 所有业务流程都调用此类来操作flowable工作流
 * 发现没有对应的方法请自行在此添加操作，不要在其它地方操作
 *
 * <AUTHOR>
 */
@Service
@Transactional(readOnly = true)
public class FlowApiServiceImpl extends FlowServiceFactory implements FlowApiService {

    private static final Logger logger = LoggerFactory.getLogger(FlowApiServiceImpl.class);

    @Autowired
    private SysUserService sysUserService;

    /**
     * 启动一个流程    使用流程KEY进行启动
     *
     * @param processInstanceKey 流程定义KEY
     * @param businessKey        业务唯一值
     * @param userName
     * @return taskId
     */
    @Override
    @Transactional(readOnly = false)
    public String startProcessInstanceByKey(FlowTaskVo flowTaskVo) {
        try {
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(flowTaskVo.getProcessInstanceKey())
                    .latestVersion().singleResult();
            if (Objects.nonNull(processDefinition) && processDefinition.isSuspended()) {
                throw new ServiceException("流程已被挂起,请先激活流程");
            }
            //跳过表达式
//           variables.put("skip", true);
//           variables.put(ProcessConstants.FLOWABLE_SKIP_EXPRESSION_ENABLED, true);
            // 设置流程发起人Id到流程中
            identityService.setAuthenticatedUserId(flowTaskVo.getUserId());
            Map<String, Object> variables = new HashMap<>();
            variables.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getUserId());

            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(flowTaskVo.getProcessInstanceKey(), flowTaskVo.getBusinessKey(), variables);
            // 给第一步申请人节点设置任务执行人和意见 todo:第一个节点不设置为申请人节点有点问题？
            Task task = taskService.createTaskQuery().processInstanceId(processInstance.getProcessInstanceId()).singleResult();
            if (Objects.nonNull(task)) {
                taskService.addComment(task.getId(), processInstance.getProcessInstanceId(), FlowComment.NORMAL.getType(), flowTaskVo.getUserName() + "发起流程申请");
                variables.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());
                taskService.setAssignee(task.getId(), flowTaskVo.getUserId());
                taskService.complete(task.getId(), variables);
            }
            return task.getId();
        } catch (Exception e) {
            logger.error("流程启动失败：", e);
            throw new ServiceException("流程启动错误");
        }
    }

    /**
     * 完成当前任务
     * taskId 任务id
     * instanceid 流程实例id
     * comment 说明
     * userId 下级办理人
     * values 流程变量
     */
    @Override
    @Transactional(readOnly = false)
    public void completeTask(FlowTaskVo flowTaskVo) {
        String taskId = getTaskId(flowTaskVo.getBusinessKey());
        String instanceId = getProcInsId(flowTaskVo.getBusinessKey());
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (Objects.isNull(task)) {
            throw new ServiceException("任务不存在");
        }
        if (DelegationState.PENDING.equals(task.getDelegationState())) {
            taskService.addComment(taskId, instanceId, FlowComment.DELEGATE.getType(), flowTaskVo.getComment());
            taskService.resolveTask(taskId, flowTaskVo.getValues());
        } else {
            taskService.addComment(taskId, instanceId, FlowComment.NORMAL.getType(), flowTaskVo.getComment());
            taskService.setAssignee(taskId, flowTaskVo.getUserId());	//act_hi_taskinst表里面--完成之后下一级审核人未变化，但是act_ru_task表未变化

            taskService.complete(taskId, flowTaskVo.getValues());
        }
    }

    @Override
    public void completeTask(FlowTaskVo flowTaskVo, String commentType) {
        String taskId = getTaskId(flowTaskVo.getBusinessKey());
        String instanceId = getProcInsId(flowTaskVo.getBusinessKey());
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (Objects.isNull(task)) {
            throw new ServiceException("任务不存在");
        }
        taskService.addComment(taskId, instanceId, commentType, flowTaskVo.getComment());
        taskService.setAssignee(taskId, flowTaskVo.getUserId());	//act_hi_taskinst表里面--完成之后下一级审核人未变化，但是act_ru_task表未变化
        taskService.complete(taskId, flowTaskVo.getValues());
    }

    /**
     * 撤回流程  TODO 未完成
     * InstanceId 流程实例id
     * userid 用户id
     */
    @Override
    @Transactional(readOnly = false)
    public void revokeProcess(FlowTaskVo flowTaskVo) {
        Task task = taskService.createTaskQuery().processInstanceId(flowTaskVo.getInstanceId()).singleResult();
       if (task == null) {
            throw new ServiceException("流程未启动或已执行完成，无法撤回");
        }

        List<HistoricTaskInstance> htiList = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(task.getProcessInstanceId())
                .orderByTaskCreateTime()
                .asc()
                .list();
        String myTaskId = null;
        HistoricTaskInstance myTask = null;
        for (HistoricTaskInstance hti : htiList) {
            if (flowTaskVo.getUserId().equals(hti.getAssignee())) {
                myTaskId = hti.getId();
                myTask = hti;
                break;
            }
        }
        if (null == myTaskId) {
            throw new ServiceException("该任务非当前用户提交，无法撤回");
        }

        String processDefinitionId = myTask.getProcessDefinitionId();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);

        //变量
	    //  Map<String, VariableInstance> variables = runtimeService.getVariableInstances(currentTask.getExecutionId());
        String myActivityId = null;
        List<HistoricActivityInstance> haiList = historyService.createHistoricActivityInstanceQuery()
                .executionId(myTask.getExecutionId()).finished().list();
        for (HistoricActivityInstance hai : haiList) {
            if (myTaskId.equals(hai.getTaskId())) {
                myActivityId = hai.getActivityId();
                break;
            }
        }
        FlowNode myFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(myActivityId);

        Execution execution = runtimeService.createExecutionQuery().executionId(task.getExecutionId()).singleResult();
        String activityId = execution.getActivityId();
        FlowNode flowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(activityId);

        //记录原活动方向
        List<SequenceFlow> oriSequenceFlows = new ArrayList<>(flowNode.getOutgoingFlows());

    }

    /**
     * 取消申请
     * instanceId 流程实例id
     * userId 用户id
     */
    @Override
    @Transactional(readOnly = false)
    public void stopProcess(FlowTaskVo flowTaskVo) {
        List<Task> task = taskService.createTaskQuery().processInstanceId(flowTaskVo.getInstanceId()).list();
        if (CollectionUtils.isEmpty(task)) {
            throw new ServiceException("流程未启动或已执行完成，取消申请失败");
        }
        ProcessInstance processInstance =
                runtimeService.createProcessInstanceQuery().processInstanceId(flowTaskVo.getInstanceId()).singleResult();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
        if (Objects.nonNull(bpmnModel)) {
            Process process = bpmnModel.getMainProcess();
            List<EndEvent> endNodes = process.findFlowElementsOfType(EndEvent.class, false);
            if (!CollectionUtils.isEmpty(endNodes)) {
                Authentication.setAuthenticatedUserId(flowTaskVo.getUserId());
//                taskService.addComment(task.getId(), processInstance.getProcessInstanceId(), FlowComment.STOP.getType(),
//                        StringUtils.isBlank(flowTaskVo.getComment()) ? "取消申请" : flowTaskVo.getComment());
                String endId = endNodes.get(0).getId();
                List<Execution> executions =
                        runtimeService.createExecutionQuery().parentId(processInstance.getProcessInstanceId()).list();
                List<String> executionIds = new ArrayList<>();
                executions.forEach(execution -> executionIds.add(execution.getId()));
                runtimeService.createChangeActivityStateBuilder().moveExecutionsToSingleActivityId(executionIds, endId).changeState();
            }
        }
    }

    /**
     * 驳回任务
     * taskId 任务id
     * Comment 驳回意见
     */
    @Override
    @Transactional(readOnly = false)
    public void taskReject(FlowTaskVo flowTaskVo) {
        if (taskService.createTaskQuery().taskId(flowTaskVo.getTaskId()).singleResult().isSuspended()) {
            throw new ServiceException("任务处于挂起状态");
        }
        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(flowTaskVo.getTaskId()).singleResult();
        // 获取流程定义信息
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(task.getProcessDefinitionId()).singleResult();
        // 获取所有节点信息
        Process process = repositoryService.getBpmnModel(processDefinition.getId()).getProcesses().get(0);
        // 获取全部节点列表，包含子节点
        Collection<FlowElement> allElements = FlowableUtils.getAllElements(process.getFlowElements(), null);
        // 获取当前任务节点元素
        FlowElement source = null;
        if (allElements != null) {
            for (FlowElement flowElement : allElements) {
                // 类型为用户节点
                if (flowElement.getId().equals(task.getTaskDefinitionKey())) {
                    // 获取节点信息
                    source = flowElement;
                }
            }
        }

        // 目的获取所有跳转到的节点 targetIds
        // 获取当前节点的所有父级用户任务节点
        // 深度优先算法思想：延边迭代深入
        List<UserTask> parentUserTaskList = FlowableUtils.iteratorFindParentUserTasks(source, null, null);
        if (parentUserTaskList == null || parentUserTaskList.size() == 0) {
            throw new ServiceException("当前节点为初始任务节点，不能驳回");
        }
        // 获取活动 ID 即节点 Key
        List<String> parentUserTaskKeyList = new ArrayList<>();
        parentUserTaskList.forEach(item -> parentUserTaskKeyList.add(item.getId()));
        // 获取全部历史节点活动实例，即已经走过的节点历史，数据采用开始时间升序
        List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery().processInstanceId(task.getProcessInstanceId()).orderByHistoricTaskInstanceStartTime().asc().list();
        // 数据清洗，将回滚导致的脏数据清洗掉
        List<String> lastHistoricTaskInstanceList = FlowableUtils.historicTaskInstanceClean(allElements, historicTaskInstanceList);
        // 此时历史任务实例为倒序，获取最后走的节点
        List<String> targetIds = new ArrayList<>();
        // 循环结束标识，遇到当前目标节点的次数
        int number = 0;
        StringBuilder parentHistoricTaskKey = new StringBuilder();
        for (String historicTaskInstanceKey : lastHistoricTaskInstanceList) {
            // 当会签时候会出现特殊的，连续都是同一个节点历史数据的情况，这种时候跳过
            if (parentHistoricTaskKey.toString().equals(historicTaskInstanceKey)) {
                continue;
            }
            parentHistoricTaskKey = new StringBuilder(historicTaskInstanceKey);
            if (historicTaskInstanceKey.equals(task.getTaskDefinitionKey())) {
                number++;
            }
            // 在数据清洗后，历史节点就是唯一一条从起始到当前节点的历史记录，理论上每个点只会出现一次
            // 在流程中如果出现循环，那么每次循环中间的点也只会出现一次，再出现就是下次循环
            // number == 1，第一次遇到当前节点
            // number == 2，第二次遇到，代表最后一次的循环范围
            if (number == 2) {
                break;
            }
            // 如果当前历史节点，属于父级的节点，说明最后一次经过了这个点，需要退回这个点
            if (parentUserTaskKeyList.contains(historicTaskInstanceKey)) {
                targetIds.add(historicTaskInstanceKey);
            }
        }


        // 目的获取所有需要被跳转的节点 currentIds
        // 取其中一个父级任务，因为后续要么存在公共网关，要么就是串行公共线路
        UserTask oneUserTask = parentUserTaskList.get(0);
        // 获取所有正常进行的任务节点 Key，这些任务不能直接使用，需要找出其中需要撤回的任务
        List<Task> runTaskList = taskService.createTaskQuery().processInstanceId(task.getProcessInstanceId()).list();
        List<String> runTaskKeyList = new ArrayList<>();
        runTaskList.forEach(item -> runTaskKeyList.add(item.getTaskDefinitionKey()));
        // 需驳回任务列表
        List<String> currentIds = new ArrayList<>();
        // 通过父级网关的出口连线，结合 runTaskList 比对，获取需要撤回的任务
        List<UserTask> currentUserTaskList = FlowableUtils.iteratorFindChildUserTasks(oneUserTask, runTaskKeyList, null, null);
        currentUserTaskList.forEach(item -> currentIds.add(item.getId()));


        // 规定：并行网关之前节点必须需存在唯一用户任务节点，如果出现多个任务节点，则并行网关节点默认为结束节点，原因为不考虑多对多情况
        if (targetIds.size() > 1 && currentIds.size() > 1) {
            throw new ServiceException("任务出现多对多情况，无法撤回");
        }

        // 循环获取那些需要被撤回的节点的ID，用来设置驳回原因
        List<String> currentTaskIds = new ArrayList<>();
        currentIds.forEach(currentId -> runTaskList.forEach(runTask -> {
            if (currentId.equals(runTask.getTaskDefinitionKey())) {
                currentTaskIds.add(runTask.getId());
            }
        }));
        // 设置驳回意见
        currentTaskIds.forEach(item -> taskService.addComment(item, task.getProcessInstanceId(), FlowComment.REJECT.getType(), flowTaskVo.getComment()));

        try {
            // 如果父级任务多于 1 个，说明当前节点不是并行节点，原因为不考虑多对多情况
            if (targetIds.size() > 1) {
                // 1 对 多任务跳转，currentIds 当前节点(1)，targetIds 跳转到的节点(多)
                runtimeService.createChangeActivityStateBuilder()
                        .processInstanceId(task.getProcessInstanceId()).
                        moveSingleActivityIdToActivityIds(currentIds.get(0), targetIds).changeState();
            }
            // 如果父级任务只有一个，因此当前任务可能为网关中的任务
            if (targetIds.size() == 1) {
                // 1 对 1 或 多 对 1 情况，currentIds 当前要跳转的节点列表(1或多)，targetIds.get(0) 跳转到的节点(1)
                runtimeService.createChangeActivityStateBuilder()
                        .processInstanceId(task.getProcessInstanceId())
                        .moveActivityIdsToSingleActivityId(currentIds, targetIds.get(0)).changeState();
            }
        } catch (FlowableObjectNotFoundException e) {
            throw new ServiceException("未找到流程实例，流程可能已发生变化");
        } catch (FlowableException e) {
            throw new ServiceException("无法取消或开始活动");
        }
    }

    /**
     * 退回任务
     * taskId 任务id
     * TargetKey 目标跳转节点key
     * Comment 退回说明
     */
    @Override
    @Transactional(readOnly = false)
    public void taskReturn(FlowTaskVo flowTaskVo) {
        if (taskService.createTaskQuery().taskId(flowTaskVo.getTaskId()).singleResult().isSuspended()) {
            throw new ServiceException("任务处于挂起状态");
        }
        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(flowTaskVo.getTaskId()).singleResult();
        // 获取流程定义信息
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(task.getProcessDefinitionId()).singleResult();
        // 获取所有节点信息
        Process process = repositoryService.getBpmnModel(processDefinition.getId()).getProcesses().get(0);
        // 获取全部节点列表，包含子节点
        Collection<FlowElement> allElements = FlowableUtils.getAllElements(process.getFlowElements(), null);
        // 获取当前任务节点元素
        FlowElement source = null;
        // 获取跳转的节点元素
        FlowElement target = null;
        if (allElements != null) {
            for (FlowElement flowElement : allElements) {
                // 当前任务节点元素
                if (flowElement.getId().equals(task.getTaskDefinitionKey())) {
                    source = flowElement;
                }
                // 跳转的节点元素
                if (flowElement.getId().equals(flowTaskVo.getTargetKey())) {
                    target = flowElement;
                }
            }
        }

        // 从当前节点向前扫描
        // 如果存在路线上不存在目标节点，说明目标节点是在网关上或非同一路线上，不可跳转
        // 否则目标节点相对于当前节点，属于串行
        Boolean isSequential = FlowableUtils.iteratorCheckSequentialReferTarget(source, flowTaskVo.getTargetKey(), null, null);
        if (!isSequential) {
            throw new ServiceException("当前节点相对于目标节点，不属于串行关系，无法回退");
        }


        // 获取所有正常进行的任务节点 Key，这些任务不能直接使用，需要找出其中需要撤回的任务
        List<Task> runTaskList = taskService.createTaskQuery().processInstanceId(task.getProcessInstanceId()).list();
        List<String> runTaskKeyList = new ArrayList<>();
        runTaskList.forEach(item -> runTaskKeyList.add(item.getTaskDefinitionKey()));
        // 需退回任务列表
        List<String> currentIds = new ArrayList<>();
        // 通过父级网关的出口连线，结合 runTaskList 比对，获取需要撤回的任务
        List<UserTask> currentUserTaskList = FlowableUtils.iteratorFindChildUserTasks(target, runTaskKeyList, null, null);
        currentUserTaskList.forEach(item -> currentIds.add(item.getId()));

        // 循环获取那些需要被撤回的节点的ID，用来设置驳回原因
        List<String> currentTaskIds = new ArrayList<>();
        currentIds.forEach(currentId -> runTaskList.forEach(runTask -> {
            if (currentId.equals(runTask.getTaskDefinitionKey())) {
                currentTaskIds.add(runTask.getId());
            }
        }));
        // 设置回退意见
        for (String currentTaskId : currentTaskIds) {
            taskService.addComment(currentTaskId, task.getProcessInstanceId(), FlowComment.REBACK.getType(), flowTaskVo.getComment());
        }

        try {
            // 1 对 1 或 多 对 1 情况，currentIds 当前要跳转的节点列表(1或多)，targetKey 跳转到的节点(1)
            runtimeService.createChangeActivityStateBuilder()
                    .processInstanceId(task.getProcessInstanceId())
                    .moveActivityIdsToSingleActivityId(currentIds, flowTaskVo.getTargetKey()).changeState();
        } catch (FlowableObjectNotFoundException e) {
            throw new ServiceException("未找到流程实例，流程可能已发生变化");
        } catch (FlowableException e) {
            throw new ServiceException("无法取消或开始活动");
        }
    }

    /**
     * 删除任务
     * taskId 任务id
     * comment 删除说明
     */
    @Override
    @Transactional(readOnly = false)
    public void deleteTask(FlowTaskVo flowTaskVo) {
        // TODO 待确认删除任务是物理删除任务 还是逻辑删除，让这个任务直接通过？
        taskService.deleteTask(flowTaskVo.getTaskId(), flowTaskVo.getComment());
    }

    /**
     * 认领/签收任务
     * taskId 任务id
     * userId 认领/签收人
     */
    @Override
    @Transactional(readOnly = false)
    public void claim(FlowTaskVo flowTaskVo) {
        taskService.claim(flowTaskVo.getTaskId(), flowTaskVo.getUserId());
    }

    /**
     * 取消认领/签收任务
     * taskId 任务id
     */
    @Override
    @Transactional(readOnly = false)
    public void unClaim(FlowTaskVo flowTaskVo) {
        taskService.unclaim(flowTaskVo.getTaskId());
    }

    /**
     * 委派任务
     * taskId 任务id
     * assignee 委派办理人
     */
    @Override
    @Transactional(readOnly = false)
    public void delegateTask(FlowTaskVo flowTaskVo) {
        taskService.delegateTask(flowTaskVo.getTaskId(), flowTaskVo.getAssignee());
    }

    /**
     * 转办任务
     * taskId 任务id
     * assignee 办理人
     */
    @Override
    @Transactional(readOnly = false)
    public void assignTask(FlowTaskVo flowTaskVo) {
        taskService.setAssignee(flowTaskVo.getTaskId(), flowTaskVo.getAssignee());
    }

    /**
     * 获取所有可回退的节点
     * taskId 任务id
     */
    @Override
    public List<UserTask> findReturnTaskList(FlowTaskVo flowTaskVo) {
        // 当前任务 task
        Task task = taskService.createTaskQuery().taskId(flowTaskVo.getTaskId()).singleResult();
        // 获取流程定义信息
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(task.getProcessDefinitionId()).singleResult();
        // 获取所有节点信息，暂不考虑子流程情况
        Process process = repositoryService.getBpmnModel(processDefinition.getId()).getProcesses().get(0);
        Collection<FlowElement> flowElements = process.getFlowElements();
        // 获取当前任务节点元素
        UserTask source = null;
        if (flowElements != null) {
            for (FlowElement flowElement : flowElements) {
                // 类型为用户节点
                if (flowElement.getId().equals(task.getTaskDefinitionKey())) {
                    source = (UserTask) flowElement;
                }
            }
        }
        // 获取节点的所有路线
        List<List<UserTask>> roads = FlowableUtils.findRoad(source, null, null, null);
        // 可回退的节点列表
        List<UserTask> userTaskList = new ArrayList<>();
        for (List<UserTask> road : roads) {
            if (userTaskList.size() == 0) {
                // 还没有可回退节点直接添加
                userTaskList = road;
            } else {
                // 如果已有回退节点，则比对取交集部分
                userTaskList.retainAll(road);
            }
        }
        return userTaskList;
    }

    /**
     * 我的待办
     */
    @Override
    public PageInfo<FlowTaskDto> todoList(String userId, Integer pageNum, Integer pageSize) {
        PageInfo<FlowTaskDto> page = new PageInfo<>();
        TaskQuery taskQuery = taskService.createTaskQuery()
                .active()
                .includeProcessVariables()
                .taskAssignee(userId)
                .orderByTaskCreateTime().desc();
        page.setTotal(taskQuery.count());
        List<Task> taskList = taskQuery.listPage(pageNum - 1, pageSize);
        List<FlowTaskDto> flowList = new ArrayList<>();
        for (Task task : taskList) {
            FlowTaskDto flowTask = new FlowTaskDto();
            // 当前流程信息
            flowTask.setTaskId(task.getId());
            flowTask.setTaskDefKey(task.getTaskDefinitionKey());
            flowTask.setCreateTime(task.getCreateTime());
            flowTask.setProcDefId(task.getProcessDefinitionId());
            flowTask.setTaskName(task.getName());
            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(task.getProcessDefinitionId())
                    .singleResult();
            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(task.getProcessInstanceId());

            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId())
                    .singleResult();
            SysUser startUser = sysUserService.getUser(historicProcessInstance.getStartUserId());
            flowTask.setStartUserId(startUser.getUsername());
            flowTask.setStartUserName(startUser.getNickname());
            flowList.add(flowTask);
        }
        page.setList(flowList);
        return page;
    }

    /**
     * 我的已办
     */
    @Override
    public PageInfo<FlowTaskDto> finishedList(String userId, Integer pageNum, Integer pageSize) {
        PageInfo<FlowTaskDto> page = new PageInfo<>();
        HistoricTaskInstanceQuery taskInstanceQuery = historyService.createHistoricTaskInstanceQuery()
                .includeProcessVariables()
                .finished()
                .taskAssignee(userId)
                .orderByHistoricTaskInstanceEndTime()
                .desc();
        List<HistoricTaskInstance> historicTaskInstanceList = taskInstanceQuery.listPage(pageNum - 1, pageSize);
        List<FlowTaskDto> hisTaskList = Lists.newArrayList();
        for (HistoricTaskInstance histTask : historicTaskInstanceList) {
            FlowTaskDto flowTask = new FlowTaskDto();
            // 当前流程信息
            flowTask.setTaskId(histTask.getId());
            // 审批人员信息
            flowTask.setCreateTime(histTask.getCreateTime());
            flowTask.setFinishTime(histTask.getEndTime());
            flowTask.setDuration(getDate(histTask.getDurationInMillis()));
            flowTask.setProcDefId(histTask.getProcessDefinitionId());
            flowTask.setTaskDefKey(histTask.getTaskDefinitionKey());
            flowTask.setTaskName(histTask.getName());

            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(histTask.getProcessDefinitionId())
                    .singleResult();
            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(histTask.getProcessInstanceId());
            flowTask.setHisProcInsId(histTask.getProcessInstanceId());

            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(histTask.getProcessInstanceId())
                    .singleResult();
            SysUser startUser = sysUserService.getUser(historicProcessInstance.getStartUserId());
            flowTask.setStartUserId(startUser.getNickname());
            flowTask.setStartUserName(startUser.getNickname());
            hisTaskList.add(flowTask);
        }
        page.setTotal(hisTaskList.size());
        page.setList(hisTaskList);
        return page;
    }

    /**
     * 通过业务key得到表单配置
     *
     * @param pid
     * @return
     */
    @Override
    public Map<String, Object> getFormKey(String pid) {
        String businessKey = "";
        String formKey = "";
        String prKey = "";
        String localAssignee = "";

        try {
            List<Task> tasks = taskService.createTaskQuery()
                    .processInstanceId(pid).list();
            String id = "";
            // 1 获取任务对象,会签状态下回有多个tasks
            if (tasks.size() >= 1) {
                id = tasks.get(0).getId();
            }
            TaskFormData formData = formService.getTaskFormData(id);
            formKey = formData.getFormKey();

            Task task = taskService.createTaskQuery().taskId(id).singleResult();
            localAssignee = task.getAssignee();

            // 2 通过任务对象获取流程实例
            ProcessInstance pi = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId()).singleResult();
            // 3 通过流程实例获取“业务键”
            businessKey = pi.getBusinessKey();

        } catch (Exception e) {
            //表示流程已经结束
            ProcessInstanceHistoryLogQuery plog = historyService.createProcessInstanceHistoryLogQuery(pid);
            //plog.singleResult().getBusinessKey();
            //bukey
            prKey = plog.singleResult().getProcessDefinitionId();
            if (StringUtils.isEmpty(prKey)) {
                throw new ServiceException("系统异常");
            }
            prKey = prKey.split(":")[0];
            switch (prKey) {
                //读取字典流程结束时默认页面
                // 需求征集
                case "SYSTEM_FLOWABLE_STDP_DEMAND_APPLY":
					formKey = "/demand/apply/audit";
                    break;
                 //请假
				case "FLOWABLE_LEAVE":
					formKey = "/user/leave/dept";
                    break;
				default:
					break;
            }
        }
        Map<String, Object> handleMap = new HashMap<String, Object>();
        handleMap.put("url", formKey);
        handleMap.put("businessKey", businessKey);
        handleMap.put("localAssignee", localAssignee);

        return handleMap;
    }

    /**
     * 根据业务key得到流程实例id
     */
    @Override
    public String getProcInsId(String businessKey) {
        //使用businessKey流程实例查询
        ProcessInstance processInstance = runtimeService
                .createProcessInstanceQuery()
                .processInstanceBusinessKey(businessKey)
                .singleResult();
        return processInstance.getId();
    }

    /**
     * 根据业务key得到流程实例id 对应集合
     */
    @Override
    public List<FlwTaskBukeyVo> getProcInsId(List<String> businessKeys) {
        List<FlwTaskBukeyVo> proidBukeyList = new ArrayList<>();
        for (String buKey : businessKeys) {
            FlwTaskBukeyVo proidBukey = new FlwTaskBukeyVo();
            proidBukey.setBusinessKey(buKey);
            proidBukey.setProcInsId(getProcInsId(buKey));
            proidBukeyList.add(proidBukey);
        }
        return proidBukeyList;
    }

    @Override
    public Boolean hasTask(String businessKey) {
        Task task = taskService.createTaskQuery()
                .processInstanceBusinessKey(businessKey)
                .singleResult();
        return task != null;
    }

    /**
     * 根据业务key得到taskid
     *
     * @param businessKey
     * @return
     */
    @Override
    public String getTaskId(String businessKey) {
        Task task = taskService.createTaskQuery()
                .processInstanceBusinessKey(businessKey)
                .singleResult();
        return task==null?null:task.getId();
    }

    /**
     * 根据业务key得到taskid 对应集合
     *
     * @param businessKeys
     * @return
     */
    @Override
    public List<FlwTaskBukeyVo> getTaskId(List<String> businessKeys) {
        List<FlwTaskBukeyVo> taskIdBukeyList = new ArrayList<>();
        for (String buKey : businessKeys) {
            FlwTaskBukeyVo taskIdBukey = new FlwTaskBukeyVo();
            taskIdBukey.setBusinessKey(buKey);
            taskIdBukey.setTaskId(getTaskId(buKey));
            taskIdBukeyList.add(taskIdBukey);
        }
        return taskIdBukeyList;
    }

    /**
     * 获取流程执行过程
     */
    @Override
    public List<FlowViewerDto> getFlowViewer(String procInsId) {
        List<FlowViewerDto> flowViewerList = new ArrayList<>();
        FlowViewerDto flowViewerDto;
        // 获得活动的节点
        List<HistoricActivityInstance> hisActIns = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(procInsId)
                .orderByHistoricActivityInstanceStartTime()
                .asc().list();
        for (HistoricActivityInstance activityInstance : hisActIns) {
            if (!"sequenceFlow".equals(activityInstance.getActivityType())) {
                flowViewerDto = new FlowViewerDto();
                flowViewerDto.setKey(activityInstance.getActivityId());
                flowViewerDto.setCompleted(!Objects.isNull(activityInstance.getEndTime()));
                flowViewerList.add(flowViewerDto);
            }
        }
        return flowViewerList;
    }

    /**
     * 根据流程定义key得到流程定义xml
     */
    @Override
    public String readXml(String processInstanceKey) {
        ProcessDefinition definition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(processInstanceKey).singleResult();
        InputStream inputStream = repositoryService.getResourceAsStream(definition.getDeploymentId(), definition.getResourceName());
        String result;
        try {
            result = IOUtils.toString(inputStream, StandardCharsets.UTF_8.name());
            return result;
        } catch (IOException e) {
            logger.error("读取流程定义xml失败", e);
            throw new ServiceException("读取流程定义xml失败");
        }
    }

    /**
     * 流程审批记录
     */
    @Override
    public List<FlowTaskDto> flowRecord(String procInsId, String processInstanceKey) {
        Map<String, Object> map = new HashMap<String, Object>();
        if (StringUtils.isNotBlank(procInsId)) {

        }
        List<HistoricActivityInstance> list = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(procInsId)
                .orderByHistoricActivityInstanceStartTime()
                .desc().list();
        List<FlowTaskDto> hisFlowList = new ArrayList<>();
        for (HistoricActivityInstance histIns : list) {
            if (StringUtils.isNotBlank(histIns.getTaskId())) {
                FlowTaskDto flowTask = new FlowTaskDto();
                flowTask.setTaskId(histIns.getTaskId());
                flowTask.setTaskName(histIns.getActivityName());
                flowTask.setCreateTime(histIns.getStartTime());
                flowTask.setFinishTime(histIns.getEndTime());
                if (StringUtils.isNotBlank(histIns.getAssignee())) {
                    SysUser sysUser = sysUserService.getUser(histIns.getAssignee());
                    flowTask.setAssigneeId(sysUser.getUsername());
                    flowTask.setAssigneeName(sysUser.getNickname());
                }

                // 展示审批人员
                List<HistoricIdentityLink> linksForTask = historyService.getHistoricIdentityLinksForTask(histIns.getTaskId());
                StringBuilder stringBuilder = new StringBuilder();
                for (HistoricIdentityLink identityLink : linksForTask) {
                    if ("candidate".equals(identityLink.getType())) {
                        if (StringUtils.isNotBlank(identityLink.getUserId())) {
                            SysUser sysUser = sysUserService.getUser(identityLink.getUserId());
                            stringBuilder.append(sysUser.getNickname()).append(",");
                        }
//                            if (StringUtils.isNotBlank(identityLink.getGroupId())) {
//                                SysRole sysRole = sysRoleService.selectRoleById(Long.parseLong(identityLink.getGroupId()));
//                                stringBuilder.append(sysRole.getRoleName()).append(",");
//                            }
                    }
                }
                if (StringUtils.isNotBlank(stringBuilder)) {
                    flowTask.setCandidate(stringBuilder.substring(0, stringBuilder.length() - 1));
                }

                flowTask.setDuration(histIns.getDurationInMillis() == null || histIns.getDurationInMillis() == 0 ? null : getDate(histIns.getDurationInMillis()));
                // 获取意见评论内容
                List<Comment> commentList = taskService.getProcessInstanceComments(histIns.getProcessInstanceId());
                commentList.forEach(comment -> {
                    if (histIns.getTaskId().equals(comment.getTaskId())) {

                        FlowCommentDto commentDto = new FlowCommentDto();
                        commentDto.setType(comment.getType());
                        commentDto.setComment(comment.getFullMessage());
                        flowTask.setComment(commentDto);
                    }
                });
                hisFlowList.add(flowTask);
            }
        }
        map.put("flowList", hisFlowList);
        return null;
    }

    /**
     * 流程完成时间处理
     *
     * @param ms
     * @return
     */
    private String getDate(long ms) {

        long day = ms / (24 * 60 * 60 * 1000);
        long hour = (ms / (60 * 60 * 1000) - day * 24);
        long minute = ((ms / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long second = (ms / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - minute * 60);

        if (day > 0) {
            return day + "天" + hour + "小时" + minute + "分钟";
        }
        if (hour > 0) {
            return hour + "小时" + minute + "分钟";
        }
        if (minute > 0) {
            return minute + "分钟";
        }
        if (second > 0) {
            return second + "秒";
        } else {
            return 0 + "秒";
        }
    }

    @Autowired
    FlowTaskService flowTaskService;

    /**
     * 内部调用启动流程实例并前进一步
     *
     * @param taskVo
     * @param currentUserId
     * @return
     */
    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public FlowNodeVo startProcessWithNext(FlowTaskVo taskVo, String currentUserId) {
        // 启动流程
        String taskId = startProcessInstanceByKeyAndAreaCode(taskVo);
        if (taskId != null) {
            // 自动移动到下一个节点
            return flowTaskService.complete2(taskVo, currentUserId);
        }
        return null;
    }


    /**
     * 根据Key启动流程返回taskId
     *
     * @param processInstanceKey 流程key
     * @param businessKey        业务Id
     * @param assignee           受理人
     * @param acceptAreaCode     受理地区
     * @return taskId
     */
    @Transactional(isolation = Isolation.READ_COMMITTED)
//    public String startProcessInstanceByKeyAndAreaCode(String processInstanceKey, String businessKey, String assignee, String acceptAreaCode) {
    public String startProcessInstanceByKeyAndAreaCode(FlowTaskVo taskVo) {
        // 用流程定义的KEY启动，会自动选择KEY相同的流程定义中最新版本的那个(KEY为模型中的流程唯一标识)
        // taskVo.getProcessInstanceKey(), taskVo.getBusinessKey(), taskVo.getAssignee(), taskVo.getAcceptAreaCode()
        Map<String, Object> map = taskVo.getValues();
        String businessKey = taskVo.getBusinessKey();
        String assignee = taskVo.getAssignee();
        String processInstanceKey = taskVo.getProcessInstanceKey();
        if(map == null){
            map = new LinkedHashMap<>();
        }
        map.put("INITIATOR", assignee);// 启动后指定下级处理人为当前登录人
        map.put("_FLOWABLE_SKIP_EXPRESSION_ENABLED", true);
        map.put(ProcessConstants.ACCEPT_AREA_CODE, taskVo.getAcceptAreaCode());
        // 判断流程是否已启动，防止同一个 businessKey 启动多个流程
        long count = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).count();
        if (count > 0) {
            return null;
        }
        try {
            identityService.setAuthenticatedUserId(assignee);
            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processInstanceKey, businessKey, map);
            Task task = processEngine.getTaskService().createTaskQuery().processInstanceId(processInstance.getId()).orderByProcessInstanceId().desc().singleResult();
            return task.getId();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("当前流程未激活");
        }
    }

}
