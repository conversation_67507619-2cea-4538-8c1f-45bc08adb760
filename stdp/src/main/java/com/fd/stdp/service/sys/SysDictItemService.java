package com.fd.stdp.service.sys;

import java.util.List;

import com.fd.stdp.beans.sys.vo.TreeDropVo;
import com.fd.stdp.beans.sys.SysDictItem;
import com.fd.stdp.beans.sys.vo.SysDictItemVo;
import com.fd.stdp.beans.sys.vo.SysDictVo;
import com.github.pagehelper.PageInfo;

/**
 *@Description: 系统数据字典分类
 *@Author: linqiang
 *@Date: 2020-07-05 15:14:51
 */
public interface SysDictItemService {

    /**
     * @Description: 根据userId查询字典（新）
     * @param userId
     * @return List<BasicDictItem>
     * @Author: linqiang
     */
    List<SysDictItem> listItem(SysDictItem dictItem, boolean userType);

    /**
     * @Description: 系统数据字典分类（新）
     * @param basicDictItem
     * @return String * @throws
     * @Author: linqiang
     */
    void addBasicDictItem(SysDictItem basicDictItem, SysDictVo dictVo);

    /**
     * @Description: 更新系统数据字典（新）
     * @param basicDictItem
     * @return String * @throws
     * @Author: linqiang
     */
    String updateItemById(SysDictItem basicDictItem, boolean userId);

    /**
     * @Description: 删除系统数据字典分类（新）
     * @param id void 系统数据字典分类ID
     * @Author: linqiang
     */
    void deleteDictById(String id, boolean userType);

    /**
     * @Description: 根据字典code得到字典的值
     * @param code
     * @return BasicDictItem * @throws
     */
    SysDictItem findDictItem(String code);

    /**
     * @Description: 根据字典CODE得到下级结点列表一级
     * @param code
     * @return BasicDictItem * @throws
     */
    List<SysDictItem> findDictItemByDictType(String typeCode);

    //------以下是系统生成方法------
    /**
     *@Description: 保存或更新系统数据字典分类
     *@param basicDictItem 系统数据字典分类对象
     *@return String 系统数据字典分类ID
     *@Author: linqiang
     */
    String saveOrUpdateBasicDictItem(SysDictItem basicDictItem);

    /**
     *@Description: 查询系统数据字典分类详情
     *@param id
     *@return BasicDictItem
     *@Author: linqiang
     */
    SysDictItem findById(String id);

    /**
     *@Description: 分页查询系统数据字典分类
     *@param basicDictItemVo
     *@return PageInfo<BasicDictItem>
     *@Author: linqiang
     */
    PageInfo<SysDictItem> findPageByQuery(SysDictItemVo basicDictItemVo);

    /**
     * 得到字典 treeDrop
     * @param dictItem
     * @return
     */
	List<TreeDropVo> getTreeDrop(SysDictItem dictItem);

	/**
	 * 
	 * @param sysDictItem
	 * @return
	 */
	PageInfo<SysDictItem> findDictItemByCodes(SysDictItemVo sysDictItem);
}
