package com.fd.stdp.service.project.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractDevices;
import com.fd.stdp.beans.project.vo.ProjectContractDevicesVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectContractDevicesMapper;
import com.fd.stdp.service.project.ProjectContractDevicesService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 仪器设备
 *@Author: wangsh
 *@Date: 2022-01-12 14:18:55
 */
public class ProjectContractDevicesServiceImpl extends BaseServiceImpl<ProjectContractDevicesMapper, ProjectContractDevices> implements ProjectContractDevicesService{

	public static final Logger logger = LoggerFactory.getLogger(ProjectContractDevicesServiceImpl.class);
	
	@Autowired
	private ProjectContractDevicesMapper projectContractDevicesMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新仪器设备
	 *@param projectContractDevices 仪器设备对象
	 *@return String 仪器设备ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateProjectContractDevices(ProjectContractDevices projectContractDevices) {
		if(projectContractDevices==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(projectContractDevices.getId())){
			//新增
			projectContractDevices.setId(UUIDUtils.getUUID());
			projectContractDevicesMapper.insertSelective(projectContractDevices);
		}else{
			//避免页面传入修改
			projectContractDevices.setYn(null);
			projectContractDevicesMapper.updateByPrimaryKeySelective(projectContractDevices);
		}
		return projectContractDevices.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除仪器设备
	 *@param id void 仪器设备ID
	 *@Author: wangsh
	 */
	public void deleteProjectContractDevices(String id) {
		//TODO 做判断后方能执行删除
		ProjectContractDevices projectContractDevices=projectContractDevicesMapper.selectByPrimaryKey(id);
		if(projectContractDevices==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		ProjectContractDevices temprojectContractDevices=new ProjectContractDevices();
		temprojectContractDevices.setYn(CommonConstant.FLAG_NO);
		temprojectContractDevices.setId(projectContractDevices.getId());
		projectContractDevicesMapper.updateByPrimaryKeySelective(temprojectContractDevices);
	}

    /**
     * @Description: 批量删除仪器设备
     * @param projectContractDevicesVo
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiProjectContractDevices(ProjectContractDevicesVo projectContractDevicesVo) {
		projectContractDevicesVo.getIds().stream().forEach(id-> this.deleteProjectContractDevices(id));
	}

	@Override
	/**
	 *@Description: 查询仪器设备详情
	 *@param id
	 *@return ProjectContractDevices
	 *@Author: wangsh
	 */
	public ProjectContractDevices findById(String id) {
		return projectContractDevicesMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询仪器设备
	 *@param projectContractDevicesVo
	 *@return PageInfo<ProjectContractDevices>
	 *@Author: wangsh
	 */
	public PageInfo<ProjectContractDevices> findPageByQuery(ProjectContractDevicesVo projectContractDevicesVo) {
		PageHelper.startPage(projectContractDevicesVo.getPageNum(),projectContractDevicesVo.getPageSize());
		Example example=new Example(ProjectContractDevices.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(projectContractDevicesVo.getName())){
		//	criteria.andEqualTo(projectContractDevicesVo.getName());
		//}
		List<ProjectContractDevices> projectContractDevicesList=projectContractDevicesMapper.selectByExample(example);
		return new PageInfo<ProjectContractDevices>(projectContractDevicesList);
	}

}
