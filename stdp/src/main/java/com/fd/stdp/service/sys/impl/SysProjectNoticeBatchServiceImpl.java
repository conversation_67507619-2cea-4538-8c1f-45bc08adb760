package com.fd.stdp.service.sys.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.sys.SysProjectNoticeBatch;
import com.fd.stdp.beans.sys.vo.SysProjectNoticeBatchVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.sys.SysProjectNoticeBatchMapper;
import com.fd.stdp.service.sys.SysProjectNoticeBatchService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 公告项目Service业务层处理
 * @date 2021-11-15
 */
@Service
@Transactional(readOnly = true)
public class SysProjectNoticeBatchServiceImpl extends BaseServiceImpl<SysProjectNoticeBatchMapper, SysProjectNoticeBatch> implements SysProjectNoticeBatchService {

    private static final Logger logger = LoggerFactory.getLogger(SysProjectNoticeBatchServiceImpl.class);
    @Autowired
    private SysProjectNoticeBatchMapper sysProjectNoticeBatchMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新公告项目
     *@param sysProjectNoticeBatch 公告项目对象
     *@return String 公告项目ID
     *@Author: yujianfei
     */
    public String saveOrUpdateSysProjectNoticeBatch(SysProjectNoticeBatch sysProjectNoticeBatch) {
        if (sysProjectNoticeBatch == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(sysProjectNoticeBatch.getId())) {
            //新增
            sysProjectNoticeBatch.setId(UUIDUtils.getUUID());
            sysProjectNoticeBatchMapper.insertSelective(sysProjectNoticeBatch);
        } else {
            //避免页面传入修改
            sysProjectNoticeBatch.setYn(null);
            sysProjectNoticeBatchMapper.updateByPrimaryKeySelective(sysProjectNoticeBatch);
        }
        return sysProjectNoticeBatch.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除公告项目
     *@param id void 公告项目ID
     *@Author: yujianfei
     */
    public void deleteSysProjectNoticeBatch(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            SysProjectNoticeBatch sysProjectNoticeBatch = sysProjectNoticeBatchMapper.selectByPrimaryKey(id);
            if (sysProjectNoticeBatch == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            SysProjectNoticeBatch temsysProjectNoticeBatch = new SysProjectNoticeBatch();
            temsysProjectNoticeBatch.setYn(CommonConstant.FLAG_NO);
            temsysProjectNoticeBatch.setId(sysProjectNoticeBatch.getId());
            sysProjectNoticeBatchMapper.updateByPrimaryKeySelective(temsysProjectNoticeBatch);
        }
    }

    /**
     * @param id
     * @return SysProjectNoticeBatch
     * @Description: 查询公告项目详情
     * @Author: yujianfei
     */
    @Override
    public SysProjectNoticeBatch findById(String id) {
        return sysProjectNoticeBatchMapper.selectByPrimaryKey(id);
    }


    /**
     * @param sysProjectNoticeBatchVo
     * @return PageInfo<SysProjectNoticeBatch>
     * @Description: 分页查询公告项目
     * @Author: yujianfei
     */
    @Override
    public PageInfo<SysProjectNoticeBatch> findPageByQuery(SysProjectNoticeBatchVo sysProjectNoticeBatchVo) {
        PageHelper.startPage(sysProjectNoticeBatchVo.getPageNum(), sysProjectNoticeBatchVo.getPageSize());
        Example example = new Example(SysProjectNoticeBatch.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        if(!StringUtils.isEmpty(sysProjectNoticeBatchVo.getNoticeId())){
        	criteria.andEqualTo("noticeId",sysProjectNoticeBatchVo.getNoticeId());
        }
        List<SysProjectNoticeBatch> sysProjectNoticeBatchList = sysProjectNoticeBatchMapper.selectByExample(example);
        return new PageInfo<SysProjectNoticeBatch>(sysProjectNoticeBatchList);
    }
}
