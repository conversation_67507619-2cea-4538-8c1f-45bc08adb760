package com.fd.stdp.service.basic;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonExpertCommittee;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertCommitteeVo;
/**
 *@Description: 专家委员会库
 *@Author: wangsh
 *@Date: 2022-01-07 13:20:37
 */
public interface BasicPersonExpertCommitteeService {

	/**
	 *@Description: 保存或更新专家委员会库
	 *@param basicPersonExpertCommittee 专家委员会库对象
	 *@return String 专家委员会库ID
	 *@Author: wangsh
	 */
	String saveOrUpdateBasicPersonExpertCommittee(BasicPersonExpertCommittee basicPersonExpertCommittee);
	
	/**
	 *@Description: 删除专家委员会库
	 *@param id void 专家委员会库ID
	 *@Author: wangsh
	 */
	void deleteBasicPersonExpertCommittee(String id);

	/**
	 *@Description: 查询专家委员会库详情
	 *@param id
	 *@return BasicPersonExpertCommittee
	 *@Author: wangsh
	 */
	BasicPersonExpertCommittee findById(String id);

	/**
	 *@Description: 分页查询专家委员会库
	 *@param basicPersonExpertCommitteeVo
	 *@return PageInfo<BasicPersonExpertCommittee>
	 *@Author: wangsh
	 */
	PageInfo<BasicPersonExpertCommittee> findPageByQuery(BasicPersonExpertCommitteeVo basicPersonExpertCommitteeVo);

	/**
	 * 批量删除
     * @param vo
     */
    void deleteMultiBasicPersonExpertCommittee(List<String> vo);

	/**
	 * 推荐
	 * @param basicPersonExpertCommittee
	 * @return
	 */
	String submitBasicPersonExpertCommittee(BasicPersonExpertCommitteeVo basicPersonExpertCommittee);

	/**
	 * 审核
	 * @param basicPersonExpertCommittee
	 * @return
	 */
	String auditBasicPersonExpertCommittee(BasicPersonExpertCommitteeVo basicPersonExpertCommittee);

	/**
	 * 退回
	 * @param basicPersonExpertCommittee
	 * @return
	 */
	String sendBackBasicPersonExpertCommittee(BasicPersonExpertCommitteeVo basicPersonExpertCommittee);

	/**
	 * 拒绝
	 * @param basicPersonExpertCommittee
	 * @return
	 */
	String rejectBasicPersonExpertCommittee(BasicPersonExpertCommitteeVo basicPersonExpertCommittee);

    PageInfo<BasicPersonExpertCommittee> todoList(BasicPersonExpertCommitteeVo basicPersonExpertCommitteeVo);

	PageInfo<BasicPersonExpertCommittee> finishedList(BasicPersonExpertCommitteeVo basicPersonExpertCommitteeVo);

	/**
	 * 查询是否在推荐期间内
	 * @return
	 */
	Boolean inRecommendTime();
}
