package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectApplyCooperationUnit;
import com.fd.stdp.beans.project.vo.ProjectApplyCooperationUnitVo;

/**
 * 项目合作单位Service接口
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
public interface ProjectApplyCooperationUnitService {
    /**
     * @param projectApplyCooperationUnit 项目合作单位对象
     * @return String 项目合作单位ID
     * @Description: 保存或更新项目合作单位
     * @Author: yujianfei
     */
    String saveOrUpdateProjectApplyCooperationUnit(ProjectApplyCooperationUnit projectApplyCooperationUnit);

    /**
     * @param ids void 项目合作单位ID
     * @Description: 删除项目合作单位
     * @Author: yujianfei
     */
    void deleteProjectApplyCooperationUnit(List<String> ids);

    /**
     * @param id
     * @return ProjectApplyCooperationUnit
     * @Description: 查询项目合作单位详情
     * @Author: yujianfei
     */
    ProjectApplyCooperationUnit findById(String id);

    /**
     * @param projectApplyCooperationUnitVo
     * @return PageInfo<ProjectApplyCooperationUnit>
     * @Description: 分页查询项目合作单位
     * @Author: yujianfei
     */
    PageInfo<ProjectApplyCooperationUnit> findPageByQuery(ProjectApplyCooperationUnitVo projectApplyCooperationUnitVo);
}
