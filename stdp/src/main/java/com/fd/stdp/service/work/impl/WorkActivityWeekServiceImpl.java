package com.fd.stdp.service.work.impl;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.work.WorkActivityWeek;
import com.fd.stdp.beans.work.vo.WorkActivityWeekVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.work.WorkActivityWeekMapper;
import com.fd.stdp.service.work.WorkActivityWeekService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 科技活动周开展情况Service业务层处理
 * @date 2021-11-12
 */
@Service
@Transactional(readOnly = true)
public class WorkActivityWeekServiceImpl extends BaseServiceImpl<WorkActivityWeekMapper, WorkActivityWeek> implements WorkActivityWeekService {

    private static final Logger logger = LoggerFactory.getLogger(WorkActivityWeekServiceImpl.class);
    @Autowired
    private WorkActivityWeekMapper workActivityWeekMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新科技活动周开展情况
     *@param workActivityWeek 科技活动周开展情况对象
     *@return String 科技活动周开展情况ID
     *@Author: yujianfei
     */
    public String saveOrUpdateWorkActivityWeek(WorkActivityWeek workActivityWeek) {
        if (workActivityWeek == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(workActivityWeek.getId())) {
            //新增
            workActivityWeek.setId(UUIDUtils.getUUID());
            workActivityWeekMapper.insertSelective(workActivityWeek);
        } else {
            //避免页面传入修改
            workActivityWeek.setYn(null);
            workActivityWeekMapper.updateByPrimaryKeySelective(workActivityWeek);
        }
        return workActivityWeek.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除科技活动周开展情况
     *@param id void 科技活动周开展情况ID
     *@Author: yujianfei
     */
    public void deleteWorkActivityWeek(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            WorkActivityWeek workActivityWeek = workActivityWeekMapper.selectByPrimaryKey(id);
            if (workActivityWeek == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            WorkActivityWeek temworkActivityWeek = new WorkActivityWeek();
            temworkActivityWeek.setYn(CommonConstant.FLAG_NO);
            temworkActivityWeek.setId(workActivityWeek.getId());
            workActivityWeekMapper.updateByPrimaryKeySelective(temworkActivityWeek);
        }
    }

    /**
     * @param id
     * @return WorkActivityWeek
     * @Description: 查询科技活动周开展情况详情
     * @Author: yujianfei
     */
    @Override
    public WorkActivityWeek findById(String id) {
        return workActivityWeekMapper.selectByPrimaryKey(id);
    }


    /**
     * @param workActivityWeekVo
     * @return PageInfo<WorkActivityWeek>
     * @Description: 分页查询科技活动周开展情况
     * @Author: yujianfei
     */
    @Override
    public PageInfo<WorkActivityWeek> findPageByQuery(WorkActivityWeekVo workActivityWeekVo) {
        PageHelper.startPage(workActivityWeekVo.getPageNum(), workActivityWeekVo.getPageSize());
        Example example = new Example(WorkActivityWeek.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(workActivityWeekVo.getName())){
        //	criteria.andEqualTo(workActivityWeekVo.getName());
        //}
        List<WorkActivityWeek> workActivityWeekList = workActivityWeekMapper.selectByExample(example);
        return new PageInfo<WorkActivityWeek>(workActivityWeekList);
    }

    @Override
    public PageInfo<WorkActivityWeekVo> findStatisticByQuery(WorkActivityWeekVo workActivityWeekVo) {
        // PageHelper.startPage(workActivityWeekVo.getPageNum(), workActivityWeekVo.getPageSize());
        // 统计市县的
        List<WorkActivityWeekVo> workActivityWeekList = workActivityWeekMapper.findStatisticByQuery(workActivityWeekVo);
        // 统计直属单位的
        List<WorkActivityWeekVo> workActivityWeekList2 = workActivityWeekMapper.findStatisticByQueryOrg(workActivityWeekVo);
        workActivityWeekList.addAll(workActivityWeekList2);

        WorkActivityWeekVo totalVo = new WorkActivityWeekVo();
        totalVo.setSorgName("合计");
        Field[] fileds = WorkActivityWeekVo.class.getSuperclass().getDeclaredFields();
        for (Field field:fileds) {
            try {
                if(field.getType() == Integer.class) {
                    field.setAccessible(true);
                    field.set(totalVo, 0);
                } else if(field.getType() == BigDecimal.class) {
                    field.setAccessible(true);
                    field.set(totalVo, new BigDecimal(0));
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }

        workActivityWeekList.forEach(week->{
            for (Field field:fileds) {
                try {
                    if(null != field.get(week)) {
                        if (field.getType() == Integer.class) {
                            field.setAccessible(true);
                            field.set(totalVo, (Integer) field.get(totalVo) + (Integer) field.get(week));
                        } else if (field.getType() == BigDecimal.class) {
                            field.setAccessible(true);
                            field.set(totalVo, ((BigDecimal) field.get(totalVo)).add((BigDecimal) field.get(week)));
                        }
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        });
        workActivityWeekList.add(totalVo);
        return new PageInfo<WorkActivityWeekVo>(workActivityWeekList);
    }
}
