package com.fd.stdp.service.sys.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.fd.stdp.beans.sys.AreaTree;
import com.fd.stdp.beans.sys.BasicAreacode;
import com.fd.stdp.beans.sys.vo.AreaCodeVo;
import com.fd.stdp.beans.sys.vo.BasicAreacodeVo;
import com.fd.stdp.beans.sys.vo.TreeNode;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.redis.RedisUtil;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.constant.RedisConstant;
import com.fd.stdp.dao.sys.BasicAreacodeMapper;
import com.fd.stdp.service.sys.BasicAreacodeService;
import com.fd.stdp.util.UUIDUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * @Description: 行政区划信息
 * @Author: linqiang
 * @Date: 2020-07-07 19:11:41
 */
@Service
@Transactional(readOnly = true)
public class BasicAreacodeServiceImpl extends BaseServiceImpl<BasicAreacodeMapper, BasicAreacode>
		implements BasicAreacodeService {

	public static final Logger logger = LoggerFactory.getLogger(BasicAreacodeServiceImpl.class);

	@Autowired
	private BasicAreacodeMapper basicAreacodeMapper;
	@Autowired
	private RedisUtil redisUtil;
//
//	/**
//	 * @Description: 保存或更新行政区划信息
//	 * @param basicAreacode 行政区划信息对象
//	 * @return String 行政区划信息ID
//	 * @Author: linqiang
//	 */
//	@Override
//	@Transactional(readOnly = false)
//	public String saveOrUpdateBasicAreacode(BasicAreacode basicAreacode) {
//		if (basicAreacode == null) {
//			throw new ServiceException("数据异常");
//		}
//		if (StringUtils.isEmpty(basicAreacode.getId())) {
//			// 新增
//			basicAreacode.setId(UUIDUtils.getUUID());
//			basicAreacodeMapper.insertSelective(basicAreacode);
//		} else {
//			// 避免页面传入修改
//			basicAreacode.setYn(null);
//			basicAreacodeMapper.updateByPrimaryKeySelective(basicAreacode);
//		}
//		return basicAreacode.getId();
//	}
//
//	/**
//	 * @Description: 删除行政区划信息
//	 * @param id void 行政区划信息ID
//	 * @Author: linqiang
//	 */
//	@Override
//	@Transactional(readOnly = false)
//	public void deleteBasicAreacode(String id) {
//		// TODO 做判断后方能执行删除
//		BasicAreacode basicAreacode = basicAreacodeMapper.selectByPrimaryKey(id);
//		if (basicAreacode == null) {
//			throw new ServiceException("非法请求");
//		}
//		// 逻辑删除
//		BasicAreacode tembasicAreacode = new BasicAreacode();
//		tembasicAreacode.setYn(CommonConstant.FLAG_NO);
//		tembasicAreacode.setId(basicAreacode.getId());
//		basicAreacodeMapper.updateByPrimaryKeySelective(tembasicAreacode);
//	}
//
//	/**
//	 * @Description: 批量删除行政区划信息
//	 * @param ids void 行政区划信息ID
//	 * @Author: linqiang
//	 */
//	@Override
//	@Transactional(readOnly = false)
//	public void deleteBatchAreacode(List<String> ids) {
//		if (CollectionUtils.isEmpty(ids)) {
//			throw new ServiceException("非法请求");
//		}
//		BasicAreacode basicAreacode = null;
//		BasicAreacode tembasicAreacode = null;
//		for (String id : ids) {
//			// TODO 做判断后方能执行删除
//			basicAreacode = basicAreacodeMapper.selectByPrimaryKey(id);
//			if (basicAreacode == null) {
//				throw new ServiceException("非法请求");
//			}
//			// 逻辑删除
//			tembasicAreacode = new BasicAreacode();
//			tembasicAreacode.setYn(CommonConstant.FLAG_NO);
//			tembasicAreacode.setId(basicAreacode.getId());
//			basicAreacodeMapper.updateByPrimaryKeySelective(tembasicAreacode);
//		}
//	}
//
//	/**
//	 * @Description: 查询行政区划信息详情
//	 * @param id
//	 * @return BasicAreacode
//	 * @Author: linqiang
//	 */
//	@Override
//	public BasicAreacode findById(String id) {
//		return basicAreacodeMapper.selectByPrimaryKey(id);
//	}
//
//	/**
//	 * @Description: 分页查询行政区划信息
//	 * @param basicAreacodeVo
//	 * @return PageInfo<BasicAreacode>
//	 * @Author: linqiang
//	 */
//	@Override
//	public PageInfo<BasicAreacode> findPageByQuery(BasicAreacodeVo basicAreacodeVo) {
//		PageHelper.startPage(basicAreacodeVo.getPageNum(), basicAreacodeVo.getPageSize());
//		Example example = new Example(BasicAreacode.class);
//		Criteria criteria = example.createCriteria();
//		criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
//		// 查询条件
//		if (!StringUtils.isEmpty(basicAreacodeVo.getAreaCode())) {
//			criteria.andLike("areaCode", "%" + basicAreacodeVo.getAreaCode() + "%");
//		}
//		if (!StringUtils.isEmpty(basicAreacodeVo.getAreaName())) {
//			criteria.andLike("areaName", "%" + basicAreacodeVo.getAreaName() + "%");
//		}
//		if (StringUtils.isEmpty(basicAreacodeVo.getSortField())) {
//			example.orderBy("areaCode").asc();
//		} else {
//			if (!StringUtils.isEmpty(basicAreacodeVo.getSortType())) {
//				example.setOrderByClause(basicAreacodeVo.getSortField() + " " + basicAreacodeVo.getSortType());
//			}
//		}
//		List<BasicAreacode> basicAreacodeList = basicAreacodeMapper.selectByExample(example);
//		return new PageInfo<BasicAreacode>(basicAreacodeList);
//	}

	/**
	 * 
	 * @Description 根据省Code初始化行政区划树
	 * @param name
	 * @return List<DictTree>
	 */
	@Override
	public List<AreaTree> getTree(String code) {
		if (StringUtils.isEmpty(code)) {
			throw new ServiceException("数据异常");
		}
		String root = code;
		if (code.substring(2, 6).equals("0000")) {
			code = code.substring(0, 2);
		} else if (code.substring(4, 6).equals("00")) { // 市
			code = code.substring(0, 4);
		}
		List<AreaTree> tree = basicAreacodeMapper.findAllAreabyProvince(code);
		// 生成树
		List<AreaTree> dictTree = getDictTree(tree, root);
		return dictTree;
	}

	private List<AreaTree> getDictTree(List<AreaTree> areaList, String root) {
		if (StringUtils.isEmpty(root)) {
			root = "-1";
		}
		List<AreaTree> trees = new ArrayList<AreaTree>();
		for (AreaTree treeNode : areaList) {
			if (root.equals(treeNode.getParentCode())) {
				trees.add(treeNode);
			}
			for (AreaTree it : areaList) {
				if (it.getParentCode().equals(treeNode.getCode())) {
					if (treeNode.getChildren() == null) {
						treeNode.setChildren(new ArrayList<TreeNode>());
					}
					treeNode.add(it);
				}
			}
		}
		return trees;
	}

	/**
	 * @Description:根据区划CODE得到下级下拉
	 * @param code
	 * @param level
	 * @return List<Map<String,String>> * @throws
	 * @linqiang
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<Map<String, String>> findLevelByCode(String code, String level) {
		if (StringUtils.isEmpty(code)) {
			throw new ServiceException("非法请求");
		}
		if (!findAll(code, level)) {
			return null;
		}
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		switch (level) {
		case "city":
			list = (List<Map<String, String>>) redisUtil.get(RedisConstant.SYS_ARECODE_CITY_CATCH + "_" + code);
			break;
		case "county":
			list = (List<Map<String, String>>) redisUtil.get(RedisConstant.SYS_ARECODE_COUNTY_CATCH + "_" + code);
			break;
		default:
			break;
		}
		return list;
	}

	/**
	 * 查询XXX省下的市；查询XXX市下的区/县
	 */
	@SuppressWarnings("unchecked")
	// @Override
	public boolean findAll(String code, String level) {
		List<Map<String, String>> areaList = new ArrayList<Map<String, String>>();
		switch (level) {
		case "city":
			areaList = (List<Map<String, String>>) redisUtil.get(RedisConstant.SYS_ARECODE_CITY_CATCH + "_" + code);
			if (CollectionUtils.isEmpty(areaList)) {
				areaList = basicAreacodeMapper.findAllCity(code);
				redisUtil.set(RedisConstant.SYS_ARECODE_CITY_CATCH + "_" + code, areaList,
						RedisConstant.REDIS_EXPIRE_ONE_DAY);
			}
			break;
		case "county":
			areaList = (List<Map<String, String>>) redisUtil.get(RedisConstant.SYS_ARECODE_COUNTY_CATCH + "_" + code);
			if (CollectionUtils.isEmpty(areaList)) {
				areaList = basicAreacodeMapper.findAllArea(code);
				redisUtil.set(RedisConstant.SYS_ARECODE_COUNTY_CATCH + "_" + code, areaList,
						RedisConstant.REDIS_EXPIRE_ONE_DAY);
			}
			break;
		default:
			break;
		}
		if (CollectionUtils.isEmpty(areaList)) {
			return false;
		}
		return true;
	}

	/**
	 * 查找所有省行政区划
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<Map<String, String>> findAllProvice() {
		List<Map<String, String>> provinceList = (List<Map<String, String>>) redisUtil
				.get(RedisConstant.SYS_ARECODE_PROVINCE_CATCH);
		if (CollectionUtils.isEmpty(provinceList)) {
			provinceList = basicAreacodeMapper.findAllProvince();
			redisUtil.set(RedisConstant.SYS_ARECODE_PROVINCE_CATCH, provinceList, RedisConstant.REDIS_EXPIRE_ONE_DAY);
		}
		return null;
	}

	/**
	 * @Description 根据区划CODE得到NAME
	 * @param code
	 * @return String * @throws
	 * <AUTHOR>
	 */
	@Override
	public String findNameByCode(String code) {
		if (StringUtils.isEmpty(code)) {
			throw new ServiceException("非法请求");
		}
		String areaName = "";
		Example example = null;
		// 省
		if (code.substring(2, 6).equals("0000")) {
			example = new Example(BasicAreacode.class);
			example.createCriteria().andEqualTo("areaCode", code);
			List<BasicAreacode> areaCodeList = this.selectByExample(example);
			return areaCodeList.get(0).getAreaName();
		} else if (code.substring(4, 6).equals("00")) { // 市
			example = new Example(BasicAreacode.class);
			example.createCriteria().andEqualTo("areaCode", code);
			List<BasicAreacode> areaCodeList = this.selectByExample(example);
			return areaCodeList.get(0).getAreaName();
		} else {
			example = new Example(BasicAreacode.class);
			example.createCriteria().andEqualTo("areaCode", code);
			List<BasicAreacode> areaCodeList1 = this.selectByExample(example);
			areaName += areaCodeList1.get(0).getAreaName();
			return areaName;
		}
	}

	/**
	 * @Description 根据区划CODE得到NAME
	 * @param code
	 * @return String * @throws
	 * <AUTHOR>
	 */
	@Override
	@SuppressWarnings("unchecked")
	public List<AreaCodeVo> listArea(String code) {
		List<AreaCodeVo> listAreaCode = null;
		// 省
		if (!StringUtils.isEmpty(code)) {
			if (code.substring(2, 6).equals("0000")) {
				listAreaCode = (List<AreaCodeVo>) redisUtil
						.get(RedisConstant.SYS_ARECODE_PROVINCE_CATCH + "_" + code.substring(0, 2));
				if (CollectionUtils.isEmpty(listAreaCode)) {
					listAreaCode = basicAreacodeMapper.listArea(code.substring(0, 2));
					redisUtil.set(RedisConstant.SYS_ARECODE_PROVINCE_CATCH + "_" + code.substring(0, 2), listAreaCode,
							RedisConstant.REDIS_EXPIRE_ONE_DAY);
				}
			} else if (code.substring(4, 6).equals("00")) { // 市
				listAreaCode = (List<AreaCodeVo>) redisUtil
						.get(RedisConstant.SYS_ARECODE_CITY_CATCH + "_" + code.substring(0, 4));
				if (CollectionUtils.isEmpty(listAreaCode)) {
					listAreaCode = basicAreacodeMapper.listArea(code.substring(0, 4));
					redisUtil.set(RedisConstant.SYS_ARECODE_CITY_CATCH + "_" + code.substring(0, 4), listAreaCode,
							RedisConstant.REDIS_EXPIRE_ONE_DAY);
				}

			}
		} else {// 全国行政区划列表
			listAreaCode = (List<AreaCodeVo>) redisUtil.get(RedisConstant.SYS_ARECODE_COUNTRY);
			if (CollectionUtils.isEmpty(listAreaCode)) {
				listAreaCode = basicAreacodeMapper.listArea(null);
				redisUtil.set(RedisConstant.SYS_ARECODE_COUNTRY, listAreaCode, RedisConstant.REDIS_EXPIRE_ONE_DAY);
			}

		}
		return listAreaCode;
	}

	@Override
	public List<BasicAreacode> findAreaByCode(String code) {
		if (StringUtils.isEmpty(code)) {
			throw new ServiceException("数据异常，请联系管理员");
		}
		List<BasicAreacode> areaList = new ArrayList<BasicAreacode>();
		Example example = new Example(BasicAreacode.class);
		example.createCriteria().andEqualTo("parentCode", code);
		example.orderBy("areaCode").asc();
		areaList = basicAreacodeMapper.selectByExample(example);
		return areaList;
	}

	@Override
	public List<BasicAreacode> getNamesByCode(String code) {
		if (StringUtils.isEmpty(code)) {
			throw new ServiceException("数据异常，请联系管理员");
		}
		List<BasicAreacode> areaList = new ArrayList<BasicAreacode>();
		if (code.substring(2, 6).equals("0000")) {
			setAreaList(code, areaList);
		} else if (code.substring(4, 6).equals("00")) { // 市
			setAreaList(code.substring(0, 2) + "0000", areaList);
			setAreaList(code, areaList);
		} else {
			setAreaList(code.substring(0, 2) + "0000", areaList);
			setAreaList(code.substring(0, 4) + "00", areaList);
			setAreaList(code, areaList);
		}

		return areaList;
	}

	@Override
	public List<BasicAreacode> getProvince() {
		return basicAreacodeMapper.getProvince();
	}

	private void setAreaList(String code, List<BasicAreacode> areaList) {
		BasicAreacode area = new BasicAreacode();
		area.setAreaCode(code);
		area.setAreaName(findNameByCode(code));
		areaList.add(area);
	}

}
