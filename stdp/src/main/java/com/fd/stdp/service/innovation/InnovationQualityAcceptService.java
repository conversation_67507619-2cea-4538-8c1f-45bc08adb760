package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityAcceptApply;
import com.fd.stdp.beans.innovation.vo.InnovationQualityAcceptApplyVo;
/**
 *@Description: 省质检中心验收申请
 *@Author: wangsh
 *@Date: 2022-02-11 16:02:11
 */
public interface InnovationQualityAcceptService {

	/**
	 *@Description: 保存或更新省质检中心验收申请
	 *@param innovationQualityAcceptApply 省质检中心验收申请对象
	 *@return String 省质检中心验收申请ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationQualityAcceptApply(InnovationQualityAcceptApplyVo innovationQualityAcceptApply);
	
	/**
	 *@Description: 删除省质检中心验收申请
	 *@param id void 省质检中心验收申请ID
	 *@Author: wangsh
	 */
	void deleteInnovationQualityAcceptApply(String id);

	/**
	 * @Description: 批量删除省质检中心验收申请
	 * @param ids
	 */
    void deleteMultiInnovationQualityAcceptApply(List<String> ids);

	/**
	 *@Description: 查询省质检中心验收申请详情
	 *@param id
	 *@return InnovationQualityAcceptApply
	 *@Author: wangsh
	 */
	InnovationQualityAcceptApply findById(String id);

	/**
	 *@Description: 分页查询省质检中心验收申请
	 *@param innovationQualityAcceptApplyVo
	 *@return PageInfo<InnovationQualityAcceptApply>
	 *@Author: wangsh
	 */
	PageInfo<InnovationQualityAcceptApply> findPageByQuery(InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo);
	
	
	/**
	 * 提交
	 * @param innovationQualityAcceptApplyVo
	 * @return
	 */
    String submitInnovationQualityAcceptApply(InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo);

	/**
	 * 审核
	 * @param innovationQualityAcceptApplyVo
	 * @return
	 */
	String auditInnovationQualityAcceptApply(InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo);

	/**
	 * 退回
	 * @param innovationQualityAcceptApplyVo
	 * @return
	 */
	String sendBackInnovationQualityAcceptApply(InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo);

	/**
	 * 任务书下达
	 * @param innovationQualityAcceptApplyVo
	 * @return
	 */
	String releaseInnovationQualityAcceptApply(InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo);

	/**
	 * 待办列表
	 */
	PageInfo<InnovationQualityAcceptApply> todoList(InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationQualityAcceptApply> finishedList(InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationQualityAcceptApply> endList(InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo);

	/**
	 * 专家提交
	 * @param vo
	 * @return
	 */
    String expertSubmitInnovationQualityAcceptApply(InnovationQualityAcceptApplyVo vo);
}
