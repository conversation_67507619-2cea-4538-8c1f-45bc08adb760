package com.fd.stdp.service.sys;

import java.util.List;
import java.util.Map;

import com.fd.stdp.beans.sys.AreaTree;
import com.fd.stdp.beans.sys.BasicAreacode;
import com.fd.stdp.beans.sys.vo.AreaCodeVo;
import com.fd.stdp.beans.sys.vo.BasicAreacodeVo;
import com.github.pagehelper.PageInfo;

/**
 * @Description: 行政区划信息
 * @Author: linqiang
 * @Date: 2020-07-07 19:11:41
 */
public interface BasicAreacodeService {
//
//	/**
//	 * @Description: 保存或更新行政区划信息
//	 * @param basicAreacode 行政区划信息对象
//	 * @return String 行政区划信息ID
//	 * @Author: linqiang
//	 */
//	String saveOrUpdateBasicAreacode(BasicAreacode basicAreacode);
//
//	/**
//	 * @Description: 删除行政区划信息
//	 * @param id void 行政区划信息ID
//	 * @Author: linqiang
//	 */
//	void deleteBasicAreacode(String id);
//
//	/**
//	 * @Description: 批量删除行政区划信息
//	 * @param ids void 行政区划信息ID
//	 * @Author: linqiang
//	 */
//	void deleteBatchAreacode(List<String> ids);
//
//	/**
//	 * @Description: 查询行政区划信息详情
//	 * @param id
//	 * @return BasicAreacode
//	 * @Author: linqiang
//	 */
//	BasicAreacode findById(String id);
//
//	/**
//	 * @Description: 分页查询行政区划信息
//	 * @param basicAreacodeVo
//	 * @return PageInfo<BasicAreacode>
//	 * @Author: linqiang
//	 */
//	PageInfo<BasicAreacode> findPageByQuery(BasicAreacodeVo basicAreacodeVo);

	/**
	 * @Description: 行政区划树信息
	 * @param basicAreacodeVo
	 * @return PageInfo<BasicAreacode>
	 * @Author: linqiang
	 */
	List<AreaTree> getTree(String code);

	/**
	 * @Description:根据区划CODE得到下级下拉
	 * @param code
	 * @param level
	 * @return List<Map<String,String>> * @throws
	 * @linqiang
	 */
	List<Map<String, String>> findLevelByCode(String code, String level);

	/**
	 * 查找所有省行政区划
	 */
	List<Map<String, String>> findAllProvice();

	/**
	 * @Description 根据区划CODE得到NAME
	 * @param code
	 * @return String * @throws
	 * <AUTHOR>
	 */
	String findNameByCode(String code);

	/**
	 * @Description 根据区划CODE得到下级下拉带当前的行政区划
	 * @param code
	 * @return List<Map<String,String>> * @throws
	 * <AUTHOR>
	 */
	List<BasicAreacode> findAreaByCode(String code);

	List<AreaCodeVo> listArea(String code);

	List<BasicAreacode> getNamesByCode(String code);

	List<BasicAreacode> getProvince();
}
