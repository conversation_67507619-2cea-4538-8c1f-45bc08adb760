package com.fd.stdp.service.innovation.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import com.fd.stdp.beans.innovation.InnovationQualityAcceptDevice;
import com.fd.stdp.beans.innovation.InnovationQualityContractProduct;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.dao.basic.BasicGradeLinkedMapper;
import com.fd.stdp.dao.innovation.InnovationQualityAcceptDeviceMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.basic.BasicGradeLinkedService;
import com.fd.stdp.service.flowable.FlowCommonService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityAcceptApply;
import com.fd.stdp.beans.innovation.vo.InnovationQualityAcceptApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationQualityAcceptApplyMapper;
import com.fd.stdp.service.innovation.InnovationQualityAcceptService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.fd.stdp.common.BaseController.getCurrentUserId;
import static com.fd.stdp.common.BaseController.getCurrentUserName;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 省质检中心验收申请
 *@Author: wangsh
 *@Date: 2022-02-11 16:02:11
 */
public class InnovationQualityAcceptServiceImpl extends BaseServiceImpl<InnovationQualityAcceptApplyMapper, InnovationQualityAcceptApply> implements InnovationQualityAcceptService {

	public static final Logger logger = LoggerFactory.getLogger(InnovationQualityAcceptServiceImpl.class);
	
	@Autowired
	private InnovationQualityAcceptApplyMapper innovationQualityAcceptApplyMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private InnovationQualityAcceptDeviceMapper innovationQualityAcceptDeviceMapper;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	@Autowired
	private BasicGradeLinkedService basicGradeLinkedService;
	@Autowired
	private BasicGradeLinkedMapper basicGradeLinkedMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新省质检中心验收申请
	 *@param innovationQualityAcceptApply 省质检中心验收申请对象
	 *@return String 省质检中心验收申请ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationQualityAcceptApply(InnovationQualityAcceptApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationQualityAcceptApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationQualityAcceptApplyMapper.updateByPrimaryKeySelective(vo);
		}

		if(!CollectionUtils.isEmpty(vo.getDeviceList())){
			int sort = 1;
			for (InnovationQualityAcceptDevice d:vo.getDeviceList()
				 ) {
				d.setSort(sort++);
			}
			updateList(vo, vo.getDeviceList(), innovationQualityAcceptDeviceMapper,"setApplyId");
		}
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除省质检中心验收申请
	 *@param id void 省质检中心验收申请ID
	 *@Author: wangsh
	 */
	public void deleteInnovationQualityAcceptApply(String id) {
		//TODO 做判断后方能执行删除
		InnovationQualityAcceptApply innovationQualityAcceptApply=innovationQualityAcceptApplyMapper.selectByPrimaryKey(id);
		if(innovationQualityAcceptApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationQualityAcceptApply teminnovationQualityAcceptApply=new InnovationQualityAcceptApply();
		teminnovationQualityAcceptApply.setYn(CommonConstant.FLAG_NO);
		teminnovationQualityAcceptApply.setId(innovationQualityAcceptApply.getId());
		innovationQualityAcceptApplyMapper.updateByPrimaryKeySelective(teminnovationQualityAcceptApply);
	}

    /**
     * @Description: 批量删除省质检中心验收申请
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationQualityAcceptApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationQualityAcceptApply(id));
	}

	@Override
	/**
	 *@Description: 查询省质检中心验收申请详情
	 *@param id
	 *@return InnovationQualityAcceptApply
	 *@Author: wangsh
	 */
	public InnovationQualityAcceptApply findById(String id) {
		InnovationQualityAcceptApply innovationQualityAcceptApply = innovationQualityAcceptApplyMapper.selectByPrimaryKey(id);
		InnovationQualityAcceptApplyVo vo = new InnovationQualityAcceptApplyVo();
		BeanUtils.copyProperties(innovationQualityAcceptApply, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		Example example = new Example(InnovationQualityAcceptDevice.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
		example.orderBy("sort");
		vo.setDeviceList(innovationQualityAcceptDeviceMapper.selectByExample(example));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		vo.setGradeExperts(basicGradeLinkedService.findByFormId(id));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询省质检中心验收申请
	 *@param innovationQualityAcceptApplyVo
	 *@return PageInfo<InnovationQualityAcceptApply>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationQualityAcceptApply> findPageByQuery(InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo) {
		PageHelper.startPage(innovationQualityAcceptApplyVo.getPageNum(),innovationQualityAcceptApplyVo.getPageSize());
		Example example=new Example(InnovationQualityAcceptApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationQualityAcceptApplyVo.getName())){
		//	criteria.andEqualTo(innovationQualityAcceptApplyVo.getName());
		//}
		List<InnovationQualityAcceptApply> innovationQualityAcceptApplyList=innovationQualityAcceptApplyMapper.selectByExample(example);
		return new PageInfo<InnovationQualityAcceptApply>(innovationQualityAcceptApplyList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationQualityAcceptApply(InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo) {
		String id = this.saveOrUpdateInnovationQualityAcceptApply(innovationQualityAcceptApplyVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_QUALITY_ACCEPTAPPLY, innovationQualityAcceptApplyVo, this.mapper,
				StringUtils.isNotBlank(innovationQualityAcceptApplyVo.getAuditAdvice())?innovationQualityAcceptApplyVo.getAuditAdvice():"提交省质检中心验收申请");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationQualityAcceptApply(InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo) {
		InnovationQualityAcceptApply old = this.findById(innovationQualityAcceptApplyVo.getId());
		if(StringUtils.equals(old.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())){
			if(CollectionUtils.isEmpty(innovationQualityAcceptApplyVo.getGradeExperts())){
				throw new ServiceException("请选择专家");
			}
			innovationQualityAcceptApplyVo.getGradeExperts().forEach(ge->{
				ge.setResultType(null);
				ge.setOpinion(null);
				ge.setGradeOption(null);
			});
			basicGradeLinkedService.clearAndUpdateBasicGradeLinked(old.getId(), innovationQualityAcceptApplyVo.getGradeExperts());
		}
		flowCommonService.doFlowStepAudit(innovationQualityAcceptApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityAcceptApplyVo.getAuditAdvice()) ? innovationQualityAcceptApplyVo.getAuditAdvice() : "省质检中心验收申请审核通过"
				, FlowStatusEnum.EXPERTS_GRADE.getCode(), FlowStatusEnum.EXPERTS_GRADE.getRole());
		return innovationQualityAcceptApplyVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationQualityAcceptApply(InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo) {
		flowCommonService.doFlowStepSendBack(innovationQualityAcceptApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityAcceptApplyVo.getAuditAdvice())?innovationQualityAcceptApplyVo.getAuditAdvice():"省质检中心验收申请退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationQualityAcceptApply(InnovationQualityAcceptApplyVo innovationQualityAcceptApplyVo) {
		if(StringUtils.equals("0", innovationQualityAcceptApplyVo.getIsPass())){
			Map<String, Object> map = new HashMap<>();
			map.put("ISPASS", 0);
			flowCommonService.doCompleteTask(innovationQualityAcceptApplyVo, this.mapper
					, "省质检中心验收申请审核未通过"
					, FlowStatusEnum.WAIT_APPLY.getCode(), AssigneeConstant.ORG_HEAD_ROLE, null, map);
		} else if(StringUtils.equals("1", innovationQualityAcceptApplyVo.getIsPass())) {
			flowCommonService.doCompleteTask(innovationQualityAcceptApplyVo, this.mapper
					, "省质检中心验收申请审核通过"
					, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		}

		return null;
	}


	@Override
	public PageInfo<InnovationQualityAcceptApply> todoList(InnovationQualityAcceptApplyVo vo) {

		Example example = new Example(InnovationQualityAcceptApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationQualityAcceptApply> finishedList(InnovationQualityAcceptApplyVo vo) {
		Example example = new Example(InnovationQualityAcceptApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationQualityAcceptApply> endList(InnovationQualityAcceptApplyVo vo) {
		Example example = new Example(InnovationQualityAcceptApply.class);
		Criteria criteria = getCriteria(vo, example);
		criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		example.orderBy("createTime").desc();
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}

	@Override
	@Transactional(readOnly = false)
	public String expertSubmitInnovationQualityAcceptApply(InnovationQualityAcceptApplyVo vo) {
		String userId = getCurrentUserId();
		Boolean nextStep = false;
		vo.getGradeExperts().stream().filter(grade->StringUtils.equals(userId, grade.getUserId()))
				.forEach(grade->{
					basicGradeLinkedMapper.updateByPrimaryKeySelective(grade);
				});
		List<BasicGradeLinkedVo> linkeds = basicGradeLinkedService.findByFormId(vo.getId());
		boolean isLeader = false;
		for (BasicGradeLinkedVo grade:linkeds) {
			if (StringUtils.equals(userId, grade.getUserId())) {
				isLeader = StringUtils.equals("组长", grade.getUserType());
				break;
			}
		}
		if(basicGradeLinkedService.formGradeAllFinished(vo.getId())) {
			nextStep = isLeader;
		} else {
			if(isLeader) {
				throw new ServiceException("组长请等待组员全部提交后最终确认提交");
			}
		}
		if(nextStep){
			flowCommonService.doCompleteTask(vo, this.mapper, "全部专家评审完成"
					, FlowStatusEnum.PROJECT_RELEASE.getCode(), FlowStatusEnum.PROJECT_RELEASE.getRole(), getCurrentUserName());
		}
		return null;
	}

	private Criteria getCriteria(InnovationQualityAcceptApply vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getApplyCenterName())){
			criteria.andLike("applyCenterName", "%" + vo.getApplyCenterName()+ "%");
		}
		return criteria;
	}
}
