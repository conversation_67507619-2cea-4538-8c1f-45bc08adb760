package com.fd.stdp.service.gen;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.gen.GenTable;
import com.fd.stdp.beans.gen.vo.GenTableVo;
/**
 *@Description: 代码生成业务表
 *@Author: linqiang
 *@Date: 2021-10-15 09:59:19
 */
public interface GenTableService {

	/**
	 *@Description: 保存或更新代码生成业务表
	 *@param genTable 代码生成业务表对象
	 *@return String 代码生成业务表ID
	 *@Author: linqiang
	 */
	String saveOrUpdateGenTable(GenTable genTable);
	
	/**
	 *@Description: 删除代码生成业务表
	 *@param id void 代码生成业务表ID
	 *@Author: linqiang
	 */
	void deleteGenTable(String id);

	/**
	 *@Description: 查询代码生成业务表详情
	 *@param id
	 *@return GenTable
	 *@Author: linqiang
	 */
	GenTable findById(String id);

	/**
	 *@Description: 分页查询代码生成业务表
	 *@param genTableVo
	 *@return PageInfo<GenTable>
	 *@Author: linqiang
	 */
	PageInfo<GenTable> findPageByQuery(GenTableVo genTableVo);
	
	
	/**
     * 查询据库列表
     * 
     * @param genTable 业务信息
     * @return 数据库表集合
     */
    PageInfo<GenTable> findDbTableList(GenTableVo genTable);
	
	/**
     * 查询据库列表
     * 
     * @param tableNames 表名称组
     * @return 数据库表集合
     */
    List<GenTable> findDbTableListByNames(String[] tableNames);
	/**
     * 导入表结构
     * 
     * @param tableList 导入表列表
     */
    void importGenTable(List<GenTable> tableList,String operName);
	
	 /**
     * 预览代码
     * 
     * @param tableId 表编号
     * @return 预览数据列表
     */
    Map<String, String> previewCode(String tableId);

    /**
     * 生成代码（下载方式）
     * 
     * @param tableName 表名称
     * @return 数据
     */
    byte[] downloadCode(String tableName);

    /**
     * 生成代码（自定义路径）
     * 
     * @param tableName 表名称
     * @return 数据
     */
    void generatorCode(String tableName);

    /**
     * 同步数据库
     * 
     * @param tableName 表名称
     */
    void synchDb(String tableName);

    /**
     * 批量生成代码（下载方式）
     * 
     * @param tableNames 表数组
     * @return 数据
     */
    byte[] downloadCode(String[] tableNames);
	
	 /**
     * 修改保存参数校验
     * 
     * @param genTable 业务信息
     */
    void validateEdit(GenTableVo genTable);

	List<GenTable> findAll();

	void deleteGenTableByIds(String[] tableIds);

}
