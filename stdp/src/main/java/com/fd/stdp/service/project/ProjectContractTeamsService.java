package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractTeams;
import com.fd.stdp.beans.project.vo.ProjectContractTeamsVo;
/**
 *@Description: 项目组成员
 *@Author: wangsh
 *@Date: 2022-01-12 14:18:27
 */
public interface ProjectContractTeamsService {

	/**
	 *@Description: 保存或更新项目组成员
	 *@param projectContractTeams 项目组成员对象
	 *@return String 项目组成员ID
	 *@Author: wangsh
	 */
	String saveOrUpdateProjectContractTeams(ProjectContractTeams projectContractTeams);
	
	/**
	 *@Description: 删除项目组成员
	 *@param id void 项目组成员ID
	 *@Author: wangsh
	 */
	void deleteProjectContractTeams(String id);

	/**
	 * @Description: 批量删除项目组成员
	 * @param projectContractTeamsVo
	 */
    void deleteMultiProjectContractTeams(ProjectContractTeamsVo projectContractTeamsVo);

	/**
	 *@Description: 查询项目组成员详情
	 *@param id
	 *@return ProjectContractTeams
	 *@Author: wangsh
	 */
	ProjectContractTeams findById(String id);

	/**
	 *@Description: 分页查询项目组成员
	 *@param projectContractTeamsVo
	 *@return PageInfo<ProjectContractTeams>
	 *@Author: wangsh
	 */
	PageInfo<ProjectContractTeams> findPageByQuery(ProjectContractTeamsVo projectContractTeamsVo);
}
