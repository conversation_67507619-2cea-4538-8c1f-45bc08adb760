package com.fd.stdp.service.appraisal.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.appraisal.AppraisalApplyItem;
import com.fd.stdp.beans.appraisal.vo.AppraisalApplyItemVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.appraisal.AppraisalApplyItemMapper;
import com.fd.stdp.service.appraisal.AppraisalApplyItemService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 评价申请项Service业务层处理
 * @date 2021-11-18
 */
@Service
@Transactional(readOnly = true)
public class AppraisalApplyItemServiceImpl extends BaseServiceImpl<AppraisalApplyItemMapper, AppraisalApplyItem> implements AppraisalApplyItemService {

    private static final Logger logger = LoggerFactory.getLogger(AppraisalApplyItemServiceImpl.class);
    @Autowired
    private AppraisalApplyItemMapper appraisalApplyItemMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新评价申请项
     *@param appraisalApplyItem 评价申请项对象
     *@return String 评价申请项ID
     *@Author: linqiang
     */
    public String saveOrUpdateAppraisalApplyItem(AppraisalApplyItem appraisalApplyItem) {
        if (appraisalApplyItem == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(appraisalApplyItem.getId())) {
            //新增
            appraisalApplyItem.setId(UUIDUtils.getUUID());
            appraisalApplyItemMapper.insertSelective(appraisalApplyItem);
        } else {
            //避免页面传入修改
            appraisalApplyItem.setYn(null);
            appraisalApplyItemMapper.updateByPrimaryKeySelective(appraisalApplyItem);
        }
        return appraisalApplyItem.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除评价申请项
     *@param id void 评价申请项ID
     *@Author: linqiang
     */
    public void deleteAppraisalApplyItem(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            AppraisalApplyItem appraisalApplyItem = appraisalApplyItemMapper.selectByPrimaryKey(id);
            if (appraisalApplyItem == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            AppraisalApplyItem temappraisalApplyItem = new AppraisalApplyItem();
            temappraisalApplyItem.setYn(CommonConstant.FLAG_NO);
            temappraisalApplyItem.setId(appraisalApplyItem.getId());
            appraisalApplyItemMapper.updateByPrimaryKeySelective(temappraisalApplyItem);
        }
    }

    /**
     * @param id
     * @return AppraisalApplyItem
     * @Description: 查询评价申请项详情
     * @Author: linqiang
     */
    @Override
    public AppraisalApplyItem findById(String id) {
        return appraisalApplyItemMapper.selectByPrimaryKey(id);
    }


    /**
     * @param appraisalApplyItemVo
     * @return PageInfo<AppraisalApplyItem>
     * @Description: 分页查询评价申请项
     * @Author: linqiang
     */
    @Override
    public PageInfo<AppraisalApplyItem> findPageByQuery(AppraisalApplyItemVo appraisalApplyItemVo) {
        PageHelper.startPage(appraisalApplyItemVo.getPageNum(), appraisalApplyItemVo.getPageSize());
        Example example = new Example(AppraisalApplyItem.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(appraisalApplyItemVo.getName())){
        //	criteria.andEqualTo(appraisalApplyItemVo.getName());
        //}
        List<AppraisalApplyItem> appraisalApplyItemList = appraisalApplyItemMapper.selectByExample(example);
        return new PageInfo<AppraisalApplyItem>(appraisalApplyItemList);
    }
}
