package com.fd.stdp.service.appraisal.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.appraisal.AppraisalAdvices;
import com.fd.stdp.beans.appraisal.vo.AppraisalAdvicesVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.appraisal.AppraisalAdvicesMapper;
import com.fd.stdp.service.appraisal.AppraisalAdvicesService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 评价退回Service业务层处理
 * @date 2021-11-19
 */
@Service
@Transactional(readOnly = true)
public class AppraisalAdvicesServiceImpl extends BaseServiceImpl<AppraisalAdvicesMapper, AppraisalAdvices> implements AppraisalAdvicesService {

    private static final Logger logger = LoggerFactory.getLogger(AppraisalAdvicesServiceImpl.class);
    @Autowired
    private AppraisalAdvicesMapper appraisalAdvicesMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新评价退回
     *@param appraisalAdvices 评价退回对象
     *@return String 评价退回ID
     *@Author: yujianfei
     */
    public String saveOrUpdateAppraisalAdvices(AppraisalAdvices appraisalAdvices) {
        if (appraisalAdvices == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(appraisalAdvices.getId())) {
            //新增
            appraisalAdvices.setId(UUIDUtils.getUUID());
            appraisalAdvicesMapper.insertSelective(appraisalAdvices);
        } else {
            //避免页面传入修改
            appraisalAdvices.setYn(null);
            appraisalAdvicesMapper.updateByPrimaryKeySelective(appraisalAdvices);
        }
        return appraisalAdvices.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除评价退回
     *@param id void 评价退回ID
     *@Author: yujianfei
     */
    public void deleteAppraisalAdvices(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            AppraisalAdvices appraisalAdvices = appraisalAdvicesMapper.selectByPrimaryKey(id);
            if (appraisalAdvices == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            AppraisalAdvices temappraisalAdvices = new AppraisalAdvices();
            temappraisalAdvices.setYn(CommonConstant.FLAG_NO);
            temappraisalAdvices.setId(appraisalAdvices.getId());
            appraisalAdvicesMapper.updateByPrimaryKeySelective(temappraisalAdvices);
        }
    }

    /**
     * @param id
     * @return AppraisalAdvices
     * @Description: 查询评价退回详情
     * @Author: yujianfei
     */
    @Override
    public AppraisalAdvices findById(String id) {
        return appraisalAdvicesMapper.selectByPrimaryKey(id);
    }


    /**
     * @param appraisalAdvicesVo
     * @return PageInfo<AppraisalAdvices>
     * @Description: 分页查询评价退回
     * @Author: yujianfei
     */
    @Override
    public PageInfo<AppraisalAdvices> findPageByQuery(AppraisalAdvicesVo appraisalAdvicesVo) {
        PageHelper.startPage(appraisalAdvicesVo.getPageNum(), appraisalAdvicesVo.getPageSize());
        Example example = new Example(AppraisalAdvices.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(appraisalAdvicesVo.getName())){
        //	criteria.andEqualTo(appraisalAdvicesVo.getName());
        //}
        List<AppraisalAdvices> appraisalAdvicesList = appraisalAdvicesMapper.selectByExample(example);
        return new PageInfo<AppraisalAdvices>(appraisalAdvicesList);
    }
}
