package com.fd.stdp.service.project.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractPlan;
import com.fd.stdp.beans.project.vo.ProjectContractPlanVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectContractPlanMapper;
import com.fd.stdp.service.project.ProjectContractPlanService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 项目分年度(阶段)进度安排
 *@Author: wangsh
 *@Date: 2022-01-12 14:18:41
 */
public class ProjectContractPlanServiceImpl extends BaseServiceImpl<ProjectContractPlanMapper, ProjectContractPlan> implements ProjectContractPlanService{

	public static final Logger logger = LoggerFactory.getLogger(ProjectContractPlanServiceImpl.class);
	
	@Autowired
	private ProjectContractPlanMapper projectContractPlanMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新项目分年度(阶段)进度安排
	 *@param projectContractPlan 项目分年度(阶段)进度安排对象
	 *@return String 项目分年度(阶段)进度安排ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateProjectContractPlan(ProjectContractPlan projectContractPlan) {
		if(projectContractPlan==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(projectContractPlan.getId())){
			//新增
			projectContractPlan.setId(UUIDUtils.getUUID());
			projectContractPlanMapper.insertSelective(projectContractPlan);
		}else{
			//避免页面传入修改
			projectContractPlan.setYn(null);
			projectContractPlanMapper.updateByPrimaryKeySelective(projectContractPlan);
		}
		return projectContractPlan.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除项目分年度(阶段)进度安排
	 *@param id void 项目分年度(阶段)进度安排ID
	 *@Author: wangsh
	 */
	public void deleteProjectContractPlan(String id) {
		//TODO 做判断后方能执行删除
		ProjectContractPlan projectContractPlan=projectContractPlanMapper.selectByPrimaryKey(id);
		if(projectContractPlan==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		ProjectContractPlan temprojectContractPlan=new ProjectContractPlan();
		temprojectContractPlan.setYn(CommonConstant.FLAG_NO);
		temprojectContractPlan.setId(projectContractPlan.getId());
		projectContractPlanMapper.updateByPrimaryKeySelective(temprojectContractPlan);
	}

    /**
     * @Description: 批量删除项目分年度(阶段)进度安排
     * @param projectContractPlanVo
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiProjectContractPlan(ProjectContractPlanVo projectContractPlanVo) {
		projectContractPlanVo.getIds().stream().forEach(id-> this.deleteProjectContractPlan(id));
	}

	@Override
	/**
	 *@Description: 查询项目分年度(阶段)进度安排详情
	 *@param id
	 *@return ProjectContractPlan
	 *@Author: wangsh
	 */
	public ProjectContractPlan findById(String id) {
		return projectContractPlanMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询项目分年度(阶段)进度安排
	 *@param projectContractPlanVo
	 *@return PageInfo<ProjectContractPlan>
	 *@Author: wangsh
	 */
	public PageInfo<ProjectContractPlan> findPageByQuery(ProjectContractPlanVo projectContractPlanVo) {
		PageHelper.startPage(projectContractPlanVo.getPageNum(),projectContractPlanVo.getPageSize());
		Example example=new Example(ProjectContractPlan.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(projectContractPlanVo.getName())){
		//	criteria.andEqualTo(projectContractPlanVo.getName());
		//}
		List<ProjectContractPlan> projectContractPlanList=projectContractPlanMapper.selectByExample(example);
		return new PageInfo<ProjectContractPlan>(projectContractPlanList);
	}

}
