package com.fd.stdp.service.sys.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.fd.stdp.beans.sys.SysRole;
import com.fd.stdp.beans.sys.SysRolePermission;
import com.fd.stdp.beans.sys.vo.PermissionVO;
import com.fd.stdp.beans.sys.vo.RoleAndRoleTypesVO;
import com.fd.stdp.beans.sys.vo.RolePermissionVO;
import com.fd.stdp.beans.sys.vo.RoleTypePermissionVO;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.dao.sys.SysRoleMapper;
import com.fd.stdp.dao.sys.SysRolePermissionMapper;
import com.fd.stdp.service.sys.RolePermissionService;
import com.fd.stdp.service.sys.RoleService;
import com.fd.stdp.util.UUIDUtils;

import tk.mybatis.mapper.entity.Example;

/**
 * 角色service实现类
 * 
 * <AUTHOR>
 * @date 2018/03/1
 */
@Service
@Transactional(readOnly = true)
public class RolePermissionServiceImpl extends BaseServiceImpl<SysRolePermissionMapper, SysRolePermission>
		implements RolePermissionService {

//	@Resource

//    private ExceptionManager exceptionManager;

	@Autowired
	private RoleService roleService;

	@Autowired
	private SysRoleMapper sysRoleMapper;

	@Autowired
	private SysRolePermissionMapper sysRolePermissionMapper;

	/**
	 * 给角色授权
	 * 
	 * @param roleId
	 * @param permissions
	 */
	@Override
	@Transactional(readOnly = false)
	public void savePermissions(String roleId, String menuId, List<String> permissions, String type) {
		if (roleId == null) {
			return;
		}
		SysRole role = new SysRole();
		role.setId(roleId);

		SysRole roleObj = this.roleService.selectOne(role);
		if (roleObj == null) {
			throw new ServiceException("未找到角色信息!");
		}

		String roleCode = roleObj.getRoleCode();

		/*** delete permission **/
		this.mapper.deletePermissionByRoleCode(roleCode, type, menuId);

		if (permissions != null) {
			for (String permission : permissions) {
				if (!permission.equals("0")) {
					if (PermissionVO.TYPE_MENU.equals(type)) {
						SysRolePermission per = new SysRolePermission();
						per.setResourceId(permission);
						per.setResourceType(CommonConstant.RESOURCE_TYPE_MENU);
						per.setRoleCode(roleCode);
						per.setId(UUIDUtils.getUUID());
						sysRolePermissionMapper.insertSelective(per);
					} else if (PermissionVO.TYPE_BTN.equals(type)) {
						SysRolePermission per = new SysRolePermission();
						per.setResourceId(permission);
						per.setResourceType(CommonConstant.RESOURCE_TYPE_BTN);
						per.setRoleCode(roleCode);
						per.setId(UUIDUtils.getUUID());
						sysRolePermissionMapper.insertSelective(per);
					}
				}
			}
		}
	}

	/**
	 * 查询角色权限
	 * 
	 * @param roleId
	 * @return
	 */
	@Override
	public List<SysRolePermission> listPermissions(String roleId) {
		if (roleId == null) {
			return null;
		}

		SysRole roleObj = this.sysRoleMapper.selectByPrimaryKey(roleId);
		if (roleObj == null) {
			throw new ServiceException("未找到角色信息!");
		}

		Example example = new Example(SysRolePermission.class);
		example.createCriteria().andEqualTo("roleCode", roleObj.getRoleCode());
		return this.mapper.selectByExample(example);
	}

	@Override
	public List<RoleTypePermissionVO> getUserAllPermission(String userId) {
		if (StringUtils.isEmpty(userId)) {
			throw new ServiceException("用户Id不能为空!");
		}
		ArrayList<RoleTypePermissionVO> roleTypeVOs = new ArrayList<>();
		List<SysRole> roleList = this.sysRoleMapper.listByUserId(userId);
		if (CollectionUtils.isEmpty(roleList)) {
			return roleTypeVOs;
		}
		// 查询同一角色类型下的所有权限
		for (SysRole role : roleList) {
			RoleTypePermissionVO roleTypePermissionVO = new RoleTypePermissionVO();

			List<String> roleIds = new ArrayList<>();
			roleIds.add(role.getId());
			List<String> menuPermissionCodes = this.mapper.findMenuPermissionCodeByRoleId(roleIds);
			List<String> elementPermissionCodes = this.mapper.findElementPermissionCodeByRoleId(roleIds);
			roleTypePermissionVO.setRoleType("ROLETYPE");
			roleTypePermissionVO.setMenuPermissionCodes(menuPermissionCodes);
			roleTypePermissionVO.setElementPermissionCodes(elementPermissionCodes);
			roleTypeVOs.add(roleTypePermissionVO);
		}
		return roleTypeVOs;
	}

	@Override
	public List<RolePermissionVO> getUserRoleAllPermission(String userId) {
		if (StringUtils.isEmpty(userId)) {
			throw new ServiceException("用户Id不能为空!");
		}
		ArrayList<RolePermissionVO> roleVOs = new ArrayList<>();
		List<RoleAndRoleTypesVO> roleVoList = this.sysRoleMapper.listRoleVOByUserId(userId);
		logger.info("<getUserRoleAllPermission><roleVoList>" + roleVoList);
		if (CollectionUtils.isEmpty(roleVoList)) {
			return roleVOs;
		}
		// 查询同一角色类型下的所有权限
		for (RoleAndRoleTypesVO crtr : roleVoList) {
			List<String> roleIds = new ArrayList<>();
			roleIds.add(crtr.getId());
			RolePermissionVO rolePermissionVO = new RolePermissionVO();
			List<String> menuPermissionCodes = this.mapper.findMenuPermissionCodeByRoleId(roleIds);
			List<String> elementPermissionCodes = this.mapper.findElementPermissionCodeByRoleId(roleIds);
			rolePermissionVO.setRoleCode(crtr.getRoleCode());
			rolePermissionVO.setRoleName(crtr.getRoleName());
			rolePermissionVO.setRoleType(crtr.getRoleTypes());
			rolePermissionVO.setMenuPermissionCodes(menuPermissionCodes);
			rolePermissionVO.setElementPermissionCodes(elementPermissionCodes);
			roleVOs.add(rolePermissionVO);
		}
		logger.info("<getUserRoleAllPermission><roleVOs>" + roleVOs);
		return roleVOs;
	}
}
