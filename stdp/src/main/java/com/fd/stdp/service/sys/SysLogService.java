package com.fd.stdp.service.sys;

import java.util.List;

import com.fd.stdp.beans.sys.SysLogRecord;
import com.fd.stdp.beans.sys.vo.SysLogRecordExportVo;
import com.fd.stdp.beans.sys.vo.SysLogRecordVo;
import com.github.pagehelper.PageInfo;

/**
 * @Description: 系统日志
 * @Author: hzh
 * @Date: 2019-11-04 14:33:24
 */
public interface SysLogService {

	/**
	 * 
	 * @Description: 保存或更新日志记录
	 * @param logRecord 日志记录对象
	 * @return String 日志记录ID
	 * @Author: linqiang
	 */
	String saveOrUpdateLogRecord(SysLogRecord logRecord);

	/**
	 * 
	 * @Description: 查询日志记录
	 * @param id 日志ID
	 * @return SysLogRecord
	 * @Author: hzh
	 */
	SysLogRecord findById(String id);

	/**
	 * 
	 * @Description: 分页查询所有日志
	 * @param logRecordvo
	 * @return PageInfo<LogRecord>
	 * @Author: linqiang
	 */
	PageInfo<SysLogRecord> findAllPage(SysLogRecordVo logRecordvo);

	/**
	 * @Description:导出日志信息
	 * @param sysLogRecordVo
	 * @return List<SysLogRecordExportVo>
	 * @Author: szx
	 */
	List<SysLogRecordExportVo> export(SysLogRecordVo sysLogRecordVo);
}
