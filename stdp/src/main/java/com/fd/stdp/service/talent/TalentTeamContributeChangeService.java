package com.fd.stdp.service.talent;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamContributeChange;
import com.fd.stdp.beans.talent.vo.TalentTeamContributeChangeVo;
/**
 *@Description: 创新团队建设任务书变更
 *@Author: wangsh
 *@Date: 2022-02-14 19:56:46
 */
public interface TalentTeamContributeChangeService {

	/**
	 *@Description: 保存或更新创新团队建设任务书变更
	 *@param talentTeamContributeChange 创新团队建设任务书变更对象
	 *@return String 创新团队建设任务书变更ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTalentTeamContributeChange(TalentTeamContributeChangeVo talentTeamContributeChange);

	/**
	 * 不启动流程的新增
	 * @param talentTeamContributeChange
	 * @return
	 */
	String saveOrUpdateTalentTeamContributeChangeNoFlow(TalentTeamContributeChangeVo talentTeamContributeChange);
	
	/**
	 *@Description: 删除创新团队建设任务书变更
	 *@param id void 创新团队建设任务书变更ID
	 *@Author: wangsh
	 */
	void deleteTalentTeamContributeChange(String id);

	/**
	 * @Description: 批量删除创新团队建设任务书变更
	 * @param ids
	 */
    void deleteMultiTalentTeamContributeChange(List<String> ids);

	/**
	 *@Description: 查询创新团队建设任务书变更详情
	 *@param id
	 *@return TalentTeamContributeChange
	 *@Author: wangsh
	 */
	TalentTeamContributeChange findById(String id);

	/**
	 *@Description: 分页查询创新团队建设任务书变更
	 *@param talentTeamContributeChangeVo
	 *@return PageInfo<TalentTeamContributeChange>
	 *@Author: wangsh
	 */
	PageInfo<TalentTeamContributeChange> findPageByQuery(TalentTeamContributeChangeVo talentTeamContributeChangeVo);
	
	
	/**
	 * 提交
	 * @param talentTeamContributeChangeVo
	 * @return
	 */
    String submitTalentTeamContributeChange(TalentTeamContributeChangeVo talentTeamContributeChangeVo);

	/**
	 * 审核
	 * @param talentTeamContributeChangeVo
	 * @return
	 */
	String auditTalentTeamContributeChange(TalentTeamContributeChangeVo talentTeamContributeChangeVo);

	/**
	 * 退回
	 * @param talentTeamContributeChangeVo
	 * @return
	 */
	String sendBackTalentTeamContributeChange(TalentTeamContributeChangeVo talentTeamContributeChangeVo);

	/**
	 * 任务书下达
	 * @param talentTeamContributeChangeVo
	 * @return
	 */
	String releaseTalentTeamContributeChange(TalentTeamContributeChangeVo talentTeamContributeChangeVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<TalentTeamContributeChange> todoList(TalentTeamContributeChangeVo talentTeamContributeChangeVo);

	/**
	 * 已办列表
	 */
	PageInfo<TalentTeamContributeChange> finishedList(TalentTeamContributeChangeVo talentTeamContributeChangeVo);

	/**
	 * 已完成列表
	 */
	PageInfo<TalentTeamContributeChange> endList(TalentTeamContributeChangeVo talentTeamContributeChangeVo);
}
