package com.fd.stdp.service.audit;

import java.util.List;

import com.fd.stdp.beans.audit.InnerAuditProjectInformation;
import com.fd.stdp.beans.audit.vo.InnerAuditInformationCollectionVo;
import com.fd.stdp.beans.audit.vo.InnerAuditProjectInformationVo;
import com.github.pagehelper.PageInfo;

/**
 *@Description: 委托社会审计机构审计项目情况表
 *@Author: sef
 *@Date: 2022-06-06 13:56:38
 */
public interface InnerAuditProjectInformationsService {

	/**
	 *@Description: 保存或更新委托社会审计机构审计项目情况表
	 *@param innerAuditProjectInformation 委托社会审计机构审计项目情况表对象
	 *@return String 委托社会审计机构审计项目情况表ID
	 *@Author: sef
	 */
	String saveOrUpdateInnerAuditProjectInformation(InnerAuditProjectInformationVo innerAuditProjectInformation);
	
	/**
	 *@Description: 删除委托社会审计机构审计项目情况表
	 *@param id void 委托社会审计机构审计项目情况表ID
	 *@Author: sef
	 */
	void deleteInnerAuditProjectInformation(String id);

	/**
	 * @Description: 批量删除委托社会审计机构审计项目情况表
	 * @param ids
	 */
    void deleteMultiInnerAuditProjectInformation(List<String> ids);

	/**
	 *@Description: 查询委托社会审计机构审计项目情况表详情
	 *@param id
	 *@return InnerAuditProjectInformation
	 *@Author: sef
	 */
	InnerAuditProjectInformation findById(String id);

	/**
	 *@Description: 分页查询委托社会审计机构审计项目情况表
	 *@param innerAuditProjectInformationVo
	 *@return PageInfo<InnerAuditProjectInformation>
	 *@Author: sef
	 */
	PageInfo<InnerAuditProjectInformationVo> findPageByQuery(InnerAuditProjectInformationVo innerAuditProjectInformationVo);
	
	
	/**
	 * 提交
	 * @param innerAuditProjectInformationVo
	 * @return
	 */
    String submitInnerAuditProjectInformation(InnerAuditProjectInformationVo innerAuditProjectInformationVo);

	/**
	 * 审核
	 * @param innerAuditProjectInformationVo
	 * @return
	 */
	String auditInnerAuditProjectInformation(InnerAuditProjectInformationVo innerAuditProjectInformationVo);

	/**
	 * 退回
	 * @param innerAuditProjectInformationVo
	 * @return
	 */
	String sendBackInnerAuditProjectInformation(InnerAuditProjectInformationVo innerAuditProjectInformationVo);

	/**
	 * 任务书下达
	 * @param innerAuditProjectInformationVo
	 * @return
	 */
	String releaseInnerAuditProjectInformation(InnerAuditProjectInformationVo innerAuditProjectInformationVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnerAuditProjectInformation> todoList(InnerAuditProjectInformationVo innerAuditProjectInformationVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnerAuditProjectInformation> finishedList(InnerAuditProjectInformationVo innerAuditProjectInformationVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnerAuditProjectInformation> endList(InnerAuditProjectInformationVo innerAuditProjectInformationVo);

    void saveBatchInnerAuditWorkContacts(List<InnerAuditProjectInformationVo> contactList);

	PageInfo findList(InnerAuditProjectInformationVo vo);

	List exportInnerAuditProjectInformationVo(InnerAuditProjectInformationVo vo);

	List exportInnerAuditProjectInformationVoAllUnit(InnerAuditProjectInformationVo vo);
}
