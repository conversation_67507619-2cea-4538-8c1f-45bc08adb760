package com.fd.stdp.service.basic.impl;

import java.util.ArrayList;
import java.util.List;

import com.fd.stdp.beans.basic.BasicPersonLinked;
import com.fd.stdp.beans.basic.vo.BasicPersonLinkedVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicUnitLinked;
import com.fd.stdp.beans.basic.vo.BasicUnitLinkedVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicUnitLinkedMapper;
import com.fd.stdp.service.basic.BasicUnitLinkedService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 单位关联表
 *@Author: wangsh
 *@Date: 2022-02-10 14:49:38
 */
public class BasicUnitLinkedServiceImpl extends BaseServiceImpl<BasicUnitLinkedMapper, BasicUnitLinked> implements BasicUnitLinkedService{

	public static final Logger logger = LoggerFactory.getLogger(BasicUnitLinkedServiceImpl.class);
	
	@Autowired
	private BasicUnitLinkedMapper basicUnitLinkedMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新单位关联表
	 *@param basicUnitLinked 单位关联表对象
	 *@return String 单位关联表ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateBasicUnitLinked(BasicUnitLinkedVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			basicUnitLinkedMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			basicUnitLinkedMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除单位关联表
	 *@param id void 单位关联表ID
	 *@Author: wangsh
	 */
	public void deleteBasicUnitLinked(String id) {
		//TODO 做判断后方能执行删除
		BasicUnitLinked basicUnitLinked=basicUnitLinkedMapper.selectByPrimaryKey(id);
		if(basicUnitLinked==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		BasicUnitLinked tembasicUnitLinked=new BasicUnitLinked();
		tembasicUnitLinked.setYn(CommonConstant.FLAG_NO);
		tembasicUnitLinked.setId(basicUnitLinked.getId());
		basicUnitLinkedMapper.updateByPrimaryKeySelective(tembasicUnitLinked);
	}

    /**
     * @Description: 批量删除单位关联表
     * @param basicUnitLinkedVo
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiBasicUnitLinked(List<String> ids) {
		ids.stream().forEach(id-> this.deleteBasicUnitLinked(id));
	}

	@Override
	/**
	 *@Description: 查询单位关联表详情
	 *@param id
	 *@return BasicUnitLinked
	 *@Author: wangsh
	 */
	public BasicUnitLinked findById(String id) {
		return basicUnitLinkedMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询单位关联表
	 *@param basicUnitLinkedVo
	 *@return PageInfo<BasicUnitLinked>
	 *@Author: wangsh
	 */
	public PageInfo<BasicUnitLinked> findPageByQuery(BasicUnitLinkedVo basicUnitLinkedVo) {
		PageHelper.startPage(basicUnitLinkedVo.getPageNum(),basicUnitLinkedVo.getPageSize());
		Example example=new Example(BasicUnitLinked.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(basicUnitLinkedVo.getName())){
		//	criteria.andEqualTo(basicUnitLinkedVo.getName());
		//}
		List<BasicUnitLinked> basicUnitLinkedList=basicUnitLinkedMapper.selectByExample(example);
		return new PageInfo<BasicUnitLinked>(basicUnitLinkedList);
	}

	@Override
	public List<BasicUnitLinkedVo> findByFormId(String formId) {
		Example example = new Example(BasicUnitLinked.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("formId", formId);
		List<BasicUnitLinked> basicUnitLinkeds = basicUnitLinkedMapper.selectByExample(example);
		List<BasicUnitLinkedVo> basicUnitLinkedVos = new ArrayList<>();
		for (BasicUnitLinked b:basicUnitLinkeds) {
			BasicUnitLinkedVo linkedVo = new BasicUnitLinkedVo();
			BeanUtils.copyProperties(b, linkedVo);
			basicUnitLinkedVos.add(linkedVo);
		}
		return basicUnitLinkedVos;
	}

	@Override
	public void clearAndUpdateBasicUnitLinked(String formId, List<BasicUnitLinkedVo> linkedList) {
		if(StringUtils.isBlank(formId)){
			throw new ServiceException("未保存的表单无法关联人员");
		}
		basicUnitLinkedMapper.clearByFormId(formId);
		if (!CollectionUtils.isEmpty(linkedList)) {
			for (BasicUnitLinked b:linkedList) {
				if(b.getFormId() != null) {
					b.setYn(CommonConstant.FLAG_YES);
					basicUnitLinkedMapper.updateByPrimaryKeySelective(b);
				} else {
					b.setFormId(formId);
					b.setId(UUIDUtils.getUUID());
					basicUnitLinkedMapper.insertSelective(b);
				}
			}
		}
	}

}
