package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityChange;
import com.fd.stdp.beans.innovation.vo.InnovationQualityChangeVo;
/**
 *@Description: 省质检中心变更
 *@Author: wangsh
 *@Date: 2022-03-04 09:23:32
 */
public interface InnovationQualityChangeService {

	/**
	 *@Description: 保存或更新省质检中心变更
	 *@param innovationQualityChange 省质检中心变更对象
	 *@return String 省质检中心变更ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationQualityChange(InnovationQualityChangeVo innovationQualityChange);
	
	/**
	 *@Description: 删除省质检中心变更
	 *@param id void 省质检中心变更ID
	 *@Author: wangsh
	 */
	void deleteInnovationQualityChange(String id);

	/**
	 * @Description: 批量删除省质检中心变更
	 * @param ids
	 */
    void deleteMultiInnovationQualityChange(List<String> ids);

	/**
	 *@Description: 查询省质检中心变更详情
	 *@param id
	 *@return InnovationQualityChange
	 *@Author: wangsh
	 */
	InnovationQualityChange findById(String id);

	/**
	 *@Description: 分页查询省质检中心变更
	 *@param innovationQualityChangeVo
	 *@return PageInfo<InnovationQualityChange>
	 *@Author: wangsh
	 */
	PageInfo<InnovationQualityChange> findPageByQuery(InnovationQualityChangeVo innovationQualityChangeVo);
	
	
	/**
	 * 提交
	 * @param innovationQualityChangeVo
	 * @return
	 */
    String submitInnovationQualityChange(InnovationQualityChangeVo innovationQualityChangeVo);

	/**
	 * 审核
	 * @param innovationQualityChangeVo
	 * @return
	 */
	String auditInnovationQualityChange(InnovationQualityChangeVo innovationQualityChangeVo);

	/**
	 * 退回
	 * @param innovationQualityChangeVo
	 * @return
	 */
	String sendBackInnovationQualityChange(InnovationQualityChangeVo innovationQualityChangeVo);

	/**
	 * 任务书下达
	 * @param innovationQualityChangeVo
	 * @return
	 */
	String releaseInnovationQualityChange(InnovationQualityChangeVo innovationQualityChangeVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnovationQualityChange> todoList(InnovationQualityChangeVo innovationQualityChangeVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationQualityChange> finishedList(InnovationQualityChangeVo innovationQualityChangeVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationQualityChange> endList(InnovationQualityChangeVo innovationQualityChangeVo);
}
