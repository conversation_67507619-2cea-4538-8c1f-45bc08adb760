package com.fd.stdp.service.basic;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicRecommend;
import com.fd.stdp.beans.basic.vo.BasicRecommendVo;
/**
 *@Description: 窗口表
 *@Author: wangsh
 *@Date: 2022-02-15 15:20:33
 */
public interface BasicRecommendService {

	/**
	 *@Description: 保存或更新窗口表
	 *@param basicRecommend 窗口表对象
	 *@return String 窗口表ID
	 *@Author: wangsh
	 */
	String saveOrUpdateBasicRecommend(BasicRecommendVo basicRecommend);
	
	/**
	 *@Description: 删除窗口表
	 *@param id void 窗口表ID
	 *@Author: wangsh
	 */
	void deleteBasicRecommend(String id);

	/**
	 * @Description: 批量删除窗口表
	 * @param ids
	 */
    void deleteMultiBasicRecommend(List<String> ids);

	/**
	 *@Description: 查询窗口表详情
	 *@param id
	 *@return BasicRecommend
	 *@Author: wangsh
	 */
	BasicRecommend findById(String id);

	/**
	 *@Description: 分页查询窗口表
	 *@param basicRecommendVo
	 *@return PageInfo<BasicRecommend>
	 *@Author: wangsh
	 */
	PageInfo<BasicRecommend> findPageByQuery(BasicRecommendVo basicRecommendVo);

	BasicRecommend findByRecommandType(String type);
}
