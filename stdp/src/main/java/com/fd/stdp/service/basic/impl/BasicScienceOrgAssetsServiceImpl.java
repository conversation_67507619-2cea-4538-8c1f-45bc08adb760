package com.fd.stdp.service.basic.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicScienceOrgAssets;
import com.fd.stdp.beans.basic.vo.BasicScienceOrgAssetsVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicScienceOrgAssetsMapper;
import com.fd.stdp.service.basic.BasicScienceOrgAssetsService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 机构资产Service业务层处理
 * @date 2021-11-09
 */
@Service
@Transactional(readOnly = true)
public class BasicScienceOrgAssetsServiceImpl extends BaseServiceImpl<BasicScienceOrgAssetsMapper, BasicScienceOrgAssets> implements BasicScienceOrgAssetsService {

    private static final Logger logger = LoggerFactory.getLogger(BasicScienceOrgAssetsServiceImpl.class);
    @Autowired
    private BasicScienceOrgAssetsMapper basicScienceOrgAssetsMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新机构资产
     *@param basicScienceOrgAssets 机构资产对象
     *@return String 机构资产ID
     *@Author: yujianfei
     */
    public String saveOrUpdateBasicScienceOrgAssets(BasicScienceOrgAssets basicScienceOrgAssets) {
        if (basicScienceOrgAssets == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(basicScienceOrgAssets.getId())) {
            //新增
            basicScienceOrgAssets.setId(UUIDUtils.getUUID());
            basicScienceOrgAssetsMapper.insertSelective(basicScienceOrgAssets);
        } else {
            //避免页面传入修改
            basicScienceOrgAssets.setYn(null);
            basicScienceOrgAssetsMapper.updateByPrimaryKeySelective(basicScienceOrgAssets);
        }
        return basicScienceOrgAssets.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除机构资产
     *@param id void 机构资产ID
     *@Author: yujianfei
     */
    public void deleteBasicScienceOrgAssets(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            BasicScienceOrgAssets basicScienceOrgAssets = basicScienceOrgAssetsMapper.selectByPrimaryKey(id);
            if (basicScienceOrgAssets == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            BasicScienceOrgAssets tembasicScienceOrgAssets = new BasicScienceOrgAssets();
            tembasicScienceOrgAssets.setYn(CommonConstant.FLAG_NO);
            tembasicScienceOrgAssets.setId(basicScienceOrgAssets.getId());
            basicScienceOrgAssetsMapper.updateByPrimaryKeySelective(tembasicScienceOrgAssets);
        }
    }

    /**
     * @param id
     * @return BasicScienceOrgAssets
     * @Description: 查询机构资产详情
     * @Author: yujianfei
     */
    @Override
    public BasicScienceOrgAssets findById(String id) {
        return basicScienceOrgAssetsMapper.selectByPrimaryKey(id);
    }


    /**
     * @param basicScienceOrgAssetsVo
     * @return PageInfo<BasicScienceOrgAssets>
     * @Description: 分页查询机构资产
     * @Author: yujianfei
     */
    @Override
    public PageInfo<BasicScienceOrgAssets> findPageByQuery(BasicScienceOrgAssetsVo basicScienceOrgAssetsVo) {
        PageHelper.startPage(basicScienceOrgAssetsVo.getPageNum(), basicScienceOrgAssetsVo.getPageSize());
        Example example = new Example(BasicScienceOrgAssets.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(basicScienceOrgAssetsVo.getName())){
        //	criteria.andEqualTo(basicScienceOrgAssetsVo.getName());
        //}
        List<BasicScienceOrgAssets> basicScienceOrgAssetsList = basicScienceOrgAssetsMapper.selectByExample(example);
        return new PageInfo<BasicScienceOrgAssets>(basicScienceOrgAssetsList);
    }
}
