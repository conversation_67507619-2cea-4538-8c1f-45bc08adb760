package com.fd.stdp.service.basic;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonRecord;
import com.fd.stdp.beans.basic.vo.BasicPersonRecordVo;
/**
 *@Description: 机构人员报备
 *@Author: wangsh
 *@Date: 2022-02-15 16:58:00
 */
public interface BasicPersonRecordService {

	/**
	 *@Description: 保存或更新机构人员报备
	 *@param basicPersonRecord 机构人员报备对象
	 *@return String 机构人员报备ID
	 *@Author: wangsh
	 */
	String saveOrUpdateBasicPersonRecord(BasicPersonRecordVo basicPersonRecord);
	
	/**
	 *@Description: 删除机构人员报备
	 *@param id void 机构人员报备ID
	 *@Author: wangsh
	 */
	void deleteBasicPersonRecord(String id);

	/**
	 * @Description: 批量删除机构人员报备
	 * @param ids
	 */
    void deleteMultiBasicPersonRecord(List<String> ids);

	/**
	 *@Description: 查询机构人员报备详情
	 *@param id
	 *@return BasicPersonRecord
	 *@Author: wangsh
	 */
	BasicPersonRecord findById(String id);

	/**
	 *@Description: 分页查询机构人员报备
	 *@param basicPersonRecordVo
	 *@return PageInfo<BasicPersonRecord>
	 *@Author: wangsh
	 */
	PageInfo<BasicPersonRecord> findPageByQuery(BasicPersonRecordVo basicPersonRecordVo);


	Object findIsRecord(String currentScienceOrgName);
}
