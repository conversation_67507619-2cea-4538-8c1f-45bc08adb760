package com.fd.stdp.service.sys;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.sys.SysProjectNoticeBatch;
import com.fd.stdp.beans.sys.vo.SysProjectNoticeBatchVo;

/**
 * 公告项目Service接口
 *
 * <AUTHOR>
 * @date 2021-11-15
 */
public interface SysProjectNoticeBatchService {
    /**
     * @param sysProjectNoticeBatch 公告项目对象
     * @return String 公告项目ID
     * @Description: 保存或更新公告项目
     * @Author: yujianfei
     */
    String saveOrUpdateSysProjectNoticeBatch(SysProjectNoticeBatch sysProjectNoticeBatch);

    /**
     * @param ids void 公告项目ID
     * @Description: 删除公告项目
     * @Author: yujianfei
     */
    void deleteSysProjectNoticeBatch(List<String> ids);

    /**
     * @param id
     * @return SysProjectNoticeBatch
     * @Description: 查询公告项目详情
     * @Author: yujianfei
     */
    SysProjectNoticeBatch findById(String id);

    /**
     * @param sysProjectNoticeBatchVo
     * @return PageInfo<SysProjectNoticeBatch>
     * @Description: 分页查询公告项目
     * @Author: yujianfei
     */
    PageInfo<SysProjectNoticeBatch> findPageByQuery(SysProjectNoticeBatchVo sysProjectNoticeBatchVo);
}
