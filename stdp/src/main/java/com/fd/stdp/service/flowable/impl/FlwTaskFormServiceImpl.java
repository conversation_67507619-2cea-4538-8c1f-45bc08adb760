package com.fd.stdp.service.flowable.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fd.stdp.beans.flowable.FlwTaskForm;
import com.fd.stdp.beans.flowable.vo.FlwTaskFormVo;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.dao.flowable.FlwTaskFormMapper;
import com.fd.stdp.service.flowable.FlwTaskFormService;
import com.fd.stdp.util.StringUtils;
import com.fd.stdp.util.UUIDUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 *@Description: 流程任务关联表单
 *@Author: linqiang
 *@Date: 2021-10-15 09:22:00
 */
@Service
@Transactional(readOnly = true)
public class FlwTaskFormServiceImpl extends BaseServiceImpl<FlwTaskFormMapper, FlwTaskForm> implements FlwTaskFormService{

	public static final Logger logger = LoggerFactory.getLogger(FlwTaskFormServiceImpl.class);
	
	@Autowired
	private FlwTaskFormMapper flwTaskFormMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新流程任务关联表单
	 *@param flwTaskForm 流程任务关联表单对象
	 *@return String 流程任务关联表单ID
	 *@Author: linqiang
	 */
	public String saveOrUpdateFlwTaskForm(FlwTaskForm flwTaskForm) {
		if(flwTaskForm==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(flwTaskForm.getId())){
			//新增
			flwTaskForm.setId(UUIDUtils.getUUID());
			flwTaskFormMapper.insertSelective(flwTaskForm);
		}else{
			//避免页面传入修改
			flwTaskForm.setYn(null);
			flwTaskFormMapper.updateByPrimaryKeySelective(flwTaskForm);
		}
		return flwTaskForm.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除流程任务关联表单
	 *@param id void 流程任务关联表单ID
	 *@Author: linqiang
	 */
	public void deleteFlwTaskForm(String id) {
		//TODO 做判断后方能执行删除
		FlwTaskForm flwTaskForm=flwTaskFormMapper.selectByPrimaryKey(id);
		if(flwTaskForm==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		FlwTaskForm temflwTaskForm=new FlwTaskForm();
		temflwTaskForm.setYn(CommonConstant.FLAG_NO);
		temflwTaskForm.setId(flwTaskForm.getId());
		flwTaskFormMapper.updateByPrimaryKeySelective(temflwTaskForm);
	}

	/**
	 *@Description: 查询流程任务关联表单详情
	 *@param id
	 *@return FlwTaskForm
	 *@Author: linqiang
	 */
    @Override
	public FlwTaskForm findById(String id) {
		return flwTaskFormMapper.selectByPrimaryKey(id);
	}


	/**
	 *@Description: 分页查询流程任务关联表单
	 *@param flwTaskFormVo
	 *@return PageInfo<FlwTaskForm>
	 *@Author: linqiang
	 */
	@Override
	public PageInfo<FlwTaskForm> findPageByQuery(FlwTaskFormVo flwTaskFormVo) {
		PageHelper.startPage(flwTaskFormVo.getPageNum(),flwTaskFormVo.getPageSize());
		Example example=new Example(FlwTaskForm.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if(!StringUtils.isEmpty(flwTaskFormVo.getTaskId())) {
    		criteria.andEqualTo("taskId",flwTaskFormVo.getTaskId());
    	}
    	if(!StringUtils.isEmpty(flwTaskFormVo.getFormId())) {
    		criteria.andEqualTo("formId",flwTaskFormVo.getFormId());
    	}
		List<FlwTaskForm> flwTaskFormList=flwTaskFormMapper.selectByExample(example);
		return new PageInfo<FlwTaskForm>(flwTaskFormList);
	}

	@Override
	public List<FlwTaskForm> findFlwTaskFormList(FlwTaskForm flwTaskForm) {
		Example example =new Example(FlwTaskForm.class);
    	Criteria  criteria = example.createCriteria();
    	if(!StringUtils.isEmpty(flwTaskForm.getTaskId())) {
    		criteria.andEqualTo("taskId",flwTaskForm.getTaskId());
    	}
    	if(!StringUtils.isEmpty(flwTaskForm.getFormId())) {
    		criteria.andEqualTo("formId",flwTaskForm.getFormId());
    	}
    	return flwTaskFormMapper.selectByExample(example);
	}

	@Override
	@Transactional(readOnly = false)
	public int deleteFlwTaskFormByIds(String[] ids) {
		if(ids==null||ids.length==0) {
			throw new ServiceException("数据异常");
		}
		int count=0;
		for(String id:ids) {
			count+=flwTaskFormMapper.deleteByPrimaryKey(id);
		}
		return count;
	}

}
