package com.fd.stdp.service.flowable;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.flowable.FlwForm;
import com.fd.stdp.beans.flowable.vo.FlwFormVo;
/**
 *@Description: 流程表单
 *@Author: linqiang
 *@Date: 2021-10-15 09:20:44
 */
public interface FlwFormService {

	/**
	 *@Description: 保存或更新流程表单
	 *@param flwForm 流程表单对象
	 *@return String 流程表单ID
	 *@Author: linqiang
	 */
	String saveOrUpdateFlwForm(FlwForm flwForm);
	
	/**
	 *@Description: 删除流程表单
	 *@param id void 流程表单ID
	 *@Author: linqiang
	 */
	void deleteFlwForm(String id);

	/**
	 *@Description: 查询流程表单详情
	 *@param id
	 *@return FlwForm
	 *@Author: linqiang
	 */
	FlwForm findById(String id);

	/**
	 *@Description: 分页查询流程表单
	 *@param flwFormVo
	 *@return PageInfo<FlwForm>
	 *@Author: linqiang
	 */
	PageInfo<FlwForm> findPageByQuery(FlwFormVo flwFormVo);
	
	    /**
     * 查询流程表单列表
     * 
     * @param flwForm 流程表单
     * @return 流程表单集合
     */
    public List<FlwForm> findFlwFormList(FlwForm flwForm);
	
	 /**
     * 批量删除流程表单
     * 
     * @param formIds 需要删除的流程表单ID
     * @return 结果
     */
    public int deleteFlwFormByIds(String[] formIds);
}
