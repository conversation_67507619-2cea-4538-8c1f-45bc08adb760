package com.fd.stdp.service.audit.impl;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.fd.stdp.beans.audit.InnerAuditInformationCollection;
import com.fd.stdp.beans.audit.InnerAuditOrg;
import com.fd.stdp.beans.audit.vo.*;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.constant.InnerAuditBasicConstant;
import com.fd.stdp.dao.audit.InnerAuditOrgMapper;
import com.fd.stdp.service.user.SysUserUtilService;
import com.fd.stdp.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.audit.InnerAuditInformationCollectionsMapper;
import com.fd.stdp.service.audit.InnerAuditInformationCollectionsService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import static com.fd.stdp.common.BaseController.getCurrentOrgName;
import static com.fd.stdp.common.BaseController.getLoginUser;
import static com.fd.stdp.service.audit.impl.InnerAuditUtil.FANGYUAN;
import static com.fd.stdp.util.AppUserUtil.getCurrentRealName;
import static com.fd.stdp.util.AppUserUtil.getCurrentUserName;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 浙江省内部审计人员信息采集表
 *@Author: sef
 *@Date: 2022-06-06 13:55:59
 */
public class InnerAuditInformationCollectionsServiceImpl extends BaseServiceImpl<InnerAuditInformationCollectionsMapper, InnerAuditInformationCollection> implements InnerAuditInformationCollectionsService {

    public static final Logger logger = LoggerFactory.getLogger(InnerAuditInformationCollectionsServiceImpl.class);

    @Autowired
    private InnerAuditInformationCollectionsMapper innerAuditInformationCollectionsMapper;
    @Autowired
    private FlowCommonService flowCommonService;

    @Autowired
    private SysUserUtilService sysUserUtilService;

    @Autowired
    private InnerAuditOrgMapper innerAuditOrgMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新浙江省内部审计人员信息采集表
     *@param innerAuditInformationCollection 浙江省内部审计人员信息采集表对象
     *@return String 浙江省内部审计人员信息采集表ID
     *@Author: sef
     */
    public String saveOrUpdateInnerAuditInformationCollection(InnerAuditInformationCollectionVo vo) {
        if (vo == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(vo.getId())) {
            //新增
            vo.setId(UUIDUtils.getUUID());
            vo.setFillState(InnerAuditBasicConstant.FILL_STATE_ESCALATED);
            innerAuditInformationCollectionsMapper.insertSelective(vo);
        } else {
            //避免页面传入修改
            vo.setYn(null);
            innerAuditInformationCollectionsMapper.updateByPrimaryKeySelective(vo);
        }
        return vo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除浙江省内部审计人员信息采集表
     *@param id void 浙江省内部审计人员信息采集表ID
     *@Author: sef
     */
    public void deleteInnerAuditInformationCollection(String id) {
        InnerAuditInformationCollection innerAuditInformationCollection = innerAuditInformationCollectionsMapper.selectByPrimaryKey(id);
        if (innerAuditInformationCollection == null) {
            throw new ServiceException("非法请求");
        }
        //逻辑删除
        InnerAuditInformationCollection teminnerAuditInformationCollection = new InnerAuditInformationCollection();
        teminnerAuditInformationCollection.setYn(CommonConstant.FLAG_NO);
        teminnerAuditInformationCollection.setId(innerAuditInformationCollection.getId());
        teminnerAuditInformationCollection.setIsBelongUnit(CommonConstant.FLAG_NO);
        innerAuditInformationCollectionsMapper.updateByPrimaryKeySelective(teminnerAuditInformationCollection);
    }

    /**
     * @param ids
     * @Description: 批量删除浙江省内部审计人员信息采集表
     */
    @Override
    @Transactional(readOnly = false)
    public void deleteMultiInnerAuditInformationCollection(List<String> ids) {
        ids.stream().forEach(id -> this.deleteInnerAuditInformationCollection(id));
    }

    @Override
    /**
     *@Description: 查询浙江省内部审计人员信息采集表详情
     *@param id
     *@return InnerAuditInformationCollection
     *@Author: sef
     */
    public InnerAuditInformationCollection findById(String id) {
        return innerAuditInformationCollectionsMapper.selectByPrimaryKey(id);
    }

    @Override
    /**
     *@Description: 分页查询浙江省内部审计人员信息采集表
     *@param innerAuditInformationCollectionVo
     *@return PageInfo<InnerAuditInformationCollection>
     *@Author: sef
     */
    public PageInfo<InnerAuditInformationCollection> findPageByQuery(InnerAuditInformationCollectionVo innerAuditInformationCollectionVo) {
        //PageHelper.startPage(innerAuditInformationCollectionVo.getPageNum(), innerAuditInformationCollectionVo.getPageSize());
        Example example = new Example(InnerAuditInformationCollection.class);
        example.setOrderByClause(" ORDER_VAL");
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);

        if (StringUtils.isEmpty(innerAuditInformationCollectionVo.getUnitName()) && StringUtils.isEmpty(innerAuditInformationCollectionVo.getOrgName())) {
            InnerAuditUtil.OrgTypeEnum orgTypeEnum = getFillType(getCurrentOrgName());
            switch (orgTypeEnum) {
                case PROVINCE:
                    break;
                default:
                    criteria.andEqualTo("unitName", getCurrentOrgName());
            }
        } else {
            if (!StringUtils.isEmpty(innerAuditInformationCollectionVo.getUnitName()) && StringUtils.isEmpty(innerAuditInformationCollectionVo.getOrgName())) {
                criteria.andLike("unitName", '%' + innerAuditInformationCollectionVo.getUnitName() + '%');
            } else {
                criteria.andLike("orgName", '%' + innerAuditInformationCollectionVo.getOrgName() + '%');
            }
        }


        //查询条件
        if (!StringUtils.isEmpty(innerAuditInformationCollectionVo.getName())) {
            criteria.andLike("name", '%' + innerAuditInformationCollectionVo.getName() + '%');
        }
        if (!StringUtils.isEmpty(innerAuditInformationCollectionVo.getAuditAdvice())) {
            criteria.andLike("auditAdvice", '%' + innerAuditInformationCollectionVo.getAuditAdvice() + '%');
        }
        if (!StringUtils.isEmpty(innerAuditInformationCollectionVo.getUnitUscc())) {
            criteria.andEqualTo("unitUscc", innerAuditInformationCollectionVo.getUnitUscc());
        }
        if (!StringUtils.isEmpty(innerAuditInformationCollectionVo.getOrgName())) {
            criteria.andLike("orgName", '%' + innerAuditInformationCollectionVo.getOrgName() + '%');
        }
        if (!StringUtils.isEmpty(innerAuditInformationCollectionVo.getPhone())) {
            criteria.andLike("phone", '%' + innerAuditInformationCollectionVo.getPhone() + '%');
        }
        if (!StringUtils.isEmpty(innerAuditInformationCollectionVo.getDegree())) {
            criteria.andLike("degree", '%' + innerAuditInformationCollectionVo.getDegree() + '%');
        }
        if (!ObjectUtils.isEmpty(innerAuditInformationCollectionVo.getFillState())) {
            criteria.andEqualTo("fillState", innerAuditInformationCollectionVo.getFillState());
        }
        List<InnerAuditInformationCollection> innerAuditInformationCollectionList = innerAuditInformationCollectionsMapper.selectByExample(example);
        PageInfo pageInfo = new PageInfo(innerAuditInformationCollectionList.isEmpty() ? Collections.emptyList() : innerAuditInformationCollectionList);
        pageInfo.setList(innerAuditInformationCollectionList.isEmpty() ? Collections.emptyList() :
                startPage(innerAuditInformationCollectionList, innerAuditInformationCollectionVo.getPageNum() == null ?
                                1 : innerAuditInformationCollectionVo.getPageNum(),
                        innerAuditInformationCollectionVo.getPageSize() == null ? 10 : innerAuditInformationCollectionVo.getPageSize()));
        pageInfo.setPageNum(innerAuditInformationCollectionVo.getPageNum());
        pageInfo.setPageSize(innerAuditInformationCollectionVo.getPageSize());
        return pageInfo;
    }

    @Override
    @Transactional(readOnly = false)
    public String submitInnerAuditInformationCollection(InnerAuditInformationCollectionVo innerAuditInformationCollectionVo) {
        List<InnerAuditInformationCollectionVo> infomationList = innerAuditInformationCollectionVo.getInfomationList();
        infomationList.stream().forEach(item -> item.setFillState(InnerAuditBasicConstant.FILL_STATE_PROVINCIAL_BUREAU_MODIFICATION));
        this.saveBatchInnerAuditWorkContacts(infomationList);
        return "success";
    }

    @Override
    @Transactional(readOnly = false)
    public String auditInnerAuditInformationCollection(InnerAuditInformationCollectionVo innerAuditInformationCollectionVo) {
        flowCommonService.doFlowStepAudit(innerAuditInformationCollectionVo, this.mapper
                , StringUtils.isNotBlank(innerAuditInformationCollectionVo.getAuditAdvice()) ? innerAuditInformationCollectionVo.getAuditAdvice() : "浙江省内部审计人员信息采集表审核通过"
                , FlowStatusEnum.END.getCode());
        return innerAuditInformationCollectionVo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    public String sendBackInnerAuditInformationCollection(InnerAuditInformationCollectionVo vo) {
        if (StringUtils.isNotBlank(vo.getUnitName())) {
            innerAuditInformationCollectionsMapper.sendBack(vo.getUnitName());
            return "success";
        }
        return "缺少单位名称！";
    }

    @Override
    @Transactional(readOnly = false)
    public String releaseInnerAuditInformationCollection(InnerAuditInformationCollectionVo innerAuditInformationCollectionVo) {
        flowCommonService.doCompleteTask(innerAuditInformationCollectionVo, this.mapper
                , "浙江省内部审计人员信息采集表任务书下达完成"
                , FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
        return null;
    }


    @Override
    public PageInfo<InnerAuditInformationCollection> todoList(InnerAuditInformationCollectionVo vo) {

        Example example = new Example(InnerAuditInformationCollection.class);
        Criteria criteria = getCriteria(vo, example);
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
    }

    @Override
    public PageInfo<InnerAuditInformationCollection> finishedList(InnerAuditInformationCollectionVo vo) {
        Example example = new Example(InnerAuditInformationCollection.class);
        Criteria criteria = getCriteria(vo, example);
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
    }

    @Override
    public PageInfo<InnerAuditInformationCollection> endList(InnerAuditInformationCollectionVo vo) {
        Example example = new Example(InnerAuditInformationCollection.class);
        example.orderBy("createTime").desc();
        Criteria criteria = getCriteria(vo, example);
        return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
    }

    @Override
    @Transactional(readOnly = false)
    public void saveBatchInnerAuditWorkContacts(List<InnerAuditInformationCollectionVo> contactList) {
        if(CollectionUtils.isEmpty(contactList)) {
            throw new ServiceException("请添加内容后重试");
        }
        // 已存在(单位)
        List<InnerAuditInformationCollectionVo> collect = contactList.stream().filter(item ->
                org.springframework.util.StringUtils.hasText(org.apache.commons.lang.ObjectUtils.toString(innerAuditInformationCollectionsMapper.findByUnitIsOrNotExist(item), "")))
                .collect(Collectors.toList());

        // 批量删除 逻辑删除
        if (!collect.isEmpty()) {
            innerAuditInformationCollectionsMapper.deleteByUnit(collect.get(0).getUnitName());
        }

        InnerAuditInformationCollectionVo old = collect.get(0);
        contactList.stream().forEach(item -> {
                    if (StringUtils.isBlank(item.getOrgName())) {
                        item.setOrgName(getCurrentOrgName());
                        item.setOrgCode(getLoginUser().getAreaCode());
                    }
                    item.setId(UUIDUtils.getUUID());
                    item.setFillState(old.getFillState() == null?InnerAuditBasicConstant.FILL_STATE_ESCALATED:old.getFillState());
                    item.setFillTime(old.getFillTime() == null ? new Date() : old.getFillTime());
                    item.setUnitName(old.getUnitName() == null ? getCurrentOrgName() : old.getUnitName());
                    innerAuditInformationCollectionsMapper.insert(item);
                }
        );
    }

    private Criteria getCriteria(InnerAuditInformationCollectionVo vo, Example example) {
        Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
        return criteria;
    }


    private Integer getOperType(String orgName) {
        LoginUser loginUser = getLoginUser();
        Integer operType = sysUserUtilService.getUserOperType(loginUser);
        Example example = new Example(InnerAuditOrg.class);
        example.createCriteria().andEqualTo("orgName", orgName);
        if (CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))) {
            return operType;
        }
        return 0;
    }

    private boolean getIsProOrg(String org) {
        Example example = new Example(InnerAuditOrg.class);
        example.createCriteria().andLike("orgName", "%" + org + "%");
        if (CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))) {
            return false;
        }
        return true;
    }

    // 0 省局 1 直属单位 2市局 3 县局
    private InnerAuditUtil.OrgTypeEnum getFillType(String org) {
        if (getIsProOrg(org)) {
            if (FANGYUAN.equals(org)) {
                return InnerAuditUtil.OrgTypeEnum.PROVINCE_GO_EM;
            }
            return InnerAuditUtil.OrgTypeEnum.PROVINCE_GO;
        }
        int orgType = getOperType(org);
        if (2 == orgType) {
            return InnerAuditUtil.OrgTypeEnum.PROVINCE;
        } else if (1 == orgType) {
            return InnerAuditUtil.OrgTypeEnum.CITY;
        }
        return InnerAuditUtil.OrgTypeEnum.COUNTRY;
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除浙江省内部审计人员信息采集表
     *@param id void 浙江省内部审计人员信息采集表ID
     *@Author: sef
     */
    public void removeInnerAuditInformationCollection(String id) {
        InnerAuditInformationCollection innerAuditInformationCollection = innerAuditInformationCollectionsMapper.selectByPrimaryKey(id);
        if (innerAuditInformationCollection == null) {
            throw new ServiceException("非法请求");
        }
        //逻辑删除
        InnerAuditInformationCollection teminnerAuditInformationCollection = new InnerAuditInformationCollection();
        teminnerAuditInformationCollection.setId(innerAuditInformationCollection.getId());
        teminnerAuditInformationCollection.setIsBelongUnit(CommonConstant.FLAG_NO);
        innerAuditInformationCollectionsMapper.updateByPrimaryKeySelective(teminnerAuditInformationCollection);
    }


    @Override
    public PageInfo findList(InnerAuditInformationCollectionVo vo) {
        InnerAuditUtil.OrgTypeEnum orgTypeEnum = getFillType(getCurrentOrgName());
        Boolean provinceFlag = new Boolean(false);
        switch (orgTypeEnum) {
            case PROVINCE:
                provinceFlag = true;
                break;
        }
        if (provinceFlag) {
            PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
            PageInfo pageInfo = new PageInfo(innerAuditInformationCollectionsMapper.getProvinceList(vo));
            return pageInfo;
        }

        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        PageInfo pageInfo = new PageInfo(innerAuditInformationCollectionsMapper.selectUnitName('%' + getCurrentOrgName() + '%'));
        return pageInfo;
    }

    @Override
    public List exportInnerAuditInformationCollectionVo(InnerAuditInformationCollectionVo vo) {
        List<InnerAuditInformationCollection> innerAudit = innerAuditInformationCollectionsMapper.selectUnitName('%' + getCurrentOrgName() + '%');
        List<InnerAuditInformationCollectionExportVo> list = new ArrayList<>();
        AtomicInteger orderValCn = new AtomicInteger();
        innerAudit.stream().forEach(item -> {
            InnerAuditInformationCollectionExportVo innerAuditVo = new InnerAuditInformationCollectionExportVo();
            BeanUtils.copyProperties(item, innerAuditVo);
            innerAuditVo.setOrderVal(orderValCn.incrementAndGet());
            /*innerAuditVo.setFillTimeLast(DateUtils.date2String(item.getFillTime()));*/
            if (StringUtils.isNotEmpty(item.getBirthday())) {
                String sDate = item.getBirthday().toString();
                innerAuditVo.setBirthdayCn(sDate.split("-")[0] + "年" + sDate.split("-")[1] + "月");
            }

            innerAuditVo.setIsCommunistPartyCn(item.getIsCommunistParty() == 1 ? "是" : "否");
            innerAuditVo.setIsInnerTalentCn(item.getIsInnerTalent() == 1 ? "是" : "否");
            innerAuditVo.setIsWillingAttendCn(item.getIsWillingAttend() == 2 ? "是" : "否");
            list.add(innerAuditVo);
        });
        if (!list.isEmpty()) {
            list.get(0).setFillTimeLast(DateUtils.date2String(innerAudit.get(0).getFillTime()));
        }
        return list;
    }

    @Override
    public List exportInnerAuditInformationCollectionVoAllUnit(InnerAuditInformationCollectionVo vo) {
        List<InnerAuditInformationCollection> innerAudit = innerAuditInformationCollectionsMapper.selectSubmit();
        List<InnerAuditInformationCollectionExportVo> list = new ArrayList<>();
        // 多个单位导出一起时序号重编
        AtomicInteger orderValCn = new AtomicInteger();
        innerAudit.stream().forEach(item -> {
            InnerAuditInformationCollectionExportVo innerAuditVo = new InnerAuditInformationCollectionExportVo();
            BeanUtils.copyProperties(item, innerAuditVo);
            if (StringUtils.isNotEmpty(item.getBirthday())) {
                String sDate = item.getBirthday().toString();
                innerAuditVo.setBirthdayCn(sDate.split("-")[0] + "年" + sDate.split("-")[1] + "月");
            }
            innerAuditVo.setIsCommunistPartyCn(item.getIsCommunistParty() == 1 ? "是" : "否");
            innerAuditVo.setIsInnerTalentCn(item.getIsInnerTalent() == 1 ? "是" : "否");
            innerAuditVo.setIsWillingAttendCn(item.getIsWillingAttend() == 2 ? "是" : "否");
            innerAuditVo.setOrderVal(orderValCn.incrementAndGet());
            list.add(innerAuditVo);
        });
        /*if (!list.isEmpty()) {
            list.get(0).setFillTimeLast(DateUtils.date2String(innerAudit.get(0).getFillTime()));
        }*/
        return list;
    }
}
