package com.fd.stdp.service.project;

import java.util.List;
import java.util.Map;

import com.fd.stdp.beans.project.ProjectApplyExpertExtract;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertExtractVo;
import com.fd.stdp.util.expertExtract.beans.fegn.*;
import com.github.pagehelper.PageInfo;

/**
 * 专家抽取任务Service接口
 *
 * <AUTHOR>
 * @date 2025-03-18
 */
public interface ProjectApplyExpertExtractService {
    /**
     * @param projectApplyExpertExtract 专家抽取任务对象
     * @return String 专家抽取任务ID
     * @Description: 保存或更新专家抽取任务
     * @Author: zhangYu
     */
    String saveOrUpdateProjectApplyExpertExtract(ProjectApplyExpertExtract projectApplyExpertExtract);

    /**
     * @param ids void 专家抽取任务ID
     * @Description: 删除专家抽取任务
     * @Author: zhangYu
     */
    void deleteProjectApplyExpertExtract(List<String> ids);

    /**
     * @param id
     * @return ProjectApplyExpertExtract
     * @Description: 查询专家抽取任务详情
     * @Author: zhangYu
     */
    ProjectApplyExpertExtractVo findById(String id);

    /**
     * @param projectApplyExpertExtractVo
     * @return PageInfo<ProjectApplyExpertExtract>
     * @Description: 分页查询专家抽取任务
     * @Author: zhangYu
     */
    List<ProjectApplyExpertExtractVo> findPageByQuery(ProjectApplyExpertExtractVo projectApplyExpertExtractVo);

    /**
     * @param restBusiRandom
     * @return ProjectApplyExpertExtract
     * @Description: 抽取专家
     * @Author: zhangYu
     */
    ProjectApplyExpertExtract extract(RestBusiRandom restBusiRandom);

    /**
     * @param applyId
     * @return ProjectApplyExpertExtract
     * @Description: 根据applyId查询专家抽取任务
     * @Author: zhangYu
     */
    ProjectApplyExpertExtract findByApplyId(String applyId);

    /**
     * @param restBusiRandom
     * @return ProjectApplyExpertExtract
     * @Description: 追加抽取专家
     * @Author: zhangYu
     */
    BusiRandomVo addCondition(RestBusiRandom restBusiRandom);

    List<String> findRecusalUnitListByQuery(BusiRandomVo busiRandom);


    List<String> findRecusalExportListByQuery(BusiRandomVo vo);

    List<SysDictItem> findDictItemByDictType(BusiRandomExpert vo);

    List<TreeSelect> selectDictItemByDictType(BusiRandomExpert vo);

    Boolean changeExpertStatus(BusiRandomExpert vo);


    /**
     * @param id
     * @return BusiRandomVo
     * @Description: 获取抽取信息
     * @Author: zhangYu
     */
    BusiRandomVo findRandomInfoById(String id);

    /**
     * 确认选择专家
     *
     * @param id
     */
    void confirmExpert(String id, String userName, String RoleName);
}
