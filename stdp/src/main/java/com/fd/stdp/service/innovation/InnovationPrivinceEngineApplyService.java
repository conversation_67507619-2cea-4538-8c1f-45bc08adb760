package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationPrivinceEngineApply;
import com.fd.stdp.beans.innovation.vo.InnovationPrivinceEngineApplyVo;
/**
 *@Description: 省工程研究中心
 *@Author: wangsh
 *@Date: 2022-03-07 19:46:26
 */
public interface InnovationPrivinceEngineApplyService {

	/**
	 *@Description: 保存或更新省工程研究中心
	 *@param innovationPrivinceEngineApply 省工程研究中心对象
	 *@return String 省工程研究中心ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationPrivinceEngineApply(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApply);
	
	/**
	 *@Description: 删除省工程研究中心
	 *@param id void 省工程研究中心ID
	 *@Author: wangsh
	 */
	void deleteInnovationPrivinceEngineApply(String id);

	/**
	 * @Description: 批量删除省工程研究中心
	 * @param ids
	 */
    void deleteMultiInnovationPrivinceEngineApply(List<String> ids);

	/**
	 *@Description: 查询省工程研究中心详情
	 *@param id
	 *@return InnovationPrivinceEngineApply
	 *@Author: wangsh
	 */
	InnovationPrivinceEngineApply findById(String id);

	/**
	 *@Description: 分页查询省工程研究中心
	 *@param innovationPrivinceEngineApplyVo
	 *@return PageInfo<InnovationPrivinceEngineApply>
	 *@Author: wangsh
	 */
	PageInfo<InnovationPrivinceEngineApply> findPageByQuery(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApplyVo);
	
	
	/**
	 * 提交
	 * @param innovationPrivinceEngineApplyVo
	 * @return
	 */
    String submitInnovationPrivinceEngineApply(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApplyVo);

	/**
	 * 审核
	 * @param innovationPrivinceEngineApplyVo
	 * @return
	 */
	String auditInnovationPrivinceEngineApply(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApplyVo);

	/**
	 * 退回
	 * @param innovationPrivinceEngineApplyVo
	 * @return
	 */
	String sendBackInnovationPrivinceEngineApply(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApplyVo);

	/**
	 * 任务书下达
	 * @param innovationPrivinceEngineApplyVo
	 * @return
	 */
	String releaseInnovationPrivinceEngineApply(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApplyVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnovationPrivinceEngineApply> todoList(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApplyVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationPrivinceEngineApply> finishedList(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApplyVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationPrivinceEngineApply> endList(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApplyVo);
}
