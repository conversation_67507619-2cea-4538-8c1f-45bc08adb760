package com.fd.stdp.service.innovation.impl;

import java.util.List;

import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryApplyVo;
import com.fd.stdp.beans.innovation.vo.InnovationQualityContractVo;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.basic.BasicPersonLinkedService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.innovation.InnovationQualityContractService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityApply;
import com.fd.stdp.beans.innovation.vo.InnovationQualityApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationQualityApplyMapper;
import com.fd.stdp.service.innovation.InnovationQualityApplyService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.fd.stdp.common.BaseController.getCurrentUserName;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 省质检中心申报
 *@Author: wangsh
 *@Date: 2022-02-11 11:18:37
 */
public class InnovationQualityApplyServiceImpl extends BaseServiceImpl<InnovationQualityApplyMapper, InnovationQualityApply> implements InnovationQualityApplyService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationQualityApplyServiceImpl.class);
	
	@Autowired
	private InnovationQualityApplyMapper innovationQualityApplyMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicPersonLinkedService basicPersonLinkedService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	@Autowired
	private InnovationQualityContractService innovationQualityContractService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新省质检中心申报
	 *@param innovationQualityApply 省质检中心申报对象
	 *@return String 省质检中心申报ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationQualityApply(InnovationQualityApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationQualityApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationQualityApplyMapper.updateByPrimaryKeySelective(vo);
		}

		// 人员
		if(!CollectionUtils.isEmpty(vo.getTeamList())) {
			basicPersonLinkedService.clearAndUpdateBasicPersonLinked(vo.getId(), vo.getTeamList());
		}
		// 附件
		if(!CollectionUtils.isEmpty(vo.getFiles())) {
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除省质检中心申报
	 *@param id void 省质检中心申报ID
	 *@Author: wangsh
	 */
	public void deleteInnovationQualityApply(String id) {
		//TODO 做判断后方能执行删除
		InnovationQualityApply innovationQualityApply=innovationQualityApplyMapper.selectByPrimaryKey(id);
		if(innovationQualityApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationQualityApply teminnovationQualityApply=new InnovationQualityApply();
		teminnovationQualityApply.setYn(CommonConstant.FLAG_NO);
		teminnovationQualityApply.setId(innovationQualityApply.getId());
		innovationQualityApplyMapper.updateByPrimaryKeySelective(teminnovationQualityApply);
	}

    /**
     * @Description: 批量删除省质检中心申报
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationQualityApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationQualityApply(id));
	}

	@Override
	/**
	 *@Description: 查询省质检中心申报详情
	 *@param id
	 *@return InnovationQualityApply
	 *@Author: wangsh
	 */
	public InnovationQualityApply findById(String id) {
		InnovationQualityApply innovationQualityApply = innovationQualityApplyMapper.selectByPrimaryKey(id);
		InnovationQualityApplyVo vo = new InnovationQualityApplyVo();
		BeanUtils.copyProperties(innovationQualityApply, vo);


		/**
		 * 人员
		 */
		vo.setTeamList(basicPersonLinkedService.findByFormId(vo.getId()));
		/**
		 * 附件
		 */
		vo.setFiles(basicFileAppendixService.findByFormId(vo.getId()));
		/**
		 * 流程
		 */
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询省质检中心申报
	 *@param innovationQualityApplyVo
	 *@return PageInfo<InnovationQualityApply>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationQualityApply> findPageByQuery(InnovationQualityApplyVo innovationQualityApplyVo) {
		PageHelper.startPage(innovationQualityApplyVo.getPageNum(),innovationQualityApplyVo.getPageSize());
		Example example=new Example(InnovationQualityApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationQualityApplyVo.getName())){
		//	criteria.andEqualTo(innovationQualityApplyVo.getName());
		//}
		List<InnovationQualityApply> innovationQualityApplyList=innovationQualityApplyMapper.selectByExample(example);
		return new PageInfo<InnovationQualityApply>(innovationQualityApplyList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationQualityApply(InnovationQualityApplyVo innovationQualityApplyVo) {
		String id = this.saveOrUpdateInnovationQualityApply(innovationQualityApplyVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innovationQualityApplyVo, this.mapper,
				StringUtils.isNotBlank(innovationQualityApplyVo.getAuditAdvice())?innovationQualityApplyVo.getAuditAdvice():"提交质检中心申报");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationQualityApply(InnovationQualityApplyVo innovationQualityApplyVo) {
		InnovationQualityApplyVo old = (InnovationQualityApplyVo) findById(innovationQualityApplyVo.getId());
		if(StringUtils.equals(old.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())){
			// 添加任务书
			InnovationQualityContractVo contractVo = new InnovationQualityContractVo();
			BeanUtils.copyProperties(old, contractVo);
			contractVo.setId(null);
			contractVo.setApplyId(old.getId());
			contractVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			contractVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			innovationQualityContractService.saveOrUpdateInnovationQualityContract(contractVo);
		}

		// todo 省局审核通过 会同时选择专家
		flowCommonService.doFlowStepAudit(innovationQualityApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityApplyVo.getAuditAdvice()) ? innovationQualityApplyVo.getAuditAdvice() : "质检中心申报审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationQualityApplyVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationQualityApply(InnovationQualityApplyVo innovationQualityApplyVo) {
		flowCommonService.doFlowStepSendBack(innovationQualityApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityApplyVo.getAuditAdvice())?innovationQualityApplyVo.getAuditAdvice():"质检中心申报退回"
				, false
		);
		return null;
	}

	@Override
	public String choseExpertInnovationQualityApply(InnovationQualityApplyVo innovationQualityApplyVo) {
		return null;
	}

	@Override
	public String expoertSubmitInnovationQualityApply(InnovationQualityApplyVo innovationQualityApplyVo) {

		flowCommonService.doCompleteTask(innovationQualityApplyVo, this.mapper
				, "全部专家评审完成"
				, FlowStatusEnum.PROJECT_REVIEW.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE, getCurrentUserName() + "等" + 1 + "人");

		return null;
	}

	@Override
	public String releaseInnovationQualityApply(InnovationQualityApplyVo innovationQualityApplyVo) {
		flowCommonService.doCompleteTask(innovationQualityApplyVo, this.mapper
				, "质检中心申报完成"
				, FlowStatusEnum.PROJECT_RELEASE.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}

	@Override
	public PageInfo<InnovationQualityApply> todoList(InnovationQualityApplyVo vo) {

		Example example = new Example(InnovationQualityApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationQualityApply> finishedList(InnovationQualityApplyVo vo) {
		Example example = new Example(InnovationQualityApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationQualityApply> endList(InnovationQualityApplyVo vo) {
		Example example = new Example(InnovationQualityApply.class);
		Criteria criteria = getCriteria(vo, example);
		criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		example.orderBy("createTime").desc();
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}

	private Criteria getCriteria(InnovationQualityApply vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getApplyCenterName())){
			criteria.andLike("applyCenterName", "%" + vo.getApplyCenterName()+ "%");
		}
		return criteria;
	}
}
