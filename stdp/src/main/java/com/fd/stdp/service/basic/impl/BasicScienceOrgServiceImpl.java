package com.fd.stdp.service.basic.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.fd.stdp.beans.basic.BasicManageOrg;
import com.fd.stdp.beans.basic.BasicScienceOrgAssets;
import com.fd.stdp.beans.sys.BasicAreacode;
import com.fd.stdp.beans.sys.vo.BasicAreacodeVo;
import com.fd.stdp.dao.basic.BasicManageOrgMapper;
import com.fd.stdp.dao.basic.BasicScienceOrgAssetsMapper;
import com.fd.stdp.dao.sys.BasicAreacodeMapper;
import com.fd.stdp.dao.sys.SysUserMapper;
import com.fd.stdp.service.basic.BasicManageOrgService;
import com.fd.stdp.util.AppUserUtil;
import liquibase.pro.packaged.C;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.basic.vo.BasicScienceOrgVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicScienceOrgMapper;
import com.fd.stdp.service.basic.BasicScienceOrgService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 机构基础信息Service业务层处理
 * @date 2021-11-09
 */
@Service
@Transactional(readOnly = true)
public class BasicScienceOrgServiceImpl extends BaseServiceImpl<BasicScienceOrgMapper, BasicScienceOrg> implements BasicScienceOrgService {

    private static final Logger logger = LoggerFactory.getLogger(BasicScienceOrgServiceImpl.class);
    @Autowired
    private BasicScienceOrgMapper basicScienceOrgMapper;

    @Autowired
    private BasicScienceOrgAssetsMapper basicScienceOrgAssetsMapper;

    @Autowired
    private BasicManageOrgMapper basicManageOrgMapper;

    @Autowired
    private BasicAreacodeMapper basicAreacodeMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private BasicAreacodeMapper areacodeMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新机构基础信息
     *@param basicScienceOrg 机构基础信息对象
     *@return String 机构基础信息ID
     *@Author: yujianfei
     */
    public String saveOrUpdateBasicScienceOrg(BasicScienceOrgVo basicScienceOrgVo) {
        if (basicScienceOrgVo == null) {
            throw new ServiceException("数据异常");
        }
        // 行政区划名称AREA_NAME
        if (!CollectionUtils.isEmpty(basicScienceOrgVo.getAreaCodeList())) {
            if (basicScienceOrgVo.getAreaCodeList().size() == 1) {
                basicScienceOrgVo.setAreaCode(basicScienceOrgVo.getAreaCodeList().get(0));
                BasicAreacodeVo province = basicAreacodeMapper.findByAreaCode(basicScienceOrgVo.getAreaCodeList().get(0));
                basicScienceOrgVo.setAreaName(province.getAreaName());
            } else if (basicScienceOrgVo.getAreaCodeList().size() == 2)  {
                basicScienceOrgVo.setAreaCode(basicScienceOrgVo.getAreaCodeList().get(1));
                BasicAreacodeVo province = basicAreacodeMapper.findByAreaCode(basicScienceOrgVo.getAreaCodeList().get(0));
                BasicAreacodeVo city = basicAreacodeMapper.findByAreaCode(basicScienceOrgVo.getAreaCodeList().get(1));
                basicScienceOrgVo.setAreaName(province.getAreaName() + city.getAreaName());
            } else if (basicScienceOrgVo.getAreaCodeList().size() == 3) {
                basicScienceOrgVo.setAreaCode(basicScienceOrgVo.getAreaCodeList().get(2));
                BasicAreacodeVo city = basicAreacodeMapper.findByAreaCode(basicScienceOrgVo.getAreaCodeList().get(1));
                BasicAreacodeVo county = basicAreacodeMapper.findByAreaCode(basicScienceOrgVo.getAreaCodeList().get(2));
                basicScienceOrgVo.setAreaName(city.getAreaName() + county.getAreaName());
            }
        }
        if (StringUtils.isEmpty(basicScienceOrgVo.getId())) {
            if (!StringUtils.isEmpty(basicScienceOrgVo.getOrgCode())) {
                Example example = new Example(BasicScienceOrg.class);
                Criteria criteria = example.createCriteria();
                criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
                criteria.andEqualTo("orgCode", basicScienceOrgVo.getOrgCode());
                List<BasicScienceOrg> list = basicScienceOrgMapper.selectByExample(example);
                if (!CollectionUtils.isEmpty(list)) {
                    throw new ServiceException("机构代码重复");
                }
            }
            //新增
            basicScienceOrgVo.setId(UUIDUtils.getUUID());
            BasicManageOrg basicManageOrg = basicManageOrgMapper.selectByPrimaryKey(basicScienceOrgVo.getMorgId());
            if (basicManageOrg != null) {
                basicScienceOrgVo.setMorgName(basicManageOrg.getOrgName());
            }
            basicScienceOrgMapper.insertSelective(basicScienceOrgVo);
        } else {
            //避免页面传入修改
            basicScienceOrgVo.setYn(null);
            basicScienceOrgMapper.updateByPrimaryKeySelective(basicScienceOrgVo);
            
            if(!StringUtils.isEmpty(basicScienceOrgVo.getAreaName()) && !StringUtils.isEmpty(basicScienceOrgVo.getAreaCode())) {
            	sysUserMapper.updateAreaByScienceId(basicScienceOrgVo.getId(), basicScienceOrgVo.getAreaCode(), basicScienceOrgVo.getAreaName());
            }
        }
        
        BasicScienceOrgAssets sorgBasicScienceOrgAssets = new BasicScienceOrgAssets();
        sorgBasicScienceOrgAssets.setSorgId(basicScienceOrgVo.getId());
        //更新或者添加到assets表中
        if (!CollectionUtils.isEmpty(basicScienceOrgVo.getAssetList())) {
            if (!CollectionUtils.isEmpty(findById(basicScienceOrgVo.getId()).getAssetList())) {
                List<String> oldList = findById(basicScienceOrgVo.getId()).getAssetList().stream().map(BasicScienceOrgAssets::getId).collect(Collectors.toList());
                List<String> newList = basicScienceOrgVo.getAssetList().stream().map(BasicScienceOrgAssets::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(oldList)) {
                    List<String> deleteList = oldList.stream().filter(p -> !newList.contains(p)).collect(Collectors.toList());
                    //删除数据
                    for (String s : deleteList) {
                        BasicScienceOrgAssets deleteBasicScienceOrgAssets = new BasicScienceOrgAssets();
                        deleteBasicScienceOrgAssets.setId(s);
                        basicScienceOrgAssetsMapper.delete(deleteBasicScienceOrgAssets);
                    }
                }
            }
            for (BasicScienceOrgAssets basicScienceOrgAssets : basicScienceOrgVo.getAssetList()) {
                if (StringUtils.isEmpty(basicScienceOrgAssets.getId())) {
                    basicScienceOrgAssets.setSorgId(basicScienceOrgVo.getId());
                    basicScienceOrgAssets.setId(UUIDUtils.getUUID());
                    basicScienceOrgAssetsMapper.insert(basicScienceOrgAssets);
                } else {
                    // 判断是否已存在
                    Example example = new Example(BasicScienceOrgAssets.class);
                    Criteria criteria = example.createCriteria();
                    criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
                    criteria.andEqualTo("id", basicScienceOrgAssets.getId());
                    List<BasicScienceOrgAssets> list = basicScienceOrgAssetsMapper.selectByExample(example);
                    if (!CollectionUtils.isEmpty(list)) {
                        basicScienceOrgAssetsMapper.updateByPrimaryKeySelective(basicScienceOrgAssets);
                    }
                }
            }
        }else {
            //数据为空,删除所有的数据
            basicScienceOrgAssetsMapper.delete(sorgBasicScienceOrgAssets);
        }
        return basicScienceOrgVo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除机构基础信息
     *@param id void 机构基础信息ID
     *@Author: yujianfei
     */
    public void deleteBasicScienceOrg(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            BasicScienceOrg basicScienceOrg = basicScienceOrgMapper.selectByPrimaryKey(id);
            if (basicScienceOrg == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            BasicScienceOrg tembasicScienceOrg = new BasicScienceOrg();
            tembasicScienceOrg.setYn(CommonConstant.FLAG_NO);
            tembasicScienceOrg.setId(basicScienceOrg.getId());
            basicScienceOrgMapper.updateByPrimaryKeySelective(tembasicScienceOrg);
        }
    }

    /**
     * @param id
     * @return BasicScienceOrg
     * @Description: 查询机构基础信息详情
     * @Author: yujianfei
     */
    @Override
    public BasicScienceOrgVo findById(String id) {
        BasicScienceOrg basicScienceOrg = basicScienceOrgMapper.selectByPrimaryKey(id);
        if(basicScienceOrg == null){
            basicScienceOrg = new BasicScienceOrg();
            basicScienceOrg.setId(id);
        }
        Example example = new Example(BasicScienceOrgAssets.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        criteria.andEqualTo("sorgId", id);
        List<BasicScienceOrgAssets> list = basicScienceOrgAssetsMapper.selectByExample(example);
        BasicScienceOrgVo basicScienceOrgVo = new BasicScienceOrgVo();
        BeanUtils.copyProperties(basicScienceOrg, basicScienceOrgVo);
        if (!CollectionUtils.isEmpty(list)) {
            basicScienceOrgVo.setAssetList(list);
        }
        return basicScienceOrgVo;
    }


    /**
     * @param basicScienceOrgVo
     * @return PageInfo<BasicScienceOrg>
     * @Description: 分页查询机构基础信息
     * @Author: yujianfei
     */
    @Override
    public PageInfo<BasicScienceOrg> findPageByQuery(BasicScienceOrgVo basicScienceOrgVo) {
        PageHelper.startPage(basicScienceOrgVo.getPageNum(), basicScienceOrgVo.getPageSize());

        if (!StringUtils.isEmpty(basicScienceOrgVo.getAreaCode())) {
            String code = null;
            if ("0000".equals(basicScienceOrgVo.getAreaCode().substring(2, 6))) {
                code = basicScienceOrgVo.getAreaCode().substring(0, 2);

            } else if ("00".equals(basicScienceOrgVo.getAreaCode().substring(4, 6))) {
                code = basicScienceOrgVo.getAreaCode().substring(0, 4);
            } else {
                code = basicScienceOrgVo.getAreaCode();
            }
            basicScienceOrgVo.setAreaCode(code);
        }
        List<BasicScienceOrg> basicScienceOrgList = basicScienceOrgMapper.findPageByQuery(basicScienceOrgVo);
        List<BasicAreacode> basicAreacodeList = basicAreacodeMapper.selectAll();
        basicScienceOrgList.stream()
                .filter(org->org.getAreaCode() != null && org.getAreaCode().length() == 6)
                .forEach(org->{
                    if(org.getAreaCode().endsWith("0000")) {
                        org.setCity(basicAreacodeList.stream().filter(code->code.getAreaCode().equals(org.getAreaCode())).findAny().get().getAreaName());
                    } else if(org.getAreaCode().endsWith("00")) {
                        org.setCity(basicAreacodeList.stream().filter(code->code.getAreaCode().equals(org.getAreaCode().substring(0, 2) + "0000")).findAny().get().getAreaName());
                        org.setCounty(basicAreacodeList.stream().filter(code->code.getAreaCode().equals(org.getAreaCode())).findAny().get().getAreaName());
                    } else {
                        org.setCity(basicAreacodeList.stream().filter(code->code.getAreaCode().equals(org.getAreaCode().substring(0, 4) + "00")).findAny().get().getAreaName());
                        org.setCounty(basicAreacodeList.stream().filter(code->code.getAreaCode().equals(org.getAreaCode())).findAny().get().getAreaName());
                    }
        });
        
        // 修复linkTel字段科学计数法问题
        basicScienceOrgList.forEach(org -> {
            if (org.getLinkTel() != null && org.getLinkTel().contains("E")) {
                // 如果包含科学计数法标识，尝试转换回原始格式
                try {
                    // 检查是否为科学计数法格式
                    if (org.getLinkTel().matches(".*[0-9]+\\.?[0-9]*E[+-]?[0-9]+.*")) {
                        // 将科学计数法转换为普通数字字符串
                        java.math.BigDecimal bd = new java.math.BigDecimal(org.getLinkTel());
                        org.setLinkTel(bd.toPlainString());
                    }
                } catch (NumberFormatException e) {
                    // 如果转换失败，保持原值
                    logger.warn("Failed to convert linkTel from scientific notation: {}", org.getLinkTel());
                }
            }
        });

        return new PageInfo<BasicScienceOrg>(basicScienceOrgList);
    }

    /**
     * @return List<BasicScienceOrg>
     * @Description: 查询所有机构列表（只返回机构名称和机构代码）
     * @Author: zgx
     */
    @Override
    public List<BasicScienceOrg> findAllOrgList() {
        Example example = new Example(BasicScienceOrg.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        example.selectProperties("orgName", "orgCode", "id");
        return basicScienceOrgMapper.selectByExample(example);
    }

    /**
     * @param basicScienceOrgVo 查询条件
     * @return PageInfo<BasicScienceOrg>
     * @Description: 分页查询机构列表（只返回机构名称和机构代码）
     * @Author: zgx
     */
    @Override
    public PageInfo<BasicScienceOrg> findOrgListByPage(BasicScienceOrgVo basicScienceOrgVo) {
        PageHelper.startPage(basicScienceOrgVo.getPageNum(), basicScienceOrgVo.getPageSize());
        
        Example example = new Example(BasicScienceOrg.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);

        if (!StringUtils.isEmpty(basicScienceOrgVo.getKeyword())) {
            Criteria orCriteria = example.or();
            orCriteria.andEqualTo("yn", CommonConstant.FLAG_YES);
            orCriteria.andLike("orgCode", "%" + basicScienceOrgVo.getKeyword() + "%");
            
            criteria.andLike("orgName", "%" + basicScienceOrgVo.getKeyword() + "%");
        }
        
        // 只选择需要的字段
        example.selectProperties("id", "orgName", "orgCode");

        
        List<BasicScienceOrg> orgList = basicScienceOrgMapper.selectByExample(example);
        return new PageInfo<>(orgList);
    }
}
