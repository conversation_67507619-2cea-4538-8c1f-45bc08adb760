package com.fd.stdp.service.flowable.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fd.stdp.beans.flowable.FlwForm;
import com.fd.stdp.beans.flowable.vo.FlwFormVo;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.dao.flowable.FlwFormMapper;
import com.fd.stdp.service.flowable.FlwFormService;
import com.fd.stdp.util.StringUtils;
import com.fd.stdp.util.UUIDUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 *@Description: 流程表单
 *@Author: linqiang
 *@Date: 2021-10-15 09:20:44
 */
@Service
@Transactional(readOnly = true)
public class FlwFormServiceImpl extends BaseServiceImpl<FlwFormMapper, FlwForm> implements FlwFormService{

	public static final Logger logger = LoggerFactory.getLogger(FlwFormServiceImpl.class);
	
	@Autowired
	private FlwFormMapper flwFormMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新流程表单
	 *@param flwForm 流程表单对象
	 *@return String 流程表单ID
	 *@Author: linqiang
	 */
	public String saveOrUpdateFlwForm(FlwForm flwForm) {
		if(flwForm==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(flwForm.getId())){
			//新增
			flwForm.setId(UUIDUtils.getUUID());
			flwFormMapper.insertSelective(flwForm);
		}else{
			//避免页面传入修改
			flwForm.setYn(null);
			flwFormMapper.updateByPrimaryKeySelective(flwForm);
		}
		return flwForm.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除流程表单
	 *@param id void 流程表单ID
	 *@Author: linqiang
	 */
	public void deleteFlwForm(String id) {
		//TODO 做判断后方能执行删除
		FlwForm flwForm=flwFormMapper.selectByPrimaryKey(id);
		if(flwForm==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		FlwForm temflwForm=new FlwForm();
		temflwForm.setYn(CommonConstant.FLAG_NO);
		temflwForm.setId(flwForm.getId());
		flwFormMapper.updateByPrimaryKeySelective(temflwForm);
	}

	/**
	 *@Description: 查询流程表单详情
	 *@param id
	 *@return FlwForm
	 *@Author: linqiang
	 */
    @Override
	public FlwForm findById(String id) {
		return flwFormMapper.selectByPrimaryKey(id);
	}


	/**
	 *@Description: 分页查询流程表单
	 *@param flwFormVo
	 *@return PageInfo<FlwForm>
	 *@Author: linqiang
	 */
	@Override
	public PageInfo<FlwForm> findPageByQuery(FlwFormVo flwFormVo) {
		PageHelper.startPage(flwFormVo.getPageNum(),flwFormVo.getPageSize());
		Example example=new Example(FlwForm.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if(!StringUtils.isEmpty(flwFormVo.getFormName())) {
    		criteria.andLike("formName","%"+flwFormVo.getFormName()+"%");
    	}
    	if(!StringUtils.isEmpty(flwFormVo.getFormContent())) {
    		criteria.andLike("formContent","%"+flwFormVo.getFormContent()+"%");
    	}
		List<FlwForm> flwFormList=flwFormMapper.selectByExample(example);
		return new PageInfo<FlwForm>(flwFormList);
	}

	@Override
	public List<FlwForm> findFlwFormList(FlwForm flwForm) {
		Example example =new Example(FlwForm.class);
    	Criteria  criteria = example.createCriteria();
    	if(!StringUtils.isEmpty(flwForm.getFormName())) {
    		criteria.andLike("formName","%"+flwForm.getFormName()+"%");
    	}
    	if(!StringUtils.isEmpty(flwForm.getFormContent())) {
    		criteria.andLike("formContent","%"+flwForm.getFormContent()+"%");
    	}
    	return flwFormMapper.selectByExample(example);
	}

	@Override
	@Transactional(readOnly = false)
	public int deleteFlwFormByIds(String[] formIds) {
		if(formIds==null||formIds.length==0) {
			throw new ServiceException("数据异常");
		}
		int count=0;
		for(String id:formIds) {
			count+=flwFormMapper.deleteByPrimaryKey(id);
		}
		return count;
	}

}
