package com.fd.stdp.service.project;

import java.util.List;

import com.fd.stdp.beans.project.vo.ProjectApplyExpertMumberVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractAccept;
import com.fd.stdp.beans.project.vo.ProjectContractAcceptVo;
/**
 *@Description: 任务书验收表单
 *@Author: wangsh
 *@Date: 2022-03-03 10:16:28
 */
public interface ProjectContractAcceptService {

	/**
	 *@Description: 保存或更新任务书验收表单
	 *@param projectContractAccept 任务书验收表单对象
	 *@return String 任务书验收表单ID
	 *@Author: wangsh
	 */
	String saveOrUpdateProjectContractAccept(ProjectContractAcceptVo projectContractAccept);
	
	/**
	 *@Description: 删除任务书验收表单
	 *@param id void 任务书验收表单ID
	 *@Author: wangsh
	 */
	void deleteProjectContractAccept(String id);

	/**
	 * @Description: 批量删除任务书验收表单
	 * @param ids
	 */
    void deleteMultiProjectContractAccept(List<String> ids);

	/**
	 *@Description: 查询任务书验收表单详情
	 *@param id
	 *@return ProjectContractAccept
	 *@Author: wangsh
	 */
	ProjectContractAccept findById(String id);

	/**
	 *@Description: 分页查询任务书验收表单
	 *@param projectContractAcceptVo
	 *@return PageInfo<ProjectContractAccept>
	 *@Author: wangsh
	 */
	PageInfo<ProjectContractAccept> findPageByQuery(ProjectContractAcceptVo projectContractAcceptVo);
	
	
	/**
	 * 提交
	 * @param projectContractAcceptVo
	 * @return
	 */
    String submitProjectContractAccept(ProjectContractAcceptVo projectContractAcceptVo);

	/**
	 * 审核
	 * @param projectContractAcceptVo
	 * @return
	 */
	String auditProjectContractAccept(ProjectContractAcceptVo projectContractAcceptVo);

	/**
	 * 退回
	 * @param projectContractAcceptVo
	 * @return
	 */
	String sendBackProjectContractAccept(ProjectContractAcceptVo projectContractAcceptVo);

	/**
	 * 任务书下达
	 * @param projectContractAcceptVo
	 * @return
	 */
	String releaseProjectContractAccept(ProjectContractAcceptVo projectContractAcceptVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<ProjectContractAccept> todoList(ProjectContractAcceptVo projectContractAcceptVo);

	/**
	 * 已办列表
	 */
	PageInfo<ProjectContractAccept> finishedList(ProjectContractAcceptVo projectContractAcceptVo);

	/**
	 * 已完成列表
	 */
	PageInfo<ProjectContractAccept> endList(ProjectContractAcceptVo projectContractAcceptVo);

	String expertSubmitProjectContractAccept(ProjectApplyExpertMumberVo vo);
}
