package com.fd.stdp.service.notice.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.notice.NoticeApply;
import com.fd.stdp.beans.notice.vo.NoticeApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.notice.NoticeApplyMapper;
import com.fd.stdp.service.notice.NoticeApplyService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import static com.fd.stdp.common.BaseController.getUserRoleList;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 
 *@Author: wangsh
 *@Date: 2022-03-15 09:45:58
 */
public class NoticeApplyServiceImpl extends BaseServiceImpl<NoticeApplyMapper, NoticeApply> implements NoticeApplyService{

	public static final Logger logger = LoggerFactory.getLogger(NoticeApplyServiceImpl.class);
	
	@Autowired
	private NoticeApplyMapper noticeApplyMapper;
	@Autowired
	private BasicFileAppendixService fileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新
	 *@param noticeApply 对象
	 *@return String ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateNoticeApply(NoticeApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		vo.setNoticeDate(new Date());
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			noticeApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			noticeApplyMapper.updateByPrimaryKeySelective(vo);
		}

		if(vo.getFiles() != null){
			fileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除
	 *@param id void ID
	 *@Author: wangsh
	 */
	public void deleteNoticeApply(String id) {
		//TODO 做判断后方能执行删除
		NoticeApply noticeApply=noticeApplyMapper.selectByPrimaryKey(id);
		if(noticeApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		NoticeApply temnoticeApply=new NoticeApply();
		temnoticeApply.setYn(CommonConstant.FLAG_NO);
		temnoticeApply.setId(noticeApply.getId());
		noticeApplyMapper.updateByPrimaryKeySelective(temnoticeApply);
	}

    /**
     * @Description: 批量删除
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiNoticeApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteNoticeApply(id));
	}

	@Override
	/**
	 *@Description: 查询详情
	 *@param id
	 *@return NoticeApply
	 *@Author: wangsh
	 */
	public NoticeApply findById(String id) {
		NoticeApply noticeApply = noticeApplyMapper.selectByPrimaryKey(id);
		NoticeApplyVo vo = new NoticeApplyVo();
		BeanUtils.copyProperties(noticeApply, vo);
		vo.setFiles(fileAppendixService.findByFormId(id));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询
	 *@param noticeApplyVo
	 *@return PageInfo<NoticeApply>
	 *@Author: wangsh
	 */
	public PageInfo<NoticeApply> findPageByQuery(NoticeApplyVo noticeApplyVo) {
		List<String> roleList = getUserRoleList().stream().map(role->role.getRoleCode() + "@ROLE").collect(Collectors.toList());
		String viewAll = roleList.stream().filter(role->StringUtils.equals(AssigneeConstant.DEPT_PROVINCE_ROLE, role) || StringUtils.equals(AssigneeConstant.SUPERADMIN_ROLE, role))
				.findAny().orElse(null);
		PageHelper.startPage(noticeApplyVo.getPageNum(),noticeApplyVo.getPageSize());
		Example example=new Example(NoticeApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if(!StringUtils.isEmpty(noticeApplyVo.getNoticeType())){
			criteria.andEqualTo("noticeType",noticeApplyVo.getNoticeType());
		}
		if(viewAll == null) {
			// 是否有浏览全部的权限
			criteria.andEqualTo("isOpen", 1);
		}
		example.setOrderByClause(" SUBMIT_TIME desc");
		
		List<NoticeApply> noticeApplyList=noticeApplyMapper.selectByExample(example);
		return new PageInfo<NoticeApply>(noticeApplyList);
	}

	@Override
	public void open(String id) {
		NoticeApply noticeApply = findById(id);
		noticeApply.setIsOpen(1);
		this.mapper.updateByPrimaryKeySelective(noticeApply);
	}

	@Override
	public void close(String id) {
		NoticeApply noticeApply = findById(id);
		noticeApply.setIsOpen(0);
		this.mapper.updateByPrimaryKeySelective(noticeApply);
	}

	private Criteria getCriteria(NoticeApply vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
		return criteria;
	}
}
