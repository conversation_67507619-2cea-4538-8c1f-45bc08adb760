package com.fd.stdp.service.project.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractTeams;
import com.fd.stdp.beans.project.vo.ProjectContractTeamsVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectContractTeamsMapper;
import com.fd.stdp.service.project.ProjectContractTeamsService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 项目组成员
 *@Author: wangsh
 *@Date: 2022-01-12 14:18:27
 */
public class ProjectContractTeamsServiceImpl extends BaseServiceImpl<ProjectContractTeamsMapper, ProjectContractTeams> implements ProjectContractTeamsService{

	public static final Logger logger = LoggerFactory.getLogger(ProjectContractTeamsServiceImpl.class);
	
	@Autowired
	private ProjectContractTeamsMapper projectContractTeamsMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新项目组成员
	 *@param projectContractTeams 项目组成员对象
	 *@return String 项目组成员ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateProjectContractTeams(ProjectContractTeams projectContractTeams) {
		if(projectContractTeams==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(projectContractTeams.getId())){
			//新增
			projectContractTeams.setId(UUIDUtils.getUUID());
			projectContractTeamsMapper.insertSelective(projectContractTeams);
		}else{
			//避免页面传入修改
			projectContractTeams.setYn(null);
			projectContractTeamsMapper.updateByPrimaryKeySelective(projectContractTeams);
		}
		return projectContractTeams.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除项目组成员
	 *@param id void 项目组成员ID
	 *@Author: wangsh
	 */
	public void deleteProjectContractTeams(String id) {
		//TODO 做判断后方能执行删除
		ProjectContractTeams projectContractTeams=projectContractTeamsMapper.selectByPrimaryKey(id);
		if(projectContractTeams==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		ProjectContractTeams temprojectContractTeams=new ProjectContractTeams();
		temprojectContractTeams.setYn(CommonConstant.FLAG_NO);
		temprojectContractTeams.setId(projectContractTeams.getId());
		projectContractTeamsMapper.updateByPrimaryKeySelective(temprojectContractTeams);
	}

    /**
     * @Description: 批量删除项目组成员
     * @param projectContractTeamsVo
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiProjectContractTeams(ProjectContractTeamsVo projectContractTeamsVo) {
		projectContractTeamsVo.getIds().stream().forEach(id-> this.deleteProjectContractTeams(id));
	}

	@Override
	/**
	 *@Description: 查询项目组成员详情
	 *@param id
	 *@return ProjectContractTeams
	 *@Author: wangsh
	 */
	public ProjectContractTeams findById(String id) {
		return projectContractTeamsMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询项目组成员
	 *@param projectContractTeamsVo
	 *@return PageInfo<ProjectContractTeams>
	 *@Author: wangsh
	 */
	public PageInfo<ProjectContractTeams> findPageByQuery(ProjectContractTeamsVo projectContractTeamsVo) {
		PageHelper.startPage(projectContractTeamsVo.getPageNum(),projectContractTeamsVo.getPageSize());
		Example example=new Example(ProjectContractTeams.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(projectContractTeamsVo.getName())){
		//	criteria.andEqualTo(projectContractTeamsVo.getName());
		//}
		List<ProjectContractTeams> projectContractTeamsList=projectContractTeamsMapper.selectByExample(example);
		return new PageInfo<ProjectContractTeams>(projectContractTeamsList);
	}

}
