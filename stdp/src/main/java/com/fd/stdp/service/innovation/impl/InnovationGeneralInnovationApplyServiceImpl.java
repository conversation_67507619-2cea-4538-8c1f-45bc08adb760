package com.fd.stdp.service.innovation.impl;

import java.util.List;

import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationGeneralInnovationApply;
import com.fd.stdp.beans.innovation.vo.InnovationGeneralInnovationApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationGeneralInnovationApplyMapper;
import com.fd.stdp.service.innovation.InnovationGeneralInnovationApplyService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 总局技术创新中心
 *@Author: wangsh
 *@Date: 2022-03-07 19:46:04
 */
public class InnovationGeneralInnovationApplyServiceImpl extends BaseServiceImpl<InnovationGeneralInnovationApplyMapper, InnovationGeneralInnovationApply> implements InnovationGeneralInnovationApplyService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationGeneralInnovationApplyServiceImpl.class);
	
	@Autowired
	private InnovationGeneralInnovationApplyMapper innovationGeneralInnovationApplyMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新总局技术创新中心
	 *@param innovationGeneralInnovationApply 总局技术创新中心对象
	 *@return String 总局技术创新中心ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationGeneralInnovationApply(InnovationGeneralInnovationApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationGeneralInnovationApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationGeneralInnovationApplyMapper.updateByPrimaryKeySelective(vo);
		}

		/**
		 * 附件
		 */
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除总局技术创新中心
	 *@param id void 总局技术创新中心ID
	 *@Author: wangsh
	 */
	public void deleteInnovationGeneralInnovationApply(String id) {
		//TODO 做判断后方能执行删除
		InnovationGeneralInnovationApply innovationGeneralInnovationApply=innovationGeneralInnovationApplyMapper.selectByPrimaryKey(id);
		if(innovationGeneralInnovationApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationGeneralInnovationApply teminnovationGeneralInnovationApply=new InnovationGeneralInnovationApply();
		teminnovationGeneralInnovationApply.setYn(CommonConstant.FLAG_NO);
		teminnovationGeneralInnovationApply.setId(innovationGeneralInnovationApply.getId());
		innovationGeneralInnovationApplyMapper.updateByPrimaryKeySelective(teminnovationGeneralInnovationApply);
	}

    /**
     * @Description: 批量删除总局技术创新中心
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationGeneralInnovationApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationGeneralInnovationApply(id));
	}

	@Override
	/**
	 *@Description: 查询总局技术创新中心详情
	 *@param id
	 *@return InnovationGeneralInnovationApply
	 *@Author: wangsh
	 */
	public InnovationGeneralInnovationApply findById(String id) {
		InnovationGeneralInnovationApply apply = innovationGeneralInnovationApplyMapper.selectByPrimaryKey(id);
		InnovationGeneralInnovationApplyVo vo = new InnovationGeneralInnovationApplyVo();
		BeanUtils.copyProperties(apply, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询总局技术创新中心
	 *@param innovationGeneralInnovationApplyVo
	 *@return PageInfo<InnovationGeneralInnovationApply>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationGeneralInnovationApply> findPageByQuery(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApplyVo) {
		PageHelper.startPage(innovationGeneralInnovationApplyVo.getPageNum(),innovationGeneralInnovationApplyVo.getPageSize());
		Example example=new Example(InnovationGeneralInnovationApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationGeneralInnovationApplyVo.getName())){
		//	criteria.andEqualTo(innovationGeneralInnovationApplyVo.getName());
		//}
		List<InnovationGeneralInnovationApply> innovationGeneralInnovationApplyList=innovationGeneralInnovationApplyMapper.selectByExample(example);
		return new PageInfo<InnovationGeneralInnovationApply>(innovationGeneralInnovationApplyList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationGeneralInnovationApply(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApplyVo) {
		String id = this.saveOrUpdateInnovationGeneralInnovationApply(innovationGeneralInnovationApplyVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innovationGeneralInnovationApplyVo, this.mapper,
				StringUtils.isNotBlank(innovationGeneralInnovationApplyVo.getAuditAdvice())?innovationGeneralInnovationApplyVo.getAuditAdvice():"提交总局技术创新中心");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationGeneralInnovationApply(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApplyVo) {
		flowCommonService.doFlowStepAudit(innovationGeneralInnovationApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationGeneralInnovationApplyVo.getAuditAdvice()) ? innovationGeneralInnovationApplyVo.getAuditAdvice() : "总局技术创新中心审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationGeneralInnovationApplyVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationGeneralInnovationApply(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApplyVo) {
		flowCommonService.doFlowStepSendBack(innovationGeneralInnovationApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationGeneralInnovationApplyVo.getAuditAdvice())?innovationGeneralInnovationApplyVo.getAuditAdvice():"总局技术创新中心退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationGeneralInnovationApply(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApplyVo) {
		flowCommonService.doCompleteTask(innovationGeneralInnovationApplyVo, this.mapper
				, "总局技术创新中心任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnovationGeneralInnovationApply> todoList(InnovationGeneralInnovationApplyVo vo) {

		Example example = new Example(InnovationGeneralInnovationApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationGeneralInnovationApply> finishedList(InnovationGeneralInnovationApplyVo vo) {
		Example example = new Example(InnovationGeneralInnovationApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnovationGeneralInnovationApply> endList(InnovationGeneralInnovationApplyVo vo) {
        Example example = new Example(InnovationGeneralInnovationApply.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(InnovationGeneralInnovationApply vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getLaboratory())){
			criteria.andLike("laboratory", "%" + vo.getLaboratory()+ "%");
		}
		return criteria;
	}
}
