package com.fd.stdp.service.project;

import java.util.List;

import com.fd.stdp.beans.project.vo.ProjectApplyExpertsVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractInterimReport;
import com.fd.stdp.beans.project.vo.ProjectContractInterimReportVo;
/**
 *@Description: 任务书中期检查表单
 *@Author: wangsh
 *@Date: 2022-02-14 14:37:22
 */
public interface ProjectContractInterimReportService {

	/**
	 *@Description: 保存或更新任务书中期检查表单
	 *@param projectContractInterimReport 任务书中期检查表单对象
	 *@return String 任务书中期检查表单ID
	 *@Author: wangsh
	 */
	String saveOrUpdateProjectContractInterimReport(ProjectContractInterimReportVo projectContractInterimReport);
	
	/**
	 *@Description: 删除任务书中期检查表单
	 *@param id void 任务书中期检查表单ID
	 *@Author: wangsh
	 */
	void deleteProjectContractInterimReport(String id);

	/**
	 * @Description: 批量删除任务书中期检查表单
	 * @param ids
	 */
    void deleteMultiProjectContractInterimReport(List<String> ids);

	/**
	 *@Description: 查询任务书中期检查表单详情
	 *@param id
	 *@return ProjectContractInterimReport
	 *@Author: wangsh
	 */
	ProjectContractInterimReport findById(String id);

	/**
	 *@Description: 分页查询任务书中期检查表单
	 *@param projectContractInterimReportVo
	 *@return PageInfo<ProjectContractInterimReport>
	 *@Author: wangsh
	 */
	PageInfo<ProjectContractInterimReport> findPageByQuery(ProjectContractInterimReportVo projectContractInterimReportVo);
	
	
	/**
	 * 提交
	 * @param projectContractInterimReportVo
	 * @return
	 */
    String submitProjectContractInterimReport(ProjectContractInterimReportVo projectContractInterimReportVo);

	/**
	 * 审核
	 * @param projectContractInterimReportVo
	 * @return
	 */
	String auditProjectContractInterimReport(ProjectContractInterimReportVo projectContractInterimReportVo);

	/**
	 * 退回
	 * @param projectContractInterimReportVo
	 * @return
	 */
	String sendBackProjectContractInterimReport(ProjectContractInterimReportVo projectContractInterimReportVo);

	/**
	 * 任务书下达
	 * @param projectContractInterimReportVo
	 * @return
	 */
	String releaseProjectContractInterimReport(ProjectContractInterimReportVo projectContractInterimReportVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<ProjectContractInterimReport> todoList(ProjectContractInterimReportVo projectContractInterimReportVo);

	/**
	 * 已办列表
	 */
	PageInfo<ProjectContractInterimReport> finishedList(ProjectContractInterimReportVo projectContractInterimReportVo);

	/**
	 * 已完成列表
	 */
	PageInfo<ProjectContractInterimReport> endList(ProjectContractInterimReportVo projectContractInterimReportVo);

    String expertSubmitProjectContractInterimReport(ProjectApplyExpertsVo projectApplyExpertsVo);
}
