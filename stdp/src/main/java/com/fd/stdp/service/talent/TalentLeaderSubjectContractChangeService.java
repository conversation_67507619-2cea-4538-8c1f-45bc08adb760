package com.fd.stdp.service.talent;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectContractChange;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectContractChangeVo;
/**
 *@Description: 学科带头人建设任务书变更
 *@Author: wangsh
 *@Date: 2022-02-16 09:00:53
 */
public interface TalentLeaderSubjectContractChangeService {

	/**
	 *@Description: 保存或更新学科带头人建设任务书变更
	 *@param talentLeaderSubjectContractChange 学科带头人建设任务书变更对象
	 *@return String 学科带头人建设任务书变更ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTalentLeaderSubjectContractChange(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChange);
	
	/**
	 *@Description: 删除学科带头人建设任务书变更
	 *@param id void 学科带头人建设任务书变更ID
	 *@Author: wangsh
	 */
	void deleteTalentLeaderSubjectContractChange(String id);

	/**
	 * @Description: 批量删除学科带头人建设任务书变更
	 * @param ids
	 */
    void deleteMultiTalentLeaderSubjectContractChange(List<String> ids);

	/**
	 *@Description: 查询学科带头人建设任务书变更详情
	 *@param id
	 *@return TalentLeaderSubjectContractChange
	 *@Author: wangsh
	 */
	TalentLeaderSubjectContractChange findById(String id);

	/**
	 *@Description: 分页查询学科带头人建设任务书变更
	 *@param talentLeaderSubjectContractChangeVo
	 *@return PageInfo<TalentLeaderSubjectContractChange>
	 *@Author: wangsh
	 */
	PageInfo<TalentLeaderSubjectContractChange> findPageByQuery(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChangeVo);
	
	
	/**
	 * 提交
	 * @param talentLeaderSubjectContractChangeVo
	 * @return
	 */
    String submitTalentLeaderSubjectContractChange(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChangeVo);

	/**
	 * 审核
	 * @param talentLeaderSubjectContractChangeVo
	 * @return
	 */
	String auditTalentLeaderSubjectContractChange(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChangeVo);

	/**
	 * 退回
	 * @param talentLeaderSubjectContractChangeVo
	 * @return
	 */
	String sendBackTalentLeaderSubjectContractChange(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChangeVo);

	/**
	 * 任务书下达
	 * @param talentLeaderSubjectContractChangeVo
	 * @return
	 */
	String releaseTalentLeaderSubjectContractChange(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChangeVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<TalentLeaderSubjectContractChange> todoList(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChangeVo);

	/**
	 * 已办列表
	 */
	PageInfo<TalentLeaderSubjectContractChange> finishedList(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChangeVo);

	/**
	 * 已完成列表
	 */
	PageInfo<TalentLeaderSubjectContractChange> endList(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChangeVo);
}
