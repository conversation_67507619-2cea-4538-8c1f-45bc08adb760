package com.fd.stdp.service.flowable.impl;

import com.fd.stdp.beans.basic.BasicManageOrg;
import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.flowable.FlowTaskDto;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.beans.sys.SysRole;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.beans.tech.vo.TechAchievementVo;
import com.fd.stdp.common.BaseEntity;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.mybatis.BaseInfoMapper;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.constant.ProcessConstants;
import com.fd.stdp.dao.basic.BasicManageOrgMapper;
import com.fd.stdp.dao.basic.BasicScienceOrgMapper;
import com.fd.stdp.dao.project.ProjectApplyInfoMapper;
import com.fd.stdp.dao.tech.TechAchievementMapper;
import com.fd.stdp.enums.FlowComment;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.sys.SysUserService;
import com.fd.stdp.service.user.SysUserUtilService;
import com.fd.stdp.util.AppUserUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.common.example.SelectByExampleMapper;
import tk.mybatis.mapper.entity.Example;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Struct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.fd.stdp.common.BaseController.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 流程的通用执行类
 * @date 2022/1/4 14:44
 */
@Service
@Transactional(readOnly = true)
public class FlowCommonServiceImpl implements FlowCommonService {

    @Autowired
    private FlowApiService flowApiService;
    @Autowired
    protected TaskService taskService;
    @Autowired
    protected RuntimeService runtimeService;
    @Autowired
    protected RepositoryService repositoryService;
    @Autowired
    protected HistoryService historyService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysUserUtilService sysUserUtilService;

    @Autowired
    private BasicScienceOrgMapper basicScienceOrgMapper;

    /**
     * 创建并获取一个FlowTaskVo对象
     * @param taskId
     * @param businessKey
     * @param comment
     * @param assignee
     * @param values
     * @return
     */
    @Override
    public FlowTaskVo createFlowTaskVo(String taskId, String businessKey, String comment, String assignee, Map<String, Object> values) {
        FlowTaskVo flowTaskVo = new FlowTaskVo();
        flowTaskVo.setTaskId(taskId);
        flowTaskVo.setBusinessKey(businessKey);
        flowTaskVo.setUserId(getCurrentUserId());
        flowTaskVo.setUserName(getCurrentUserName());
        flowTaskVo.setComment(comment);
        flowTaskVo.setAssignee(assignee);
        flowTaskVo.setValues(values);
        return flowTaskVo;
    }

    /**
     * 开启一个流程
     * @param processInstanceKey 流程标识
     * @param obj 业务对象
     * @param mapper 业务mapper
     * @param comment 描述
     * @return 流程id
     */
    @Override
    public String doFlowStart(String processInstanceKey, BaseEntity obj, BaseInfoMapper mapper, String comment) {
        // 通过反射获取 getFlowStatus setFlowStatus setFlowUser 3个方法
        Method getFlowStatusMethod = null;
        Method setFlowStatusMethod = null;
        Method setFlowUser = null;
        try {
            // 保证我们对象的数据是库中最新的
            obj = (BaseEntity) mapper.selectByPrimaryKey(obj.getId());
            getFlowStatusMethod = obj.getClass().getMethod("getFlowStatus");
            setFlowStatusMethod = obj.getClass().getMethod("setFlowStatus", String.class);
            setFlowUser = obj.getClass().getMethod("setFlowUser", String.class);
        } catch (NoSuchMethodException e) {
            // 若获取方法失败，则传入的对象obj无法进行流程操作
            e.printStackTrace();
            throw new ServiceException("流程错误，操作非流程对象");
        }
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            String taskId = null;
            if(StringUtils.isNotBlank((CharSequence) getFlowStatusMethod.invoke(obj))){
                taskId = flowApiService.getTaskId(obj.getId());
            }
            if(taskId != null){
                // 如果taskid非空 则流程已存在 不能重复开启流程
                return taskId;
            }
            FlowTaskVo flowTaskVo = createFlowTaskVo(taskId, obj.getId(), comment, AssigneeConstant.ORG_HEAD_ROLE, null);
            // 设置开启流程后的流程状态与流程所属角色（第一个状态理应在新增的业务中添加，非必须）
            setFlowUser.invoke(obj, flowTaskVo.getAssignee());
            setFlowStatusMethod.invoke(obj, FlowStatusEnum.WAIT_APPLY.getCode());
            mapper.updateByPrimaryKeySelective(obj);

            map.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());
            flowTaskVo.setValues(map);
            flowTaskVo.setProcessInstanceKey(processInstanceKey);
            taskId = flowApiService.startProcessInstanceByKey(flowTaskVo);
            return taskId;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("流程错误，流程启动异常");
        }
    }

    /**
     * 执行工作流的提交任务 通用
     * @param processInstanceKey 流程标识
     * @param obj 业务对象
     * @param mapper 业务mapper
     * @param comment 流程意见
     */
    @Override
    @Transactional(readOnly = false)
    public synchronized void doFlowStepSubmit(String processInstanceKey, BaseEntity obj, BaseInfoMapper mapper, String comment){
        Method getFlowStatusMethod = null;
        Method setFlowStatusMethod = null;
        Method setFlowUser = null;
        try {
            obj = (BaseEntity) mapper.selectByPrimaryKey(obj.getId());
            getFlowStatusMethod = obj.getClass().getMethod("getFlowStatus");
            setFlowStatusMethod = obj.getClass().getMethod("setFlowStatus", String.class);
            setFlowUser = obj.getClass().getMethod("setFlowUser", String.class);
            if(!StringUtils.equals((CharSequence) getFlowStatusMethod.invoke(obj), FlowStatusEnum.WAIT_APPLY.getCode())) {
                throw new ServiceException("流程状态异常，请确认");
            }
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
            throw new ServiceException("流程错误，操作非流程对象");
        }
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            String taskId = flowApiService.getTaskId(obj.getId());
            if (StringUtils.isBlank(taskId)) {
                // 不存在任务，创建任务
                taskId = doFlowStart(processInstanceKey, obj, mapper, "流程开启");
            }
            FlowTaskVo flowTaskVo = createFlowTaskVo(taskId, obj.getId(), comment, null, null);
            flowTaskVo.setAssignee(AssigneeConstant.ORG_ADMIN_ROLE);
            setFlowStatusMethod.invoke(obj, FlowStatusEnum.ORG_AUDIT.getCode());
            // 此处设置codeType也无必要
            map.put("CODETYPE", -1);
            setFlowUser.invoke(obj, flowTaskVo.getAssignee());
            mapper.updateByPrimaryKeySelective(obj);

            map.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());
            flowTaskVo.setValues(map);
            flowTaskVo.setTaskId(taskId);
            flowApiService.completeTask(flowTaskVo);

        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("流程错误，流程执行异常");
        }
    }

    /**
     * 执行工作流的审核任务 通用
     * @param obj 业务对象
     * @param mapper 业务mapper
     * @param comment 流程意见
     * @param nextFlowStatus 审核结束后的下一个状态
     */
    @Override
    @Transactional(readOnly = false)
    public void doFlowStepAudit(BaseEntity obj, BaseInfoMapper mapper, String comment, String nextFlowStatus) {
        doFlowStepAudit(obj, mapper, comment, nextFlowStatus, AssigneeConstant.DEPT_PROVINCE_ROLE);
    }

    /**
     * 执行工作流的审核任务 通用
     * @param obj 业务对象
     * @param mapper 业务mapper
     * @param comment 流程意见
     * @param nextFlowStatus 审核结束后的下一个状态
     * @param nextFlowAssgine 审核结束后的下一个状态指定角色
     *
     * @Description: 审核分为 机构管理员/县管理部门/市管理部门/省局审核4种情况
     * 其中机构管理员审核之后 根据机构所属类型(省级机构 市级机构 县级机构)设置下一步的审核部门 通过当前用户的areaCode结尾的0的数量判断
     * 县市审核后自动流转到下一级
     * 省局审核后通过传入的参数中进行下一步流转
     */
    @Override
    @Transactional(readOnly = false)
    public synchronized void doFlowStepAudit(BaseEntity obj, BaseInfoMapper mapper, String comment, String nextFlowStatus, String nextFlowAssgine) {
        Method getFlowStatusMethod = null;
        Method setFlowStatusMethod = null;
        Method setFlowUser = null;
        Method getFlowUser = null;
        try {
            obj = (BaseEntity) mapper.selectByPrimaryKey(obj.getId());
            getFlowStatusMethod = obj.getClass().getMethod("getFlowStatus");
            setFlowStatusMethod = obj.getClass().getMethod("setFlowStatus", String.class);
            setFlowUser = obj.getClass().getMethod("setFlowUser", String.class);
            getFlowUser = obj.getClass().getMethod("getFlowUser");
            if(!hasOperRole((String) getFlowUser.invoke(obj))){
                throw new ServiceException("无操作权限");
            }

            String taskId = flowApiService.getTaskId(obj.getId());
            if(taskId == null) {
                throw  new ServiceException("未找到流程");
            }
            Map<String, Object> map = new HashMap<String, Object>();
            FlowTaskVo flowTaskVo = createFlowTaskVo(taskId, obj.getId(), comment, null, null);
            String userId = getCurrentUserId();
            SysUser sysUser = sysUserService.getUserById(userId);
            Integer operType = sysUserUtilService.getUserOperType(sysUser);
            if(StringUtils.equals((CharSequence) getFlowStatusMethod.invoke(obj), FlowStatusEnum.ORG_AUDIT.getCode())) {
                // 如果当前是是申请单位审核，设置下一级为相同等级
                switch (operType){
                    case 0:
                        // 设置下一级为县级
                        flowTaskVo.setAssignee(AssigneeConstant.DEPT_COUNTY_ROLE);
                        setFlowStatusMethod.invoke(obj, FlowStatusEnum.COUNTY_AUDIT.getCode());
                        break;
                    case 1:
                        // 设置下一级为市级
                        flowTaskVo.setAssignee(AssigneeConstant.DEPT_CITY_ROLE);
                        setFlowStatusMethod.invoke(obj, FlowStatusEnum.CITY_AUDIT.getCode());
                        break;
                    case 2:
                        // 设置下一级为省级
                        flowTaskVo.setAssignee(AssigneeConstant.DEPT_PROVINCE_ROLE);
                        setFlowStatusMethod.invoke(obj, FlowStatusEnum.PROVINCE_AUDIT.getCode());
                        break;
                    default:
                }
            } else {
                // 否则设置下一级
                switch (operType) {
                    case 0:
                        // 县级审核  设置下一级为市级
                        flowTaskVo.setAssignee(AssigneeConstant.DEPT_CITY_ROLE);
                        setFlowStatusMethod.invoke(obj, FlowStatusEnum.CITY_AUDIT.getCode());
                        break;
                    case 1:
                        // 市级审核  设置下一级为省级
                        flowTaskVo.setAssignee(AssigneeConstant.DEPT_PROVINCE_ROLE);
                        setFlowStatusMethod.invoke(obj, FlowStatusEnum.PROVINCE_AUDIT.getCode());
                        break;
                    case 2:
                        // 省级审核 审核结束状态
                        flowTaskVo.setAssignee(nextFlowAssgine!=null?nextFlowAssgine:AssigneeConstant.DEPT_PROVINCE_ROLE);
                        setFlowStatusMethod.invoke(obj, nextFlowStatus);
                        break;
                    default:
                }
            }
            map.put("CODETYPE", operType);
            map.put("ISPASS", 1);
            map.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());
            flowTaskVo.setValues(map);

            setFlowUser.invoke(obj, flowTaskVo.getAssignee());
            mapper.updateByPrimaryKeySelective(obj);
            flowApiService.completeTask(flowTaskVo);

        } catch (NoSuchMethodException e) {
            e.printStackTrace();
            throw new ServiceException("流程错误，操作非流程对象");
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("流程错误，流程执行异常");
        }
    }

    /**
     * 执行工作流的退回任务 通用
     * @param obj 业务对象
     * @param mapper 业务mapper
     * @param comment 流程意见
     * @param toUpper 是否退回到上一级
     */
    @Override
    @Transactional(readOnly = false)
    public synchronized void doFlowStepSendBack(BaseEntity obj, BaseInfoMapper mapper, String comment, Boolean toUpper) {

        Method getFlowStatusMethod = null;
        Method setFlowStatusMethod = null;
        Method setFlowUser = null;
        Method setFlowUserName = null;
        try {
            obj = (BaseEntity) mapper.selectByPrimaryKey(obj.getId());
            getFlowStatusMethod = obj.getClass().getMethod("getFlowStatus");
            setFlowStatusMethod = obj.getClass().getMethod("setFlowStatus", String.class);
            setFlowUser = obj.getClass().getMethod("setFlowUser", String.class);
            setFlowUserName = obj.getClass().getMethod("setFlowUserName", String.class);
            String taskId = flowApiService.getTaskId(obj.getId());
            if(taskId == null) {
                throw  new ServiceException("未找到流程");
            }
            FlowTaskVo flowTaskVo = createFlowTaskVo(taskId, obj.getId(), comment, null, null);
            Map<String, Object> map = new HashMap<String, Object>();
            if (toUpper == null || !toUpper || StringUtils.equals((CharSequence) getFlowStatusMethod.invoke(obj), FlowStatusEnum.ORG_AUDIT.getCode())) {
                // 退回填报
                flowTaskVo.setAssignee(AssigneeConstant.ORG_HEAD_ROLE);
                setFlowStatusMethod.invoke(obj, FlowStatusEnum.WAIT_APPLY.getCode());
                map.put("ISPASS", 0);
            } else {
                String userId = getCurrentUserId();
                SysUser sysUser = sysUserService.getUserById(userId);
                Integer operType = sysUserUtilService.getUserOperType(sysUser);
                switch (operType) {
                    case 0:
                        // 退回机构管理员
                        flowTaskVo.setAssignee(AssigneeConstant.ORG_ADMIN_ROLE);
                        setFlowStatusMethod.invoke(obj, FlowStatusEnum.ORG_AUDIT.getCode());
                        map.put("ISPASS", 2);
                        break;
                    case 1:
                        // 退回县
                        flowTaskVo.setAssignee(AssigneeConstant.DEPT_COUNTY_ROLE);
                        setFlowStatusMethod.invoke(obj, FlowStatusEnum.COUNTY_AUDIT.getCode());
                        map.put("ISPASS", 2);
                        break;
                    case 2:
                        // 退回市
                        flowTaskVo.setAssignee(AssigneeConstant.DEPT_CITY_ROLE);
                        setFlowStatusMethod.invoke(obj, FlowStatusEnum.CITY_AUDIT.getCode());
                        map.put("ISPASS", 2);
                        break;
                    default:
                }
            }
            map.put("CODETYPE", -1);
            map.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());
            flowTaskVo.setValues(map);
            setFlowUser.invoke(obj, flowTaskVo.getAssignee());
            mapper.updateByPrimaryKeySelective(obj);
            flowApiService.completeTask(flowTaskVo, FlowComment.REJECT.getType());
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
            throw new ServiceException("流程错误，操作非流程对象");
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("流程错误，流程执行异常");
        }
    }

    @Override
    @Transactional(readOnly = false)
    public void doCompleteTask(BaseEntity obj, BaseInfoMapper mapper, String comment, String flowStatus, String assignee) {
        doCompleteTask(obj, mapper, comment, flowStatus, assignee, null);
    }

    /**
     *
     * @param obj
     * @param mapper
     * @param comment
     * @param flowStatus
     * @param assignee
     * @param completeUserName 传入完成的用户名
     */
    @Override
    @Transactional(readOnly = false)
    public void doCompleteTask(BaseEntity obj, BaseInfoMapper mapper, String comment, String flowStatus, String assignee, String completeUserName) {
        doCompleteTask(obj, mapper, comment, flowStatus, assignee, completeUserName, null);
    }

    @Override
    public void doCompleteTask(BaseEntity obj, BaseInfoMapper mapper, String comment, String nextFlowStatus, String nextAssignee, String completeUserName, Map values) {
        Method setFlowStatusMethod = null;
        Method setFlowUser = null;
        Method getFlowUser = null;
        Method setFlowUserName = null;
        try {
            obj = (BaseEntity) mapper.selectByPrimaryKey(obj.getId());
            setFlowStatusMethod = obj.getClass().getMethod("setFlowStatus", String.class);
            setFlowUser = obj.getClass().getMethod("setFlowUser", String.class);
            getFlowUser = obj.getClass().getMethod("getFlowUser");
            setFlowUserName = obj.getClass().getMethod("setFlowUserName", String.class);
            if(!hasOperRole((String) getFlowUser.invoke(obj))){
                throw new ServiceException("无操作权限");
            }

            String taskId = flowApiService.getTaskId(obj.getId());
            if(taskId == null) {
                throw  new ServiceException("未找到流程");
            }
            FlowTaskVo flowTaskVo = createFlowTaskVo(taskId, obj.getId(), comment, nextAssignee, values);
            if (completeUserName != null) {
                flowTaskVo.setUserName(completeUserName);
            }
            setFlowStatusMethod.invoke(obj, nextFlowStatus);
            setFlowUser.invoke(obj, nextAssignee);
            mapper.updateByPrimaryKeySelective(obj);
            Map<String, Object> map = values!=null?values:new HashMap<String, Object>();
            map.put(ProcessConstants.PROCESS_INITIATOR, nextAssignee);
            flowTaskVo.setValues(map);
            flowApiService.completeTask(flowTaskVo);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
            throw new ServiceException("流程错误，操作非流程对象");
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("流程错误，流程执行异常");
        }
    }

    /**
     * 获取待办列表，自动分页
     * @param obj
     * @param mapper
     * @param example
     * @param criteria
     * @return
     */
    @Override
    public List todoList(BaseEntity obj, BaseInfoMapper mapper, Example example, Example.Criteria criteria) {
        Integer pageNum = null;
        Integer pageSize = null;
        try{
            Method getPageNum = obj.getClass().getMethod("getPageNum");
            Method getPageSize = obj.getClass().getMethod("getPageSize");
            pageNum = (Integer) getPageNum.invoke(obj);
            pageSize = (Integer) getPageSize.invoke(obj);
        } catch (Exception e){

        }
        return todoList(obj, mapper, example, criteria, pageNum, pageSize);
    }

    @Override
    public List todoList(BaseEntity obj, BaseInfoMapper mapper, Example example, Example.Criteria criteria, Integer pageNum, Integer pageSize) {
        // 超级管理员特殊处理
        if(hasOperRole(CommonConstant.SUPER_ADMIN_ROLE + "@ROLE")) {
            if(pageNum != null && pageSize != null) {
                PageHelper.startPage(pageNum, pageSize);
            }
            return mapper.selectByExample(example);
        }
        Long startT = System.currentTimeMillis();
        List<String> roleList = getRoleList();

        // 专家待办的特殊处理
        if(roleList.size() == 1 && roleList.get(0).equals(CommonConstant.EXPERT_ROLE + "@ROLE")){
            return expertTodoList(mapper, obj, pageNum, pageSize);
        }

        criteria.andCondition("(flow_status < 999 or flow_status is null or flow_status = '')");
        if(roleList.size() == 1 && roleList.get(0).equals(AssigneeConstant.ORG_HEAD_ROLE)){
            criteria.andEqualTo("createUser", getCurrentUserName()).andCondition("(flow_user = '" + AssigneeConstant.ORG_HEAD_ROLE + "' or flow_user is null or flow_user = '')");
        } else {
            criteria.andIn("flowUser", roleList);
        }

        if(roleList.size() == 1 && (roleList.get(0).equals(AssigneeConstant.ORG_HEAD_ROLE) || roleList.get(0).equals(AssigneeConstant.ORG_ADMIN_ROLE))){
            criteria.andCondition("(org_name = '" + getCurrentOrgName() + "' or org_code = '" + getCurrentScienceOrgId() + "')");
        }
        if(roleList.size() == 1 && (roleList.get(0).equals(AssigneeConstant.DEPT_COUNTY_ROLE) || roleList.get(0).equals(AssigneeConstant.DEPT_CITY_ROLE))){
            String areaCode = getLoginUser().getAreaCode();
            Example orgExample = new Example(BasicScienceOrg.class);
            Example.Criteria orgCriteria = orgExample.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
            if(roleList.get(0).equals(AssigneeConstant.DEPT_COUNTY_ROLE)){
                orgCriteria.andEqualTo("areaCode", areaCode);
            } else if(roleList.get(0).equals(AssigneeConstant.DEPT_CITY_ROLE)){
                orgCriteria.andLike("areaCode", areaCode.substring(0,4) + "%");
            }
            List<String> orgIds = basicScienceOrgMapper.selectByExample(orgExample).stream().map(b->b.getId()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(orgIds)){
                criteria.andCondition("0 = 1");
            } else {
                criteria.andIn("orgCode", orgIds);
            }
        }

        if(pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }
        return mapper.selectByExample(example);
    }

    private Boolean hasOperRole(String needRole){
        return hasOperRole(needRole, getRoleList());
    }

    private Boolean hasOperRole(String needRole, List<String> roleList){
        for (String role:roleList) {
            if(StringUtils.equals(role, needRole)){
                return true;
            }
        }
        return false;
    }

    private Boolean hasRoleOnly(String needRole, List<String> roleList){
        return roleList.size() == 1 && StringUtils.equals(needRole, roleList.get(0));
    }

    private List<String> getRoleList() {
        //获取当前用户的角色【多角色】
        List<SysRole> userRoleList = getUserRoleList();
        assert userRoleList != null;
        List<String> roleList = userRoleList.stream().map(role->role.getRoleCode() + "@ROLE").distinct().collect(Collectors.toList());
        return roleList;
    }

    @Override
    public PageInfo todoTaskList(BaseEntity obj, BaseInfoMapper mapper, Example example, Example.Criteria criteria, Integer pageNum, Integer pageSize) {
        List<String> roleList = getRoleList();
        List<BaseEntity> baseEntities = mapper.selectByExample(example);

        TaskQuery taskQuery = taskService.createTaskQuery().active()
                .includeProcessVariables().taskAssigneeIds(roleList).orderByTaskCreateTime().desc();
        List<FlowTaskDto> flowList = new ArrayList<>();
        for (Task task : taskQuery.list()) {
            FlowTaskDto flowTask = new FlowTaskDto();
            // 当前流程信息
            flowTask.setTaskId(task.getId());
            flowTask.setTaskDefKey(task.getTaskDefinitionKey());
            flowTask.setCreateTime(task.getCreateTime());
            flowTask.setProcDefId(task.getProcessDefinitionId());
            flowTask.setTaskName(task.getName());

            // 通过任务对象获取流程实例
            ProcessInstance pi = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId()).singleResult();
            if(!idIn(pi.getBusinessKey(), baseEntities)){
                continue;
            }
            flowTask.setBusinessKey(pi.getBusinessKey());

            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(task.getProcessDefinitionId())
                    .singleResult();

            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(task.getProcessInstanceId());

            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId()).singleResult();
            if(!StringUtils.isBlank(historicProcessInstance.getStartUserId())) {
                SysUser startUser = sysUserService.findById(historicProcessInstance.getStartUserId());
                if (startUser != null) {
                    flowTask.setStartUserId(startUser.getId());
                    flowTask.setStartUserName(startUser.getNickname());
                }
            }
            flowList.add(flowTask);
        }
        PageInfo<FlowTaskDto> page = new PageInfo<>();
        page.setTotal(taskQuery.count());
        page.setList(flowList);
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        return page;

    }

    private boolean idIn(String businessKey, List<BaseEntity> baseEntities) {
        for (BaseEntity b:baseEntities) {
            if (StringUtils.equals(businessKey, b.getId())){
                return true;
            }
        }
        return false;
    }

    /**
     * 专家的待办
     * @param mapper
     * @param obj
     * @return
     */
    private List expertTodoList(Object mapper, BaseEntity obj, Integer pageNum, Integer pageSize) {
        String userId = getCurrentUserId();
        try {
            Method queryExpertTodoList = mapper.getClass().getMethod("queryExpertTodoList", Object.class);
            obj.setAttr4(userId);
            if(pageNum != null && pageSize != null) {
                PageHelper.startPage(pageNum, pageSize);
            }
            return (List) queryExpertTodoList.invoke(mapper, obj);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return new ArrayList();
    }

    /**
     * 专家的已办
     * @param mapper
     * @param obj
     * @return
     */
    private List expertFinishedList(Object mapper, BaseEntity obj, Integer pageNum, Integer pageSize) {
        String userId = getCurrentUserId();
        try {
            Method queryExpertTodoList = mapper.getClass().getMethod("queryExpertFinishedList", Object.class);
            obj.setAttr4(userId);
            if(pageNum != null && pageSize != null) {
                PageHelper.startPage(pageNum, pageSize);
            }
            return (List) queryExpertTodoList.invoke(mapper, obj);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return new ArrayList();
    }

    /**
     * 获取已办列表，自动分页
     * @param obj
     * @param mapper
     * @param example
     * @param criteria
     * @return
     */
    @Override
    public List finishedList(BaseEntity obj, BaseInfoMapper mapper, Example example, Example.Criteria criteria) {
        Integer pageNum = null;
        Integer pageSize = null;
        try{
            Method getPageNum = obj.getClass().getMethod("getPageNum");
            Method getPageSize = obj.getClass().getMethod("getPageSize");
            pageNum = (Integer) getPageNum.invoke(obj);
            pageSize = (Integer) getPageSize.invoke(obj);
        } catch (Exception e){

        }
        return finishedList(obj, mapper, example, criteria, pageNum, pageSize);
    }

    @Override
    public List finishedList(BaseEntity obj, BaseInfoMapper mapper, Example example, Example.Criteria criteria, Integer pageNum, Integer pageSize) {

        List<String> roleList = new ArrayList<>();
        //获取当前用户的角色【多角色】
        List<SysRole> userRoleList = getUserRoleList();
        assert userRoleList != null;
        for (SysRole sysRole : userRoleList) {
            roleList.add(sysRole.getRoleCode() + "@ROLE");
        }

        if(roleList.size() == 1 && roleList.get(0).equals(CommonConstant.EXPERT_ROLE + "@ROLE")){
            return expertFinishedList(mapper, obj, pageNum, pageSize);
        }

        HistoricTaskInstanceQuery taskInstanceQuery = historyService.createHistoricTaskInstanceQuery()
                .includeProcessVariables()
                .finished()
                .taskAssignee(getCurrentUserId())
                .orderByHistoricTaskInstanceEndTime()
                .desc();
        List<HistoricTaskInstance> historicTaskInstanceList = taskInstanceQuery.list();
        List<FlowTaskDto> hisTaskList = Lists.newArrayList();
        List<String> ids = new ArrayList<>();
        for (HistoricTaskInstance histTask : historicTaskInstanceList) {

            // 通过任务对象获取流程实例
            ProcessInstance pi = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(histTask.getProcessInstanceId()).singleResult();
            if(pi == null){
                HistoricProcessInstance hpi = historyService.createHistoricProcessInstanceQuery().processInstanceId(histTask.getProcessInstanceId()).singleResult();
                if(hpi == null) {
                    continue;
                } else {
                    ids.add(hpi.getBusinessKey());
                }
            } else {
                ids.add(pi.getBusinessKey());
            }
        }

        if(roleList.size() == 1 && roleList.get(0).equals(AssigneeConstant.ORG_HEAD_ROLE)){
            criteria.andEqualTo("createUser", getCurrentUserName());
        }
        if(roleList.size() == 1 && (roleList.get(0).equals(AssigneeConstant.ORG_HEAD_ROLE) || roleList.get(0).equals(AssigneeConstant.ORG_ADMIN_ROLE))){
            criteria.andCondition("(org_name = '" + getCurrentOrgName() + "' or org_code = '" + getCurrentScienceOrgId() + "')");
        }

        if (!CollectionUtils.isEmpty(ids)) {
            criteria.andIn("id", ids);
        } else {
            return new ArrayList();
        }

        if(pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }
        return mapper.selectByExample(example);
    }

    @Override
    public FlowTaskDto findTaskDto(BaseEntity obj) {
        // 进行中流程
        TaskQuery taskQuery = taskService.createTaskQuery().processInstanceBusinessKey(obj.getId()).orderByTaskCreateTime().desc();
        for (Task task : taskQuery.list()) {

            FlowTaskDto flowTask = new FlowTaskDto();
            // 当前流程信息
            flowTask.setTaskId(task.getId());
            flowTask.setTaskDefKey(task.getTaskDefinitionKey());
            flowTask.setCreateTime(task.getCreateTime());
            flowTask.setProcDefId(task.getProcessDefinitionId());
            flowTask.setTaskName(task.getName());
            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(task.getProcessDefinitionId())
                    .singleResult();

            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(task.getProcessInstanceId());

            flowTask.setBusinessKey(obj.getId());

            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId()).singleResult();
            if(!StringUtils.isBlank(historicProcessInstance.getStartUserId())) {
                SysUser startUser = sysUserService.findById(historicProcessInstance.getStartUserId());
                if (startUser != null) {
                    flowTask.setStartUserId(startUser.getId());
                    flowTask.setStartUserName(startUser.getNickname());
                }
            }
            return flowTask;
        }
        // 历史流程
        HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery().processInstanceBusinessKey(obj.getId()).orderByTaskCreateTime().desc();
        for(HistoricTaskInstance historicTask:historicTaskInstanceQuery.list()){

            FlowTaskDto flowTask = new FlowTaskDto();
            // 当前流程信息
            flowTask.setTaskId(historicTask.getId());
            flowTask.setTaskDefKey(historicTask.getTaskDefinitionKey());
            flowTask.setCreateTime(historicTask.getCreateTime());
            flowTask.setProcDefId(historicTask.getProcessDefinitionId());
            flowTask.setTaskName(historicTask.getName());
            // 流程定义信息
            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(historicTask.getProcessDefinitionId())
                    .singleResult();

            flowTask.setDeployId(pd.getDeploymentId());
            flowTask.setProcDefName(pd.getName());
            flowTask.setProcDefVersion(pd.getVersion());
            flowTask.setProcInsId(historicTask.getProcessInstanceId());

            flowTask.setBusinessKey(obj.getId());

            // 流程发起人信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(historicTask.getProcessInstanceId()).singleResult();
            if(!StringUtils.isBlank(historicProcessInstance.getStartUserId())) {
                SysUser startUser = sysUserService.findById(historicProcessInstance.getStartUserId());
                if (startUser != null) {
                    flowTask.setStartUserId(startUser.getId());
                    flowTask.setStartUserName(startUser.getNickname());
                }
            }
            return flowTask;
        }
        return new FlowTaskDto();
    }

    @Override
    public void doFlowStepSubmitToExpert(String processInstanceKey, BaseEntity obj, BaseInfoMapper mapper, String comment) {
        Method getFlowStatusMethod = null;
        Method setFlowStatusMethod = null;
        Method setFlowUser = null;
        Method setFlowUserName = null;
        try {
            obj = (BaseEntity) mapper.selectByPrimaryKey(obj.getId());
            getFlowStatusMethod = obj.getClass().getMethod("getFlowStatus");
            setFlowStatusMethod = obj.getClass().getMethod("setFlowStatus", String.class);
            setFlowUser = obj.getClass().getMethod("setFlowUser", String.class);
            setFlowUserName = obj.getClass().getMethod("setFlowUserName", String.class);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
            throw new ServiceException("流程错误，操作非流程对象");
        }
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            String taskId = null;
            if(StringUtils.isNotBlank((CharSequence) getFlowStatusMethod.invoke(obj))){
                taskId = flowApiService.getTaskId(obj.getId());
            }
            FlowTaskVo flowTaskVo = createFlowTaskVo(taskId, obj.getId(), comment, null, null);
            flowTaskVo.setAssignee(AssigneeConstant.EXPERT_ROLE);
            setFlowStatusMethod.invoke(obj, FlowStatusEnum.EXPERTS_GRADE.getCode());
            setFlowUser.invoke(obj, flowTaskVo.getAssignee());
            map.put("CODETYPE", 3);
            mapper.updateByPrimaryKeySelective(obj);

            if (StringUtils.isBlank(taskId)) {
                // 不存在任务，创建任务
                flowTaskVo.setProcessInstanceKey(processInstanceKey);
                taskId = flowApiService.startProcessInstanceByKey(flowTaskVo);
            }

            map.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());
            flowTaskVo.setValues(map);
            flowTaskVo.setTaskId(taskId);
            flowApiService.completeTask(flowTaskVo);

        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("流程错误，流程执行异常");
        }
    }

    @Override
    public List endList(BaseEntity obj, BaseInfoMapper mapper, Example example, Example.Criteria criteria) {
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        List<String> roleList = getRoleList();
        if(roleList.size() == 1 && (roleList.get(0).equals(AssigneeConstant.ORG_HEAD_ROLE) || roleList.get(0).equals(AssigneeConstant.ORG_ADMIN_ROLE))){
            // criteria.andEqualTo("orgName", getCurrentOrgName());
            criteria.andCondition("(org_name = '" + getCurrentOrgName() + "' or org_code = '" + getCurrentScienceOrgId() + "')");
        }
        if(roleList.size() == 1 && (roleList.get(0).equals(AssigneeConstant.ORG_HEAD_ROLE))){
            criteria.andEqualTo("createUser", (getLoginUser()).getUsername());
        }
        Integer pageNum = null;
        Integer pageSize = null;
        try{
            Method getPageNum = obj.getClass().getMethod("getPageNum");
            Method getPageSize = obj.getClass().getMethod("getPageSize");
            pageNum = (Integer) getPageNum.invoke(obj);
            pageSize = (Integer) getPageSize.invoke(obj);
        } catch (Exception e){

        }
        if(pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }
        return mapper.selectByExample(example);
    }


    /**
     * 流程完成时间处理
     *
     * @param ms
     * @return
     */
    private String getDate(long ms) {

        long day = ms / (24 * 60 * 60 * 1000);
        long hour = (ms / (60 * 60 * 1000) - day * 24);
        long minute = ((ms / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long second = (ms / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - minute * 60);

        if (day > 0) {
            return day + "天" + hour + "小时" + minute + "分钟";
        }
        if (hour > 0) {
            return hour + "小时" + minute + "分钟";
        }
        if (minute > 0) {
            return minute + "分钟";
        }
        if (second > 0) {
            return second + "秒";
        } else {
            return 0 + "秒";
        }
    }
}
