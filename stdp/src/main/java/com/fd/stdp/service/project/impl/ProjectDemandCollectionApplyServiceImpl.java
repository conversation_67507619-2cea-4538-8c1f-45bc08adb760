package com.fd.stdp.service.project.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.fd.stdp.beans.basic.BasicPerson;
import com.fd.stdp.beans.basic.vo.BasicPersonVo;
import com.fd.stdp.beans.flowable.FlowTaskDto;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.project.ProjectDemandCollectionExperts;
import com.fd.stdp.beans.project.ProjectDemandCollectionGuid;
import com.fd.stdp.beans.sys.*;
import com.fd.stdp.beans.sys.vo.RoleAndRoleTypesVO;
import com.fd.stdp.common.BaseEntity;
import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.constant.*;
import com.fd.stdp.dao.project.ProjectDemandCollectionExpertsMapper;
import com.fd.stdp.dao.project.ProjectDemandCollectionGuidMapper;
import com.fd.stdp.dao.sys.*;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.flowable.FlwDeployFormService;
import com.fd.stdp.service.sys.RoleService;
import com.fd.stdp.service.sys.SysUserService;
import com.fd.stdp.util.AppUserUtil;
import com.fd.stdp.util.DateUtils;
import com.google.common.collect.Lists;
import liquibase.pro.packaged.S;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractApply;
import com.fd.stdp.beans.project.ProjectDemandCollectionApply;
import com.fd.stdp.beans.project.vo.ProjectDemandCollectionApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectDemandCollectionApplyMapper;
import com.fd.stdp.service.project.ProjectDemandCollectionApplyService;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;

import static com.fd.stdp.common.BaseController.getLoginUser;
import static com.fd.stdp.common.BaseController.getUserRoleList;

/**
 * <AUTHOR>
 * @Description: 需求征集申请Service业务层处理
 * @date 2021-11-16
 */
@Service
@Transactional(readOnly = true)
public class ProjectDemandCollectionApplyServiceImpl extends BaseServiceImpl<ProjectDemandCollectionApplyMapper, ProjectDemandCollectionApply> implements ProjectDemandCollectionApplyService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectDemandCollectionApplyServiceImpl.class);
    @Autowired
    private ProjectDemandCollectionApplyMapper projectDemandCollectionApplyMapper;

    @Autowired
    private SysProjectNoticeBatchMapper sysProjectNoticeBatchMapper;

    @Autowired
    private SysProjectNoticeMapper sysProjectNoticeMapper;

    @Autowired
    private SysDictItemMapper sysDictItemMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ProjectDemandCollectionExpertsMapper projectDemandCollectionExpertsMapper;

    @Autowired
    private ProjectDemandCollectionGuidMapper projectDemandCollectionGuidMapper;    

	@Autowired
	private FlowCommonService flowCommonService;

    @Autowired
    private FlowApiService flowApiService;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private RoleService sysRoleService;

    @Autowired
    private FlwDeployFormService flwDeployFormService;

    @Autowired
    protected HistoryService historyService;

    @Autowired
    protected RuntimeService runtimeService;

    @Autowired
    protected RepositoryService repositoryService;

    @Autowired
    protected TaskService taskService;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新需求征集申请
     *@param projectDemandCollectionApplyVo 需求征集申请对象
     *@return String 需求征集申请ID
     *@Author: yujianfei
     */
    public String saveOrUpdateProjectDemandCollectionApply(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        if (projectDemandCollectionApplyVo == null) {
            throw new ServiceException("数据异常");
        }
        SysUser loginAppUser = (SysUser) AppUserUtil.getLoginAppUser();
        assert loginAppUser != null;
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandTypeCode())) {
            SysDictItem sysDictItem = sysDictItemMapper.findByItemCode(projectDemandCollectionApplyVo.getDemandTypeCode());
            projectDemandCollectionApplyVo.setDemandTypeText(sysDictItem.getItemValue());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getProfessionalFiledCode())) {
            SysDictItem secondSysDictItem = sysDictItemMapper.findByItemCode(projectDemandCollectionApplyVo.getProfessionalFiledCode());
            projectDemandCollectionApplyVo.setProfessionalFiled(secondSysDictItem.getItemValue());
        }

        projectDemandCollectionApplyVo.setScienceOrgId(loginAppUser.getScienceOrgId());
        projectDemandCollectionApplyVo.setOrgCode(loginAppUser.getScienceOrgId());
        projectDemandCollectionApplyVo.setOrgName(loginAppUser.getScienceOrgName());
        if (null == projectDemandCollectionApplyVo.getCodeType()) {
            //保存
            projectDemandCollectionApplyVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
            projectDemandCollectionApplyVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
            projectDemandCollectionApplyVo.setFlowUserName(loginAppUser.getId());
        } else if (Constant.FLAG_NO.equals(projectDemandCollectionApplyVo.getCodeType())) {
            //提交
//            projectDemandCollectionApplyVo.setFlowStatus(Constant.FLOW_STATUS_DSQDWSH);

            //projectDemandCollectionApplyVo.setFlowUser(loginAppUser.getId());
            //projectDemandCollectionApplyVo.setFlowUserName(loginAppUser.getNickname());

            //scienceOrgID到sysuser表中去查询scienceOrgID,得到的list循环去sysUserRole查询userId
//            List<SysUser> sysUserList = sysUserMapper.listByScienceOrgId(projectDemandCollectionApplyVo.getScienceOrgId());

            //获取下一流程用户[审核单位]
//            for (SysUser sysUser : sysUserList) {
//                Example sysUserRoleExample = new Example(SysUserRole.class);
//                Criteria sysUserRoleCriteria = sysUserRoleExample.createCriteria();
//                sysUserRoleCriteria.andEqualTo("userId", sysUser.getId());
//                sysUserRoleCriteria.andEqualTo("yn", Constant.FLAG_YES);
//                List<SysUserRole> userRoleList = sysUserRoleMapper.selectByExample(sysUserRoleExample);
//                for (SysUserRole sysUserRole : userRoleList) {
//                    if ("34adfbfa74e34375bfa94e7ef26fb900".equals(sysUserRole.getRoleId())) {
//                        projectDemandCollectionApplyVo.setFlowUser(sysUserRole.getUserId());
//                        projectDemandCollectionApplyVo.setFlowUserName(sysUserMapper.selectByPrimaryKey(sysUserRole.getUserId()).getNickname());
//                    }
//                }
//            }
            
            if(StringUtils.isEmpty(projectDemandCollectionApplyVo.getSerialNumber())) {
            	projectDemandCollectionApplyVo.setSerialNumber(getFildeSerialNumber(projectDemandCollectionApplyVo));
            }
        }

        if (StringUtils.isEmpty(projectDemandCollectionApplyVo.getId())) {
            //新增
            projectDemandCollectionApplyVo.setId(UUIDUtils.getUUID());
            projectDemandCollectionApplyMapper.insertSelective(projectDemandCollectionApplyVo);

        } else {
            //避免页面传入修改
            projectDemandCollectionApplyVo.setYn(null);
            projectDemandCollectionApplyMapper.updateByPrimaryKeySelective(projectDemandCollectionApplyVo);
        }

        if (Constant.FLAG_NO.equals(projectDemandCollectionApplyVo.getCodeType())) {
//            FlowTaskVo flowTaskVo = new FlowTaskVo();
//            flowTaskVo.setBusinessKey(projectDemandCollectionApplyVo.getId()); //业务id
//            flowTaskVo.setUserId(loginAppUser.getId()); //当前流程发起者的用户信息
//            flowTaskVo.setUserName(loginAppUser.getNickname());
//            Map<String, Object> map = new HashMap<>();
//            // 0表示传递给【机构管理员】
//            //map.put("codeType", null);
//            map.put("codeType", Constant.FLAG_NO);  //路径中传递的参数
//            flowTaskVo.setValues(map);
//            flowTaskVo.setComment(loginAppUser.getNickname() + "提交");   //审批信息
//            flowTaskVo.setAssignee(AssigneeConstant.ORG_ADMIN_ROLE);    // 下一级审批人
//            flowTaskVo.setProcessInstanceKey(FlowableConstant.SYSTEM_FLOWABLE_STDP_DEMAND_APPLY); //对应的流程名
//            flowApiService.startProcessInstanceByKey(flowTaskVo);   // 开启流程
            flowCommonService.doFlowStepSubmit(FlowableConstant.SYSTEM_FLOWABLE_STDP_DEMAND_APPLY
                    , projectDemandCollectionApplyVo, this.mapper
                    , loginAppUser.getNickname() + "提交");
        }


        return projectDemandCollectionApplyVo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除需求征集申请
     *@param id void 需求征集申请ID
     *@Author: yujianfei
     */
    public void deleteProjectDemandCollectionApply(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            ProjectDemandCollectionApply projectDemandCollectionApply = projectDemandCollectionApplyMapper.selectByPrimaryKey(id);
            if (projectDemandCollectionApply == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            ProjectDemandCollectionApply temprojectDemandCollectionApply = new ProjectDemandCollectionApply();
            temprojectDemandCollectionApply.setYn(CommonConstant.FLAG_NO);
            temprojectDemandCollectionApply.setId(projectDemandCollectionApply.getId());
            projectDemandCollectionApplyMapper.updateByPrimaryKeySelective(temprojectDemandCollectionApply);

            // 流程删除
            FlowTaskVo flowTaskVo = new FlowTaskVo();
            flowTaskVo.setTaskId(flowApiService.getTaskId(id));
            flowTaskVo.setComment("需求删除");
            if(flowTaskVo.getTaskId() != null) {
                flowApiService.deleteTask(flowTaskVo);
            }
        }
    }

    /**
     * @param id
     * @return ProjectDemandCollectionApply
     * @Description: 查询需求征集申请详情
     * @Author: yujianfei
     */
    @Override
    public ProjectDemandCollectionApply findById(String id) {
        //return projectDemandCollectionApplyMapper.findById(id);
        ProjectDemandCollectionApply projectDemandCollectionApply = projectDemandCollectionApplyMapper.selectByPrimaryKey(id);
        ProjectDemandCollectionApplyVo vo = new ProjectDemandCollectionApplyVo();
        BeanUtils.copyProperties(projectDemandCollectionApply, vo);
        vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
        return vo;
    }


    /**
     * @param projectDemandCollectionApplyVo
     * @return PageInfo<ProjectDemandCollectionApply>
     * @Description: 分页查询需求征集申请
     * @Author: yujianfei
     */
    @Override
    public PageInfo<ProjectDemandCollectionApply> findPageByQuery(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        PageHelper.startPage(projectDemandCollectionApplyVo.getPageNum(), projectDemandCollectionApplyVo.getPageSize());
        Example example = new Example(ProjectDemandCollectionApply.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandTypeCode())) {
            criteria.andEqualTo("demandTypeCode", projectDemandCollectionApplyVo.getDemandTypeCode());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandSecondTypeCode())) {
            criteria.andEqualTo("demandSecondTypeCode", projectDemandCollectionApplyVo.getDemandSecondTypeCode());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getOrgName())) {
            criteria.andLike("orgName", "%" + projectDemandCollectionApplyVo.getOrgName() + "%");
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getBatchId())) {
            criteria.andEqualTo("batchId", projectDemandCollectionApplyVo.getBatchId());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getProfessionalFiled())) {
            criteria.andEqualTo("professionalFiled", projectDemandCollectionApplyVo.getProfessionalFiled());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandName())) {
            criteria.andLike("demandName", "%" + projectDemandCollectionApplyVo.getDemandName() + "%");
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandSecondTypeName())) {
            criteria.andLike("demandSecondTypeName", "%" + projectDemandCollectionApplyVo.getDemandSecondTypeName() + "%");
        }

        //已整合
        if (projectDemandCollectionApplyVo.getIntergrate()!=null && Constant.FLAG_YES.equals(projectDemandCollectionApplyVo.getIntergrate())) {
            criteria.andEqualTo("intergrate", projectDemandCollectionApplyVo.getIntergrate());
        }

        // 机构管理员
        if (projectDemandCollectionApplyVo.getCodeType()!=null && Constant.FLAG_NO.equals(projectDemandCollectionApplyVo.getCodeType())) {
            criteria.andEqualTo("flowUser", AppUserUtil.getCurrentUserId());
        }
        example.orderBy("createTime").desc();

        List<ProjectDemandCollectionApply> projectDemandCollectionApplyList = projectDemandCollectionApplyMapper.selectByExample(example);
        return new PageInfo<ProjectDemandCollectionApply>(projectDemandCollectionApplyList);
    }


    /**
     * @param projectDemandCollectionApplyVo 需求征集数据 json
     * @return RestApiResponse<?>
     * @Description: 审核
     * @Author: yujianfei
     */
    @Override
    @Transactional(readOnly = false)
    public void audit(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        String assignee = null;
        if (Constant.FLAG_TWO.equals(projectDemandCollectionApplyVo.getCodeType())) {
            //省局审核【指定给专家】
            projectDemandCollectionApplyVo.setFlowUserName(projectDemandCollectionApplyVo.getExpertId());

            assignee = projectDemandCollectionApplyVo.getExpertId();

        }
        //专家评审
        if (Constant.FLAG_THREE.equals(projectDemandCollectionApplyVo.getCodeType())) {
            ProjectDemandCollectionExperts experts = new ProjectDemandCollectionExperts();
            experts.setDemandId(projectDemandCollectionApplyVo.getId());
            experts.setExpertNickName(getLoginUser().getNickname());
            experts.setExpertConclusion(projectDemandCollectionApplyVo.getExpertConclusion());
            projectDemandCollectionExpertsMapper.insertSelective(experts);
            flowCommonService.doCompleteTask(projectDemandCollectionApplyVo, this.mapper, "专家评审完成"
                    , FlowStatusEnum.PROJECT_REVIEW.getCode(), FlowStatusEnum.PROJECT_REVIEW.getRole(), experts.getExpertNickName());
        } else {
            flowCommonService.doFlowStepAudit(projectDemandCollectionApplyVo
                    , this.mapper, "", FlowStatusEnum.EXPERTS_GRADE.getCode()
                    , assignee == null ? FlowStatusEnum.EXPERTS_GRADE.getRole() : assignee);
        }
//        SysUser loginAppUser = (SysUser) AppUserUtil.getLoginAppUser();
//        assert loginAppUser != null;
////        SysUser scienceSysUser = sysUserMapper.findScienceByAreaCode("330000");
//
//        FlowTaskVo flowTaskVo = new FlowTaskVo();
//        Map<String, Object> map = new HashMap<>();
//        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getCodeType())) {
//            //申请单位审核
//            if (Constant.FLAG_NO.equals(projectDemandCollectionApplyVo.getCodeType())) {
//                // 属直【指定给省局】
//                if (!StringUtils.isEmpty(loginAppUser.getAreaCode()) && "330000".equals(loginAppUser.getAreaCode())) {
//                    projectDemandCollectionApplyVo.setFlowStatus(Constant.FLOW_STATUS_DPSH);
////                    projectDemandCollectionApplyVo.setFlowUser(scienceSysUser.getId());
////                    projectDemandCollectionApplyVo.setFlowUserName(scienceSysUser.getNickname());
//
//                    flowTaskVo.setAssignee(AssigneeConstant.DEPT_PROVINCE_ROLE);
//
//                    // 2表示传递给【省局】
//                    map.put("codeType", Constant.FLAG_TWO);
//                    map.put("ISPRO", Constant.FLAG_YES);
//                    flowTaskVo.setValues(map);
//                } else {
//                    projectDemandCollectionApplyVo.setFlowStatus(Constant.FLOW_STATUS_DCSH);
////                    projectDemandCollectionApplyVo.setFlowUser(sysUserMapper.findScienceByAreaCode(loginAppUser.getAreaCode().substring(0, 4) + "00").getId());
////                    projectDemandCollectionApplyVo.setFlowUserName(sysUserMapper.findScienceByAreaCode(loginAppUser.getAreaCode().substring(0, 4) + "00").getNickname());
//                    flowTaskVo.setAssignee(AssigneeConstant.DEPT_CITY_ROLE);
//
//                    // 1表示传递给【市局】
//                    map.put("codeType", Constant.FLAG_YES);
//                    map.put("ISPRO", Constant.FLAG_NO);
//                    flowTaskVo.setValues(map);
//                }
//            }
//            if (Constant.FLAG_YES.equals(projectDemandCollectionApplyVo.getCodeType())) {
//                //市局审核【指定给省局】
//                projectDemandCollectionApplyVo.setFlowStatus(Constant.FLOW_STATUS_DPSH);
////                projectDemandCollectionApplyVo.setFlowUser(scienceSysUser.getId());
////                projectDemandCollectionApplyVo.setFlowUserName(scienceSysUser.getNickname());
//                flowTaskVo.setAssignee(AssigneeConstant.DEPT_PROVINCE_ROLE);
//
//                // 2表示传递给【省局】
//                map.put("codeType", Constant.FLAG_TWO);
//                flowTaskVo.setValues(map);
//            }
//            if (Constant.FLAG_TWO.equals(projectDemandCollectionApplyVo.getCodeType())) {
//                //省局审核【指定给专家】
//                projectDemandCollectionApplyVo.setFlowStatus(Constant.FLOW_STATUS_DZJSH);
//                projectDemandCollectionApplyVo.setFlowUser(projectDemandCollectionApplyVo.getExpertId());
//                projectDemandCollectionApplyVo.setFlowUserName(sysUserMapper.selectByPrimaryKey(projectDemandCollectionApplyVo.getExpertId()).getNickname());
//
//                flowTaskVo.setAssignee(projectDemandCollectionApplyVo.getExpertId());
//
//                // 3表示传递给【专家】
//                map.put("codeType", Constant.FLAG_THREE);
//
//            }
//            //专家评审
//            if (Constant.FLAG_THREE.equals(projectDemandCollectionApplyVo.getCodeType())) {
//                projectDemandCollectionApplyVo.setFlowStatus(Constant.WU);
//                projectDemandCollectionApplyVo.setFlowUser(loginAppUser.getId());
//                projectDemandCollectionApplyVo.setFlowUserName(loginAppUser.getNickname());
//
//                ProjectDemandCollectionExperts experts = new ProjectDemandCollectionExperts();
//                experts.setDemandId(projectDemandCollectionApplyVo.getId());
//                experts.setExpertNickName(loginAppUser.getNickname());
//                experts.setExpertConclusion(projectDemandCollectionApplyVo.getExpertConclusion());
//                projectDemandCollectionExpertsMapper.insertSelective(experts);
//
//                //专家不需要指定下一级，完成
//                //flowTaskVo.setAssignee(AssigneeConstant.EXPERT_ROLE);
//            }
//        }
//        projectDemandCollectionApplyMapper.updateByPrimaryKeySelective(projectDemandCollectionApplyVo);
//
//        flowTaskVo.setBusinessKey(projectDemandCollectionApplyVo.getId());
//        flowTaskVo.setUserId(loginAppUser.getId());
//        flowTaskVo.setUserName(loginAppUser.getUsername());
//        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getExpertAdvice())) {
//            flowTaskVo.setComment(projectDemandCollectionApplyVo.getExpertAdvice());
//        } else {
//            flowTaskVo.setComment(loginAppUser.getNickname() + "审核");
//        }
//        map.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());
//        flowTaskVo.setValues(map);
//        flowApiService.completeTask(flowTaskVo);
    }


    /**
     * @param projectDemandCollectionApplyVo 需求征集数据 json
     * @return RestApiResponse<?>
     * @Description: 整合
     * @Author: yujianfei
     */
    @Override
    @Transactional(readOnly = false)
    public void intergrate(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        if (null == projectDemandCollectionApplyVo) {
            throw new ServiceException("数据异常");
        }
        SysUser loginAppUser = (SysUser) AppUserUtil.getLoginAppUser();
        assert loginAppUser != null;
        if (!CollectionUtils.isEmpty(projectDemandCollectionApplyVo.getIds())) {
            for (String applyId : projectDemandCollectionApplyVo.getIds()) {
                ProjectDemandCollectionApply projectDemandCollectionApply = projectDemandCollectionApplyMapper.selectByPrimaryKey(applyId);
                projectDemandCollectionApply.setIntergrate(Constant.FLAG_YES);
                projectDemandCollectionApplyMapper.updateByPrimaryKeySelective(projectDemandCollectionApply);
            }
        }
        ProjectDemandCollectionGuid projectDemandCollectionGuid = new ProjectDemandCollectionGuid();
        BeanUtils.copyProperties(projectDemandCollectionApplyVo, projectDemandCollectionGuid);
        //projectDemandCollectionGuid.setGuidName(projectDemandCollectionApplyVo.getGuidName());
        projectDemandCollectionGuid.setBatchId(projectDemandCollectionApplyVo.getBatchId());
        projectDemandCollectionGuid.setId(UUIDUtils.getUUID());

        // 去除最后一个逗号
        projectDemandCollectionGuid.setOrgName(removeLastPoint(projectDemandCollectionApplyVo.getOrgName()));
        projectDemandCollectionGuid.setResearchContents(removeLastPoint(projectDemandCollectionApplyVo.getResearchContents()));
        projectDemandCollectionGuid.setTechnicalIndicators(removeLastPoint(projectDemandCollectionApplyVo.getTechnicalIndicators()));
        projectDemandCollectionGuid.setExpertedResults(removeLastPoint(projectDemandCollectionApplyVo.getExpertedResults()));
        projectDemandCollectionGuid.setTimeLimit(removeLastPoint(projectDemandCollectionApplyVo.getTimeLimit()));
        projectDemandCollectionGuid.setProfessionalFiled(removeLastPoint(projectDemandCollectionApplyVo.getProfessionalFiled()));
        projectDemandCollectionGuid.setStatus("0");
        projectDemandCollectionGuidMapper.insertSelective(projectDemandCollectionGuid);

        // 指南发布给省局修改
//        FlowTaskVo flowTaskVo = new FlowTaskVo();
//        flowTaskVo.setBusinessKey(projectDemandCollectionGuid.getId());
//        flowTaskVo.setUserId(loginAppUser.getId());
//        flowTaskVo.setUserName(loginAppUser.getNickname());
//        Map<String, Object> map = new HashMap<>();
//
//        flowTaskVo.setComment(loginAppUser.getNickname() + "整合");
//        flowTaskVo.setAssignee(AssigneeConstant.DEPT_PROVINCE_ROLE);
//        flowTaskVo.setProcessInstanceKey(FlowableConstant.SYSTEM_FLOWABLE_STDP_DEMAND_APPLY);
//        flowApiService.completeTask(flowTaskVo);
    }


    /**
     * 去除最后一个字符串
     * @param targetString
     * <AUTHOR>
     * @Date 2021/12/23
     * @return
     */
    public String removeLastPoint(String targetString) {
        return targetString.substring(0, targetString .length() - 1);
    }


    /**
     * @param projectDemandCollectionApplyVo 需求征集数据 json
     * @return RestApiResponse<?>
     * @Description: 退回
     * @Author: yujianfei
     */
    @Override
    @Transactional(readOnly = false)
    public void sendBack(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
        if (null == projectDemandCollectionApplyVo) {
            throw new ServiceException("数据异常");
        }
//        SysUser loginAppUser = (SysUser) AppUserUtil.getLoginAppUser();
//
//        //流程FLOW_STATUS更新  TODO
//
//        FlowTaskVo flowTaskVo = new FlowTaskVo();
//        flowTaskVo.setBusinessKey(projectDemandCollectionApplyVo.getId());
//        assert loginAppUser != null;
//        flowTaskVo.setUserId(loginAppUser.getId());
//        flowTaskVo.setUserName(loginAppUser.getNickname());
//        Map<String, Object> map = new HashMap<>();
//        // null表示传递给【机构管理员】
////        map.put("codeType", Constant.FLAG_NO);
////        flowTaskVo.setValues(map);
//        flowTaskVo.setComment(loginAppUser.getNickname() + "提交");
//        //flowTaskVo.setAssignee(AssigneeConstant.ORG_ADMIN_ROLE);
//        flowTaskVo.setProcessInstanceKey(FlowableConstant.SYSTEM_FLOWABLE_STDP_DEMAND_APPLY);
//        flowTaskVo.setTaskId(flowApiService.getTaskId(flowTaskVo.getBusinessKey()));
//        flowApiService.taskReject(flowTaskVo);
        flowCommonService.doFlowStepSendBack(projectDemandCollectionApplyVo, this.mapper, "退回", false);
    }
    
//    @Override
//	public PageInfo<ProjectDemandCollectionApply> todoList(ProjectDemandCollectionApplyVo vo) {
//		Example example = new Example(ProjectContractApply.class);
//		Criteria criteria = getCriteria(vo, example);
//		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
//		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
//	}
//
//	private Criteria getCriteria(ProjectDemandCollectionApplyVo vo, Example example) {
//		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
//		//查询条件
//        if (!StringUtils.isEmpty(vo.getDemandTypeCode())) {
//            criteria.andEqualTo("demandTypeCode", vo.getDemandTypeCode());
//        }
//        if (!StringUtils.isEmpty(vo.getDemandSecondTypeCode())) {
//            criteria.andEqualTo("demandSecondTypeCode", vo.getDemandSecondTypeCode());
//        }
//        if (!StringUtils.isEmpty(vo.getOrgName())) {
//            criteria.andLike("orgName", "%" + vo.getOrgName() + "%");
//        }
//        if (!StringUtils.isEmpty(vo.getBatchId())) {
//            criteria.andEqualTo("batchId", vo.getBatchId());
//        }
//        if (!StringUtils.isEmpty(vo.getProfessionalFiled())) {
//            criteria.andEqualTo("professionalFiled", vo.getProfessionalFiled());
//        }
//        if (!StringUtils.isEmpty(vo.getDemandName())) {
//            criteria.andLike("demandName", "%" + vo.getDemandName() + "%");
//        }
//        if (!StringUtils.isEmpty(vo.getDemandSecondTypeName())) {
//            criteria.andLike("demandSecondTypeName", "%" + vo.getDemandSecondTypeName() + "%");
//        }
//
//        //已整合
//        if (!StringUtils.isEmpty(vo.getIntergrate()) && Constant.FLAG_YES.equals(vo.getIntergrate())) {
//            criteria.andEqualTo("intergrate", vo.getIntergrate());
//        }
//
//        // 机构管理员
//        if (!StringUtils.isEmpty(vo.getCodeType()) && Constant.FLAG_NO.equals(vo.getCodeType())) {
//            criteria.andEqualTo("flowUser", AppUserUtil.getCurrentUserId());
//        }
//		return criteria;
//	}


    /**
     * 代办列表
     * @param vo
     * @param currentUserId
     * @return
     */
    @Override
    public PageInfo todoList(ProjectDemandCollectionApplyVo vo, String currentUserId) {
        Example example = new Example(ProjectDemandCollectionApply.class);
        Criteria criteria = getCriteria(vo, example);
        example.orderBy("createTime").desc();
        return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria,vo.getPageNum(),vo.getPageSize()));
//        List<String> roleList = new ArrayList<>();
//        //获取当前用户的角色【多角色】
//        List<SysRole> userRoleList = getUserRoleList();
//        assert userRoleList != null;
//        for (SysRole sysRole : userRoleList) {
//            roleList.add(sysRole.getRoleCode() + "@ROLE");
//        }
//
//        PageInfo<FlowTaskDto> page = new PageInfo<>();
//        TaskQuery taskQuery = taskService.createTaskQuery().active()
//                .includeProcessVariables().taskAssigneeIds(roleList).orderByTaskCreateTime().desc();
//        page.setTotal(taskQuery.count());
//        List<Task> taskList = taskQuery.listPage(projectDemandCollectionApplyVo.getPageNum() - 1, projectDemandCollectionApplyVo.getPageSize());
//        List<FlowTaskDto> flowList = new ArrayList<>();
//        List<ProjectDemandCollectionApply> applyList = Lists.newArrayList();
//        for (Task task : taskList) {
//            FlowTaskDto flowTask = new FlowTaskDto();
//            // 当前流程信息
//            flowTask.setTaskId(task.getId());
//            flowTask.setTaskDefKey(task.getTaskDefinitionKey());
//            flowTask.setCreateTime(task.getCreateTime());
//            flowTask.setProcDefId(task.getProcessDefinitionId());
//            flowTask.setTaskName(task.getName());
//            // 流程定义信息
//            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
//                    .processDefinitionId(task.getProcessDefinitionId())
//                    .singleResult();
//
//
//
//            flowTask.setDeployId(pd.getDeploymentId());
//            flowTask.setProcDefName(pd.getName());
//            flowTask.setProcDefVersion(pd.getVersion());
//            flowTask.setProcInsId(task.getProcessInstanceId());
//
//            // 通过任务对象获取流程实例
//            ProcessInstance pi = runtimeService.createProcessInstanceQuery()
//                    .processInstanceId(task.getProcessInstanceId()).singleResult();
//            flowTask.setBusinessKey(pi.getBusinessKey());
//            ProjectDemandCollectionApply addApply = projectDemandCollectionApplyMapper.selectByPrimaryKey(pi.getBusinessKey());
//
//            // 流程发起人信息
//            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
//                    .processInstanceId(task.getProcessInstanceId()).singleResult();
////            SysUser startUser = sysUserService.selectUserById(Long.parseLong(task.getAssignee()));
//            if(!StringUtils.isEmpty(historicProcessInstance.getStartUserId())) {
//                SysUser startUser = sysUserService.findById(historicProcessInstance.getStartUserId());
//                flowTask.setStartUserId(startUser.getId());
//                flowTask.setStartUserName(startUser.getNickname());
//            }
////            flowTask.setStartDeptName(startUser.getDept().getDeptName());
//            if(addApply != null)
//            {
//            	flowTask.setSerialNumber(addApply.getSerialNumber());
//	            applyList.add(addApply);
//	            flowList.add(flowTask);
//	        }
//        }
//        flowList = getListByCondition(projectDemandCollectionApplyVo, flowList, applyList);
//
//
//        page.setList(flowList);
//        page.setPageNum(projectDemandCollectionApplyVo.getPageNum());
//        page.setPageSize(projectDemandCollectionApplyVo.getPageSize());
//        return RestApiResponse.ok(page);

    }

    private Criteria getCriteria(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo, Example example) {
        Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandTypeCode())) {
            criteria.andEqualTo("demandTypeCode", projectDemandCollectionApplyVo.getDemandTypeCode());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandSecondTypeName())) {
            criteria.andEqualTo("demandSecondTypeName", projectDemandCollectionApplyVo.getDemandSecondTypeName());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getBatchId())) {
            criteria.andEqualTo("batchId", projectDemandCollectionApplyVo.getBatchId());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getProfessionalFiled())) {
            criteria.andEqualTo("professionalFiled", projectDemandCollectionApplyVo.getProfessionalFiled());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getOrgName())) {
            criteria.andLike("orgName", "%" + projectDemandCollectionApplyVo.getOrgName() + "%");
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandName())) {
            criteria.andLike("demandName", "%" + projectDemandCollectionApplyVo.getDemandName() + "%");
        }
        return criteria;
    }

    /**
     * 代办已办列表查询条件
     * @param projectDemandCollectionApplyVo
     * @param flowList
     * @param applyList
     * @return
     */
    private List<FlowTaskDto> getListByCondition(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo, List<FlowTaskDto> flowList, List<ProjectDemandCollectionApply> applyList) {
        // 查询条件
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandTypeCode())) {
            applyList = applyList.stream().filter(p -> p.getDemandTypeCode().equals(projectDemandCollectionApplyVo.getDemandTypeCode())).collect(Collectors.toList());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandSecondTypeName())) {
            applyList = applyList.stream().filter(p -> p.getDemandSecondTypeName().contains(projectDemandCollectionApplyVo.getDemandSecondTypeName())).collect(Collectors.toList());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getBatchId())) {
            applyList = applyList.stream().filter(p -> p.getBatchId().equals(projectDemandCollectionApplyVo.getBatchId())).collect(Collectors.toList());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getProfessionalFiled())) {
            applyList = applyList.stream().filter(p -> p.getProfessionalFiled().equals(projectDemandCollectionApplyVo.getProfessionalFiled())).collect(Collectors.toList());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getOrgName())) {
            applyList = applyList.stream().filter(p -> p.getOrgName().contains(projectDemandCollectionApplyVo.getOrgName())).collect(Collectors.toList());
        }
        if (!StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandName())) {
            applyList = applyList.stream().filter(p -> p.getDemandName().contains(projectDemandCollectionApplyVo.getDemandName())).collect(Collectors.toList());
        }
        
        applyList = applyList.stream().filter(p -> p != null).collect(Collectors.toList());
        if(applyList.size() > 0) {
        	List<String> ids = applyList.stream().map(ProjectDemandCollectionApply::getId).collect(Collectors.toList());
            if ((!StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandTypeCode()) ||
                    !StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandSecondTypeName()) ||
                    !StringUtils.isEmpty(projectDemandCollectionApplyVo.getBatchId()) ||
                    !StringUtils.isEmpty(projectDemandCollectionApplyVo.getProfessionalFiled()) ||
                    !StringUtils.isEmpty(projectDemandCollectionApplyVo.getOrgName()) ||
                    !StringUtils.isEmpty(projectDemandCollectionApplyVo.getDemandName()))) {
                flowList = flowList.stream().filter(p -> ids.contains(p.getBusinessKey())).collect(Collectors.toList());
            }
        }
        
        return flowList;
    }

    /**
     * 流程完成时间处理
     *
     * @param ms
     * @return
     */
    private String getDate(long ms) {

        long day = ms / (24 * 60 * 60 * 1000);
        long hour = (ms / (60 * 60 * 1000) - day * 24);
        long minute = ((ms / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long second = (ms / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - minute * 60);

        if (day > 0) {
            return day + "天" + hour + "小时" + minute + "分钟";
        }
        if (hour > 0) {
            return hour + "小时" + minute + "分钟";
        }
        if (minute > 0) {
            return minute + "分钟";
        }
        if (second > 0) {
            return second + "秒";
        } else {
            return 0 + "秒";
        }
    }

    /**
     * 已办任务列表
     *
     * @return
     */
    @Override
    public PageInfo finishedList(ProjectDemandCollectionApplyVo vo,String userId) {

        Example example = new Example(ProjectDemandCollectionApply.class);
        Criteria criteria = getCriteria(vo, example);
        example.orderBy("createTime").desc();
        PageInfo pageInfo = new PageInfo<ProjectDemandCollectionApply>(flowCommonService.finishedList(vo, this.mapper, example, criteria, vo.getPageNum(),vo.getPageSize()));
//        List<FlowTaskDto> tasks = new ArrayList<>();
//        pageInfo.getList().forEach(p->{
//            FlowTaskDto task = flowCommonService.findTaskDto((BaseEntity) p);
//            List list = new ArrayList();
//            list.add(p);
//            task.setApplyList(list);
//            tasks.add(task);
//        });
//        pageInfo.setList(tasks);
        return pageInfo;
//        PageInfo<FlowTaskDto> page = new PageInfo<>();
//        HistoricTaskInstanceQuery taskInstanceQuery = historyService.createHistoricTaskInstanceQuery()
//                .includeProcessVariables()
//                .finished()
//                .taskAssignee(userId)
//                .orderByHistoricTaskInstanceEndTime()
//                .desc();
//        List<HistoricTaskInstance> historicTaskInstanceList = taskInstanceQuery.listPage(projectDemandCollectionApplyVo.getPageNum() - 1, projectDemandCollectionApplyVo.getPageSize());
//        List<FlowTaskDto> hisTaskList = Lists.newArrayList();
//        List<ProjectDemandCollectionApply> applyList = Lists.newArrayList();
//        for (HistoricTaskInstance histTask : historicTaskInstanceList) {
//            FlowTaskDto flowTask = new FlowTaskDto();
//            // 当前流程信息
//            flowTask.setTaskId(histTask.getId());
//            // 审批人员信息
//            flowTask.setCreateTime(histTask.getCreateTime());
//            flowTask.setFinishTime(histTask.getEndTime());
//            flowTask.setDuration(getDate(histTask.getDurationInMillis()));
//            flowTask.setProcDefId(histTask.getProcessDefinitionId());
//            flowTask.setTaskDefKey(histTask.getTaskDefinitionKey());
//            flowTask.setTaskName(histTask.getName());
//
//            // 流程定义信息
//            ProcessDefinition pd = repositoryService.createProcessDefinitionQuery()
//                    .processDefinitionId(histTask.getProcessDefinitionId()).singleResult();
//            flowTask.setDeployId(pd.getDeploymentId());
//            flowTask.setProcDefName(pd.getName());
//            flowTask.setProcDefVersion(pd.getVersion());
//            flowTask.setProcInsId(histTask.getProcessInstanceId());
//            flowTask.setHisProcInsId(histTask.getProcessInstanceId());
//
//            // 通过任务对象获取流程实例
//            ProcessInstance pi = runtimeService.createProcessInstanceQuery()
//                    .processInstanceId(histTask.getProcessInstanceId()).singleResult();
//            if(null != pi)
//            {
//	            flowTask.setBusinessKey(pi.getBusinessKey());
//	            ProjectDemandCollectionApply addApply = projectDemandCollectionApplyMapper.selectByPrimaryKey(pi.getBusinessKey());
//	            // 流程发起人信息
//	            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
//	                    .processInstanceId(histTask.getProcessInstanceId()).singleResult();
//	            SysUser startUser = sysUserService.findById(historicProcessInstance.getStartUserId());
//	            flowTask.setStartUserId(startUser.getNickname());
//	            flowTask.setStartUserName(startUser.getNickname());
////	            flowTask.setStartDeptName(startUser.getDept().getDeptName());
//	            if(null != addApply) {
//	            	flowTask.setSerialNumber(addApply.getSerialNumber());
//	                applyList.add(addApply);
//	                hisTaskList.add(flowTask);
//	            }
//            }
//        }
//
//        // 查询条件
//        hisTaskList = getListByCondition(projectDemandCollectionApplyVo, hisTaskList, applyList);
//
//        page.setTotal(hisTaskList.size());
//        page.setPageNum(projectDemandCollectionApplyVo.getPageNum());
//        page.setPageSize(projectDemandCollectionApplyVo.getPageSize());
//        page.setList(hisTaskList);
//        return RestApiResponse.ok(page);
    }

    @Override
    public ProjectDemandCollectionGuid intergrateData(List<String> ids) {
        ProjectDemandCollectionGuid guid = new ProjectDemandCollectionGuid();
        Example example = new Example(ProjectDemandCollectionApply.class);
        example.createCriteria().andIn("id", ids);
        List<ProjectDemandCollectionApply> projectDemandCollectionApplies = this.mapper.selectByExample(example);
        if(!CollectionUtils.isEmpty(projectDemandCollectionApplies)){
            ProjectDemandCollectionApply apply = projectDemandCollectionApplies.get(0);
            BeanUtils.copyProperties(apply, guid);
            projectDemandCollectionApplies.forEach(a -> {
                guid.setOrgName(guid.getOrgName() + "," + a.getOrgName());
                guid.setResearchContents(guid.getResearchContents() + "," + a.getResearchContents());
                guid.setTechnicalIndicators(guid.getTechnicalIndicators() + "," + a.getTechnicalIndicators());
                guid.setExpertedResults(guid.getExpertedResults() + "," + a.getExpertedResults());
                guid.setProfessionalFiled(guid.getProfessionalFiled() + "," + a.getProfessionalFiled());
                guid.setTimeLimit(guid.getTimeLimit() + "," + a.getTimeLimit());
            });
        }
        return guid;
    }

    private String getFildeSerialNumber(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo) {
    	String serial = projectDemandCollectionApplyMapper.getSerial(projectDemandCollectionApplyVo.getProfessionalFiledCode(),projectDemandCollectionApplyVo.getBatchId());
    	
    	if(StringUtils.isEmpty(serial)) {    		
    		serial = String.valueOf(DateUtils.getYearOfDate(DateUtils.getCurrentDate()));
    	}
    	String maxSerial = projectDemandCollectionApplyMapper.getMaxSerial(serial);
    	
    	if(StringUtils.isEmpty(maxSerial)) {
    		maxSerial = serial + "0001";
    	}else {
    		maxSerial = String.valueOf(Integer.valueOf(maxSerial).intValue() + 1);
    	}
    	
    	return maxSerial;
    }
}
