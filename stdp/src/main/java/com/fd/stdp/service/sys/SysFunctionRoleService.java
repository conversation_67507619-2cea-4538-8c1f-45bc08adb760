package com.fd.stdp.service.sys;

import java.util.List;

import com.fd.stdp.beans.sys.SysFunctionRole;
import com.fd.stdp.beans.sys.vo.SysFunctionRoleVo;
import com.github.pagehelper.PageInfo;

/**
 * @Description:
 * @Author: qyj
 * @Date: 2020-07-12 15:59:28
 */
public interface SysFunctionRoleService {

    /**
     * @param sysFunctionRole 对象
     * @return String ID
     * @Description: 保存或更新
     * @Author: qyj
     */
    String saveOrUpdateSysFunctionRole(SysFunctionRole sysFunctionRole);

    /**
     * @param id void ID
     * @Description: 删除
     * @Author: qyj
     */
    void deleteSysFunctionRole(String id);

    /**
     * @param id
     * @return SysFunctionRole
     * @Description: 查询详情
     * @Author: qyj
     */
    SysFunctionRole findById(String id);

    /**
     * @param sysFunctionRoleVo
     * @return PageInfo<SysFunctionRole>
     * @Description: 分页查询
     * @Author: qyj
     */
    PageInfo<SysFunctionRole> findPageByQuery(SysFunctionRoleVo sysFunctionRoleVo);

    /**
     * 查找所有的functionRole
     *
     * @return
     */
    List<SysFunctionRole> findAll();

}
