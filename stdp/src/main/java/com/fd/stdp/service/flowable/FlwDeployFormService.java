package com.fd.stdp.service.flowable;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.flowable.FlwDeployForm;
import com.fd.stdp.beans.flowable.FlwForm;
import com.fd.stdp.beans.flowable.vo.FlwDeployFormVo;
/**
 *@Description: 流程实例关联表单
 *@Author: linqiang
 *@Date: 2021-10-15 08:59:48
 */
public interface FlwDeployFormService {

	/**
	 *@Description: 保存或更新流程实例关联表单
	 *@param flwDeployForm 流程实例关联表单对象
	 *@return String 流程实例关联表单ID
	 *@Author: linqiang
	 */
	String saveOrUpdateFlwDeployForm(FlwDeployForm flwDeployForm);
	
	/**
	 *@Description: 删除流程实例关联表单
	 *@param id void 流程实例关联表单ID
	 *@Author: linqiang
	 */
	void deleteFlwDeployForm(String id);

	/**
	 *@Description: 查询流程实例关联表单详情
	 *@param id
	 *@return FlwDeployForm
	 *@Author: linqiang
	 */
	FlwDeployForm findById(String id);

	/**
	 *@Description: 分页查询流程实例关联表单
	 *@param flwDeployFormVo
	 *@return PageInfo<FlwDeployForm>
	 *@Author: linqiang
	 */
	PageInfo<FlwDeployForm> findPageByQuery(FlwDeployFormVo flwDeployFormVo);
	
	/**
     * 查询流程实例关联表单列表
     * 
     * @param flwDeployForm 流程实例关联表单
     * @return 流程实例关联表单集合
     */
    List<FlwDeployForm> findFlwDeployFormList(FlwDeployForm flwDeployForm);
	
	/**
     * 批量删除流程实例关联表单
     * 
     * @param ids 需要删除的流程实例关联表单ID
     * @return 结果
     */
    int deleteFlwDeployFormByIds(String[] ids);
	
	 /**
     * 查询流程挂着的表单
     * @param deployId
     * @return
     */
    FlwForm findFlwDeployFormByDeployId(String deployId);

	List<FlwForm> findFlwDeployFormByDeployIds(List deployIds);
}
