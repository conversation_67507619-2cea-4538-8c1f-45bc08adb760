package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationOtherInnovationApply;
import com.fd.stdp.beans.innovation.vo.InnovationOtherInnovationApplyVo;
/**
 *@Description: 其他载体
 *@Author: wangsh
 *@Date: 2022-03-07 19:46:34
 */
public interface InnovationOtherInnovationApplyService {

	/**
	 *@Description: 保存或更新其他载体
	 *@param innovationOtherInnovationApply 其他载体对象
	 *@return String 其他载体ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationOtherInnovationApply(InnovationOtherInnovationApplyVo innovationOtherInnovationApply);
	
	/**
	 *@Description: 删除其他载体
	 *@param id void 其他载体ID
	 *@Author: wangsh
	 */
	void deleteInnovationOtherInnovationApply(String id);

	/**
	 * @Description: 批量删除其他载体
	 * @param ids
	 */
    void deleteMultiInnovationOtherInnovationApply(List<String> ids);

	/**
	 *@Description: 查询其他载体详情
	 *@param id
	 *@return InnovationOtherInnovationApply
	 *@Author: wangsh
	 */
	InnovationOtherInnovationApply findById(String id);

	/**
	 *@Description: 分页查询其他载体
	 *@param innovationOtherInnovationApplyVo
	 *@return PageInfo<InnovationOtherInnovationApply>
	 *@Author: wangsh
	 */
	PageInfo<InnovationOtherInnovationApply> findPageByQuery(InnovationOtherInnovationApplyVo innovationOtherInnovationApplyVo);
	
	
	/**
	 * 提交
	 * @param innovationOtherInnovationApplyVo
	 * @return
	 */
    String submitInnovationOtherInnovationApply(InnovationOtherInnovationApplyVo innovationOtherInnovationApplyVo);

	/**
	 * 审核
	 * @param innovationOtherInnovationApplyVo
	 * @return
	 */
	String auditInnovationOtherInnovationApply(InnovationOtherInnovationApplyVo innovationOtherInnovationApplyVo);

	/**
	 * 退回
	 * @param innovationOtherInnovationApplyVo
	 * @return
	 */
	String sendBackInnovationOtherInnovationApply(InnovationOtherInnovationApplyVo innovationOtherInnovationApplyVo);

	/**
	 * 任务书下达
	 * @param innovationOtherInnovationApplyVo
	 * @return
	 */
	String releaseInnovationOtherInnovationApply(InnovationOtherInnovationApplyVo innovationOtherInnovationApplyVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnovationOtherInnovationApply> todoList(InnovationOtherInnovationApplyVo innovationOtherInnovationApplyVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationOtherInnovationApply> finishedList(InnovationOtherInnovationApplyVo innovationOtherInnovationApplyVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationOtherInnovationApply> endList(InnovationOtherInnovationApplyVo innovationOtherInnovationApplyVo);
}
