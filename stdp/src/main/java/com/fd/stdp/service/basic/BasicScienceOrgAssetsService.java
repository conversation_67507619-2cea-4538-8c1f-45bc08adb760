package com.fd.stdp.service.basic;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicScienceOrgAssets;
import com.fd.stdp.beans.basic.vo.BasicScienceOrgAssetsVo;

/**
 * 机构资产Service接口
 *
 * <AUTHOR>
 * @date 2021-11-09
 */
public interface BasicScienceOrgAssetsService {
    /**
     * @param basicScienceOrgAssets 机构资产对象
     * @return String 机构资产ID
     * @Description: 保存或更新机构资产
     * @Author: yujianfei
     */
    String saveOrUpdateBasicScienceOrgAssets(BasicScienceOrgAssets basicScienceOrgAssets);

    /**
     * @param ids void 机构资产ID
     * @Description: 删除机构资产
     * @Author: yujianfei
     */
    void deleteBasicScienceOrgAssets(List<String> ids);

    /**
     * @param id
     * @return BasicScienceOrgAssets
     * @Description: 查询机构资产详情
     * @Author: yujianfei
     */
    BasicScienceOrgAssets findById(String id);

    /**
     * @param basicScienceOrgAssetsVo
     * @return PageInfo<BasicScienceOrgAssets>
     * @Description: 分页查询机构资产
     * @Author: yujianfei
     */
    PageInfo<BasicScienceOrgAssets> findPageByQuery(BasicScienceOrgAssetsVo basicScienceOrgAssetsVo);
}
