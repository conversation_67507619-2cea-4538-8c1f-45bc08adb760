package com.fd.stdp.service.project.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectApplyDevices;
import com.fd.stdp.beans.project.vo.ProjectApplyDevicesVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectApplyDevicesMapper;
import com.fd.stdp.service.project.ProjectApplyDevicesService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 项目设备购置预算明细Service业务层处理
 * @date 2021-11-26
 */
@Service
@Transactional(readOnly = true)
public class ProjectApplyDevicesServiceImpl extends BaseServiceImpl<ProjectApplyDevicesMapper, ProjectApplyDevices> implements ProjectApplyDevicesService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectApplyDevicesServiceImpl.class);
    @Autowired
    private ProjectApplyDevicesMapper projectApplyDevicesMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新项目设备购置预算明细
     *@param projectApplyDevices 项目设备购置预算明细对象
     *@return String 项目设备购置预算明细ID
     *@Author: yujianfei
     */
    public String saveOrUpdateProjectApplyDevices(ProjectApplyDevices projectApplyDevices) {
        if (projectApplyDevices == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(projectApplyDevices.getId())) {
            //新增
            projectApplyDevices.setId(UUIDUtils.getUUID());
            projectApplyDevicesMapper.insertSelective(projectApplyDevices);
        } else {
            //避免页面传入修改
            projectApplyDevices.setYn(null);
            projectApplyDevicesMapper.updateByPrimaryKeySelective(projectApplyDevices);
        }
        return projectApplyDevices.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除项目设备购置预算明细
     *@param id void 项目设备购置预算明细ID
     *@Author: yujianfei
     */
    public void deleteProjectApplyDevices(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            ProjectApplyDevices projectApplyDevices = projectApplyDevicesMapper.selectByPrimaryKey(id);
            if (projectApplyDevices == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            ProjectApplyDevices temprojectApplyDevices = new ProjectApplyDevices();
            temprojectApplyDevices.setYn(CommonConstant.FLAG_NO);
            temprojectApplyDevices.setId(projectApplyDevices.getId());
            projectApplyDevicesMapper.updateByPrimaryKeySelective(temprojectApplyDevices);
        }
    }

    /**
     * @param id
     * @return ProjectApplyDevices
     * @Description: 查询项目设备购置预算明细详情
     * @Author: yujianfei
     */
    @Override
    public ProjectApplyDevices findById(String id) {
        return projectApplyDevicesMapper.selectByPrimaryKey(id);
    }


    /**
     * @param projectApplyDevicesVo
     * @return PageInfo<ProjectApplyDevices>
     * @Description: 分页查询项目设备购置预算明细
     * @Author: yujianfei
     */
    @Override
    public PageInfo<ProjectApplyDevices> findPageByQuery(ProjectApplyDevicesVo projectApplyDevicesVo) {
        PageHelper.startPage(projectApplyDevicesVo.getPageNum(), projectApplyDevicesVo.getPageSize());
        Example example = new Example(ProjectApplyDevices.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(projectApplyDevicesVo.getName())){
        //	criteria.andEqualTo(projectApplyDevicesVo.getName());
        //}
        List<ProjectApplyDevices> projectApplyDevicesList = projectApplyDevicesMapper.selectByExample(example);
        return new PageInfo<ProjectApplyDevices>(projectApplyDevicesList);
    }
}
