package com.fd.stdp.service.basic;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonExpertChange;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertChangeVo;
/**
 *@Description: 专家变更表
 *@Author: wangsh
 *@Date: 2022-06-23 15:07:54
 */
public interface BasicPersonExpertChangeService {

	/**
	 *@Description: 保存或更新专家变更表
	 *@param basicPersonExpertChange 专家变更表对象
	 *@return String 专家变更表ID
	 *@Author: wangsh
	 */
	String saveOrUpdateBasicPersonExpertChange(BasicPersonExpertChangeVo basicPersonExpertChange);
	
	/**
	 *@Description: 删除专家变更表
	 *@param id void 专家变更表ID
	 *@Author: wangsh
	 */
	void deleteBasicPersonExpertChange(String id);

	/**
	 * @Description: 批量删除专家变更表
	 * @param ids
	 */
    void deleteMultiBasicPersonExpertChange(List<String> ids);

	/**
	 *@Description: 查询专家变更表详情
	 *@param id
	 *@return BasicPersonExpertChange
	 *@Author: wangsh
	 */
	BasicPersonExpertChange findById(String id);

	/**
	 *@Description: 分页查询专家变更表
	 *@param basicPersonExpertChangeVo
	 *@return PageInfo<BasicPersonExpertChange>
	 *@Author: wangsh
	 */
	PageInfo<BasicPersonExpertChange> findPageByQuery(BasicPersonExpertChangeVo basicPersonExpertChangeVo);
	
	
	/**
	 * 提交
	 * @param basicPersonExpertChangeVo
	 * @return
	 */
    String submitBasicPersonExpertChange(BasicPersonExpertChangeVo basicPersonExpertChangeVo);

	/**
	 * 审核
	 * @param basicPersonExpertChangeVo
	 * @return
	 */
	String auditBasicPersonExpertChange(BasicPersonExpertChangeVo basicPersonExpertChangeVo);

	/**
	 * 退回
	 * @param basicPersonExpertChangeVo
	 * @return
	 */
	String sendBackBasicPersonExpertChange(BasicPersonExpertChangeVo basicPersonExpertChangeVo);

	/**
	 * 任务书下达
	 * @param basicPersonExpertChangeVo
	 * @return
	 */
	String releaseBasicPersonExpertChange(BasicPersonExpertChangeVo basicPersonExpertChangeVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<BasicPersonExpertChange> todoList(BasicPersonExpertChangeVo basicPersonExpertChangeVo);

	/**
	 * 已办列表
	 */
	PageInfo<BasicPersonExpertChange> finishedList(BasicPersonExpertChangeVo basicPersonExpertChangeVo);

	/**
	 * 已完成列表
	 */
	PageInfo<BasicPersonExpertChange> endList(BasicPersonExpertChangeVo basicPersonExpertChangeVo);
}
