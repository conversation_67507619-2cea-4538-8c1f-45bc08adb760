package com.fd.stdp.service.sys.impl;

import java.util.*;
import java.util.regex.Pattern;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fd.stdp.beans.rest.*;

import java.net.URI;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import com.fd.stdp.config.properties.ZhuanjiaProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import com.fd.stdp.beans.basic.BasicManageOrg;
import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.basic.vo.BasicScienceOrgVo;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.config.SSLRestTemplate;
import com.fd.stdp.config.properties.ExpertProperties;
import com.fd.stdp.config.properties.OrgProperties;
import com.fd.stdp.config.properties.SuperviseProperties;
import com.fd.stdp.dao.basic.BasicManageOrgMapper;
import com.fd.stdp.dao.basic.BasicScienceOrgMapper;
import com.fd.stdp.service.sys.UserRoleService;
import com.fd.stdp.util.AppUserUtil;
import com.fd.stdp.util.Base62Helper;
import com.fd.stdp.util.RSAEncrypt;
import com.github.pagehelper.PageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import com.fd.stdp.beans.sys.SysDictItem;
import com.fd.stdp.beans.sys.SysRole;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.beans.sys.SysUserFunctionRole;
import com.fd.stdp.beans.sys.SysUserRole;
import com.fd.stdp.beans.sys.vo.SysUserVo;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.dao.sys.SysRoleMapper;
import com.fd.stdp.dao.sys.SysRolePermissionMapper;
import com.fd.stdp.dao.sys.SysUserFunctionRoleMapper;
import com.fd.stdp.dao.sys.SysUserMapper;
import com.fd.stdp.dao.sys.SysUserRoleMapper;
import com.fd.stdp.service.sys.RoleService;
import com.fd.stdp.service.sys.SysDictItemService;
import com.fd.stdp.service.sys.SysUserService;
import com.fd.stdp.util.UUIDUtils;
import com.github.pagehelper.PageInfo;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional
/**
 * @Description: 系统用户表
 * @Author: qyj
 * @Date: 2020-07-05 16:25:12
 */
public class SysUserServiceImpl extends BaseServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    public static final Logger logger = LoggerFactory.getLogger(SysUserServiceImpl.class);

    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private RoleService roleService;
    @Autowired
    private SysRolePermissionMapper sysRolePermissionMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysUserFunctionRoleMapper sysUserFunctionRoleMapper;

    @Autowired
    private BasicScienceOrgMapper basicScienceOrgMapper;

    @Autowired
    private ExpertProperties expertProperties;

    @Autowired
    private OrgProperties orgProperties;

    @Autowired
    private SuperviseProperties superviseProperties;

    @Autowired
    private ZhuanjiaProperties zhuanjiaProperties;

    @Autowired
    private UserRoleService userRoleService;

    @Value("${expertLogin.getLoginInfoUrl}")
    private String expertLoginUrl;

    @Lazy
    @Autowired
    private UserDetailsServiceImpl userDetailsServiceImpl;

    @Autowired
    private BasicManageOrgMapper basicManageOrgMapper;

    @Autowired
    private SysDictItemService sysDictItemService;
    // 数字
    public static final String REGEX_NUM = "^[-]?\\d+[.]?\\d*$";
    // 字母
    public static final String REGEX_CHAR = "^[A-Za-z]+$";
    // 数字+字母
    public static final String REGEX_NUM_AND_CHAR = "^[A-Za-z0-9]+$";
    // 数字+字母+特殊字符
    public static final String REGEX = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@#$%^&+=!*()])[A-Za-z\\d@#$%^&+=!*()]{8,16}$";
    private BCryptPasswordEncoder bp = new BCryptPasswordEncoder();

    /**
     * 检查用户类型是否匹配
     *
     * @param userType     用户类型值
     * @param userTypeCode 字典编码（SSO_LOGIN_PERSON 或 SSO_LOGIN_ORG）
     * @return 是否匹配
     */
    private boolean isUserTypeMatch(Integer userType, String userTypeCode) {
        if (userType == null) {
            return false;
        }
        try {
            SysDictItem dictItem = sysDictItemService.findById(userTypeCode);
            System.out.println(dictItem);
            if (dictItem != null && !StringUtils.isEmpty(dictItem.getItemDesc())) {
                String itemDesc = dictItem.getItemDesc();
                // 处理逗号分隔的值
                String[] typeValues = itemDesc.split(",");
                for (String typeValue : typeValues) {
                    try {
                        if (Integer.parseInt(typeValue.trim()) == userType) {
                            return true;
                        }
                    } catch (NumberFormatException e) {
                        logger.warn("字典值格式错误，无法转换为数字: {}", typeValue.trim());
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("检查用户类型匹配失败，userTypeCode: {}", userTypeCode, e);
        }
        return false;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT, rollbackFor = {
            RuntimeException.class})
    /**
     * @Description: 保存或更新系统用户表
     * @param sysUser 系统用户表对象
     * @return String 系统用户表ID
     * @Author: qyj
     */
    public String saveOrUpdateSysUser(SysUserVo sysUser) {
        if (null == sysUser) {
            throw new ServiceException("数据异常");
        }

        /**
         * 本操作涉及到3张表 系统用户表 sys_user 系统用户详情表 sys_user_detail 系统用户角色表 sys_user_role
         * 操作分为两类，一类是新增一类是修改 新增操作： 1.添加数据到sys_user中 2.将sys_user的id添加到sys_user_detail中
         * 3.在sys_user_role中关联sys_user中id与sys_role的id 更新操作：
         * 1.更新sys_user表，根据sys_user的主键id更新表中内容即可
         * 2.更新sys_user_detail表，根据sys_user的id查询detail表中的对应的userID更新内容即可
         * 3.更新sys_user_role表，根据sys_user的id查询出所有的角色，再更新sys_user_role表中内容即可
         */
        // 新增操作
        if (StringUtils.isEmpty(sysUser.getId())) {
            // 数据校验
            checkData(sysUser);
            // 1.添加数据到sys_user中
            // 首先根据用户的登录名 查询username是否重复
            SysUser user = this.getUser(sysUser.getUsername());
            if (null != user) {
                throw new ServiceException("登录名重复");
            }
            SysUser addUser = new SysUser();
            encapSysUser(sysUser, addUser);
            sysUserMapper.insertSelective(addUser);
            List<SysUserRole> sysUserRoles = encapSysUserRole(sysUser);
            for (SysUserRole sysUserRole : sysUserRoles) {
                sysUserRoleMapper.insert(sysUserRole);
            }
            //sysUserRoleMapper.insertList(sysUserRoles);
        } else {
            // 更新操作
            SysUser addUser = sysUserMapper.selectByPrimaryKey(sysUser.getId());
            if (null == addUser) {
                throw new ServiceException("跟新用户时,参数异常");
            }
            encapSysUser(sysUser, addUser);
            sysUserMapper.updateByPrimaryKeySelective(addUser);
            List<SysUserRole> sysUserRoles = encapSysUserRole(sysUser);
            for (SysUserRole sysUserRole : sysUserRoles) {
                sysUserRoleMapper.insert(sysUserRole);
            }
            //sysUserRoleMapper.insertList(sysUserRoles);
        }
        return sysUser.getId();
    }

    /**
     * 对前端提交的数据做校验
     *
     * @param sysUser 前端提交的数据
     */
    private void checkData(SysUserVo sysUser) {
        // 校验用户名
        if (StringUtils.isEmpty(sysUser.getUsername())) {
            throw new ServiceException("用户登录名必须填写");
        }
        String trim = sysUser.getUsername().trim();
        if (trim.length() > CommonConstant.USER_NAME_LENGTH) {
            throw new ServiceException("用户名不得超过" + CommonConstant.USER_NAME_LENGTH + "位");
        }
        sysUser.setUsername(trim);
        // 校验用户密码
        if (StringUtils.isEmpty(sysUser.getPassword())) {
            throw new ServiceException("用户密码必须填写");
        }
        String password = null;
        try {
            password = RSAEncrypt.privateKeyDecrypt(sysUser.getPassword(), RSAEncrypt.PRIVATE_KEY);
        } catch (Exception e) {
            throw new ServiceException("参数异常");
        }
        if (password.length() > CommonConstant.USER_PASSWORD_LENGTH) {
            throw new ServiceException("用户密码长度不得超过" + CommonConstant.USER_PASSWORD_LENGTH + "位");
        }
        if (password.length() < CommonConstant.MINSIZE) {
            throw new ServiceException("用户密码长度不得少于" + CommonConstant.MINSIZE + "位");
        }
        // 新密码正则校验
        if (!Pattern.matches(REGEX, password)) {
            throw new ServiceException("您的新密码复杂度太低（密码中必须包含大写字母、小写字母、数字、特殊字符）");
        }
    }

    /**
     * 将页面提交的数据封装到系统用户权限中
     *
     * @param sysUser 页面提交的数据
     * @return 多个function_role角色
     */
    private List<SysUserFunctionRole> encapFunctionRole(SysUserVo sysUser) {
        if (StringUtils.isEmpty(sysUser.getId())) {
            sysUser.setId(UUIDUtils.getUUID());
        }
        List<String> roleIds = sysUser.getRoleFunctionIds();
        // 先删除所有角色
        Example example = new Example(SysUserFunctionRole.class);
        example.createCriteria().andEqualTo("userId", sysUser.getId());
        sysUserFunctionRoleMapper.deleteByExample(example);
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<SysUserFunctionRole> sysUserRoles = new ArrayList<>();
            for (String roleId : roleIds) {
                SysUserFunctionRole sysUserFunctionRole = new SysUserFunctionRole();
                sysUserFunctionRole.setId(UUIDUtils.getUUID());
                sysUserFunctionRole.setRoleId(roleId);
                sysUserFunctionRole.setUserId(sysUser.getId());
                sysUserFunctionRole.setYn(1);
                sysUserRoles.add(sysUserFunctionRole);
            }
            return sysUserRoles;
        }
        return null;
    }

    /**
     * 将页面提交的数据封装到系统用户权限中
     *
     * @param sysUser 页面提交的数据
     * @return 多个role角色
     */
    private List<SysUserRole> encapSysUserRole(SysUserVo sysUser) {
        if (StringUtils.isEmpty(sysUser.getId())) {
            sysUser.setId(UUIDUtils.getUUID());
        }
        List<String> roleIds = sysUser.getRoleIds();
        // 先删除所有角色
        Example example = new Example(SysUserRole.class);
        example.createCriteria().andEqualTo("userId", sysUser.getId());
        sysUserRoleMapper.deleteByExample(example);
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<SysUserRole> sysUserRoles = new ArrayList<>();
            for (String roleId : roleIds) {
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setId(UUIDUtils.getUUID());
                sysUserRole.setRoleId(roleId);
                sysUserRole.setUserId(sysUser.getId());
                sysUserRole.setYn(1);
                sysUserRoles.add(sysUserRole);
            }
            return sysUserRoles;
        }
        return null;
    }

    /**
     * 将页面提交的数据封装到对应的bean中
     *
     * @param sysUser 页面提交的数据
     * @param addUser 对应的bean
     */
    private void encapSysUser(SysUserVo sysUser, SysUser addUser) {
        if (StringUtils.isEmpty(sysUser.getId())) {
            addUser.setId(UUIDUtils.getUUID());
        } else {
            addUser.setId(sysUser.getId());
        }

        addUser.setNickname(sysUser.getNickname());
        addUser.setUsername(sysUser.getUsername());
        addUser.setUscc(sysUser.getUscc());
        addUser.setAreaCode(sysUser.getAreaCode());
        addUser.setAreaName(sysUser.getAreaName());
        addUser.setYthUserId(sysUser.getYthUserId());
        if (!StringUtils.isEmpty(sysUser.getPassword())) {
            // 密码加密
//			byte[] decode = Base64.getDecoder().decode(sysUser.getPassword());
//			String password = new String(decode);
            String password = null;
            try {
                password = RSAEncrypt.privateKeyDecrypt(sysUser.getPassword(), RSAEncrypt.PRIVATE_KEY);
            } catch (Exception e) {
                throw new ServiceException("参数异常");
            }
            password = bp.encode(password);
            addUser.setPassword(password);
        }
        addUser.setExpertId(sysUser.getExpertId());
        addUser.setPersonId(sysUser.getPersonId());
        addUser.setPhone(sysUser.getPhone());
        addUser.setRemark(sysUser.getRemark());
        addUser.setYn(1);
        addUser.setEnabled(1);
        if (!StringUtils.isEmpty(sysUser.getUserId())) {
            addUser.setId(sysUser.getUserId());
        }
        sysUser.setId(addUser.getId());
        addUser.setType(sysUser.getType());
        addUser.setTypeName(sysUser.getTypeName());

        if (!StringUtils.isEmpty(sysUser.getScienceOrgId())) {
            addUser.setScienceOrgId(sysUser.getScienceOrgId());
            BasicScienceOrg basicScienceOrg = basicScienceOrgMapper.selectByPrimaryKey(sysUser.getScienceOrgId());
            addUser.setScienceOrgName(basicScienceOrg.getOrgName());
            addUser.setAreaCode(basicScienceOrg.getAreaCode());
            addUser.setAreaName(basicScienceOrg.getAreaName());
        }
        if (!StringUtils.isEmpty(sysUser.getManageOrgId())) {
            addUser.setManageOrgId(sysUser.getManageOrgId());
            BasicManageOrg basicManageOrg = basicManageOrgMapper.selectByPrimaryKey(sysUser.getManageOrgId());
            addUser.setManageOrgName(basicManageOrg.getOrgName());
            addUser.setAreaCode(basicManageOrg.getAreaCode());
            addUser.setAreaName(basicManageOrg.getAreaName());
        }
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * @Description: 删除系统用户表
     * @param id 系统用户表ID
     * @Author: qyj
     */
    public void deleteSysUser(List<String> id) {
        if (CollectionUtils.isEmpty(id)) {
            throw new ServiceException("参数异常");
        }
        sysUserMapper.updateYnByIds(id);
    }

    @Override
    /**
     * @Description: 查询系统用户表详情
     * @param id
     * @return SysUser
     * @Author: qyj
     */
    public SysUser findById(String id) {
        if (StringUtils.isEmpty(id)) {
            throw new ServiceException("参数异常");
        }
        SysUserVo sysUserVo = new SysUserVo();
        sysUserVo.setId(id);
        List<SysUserVo> pageByQuery = sysUserMapper.findPageByQuery(sysUserVo);
        if (pageByQuery.isEmpty()) {
            throw new ServiceException("查无此用户");
        }
        return pageByQuery.get(0);
    }

    @Override
    /**
     * @Description: 分页查询系统用户表
     * @param sysUserVo
     * @return PageInfo<SysUser>
     * @Author: qyj
     */
    public PageInfo<SysUserVo> findPageByQuery(SysUserVo sysUserVo) {
        if (null == sysUserVo) {
            throw new ServiceException("参数异常");
        }
        PageHelper.startPage(sysUserVo.getPageNum(), sysUserVo.getPageSize());
        List<SysUserVo> sysUserList = sysUserMapper.findPageByQuery(sysUserVo);
        return new PageInfo<SysUserVo>(sysUserList);
    }

    /**
     * 通过用户名得到用户对象
     *
     * @param username
     * @return
     * @Author: qyj
     */
    @Override
    public SysUser getUser(String username) {
        if (StringUtils.isEmpty(username)) {
            throw new ServiceException("参数异常");
        }
        Example example = new Example(SysUser.class);
        Criteria criteria = example.createCriteria();
        //用户名登陆
        criteria.andEqualTo("username", username).andEqualTo("yn", 1);

//        // 根据手机号登陆
//        Criteria criteria1 = example.createCriteria();
//        criteria1.andEqualTo("phone", username).andEqualTo("yn", 1);

//        example.or(criteria1);
        List<SysUser> sysUsers = sysUserMapper.selectByExample(example);
        if (sysUsers.size() > 1) {
            throw new ServiceException("用户名重复");
        } else if (sysUsers.size() == 0) {
            return null;
        }
        return sysUsers.get(0);
    }

    /**
     * 通过电话得到用户对象
     *
     * @param phone
     * @return
     * @Author: yujianfei
     */
    @Override
    public SysUser getUserByPhone(String phone) {
        if (StringUtils.isEmpty(phone)) {
            throw new ServiceException("联系电话不能为空");
        }
        Example example = new Example(SysUser.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("phone", phone).andEqualTo("yn", 1);

        List<SysUser> sysUsers = sysUserMapper.selectByExample(example);
        return sysUsers.stream().findFirst().orElse(null);
    }

    @Override
    public List<SysUser> getUserListByPhone(String phone) {
        if (StringUtils.isEmpty(phone)) {
            throw new ServiceException("联系电话不能为空");
        }
        Example example = new Example(SysUser.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("phone", phone).andEqualTo("yn", 1);

        List<SysUser> sysUsers = sysUserMapper.selectByExample(example);
        return sysUsers;
    }


    /**
     * 通过用户id得到所有的权限code
     *
     * @param id
     * @return
     * @Author: qyj
     */
    @Override
    public Set<String> listElementByUserId(String id) {
        Set<String> re = new HashSet<String>();
        List<SysRole> roleList = sysRoleMapper.getByUserId(id);
        if (CollectionUtils.isEmpty(roleList)) {
            return re;
        }
        List<String> roleIds = new ArrayList<String>();
        for (SysRole role : roleList) {
            roleIds.add(role.getId());
        }
        List<String> roleCodeList = sysRolePermissionMapper.findElementPermissionCodeByRoleId(roleIds);
        if (CollectionUtils.isEmpty(roleCodeList)) {
            return re;
        }
        for (String code : roleCodeList) {
            re.add(code);
        }
        return re;
    }

    /**
     * 通过用户id得到所有的角色
     *
     * @param id
     * @return
     * @Author: qyj
     */
    @Override
    public List<SysRole> listRolesByUserId(String id) {
        return sysRoleMapper.getByUserId(id);
    }

    /**
     * 通过用户id查询用户信息
     *
     * @param userId 用户id
     * @return 用户信息
     * @Author: qyj
     */
    @Override
    public SysUser getUserById(String userId) {
        if (null == userId) {
            throw new ServiceException("参数异常");
        }
        return sysUserMapper.selectByPrimaryKey(userId);
    }

    /**
     * 修改密码
     *
     * @param sysUser
     */
    @Override
    public void resetPwd(SysUserVo sysUser) {
        if (null == sysUser) {
            throw new ServiceException("参数异常");
        }
        String newPwd = "";
        try {
            newPwd = RSAEncrypt.privateKeyDecrypt(sysUser.getNewPwd(), RSAEncrypt.PRIVATE_KEY);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("参数异常");
        }
        if (newPwd.length() > CommonConstant.MAXSIZE
                || newPwd.length() < CommonConstant.MINSIZE) {
            throw new ServiceException("密码长度最小是8位，最长是18位");
        }

//		byte[] oldPassword = Base64.getDecoder().decode(sysUser.getOldPwd());
        SysUser sysUser1 = sysUserMapper.selectByPrimaryKey(sysUser.getId());
        String oldPassword = null;
        try {
            oldPassword = RSAEncrypt.privateKeyDecrypt(sysUser.getOldPwd(), RSAEncrypt.PRIVATE_KEY);
        } catch (Exception e) {
            throw new ServiceException("参数异常");
        }
        boolean matches = bp.matches(oldPassword, sysUser1.getPassword());
        if (!matches) {
            throw new ServiceException("旧密码错误");
        }
        String password = null;
        try {
            password = RSAEncrypt.privateKeyDecrypt(sysUser.getPassword(), RSAEncrypt.PRIVATE_KEY);
        } catch (Exception e) {
            throw new ServiceException("参数异常");
        }
        if (!newPwd.equals(password)) {
            throw new ServiceException("新密码与确认密码不一致");
        }
        // 新密码正则校验
        if (!Pattern.matches(REGEX, password)) {
            throw new ServiceException("您的新密码复杂度太低（密码中必须包含大写字母、小写字母、数字、特殊字符）");
        }

        sysUser1.setId(sysUser.getId());
//		byte[] decode = Base64.getDecoder().decode(sysUser.getPassword());
//		String s = new String(decode);
        sysUser1.setPassword(bp.encode(password));
        sysUserMapper.updateByPrimaryKey(sysUser1);
    }

    /**
     * 通过主键修改对应用户得密码
     *
     * @param sysUserVo
     * @return
     */
    @Override
    public int updatePasswordById(SysUserVo sysUserVo) {
        if (null == sysUserVo) {
            throw new ServiceException("参数异常");
        }
        SysUser sysUser = sysUserMapper.selectByPrimaryKey(sysUserVo.getId());
        if (null == sysUser) {
            throw new ServiceException("传递错误参数");
        }
//		byte[] decode = Base64.getDecoder().decode(sysUserVo.getPassword());
//		String password = new String(decode);
        String password;
        try {
            password = RSAEncrypt.privateKeyDecrypt(sysUserVo.getPassword(), RSAEncrypt.PRIVATE_KEY);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        password = bp.encode(password);
        sysUser.setPassword(password);
        return sysUserMapper.updateByPrimaryKey(sysUser);
    }

    /**
     * 保存用户信息
     *
     * @param map
     */
    @Override
    @Transactional(readOnly = false)
    public void saveUser(Map<String, String> map) {
        try {
            String userid = map.get("userid");
            String userType = map.get("userType");// fr:企业；gr:个人。
            SysUser user = sysUserMapper.selectByPrimaryKey(userid);
            // 若库中不存在该用户,新增用户
            if (user == null) {
                user = new SysUser();
                user.setId(userid);
                user.setType("gr".equals(userType) ? "PERSONAL" : "COMPANY");
                user.setType("gr".equals(userType) ? "个人用户" : "企业用户");
                user.setEnabled(1);
                user.setNickname(convertObj(map.get("username")));
                user.setUsername(convertObj(map.get("username")));
                user.setPassword(bp.encode(CommonConstant.DEFAULT_PASSWORD));
                user.setSalt(userid.hashCode() + "");
                user.setYn(1);

                if ("gr".equals(userType)) {
                    user.setSex(convertObj(map.get("sex")));
                    user.setPhone(convertObj(map.get("mobile")));
                } else {
                    user.setPhone(convertObj(map.get("attnPhone")));
                }
                sysUserMapper.insertSelective(user);
            }
        } catch (Exception ex) {
            logger.error("单点登录存储用户信息失败", ex);
        } finally {

        }
    }

    /**
     * Object数据类型转换
     */
    private String convertObj(Object value) {
        return value != null ? value.toString() : "";
    }

    @Override
    public List<SysUser> findAllUsers() {
        return sysUserMapper.selectAll();
    }


    /**
     * @return RestApiResponse<?>
     * @Description: 初始化用户
     * @Author: yujianfei
     */
    @Override
    public void initUser() {
        List<BasicManageOrg> orgList = basicManageOrgMapper.selectAll();
        if (!CollectionUtils.isEmpty(orgList)) {
            for (BasicManageOrg basicManageOrg : orgList) {
                SysUserVo sysUser = new SysUserVo();
                sysUser.setManageOrgId(basicManageOrg.getId());
                sysUser.setNickname(basicManageOrg.getOrgName());
                sysUser.setUsername("W" + basicManageOrg.getAreaCode());
                //市局
                List<String> roleList = new LinkedList<>();
                if (basicManageOrg.getAreaCode().endsWith("00")) {
                    roleList.add("f194f7d4389642d1aa0fc943ca26f7da");
                } else {
                    //县局
                    roleList.add("275d8f49d38a413a97c8d3ed4066460a");
                }
                sysUser.setRoleIds(roleList);
                sysUser.setType("PERSONAL");
                sysUser.setTypeName("个人用户");
                sysUser.setPassword("000000");

                SysUser addUser = new SysUser();
                encapSysUser(sysUser, addUser);
                sysUserMapper.insertSelective(addUser);
                List<SysUserRole> sysUserRoles = encapSysUserRole(sysUser);
                for (SysUserRole sysUserRole : sysUserRoles) {
                    sysUserRoleMapper.insert(sysUserRole);
                }
            }
        }
    }

    /**
     * @param areaCode 角色CODE
     * @return RestApiResponse<?>
     * @Description: 根据角色CODE查询出所有系统用户表
     * @Author: yujianfei
     */
    @Override
    public PageInfo<SysUser> findPageByRoleCode(String areaCode) {
        if (null == areaCode) {
            throw new ServiceException("参数异常");
        }
        List<SysUser> sysUserList = sysUserMapper.findPageByRoleCode(areaCode);
        return new PageInfo<SysUser>(sysUserList);
    }

    @Override
    public PageInfo<SysUserVo> findExpertPageByQuery(SysUserVo sysUserVo) {
        PageHelper.startPage(sysUserVo.getPageNum(), sysUserVo.getPageSize());
        List<SysUserVo> sysUserList = sysUserMapper.findExpertPageByQuery(sysUserVo);
        return new PageInfo<SysUserVo>(sysUserList);
    }

    /**
     * @return RestApiResponse<?>
     * @Description: 通过调用浙里检接口 查询浙里检数据
     * @Author: liuwei
     */
    @Override
    public Object getZLJInfo() {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders header = new HttpHeaders();
        Map<String, String> contentMap = new HashMap<String, String>();
        HttpEntity<Map<String, String>> httpEntity = new HttpEntity<>(contentMap, header);
        Object result = restTemplate.postForObject(CommonConstant.ZLJ_POST_URL, httpEntity, Object.class);
        return result;
    }


    @Override
    public LoginUser ythLogin(SysUserYthoauth sysUserYthoauth) {
        if (sysUserYthoauth == null || org.apache.commons.lang3.StringUtils.isBlank(sysUserYthoauth.getYthuserId()) || org.apache.commons.lang3.StringUtils.isBlank(sysUserYthoauth.getPhone())) {
            throw new ServiceException("手机不能为空");
        }
        try {
            // 获取用户信息
            SysUser sysUser = mapper.findByYthUserid(sysUserYthoauth.getYthuserId(), sysUserYthoauth.getPhone());
            if (sysUser == null) {
                return null;
            }
            //如果sysUser.getYthUserId是空的或者是null
            if (sysUser.getYthUserId() == null || sysUser.getYthUserId().equals("") || org.apache.commons.lang3.StringUtils.isBlank(sysUser.getYthUserId())) {
                //把sysUserYthoauth.getYthuserId()更新到sysUser.getYthUserId里去
                sysUser.setYthUserId(sysUserYthoauth.getYthuserId());
                mapper.updateByPrimaryKeySelective(sysUser);
            }
            return userDetailsServiceImpl.getLoginUser(sysUser);
        } catch (Exception e) {
            throw new BadCredentialsException("登录失败");
        }
    }

    /**
     * 政务网登录（企业机构）
     *
     * @param form
     * @return
     */
    @Override
    public LoginUser zwwOrgLoginApi(ZwwSsoBody form) {
        ResponseEntity<JSONObject> responseEntity = postForZljSsoLogin(form);
        JSONObject res = responseEntity.getBody();
        if (res == null) {
            throw new ServiceException("登录凭据失效，请重新登录");
        }
        if (res.getInteger("code") == 0) {
            JSONObject data = res.getJSONObject("data");
            logger.info("政务网data:{}", data);
            // 根据UserType判断账号类型：通过字典配置判断企业账号类型
            Integer userType = data.getInteger("UserType");
            if (!isUserTypeMatch(userType, "SSO_LOGIN_ORG")) {
                throw new ServiceException("认证失败，当前不是法人账号，请使用法人账号进行登录");
            }

            JSONArray companyList = data.getJSONArray("CompanyList");
            if (companyList == null || companyList.isEmpty()) {
                throw new ServiceException("认证失败，企业信息不完整，请使用有效的企业账号进行登录");
            }

            String uscc = companyList.getJSONObject(0).getString("CreditCode");
            String orgName = data.getString("UserName");
            String phone = data.getString("Mobile");
            logger.info("政务网单点登录成功，企业名称：{}, 手机号：{}, USCC：{}", orgName, phone, uscc);

            // 判断用户是否存在
            String userTypeCode = orgProperties.getUserTypeCode();
            SysUser user = sysUserMapper.getUserByUsernameAndUserType(phone, userTypeCode);

            String usernames = phone + "_" + userTypeCode;

            //   用户不存在时，创建机构信息和用户
            //   如果该用户的类型不是机构管理员
            if (user == null || (user != null && !userTypeCode.equals(user.getType()))) {
                SysUserVo sysUserVo = new SysUserVo();
                sysUserVo.setPhone(phone);
                sysUserVo.setUsername(usernames);
                sysUserVo.setNickname(orgName);
                sysUserVo.setUscc(uscc);
//				sysUserVo.setZwwUserId(data.getString("ZwwUserID"));
                user = createOrgUsers(sysUserVo);
            }else{
                if (user.getUscc() == null){
                    user.setUscc(uscc);
                    sysUserMapper.updateByPrimaryKeySelective(user);
                }
            }

            return userDetailsServiceImpl.getLoginUser(user);
        } else {
            logger.error("政务网单点登录失败，状态码：{}，消息：{}", responseEntity.getStatusCodeValue(), responseEntity.getBody());
            throw new ServiceException("登录凭据失效，请重新登录");
        }
    }


    /**
     * 政务网个人单点登录
     *
     * @param form
     * @return
     */
    @Override
    public LoginUser zwwPersonLogin(ZwwSsoBody form) {
        ResponseEntity<JSONObject> responseEntity = postForZljSsoLogin(form);
        JSONObject res = responseEntity.getBody();
        if (res == null) {
            throw new ServiceException("登录凭据失效，请重新登录");
        }
        if (res.getInteger("code") == 0) {
            JSONObject data = res.getJSONObject("data");
            logger.info("政务网data:{}", data);

            Integer userType = data.getInteger("UserType");
            if (!isUserTypeMatch(userType, "SSO_LOGIN_PERSON")) {
                throw new ServiceException("认证失败，当前不是个人账号，请使用个人账号进行登录");
            }

            String username = data.getString("UserName");
            String phone = data.getString("Mobile");
            logger.info("政务网单点登录成功，姓名：{}, 手机号：{}", username, phone);

            // 判断用户是否存在  并且用户类型不是机构管理员
            String userTypeCode = orgProperties.getUserTypeCode();

            SysUser user = sysUserMapper.getUserByUsernameAndNoUserType(phone, userTypeCode);

            String usernames = phone + "_PERSON";

            if (user == null || (user != null && orgProperties.getUserTypeCode().equals(user.getType()))) {
                SysUserVo sysUserVo = new SysUserVo();
                sysUserVo.setPhone(phone);
                sysUserVo.setUsername(usernames);
                sysUserVo.setNickname(username);
                user = createExpertUsers(sysUserVo);
            }
            // 如果不存在就创建用户并登录
            return userDetailsServiceImpl.getLoginUser(user);
        } else {
            logger.error("政务网单点登录失败，状态码：{}，消息：{}", responseEntity.getStatusCodeValue(), responseEntity.getBody());
            throw new ServiceException("登录凭据失效，请重新登录");
        }
    }

    @Value("${zwwLogin.getLoginInfoUrl}")
    private String zwwLoginUserUrl;

    @Autowired
    private SSLRestTemplate sslRestTemplate;

    /**
     * 请求浙里检用户信息，完成单点登录
     *
     * @param form
     * @return
     */
    private ResponseEntity<JSONObject> postForZljSsoLogin(ZwwSsoBody form) {
        Assert.notNull(form.getToken(), "token 不能为空");
//        AssertUtil.notNull(form.getUserType(), "userType 不能为空");
        String token = Base62Helper.base62Decode(form.getToken());
        if (token == null || token.length() < 64) {
            throw new ServiceException("token无效，请重新登录政务网后重试");
        }
        String userID = token.substring(25, 40).trim();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add("Referer", zwwLoginUserUrl);
        // 请求参数
        Map<String, String> params = new HashMap<>();
        params.put("Token", form.getToken());
        params.put("UserID", userID);
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(params, httpHeaders);

        ResponseEntity<JSONObject> responseEntity = sslRestTemplate.postForEntity(zwwLoginUserUrl, requestEntity, JSONObject.class);
        logger.info("政务网单点登录成功，{}", responseEntity.getBody());
        return responseEntity;
    }


    @Override
    public SysUser createExpertUsers(SysUserVo sysUserVo) {
        // 判断用户是否存在
        SysUser userByUsername = null;
        // 检查专家配置密码是否为空
        String expertPassword = expertProperties.getPassword();
        if (StringUtils.isEmpty(expertPassword)) {
            throw new ServiceException("用户默认密码配置为空，请检查配置文件");
        }
        // 使用RSA加密密码，以便通过checkData验证
        try {
            String encryptedPassword = RSAEncrypt.publicKeyEncrypt(expertPassword, RSAEncrypt.PUBLIC_KEK);
            sysUserVo.setPassword(encryptedPassword);
        } catch (Exception e) {
            throw new ServiceException("密码加密失败");
        }
        SysRole roleByRoleCode = roleService.findRoleByRoleCode(expertProperties.getRoleCode());
        sysUserVo.setType(expertProperties.getUserTypeCode());
        sysUserVo.setTypeName(roleByRoleCode.getRoleName());
        sysUserVo.setRoleIds(Arrays.asList(roleByRoleCode.getId()));
        try {
            userByUsername = getUserByUsername(sysUserVo.getUsername(), expertProperties.getUserTypeCode());
            if (userByUsername != null) {
                userRoleService.addRoles(userByUsername.getId(), Arrays.asList(roleByRoleCode.getId()));
                return userByUsername;
            }
        } catch (ServiceException e) {
        }
        String id = saveOrUpdate(sysUserVo);
        userByUsername = findById(id);
        return userByUsername;
    }

    @Override
    public SysUser createSuperviseUsers(SysUserVo sysUserVo) {
        // 判断用户是否存在
        SysUser userByUsername = null;
        // 检查专家配置密码是否为空
        String expertPassword = superviseProperties.getPassword();
        if (StringUtils.isEmpty(expertPassword)) {
            throw new ServiceException("用户默认密码配置为空，请检查配置文件");
        }
        // 使用RSA加密密码，以便通过checkData验证
        try {
            String encryptedPassword = RSAEncrypt.publicKeyEncrypt(expertPassword, RSAEncrypt.PUBLIC_KEK);
            sysUserVo.setPassword(encryptedPassword);
        } catch (Exception e) {
            throw new ServiceException("密码加密失败");
        }
        SysRole roleByRoleCode = roleService.findRoleByRoleCode(superviseProperties.getRoleCode());
        sysUserVo.setType(superviseProperties.getUserTypeCode());
        sysUserVo.setTypeName(roleByRoleCode.getRoleName());
        sysUserVo.setRoleIds(Arrays.asList(roleByRoleCode.getId()));
        sysUserVo.setYthUserId(sysUserVo.getYthUserId());
        try {
            userByUsername = getUserByUsername(sysUserVo.getUsername(), superviseProperties.getUserTypeCode());
            if (userByUsername != null) {
                userRoleService.addRoles(userByUsername.getId(), Arrays.asList(roleByRoleCode.getId()));
                return userByUsername;
            }
        } catch (ServiceException e) {
        }
        String id = saveOrUpdate(sysUserVo);
        userByUsername = findById(id);
        return userByUsername;
    }

    /**
     * 创建企业用户
     *
     * @param sysUserVo 用户信息
     * @return 创建的用户
     */
    public SysUser createOrgUsers(SysUserVo sysUserVo) {
        // 判断用户是否存在
        SysUser userByUsername = null;
        // 检查企业配置密码是否为空
        String orgPassword = orgProperties.getPassword();
        if (StringUtils.isEmpty(orgPassword)) {
            throw new ServiceException("企业用户默认密码配置为空，请检查配置文件");
        }
        // 使用RSA加密密码，以便通过checkData验证
        try {
            String encryptedPassword = RSAEncrypt.publicKeyEncrypt(orgPassword, RSAEncrypt.PUBLIC_KEK);
            sysUserVo.setPassword(encryptedPassword);
        } catch (Exception e) {
            throw new ServiceException("密码加密失败");
        }
        SysRole roleByRoleCode = roleService.findRoleByRoleCode(orgProperties.getRoleCode());
        sysUserVo.setType(orgProperties.getUserTypeCode());
        sysUserVo.setTypeName(roleByRoleCode.getRoleName());
        sysUserVo.setRoleIds(Arrays.asList(roleByRoleCode.getId()));
        try {
            userByUsername = getUserByUsername(sysUserVo.getUsername(), orgProperties.getUserTypeCode());
            if (userByUsername != null) {
                userRoleService.addRoles(userByUsername.getId(), Arrays.asList(roleByRoleCode.getId()));
                return userByUsername;
            }
        } catch (ServiceException e) {
        }

        // 用户不存在，需要创建新用户，先处理企业机构信息
        if (!StringUtils.isEmpty(sysUserVo.getUscc()) && !StringUtils.isEmpty(sysUserVo.getNickname())) {
            // 先查询机构是否已存在（根据机构代码查询）
            Example example = new Example(BasicScienceOrg.class);
            example.createCriteria().andEqualTo("orgCode", sysUserVo.getUscc()).andEqualTo("yn", CommonConstant.FLAG_YES);
            BasicScienceOrg existingOrg = basicScienceOrgMapper.selectOneByExample(example);

            BasicScienceOrg basicScienceOrg;
            if (existingOrg != null) {
                // 机构已存在，使用已有机构
                basicScienceOrg = existingOrg;
                logger.info("企业机构已存在，机构名称：{}，机构代码：{}", existingOrg.getOrgName(), existingOrg.getOrgCode());
            } else {
                // 机构不存在，创建新机构
                basicScienceOrg = new BasicScienceOrg();
                basicScienceOrg.setId(UUIDUtils.getUUID());
                basicScienceOrg.setOrgName(sysUserVo.getNickname()); // 企业名称
                basicScienceOrg.setOrgCode(sysUserVo.getUscc()); // 统一社会信用代码
                basicScienceOrg.setYn(CommonConstant.FLAG_YES);

                // 插入机构信息
                basicScienceOrgMapper.insertSelective(basicScienceOrg);
                logger.info("成功创建企业机构信息，机构名称：{}，机构代码：{}", sysUserVo.getNickname(), sysUserVo.getUscc());
            }

            // 将机构ID绑定到用户表
            sysUserVo.setScienceOrgId(basicScienceOrg.getId());
            sysUserVo.setScienceOrgName(basicScienceOrg.getOrgName()); // 使用机构表中的名称
        }

        String id = saveOrUpdate(sysUserVo);
        userByUsername = findById(id);
        return userByUsername;
    }

    /**
     * 保存或更新系统用户表
     *
     * @param sysUser 用户对象
     * @return String 用户ID
     */
    @Override
    @Transactional(readOnly = false)
    public String saveOrUpdate(SysUserVo sysUser) {
        Assert.notNull(sysUser, "用户对象不能为空");
        // 新增操作
        if (StringUtils.isEmpty(sysUser.getId())) {
            // 数据校验
            checkData(sysUser);
            // 验证用户名和手机号的唯一性
//            validateUsernameAndPhone(sysUser.getUsername(), sysUser.getPhone());
            // 构造新增用户对象
            SysUser addUser = new SysUser();
            // 封装用户数据
            encapSysUser(sysUser, addUser);
            sysUserMapper.insertSelective(addUser);
        } else {
            // 更新操作
            SysUser updateUser = sysUserMapper.selectByPrimaryKey(sysUser.getId());
            Assert.notNull(updateUser, "用户不存在或已删除");
            // 封装用户数据
            encapSysUser(sysUser, updateUser);
            sysUserMapper.updateByPrimaryKeySelective(updateUser);
        }

        // 保存用户和角色关系
        saveUserRoleToUserinfo(sysUser);
        return sysUser.getId();
    }

//    @Override
//    public LoginUser getLoginUser(LoginBody loginBody) {
//        if (loginBody == null || org.apache.commons.lang3.StringUtils.isBlank(loginBody.getUsername()) ||
//                org.apache.commons.lang3.StringUtils.isBlank(loginBody.getPassword())) {
//            throw new ServiceException("账号或密码不能为空");
//        }
//        SysUser sysUser = this.getUserByUsername(loginBody.getUsername());
//        if (sysUser == null) {
//            throw new ServiceException("用户名或密码错误");
//        }
//        if (!bp.matches(loginBody.getPassword(), sysUser.getPassword())) {
//            throw new ServiceException("用户名或密码错误");
//        }
//        return userDetailsServiceImpl.getLoginUser(sysUser);
//    }

    @Override
    public void ythBinding(SysUserYthoauth sysUserYthoauth) {
        if (sysUserYthoauth == null || org.apache.commons.lang3.StringUtils.isBlank(sysUserYthoauth.getYthuserId()) ||
                org.apache.commons.lang3.StringUtils.isBlank(sysUserYthoauth.getUserId())) {
            throw new ServiceException("绑定数据为空");
        }
        SysUser sysUser = new SysUser();
        sysUser.setId(sysUserYthoauth.getUserId());
        sysUser.setYthUserId(sysUserYthoauth.getYthuserId());
        mapper.updateByPrimaryKeySelective(sysUser);
    }

    /**
     * 验证用户名和手机号是否唯一
     *
     * @param username 用户名
     * @param phone    手机号
     */
    private void validateUsernameAndPhone(String username, String phone) {
        int count = sysUserMapper.countUserByUsername(username);
        if (count > 0) {
            throw new ServiceException("登录名重复");
        }
        // 根据用户的联系电话 查询phone是否重复
        int countPhone = sysUserMapper.countUserByPhone(phone);
        if (countPhone > 0) {
            throw new ServiceException("手机号已存在");
        }
    }

    /**
     * 根据用户名获取用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    @Override
    public SysUser getUserByUsername(String username, String userTypeCode) {
        Assert.notNull(username, "用户名不能为空");
        Assert.notNull(userTypeCode, "用户类型不能为空");

        int count = 0;

        //个人用户
        if (expertProperties.getUserTypeCode().equals(userTypeCode)) {
            count = sysUserMapper.countUserByUsernameNoUserType(username); //个人用户 排除掉机构类型的账号
        } else if (orgProperties.getUserTypeCode().equals(userTypeCode)) { //法人机构用户
            count = sysUserMapper.countUserByUsernameUserType(username);//法人用户 查找 机构类型 的账号
        }

//        if (count == 0) {
//            throw new ServiceException("用户不存在");
//        }
        if (count > 1) {
            throw new ServiceException("用户名重复");
        }

        //个人用户
        if (expertProperties.getUserTypeCode().equals(userTypeCode)) {
            return sysUserMapper.getUserByUsernameAndNoUserType(username, userTypeCode);
        } else if (orgProperties.getUserTypeCode().equals(userTypeCode)) { //法人机构用户
            return sysUserMapper.getUserByUsernameAndUserType(username, userTypeCode);
        }

        return null;
    }

    /**
     * 保存用户角色关联关系
     *
     * @param sysUser 用户对象
     */
    private void saveUserRoleToUserinfo(SysUserVo sysUser) {
        List<String> roleIds = sysUser.getRoleIds();
        // 先删除所有用户和角色关联关系
        Example example = new Example(SysUserRole.class);
        example.createCriteria().andEqualTo("userId", sysUser.getId());
        sysUserRoleMapper.deleteByExample(example);
        // 新增用户和角色关联关系
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }
        for (String roleId : roleIds) {
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setId(UUIDUtils.getUUID());
            sysUserRole.setRoleId(roleId);
            sysUserRole.setUserId(sysUser.getId());
            sysUserRoleMapper.insert(sysUserRole);
        }
    }

    /**
     * 用户绑定机构
     */
    @Override
    @Transactional(readOnly = false)
    public void bindUserToOrg(BasicScienceOrgVo basicScienceOrgVo) {
        String userId = AppUserUtil.getCurrentUserId();
        String scienceOrgId = basicScienceOrgVo.getScienceOrgId();

        if (!StringUtils.hasText(scienceOrgId)) {
            throw new ServiceException("机构ID不能为空");
        }

        // 验证机构是否存在
        BasicScienceOrg org = basicScienceOrgMapper.selectByPrimaryKey(scienceOrgId);
        if (org == null || CommonConstant.FLAG_NO.equals(org.getYn())) {
            throw new ServiceException("机构不存在或已删除");
        }

        // 更新用户的机构信息
        SysUser updateUser = new SysUser();
        updateUser.setId(userId);
        updateUser.setScienceOrgId(scienceOrgId);
        updateUser.setScienceOrgName(org.getOrgName());
        updateUser.setUscc(org.getOrgCode());

        int result = sysUserMapper.updateByPrimaryKeySelective(updateUser);
        if (result <= 0) {
            throw new ServiceException("用户绑定机构失败");
        }

        logger.info("用户绑定机构成功，用户ID：{}，机构ID：{}，机构名称：{}", userId, scienceOrgId, org.getOrgName());
    }


    /**
     * 发送专家登录请求并解析用户信息
     *
     * @param token 专家登录token
     * @return 专家用户信息
     * @throws ServiceException 请求失败或解析失败时抛出异常
     */
    private ExportUserInfo fetchExpertUserInfo(String token) {
        try {
            logger.info("传入的token: {}", token);

            // 对token进行URL编码，避免特殊字符导致请求失败
            String encodedToken = URLEncoder.encode(token, "UTF-8");
            logger.info("URL编码后的token: {}", encodedToken);

            // 构建URI对象，避免RestTemplate自动编码
            String requestUrl = expertLoginUrl + "?sign=" + encodedToken;
            logger.info("专家单点登录请求URL: {}", requestUrl);

            URI uri = URI.create(requestUrl);
            logger.info("实际请求URI: {}", uri.toString());

            RestTemplate restTemplate;
            if (uri.getScheme().equalsIgnoreCase("https")) {
                restTemplate = sslRestTemplate;
            } else {
                restTemplate = new RestTemplate();
            }

            ResponseEntity<String> response = restTemplate.exchange(uri, HttpMethod.GET, null, String.class);

            if (response.getStatusCodeValue() != 200) {
                logger.error("专家单点登录失败，状态码：{}，消息：{}", response.getStatusCodeValue(), response.getBody());

                // 尝试解析错误响应中的具体错误信息
                String errorMessage = "专家登录失败";
                try {
                    JSONObject errorJson = JSONObject.parseObject(response.getBody());
                    String message = errorJson.getString("message");
                    if (message != null && !message.isEmpty()) {
                        errorMessage = message;
                    }
                } catch (Exception e) {
                    // 如果解析失败，使用原始响应体
                    errorMessage = "专家登录失败：" + response.getBody();
                }

                throw new ServiceException(errorMessage);
            }

            // 解析响应JSON
            JSONObject responseJson = JSONObject.parseObject(response.getBody());

            // 检查响应状态
            Integer status = responseJson.getInteger("status");
            if (status == null || status != 200) {
                String message = responseJson.getString("message");
                throw new ServiceException("专家登录失败: " + (message != null ? message : "未知错误"));
            }

            // 获取用户数据并转换为ExportUserInfo对象
            JSONObject userData = responseJson.getJSONObject("data");
            if (userData == null) {
                throw new ServiceException("专家登录失败: 用户数据为空");
            }

            ExportUserInfo exportUserInfo = userData.toJavaObject(ExportUserInfo.class);
            if (exportUserInfo == null) {
                throw new ServiceException("专家登录失败: 用户数据解析失败");
            }

            return exportUserInfo;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            logger.error("专家单点登录请求异常", e);
            throw new ServiceException("专家登录失败: " + e.getMessage());
        }
    }

    /**
     * @return LoginUser
     * @throws ServiceException
     * @description: 专家单点登录
     */
    @Override
    public LoginUser expertLogin(ExportSsoBody exportSsoBody) {
        if (exportSsoBody == null || StringUtils.isEmpty(exportSsoBody.getToken())) {
            throw new ServiceException("token不能为空");
        }

        String token = exportSsoBody.getToken();

        try {
            // 发送请求获取专家用户信息
            ExportUserInfo exportUserInfo = fetchExpertUserInfo(token);

            // 获取手机号
            String phone = exportUserInfo.getPhone();
            if (StringUtils.isEmpty(phone)) {
                throw new ServiceException("专家登录失败: 手机号为空");
            }

            String userName = exportUserInfo.getUsername();
            String nickName = exportUserInfo.getNickname();
            logger.info("专家单点登录获取到手机号: {}，userNmae：{}，nickName：{}", phone, userName, nickName);

            //查找专家用户是否存在
            SysUser user = sysUserMapper.getExportUserByPhone(phone);
            if (user == null){
                SysUserVo sysUserVo = new SysUserVo();
                sysUserVo.setPhone(phone);
                sysUserVo.setUsername(phone + "_EXPERT");
                sysUserVo.setNickname(nickName);
                user = createZhuanJiaUsers(sysUserVo);
            }
            return userDetailsServiceImpl.getLoginUser(user);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            logger.error("专家单点登录异常", e);
            throw new ServiceException("专家登录失败: " + e.getMessage());
        }
    }

    private SysUser createZhuanJiaUsers(SysUserVo sysUserVo) {
        // 判断用户是否存在
        SysUser userByUsername = null;
        // 检查专家配置密码是否为空
        String expertPassword = zhuanjiaProperties.getPassword();
        if (StringUtils.isEmpty(expertPassword)) {
            throw new ServiceException("用户默认密码配置为空，请检查配置文件");
        }
        // 使用RSA加密密码，以便通过checkData验证
        try {
            String encryptedPassword = RSAEncrypt.publicKeyEncrypt(expertPassword, RSAEncrypt.PUBLIC_KEK);
            sysUserVo.setPassword(encryptedPassword);
        } catch (Exception e) {
            throw new ServiceException("密码加密失败");
        }
        SysRole roleByRoleCode = roleService.findRoleByRoleCode(zhuanjiaProperties.getRoleCode());
        sysUserVo.setType(zhuanjiaProperties.getUserTypeCode());
        sysUserVo.setTypeName(roleByRoleCode.getRoleName());
        sysUserVo.setRoleIds(Arrays.asList(roleByRoleCode.getId()));
        try {
            userByUsername = getUserByUsername(sysUserVo.getUsername(), zhuanjiaProperties.getUserTypeCode());
            if (userByUsername != null) {
                userRoleService.addRoles(userByUsername.getId(), Arrays.asList(roleByRoleCode.getId()));
                return userByUsername;
            }
        } catch (ServiceException e) {
        }
        String id = saveOrUpdate(sysUserVo);
        userByUsername = findById(id);
        return userByUsername;
    }

}
