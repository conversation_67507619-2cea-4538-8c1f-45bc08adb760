package com.fd.stdp.service.tech;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.tech.TechAwardsAchievement;
import com.fd.stdp.beans.tech.vo.TechAwardsAchievementVo;
/**
 *@Description: 奖项成果关联表
 *@Author: wangsh
 *@Date: 2022-01-04 14:00:43
 */
public interface TechAwardsAchievementService {

	/**
	 *@Description: 保存或更新奖项成果关联表
	 *@param techAwardsAchievement 奖项成果关联表对象
	 *@return String 奖项成果关联表ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTechAwardsAchievement(TechAwardsAchievement techAwardsAchievement);
	
	/**
	 *@Description: 删除奖项成果关联表
	 *@param id void 奖项成果关联表ID
	 *@Author: wangsh
	 */
	void deleteTechAwardsAchievement(String id);

	/**
	 *@Description: 查询奖项成果关联表详情
	 *@param id
	 *@return TechAwardsAchievement
	 *@Author: wangsh
	 */
	TechAwardsAchievement findById(String id);

	/**
	 *@Description: 分页查询奖项成果关联表
	 *@param techAwardsAchievementVo
	 *@return PageInfo<TechAwardsAchievement>
	 *@Author: wangsh
	 */
	PageInfo<TechAwardsAchievement> findPageByQuery(TechAwardsAchievementVo techAwardsAchievementVo);

	void clearByAwardId(String awardId);
}
