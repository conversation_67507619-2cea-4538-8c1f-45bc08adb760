package com.fd.stdp.service.innovation;

import java.util.List;

import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryApplyCommentVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationLaboratoryAccept;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryAcceptVo;
/**
 *@Description: 重点实验室培育申报任务书验收
 *@Author: wangsh
 *@Date: 2022-03-09 15:53:01
 */
public interface InnovationLaboratoryAcceptService {

	/**
	 *@Description: 保存或更新重点实验室培育申报任务书验收
	 *@param innovationLaboratoryAccept 重点实验室培育申报任务书验收对象
	 *@return String 重点实验室培育申报任务书验收ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationLaboratoryAccept(InnovationLaboratoryAcceptVo innovationLaboratoryAccept);
	
	/**
	 *@Description: 删除重点实验室培育申报任务书验收
	 *@param id void 重点实验室培育申报任务书验收ID
	 *@Author: wangsh
	 */
	void deleteInnovationLaboratoryAccept(String id);

	/**
	 * @Description: 批量删除重点实验室培育申报任务书验收
	 * @param ids
	 */
    void deleteMultiInnovationLaboratoryAccept(List<String> ids);

	/**
	 *@Description: 查询重点实验室培育申报任务书验收详情
	 *@param id
	 *@return InnovationLaboratoryAccept
	 *@Author: wangsh
	 */
	InnovationLaboratoryAccept findById(String id);

	/**
	 *@Description: 分页查询重点实验室培育申报任务书验收
	 *@param innovationLaboratoryAcceptVo
	 *@return PageInfo<InnovationLaboratoryAccept>
	 *@Author: wangsh
	 */
	PageInfo<InnovationLaboratoryAccept> findPageByQuery(InnovationLaboratoryAcceptVo innovationLaboratoryAcceptVo);
	
	
	/**
	 * 提交
	 * @param innovationLaboratoryAcceptVo
	 * @return
	 */
    String submitInnovationLaboratoryAccept(InnovationLaboratoryAcceptVo innovationLaboratoryAcceptVo);

	/**
	 * 审核
	 * @param innovationLaboratoryAcceptVo
	 * @return
	 */
	String auditInnovationLaboratoryAccept(InnovationLaboratoryAcceptVo innovationLaboratoryAcceptVo);

	/**
	 * 退回
	 * @param innovationLaboratoryAcceptVo
	 * @return
	 */
	String sendBackInnovationLaboratoryAccept(InnovationLaboratoryAcceptVo innovationLaboratoryAcceptVo);

	/**
	 * 任务书下达
	 * @param innovationLaboratoryAcceptVo
	 * @return
	 */
	String releaseInnovationLaboratoryAccept(InnovationLaboratoryAcceptVo innovationLaboratoryAcceptVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnovationLaboratoryAccept> todoList(InnovationLaboratoryAcceptVo innovationLaboratoryAcceptVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationLaboratoryAccept> finishedList(InnovationLaboratoryAcceptVo innovationLaboratoryAcceptVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationLaboratoryAccept> endList(InnovationLaboratoryAcceptVo innovationLaboratoryAcceptVo);

	/**
	 * 专家提交
	 * @param innovationLaboratoryAcceptVo
	 * @return
	 */
    String expertSubmitinnovationLaboratoryAccept(InnovationLaboratoryApplyCommentVo vo);
}
