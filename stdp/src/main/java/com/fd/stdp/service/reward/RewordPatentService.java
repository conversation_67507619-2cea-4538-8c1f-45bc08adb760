package com.fd.stdp.service.reward;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.reward.RewordPatent;
import com.fd.stdp.beans.reward.vo.RewordPatentVo;
/**
 *@Description: 科技成果关联专利
 *@Author: wangsh
 *@Date: 2022-07-05 16:58:49
 */
public interface RewordPatentService {

	/**
	 *@Description: 保存或更新科技成果关联专利
	 *@param rewordPatent 科技成果关联专利对象
	 *@return String 科技成果关联专利ID
	 *@Author: wangsh
	 */
	String saveOrUpdateRewordPatent(RewordPatentVo rewordPatent);
	
	/**
	 *@Description: 删除科技成果关联专利
	 *@param id void 科技成果关联专利ID
	 *@Author: wangsh
	 */
	void deleteRewordPatent(String id);

	/**
	 * @Description: 批量删除科技成果关联专利
	 * @param ids
	 */
    void deleteMultiRewordPatent(List<String> ids);

	/**
	 *@Description: 查询科技成果关联专利详情
	 *@param id
	 *@return RewordPatent
	 *@Author: wangsh
	 */
	RewordPatent findById(String id);

	/**
	 *@Description: 分页查询科技成果关联专利
	 *@param rewordPatentVo
	 *@return PageInfo<RewordPatent>
	 *@Author: wangsh
	 */
	PageInfo<RewordPatent> findPageByQuery(RewordPatentVo rewordPatentVo);
	
	
	/**
	 * 提交
	 * @param rewordPatentVo
	 * @return
	 */
    String submitRewordPatent(RewordPatentVo rewordPatentVo);

	/**
	 * 审核
	 * @param rewordPatentVo
	 * @return
	 */
	String auditRewordPatent(RewordPatentVo rewordPatentVo);

	/**
	 * 退回
	 * @param rewordPatentVo
	 * @return
	 */
	String sendBackRewordPatent(RewordPatentVo rewordPatentVo);

	/**
	 * 任务书下达
	 * @param rewordPatentVo
	 * @return
	 */
	String releaseRewordPatent(RewordPatentVo rewordPatentVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<RewordPatent> todoList(RewordPatentVo rewordPatentVo);

	/**
	 * 已办列表
	 */
	PageInfo<RewordPatent> finishedList(RewordPatentVo rewordPatentVo);

	/**
	 * 已完成列表
	 */
	PageInfo<RewordPatent> endList(RewordPatentVo rewordPatentVo);
}
