package com.fd.stdp.service.sys.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.sys.SysUserFunctionRole;
import com.fd.stdp.beans.sys.vo.SysUserFunctionRoleVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.sys.SysUserFunctionRoleMapper;
import com.fd.stdp.service.sys.SysUserFunctionRoleService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 用户角色关联
 *@Author: qyj
 *@Date: 2020-07-12 15:58:19
 */
public class SysUserFunctionRoleServiceImpl extends BaseServiceImpl<SysUserFunctionRoleMapper, SysUserFunctionRole> implements SysUserFunctionRoleService{

	public static final Logger logger = LoggerFactory.getLogger(SysUserFunctionRoleServiceImpl.class);
	
	@Autowired
	private SysUserFunctionRoleMapper sysUserFunctionRoleMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新用户角色关联
	 *@param sysUserFunctionRole 用户角色关联对象
	 *@return String 用户角色关联ID
	 *@Author: qyj
	 */
	public String saveOrUpdateSysUserFunctionRole(SysUserFunctionRole sysUserFunctionRole) {
		if(sysUserFunctionRole==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(sysUserFunctionRole.getId())){
			//新增
			sysUserFunctionRole.setId(UUIDUtils.getUUID());
			sysUserFunctionRoleMapper.insertSelective(sysUserFunctionRole);
		}else{
			//避免页面传入修改
			sysUserFunctionRole.setYn(null);
			sysUserFunctionRoleMapper.updateByPrimaryKeySelective(sysUserFunctionRole);
		}
		return sysUserFunctionRole.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除用户角色关联
	 *@param id void 用户角色关联ID
	 *@Author: qyj
	 */
	public void deleteSysUserFunctionRole(String id) {
		//TODO 做判断后方能执行删除
		SysUserFunctionRole sysUserFunctionRole=sysUserFunctionRoleMapper.selectByPrimaryKey(id);
		if(sysUserFunctionRole==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		SysUserFunctionRole temsysUserFunctionRole=new SysUserFunctionRole();
		temsysUserFunctionRole.setYn(CommonConstant.FLAG_NO);
		temsysUserFunctionRole.setId(sysUserFunctionRole.getId());
		sysUserFunctionRoleMapper.updateByPrimaryKeySelective(temsysUserFunctionRole);
	}

	@Override
	/**
	 *@Description: 查询用户角色关联详情
	 *@param id
	 *@return SysUserFunctionRole
	 *@Author: qyj
	 */
	public SysUserFunctionRole findById(String id) {
		return sysUserFunctionRoleMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询用户角色关联
	 *@param sysUserFunctionRoleVo
	 *@return PageInfo<SysUserFunctionRole>
	 *@Author: qyj
	 */
	public PageInfo<SysUserFunctionRole> findPageByQuery(SysUserFunctionRoleVo sysUserFunctionRoleVo) {
		PageHelper.startPage(sysUserFunctionRoleVo.getPageNum(),sysUserFunctionRoleVo.getPageSize());
		Example example=new Example(SysUserFunctionRole.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(sysUserFunctionRoleVo.getName())){
		//	criteria.andEqualTo(sysUserFunctionRoleVo.getName());
		//}
		List<SysUserFunctionRole> sysUserFunctionRoleList=sysUserFunctionRoleMapper.selectByExample(example);
		return new PageInfo<SysUserFunctionRole>(sysUserFunctionRoleList);
	}

}
