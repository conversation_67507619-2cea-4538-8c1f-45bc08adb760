package com.fd.stdp.service.innovation.impl;

import java.util.List;

import com.fd.stdp.beans.innovation.vo.InnovationNationalCenterApplyVo;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationOtherInnovationApply;
import com.fd.stdp.beans.innovation.vo.InnovationOtherInnovationApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationOtherInnovationApplyMapper;
import com.fd.stdp.service.innovation.InnovationOtherInnovationApplyService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 其他载体
 *@Author: wangsh
 *@Date: 2022-03-07 19:46:34
 */
public class InnovationOtherInnovationApplyServiceImpl extends BaseServiceImpl<InnovationOtherInnovationApplyMapper, InnovationOtherInnovationApply> implements InnovationOtherInnovationApplyService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationOtherInnovationApplyServiceImpl.class);
	
	@Autowired
	private InnovationOtherInnovationApplyMapper innovationOtherInnovationApplyMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新其他载体
	 *@param innovationOtherInnovationApply 其他载体对象
	 *@return String 其他载体ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationOtherInnovationApply(InnovationOtherInnovationApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationOtherInnovationApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationOtherInnovationApplyMapper.updateByPrimaryKeySelective(vo);
		}
		/**
		 * 附件
		 */
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除其他载体
	 *@param id void 其他载体ID
	 *@Author: wangsh
	 */
	public void deleteInnovationOtherInnovationApply(String id) {
		//TODO 做判断后方能执行删除
		InnovationOtherInnovationApply innovationOtherInnovationApply=innovationOtherInnovationApplyMapper.selectByPrimaryKey(id);
		if(innovationOtherInnovationApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationOtherInnovationApply teminnovationOtherInnovationApply=new InnovationOtherInnovationApply();
		teminnovationOtherInnovationApply.setYn(CommonConstant.FLAG_NO);
		teminnovationOtherInnovationApply.setId(innovationOtherInnovationApply.getId());
		innovationOtherInnovationApplyMapper.updateByPrimaryKeySelective(teminnovationOtherInnovationApply);
	}

    /**
     * @Description: 批量删除其他载体
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationOtherInnovationApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationOtherInnovationApply(id));
	}

	@Override
	/**
	 *@Description: 查询其他载体详情
	 *@param id
	 *@return InnovationOtherInnovationApply
	 *@Author: wangsh
	 */
	public InnovationOtherInnovationApply findById(String id) {
		InnovationOtherInnovationApply apply = innovationOtherInnovationApplyMapper.selectByPrimaryKey(id);
		InnovationOtherInnovationApplyVo vo = new InnovationOtherInnovationApplyVo();
		BeanUtils.copyProperties(apply, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询其他载体
	 *@param innovationOtherInnovationApplyVo
	 *@return PageInfo<InnovationOtherInnovationApply>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationOtherInnovationApply> findPageByQuery(InnovationOtherInnovationApplyVo innovationOtherInnovationApplyVo) {
		PageHelper.startPage(innovationOtherInnovationApplyVo.getPageNum(),innovationOtherInnovationApplyVo.getPageSize());
		Example example=new Example(InnovationOtherInnovationApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationOtherInnovationApplyVo.getName())){
		//	criteria.andEqualTo(innovationOtherInnovationApplyVo.getName());
		//}
		List<InnovationOtherInnovationApply> innovationOtherInnovationApplyList=innovationOtherInnovationApplyMapper.selectByExample(example);
		return new PageInfo<InnovationOtherInnovationApply>(innovationOtherInnovationApplyList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationOtherInnovationApply(InnovationOtherInnovationApplyVo innovationOtherInnovationApplyVo) {
		String id = this.saveOrUpdateInnovationOtherInnovationApply(innovationOtherInnovationApplyVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innovationOtherInnovationApplyVo, this.mapper,
				StringUtils.isNotBlank(innovationOtherInnovationApplyVo.getAuditAdvice())?innovationOtherInnovationApplyVo.getAuditAdvice():"提交其他载体");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationOtherInnovationApply(InnovationOtherInnovationApplyVo innovationOtherInnovationApplyVo) {
		flowCommonService.doFlowStepAudit(innovationOtherInnovationApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationOtherInnovationApplyVo.getAuditAdvice()) ? innovationOtherInnovationApplyVo.getAuditAdvice() : "其他载体审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationOtherInnovationApplyVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationOtherInnovationApply(InnovationOtherInnovationApplyVo innovationOtherInnovationApplyVo) {
		flowCommonService.doFlowStepSendBack(innovationOtherInnovationApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationOtherInnovationApplyVo.getAuditAdvice())?innovationOtherInnovationApplyVo.getAuditAdvice():"其他载体退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationOtherInnovationApply(InnovationOtherInnovationApplyVo innovationOtherInnovationApplyVo) {
		flowCommonService.doCompleteTask(innovationOtherInnovationApplyVo, this.mapper
				, "其他载体任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnovationOtherInnovationApply> todoList(InnovationOtherInnovationApplyVo vo) {

		Example example = new Example(InnovationOtherInnovationApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationOtherInnovationApply> finishedList(InnovationOtherInnovationApplyVo vo) {
		Example example = new Example(InnovationOtherInnovationApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnovationOtherInnovationApply> endList(InnovationOtherInnovationApplyVo vo) {
        Example example = new Example(InnovationOtherInnovationApply.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(InnovationOtherInnovationApply vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件

		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getCenterName())){
			criteria.andLike("centerName", "%" + vo.getCenterName()+ "%");
		}
		return criteria;
	}
}
