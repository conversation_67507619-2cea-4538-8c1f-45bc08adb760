package com.fd.stdp.service.project;

import java.util.List;

import com.fd.stdp.beans.project.ProjectDemandCollectionGuid;
import com.fd.stdp.common.RestApiResponse;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectDemandCollectionApply;
import com.fd.stdp.beans.project.vo.ProjectDemandCollectionApplyVo;

/**
 * 需求征集申请Service接口
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
public interface ProjectDemandCollectionApplyService {
    /**
     * @param projectDemandCollectionApplyVo 需求征集申请对象
     * @return String 需求征集申请ID
     * @Description: 保存或更新需求征集申请
     * @Author: yujianfei
     */
    String saveOrUpdateProjectDemandCollectionApply(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo);

    /**
     * @param ids void 需求征集申请ID
     * @Description: 删除需求征集申请
     * @Author: yujianfei
     */
    void deleteProjectDemandCollectionApply(List<String> ids);

    /**
     * @param id
     * @return ProjectDemandCollectionApply
     * @Description: 查询需求征集申请详情
     * @Author: yujianfei
     */
    ProjectDemandCollectionApply findById(String id);

    /**
     * @param projectDemandCollectionApplyVo
     * @return PageInfo<ProjectDemandCollectionApply>
     * @Description: 分页查询需求征集申请
     * @Author: yujianfei
     */
    PageInfo<ProjectDemandCollectionApply> findPageByQuery(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo);


    /**
     * @param projectDemandCollectionApplyVo 需求征集数据 json
     * @return RestApiResponse<?>
     * @Description: 审核
     * @Author: yujianfei
     */
    void audit(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo);

    /**
     * @param projectDemandCollectionApplyVo 需求征集数据 json
     * @return RestApiResponse<?>
     * @Description: 整合
     * @Author: yujianfei
     */
    void intergrate(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo);

    /**
     * @param projectDemandCollectionApplyVo 需求征集数据 json
     * @return RestApiResponse<?>
     * @Description: 退回
     * @Author: yujianfei
     */
    void sendBack(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo);

    /**
     * 代办列表
     * @param currentUserId
     * @return
     */
    PageInfo todoList(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo, String currentUserId);
//    PageInfo<ProjectDemandCollectionApply> todoList(ProjectDemandCollectionApplyVo vo);


    /**
     * 已办列表
     * @param projectDemandCollectionApplyVo
     * @param currentUserId
     * @return
     */
    PageInfo finishedList(ProjectDemandCollectionApplyVo projectDemandCollectionApplyVo, String currentUserId);

    ProjectDemandCollectionGuid intergrateData(List<String> ids);
}
