package com.fd.stdp.service.project.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectApplyCooperationUnit;
import com.fd.stdp.beans.project.vo.ProjectApplyCooperationUnitVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectApplyCooperationUnitMapper;
import com.fd.stdp.service.project.ProjectApplyCooperationUnitService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 项目合作单位Service业务层处理
 * @date 2021-11-26
 */
@Service
@Transactional(readOnly = true)
public class ProjectApplyCooperationUnitServiceImpl extends BaseServiceImpl<ProjectApplyCooperationUnitMapper, ProjectApplyCooperationUnit> implements ProjectApplyCooperationUnitService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectApplyCooperationUnitServiceImpl.class);
    @Autowired
    private ProjectApplyCooperationUnitMapper projectApplyCooperationUnitMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新项目合作单位
     *@param projectApplyCooperationUnit 项目合作单位对象
     *@return String 项目合作单位ID
     *@Author: yujianfei
     */
    public String saveOrUpdateProjectApplyCooperationUnit(ProjectApplyCooperationUnit projectApplyCooperationUnit) {
        if (projectApplyCooperationUnit == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(projectApplyCooperationUnit.getId())) {
            //新增
            projectApplyCooperationUnit.setId(UUIDUtils.getUUID());
            projectApplyCooperationUnitMapper.insertSelective(projectApplyCooperationUnit);
        } else {
            //避免页面传入修改
            projectApplyCooperationUnit.setYn(null);
            projectApplyCooperationUnitMapper.updateByPrimaryKeySelective(projectApplyCooperationUnit);
        }
        return projectApplyCooperationUnit.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除项目合作单位
     *@param id void 项目合作单位ID
     *@Author: yujianfei
     */
    public void deleteProjectApplyCooperationUnit(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            ProjectApplyCooperationUnit projectApplyCooperationUnit = projectApplyCooperationUnitMapper.selectByPrimaryKey(id);
            if (projectApplyCooperationUnit == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            ProjectApplyCooperationUnit temprojectApplyCooperationUnit = new ProjectApplyCooperationUnit();
            temprojectApplyCooperationUnit.setYn(CommonConstant.FLAG_NO);
            temprojectApplyCooperationUnit.setId(projectApplyCooperationUnit.getId());
            projectApplyCooperationUnitMapper.updateByPrimaryKeySelective(temprojectApplyCooperationUnit);
        }
    }

    /**
     * @param id
     * @return ProjectApplyCooperationUnit
     * @Description: 查询项目合作单位详情
     * @Author: yujianfei
     */
    @Override
    public ProjectApplyCooperationUnit findById(String id) {
        return projectApplyCooperationUnitMapper.selectByPrimaryKey(id);
    }


    /**
     * @param projectApplyCooperationUnitVo
     * @return PageInfo<ProjectApplyCooperationUnit>
     * @Description: 分页查询项目合作单位
     * @Author: yujianfei
     */
    @Override
    public PageInfo<ProjectApplyCooperationUnit> findPageByQuery(ProjectApplyCooperationUnitVo projectApplyCooperationUnitVo) {
        PageHelper.startPage(projectApplyCooperationUnitVo.getPageNum(), projectApplyCooperationUnitVo.getPageSize());
        Example example = new Example(ProjectApplyCooperationUnit.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(projectApplyCooperationUnitVo.getName())){
        //	criteria.andEqualTo(projectApplyCooperationUnitVo.getName());
        //}
        List<ProjectApplyCooperationUnit> projectApplyCooperationUnitList = projectApplyCooperationUnitMapper.selectByExample(example);
        return new PageInfo<ProjectApplyCooperationUnit>(projectApplyCooperationUnitList);
    }
}
