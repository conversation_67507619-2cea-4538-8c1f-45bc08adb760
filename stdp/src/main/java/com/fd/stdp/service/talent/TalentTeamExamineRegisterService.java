package com.fd.stdp.service.talent;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamExamineRegister;
import com.fd.stdp.beans.talent.vo.TalentTeamExamineRegisterVo;
/**
 *@Description: 创新团队考核登记
 *@Author: wangsh
 *@Date: 2022-02-14 10:22:40
 */
public interface TalentTeamExamineRegisterService {

	/**
	 *@Description: 保存或更新创新团队考核登记
	 *@param talentTeamExamineRegister 创新团队考核登记对象
	 *@return String 创新团队考核登记ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTalentTeamExamineRegister(TalentTeamExamineRegisterVo talentTeamExamineRegister);
	
	/**
	 *@Description: 删除创新团队考核登记
	 *@param id void 创新团队考核登记ID
	 *@Author: wangsh
	 */
	void deleteTalentTeamExamineRegister(String id);

	/**
	 * @Description: 批量删除创新团队考核登记
	 * @param ids
	 */
    void deleteMultiTalentTeamExamineRegister(List<String> ids);

	/**
	 *@Description: 查询创新团队考核登记详情
	 *@param id
	 *@return TalentTeamExamineRegister
	 *@Author: wangsh
	 */
	TalentTeamExamineRegister findById(String id);

	/**
	 *@Description: 分页查询创新团队考核登记
	 *@param talentTeamExamineRegisterVo
	 *@return PageInfo<TalentTeamExamineRegister>
	 *@Author: wangsh
	 */
	PageInfo<TalentTeamExamineRegister> findPageByQuery(TalentTeamExamineRegisterVo talentTeamExamineRegisterVo);
	
	
	/**
	 * 提交
	 * @param talentTeamExamineRegisterVo
	 * @return
	 */
    String submitTalentTeamExamineRegister(TalentTeamExamineRegisterVo talentTeamExamineRegisterVo);

	/**
	 * 审核
	 * @param talentTeamExamineRegisterVo
	 * @return
	 */
	String auditTalentTeamExamineRegister(TalentTeamExamineRegisterVo talentTeamExamineRegisterVo);

	/**
	 * 退回
	 * @param talentTeamExamineRegisterVo
	 * @return
	 */
	String sendBackTalentTeamExamineRegister(TalentTeamExamineRegisterVo talentTeamExamineRegisterVo);

	/**
	 * 任务书下达
	 * @param talentTeamExamineRegisterVo
	 * @return
	 */
	String releaseTalentTeamExamineRegister(TalentTeamExamineRegisterVo talentTeamExamineRegisterVo);
	
	/**
	 * 待办列表
	*/
    PageInfo<TalentTeamExamineRegister> todoList(TalentTeamExamineRegisterVo talentTeamExamineRegisterVo);

	/**
	 * 已办列表
	*/
	PageInfo<TalentTeamExamineRegister> finishedList(TalentTeamExamineRegisterVo talentTeamExamineRegisterVo);
}
