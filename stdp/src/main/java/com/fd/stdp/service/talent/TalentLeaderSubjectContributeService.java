package com.fd.stdp.service.talent;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectContribute;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectContributeVo;
/**
 *@Description: 学科带头人建设任务书
 *@Author: wangsh
 *@Date: 2022-02-14 10:05:19
 */
public interface TalentLeaderSubjectContributeService {

	/**
	 *@Description: 保存或更新学科带头人建设任务书
	 *@param talentLeaderSubjectContribute 学科带头人建设任务书对象
	 *@return String 学科带头人建设任务书ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTalentLeaderSubjectContribute(TalentLeaderSubjectContributeVo talentLeaderSubjectContribute);
	
	/**
	 *@Description: 删除学科带头人建设任务书
	 *@param id void 学科带头人建设任务书ID
	 *@Author: wangsh
	 */
	void deleteTalentLeaderSubjectContribute(String id);

	/**
	 * @Description: 批量删除学科带头人建设任务书
	 * @param ids
	 */
    void deleteMultiTalentLeaderSubjectContribute(List<String> ids);

	/**
	 *@Description: 查询学科带头人建设任务书详情
	 *@param id
	 *@return TalentLeaderSubjectContribute
	 *@Author: wangsh
	 */
	TalentLeaderSubjectContribute findById(String id);

	/**
	 *@Description: 分页查询学科带头人建设任务书
	 *@param talentLeaderSubjectContributeVo
	 *@return PageInfo<TalentLeaderSubjectContribute>
	 *@Author: wangsh
	 */
	PageInfo<TalentLeaderSubjectContribute> findPageByQuery(TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo);
	
	
	/**
	 * 提交
	 * @param talentLeaderSubjectContributeVo
	 * @return
	 */
    String submitTalentLeaderSubjectContribute(TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo);

	/**
	 * 审核
	 * @param talentLeaderSubjectContributeVo
	 * @return
	 */
	String auditTalentLeaderSubjectContribute(TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo);

	/**
	 * 退回
	 * @param talentLeaderSubjectContributeVo
	 * @return
	 */
	String sendBackTalentLeaderSubjectContribute(TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo);

	/**
	 * 任务书下达
	 * @param talentLeaderSubjectContributeVo
	 * @return
	 */
	String releaseTalentLeaderSubjectContribute(TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo);
	
	/**
	 * 待办列表
	*/
    PageInfo<TalentLeaderSubjectContribute> todoList(TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo);

	/**
	 * 已办列表
	*/
	PageInfo<TalentLeaderSubjectContribute> finishedList(TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo);
}
