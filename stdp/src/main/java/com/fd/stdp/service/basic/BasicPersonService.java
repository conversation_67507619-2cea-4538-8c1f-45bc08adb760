package com.fd.stdp.service.basic;

import java.util.List;

import com.fd.stdp.beans.basic.BasicPersonReport;
import com.fd.stdp.beans.basic.vo.BasicPersonReportVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPerson;
import com.fd.stdp.beans.basic.vo.BasicPersonVo;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 *@Description: 机构人员库
 *@Author: wangsh
 *@Date: 2022-01-07 10:53:24
 */
public interface BasicPersonService {

	/**
	 *@Description: 保存或更新机构人员库
	 *@param basicPerson 机构人员库对象
	 *@return String 机构人员库ID
	 *@Author: wangsh
	 */
	String saveOrUpdateBasicPerson(BasicPersonVo basicPerson);
	String saveOrUpdateBasicPerson(BasicPersonVo basicPerson, boolean isStartFlow);
	
	/**
	 *@Description: 删除机构人员库
	 *@param id void 机构人员库ID
	 *@Author: wangsh
	 */
	void deleteBasicPerson(String id);

	/**
	 *@Description: 查询机构人员库详情
	 *@param id
	 *@return BasicPerson
	 *@Author: wangsh
	 */
	BasicPerson findById(String id);

	/**
	 *@Description: 分页查询机构人员库
	 *@param basicPersonVo
	 *@return PageInfo<BasicPerson>
	 *@Author: wangsh
	 */
	PageInfo<BasicPerson> findPageByQuery(BasicPersonVo basicPersonVo);

	/**
	 * 全库查询人员信息（关键信息脱敏）
	 * @param basicPersonVo
	 * @return
	 */
	PageInfo<BasicPerson> findPageByQueryGlobal(BasicPersonVo basicPersonVo);

	/**
	 * 批量导入
	 * @param file
	 * @return
	 */
	String upload(MultipartFile file);

	/**
	 * 提交
	 * @param vo
	 * @return
	 */
	String submitBasicPerson(BasicPersonVo vo);

	/**
	 * 审核
	 * @param vo
	 * @return
	 */
	String auditBasicPerson(BasicPersonVo vo);

	/**
	 * 批量退回
	 * @param vo
	 * @return
	 */
	String sendBackBasicPerson(BasicPersonVo vo);

	/**
	 * 批量删除
	 * @param ids
	 */
    void deleteMultiBasicPerson(@RequestBody List<String> ids);

	/**
	 * 统计
	 * @param basicPersonVo
	 * @return
	 */
	Object statistics(BasicPersonVo basicPersonVo);

	/**
	 * 身份证号查询
	 * @param id
	 * @return
	 */
	List<BasicPerson> findByIdentityId(String id);

	/**
	 * 导出
	 * @param basicPersonVo
	 * @return
	 */
	Object export(BasicPersonVo basicPersonVo, HttpServletResponse response);

	PageInfo<BasicPerson> todoList(BasicPersonVo basicPersonVo);

	PageInfo<BasicPerson> finishedList(BasicPersonVo basicPersonVo);

	/**
	 * 提交备案
	 */
	void record();

	PageInfo<BasicPersonReport> findRecordPageByQuery(BasicPersonReportVo vo);

	BasicPersonReport findByRecordId(String id);

    void uploadSystem(MultipartFile file);

	PageInfo<BasicPerson> findPageByQueryMain(BasicPersonVo basicPersonVo);
}
