package com.fd.stdp.service.basic.impl;

import java.util.ArrayList;
import java.util.List;

import com.fd.stdp.beans.basic.BasicFileAppendix;
import com.fd.stdp.beans.basic.vo.BasicFileAppendixVo;
import com.fd.stdp.beans.sys.SysFileInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonLinked;
import com.fd.stdp.beans.basic.vo.BasicPersonLinkedVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicPersonLinkedMapper;
import com.fd.stdp.service.basic.BasicPersonLinkedService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 人员关联表
 *@Author: wangsh
 *@Date: 2022-01-25 15:48:05
 */
public class BasicPersonLinkedServiceImpl extends BaseServiceImpl<BasicPersonLinkedMapper, BasicPersonLinked> implements BasicPersonLinkedService{

	public static final Logger logger = LoggerFactory.getLogger(BasicPersonLinkedServiceImpl.class);

	@Autowired
	private BasicPersonLinkedMapper basicPersonLinkedMapper;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新人员关联表
	 *@param basicPersonLinked 人员关联表对象
	 *@return String 人员关联表ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateBasicPersonLinked(BasicPersonLinkedVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			basicPersonLinkedMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			basicPersonLinkedMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除人员关联表
	 *@param id void 人员关联表ID
	 *@Author: wangsh
	 */
	public void deleteBasicPersonLinked(String id) {
		//TODO 做判断后方能执行删除
		BasicPersonLinked basicPersonLinked=basicPersonLinkedMapper.selectByPrimaryKey(id);
		if(basicPersonLinked==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		BasicPersonLinked tembasicPersonLinked=new BasicPersonLinked();
		tembasicPersonLinked.setYn(CommonConstant.FLAG_NO);
		tembasicPersonLinked.setId(basicPersonLinked.getId());
		basicPersonLinkedMapper.updateByPrimaryKeySelective(tembasicPersonLinked);
	}

    /**
     * @Description: 批量删除人员关联表
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiBasicPersonLinked(List<String> ids) {
		ids.stream().forEach(id-> this.deleteBasicPersonLinked(id));
	}

	@Override
	/**
	 *@Description: 查询人员关联表详情
	 *@param id
	 *@return BasicPersonLinked
	 *@Author: wangsh
	 */
	public BasicPersonLinked findById(String id) {
		return basicPersonLinkedMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询人员关联表
	 *@param basicPersonLinkedVo
	 *@return PageInfo<BasicPersonLinked>
	 *@Author: wangsh
	 */
	public PageInfo<BasicPersonLinked> findPageByQuery(BasicPersonLinkedVo basicPersonLinkedVo) {
		PageHelper.startPage(basicPersonLinkedVo.getPageNum(),basicPersonLinkedVo.getPageSize());
		Example example=new Example(BasicPersonLinked.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(basicPersonLinkedVo.getName())){
		//	criteria.andEqualTo(basicPersonLinkedVo.getName());
		//}
		List<BasicPersonLinked> basicPersonLinkedList=basicPersonLinkedMapper.selectByExample(example);
		return new PageInfo<BasicPersonLinked>(basicPersonLinkedList);
	}

	@Override
	public List<BasicPersonLinkedVo> findByFormId(String formId) {
		Example example = new Example(BasicPersonLinked.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("formId", formId);
		example.orderBy("sort");
		List<BasicPersonLinked> basicPersonLinkeds = basicPersonLinkedMapper.selectByExample(example);
		List<BasicPersonLinkedVo> basicPersonLinkedVos = new ArrayList<>();
		for (BasicPersonLinked b:basicPersonLinkeds) {
			BasicPersonLinkedVo linkedVo = new BasicPersonLinkedVo();
			BeanUtils.copyProperties(b, linkedVo);
			basicPersonLinkedVos.add(linkedVo);
		}
		return basicPersonLinkedVos;
	}

	@Override
	public void clearAndUpdateBasicPersonLinked(String formId, List<BasicPersonLinkedVo> BasicPersonLinkedVoList) {
		if(StringUtils.isBlank(formId)){
			throw new ServiceException("未保存的表单无法关联人员");
		}
		basicPersonLinkedMapper.clearByFormId(formId);
		if (!CollectionUtils.isEmpty(BasicPersonLinkedVoList)) {
			int sort = 1;
			for (BasicPersonLinked b:BasicPersonLinkedVoList) {
				b.setSort(sort++);
				if(b.getFormId() != null) {
					b.setYn(CommonConstant.FLAG_YES);
					basicPersonLinkedMapper.updateByPrimaryKeySelective(b);
				} else {
					b.setFormId(formId);
					b.setId(UUIDUtils.getUUID());
					basicPersonLinkedMapper.insertSelective(b);
				}
			}
		}
	}

	@Override
	public void clearAndUpdateBasicPersonLinked(String formId, List<BasicPersonLinkedVo> BasicPersonLinkedVoList, String type) {
		if(StringUtils.isBlank(formId)){
			throw new ServiceException("未保存的表单无法关联人员");
		}
		basicPersonLinkedMapper.clearByFormIdAndType(formId, type);
		if (!CollectionUtils.isEmpty(BasicPersonLinkedVoList)) {
			int sort = 1;
			for (BasicPersonLinked b:BasicPersonLinkedVoList) {
				b.setSort(sort++);
				if(b.getFormId() != null && StringUtils.equals(b.getFormId(), formId)) {
					b.setYn(CommonConstant.FLAG_YES);
					basicPersonLinkedMapper.updateByPrimaryKeySelective(b);
				} else {
					b.setFormId(formId);
					b.setId(UUIDUtils.getUUID());
					basicPersonLinkedMapper.insertSelective(b);
				}
			}
		}
	}
}
