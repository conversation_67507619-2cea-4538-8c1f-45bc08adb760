package com.fd.stdp.service.talent.impl;

import java.util.List;

import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.flowable.FlowCommonService;
import liquibase.pro.packaged.T;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTechPioneersApply;
import com.fd.stdp.beans.talent.vo.TalentTechPioneersApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.talent.TalentTechPioneersApplyMapper;
import com.fd.stdp.service.talent.TalentTechPioneersApplyService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 科技尖兵申请书
 *@Author: wangsh
 *@Date: 2022-01-11 10:48:36
 */
public class TalentTechPioneersApplyServiceImpl extends BaseServiceImpl<TalentTechPioneersApplyMapper, TalentTechPioneersApply> implements TalentTechPioneersApplyService{

	public static final Logger logger = LoggerFactory.getLogger(TalentTechPioneersApplyServiceImpl.class);
	
	@Autowired
	private TalentTechPioneersApplyMapper talentTechPioneersApplyMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新科技尖兵申请书
	 *@param talentTechPioneersApply 科技尖兵申请书对象
	 *@return String 科技尖兵申请书ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTalentTechPioneersApply(TalentTechPioneersApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			talentTechPioneersApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			talentTechPioneersApplyMapper.updateByPrimaryKeySelective(vo);
		}

		basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		// flowCommonService.doFlowStart(FlowableConstant.FLOW_TECH_ACHIEVEMENT, vo, this.mapper, "开始科技尖兵申请流程");
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除科技尖兵申请书
	 *@param id void 科技尖兵申请书ID
	 *@Author: wangsh
	 */
	public void deleteTalentTechPioneersApply(String id) {
		//TODO 做判断后方能执行删除
		TalentTechPioneersApply talentTechPioneersApply=talentTechPioneersApplyMapper.selectByPrimaryKey(id);
		if(talentTechPioneersApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TalentTechPioneersApply temtalentTechPioneersApply=new TalentTechPioneersApply();
		temtalentTechPioneersApply.setYn(CommonConstant.FLAG_NO);
		temtalentTechPioneersApply.setId(talentTechPioneersApply.getId());
		talentTechPioneersApplyMapper.updateByPrimaryKeySelective(temtalentTechPioneersApply);
	}

    /**
     * @Description: 批量删除科技尖兵申请书
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTalentTechPioneersApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteTalentTechPioneersApply(id));
	}

	@Override
	/**
	 *@Description: 查询科技尖兵申请书详情
	 *@param id
	 *@return TalentTechPioneersApply
	 *@Author: wangsh
	 */
	public TalentTechPioneersApply findById(String id) {
		TalentTechPioneersApply talentTechPioneersApply = talentTechPioneersApplyMapper.selectByPrimaryKey(id);
		TalentTechPioneersApplyVo vo = new TalentTechPioneersApplyVo();
		BeanUtils.copyProperties(talentTechPioneersApply, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询科技尖兵申请书
	 *@param talentTechPioneersApplyVo
	 *@return PageInfo<TalentTechPioneersApply>
	 *@Author: wangsh
	 */
	public PageInfo<TalentTechPioneersApply> findPageByQuery(TalentTechPioneersApplyVo talentTechPioneersApplyVo) {
		PageHelper.startPage(talentTechPioneersApplyVo.getPageNum(),talentTechPioneersApplyVo.getPageSize());
		Example example=new Example(TalentTechPioneersApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(talentTechPioneersApplyVo.getName())){
		//	criteria.andEqualTo(talentTechPioneersApplyVo.getName());
		//}
		List<TalentTechPioneersApply> talentTechPioneersApplyList=talentTechPioneersApplyMapper.selectByExample(example);
		return new PageInfo<TalentTechPioneersApply>(talentTechPioneersApplyList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitTalentTechPioneersApply(TalentTechPioneersApplyVo vo) {
		this.saveOrUpdateTalentTechPioneersApply(vo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, vo, this.mapper,
				org.apache.commons.lang3.StringUtils.isNotBlank(vo.getAuditAdvice())?vo.getAuditAdvice():"提交科技尖兵申请");
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String auditTalentTechPioneersApply(TalentTechPioneersApplyVo vo) {
		String id = vo.getId();
		flowCommonService.doFlowStepAudit(vo, this.mapper
				, org.apache.commons.lang3.StringUtils.isNotBlank(vo.getAuditAdvice())?vo.getAuditAdvice():"通过新增机构人员申请"
				, FlowStatusEnum.END.getCode());
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackTalentTechPioneersApply(TalentTechPioneersApplyVo vo) {
		String id = vo.getId();
		flowCommonService.doFlowStepSendBack(vo, this.mapper
				, org.apache.commons.lang3.StringUtils.isNotBlank(vo.getAuditAdvice())?vo.getAuditAdvice():"新增机构人员申请退回"
				, false
		);
		return  id;
	}

	@Override
	public PageInfo<TalentTechPioneersApply> todoList(TalentTechPioneersApplyVo vo) {
		Example example = new Example(TalentTechPioneersApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TalentTechPioneersApply> finishedList(TalentTechPioneersApplyVo vo) {
		Example example = new Example(TalentTechPioneersApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TalentTechPioneersApply> endList(TalentTechPioneersApplyVo vo) {
		Example example = new Example(TalentTechPioneersApply.class);
		Criteria criteria = getCriteria(vo, example);
		criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		example.orderBy("createTime").desc();
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}


	private Criteria getCriteria(TalentTechPioneersApplyVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}
		if(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getUnitName())){
			criteria.andLike("unitName", "%" + vo.getUnitName()+ "%");
		}
		return criteria;
	}
}
