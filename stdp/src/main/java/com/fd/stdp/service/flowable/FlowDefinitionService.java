package com.fd.stdp.service.flowable;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

import com.fd.stdp.beans.flowable.FlowProcDefDto;
import com.fd.stdp.common.RestApiResponse;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @date 2021-04-03 14:41
 */
public interface FlowDefinitionService {

    boolean exist(String processDefinitionKey);


    /**
     * 流程定义列表
     *
     * @param pageNum  当前页码
     * @param pageSize 每页条数
     * @return 流程定义分页列表数据
     */
    PageInfo<FlowProcDefDto> list(Integer pageNum, Integer pageSize);

    /**
     * 导入流程文件
     *
     * @param name
     * @param category
     * @param in
     */
    void importFile(String name, String category, InputStream in);

    /**
     * 读取xml
     * @param deployId
     * @return
     */
    RestApiResponse<?> readXml(String deployId) throws IOException;

    /**
     * 根据流程定义ID启动流程实例
     *
     * @param procDefId
     * @param variables
     * @return
     */

    RestApiResponse<?> startProcessInstanceById(String procDefId,String userId,String userName, Map<String, Object> variables);


    /**
     * 激活或挂起流程定义
     *
     * @param state    状态
     * @param deployId 流程部署ID
     */
    void updateState(Integer state, String deployId);


    /**
     * 删除流程定义
     *
     * @param deployId 流程部署ID act_ge_bytearray 表中 deployment_id值
     */
    void delete(String deployId);


    /**
     * 读取图片文件
     * @param deployId
     * @return
     */
    InputStream readImage(String deployId);
}
