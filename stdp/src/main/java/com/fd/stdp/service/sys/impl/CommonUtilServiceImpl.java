package com.fd.stdp.service.sys.impl;

import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.service.basic.BasicScienceOrgService;
import com.fd.stdp.service.sys.CommonUtilService;
import com.fd.stdp.util.AppUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/1/18 13:59
 */
@Service
public class CommonUtilServiceImpl implements CommonUtilService {
    @Autowired
    private BasicScienceOrgService basicScienceOrgService;

    @Override
    public BasicScienceOrg getLoginBasicScienceOrg() {
        SysUser sysUser = (SysUser) AppUserUtil.getLoginAppUser();
        if (StringUtils.isNotBlank(sysUser.getScienceOrgId())) {
            BasicScienceOrg basicScienceOrg = basicScienceOrgService.findById(sysUser.getScienceOrgId());
            return basicScienceOrg;
        }
        return null;
    }
}
