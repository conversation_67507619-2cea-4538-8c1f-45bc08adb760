package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractCost;
import com.fd.stdp.beans.project.vo.ProjectContractCostVo;
/**
 *@Description: 经费预算
 *@Author: wangsh
 *@Date: 2022-01-12 14:18:49
 */
public interface ProjectContractCostService {

	/**
	 *@Description: 保存或更新经费预算
	 *@param projectContractCost 经费预算对象
	 *@return String 经费预算ID
	 *@Author: wangsh
	 */
	String saveOrUpdateProjectContractCost(ProjectContractCost projectContractCost);
	
	/**
	 *@Description: 删除经费预算
	 *@param id void 经费预算ID
	 *@Author: wangsh
	 */
	void deleteProjectContractCost(String id);

	/**
	 * @Description: 批量删除经费预算
	 * @param projectContractCostVo
	 */
    void deleteMultiProjectContractCost(ProjectContractCostVo projectContractCostVo);

	/**
	 *@Description: 查询经费预算详情
	 *@param id
	 *@return ProjectContractCost
	 *@Author: wangsh
	 */
	ProjectContractCost findById(String id);

	/**
	 *@Description: 分页查询经费预算
	 *@param projectContractCostVo
	 *@return PageInfo<ProjectContractCost>
	 *@Author: wangsh
	 */
	PageInfo<ProjectContractCost> findPageByQuery(ProjectContractCostVo projectContractCostVo);
}
