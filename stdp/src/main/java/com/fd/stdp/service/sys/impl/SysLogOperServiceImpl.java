package com.fd.stdp.service.sys.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.fd.stdp.beans.sys.SysLogOper;
import com.fd.stdp.beans.sys.vo.SysLogOperExportVo;
import com.fd.stdp.beans.sys.vo.SysLogOperVo;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.dao.sys.SysLogOperMapper;
import com.fd.stdp.service.sys.SysLogOperService;
import com.fd.stdp.util.UUIDUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 * @Description: 用户操作日志表
 * @Author: szx
 * @Date: 2020-07-07 17:51:45
 */
public class SysLogOperServiceImpl extends BaseServiceImpl<SysLogOperMapper, SysLogOper> implements SysLogOperService {

    public static final Logger logger = LoggerFactory.getLogger(SysLogOperServiceImpl.class);

    @Autowired
    private SysLogOperMapper sysLogOperMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     * @Description: 保存或更新用户操作日志表
     * @param sysLogOper
     *            用户操作日志表对象
     * @return String 用户操作日志表ID
     * @Author: szx
     */
    public String saveOrUpdateSysLogOper(SysLogOper sysLogOper) {
        if (sysLogOper == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(sysLogOper.getId())) {
            // 新增
            sysLogOper.setId(UUIDUtils.getUUID());
            sysLogOperMapper.insertSelective(sysLogOper);
        } else {
            // 避免页面传入修改
            sysLogOper.setYn(null);
            sysLogOperMapper.updateByPrimaryKeySelective(sysLogOper);
        }
        return sysLogOper.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     * @Description: 删除用户操作日志表
     * @param id
     *            void 用户操作日志表ID
     * @Author: szx
     */
    public void deleteSysLogOper(String id) {
        // TODO 做判断后方能执行删除
        SysLogOper sysLogOper = sysLogOperMapper.selectByPrimaryKey(id);
        if (sysLogOper == null) {
            throw new ServiceException("非法请求");
        }
        // 逻辑删除
        SysLogOper temsysLogOper = new SysLogOper();
        temsysLogOper.setYn(CommonConstant.FLAG_NO);
        temsysLogOper.setId(sysLogOper.getId());
        sysLogOperMapper.updateByPrimaryKeySelective(temsysLogOper);
    }

    @Override
    /**
     * @Description: 查询用户操作日志表详情
     * @param id
     * @return SysLogOper
     * @Author: szx
     */
    public SysLogOper findById(String id) {
        return sysLogOperMapper.selectByPrimaryKey(id);
    }

    @Override
    /**
     * @Description: 分页查询用户操作日志表
     * @param sysLogOperVo
     * @return PageInfo<SysLogOper>
     * @Author: szx
     */
    public PageInfo<SysLogOper> findPageByQuery(SysLogOperVo sysLogOperVo) {
        PageHelper.startPage(sysLogOperVo.getPageNum(), sysLogOperVo.getPageSize());
        Example example = new Example(SysLogOper.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        // 查询条件
        String userName = sysLogOperVo.getCreateUserNickname();
        String operTwoModule = sysLogOperVo.getOperTwoModule();
        String operDetails = sysLogOperVo.getOperDetails();
        List<String> operTime = sysLogOperVo.getOperTime();
        String sortField = sysLogOperVo.getSortField();
        if (!StringUtils.isEmpty(userName)) {
            criteria.andLike("createUserNickname", "%" + userName + "%");
        }

        if (!StringUtils.isEmpty(operTwoModule)) {
            Criteria o = example.createCriteria();
            o.orLike("operOneModule", "%" + operTwoModule + "%").orLike("operTwoModule", "%" + operTwoModule + "%");
            example.and(o);
        }

        if (!StringUtils.isEmpty(operDetails)) {
            criteria.andLike("operDetails", "%" + operDetails + "%");
        }

        if (operTime != null && operTime.size() == 2) {
            criteria.andBetween("createTime", operTime.get(0), operTime.get(1));
        }

        if (!StringUtils.isEmpty(sortField)) {
            if ("desc".equals(sysLogOperVo.getSortType())) {
                example.orderBy(sortField).desc();
            } else {
                example.orderBy(sortField).asc();
            }
        } else {
            example.orderBy("createTime").desc();
        }
        List<SysLogOper> sysLogOperList = sysLogOperMapper.selectByExample(example);
        return new PageInfo<SysLogOper>(sysLogOperList);
    }

    /**
     * @Description:导出日志数据
     * @param sysLogOperVo
     * @return
     * @see com.fd.stdp.service.sys.SysLogOperService#export(com.fd.stdp.beans.sys.vo.SysLogOperVo)
     * @Author: szx
     */
    @Override
    public List<SysLogOperExportVo> export(SysLogOperVo sysLogOperVo) {
        return sysLogOperMapper.selectExportData(sysLogOperVo);
    }

}
