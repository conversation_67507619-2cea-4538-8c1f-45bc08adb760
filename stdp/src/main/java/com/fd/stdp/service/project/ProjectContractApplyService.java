package com.fd.stdp.service.project;

import java.util.List;

import com.fd.stdp.beans.tech.vo.TechAchievementVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractApply;
import com.fd.stdp.beans.project.vo.ProjectContractApplyVo;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;

/**
 *@Description: 任务书申报表
 *@Author: wangsh
 *@Date: 2022-01-12 14:37:32
 */
public interface ProjectContractApplyService {

	/**
	 *@Description: 保存或更新任务书申报表 没有流程会启动新流程
	 *@param projectContractApply 任务书申报表对象
	 *@return String 任务书申报表ID
	 *@Author: wangsh
	 */
	String saveOrUpdateProjectContractApply(ProjectContractApplyVo projectContractApply);
	String saveOrUpdateProjectContractApplyOpt(ProjectContractApplyVo projectContractApply);

	/**
	 *@Description: 删除任务书申报表
	 *@param id void 任务书申报表ID
	 *@Author: wangsh
	 */
	void deleteProjectContractApply(String id);

	/**
	 * @Description: 批量删除任务书申报表
	 * @param ids
	 */
    void deleteMultiProjectContractApply(List<String> ids);

	/**
	 *@Description: 查询任务书申报表详情
	 *@param id
	 *@return ProjectContractApply
	 *@Author: wangsh
	 */
	ProjectContractApplyVo findById(String id);

	/**
	 *@Description: 分页查询任务书申报表
	 *@param projectContractApplyVo
	 *@return PageInfo<ProjectContractApply>
	 *@Author: wangsh
	 */
	PageInfo<ProjectContractApply> findPageByQuery(ProjectContractApplyVo projectContractApplyVo);

	/**
	 * 提交
	 * @param projectContractApply
	 * @return
	 */
    String submitProjectContractApply(ProjectContractApplyVo projectContractApply);

	/**
	 * 审核
	 * @param projectContractApply
	 * @return
	 */
	String auditProjectContractApply(ProjectContractApplyVo projectContractApply);

	/**
	 * 退回
	 * @param projectContractApply
	 * @return
	 */
	String sendBackProjectContractApply(ProjectContractApplyVo projectContractApply);

	/**
	 * 下达
	 * @param projectContractApply
	 * @return
	 */
	String releaseProjectContractApply(ProjectContractApplyVo projectContractApply);

	/**
	 * 完成上传
	 * @param projectContractApply
	 * @return
	 */
    String uploadProjectContractApply(ProjectContractApplyVo projectContractApply);

	// 待办列表
	PageInfo todoList(ProjectContractApplyVo projectContractApply);

	/**
	 * 已办列表
	 * @param projectContractApply
	 * @return
	 */
	PageInfo finishedList(ProjectContractApplyVo projectContractApply);

    PageInfo<ProjectContractApply> endList(ProjectContractApplyVo vo);

	void download(String id, HttpServletResponse response);

    ProjectContractApplyVo findByApplyId(String id);

	void tempApplyToInterim();
}
