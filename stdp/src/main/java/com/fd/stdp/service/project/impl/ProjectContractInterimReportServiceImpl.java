package com.fd.stdp.service.project.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fd.stdp.beans.project.*;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertsVo;
import com.fd.stdp.dao.project.ProjectApplyCostMapper;
import com.fd.stdp.dao.project.ProjectApplyDevicesMapper;
import com.fd.stdp.dao.project.ProjectApplyExpertsMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.vo.ProjectContractInterimReportVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectContractInterimReportMapper;
import com.fd.stdp.service.project.ProjectContractInterimReportService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import static com.fd.stdp.common.BaseController.getCurrentUserId;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 任务书中期检查表单
 *@Author: wangsh
 *@Date: 2022-02-14 14:37:22
 */
public class ProjectContractInterimReportServiceImpl extends BaseServiceImpl<ProjectContractInterimReportMapper, ProjectContractInterimReport> implements ProjectContractInterimReportService{

	public static final Logger logger = LoggerFactory.getLogger(ProjectContractInterimReportServiceImpl.class);

	@Autowired
	private ProjectContractInterimReportMapper projectContractInterimReportMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private ProjectApplyCostMapper projectApplyCostMapper;
	@Autowired
	private ProjectApplyDevicesMapper projectApplyDevicesMapper;
	@Autowired
	private ProjectApplyExpertsMapper projectApplyExpertsMapper;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新任务书中期检查表单
	 *@param projectContractInterimReport 任务书中期检查表单对象
	 *@return String 任务书中期检查表单ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateProjectContractInterimReport(ProjectContractInterimReportVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			projectContractInterimReportMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			projectContractInterimReportMapper.updateByPrimaryKeySelective(vo);
		}


		/**
		 * 预算
		 */
		if(vo.getProjectApplyCost() != null || vo.getProjectAllowanceCost() != null || vo.getProjectSelfCost() != null) {
			List<ProjectApplyCost> costList = new ArrayList<>();
			if (vo.getProjectApplyCost() != null) {
				vo.getProjectApplyCost().setCostType("0");
				costList.add(vo.getProjectApplyCost());
			}
			if (vo.getProjectAllowanceCost() != null) {
				vo.getProjectAllowanceCost().setCostType("1");
				costList.add(vo.getProjectAllowanceCost());
			}
			if (vo.getProjectApplyCostInterim() != null) {
				vo.getProjectApplyCostInterim().setCostType("2");
				costList.add(vo.getProjectApplyCostInterim());
			}
			if (vo.getProjectAllowanceCostInterim() != null) {
				vo.getProjectAllowanceCostInterim().setCostType("3");
				costList.add(vo.getProjectAllowanceCostInterim());
			}
			if (vo.getProjectSelfCostInterim() != null) {
				vo.getProjectSelfCostInterim().setCostType("5");
				costList.add(vo.getProjectSelfCostInterim());
			}
			updateList(vo, costList, projectApplyCostMapper, "setApplyId");
		}

		/**
		 * 设备
		 */
		if(vo.getDevicesList() != null){
			//updateDevice(vo);
			updateList(vo, vo.getDevicesList(), projectApplyDevicesMapper, "setApplyId");
		}
		// 附件
		if(vo.getFiles()!=null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		// 专家评审
		if(vo.getExpertsReviews() != null){
			updateList(vo, vo.getExpertsReviews(), projectApplyExpertsMapper, "setApplyId");
		}

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除任务书中期检查表单
	 *@param id void 任务书中期检查表单ID
	 *@Author: wangsh
	 */
	public void deleteProjectContractInterimReport(String id) {
		//TODO 做判断后方能执行删除
		ProjectContractInterimReport projectContractInterimReport=projectContractInterimReportMapper.selectByPrimaryKey(id);
		if(projectContractInterimReport==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		ProjectContractInterimReport temprojectContractInterimReport=new ProjectContractInterimReport();
		temprojectContractInterimReport.setYn(CommonConstant.FLAG_NO);
		temprojectContractInterimReport.setId(projectContractInterimReport.getId());
		projectContractInterimReportMapper.updateByPrimaryKeySelective(temprojectContractInterimReport);
	}

    /**
     * @Description: 批量删除任务书中期检查表单
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiProjectContractInterimReport(List<String> ids) {
		ids.stream().forEach(id-> this.deleteProjectContractInterimReport(id));
	}

	@Override
	/**
	 *@Description: 查询任务书中期检查表单详情
	 *@param id
	 *@return ProjectContractInterimReport
	 *@Author: wangsh
	 */
	public ProjectContractInterimReport findById(String id) {
		ProjectContractInterimReport projectContractInterimReport = projectContractInterimReportMapper.selectByPrimaryKey(id);
		ProjectContractInterimReportVo vo = new ProjectContractInterimReportVo();
		BeanUtils.copyProperties(projectContractInterimReport, vo);
		/**
		 * 预算
		 */
		Example example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "0");
		vo.setProjectApplyCost(projectApplyCostMapper.selectOneByExample(example));

		example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "1");
		vo.setProjectAllowanceCost(projectApplyCostMapper.selectOneByExample(example));

		example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "2");
		vo.setProjectApplyCostInterim(projectApplyCostMapper.selectOneByExample(example));

		example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "3");
		vo.setProjectAllowanceCostInterim(projectApplyCostMapper.selectOneByExample(example));

		example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "5");
		vo.setProjectSelfCost(projectApplyCostMapper.selectOneByExample(example));

		if(vo.getProjectApplyCost() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("0");
			vo.setProjectApplyCost(p);
		}
		if(vo.getProjectAllowanceCost() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("1");
			vo.setProjectAllowanceCost(p);
		}
		if(vo.getProjectApplyCostInterim() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("2");
			vo.setProjectApplyCostInterim(p);
		}
		if(vo.getProjectAllowanceCostInterim() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("3");
			vo.setProjectAllowanceCostInterim(p);
		}

		if(vo.getProjectSelfCostInterim() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("5");
			vo.setProjectAllowanceCostInterim(p);
		}

		/**
		 * 设备
		 */
		example = new Example(ProjectApplyDevices.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		example.orderBy("sort").asc();
		vo.setDevicesList(projectApplyDevicesMapper.selectByExample(example));

		/**
		 * 流程
		 */
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		/**
		 * 附件
		 */
		vo.setFiles(basicFileAppendixService.findByFormId(vo.getId()));

		/**
		 * 专家评审
		 */
		example = new Example(ProjectApplyDevices.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		vo.setExpertsReviews(projectApplyExpertsMapper.selectByExample(example));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询任务书中期检查表单
	 *@param projectContractInterimReportVo
	 *@return PageInfo<ProjectContractInterimReport>
	 *@Author: wangsh
	 */
	public PageInfo<ProjectContractInterimReport> findPageByQuery(ProjectContractInterimReportVo projectContractInterimReportVo) {
		PageHelper.startPage(projectContractInterimReportVo.getPageNum(),projectContractInterimReportVo.getPageSize());
		Example example=new Example(ProjectContractInterimReport.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(projectContractInterimReportVo.getName())){
		//	criteria.andEqualTo(projectContractInterimReportVo.getName());
		//}
		List<ProjectContractInterimReport> projectContractInterimReportList=projectContractInterimReportMapper.selectByExample(example);
		return new PageInfo<ProjectContractInterimReport>(projectContractInterimReportList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitProjectContractInterimReport(ProjectContractInterimReportVo projectContractInterimReportVo) {
		String id = this.saveOrUpdateProjectContractInterimReport(projectContractInterimReportVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TALENT_TEAM_APPLY, projectContractInterimReportVo, this.mapper,
				StringUtils.isNotBlank(projectContractInterimReportVo.getAuditAdvice())?projectContractInterimReportVo.getAuditAdvice():"提交任务书中期检查表单");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditProjectContractInterimReport(ProjectContractInterimReportVo projectContractInterimReportVo) {
		ProjectContractInterimReportVo vo = (ProjectContractInterimReportVo) findById(projectContractInterimReportVo.getId());
		if(StringUtils.equals(FlowStatusEnum.PROVINCE_AUDIT.getCode(),vo.getFlowStatus())) {
			if(CollectionUtils.isEmpty(projectContractInterimReportVo.getExpertsReviews())){
				throw new ServiceException("请选择专家");
			}
		}
		flowCommonService.doFlowStepAudit(projectContractInterimReportVo, this.mapper
				, StringUtils.isNotBlank(projectContractInterimReportVo.getAuditAdvice()) ? projectContractInterimReportVo.getAuditAdvice() : "任务书中期检查表单审核通过"
				, FlowStatusEnum.EXPERTS_GRADE.getCode(), AssigneeConstant.EXPERT_ROLE);
		return projectContractInterimReportVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackProjectContractInterimReport(ProjectContractInterimReportVo projectContractInterimReportVo) {
		flowCommonService.doFlowStepSendBack(projectContractInterimReportVo, this.mapper
				, StringUtils.isNotBlank(projectContractInterimReportVo.getAuditAdvice())?projectContractInterimReportVo.getAuditAdvice():"任务书中期检查表单退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseProjectContractInterimReport(ProjectContractInterimReportVo projectContractInterimReportVo) {
		saveOrUpdateProjectContractInterimReport(projectContractInterimReportVo);
		flowCommonService.doCompleteTask(projectContractInterimReportVo, this.mapper
				, "任务书中期检查表单任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<ProjectContractInterimReport> todoList(ProjectContractInterimReportVo vo) {

		Example example = new Example(ProjectContractInterimReport.class);
		Criteria criteria = getCriteria(vo, example);
		criteria.andCondition("(STRAT_DATE < (select now()))");
		example.orderBy("createTime").desc();
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<ProjectContractInterimReport> finishedList(ProjectContractInterimReportVo vo) {
		Example example = new Example(ProjectContractInterimReport.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
    public PageInfo<ProjectContractInterimReport> endList(ProjectContractInterimReportVo vo) {
        Example example = new Example(ProjectContractInterimReport.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	@Override
	@Transactional(readOnly = false)
	public String expertSubmitProjectContractInterimReport(ProjectApplyExpertsVo vo) {
		// 中期检查报告的id
		String reportId= vo.getId();
		String userId = getCurrentUserId();
		Example example = new Example(ProjectApplyExpertMumber.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("userId", userId)
				.andEqualTo("applyId", reportId);
		List<ProjectApplyExperts> experts = projectApplyExpertsMapper.selectByExample(example);
		if(CollectionUtils.isEmpty(experts)){
			throw new ServiceException("未找到评审记录");
		}
		ProjectApplyExperts applyExpert = experts.get(0);
		vo.setId(applyExpert.getId());
		vo.setIsSubmit(1);
		projectApplyExpertsMapper.updateByPrimaryKeySelective(vo);

		// 全部评审完成后 专家组组长提交进入下一步流程
		boolean allFinish = true; // 全部评审完成
		boolean isExpertLeader = true; // 是专家组组长
		example = new Example(ProjectApplyExperts.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", reportId)
				.andNotEqualTo("isSubmit", 1);
		if(!CollectionUtils.isEmpty(projectApplyExpertsMapper.selectByExample(example))){
			allFinish = false;
		}
		example = new Example(ProjectApplyExpertMumber.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", reportId)
				.andEqualTo("userId", userId).andEqualTo("expertType", 1);
		if(CollectionUtils.isEmpty(projectApplyExpertsMapper.selectByExample(example))){
			isExpertLeader = false;
		}
		if(allFinish && isExpertLeader) {
			ProjectContractInterimReport report = findById(reportId);
			flowCommonService.doCompleteTask(report, this.mapper, "全部专家评审完成", FlowStatusEnum.PROJECT_REVIEW.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		}

		return null;
	}

	private Criteria getCriteria(ProjectContractInterimReportVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getProjectName())){
			criteria.andLike("projectName", "%" + vo.getProjectName()+ "%");
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getProjectTypeCode())){
			criteria.andEqualTo("projectTypeCode", vo.getProjectTypeCode());
		}
//		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getProjectSecondTypeCode())){
//			criteria.andEqualTo("projectSecondTypeCode", vo.getProjectSecondTypeCode());
//		}
		if(null != vo.getSubmitDateStart()){
			criteria.andGreaterThanOrEqualTo("submitDate", vo.getSubmitDateStart());
		}
		if(null != vo.getSubmitDateEnd()){
			criteria.andLessThanOrEqualTo("submitDate", new Date(vo.getSubmitDateEnd().getTime() + 24 * 60 * 60 * 1000 - 1));
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getApplyUnitName())){
			criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName()+ "%");
		}
		example.orderBy("createTime").desc();
		return criteria;
	}
}
