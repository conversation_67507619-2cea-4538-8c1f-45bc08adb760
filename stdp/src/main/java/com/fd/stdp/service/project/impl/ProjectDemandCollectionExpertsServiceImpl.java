package com.fd.stdp.service.project.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectDemandCollectionExperts;
import com.fd.stdp.beans.project.vo.ProjectDemandCollectionExpertsVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectDemandCollectionExpertsMapper;
import com.fd.stdp.service.project.ProjectDemandCollectionExpertsService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 需求征集专家Service业务层处理
 * @date 2021-11-16
 */
@Service
@Transactional(readOnly = true)
public class ProjectDemandCollectionExpertsServiceImpl extends BaseServiceImpl<ProjectDemandCollectionExpertsMapper, ProjectDemandCollectionExperts> implements ProjectDemandCollectionExpertsService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectDemandCollectionExpertsServiceImpl.class);
    @Autowired
    private ProjectDemandCollectionExpertsMapper projectDemandCollectionExpertsMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新需求征集专家
     *@param projectDemandCollectionExperts 需求征集专家对象
     *@return String 需求征集专家ID
     *@Author: yujianfei
     */
    public String saveOrUpdateProjectDemandCollectionExperts(ProjectDemandCollectionExperts projectDemandCollectionExperts) {
        if (projectDemandCollectionExperts == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(projectDemandCollectionExperts.getId())) {
            //新增
            projectDemandCollectionExperts.setId(UUIDUtils.getUUID());
            projectDemandCollectionExpertsMapper.insertSelective(projectDemandCollectionExperts);
        } else {
            //避免页面传入修改
            projectDemandCollectionExperts.setYn(null);
            projectDemandCollectionExpertsMapper.updateByPrimaryKeySelective(projectDemandCollectionExperts);
        }
        return projectDemandCollectionExperts.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除需求征集专家
     *@param id void 需求征集专家ID
     *@Author: yujianfei
     */
    public void deleteProjectDemandCollectionExperts(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            ProjectDemandCollectionExperts projectDemandCollectionExperts = projectDemandCollectionExpertsMapper.selectByPrimaryKey(id);
            if (projectDemandCollectionExperts == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            ProjectDemandCollectionExperts temprojectDemandCollectionExperts = new ProjectDemandCollectionExperts();
            temprojectDemandCollectionExperts.setYn(CommonConstant.FLAG_NO);
            temprojectDemandCollectionExperts.setId(projectDemandCollectionExperts.getId());
            projectDemandCollectionExpertsMapper.updateByPrimaryKeySelective(temprojectDemandCollectionExperts);
        }
    }

    /**
     * @param id
     * @return ProjectDemandCollectionExperts
     * @Description: 查询需求征集专家详情
     * @Author: yujianfei
     */
    @Override
    public ProjectDemandCollectionExperts findById(String id) {
        return projectDemandCollectionExpertsMapper.selectByPrimaryKey(id);
    }


    /**
     * @param projectDemandCollectionExpertsVo
     * @return PageInfo<ProjectDemandCollectionExperts>
     * @Description: 分页查询需求征集专家
     * @Author: yujianfei
     */
    @Override
    public PageInfo<ProjectDemandCollectionExperts> findPageByQuery(ProjectDemandCollectionExpertsVo projectDemandCollectionExpertsVo) {
        PageHelper.startPage(projectDemandCollectionExpertsVo.getPageNum(), projectDemandCollectionExpertsVo.getPageSize());
        Example example = new Example(ProjectDemandCollectionExperts.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(projectDemandCollectionExpertsVo.getName())){
        //	criteria.andEqualTo(projectDemandCollectionExpertsVo.getName());
        //}
        List<ProjectDemandCollectionExperts> projectDemandCollectionExpertsList = projectDemandCollectionExpertsMapper.selectByExample(example);
        return new PageInfo<ProjectDemandCollectionExperts>(projectDemandCollectionExpertsList);
    }
}
