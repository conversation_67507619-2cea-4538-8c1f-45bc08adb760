package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityInterim;
import com.fd.stdp.beans.innovation.vo.InnovationQualityInterimVo;
/**
 *@Description: 省质检中心中期检查
 *@Author: wangsh
 *@Date: 2022-03-04 09:23:38
 */
public interface InnovationQualityInterimService {

	/**
	 *@Description: 保存或更新省质检中心中期检查
	 *@param innovationQualityInterim 省质检中心中期检查对象
	 *@return String 省质检中心中期检查ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationQualityInterim(InnovationQualityInterimVo innovationQualityInterim);
	
	/**
	 *@Description: 删除省质检中心中期检查
	 *@param id void 省质检中心中期检查ID
	 *@Author: wangsh
	 */
	void deleteInnovationQualityInterim(String id);

	/**
	 * @Description: 批量删除省质检中心中期检查
	 * @param ids
	 */
    void deleteMultiInnovationQualityInterim(List<String> ids);

	/**
	 *@Description: 查询省质检中心中期检查详情
	 *@param id
	 *@return InnovationQualityInterim
	 *@Author: wangsh
	 */
	InnovationQualityInterim findById(String id);

	/**
	 *@Description: 分页查询省质检中心中期检查
	 *@param innovationQualityInterimVo
	 *@return PageInfo<InnovationQualityInterim>
	 *@Author: wangsh
	 */
	PageInfo<InnovationQualityInterim> findPageByQuery(InnovationQualityInterimVo innovationQualityInterimVo);
	
	
	/**
	 * 提交
	 * @param innovationQualityInterimVo
	 * @return
	 */
    String submitInnovationQualityInterim(InnovationQualityInterimVo innovationQualityInterimVo);

	/**
	 * 审核
	 * @param innovationQualityInterimVo
	 * @return
	 */
	String auditInnovationQualityInterim(InnovationQualityInterimVo innovationQualityInterimVo);

	/**
	 * 退回
	 * @param innovationQualityInterimVo
	 * @return
	 */
	String sendBackInnovationQualityInterim(InnovationQualityInterimVo innovationQualityInterimVo);

	/**
	 * 任务书下达
	 * @param innovationQualityInterimVo
	 * @return
	 */
	String releaseInnovationQualityInterim(InnovationQualityInterimVo innovationQualityInterimVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnovationQualityInterim> todoList(InnovationQualityInterimVo innovationQualityInterimVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationQualityInterim> finishedList(InnovationQualityInterimVo innovationQualityInterimVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationQualityInterim> endList(InnovationQualityInterimVo innovationQualityInterimVo);
}
