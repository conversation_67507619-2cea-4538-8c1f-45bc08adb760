package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectDemandCollectionExperts;
import com.fd.stdp.beans.project.vo.ProjectDemandCollectionExpertsVo;

/**
 * 需求征集专家Service接口
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
public interface ProjectDemandCollectionExpertsService {
    /**
     * @param projectDemandCollectionExperts 需求征集专家对象
     * @return String 需求征集专家ID
     * @Description: 保存或更新需求征集专家
     * @Author: yujianfei
     */
    String saveOrUpdateProjectDemandCollectionExperts(ProjectDemandCollectionExperts projectDemandCollectionExperts);

    /**
     * @param ids void 需求征集专家ID
     * @Description: 删除需求征集专家
     * @Author: yujianfei
     */
    void deleteProjectDemandCollectionExperts(List<String> ids);

    /**
     * @param id
     * @return ProjectDemandCollectionExperts
     * @Description: 查询需求征集专家详情
     * @Author: yujianfei
     */
    ProjectDemandCollectionExperts findById(String id);

    /**
     * @param projectDemandCollectionExpertsVo
     * @return PageInfo<ProjectDemandCollectionExperts>
     * @Description: 分页查询需求征集专家
     * @Author: yujianfei
     */
    PageInfo<ProjectDemandCollectionExperts> findPageByQuery(ProjectDemandCollectionExpertsVo projectDemandCollectionExpertsVo);
}
