package com.fd.stdp.service.project;

import java.util.List;

import com.fd.stdp.beans.project.vo.ProjectApplyExpertMumberVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractApplyChange;
import com.fd.stdp.beans.project.vo.ProjectContractApplyChangeVo;
/**
 *@Description: 任务书申报表变更
 *@Author: wangsh
 *@Date: 2022-02-15 15:33:35
 */
public interface ProjectContractApplyChangeService {

	/**
	 *@Description: 保存或更新任务书申报表变更
	 *@param projectContractApplyChange 任务书申报表变更对象
	 *@return String 任务书申报表变更ID
	 *@Author: wangsh
	 */
	String saveOrUpdateProjectContractApplyChange(ProjectContractApplyChangeVo projectContractApplyChange);
	
	/**
	 *@Description: 删除任务书申报表变更
	 *@param id void 任务书申报表变更ID
	 *@Author: wangsh
	 */
	void deleteProjectContractApplyChange(String id);

	/**
	 * @Description: 批量删除任务书申报表变更
	 * @param ids
	 */
    void deleteMultiProjectContractApplyChange(List<String> ids);

	/**
	 *@Description: 查询任务书申报表变更详情
	 *@param id
	 *@return ProjectContractApplyChange
	 *@Author: wangsh
	 */
	ProjectContractApplyChange findById(String id);

	/**
	 *@Description: 分页查询任务书申报表变更
	 *@param projectContractApplyChangeVo
	 *@return PageInfo<ProjectContractApplyChange>
	 *@Author: wangsh
	 */
	PageInfo<ProjectContractApplyChange> findPageByQuery(ProjectContractApplyChangeVo projectContractApplyChangeVo);
	
	
	/**
	 * 提交
	 * @param projectContractApplyChangeVo
	 * @return
	 */
    String submitProjectContractApplyChange(ProjectContractApplyChangeVo projectContractApplyChangeVo);

	/**
	 * 审核
	 * @param projectContractApplyChangeVo
	 * @return
	 */
	String auditProjectContractApplyChange(ProjectContractApplyChangeVo projectContractApplyChangeVo);

	/**
	 * 退回
	 * @param projectContractApplyChangeVo
	 * @return
	 */
	String sendBackProjectContractApplyChange(ProjectContractApplyChangeVo projectContractApplyChangeVo);

	/**
	 * 任务书下达
	 * @param projectContractApplyChangeVo
	 * @return
	 */
	String releaseProjectContractApplyChange(ProjectContractApplyChangeVo projectContractApplyChangeVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<ProjectContractApplyChange> todoList(ProjectContractApplyChangeVo projectContractApplyChangeVo);

	/**
	 * 已办列表
	 */
	PageInfo<ProjectContractApplyChange> finishedList(ProjectContractApplyChangeVo projectContractApplyChangeVo);

	/**
	 * 已完成列表
	 */
	PageInfo<ProjectContractApplyChange> endList(ProjectContractApplyChangeVo projectContractApplyChangeVo);

	/**
	 * 专家论证提交
	 * @param vo
	 * @return
	 */
    String expertSubmitProjectContractApplyChange(ProjectApplyExpertMumberVo vo);
}
