package com.fd.stdp.service.sys.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.fd.stdp.beans.basic.BasicScienceOrgAssets;
import com.fd.stdp.beans.sys.SysDictItem;
import com.fd.stdp.beans.sys.SysProjectNoticeBatch;
import com.fd.stdp.dao.sys.SysDictItemMapper;
import com.fd.stdp.dao.sys.SysProjectNoticeBatchMapper;
import liquibase.pro.packaged.A;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.sys.SysProjectNotice;
import com.fd.stdp.beans.sys.vo.SysProjectNoticeVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.sys.SysProjectNoticeMapper;
import com.fd.stdp.service.sys.SysProjectNoticeService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 公告信息Service业务层处理
 * @date 2021-11-15
 */
@Service
@Transactional(readOnly = true)
public class SysProjectNoticeServiceImpl extends BaseServiceImpl<SysProjectNoticeMapper, SysProjectNotice> implements SysProjectNoticeService {

    private static final Logger logger = LoggerFactory.getLogger(SysProjectNoticeServiceImpl.class);
    @Autowired
    private SysProjectNoticeMapper sysProjectNoticeMapper;

    @Autowired
    private SysProjectNoticeBatchMapper sysProjectNoticeBatchMapper;

    @Autowired
    private SysDictItemMapper sysDictItemMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新公告信息
     *@param sysProjectNoticeVo 公告信息对象
     *@return String 公告信息ID
     *@Author: yujianfei
     */
    public String saveOrUpdateSysProjectNotice(SysProjectNoticeVo sysProjectNoticeVo) {
        if (sysProjectNoticeVo == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(sysProjectNoticeVo.getId())) {
            //新增
            sysProjectNoticeVo.setId(UUIDUtils.getUUID());
            sysProjectNoticeMapper.insertSelective(sysProjectNoticeVo);
        } else {
            //避免页面传入修改
            sysProjectNoticeVo.setYn(null);
            sysProjectNoticeMapper.updateByPrimaryKeySelective(sysProjectNoticeVo);
        }


        SysProjectNoticeBatch sysProjectNoticeBatch = new SysProjectNoticeBatch();
        sysProjectNoticeBatch.setNoticeId(sysProjectNoticeVo.getId());
        //更新或者添加到batch表中
        if (!CollectionUtils.isEmpty(sysProjectNoticeVo.getBatchList())) {
            if (!CollectionUtils.isEmpty(findById(sysProjectNoticeVo.getId()).getBatchList())) {
                List<String> oldList = findById(sysProjectNoticeVo.getId()).getBatchList().stream().map(SysProjectNoticeBatch::getId).collect(Collectors.toList());
                List<String> newList = sysProjectNoticeVo.getBatchList().stream().map(SysProjectNoticeBatch::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(oldList)) {
                    List<String> deleteList = oldList.stream().filter(p -> !newList.contains(p)).collect(Collectors.toList());
                    //删除数据
                    for (String s : deleteList) {
                        SysProjectNoticeBatch deleteSysProjectNoticeBatch = new SysProjectNoticeBatch();
                        deleteSysProjectNoticeBatch.setId(s);
                        sysProjectNoticeBatchMapper.delete(deleteSysProjectNoticeBatch);
                    }
                }
            }
            for (SysProjectNoticeBatch projectNoticeBatch : sysProjectNoticeVo.getBatchList()) {
                SysDictItem sysDictItem = sysDictItemMapper.findByItemCode(projectNoticeBatch.getProjectTypeName());
//                projectNoticeBatch.setProjectText(sysDictItem.getItemValue());
                projectNoticeBatch.setYearNo(sysProjectNoticeVo.getYearNo());
                if (StringUtils.isEmpty(projectNoticeBatch.getId())) {
                    projectNoticeBatch.setNoticeId(sysProjectNoticeVo.getId());
                    projectNoticeBatch.setId(UUIDUtils.getUUID());
                    sysProjectNoticeBatchMapper.insert(projectNoticeBatch);
                } else {
                    // 判断是否已存在
                    Example example = new Example(SysProjectNoticeBatch.class);
                    Criteria criteria = example.createCriteria();
                    criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
                    criteria.andEqualTo("id", projectNoticeBatch.getId());
                    List<SysProjectNoticeBatch> list = sysProjectNoticeBatchMapper.selectByExample(example);
                    if (!CollectionUtils.isEmpty(list)) {
                        sysProjectNoticeBatchMapper.updateByPrimaryKeySelective(projectNoticeBatch);
                    }
                }
            }
        }else {
            //数据为空,删除所有的数据
            sysProjectNoticeBatchMapper.delete(sysProjectNoticeBatch);
        }

        return sysProjectNoticeVo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除公告信息
     *@param id void 公告信息ID
     *@Author: yujianfei
     */
    public void deleteSysProjectNotice(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            SysProjectNotice sysProjectNotice = sysProjectNoticeMapper.selectByPrimaryKey(id);
            if (sysProjectNotice == null) {
                throw new ServiceException("非法请求");
            }

            //逻辑删除
            SysProjectNotice temsysProjectNotice = new SysProjectNotice();
            temsysProjectNotice.setYn(CommonConstant.FLAG_NO);
            temsysProjectNotice.setId(sysProjectNotice.getId());
            sysProjectNoticeMapper.updateByPrimaryKeySelective(temsysProjectNotice);
        }
    }

    /**
     * @param id
     * @return SysProjectNotice
     * @Description: 查询公告信息详情
     * @Author: yujianfei
     */
    @Override
    public SysProjectNoticeVo findById(String id) {
        SysProjectNotice sysProjectNotice = sysProjectNoticeMapper.selectByPrimaryKey(id);
        Example example = new Example(SysProjectNoticeBatch.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        criteria.andEqualTo("noticeId",  sysProjectNotice.getId());
        List<SysProjectNoticeBatch> list = sysProjectNoticeBatchMapper.selectByExample(example);
        SysProjectNoticeVo sysProjectNoticeVo = new SysProjectNoticeVo();
        BeanUtils.copyProperties(sysProjectNotice, sysProjectNoticeVo);
        if (!CollectionUtils.isEmpty(list)) {
            sysProjectNoticeVo.setBatchList(list);
        }
        return sysProjectNoticeVo;
    }


    /**
     * @param sysProjectNoticeVo
     * @return PageInfo<SysProjectNotice>
     * @Description: 分页查询公告信息
     * @Author: yujianfei
     */
    @Override
    public PageInfo<SysProjectNotice> findPageByQuery(SysProjectNoticeVo sysProjectNoticeVo) {
        PageHelper.startPage(sysProjectNoticeVo.getPageNum(), sysProjectNoticeVo.getPageSize());
        Example example = new Example(SysProjectNotice.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        if(!StringUtils.isEmpty(sysProjectNoticeVo.getYearNo())){
        	criteria.andEqualTo("yearNo", sysProjectNoticeVo.getYearNo());
        }
        if (!StringUtils.isEmpty(sysProjectNoticeVo.getNoticeTitle())) {
            criteria.andLike("noticeTitle", "%" + sysProjectNoticeVo.getNoticeTitle() + "%");
        }
        if (!StringUtils.isEmpty(sysProjectNoticeVo.getNoticeContents())) {
            criteria.andLike("noticeContents", "%" + sysProjectNoticeVo.getNoticeContents() + "%");
        }
        if (!StringUtils.isEmpty(sysProjectNoticeVo.getNoticeTypeCode())) {
            criteria.andEqualTo("noticeTypeCode", sysProjectNoticeVo.getNoticeTypeCode());
        }
        List<SysProjectNotice> sysProjectNoticeList = sysProjectNoticeMapper.selectByExample(example);
        return new PageInfo<SysProjectNotice>(sysProjectNoticeList);
    }
}
