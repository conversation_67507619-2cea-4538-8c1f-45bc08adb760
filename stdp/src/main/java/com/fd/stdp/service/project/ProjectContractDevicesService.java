package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractDevices;
import com.fd.stdp.beans.project.vo.ProjectContractDevicesVo;
/**
 *@Description: 仪器设备
 *@Author: wangsh
 *@Date: 2022-01-12 14:18:55
 */
public interface ProjectContractDevicesService {

	/**
	 *@Description: 保存或更新仪器设备
	 *@param projectContractDevices 仪器设备对象
	 *@return String 仪器设备ID
	 *@Author: wangsh
	 */
	String saveOrUpdateProjectContractDevices(ProjectContractDevices projectContractDevices);
	
	/**
	 *@Description: 删除仪器设备
	 *@param id void 仪器设备ID
	 *@Author: wangsh
	 */
	void deleteProjectContractDevices(String id);

	/**
	 * @Description: 批量删除仪器设备
	 * @param projectContractDevicesVo
	 */
    void deleteMultiProjectContractDevices(ProjectContractDevicesVo projectContractDevicesVo);

	/**
	 *@Description: 查询仪器设备详情
	 *@param id
	 *@return ProjectContractDevices
	 *@Author: wangsh
	 */
	ProjectContractDevices findById(String id);

	/**
	 *@Description: 分页查询仪器设备
	 *@param projectContractDevicesVo
	 *@return PageInfo<ProjectContractDevices>
	 *@Author: wangsh
	 */
	PageInfo<ProjectContractDevices> findPageByQuery(ProjectContractDevicesVo projectContractDevicesVo);
}
