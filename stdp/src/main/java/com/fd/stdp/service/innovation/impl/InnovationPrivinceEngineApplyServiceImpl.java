package com.fd.stdp.service.innovation.impl;

import java.util.List;

import com.fd.stdp.beans.innovation.vo.InnovationOtherInnovationApplyVo;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationPrivinceEngineApply;
import com.fd.stdp.beans.innovation.vo.InnovationPrivinceEngineApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationPrivinceEngineApplyMapper;
import com.fd.stdp.service.innovation.InnovationPrivinceEngineApplyService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 省工程研究中心
 *@Author: wangsh
 *@Date: 2022-03-07 19:46:26
 */
public class InnovationPrivinceEngineApplyServiceImpl extends BaseServiceImpl<InnovationPrivinceEngineApplyMapper, InnovationPrivinceEngineApply> implements InnovationPrivinceEngineApplyService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationPrivinceEngineApplyServiceImpl.class);
	
	@Autowired
	private InnovationPrivinceEngineApplyMapper innovationPrivinceEngineApplyMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新省工程研究中心
	 *@param innovationPrivinceEngineApply 省工程研究中心对象
	 *@return String 省工程研究中心ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationPrivinceEngineApply(InnovationPrivinceEngineApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationPrivinceEngineApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationPrivinceEngineApplyMapper.updateByPrimaryKeySelective(vo);
		}
		/**
		 * 附件
		 */
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除省工程研究中心
	 *@param id void 省工程研究中心ID
	 *@Author: wangsh
	 */
	public void deleteInnovationPrivinceEngineApply(String id) {
		//TODO 做判断后方能执行删除
		InnovationPrivinceEngineApply innovationPrivinceEngineApply=innovationPrivinceEngineApplyMapper.selectByPrimaryKey(id);
		if(innovationPrivinceEngineApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationPrivinceEngineApply teminnovationPrivinceEngineApply=new InnovationPrivinceEngineApply();
		teminnovationPrivinceEngineApply.setYn(CommonConstant.FLAG_NO);
		teminnovationPrivinceEngineApply.setId(innovationPrivinceEngineApply.getId());
		innovationPrivinceEngineApplyMapper.updateByPrimaryKeySelective(teminnovationPrivinceEngineApply);
	}

    /**
     * @Description: 批量删除省工程研究中心
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationPrivinceEngineApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationPrivinceEngineApply(id));
	}

	@Override
	/**
	 *@Description: 查询省工程研究中心详情
	 *@param id
	 *@return InnovationPrivinceEngineApply
	 *@Author: wangsh
	 */
	public InnovationPrivinceEngineApply findById(String id) {
		InnovationPrivinceEngineApply apply = innovationPrivinceEngineApplyMapper.selectByPrimaryKey(id);

		InnovationPrivinceEngineApplyVo vo = new InnovationPrivinceEngineApplyVo();
		BeanUtils.copyProperties(apply, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询省工程研究中心
	 *@param innovationPrivinceEngineApplyVo
	 *@return PageInfo<InnovationPrivinceEngineApply>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationPrivinceEngineApply> findPageByQuery(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApplyVo) {
		PageHelper.startPage(innovationPrivinceEngineApplyVo.getPageNum(),innovationPrivinceEngineApplyVo.getPageSize());
		Example example=new Example(InnovationPrivinceEngineApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationPrivinceEngineApplyVo.getName())){
		//	criteria.andEqualTo(innovationPrivinceEngineApplyVo.getName());
		//}
		List<InnovationPrivinceEngineApply> innovationPrivinceEngineApplyList=innovationPrivinceEngineApplyMapper.selectByExample(example);
		return new PageInfo<InnovationPrivinceEngineApply>(innovationPrivinceEngineApplyList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationPrivinceEngineApply(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApplyVo) {
		String id = this.saveOrUpdateInnovationPrivinceEngineApply(innovationPrivinceEngineApplyVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innovationPrivinceEngineApplyVo, this.mapper,
				StringUtils.isNotBlank(innovationPrivinceEngineApplyVo.getAuditAdvice())?innovationPrivinceEngineApplyVo.getAuditAdvice():"提交省工程研究中心");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationPrivinceEngineApply(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApplyVo) {
		flowCommonService.doFlowStepAudit(innovationPrivinceEngineApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationPrivinceEngineApplyVo.getAuditAdvice()) ? innovationPrivinceEngineApplyVo.getAuditAdvice() : "省工程研究中心审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationPrivinceEngineApplyVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationPrivinceEngineApply(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApplyVo) {
		flowCommonService.doFlowStepSendBack(innovationPrivinceEngineApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationPrivinceEngineApplyVo.getAuditAdvice())?innovationPrivinceEngineApplyVo.getAuditAdvice():"省工程研究中心退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationPrivinceEngineApply(InnovationPrivinceEngineApplyVo innovationPrivinceEngineApplyVo) {
		flowCommonService.doCompleteTask(innovationPrivinceEngineApplyVo, this.mapper
				, "省工程研究中心任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnovationPrivinceEngineApply> todoList(InnovationPrivinceEngineApplyVo vo) {

		Example example = new Example(InnovationPrivinceEngineApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationPrivinceEngineApply> finishedList(InnovationPrivinceEngineApplyVo vo) {
		Example example = new Example(InnovationPrivinceEngineApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnovationPrivinceEngineApply> endList(InnovationPrivinceEngineApplyVo vo) {
        Example example = new Example(InnovationPrivinceEngineApply.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(InnovationPrivinceEngineApply vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件

		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getCenterName())){
			criteria.andLike("centerName", "%" + vo.getCenterName()+ "%");
		}
		return criteria;
	}
}
