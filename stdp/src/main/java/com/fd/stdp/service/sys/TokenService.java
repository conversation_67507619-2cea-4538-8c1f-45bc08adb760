package com.fd.stdp.service.sys;

import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.beans.sys.vo.Token;

/**
 * Token管理器<br>
 * 可存储到redis或者数据库<br>
 * 具体可看实现类<br>
 * 默认基于redis，实现类为 com.boot.security.server.service.impl.TokenServiceJWTImpl<br>
 * 如要换成数据库存储，将TokenServiceImpl类上的注解@Primary挪到com.boot.security.server.service.impl.TokenServiceDbImpl
 * 
 * 
 * <AUTHOR>
 *
 *         2017年10月14日
 */
public interface TokenService {

	Token saveToken(LoginUser loginUser);

	void refresh(LoginUser loginUser);

	Object getLoginUser(String token);

	boolean deleteToken(String token);

}
