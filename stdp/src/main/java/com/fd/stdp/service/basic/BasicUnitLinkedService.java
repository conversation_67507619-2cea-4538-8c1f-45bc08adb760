package com.fd.stdp.service.basic;

import java.util.List;

import com.fd.stdp.beans.basic.vo.BasicPersonLinkedVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicUnitLinked;
import com.fd.stdp.beans.basic.vo.BasicUnitLinkedVo;
/**
 *@Description: 单位关联表
 *@Author: wangsh
 *@Date: 2022-02-10 14:49:38
 */
public interface BasicUnitLinkedService {

	/**
	 *@Description: 保存或更新单位关联表
	 *@param basicUnitLinked 单位关联表对象
	 *@return String 单位关联表ID
	 *@Author: wangsh
	 */
	String saveOrUpdateBasicUnitLinked(BasicUnitLinkedVo basicUnitLinked);
	
	/**
	 *@Description: 删除单位关联表
	 *@param id void 单位关联表ID
	 *@Author: wangsh
	 */
	void deleteBasicUnitLinked(String id);

	/**
	 * @Description: 批量删除单位关联表
	 * @param ids
	 */
    void deleteMultiBasicUnitLinked(List<String> ids);

	/**
	 *@Description: 查询单位关联表详情
	 *@param id
	 *@return BasicUnitLinked
	 *@Author: wangsh
	 */
	BasicUnitLinked findById(String id);

	/**
	 *@Description: 分页查询单位关联表
	 *@param basicUnitLinkedVo
	 *@return PageInfo<BasicUnitLinked>
	 *@Author: wangsh
	 */
	PageInfo<BasicUnitLinked> findPageByQuery(BasicUnitLinkedVo basicUnitLinkedVo);


	/**
	 * 获取表单对应的机构列表
	 * @param formId
	 * @return
	 */
	List<BasicUnitLinkedVo> findByFormId(String formId);

	/**
	 * 清除并更新表单对应的机构信息
	 * @param formId 表单id basicFileAppendixVoList人员信息
	 * @param linkedList
	 */
	void clearAndUpdateBasicUnitLinked(String formId, List<BasicUnitLinkedVo> linkedList);
}
