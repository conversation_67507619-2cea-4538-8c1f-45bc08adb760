package com.fd.stdp.service.talent.impl;

import java.util.List;

import com.fd.stdp.beans.talent.TalentTeamContributeTarget;
import com.fd.stdp.dao.talent.TalentTeamContributeTargetMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectContractChange;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectContractChangeVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.talent.TalentLeaderSubjectContractChangeMapper;
import com.fd.stdp.service.talent.TalentLeaderSubjectContractChangeService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 学科带头人建设任务书变更
 *@Author: wangsh
 *@Date: 2022-02-16 09:00:53
 */
public class TalentLeaderSubjectContractChangeServiceImpl extends BaseServiceImpl<TalentLeaderSubjectContractChangeMapper, TalentLeaderSubjectContractChange> implements TalentLeaderSubjectContractChangeService{

	public static final Logger logger = LoggerFactory.getLogger(TalentLeaderSubjectContractChangeServiceImpl.class);
	
	@Autowired
	private TalentLeaderSubjectContractChangeMapper talentLeaderSubjectContractChangeMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private TalentTeamContributeTargetMapper talentTeamContributeTargetMapper;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新学科带头人建设任务书变更
	 *@param talentLeaderSubjectContractChange 学科带头人建设任务书变更对象
	 *@return String 学科带头人建设任务书变更ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTalentLeaderSubjectContractChange(TalentLeaderSubjectContractChangeVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			talentLeaderSubjectContractChangeMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			talentLeaderSubjectContractChangeMapper.updateByPrimaryKeySelective(vo);
		}

		if(!CollectionUtils.isEmpty(vo.getContractTargets())){
			updateList(vo, vo.getContractTargets(), talentTeamContributeTargetMapper, "setContractId");
		}

		if(!CollectionUtils.isEmpty(vo.getFiles())){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}

		// 开启流程
		// flowCommonService.doFlowStart(FlowableConstant.FLOW_TALENT_TEAM_APPLY, vo, this.mapper, "开始创新团队建设任务书变更流程");

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除学科带头人建设任务书变更
	 *@param id void 学科带头人建设任务书变更ID
	 *@Author: wangsh
	 */
	public void deleteTalentLeaderSubjectContractChange(String id) {
		//TODO 做判断后方能执行删除
		TalentLeaderSubjectContractChange talentLeaderSubjectContractChange=talentLeaderSubjectContractChangeMapper.selectByPrimaryKey(id);
		if(talentLeaderSubjectContractChange==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TalentLeaderSubjectContractChange temtalentLeaderSubjectContractChange=new TalentLeaderSubjectContractChange();
		temtalentLeaderSubjectContractChange.setYn(CommonConstant.FLAG_NO);
		temtalentLeaderSubjectContractChange.setId(talentLeaderSubjectContractChange.getId());
		talentLeaderSubjectContractChangeMapper.updateByPrimaryKeySelective(temtalentLeaderSubjectContractChange);
	}

    /**
     * @Description: 批量删除学科带头人建设任务书变更
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTalentLeaderSubjectContractChange(List<String> ids) {
		ids.stream().forEach(id-> this.deleteTalentLeaderSubjectContractChange(id));
	}

	@Override
	/**
	 *@Description: 查询学科带头人建设任务书变更详情
	 *@param id
	 *@return TalentLeaderSubjectContractChange
	 *@Author: wangsh
	 */
	public TalentLeaderSubjectContractChange findById(String id) {
		TalentLeaderSubjectContractChange talentLeaderSubjectContractChange = talentLeaderSubjectContractChangeMapper.selectByPrimaryKey(id);
		TalentLeaderSubjectContractChangeVo vo = new TalentLeaderSubjectContractChangeVo();

		BeanUtils.copyProperties(talentLeaderSubjectContractChange, vo);
		Example example = new Example(TalentTeamContributeTarget.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("contractId", id);
		vo.setContractTargets(talentTeamContributeTargetMapper.selectByExample(example));
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		// 流程
		if(StringUtils.isNotBlank(vo.getFlowStatus())) {
			vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		}
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询学科带头人建设任务书变更
	 *@param talentLeaderSubjectContractChangeVo
	 *@return PageInfo<TalentLeaderSubjectContractChange>
	 *@Author: wangsh
	 */
	public PageInfo<TalentLeaderSubjectContractChange> findPageByQuery(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChangeVo) {
		PageHelper.startPage(talentLeaderSubjectContractChangeVo.getPageNum(),talentLeaderSubjectContractChangeVo.getPageSize());
		Example example=new Example(TalentLeaderSubjectContractChange.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		criteria.andEqualTo("flowStatus",FlowStatusEnum.END.getCode());
		//查询条件
		//if(!StringUtils.isEmpty(talentLeaderSubjectContractChangeVo.getName())){
		//	criteria.andEqualTo(talentLeaderSubjectContractChangeVo.getName());
		//}
		List<TalentLeaderSubjectContractChange> talentLeaderSubjectContractChangeList=talentLeaderSubjectContractChangeMapper.selectByExample(example);
		return new PageInfo<TalentLeaderSubjectContractChange>(talentLeaderSubjectContractChangeList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitTalentLeaderSubjectContractChange(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChangeVo) {
		String id = this.saveOrUpdateTalentLeaderSubjectContractChange(talentLeaderSubjectContractChangeVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, talentLeaderSubjectContractChangeVo, this.mapper,
				StringUtils.isNotBlank(talentLeaderSubjectContractChangeVo.getAuditAdvice())?talentLeaderSubjectContractChangeVo.getAuditAdvice():"提交学科带头人建设任务书变更");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditTalentLeaderSubjectContractChange(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChangeVo) {
		flowCommonService.doFlowStepAudit(talentLeaderSubjectContractChangeVo, this.mapper
				, StringUtils.isNotBlank(talentLeaderSubjectContractChangeVo.getAuditAdvice()) ? talentLeaderSubjectContractChangeVo.getAuditAdvice() : "学科带头人建设任务书变更审核通过"
				, FlowStatusEnum.CHOOSE_EXPERTS.getCode());
		return talentLeaderSubjectContractChangeVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackTalentLeaderSubjectContractChange(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChangeVo) {
		flowCommonService.doFlowStepSendBack(talentLeaderSubjectContractChangeVo, this.mapper
				, StringUtils.isNotBlank(talentLeaderSubjectContractChangeVo.getAuditAdvice())?talentLeaderSubjectContractChangeVo.getAuditAdvice():"学科带头人建设任务书变更退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseTalentLeaderSubjectContractChange(TalentLeaderSubjectContractChangeVo talentLeaderSubjectContractChangeVo) {
		flowCommonService.doCompleteTask(talentLeaderSubjectContractChangeVo, this.mapper
				, "学科带头人建设任务书变更任务书下达完成"
				, FlowStatusEnum.PROJECT_RELEASE.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<TalentLeaderSubjectContractChange> todoList(TalentLeaderSubjectContractChangeVo vo) {

		Example example = new Example(TalentLeaderSubjectContractChange.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TalentLeaderSubjectContractChange> finishedList(TalentLeaderSubjectContractChangeVo vo) {
		Example example = new Example(TalentLeaderSubjectContractChange.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<TalentLeaderSubjectContractChange> endList(TalentLeaderSubjectContractChangeVo vo) {
        Example example = new Example(TalentLeaderSubjectContractChange.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(TalentLeaderSubjectContractChange vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}
		return criteria;
	}
}
