package com.fd.stdp.service.reward.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.reward.RewordCooperUnit;
import com.fd.stdp.beans.reward.vo.RewordCooperUnitVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.reward.RewordCooperUnitMapper;
import com.fd.stdp.service.reward.RewordCooperUnitService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 科技成果关联合作单位
 *@Author: wangsh
 *@Date: 2022-07-05 16:58:57
 */
public class RewordCooperUnitServiceImpl extends BaseServiceImpl<RewordCooperUnitMapper, RewordCooperUnit> implements RewordCooperUnitService{

	public static final Logger logger = LoggerFactory.getLogger(RewordCooperUnitServiceImpl.class);
	
	@Autowired
	private RewordCooperUnitMapper rewordCooperUnitMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新科技成果关联合作单位
	 *@param rewordCooperUnit 科技成果关联合作单位对象
	 *@return String 科技成果关联合作单位ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateRewordCooperUnit(RewordCooperUnitVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			rewordCooperUnitMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			rewordCooperUnitMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除科技成果关联合作单位
	 *@param id void 科技成果关联合作单位ID
	 *@Author: wangsh
	 */
	public void deleteRewordCooperUnit(String id) {
		//TODO 做判断后方能执行删除
		RewordCooperUnit rewordCooperUnit=rewordCooperUnitMapper.selectByPrimaryKey(id);
		if(rewordCooperUnit==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		RewordCooperUnit temrewordCooperUnit=new RewordCooperUnit();
		temrewordCooperUnit.setYn(CommonConstant.FLAG_NO);
		temrewordCooperUnit.setId(rewordCooperUnit.getId());
		rewordCooperUnitMapper.updateByPrimaryKeySelective(temrewordCooperUnit);
	}

    /**
     * @Description: 批量删除科技成果关联合作单位
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiRewordCooperUnit(List<String> ids) {
		ids.stream().forEach(id-> this.deleteRewordCooperUnit(id));
	}

	@Override
	/**
	 *@Description: 查询科技成果关联合作单位详情
	 *@param id
	 *@return RewordCooperUnit
	 *@Author: wangsh
	 */
	public RewordCooperUnit findById(String id) {
		return rewordCooperUnitMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询科技成果关联合作单位
	 *@param rewordCooperUnitVo
	 *@return PageInfo<RewordCooperUnit>
	 *@Author: wangsh
	 */
	public PageInfo<RewordCooperUnit> findPageByQuery(RewordCooperUnitVo rewordCooperUnitVo) {
		PageHelper.startPage(rewordCooperUnitVo.getPageNum(),rewordCooperUnitVo.getPageSize());
		Example example=new Example(RewordCooperUnit.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(rewordCooperUnitVo.getName())){
		//	criteria.andEqualTo(rewordCooperUnitVo.getName());
		//}
		List<RewordCooperUnit> rewordCooperUnitList=rewordCooperUnitMapper.selectByExample(example);
		return new PageInfo<RewordCooperUnit>(rewordCooperUnitList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitRewordCooperUnit(RewordCooperUnitVo rewordCooperUnitVo) {
		String id = this.saveOrUpdateRewordCooperUnit(rewordCooperUnitVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, rewordCooperUnitVo, this.mapper,
				StringUtils.isNotBlank(rewordCooperUnitVo.getAuditAdvice())?rewordCooperUnitVo.getAuditAdvice():"提交科技成果关联合作单位");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditRewordCooperUnit(RewordCooperUnitVo rewordCooperUnitVo) {
		flowCommonService.doFlowStepAudit(rewordCooperUnitVo, this.mapper
				, StringUtils.isNotBlank(rewordCooperUnitVo.getAuditAdvice()) ? rewordCooperUnitVo.getAuditAdvice() : "科技成果关联合作单位审核通过"
				, FlowStatusEnum.END.getCode());
		return rewordCooperUnitVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackRewordCooperUnit(RewordCooperUnitVo rewordCooperUnitVo) {
		flowCommonService.doFlowStepSendBack(rewordCooperUnitVo, this.mapper
				, StringUtils.isNotBlank(rewordCooperUnitVo.getAuditAdvice())?rewordCooperUnitVo.getAuditAdvice():"科技成果关联合作单位退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseRewordCooperUnit(RewordCooperUnitVo rewordCooperUnitVo) {
		flowCommonService.doCompleteTask(rewordCooperUnitVo, this.mapper
				, "科技成果关联合作单位任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<RewordCooperUnit> todoList(RewordCooperUnitVo vo) {

		Example example = new Example(RewordCooperUnit.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<RewordCooperUnit> finishedList(RewordCooperUnitVo vo) {
		Example example = new Example(RewordCooperUnit.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<RewordCooperUnit> endList(RewordCooperUnitVo vo) {
        Example example=new Example(RewordCooperUnit.class);
        example.orderBy("createTime").desc();
    	Criteria criteria=getCriteria(vo, example);
    	return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
    }

	private Criteria getCriteria(RewordCooperUnitVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
		return criteria;
	}
}
