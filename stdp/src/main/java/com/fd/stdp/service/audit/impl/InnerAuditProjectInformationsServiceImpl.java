package com.fd.stdp.service.audit.impl;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.fd.stdp.beans.audit.InnerAuditEquippedInformation;
import com.fd.stdp.beans.audit.InnerAuditOrg;
import com.fd.stdp.beans.audit.InnerAuditProjectInformation;
import com.fd.stdp.beans.audit.InnerAuditWorkContacts;
import com.fd.stdp.beans.audit.vo.*;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.constant.InnerAuditBasicConstant;
import com.fd.stdp.dao.audit.InnerAuditOrgMapper;
import com.fd.stdp.service.user.SysUserUtilService;
import net.logstash.logback.encoder.org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.audit.InnerAuditProjectInformationMapper;
import com.fd.stdp.service.audit.InnerAuditProjectInformationsService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import static com.fd.stdp.common.BaseController.getCurrentOrgName;
import static com.fd.stdp.common.BaseController.getLoginUser;
import static com.fd.stdp.service.audit.impl.InnerAuditUtil.FANGYUAN;
import static com.fd.stdp.util.AppUserUtil.getCurrentRealName;
import static com.fd.stdp.util.AppUserUtil.getCurrentUserName;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 委托社会审计机构审计项目情况表
 *@Author: sef
 *@Date: 2022-06-06 13:56:38
 */
public class InnerAuditProjectInformationsServiceImpl extends BaseServiceImpl<InnerAuditProjectInformationMapper, InnerAuditProjectInformation> implements InnerAuditProjectInformationsService {

    public static final Logger logger = LoggerFactory.getLogger(InnerAuditProjectInformationsServiceImpl.class);

    @Autowired
    private InnerAuditProjectInformationMapper innerAuditProjectInformationMapper;
    @Autowired
    private FlowCommonService flowCommonService;

    @Autowired
    private SysUserUtilService sysUserUtilService;

    @Autowired
    private InnerAuditOrgMapper innerAuditOrgMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新委托社会审计机构审计项目情况表
     *@param innerAuditProjectInformation 委托社会审计机构审计项目情况表对象
     *@return String 委托社会审计机构审计项目情况表ID
     *@Author: sef
     */
    public String saveOrUpdateInnerAuditProjectInformation(InnerAuditProjectInformationVo vo) {
        if (vo == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(vo.getId())) {
            //新增
            vo.setId(UUIDUtils.getUUID());
            vo.setFillState(InnerAuditBasicConstant.FILL_STATE_ESCALATED);
            innerAuditProjectInformationMapper.insertSelective(vo);
        } else {
            //避免页面传入修改
            vo.setYn(null);
            innerAuditProjectInformationMapper.updateByPrimaryKeySelective(vo);
        }
        return vo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除委托社会审计机构审计项目情况表
     *@param id void 委托社会审计机构审计项目情况表ID
     *@Author: sef
     */
    public void deleteInnerAuditProjectInformation(String id) {
        InnerAuditProjectInformation innerAuditProjectInformation = innerAuditProjectInformationMapper.selectByPrimaryKey(id);
        if (innerAuditProjectInformation == null) {
            throw new ServiceException("非法请求");
        }
        //逻辑删除
        InnerAuditProjectInformation teminnerAuditProjectInformation = new InnerAuditProjectInformation();
        teminnerAuditProjectInformation.setYn(CommonConstant.FLAG_NO);
        teminnerAuditProjectInformation.setId(innerAuditProjectInformation.getId());
        innerAuditProjectInformationMapper.updateByPrimaryKeySelective(teminnerAuditProjectInformation);
    }

    /**
     * @param ids
     * @Description: 批量删除委托社会审计机构审计项目情况表
     */
    @Override
    @Transactional(readOnly = false)
    public void deleteMultiInnerAuditProjectInformation(List<String> ids) {
        ids.stream().forEach(id -> this.deleteInnerAuditProjectInformation(id));
    }

    @Override
    /**
     *@Description: 查询委托社会审计机构审计项目情况表详情
     *@param id
     *@return InnerAuditProjectInformation
     *@Author: sef
     */
    public InnerAuditProjectInformation findById(String id) {
        return innerAuditProjectInformationMapper.selectByPrimaryKey(id);
    }

    @Override
    /**
     *@Description: 分页查询委托社会审计机构审计项目情况表
     *@param innerAuditProjectInformationVo
     *@return PageInfo<InnerAuditProjectInformation>
     *@Author: sef
     */
    public PageInfo<InnerAuditProjectInformationVo> findPageByQuery(InnerAuditProjectInformationVo innerAuditProjectInformationVo) {
        Example example = new Example(InnerAuditProjectInformation.class);
        example.setOrderByClause(" ORDER_VAL");
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);

        if (StringUtils.isEmpty(innerAuditProjectInformationVo.getUnitName())) {
            InnerAuditUtil.OrgTypeEnum orgTypeEnum = getFillType(getCurrentOrgName());
            switch (orgTypeEnum) {
                case PROVINCE:
                    break;
                default:
                    criteria.andEqualTo("unitName", getCurrentOrgName());
            }
        } else {
            criteria.andLike("unitName", '%' + innerAuditProjectInformationVo.getUnitName() + '%');
        }

        //查询条件
        if (!StringUtils.isEmpty(innerAuditProjectInformationVo.getProjectName())) {
            criteria.andLike("projectName", addLike(innerAuditProjectInformationVo.getProjectName()));
        }
        if (!ObjectUtils.isEmpty(innerAuditProjectInformationVo.getFillState())) {
            criteria.andEqualTo("fillState", innerAuditProjectInformationVo.getFillState());
        }
        if (!StringUtils.isEmpty(innerAuditProjectInformationVo.getAuditAdvice())) {
            criteria.andLike("auditAdvice", addLike(innerAuditProjectInformationVo.getAuditAdvice()));
        }
        if (!StringUtils.isEmpty(innerAuditProjectInformationVo.getBudgetUnitName())) {
            criteria.andLike("budgetUnitName", addLike(innerAuditProjectInformationVo.getBudgetUnitName()));
        }
        if (!StringUtils.isEmpty(innerAuditProjectInformationVo.getEntrustUnitName())) {
            criteria.andLike("entrustUnitName", addLike(innerAuditProjectInformationVo.getEntrustUnitName()));
        }
        List<InnerAuditProjectInformation> innerAuditProjectInformationList = innerAuditProjectInformationMapper.selectByExample(example);
        List<InnerAuditProjectInformationVo> tempList = new ArrayList<>(innerAuditProjectInformationList.size());
        innerAuditProjectInformationList.stream().forEach(item -> {
            InnerAuditProjectInformationVo voToDto = new InnerAuditProjectInformationVo();
            BeanUtils.copyProperties(item, voToDto);
            voToDto.setUnitNameT(InnerAuditBasicConstant.UNIT_NAME_T);
            tempList.add(voToDto);
        });
        PageInfo pageInfo = new PageInfo(tempList.isEmpty() ? Collections.emptyList() : tempList);
        pageInfo.setList(tempList.isEmpty() ? Collections.emptyList() : startPage(tempList, innerAuditProjectInformationVo.getPageNum() == null ? 1 : innerAuditProjectInformationVo.getPageNum(), innerAuditProjectInformationVo.getPageSize() == null ? 10 : innerAuditProjectInformationVo.getPageSize()));
        pageInfo.setPageNum(innerAuditProjectInformationVo.getPageNum());
        pageInfo.setPageSize(innerAuditProjectInformationVo.getPageSize());
        return pageInfo;
    }

    private String addLike(String str) {
        return '%' + str + '%';
    }

    @Override
    @Transactional(readOnly = false)
    public String submitInnerAuditProjectInformation(InnerAuditProjectInformationVo innerAuditProjectInformationVo) {
        List<InnerAuditProjectInformationVo> infomationList = innerAuditProjectInformationVo.getProjectList();
        infomationList.stream().forEach(item -> item.setFillState(InnerAuditBasicConstant.FILL_STATE_PROVINCIAL_BUREAU_MODIFICATION));
        this.saveBatchInnerAuditWorkContacts(infomationList);
        return "success";
    }

    @Override
    @Transactional(readOnly = false)
    public String auditInnerAuditProjectInformation(InnerAuditProjectInformationVo innerAuditProjectInformationVo) {
        flowCommonService.doFlowStepAudit(innerAuditProjectInformationVo, this.mapper
                , StringUtils.isNotBlank(innerAuditProjectInformationVo.getAuditAdvice()) ? innerAuditProjectInformationVo.getAuditAdvice() : "委托社会审计机构审计项目情况表审核通过"
                , FlowStatusEnum.END.getCode());
        return innerAuditProjectInformationVo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    public String sendBackInnerAuditProjectInformation(InnerAuditProjectInformationVo vo) {
        if (StringUtils.isNotBlank(vo.getUnitName())) {
            innerAuditProjectInformationMapper.sendBack(vo.getUnitName());
            return "success";
        }
        return "缺少单位名称！";
    }

    @Override
    @Transactional(readOnly = false)
    public String releaseInnerAuditProjectInformation(InnerAuditProjectInformationVo innerAuditProjectInformationVo) {
        flowCommonService.doCompleteTask(innerAuditProjectInformationVo, this.mapper
                , "委托社会审计机构审计项目情况表任务书下达完成"
                , FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
        return null;
    }


    @Override
    public PageInfo<InnerAuditProjectInformation> todoList(InnerAuditProjectInformationVo vo) {

        Example example = new Example(InnerAuditProjectInformation.class);
        Criteria criteria = getCriteria(vo, example);
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
    }

    @Override
    public PageInfo<InnerAuditProjectInformation> finishedList(InnerAuditProjectInformationVo vo) {
        Example example = new Example(InnerAuditProjectInformation.class);
        Criteria criteria = getCriteria(vo, example);
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
    }

    @Override
    public PageInfo<InnerAuditProjectInformation> endList(InnerAuditProjectInformationVo vo) {
        Example example = new Example(InnerAuditProjectInformation.class);
        example.orderBy("createTime").desc();
        Criteria criteria = getCriteria(vo, example);
        return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
    }

    @Override
    @Transactional(readOnly = false)
    public void saveBatchInnerAuditWorkContacts(List<InnerAuditProjectInformationVo> contactList) {

        if(CollectionUtils.isEmpty(contactList)) {
            throw new ServiceException("请添加内容后重试");
        }
        // 已存在(单位)
        List<InnerAuditProjectInformationVo> collect = contactList.stream().filter(item ->
                org.springframework.util.StringUtils.hasText(org.apache.commons.lang.ObjectUtils.toString(innerAuditProjectInformationMapper.findByUnitIsOrNotExist(item), ""))).collect(Collectors.toList());

        // 批量删除 逻辑删除
        if (!collect.isEmpty()) {
            innerAuditProjectInformationMapper.deleteByUnit(collect.get(0).getUnitName());
        }
        InnerAuditProjectInformationVo old = collect.get(0);
        contactList.stream().forEach(item -> {
                    if (StringUtils.isBlank(item.getOrgName())) {
                        item.setOrgName(getCurrentOrgName());
                        item.setOrgCode(getLoginUser().getAreaCode());
                    }
                    item.setId(UUIDUtils.getUUID());
                    item.setFillState(old.getFillState() == null?InnerAuditBasicConstant.FILL_STATE_ESCALATED:old.getFillState());
                    item.setFillTime(old.getFillTime()==null?new Date():old.getFillTime());
                    item.setUnitName(old.getUnitName()==null?getCurrentOrgName():old.getUnitName());
                    if (org.springframework.util.StringUtils.hasText(item.getProjectName())) {
                        // 处理特殊字符 "" -> 反转义
                        item.setProjectName(StringEscapeUtils.unescapeHtml(item.getProjectName()));// 反转义
                    }
                    item.setBudgetUnitName("浙江省市场监督管理局");
                    item.setEntrustUnitName(old.getEntrustUnitName()==null?getCurrentOrgName():old.getEntrustUnitName());
                    innerAuditProjectInformationMapper.insert(item);
                }
        );
    }

    private Criteria getCriteria(InnerAuditProjectInformationVo vo, Example example) {
        Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
        return criteria;
    }


    private Integer getOperType(String orgName) {
        LoginUser loginUser = getLoginUser();
        Integer operType = sysUserUtilService.getUserOperType(loginUser);
        Example example = new Example(InnerAuditOrg.class);
        example.createCriteria().andEqualTo("orgName", orgName);
        if (CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))) {
            return operType;
        }
        return 0;
    }

    private boolean getIsProOrg(String org) {
        Example example = new Example(InnerAuditOrg.class);
        example.createCriteria().andLike("orgName", "%" + org + "%");
        if (CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))) {
            return false;
        }
        return true;
    }

    // 0 省局 1 直属单位 2市局 3 县局
    private InnerAuditUtil.OrgTypeEnum getFillType(String org) {
        if (getIsProOrg(org)) {
            if (FANGYUAN.equals(org)) {
                return InnerAuditUtil.OrgTypeEnum.PROVINCE_GO_EM;
            }
            return InnerAuditUtil.OrgTypeEnum.PROVINCE_GO;
        }
        int orgType = getOperType(org);
        if (2 == orgType) {
            return InnerAuditUtil.OrgTypeEnum.PROVINCE;
        } else if (1 == orgType) {
            return InnerAuditUtil.OrgTypeEnum.CITY;
        }
        return InnerAuditUtil.OrgTypeEnum.COUNTRY;
    }

    @Override
    public PageInfo findList(InnerAuditProjectInformationVo vo) {
        InnerAuditUtil.OrgTypeEnum orgTypeEnum = getFillType(getCurrentOrgName());
        Boolean provinceFlag = new Boolean(false);
        switch (orgTypeEnum) {
            case PROVINCE:
                provinceFlag = true;
                break;
        }
        if (provinceFlag) {
            PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
            PageInfo pageInfo = new PageInfo(innerAuditProjectInformationMapper.getProvinceList(vo));
            return pageInfo;
        }

        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        PageInfo pageInfo = new PageInfo(innerAuditProjectInformationMapper.selectUnitName('%' + getCurrentOrgName() + '%'));
        return pageInfo;
    }

    @Override
    public List exportInnerAuditProjectInformationVo(InnerAuditProjectInformationVo vo) {
        List<InnerAuditProjectInformation> innerAudit = innerAuditProjectInformationMapper.selectUnitName('%' + getCurrentOrgName() + '%');
        List<InnerAuditProjectInformationExportVo> list = new ArrayList<>();
        AtomicInteger orderValCn = new AtomicInteger();
        innerAudit.stream().forEach(item -> {
            InnerAuditProjectInformationExportVo innerAuditVo = new InnerAuditProjectInformationExportVo();
            BeanUtils.copyProperties(item, innerAuditVo);
            innerAuditVo.setOrderVal(orderValCn.incrementAndGet());
            list.add(innerAuditVo);
        });
        return list;
    }

    @Override
    public List exportInnerAuditProjectInformationVoAllUnit(InnerAuditProjectInformationVo vo) {
        List<InnerAuditProjectInformation> innerAudit = innerAuditProjectInformationMapper.selectSubmit();
        List<InnerAuditProjectInformationExportVo> list = new ArrayList<>();
        // 多个单位导出一起时序号重编
        AtomicInteger orderValCn = new AtomicInteger();
        innerAudit.stream().forEach(item -> {
            InnerAuditProjectInformationExportVo innerAuditVo = new InnerAuditProjectInformationExportVo();
            BeanUtils.copyProperties(item, innerAuditVo);
            innerAuditVo.setOrderVal(orderValCn.incrementAndGet());
            list.add(innerAuditVo);
        });
        return list;
    }
}
