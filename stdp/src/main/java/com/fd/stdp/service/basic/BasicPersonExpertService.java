package com.fd.stdp.service.basic;

import java.util.List;

import com.fd.stdp.beans.basic.vo.BasicPersonVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonExpert;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 *@Description: 专家库
 *@Author: wangsh
 *@Date: 2022-01-07 10:53:32
 */
public interface BasicPersonExpertService {

	/**
	 *@Description: 保存或更新专家库
	 *@param basicPersonExpert 专家库对象
	 *@return String 专家库ID
	 *@Author: wangsh
	 */
	String saveOrUpdateBasicPersonExpert(BasicPersonExpert basicPersonExpert);
	
	/**
	 *@Description: 删除专家库
	 *@param id void 专家库ID
	 *@Author: wangsh
	 */
	void deleteBasicPersonExpert(String id);

	/**
	 *@Description: 查询专家库详情
	 *@param id
	 *@return BasicPersonExpert
	 *@Author: wangsh
	 */
	BasicPersonExpert findById(String id);

	/**
	 *@Description: 分页查询专家库
	 *@param basicPersonExpertVo
	 *@return PageInfo<BasicPersonExpert>
	 *@Author: wangsh
	 */
	PageInfo<BasicPersonExpert> findPageByQuery(BasicPersonExpertVo basicPersonExpertVo);

	/**
	 * 筛选入库
	 * @param basicPersonExpert
	 * @return
	 */
    String filterBasicPersonExpert(BasicPersonExpertVo basicPersonExpert);

	/**
	 * 推荐
	 * @param basicPersonExpert
	 * @return
	 */
	String submitBasicPersonExpert(BasicPersonExpertVo basicPersonExpert);

	/**
	 * 审核
	 * @param basicPersonExpert
	 * @return
	 */
	String auditBasicPersonExpert(BasicPersonExpertVo basicPersonExpert);

	/**
	 * 退回
	 * @param basicPersonExpert
	 * @return
	 */
	String sendBackBasicPersonExpert(BasicPersonExpertVo basicPersonExpert);

	/**
	 * 批量删除
     * @param vo
     */
    void deleteMultiBasicPersonExpert(List<String> vo);

    Object statistics(BasicPersonVo basicPersonVo);

    Object export(BasicPersonVo basicPersonVo, HttpServletResponse response);

	PageInfo<BasicPersonExpertVo> todoList(BasicPersonExpertVo basicPersonExpert);

	PageInfo<BasicPersonExpertVo> finishedList(BasicPersonExpertVo basicPersonExpert);

	/**
	 * 已完成列表
	 */
	PageInfo<BasicPersonExpert> endList(BasicPersonExpertVo basicPersonExpertVo);

    void upload(MultipartFile file);
}
