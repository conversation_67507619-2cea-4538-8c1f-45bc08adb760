package com.fd.stdp.service.audit;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.audit.InnerAuditOpen;
import com.fd.stdp.beans.audit.vo.InnerAuditOpenVo;
/**
 *@Description: 内审开放功能配置
 *@Author: wangsh
 *@Date: 2022-02-23 15:56:20
 */
public interface InnerAuditOpenService {

	/**
	 *@Description: 保存或更新内审开放功能配置
	 *@param innerAuditOpen 内审开放功能配置对象
	 *@return String 内审开放功能配置ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnerAuditOpen(InnerAuditOpenVo innerAuditOpen);
	
	/**
	 *@Description: 删除内审开放功能配置
	 *@param id void 内审开放功能配置ID
	 *@Author: wangsh
	 */
	void deleteInnerAuditOpen(String id);

	/**
	 * @Description: 批量删除内审开放功能配置
	 * @param ids
	 */
    void deleteMultiInnerAuditOpen(List<String> ids);

	/**
	 *@Description: 查询内审开放功能配置详情
	 *@param id
	 *@return InnerAuditOpen
	 *@Author: wangsh
	 */
	InnerAuditOpen findById(String id);

	/**
	 *@Description: 分页查询内审开放功能配置
	 *@param innerAuditOpenVo
	 *@return PageInfo<InnerAuditOpen>
	 *@Author: wangsh
	 */
	PageInfo<InnerAuditOpen> findPageByQuery(InnerAuditOpenVo innerAuditOpenVo);
	
	
	/**
	 * 提交
	 * @param innerAuditOpenVo
	 * @return
	 */
    String submitInnerAuditOpen(InnerAuditOpenVo innerAuditOpenVo);

	/**
	 * 审核
	 * @param innerAuditOpenVo
	 * @return
	 */
	String auditInnerAuditOpen(InnerAuditOpenVo innerAuditOpenVo);

	/**
	 * 退回
	 * @param innerAuditOpenVo
	 * @return
	 */
	String sendBackInnerAuditOpen(InnerAuditOpenVo innerAuditOpenVo);

	/**
	 * 任务书下达
	 * @param innerAuditOpenVo
	 * @return
	 */
	String releaseInnerAuditOpen(InnerAuditOpenVo innerAuditOpenVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnerAuditOpen> todoList(InnerAuditOpenVo innerAuditOpenVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnerAuditOpen> finishedList(InnerAuditOpenVo innerAuditOpenVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnerAuditOpen> endList(InnerAuditOpenVo innerAuditOpenVo);

    String open(InnerAuditOpenVo innerAuditOpen);

	String close(InnerAuditOpenVo innerAuditOpen);

    Object isOpen();
}
