package com.fd.stdp.service.talent;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectExamine;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectExamineVo;
/**
 *@Description: 学科带头人考核表
 *@Author: wangsh
 *@Date: 2022-02-14 10:05:09
 */
public interface TalentLeaderSubjectExamineService {

	/**
	 *@Description: 保存或更新学科带头人考核表
	 *@param talentLeaderSubjectExamine 学科带头人考核表对象
	 *@return String 学科带头人考核表ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTalentLeaderSubjectExamine(TalentLeaderSubjectExamineVo talentLeaderSubjectExamine);
	
	/**
	 *@Description: 删除学科带头人考核表
	 *@param id void 学科带头人考核表ID
	 *@Author: wangsh
	 */
	void deleteTalentLeaderSubjectExamine(String id);

	/**
	 * @Description: 批量删除学科带头人考核表
	 * @param ids
	 */
    void deleteMultiTalentLeaderSubjectExamine(List<String> ids);

	/**
	 *@Description: 查询学科带头人考核表详情
	 *@param id
	 *@return TalentLeaderSubjectExamine
	 *@Author: wangsh
	 */
	TalentLeaderSubjectExamine findById(String id);

	/**
	 *@Description: 分页查询学科带头人考核表
	 *@param talentLeaderSubjectExamineVo
	 *@return PageInfo<TalentLeaderSubjectExamine>
	 *@Author: wangsh
	 */
	PageInfo<TalentLeaderSubjectExamine> findPageByQuery(TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo);
	
	
	/**
	 * 提交
	 * @param talentLeaderSubjectExamineVo
	 * @return
	 */
    String submitTalentLeaderSubjectExamine(TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo);

	/**
	 * 审核
	 * @param talentLeaderSubjectExamineVo
	 * @return
	 */
	String auditTalentLeaderSubjectExamine(TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo);

	/**
	 * 退回
	 * @param talentLeaderSubjectExamineVo
	 * @return
	 */
	String sendBackTalentLeaderSubjectExamine(TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo);

	/**
	 * 任务书下达
	 * @param talentLeaderSubjectExamineVo
	 * @return
	 */
	String releaseTalentLeaderSubjectExamine(TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo);
	
	/**
	 * 待办列表
	*/
    PageInfo<TalentLeaderSubjectExamine> todoList(TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo);

	/**
	 * 已办列表
	*/
	PageInfo<TalentLeaderSubjectExamine> finishedList(TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo);
}
