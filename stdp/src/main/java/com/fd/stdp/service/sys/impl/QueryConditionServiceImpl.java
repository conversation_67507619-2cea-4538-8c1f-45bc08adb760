package com.fd.stdp.service.sys.impl;

import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.sys.SysRole;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.dao.basic.BasicScienceOrgMapper;
import com.fd.stdp.service.sys.QueryConditionService;
import com.fd.stdp.util.AppUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.stream.Collectors;

import static com.fd.stdp.common.BaseController.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/6/8 15:13
 */
@Service
@Transactional(readOnly = true)
public class QueryConditionServiceImpl implements QueryConditionService {

    @Autowired
    private BasicScienceOrgMapper basicScienceOrgMapper;

    @Override
    public void querySelfDataOlny(Example.Criteria criteria, String fieldName) {
        List<SysRole> userRoleList = getUserRoleList();
        List<String> roleList = userRoleList.stream().map(role->role.getRoleCode() + "@ROLE").distinct().collect(Collectors.toList());
        // 省局有全部权限
        if(hasRole(roleList, AssigneeConstant.DEPT_PROVINCE_ROLE)) {
            return;
        }
        // 市局查询市局以下
        if(hasRole(roleList, AssigneeConstant.DEPT_CITY_ROLE)) {
            String areaCode = getLoginUser().getAreaCode();
            Example orgExample = new Example(BasicScienceOrg.class);
            Example.Criteria orgCriteria = orgExample.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
            orgCriteria.andEqualTo("areaCode", areaCode);
            List<String> orgIds = basicScienceOrgMapper.selectByExample(orgExample).stream().map(b->b.getId()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(orgIds)){
                criteria.andCondition("0 = 1");
            } else {
                criteria.andIn(fieldName, orgIds);
            }
            return;
        }
        // 县局查询县以下
        if(hasRole(roleList, AssigneeConstant.DEPT_COUNTY_ROLE)) {
            String areaCode = getLoginUser().getAreaCode();
            Example orgExample = new Example(BasicScienceOrg.class);
            Example.Criteria orgCriteria = orgExample.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
            orgCriteria.andLike("areaCode", areaCode.substring(0,4) + "%");
            List<String> orgIds = basicScienceOrgMapper.selectByExample(orgExample).stream().map(b->b.getId()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(orgIds)){
                criteria.andCondition("0 = 1");
            } else {
                criteria.andIn(fieldName, orgIds);
            }
            return;
        }
        // 机构查询自己机构
        criteria.andEqualTo(fieldName, getCurrentScienceOrgId());
    }

    public boolean hasRole(List<String> roleList, String role) {
        for (String r:roleList) {
            if(StringUtils.equals(r, role)){
                return true;
            }
        }
        return false;
    }
}
