package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectApplyProgress;
import com.fd.stdp.beans.project.vo.ProjectApplyProgressVo;

/**
 * 项目计划进度安排Service接口
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
public interface ProjectApplyProgressService {
    /**
     * @param projectApplyProgress 项目计划进度安排对象
     * @return String 项目计划进度安排ID
     * @Description: 保存或更新项目计划进度安排
     * @Author: yujianfei
     */
    String saveOrUpdateProjectApplyProgress(ProjectApplyProgress projectApplyProgress);

    /**
     * @param ids void 项目计划进度安排ID
     * @Description: 删除项目计划进度安排
     * @Author: yujianfei
     */
    void deleteProjectApplyProgress(List<String> ids);

    /**
     * @param id
     * @return ProjectApplyProgress
     * @Description: 查询项目计划进度安排详情
     * @Author: yujianfei
     */
    ProjectApplyProgress findById(String id);

    /**
     * @param projectApplyProgressVo
     * @return PageInfo<ProjectApplyProgress>
     * @Description: 分页查询项目计划进度安排
     * @Author: yujianfei
     */
    PageInfo<ProjectApplyProgress> findPageByQuery(ProjectApplyProgressVo projectApplyProgressVo);
}
