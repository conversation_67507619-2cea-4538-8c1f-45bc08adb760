package com.fd.stdp.service.basic.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonRecord;
import com.fd.stdp.beans.basic.vo.BasicPersonRecordVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicPersonRecordMapper;
import com.fd.stdp.service.basic.BasicPersonRecordService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 机构人员报备
 *@Author: wangsh
 *@Date: 2022-02-15 16:58:00
 */
public class BasicPersonRecordServiceImpl extends BaseServiceImpl<BasicPersonRecordMapper, BasicPersonRecord> implements BasicPersonRecordService{

	public static final Logger logger = LoggerFactory.getLogger(BasicPersonRecordServiceImpl.class);
	
	@Autowired
	private BasicPersonRecordMapper basicPersonRecordMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新机构人员报备
	 *@param basicPersonRecord 机构人员报备对象
	 *@return String 机构人员报备ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateBasicPersonRecord(BasicPersonRecordVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			basicPersonRecordMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			basicPersonRecordMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除机构人员报备
	 *@param id void 机构人员报备ID
	 *@Author: wangsh
	 */
	public void deleteBasicPersonRecord(String id) {
		//TODO 做判断后方能执行删除
		BasicPersonRecord basicPersonRecord=basicPersonRecordMapper.selectByPrimaryKey(id);
		if(basicPersonRecord==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		BasicPersonRecord tembasicPersonRecord=new BasicPersonRecord();
		tembasicPersonRecord.setYn(CommonConstant.FLAG_NO);
		tembasicPersonRecord.setId(basicPersonRecord.getId());
		basicPersonRecordMapper.updateByPrimaryKeySelective(tembasicPersonRecord);
	}

    /**
     * @Description: 批量删除机构人员报备
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiBasicPersonRecord(List<String> ids) {
		ids.stream().forEach(id-> this.deleteBasicPersonRecord(id));
	}

	@Override
	/**
	 *@Description: 查询机构人员报备详情
	 *@param id
	 *@return BasicPersonRecord
	 *@Author: wangsh
	 */
	public BasicPersonRecord findById(String id) {
		return basicPersonRecordMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询机构人员报备
	 *@param basicPersonRecordVo
	 *@return PageInfo<BasicPersonRecord>
	 *@Author: wangsh
	 */
	public PageInfo<BasicPersonRecord> findPageByQuery(BasicPersonRecordVo basicPersonRecordVo) {
		PageHelper.startPage(basicPersonRecordVo.getPageNum(),basicPersonRecordVo.getPageSize());
		Example example=new Example(BasicPersonRecord.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(basicPersonRecordVo.getName())){
		//	criteria.andEqualTo(basicPersonRecordVo.getName());
		//}
		List<BasicPersonRecord> basicPersonRecordList=basicPersonRecordMapper.selectByExample(example);
		return new PageInfo<BasicPersonRecord>(basicPersonRecordList);
	}

	@Override
	public Object findIsRecord(String currentScienceOrgName) {
		Example example = new Example(BasicPersonRecord.class);
		example.createCriteria().andEqualTo("yn", 1).andEqualTo("orgName", currentScienceOrgName);
		return this.mapper.selectByExample(example);
	}

}
