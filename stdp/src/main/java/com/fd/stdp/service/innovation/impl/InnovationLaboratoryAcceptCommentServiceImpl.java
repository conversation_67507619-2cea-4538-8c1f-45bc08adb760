package com.fd.stdp.service.innovation.impl;

import java.util.List;

import com.fd.stdp.beans.basic.BasicGradeLinked;
import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import com.fd.stdp.dao.basic.BasicGradeLinkedMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationLaboratoryAcceptComment;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryAcceptCommentVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationLaboratoryAcceptCommentMapper;
import com.fd.stdp.service.innovation.InnovationLaboratoryAcceptCommentService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 重点实验室验收评审/变更评审/中期评估
 *@Author: wangsh
 *@Date: 2022-02-20 13:11:44
 */
public class InnovationLaboratoryAcceptCommentServiceImpl extends BaseServiceImpl<InnovationLaboratoryAcceptCommentMapper, InnovationLaboratoryAcceptComment> implements InnovationLaboratoryAcceptCommentService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationLaboratoryAcceptCommentServiceImpl.class);
	
	@Autowired
	private InnovationLaboratoryAcceptCommentMapper innovationLaboratoryAcceptCommentMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	@Autowired
	private BasicGradeLinkedMapper gradeLinkedMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新重点实验室验收评审/变更评审/中期评估
	 *@param innovationLaboratoryAcceptComment 重点实验室验收评审/变更评审/中期评估对象
	 *@return String 重点实验室验收评审/变更评审/中期评估ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationLaboratoryAcceptComment(InnovationLaboratoryAcceptCommentVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			innovationLaboratoryAcceptCommentMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationLaboratoryAcceptCommentMapper.updateByPrimaryKeySelective(vo);
		}

		/**
		 * 附件
		 */
		if(!CollectionUtils.isEmpty(vo.getFiles())) {
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}

		// flowCommonService.doFlowStart(FlowableConstant.FLOW_INNOVATION_LABORATORY_ACCEPT, vo, this.mapper, "创新载体验收流程开始");

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除重点实验室验收评审/变更评审/中期评估
	 *@param id void 重点实验室验收评审/变更评审/中期评估ID
	 *@Author: wangsh
	 */
	public void deleteInnovationLaboratoryAcceptComment(String id) {
		//TODO 做判断后方能执行删除
		InnovationLaboratoryAcceptComment innovationLaboratoryAcceptComment=innovationLaboratoryAcceptCommentMapper.selectByPrimaryKey(id);
		if(innovationLaboratoryAcceptComment==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationLaboratoryAcceptComment teminnovationLaboratoryAcceptComment=new InnovationLaboratoryAcceptComment();
		teminnovationLaboratoryAcceptComment.setYn(CommonConstant.FLAG_NO);
		teminnovationLaboratoryAcceptComment.setId(innovationLaboratoryAcceptComment.getId());
		innovationLaboratoryAcceptCommentMapper.updateByPrimaryKeySelective(teminnovationLaboratoryAcceptComment);
	}

    /**
     * @Description: 批量删除重点实验室验收评审/变更评审/中期评估
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationLaboratoryAcceptComment(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationLaboratoryAcceptComment(id));
	}

	@Override
	/**
	 *@Description: 查询重点实验室验收评审/变更评审/中期评估详情
	 *@param id
	 *@return InnovationLaboratoryAcceptComment
	 *@Author: wangsh
	 */
	public InnovationLaboratoryAcceptComment findById(String id) {
		InnovationLaboratoryAcceptComment acceptComment = innovationLaboratoryAcceptCommentMapper.selectByPrimaryKey(id);
		InnovationLaboratoryAcceptCommentVo acceptCommentVo = new InnovationLaboratoryAcceptCommentVo();
		BeanUtils.copyProperties(acceptComment, acceptCommentVo);

		/**
		 * 附件
		 */
		acceptCommentVo.setFiles(basicFileAppendixService.findByFormId(id));
		return acceptCommentVo;
	}

	@Override
	/**
	 *@Description: 分页查询重点实验室验收评审/变更评审/中期评估
	 *@param innovationLaboratoryAcceptCommentVo
	 *@return PageInfo<InnovationLaboratoryAcceptComment>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationLaboratoryAcceptComment> findPageByQuery(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo) {
		PageHelper.startPage(innovationLaboratoryAcceptCommentVo.getPageNum(),innovationLaboratoryAcceptCommentVo.getPageSize());
		Example example=new Example(InnovationLaboratoryAcceptComment.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationLaboratoryAcceptCommentVo.getName())){
		//	criteria.andEqualTo(innovationLaboratoryAcceptCommentVo.getName());
		//}
		List<InnovationLaboratoryAcceptComment> innovationLaboratoryAcceptCommentList=innovationLaboratoryAcceptCommentMapper.selectByExample(example);
		return new PageInfo<InnovationLaboratoryAcceptComment>(innovationLaboratoryAcceptCommentList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationLaboratoryAcceptComment(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo) {
		String id = this.saveOrUpdateInnovationLaboratoryAcceptComment(innovationLaboratoryAcceptCommentVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_INNOVATION_LABORATORY_ACCEPT, innovationLaboratoryAcceptCommentVo, this.mapper,
				StringUtils.isNotBlank(innovationLaboratoryAcceptCommentVo.getAuditAdvice())?innovationLaboratoryAcceptCommentVo.getAuditAdvice():"提交重点实验室验收评审/变更评审/中期评估");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationLaboratoryAcceptComment(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo) {
		flowCommonService.doFlowStepAudit(innovationLaboratoryAcceptCommentVo, this.mapper
				, StringUtils.isNotBlank(innovationLaboratoryAcceptCommentVo.getAuditAdvice()) ? innovationLaboratoryAcceptCommentVo.getAuditAdvice() : "重点实验室验收评审/变更评审/中期评估审核通过"
				, FlowStatusEnum.EXPERTS_GRADE.getCode());
		return innovationLaboratoryAcceptCommentVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationLaboratoryAcceptComment(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo) {
		flowCommonService.doFlowStepSendBack(innovationLaboratoryAcceptCommentVo, this.mapper
				, StringUtils.isNotBlank(innovationLaboratoryAcceptCommentVo.getAuditAdvice())?innovationLaboratoryAcceptCommentVo.getAuditAdvice():"重点实验室验收评审/变更评审/中期评估退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationLaboratoryAcceptComment(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo) {
		flowCommonService.doCompleteTask(innovationLaboratoryAcceptCommentVo, this.mapper
				, "重点实验室验收评审/变更评审/中期评估任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnovationLaboratoryAcceptComment> todoList(InnovationLaboratoryAcceptCommentVo vo) {

		Example example = new Example(InnovationLaboratoryAcceptComment.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationLaboratoryAcceptComment> finishedList(InnovationLaboratoryAcceptCommentVo vo) {
		Example example = new Example(InnovationLaboratoryAcceptComment.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnovationLaboratoryAcceptComment> endList(InnovationLaboratoryAcceptCommentVo vo) {
        Example example = new Example(InnovationLaboratoryAcceptComment.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	@Override
	public String expertSubmitInnovationLaboratoryAcceptComment(BasicGradeLinkedVo vo) {
		gradeLinkedMapper.updateByPrimaryKeySelective(vo);
		Example example = new Example(BasicGradeLinked.class);
		example.createCriteria().andEqualTo("formId", vo.getFormId()).andEqualTo("yn", 1).andIsNull("resultType");
		if(CollectionUtils.isEmpty(gradeLinkedMapper.selectByExample(example))){
			InnovationLaboratoryAcceptCommentVo acceptCommentVo = (InnovationLaboratoryAcceptCommentVo) findById(vo.getFormId());
			flowCommonService.doCompleteTask(acceptCommentVo, this.mapper, "全部专家完成审核", FlowStatusEnum.PROJECT_RELEASE.getCode(), null);
		}

		return null;
	}

	private Criteria getCriteria(InnovationLaboratoryAcceptComment vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getLaboratory())){
			criteria.andLike("laboratory", "%" + vo.getLaboratory()+ "%");
		}
		return criteria;
	}
}
