package com.fd.stdp.service.talent.impl;

import java.util.List;

import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.flowable.FlowCommonService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectContribute;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectContributeVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.talent.TalentLeaderSubjectContributeMapper;
import com.fd.stdp.service.talent.TalentLeaderSubjectContributeService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 学科带头人建设任务书
 *@Author: wangsh
 *@Date: 2022-02-14 10:05:19
 */
public class TalentLeaderSubjectContributeServiceImpl extends BaseServiceImpl<TalentLeaderSubjectContributeMapper, TalentLeaderSubjectContribute> implements TalentLeaderSubjectContributeService{

	public static final Logger logger = LoggerFactory.getLogger(TalentLeaderSubjectContributeServiceImpl.class);
	
	@Autowired
	private TalentLeaderSubjectContributeMapper talentLeaderSubjectContributeMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新学科带头人建设任务书
	 *@param talentLeaderSubjectContribute 学科带头人建设任务书对象
	 *@return String 学科带头人建设任务书ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTalentLeaderSubjectContribute(TalentLeaderSubjectContributeVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			talentLeaderSubjectContributeMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			talentLeaderSubjectContributeMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除学科带头人建设任务书
	 *@param id void 学科带头人建设任务书ID
	 *@Author: wangsh
	 */
	public void deleteTalentLeaderSubjectContribute(String id) {
		//TODO 做判断后方能执行删除
		TalentLeaderSubjectContribute talentLeaderSubjectContribute=talentLeaderSubjectContributeMapper.selectByPrimaryKey(id);
		if(talentLeaderSubjectContribute==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TalentLeaderSubjectContribute temtalentLeaderSubjectContribute=new TalentLeaderSubjectContribute();
		temtalentLeaderSubjectContribute.setYn(CommonConstant.FLAG_NO);
		temtalentLeaderSubjectContribute.setId(talentLeaderSubjectContribute.getId());
		talentLeaderSubjectContributeMapper.updateByPrimaryKeySelective(temtalentLeaderSubjectContribute);
	}

    /**
     * @Description: 批量删除学科带头人建设任务书
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTalentLeaderSubjectContribute(List<String> ids) {
		ids.stream().forEach(id-> this.deleteTalentLeaderSubjectContribute(id));
	}

	@Override
	/**
	 *@Description: 查询学科带头人建设任务书详情
	 *@param id
	 *@return TalentLeaderSubjectContribute
	 *@Author: wangsh
	 */
	public TalentLeaderSubjectContribute findById(String id) {
		return talentLeaderSubjectContributeMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询学科带头人建设任务书
	 *@param talentLeaderSubjectContributeVo
	 *@return PageInfo<TalentLeaderSubjectContribute>
	 *@Author: wangsh
	 */
	public PageInfo<TalentLeaderSubjectContribute> findPageByQuery(TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo) {
		PageHelper.startPage(talentLeaderSubjectContributeVo.getPageNum(),talentLeaderSubjectContributeVo.getPageSize());
		Example example=new Example(TalentLeaderSubjectContribute.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(talentLeaderSubjectContributeVo.getName())){
		//	criteria.andEqualTo(talentLeaderSubjectContributeVo.getName());
		//}
		List<TalentLeaderSubjectContribute> talentLeaderSubjectContributeList=talentLeaderSubjectContributeMapper.selectByExample(example);
		return new PageInfo<TalentLeaderSubjectContribute>(talentLeaderSubjectContributeList);
	}

	@Override
	public String submitTalentLeaderSubjectContribute(TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo) {
		String id = this.saveOrUpdateTalentLeaderSubjectContribute(talentLeaderSubjectContributeVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_INNOVATION_APPLY, talentLeaderSubjectContributeVo, this.mapper,
				StringUtils.isNotBlank(talentLeaderSubjectContributeVo.getAuditAdvice())?talentLeaderSubjectContributeVo.getAuditAdvice():"提交学科带头人建设任务书");
		return id;
	}

	@Override
	public String auditTalentLeaderSubjectContribute(TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo) {
		flowCommonService.doFlowStepAudit(talentLeaderSubjectContributeVo, this.mapper
				, StringUtils.isNotBlank(talentLeaderSubjectContributeVo.getAuditAdvice()) ? talentLeaderSubjectContributeVo.getAuditAdvice() : "学科带头人建设任务书审核通过"
				, FlowStatusEnum.CHOOSE_EXPERTS.getCode());
		return talentLeaderSubjectContributeVo.getId();
	}

	@Override
	public String sendBackTalentLeaderSubjectContribute(TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo) {
		flowCommonService.doFlowStepSendBack(talentLeaderSubjectContributeVo, this.mapper
				, StringUtils.isNotBlank(talentLeaderSubjectContributeVo.getAuditAdvice())?talentLeaderSubjectContributeVo.getAuditAdvice():"学科带头人建设任务书退回"
				, false
		);
		return null;
	}

	@Override
	public String releaseTalentLeaderSubjectContribute(TalentLeaderSubjectContributeVo talentLeaderSubjectContributeVo) {
		flowCommonService.doCompleteTask(talentLeaderSubjectContributeVo, this.mapper
				, "学科带头人建设任务书任务书下达完成"
				, FlowStatusEnum.PROJECT_RELEASE.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<TalentLeaderSubjectContribute> todoList(TalentLeaderSubjectContributeVo vo) {

		Example example = new Example(TalentLeaderSubjectContribute.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TalentLeaderSubjectContribute> finishedList(TalentLeaderSubjectContributeVo vo) {
		Example example = new Example(TalentLeaderSubjectContribute.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	private Criteria getCriteria(TalentLeaderSubjectContribute vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
		return criteria;
	}
}
