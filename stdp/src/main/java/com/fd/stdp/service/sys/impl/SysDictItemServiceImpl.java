package com.fd.stdp.service.sys.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.fd.stdp.beans.sys.SysDictItem;
import com.fd.stdp.beans.sys.vo.SysDictItemVo;
import com.fd.stdp.beans.sys.vo.SysDictVo;
import com.fd.stdp.beans.sys.vo.TreeDropVo;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.redis.RedisUtil;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.constant.RedisConstant;
import com.fd.stdp.dao.sys.SysDictItemMapper;
import com.fd.stdp.service.sys.SysDictItemService;
import com.fd.stdp.util.UUIDUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 *@Description: 系统数据字典分类
 *@Author: linqiang
 *@Date: 2020-07-05 15:14:51
 */
@Service
@Transactional(readOnly = true)
public class SysDictItemServiceImpl extends BaseServiceImpl<SysDictItemMapper, SysDictItem>
        implements SysDictItemService {

    public static final Logger logger = LoggerFactory.getLogger(SysDictItemServiceImpl.class);

    @Autowired
    private SysDictItemMapper basicDictItemMapper;

    @Autowired
    private RedisUtil redisUtil;

    /**
     *@Description: 查询字典列表
     *@param basicDictItem 系统数据字典分类对象
     *@return String 系统数据字典分类ID
     *@Author: linqiang
     */
    @Override
    public List<SysDictItem> listItem(SysDictItem dictItem, boolean userType) {
        Example example = new Example(SysDictItem.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        if (userType != true) {
            	//或者是公共字典
                criteria.andEqualTo("isOpen", CommonConstant.DICT_IS_OPEN);
        }
        example.orderBy("orderVal").asc();
        List<SysDictItem> dictItemList = basicDictItemMapper.selectByExample(example);
        return dictItemList;
    }

    /**
     *@Description: 新增系统数据字典分类
     *@param basicDictItem 系统数据字典分类对象
     *@return String 系统数据字典分类ID
     *@Author: linqiang
     */
    @Override
    @Transactional(readOnly = false)
    public void addBasicDictItem(SysDictItem basicDictItem, SysDictVo dictVo) {
        if (basicDictItem == null) {
            throw new ServiceException("添加字典内容不能为空");
        }
        String dictId = basicDictItem.getId();
        String dictCode = basicDictItem.getItemCode();
        //添加前，判断编码是否重复
        Example example = new Example(SysDictItem.class);
        example.createCriteria().andEqualTo("itemCode", dictCode).andEqualTo("yn", CommonConstant.FLAG_YES);
        int count = this.mapper.selectCountByExample(example);
        if (count > 0) {
            throw new ServiceException("编码重复");
        }
        // 字典dictId为空或者为0是叶子节点
        if (StringUtils.isEmpty(dictId) || CommonConstant.ROOT.equals(dictId)) {
            basicDictItem.setIsLeaf(1);
        }
        Integer status = basicDictItem.getStatus();
        basicDictItem.setStatusTxt(status.equals(1) ? "启用" : "禁用");

        String parentCode = basicDictItem.getParentCode();
        SysDictItem parentDict = null;
        // parentCode为空或者为0设置父Id为0
        if (StringUtils.isEmpty(parentCode) || CommonConstant.ROOT.equals(parentCode)) {
            parentCode = CommonConstant.ROOT;
            basicDictItem.setParentCode(parentCode);
        } else {
        	 Example exampleparentCode = new Example(SysDictItem.class);
        	 exampleparentCode.createCriteria().andEqualTo("itemCode", parentCode).andEqualTo("yn", CommonConstant.FLAG_YES);
            parentDict = this.basicDictItemMapper.selectOneByExample(exampleparentCode);
            if (parentDict == null) {
                throw new ServiceException("父结点不存在");
            }
            // 将父节点更改为不是叶子节点
            parentDict.setIsLeaf(0);
            this.basicDictItemMapper.updateByPrimaryKeySelective(parentDict);
        }
        if (parentDict != null) {
            redisUtil.del(RedisConstant.DICT_TYPE_CODE + parentDict.getItemCode());
        }
        // 字典dictId为空或者为0是叶子节点
        if (StringUtils.isEmpty(dictId) || CommonConstant.ROOT.equals(dictId)) {
            // new 一个dictId和创建时间，插入数据到库中
            basicDictItem.setId(UUIDUtils.getUUID());
            basicDictItem.setCreateTime(new Date());
            this.insertSelective(basicDictItem);
        } else {
            this.basicDictItemMapper.updateByPrimaryKeySelective(basicDictItem);
        }
    }

    /**
     *@Description: 更新系统数据字典分类
     *@param basicDictItem 系统数据字典分类对象
     *@return String 系统数据字典分类ID
     *@Author: linqiang
     */
    @Override
    @Transactional(readOnly = false)
    public String updateItemById(SysDictItem dictItem, boolean userType) {
        // 校验CODE重复
        if (!checkCode(dictItem.getItemCode(), dictItem.getId())) {
            throw new ServiceException("字典编码已经存在");
        }
        Example exampleCode = new Example(SysDictItem.class);
        exampleCode.createCriteria().andEqualTo("id", dictItem.getId()).andEqualTo("yn", CommonConstant.FLAG_YES);
        SysDictItem dbDict = basicDictItemMapper.selectOneByExample(exampleCode);
        if (dbDict == null) {
            throw new ServiceException("当前字典不存在");
        }
        dbDict.setItemCode(dictItem.getItemCode());
        dbDict.setItemValue(dictItem.getItemValue());
        dbDict.setItemDesc(dictItem.getItemDesc());
        dbDict.setStatus(dictItem.getStatus());
        dbDict.setStatusTxt(CommonConstant.FLAG_YES.equals(dictItem.getStatus()) ? "启用" : "禁用");
        dbDict.setOrderVal(dictItem.getOrderVal());
        if (!StringUtils.isEmpty(dbDict.getParentCode()) && !CommonConstant.ROOT.equals(dbDict.getParentCode())) {

        	 Example exampleParentCode = new Example(SysDictItem.class);
        	 exampleParentCode.createCriteria().andEqualTo("itemCode", dbDict.getParentCode()).andEqualTo("yn", CommonConstant.FLAG_YES);
             SysDictItem child = basicDictItemMapper.selectOneByExample(exampleParentCode);

            redisUtil.del(RedisConstant.DICT_TYPE_CODE + child.getItemCode());
        }
        basicDictItemMapper.updateByPrimaryKeySelective(dbDict);
        return dbDict.getId();
    }

    /**
     * @Description 校验CODE重复
     * @param code
     * @param id
     * @return boolean
     * <AUTHOR>
     */
    private boolean checkCode(String code, String id) {
        Example example = new Example(SysDictItem.class);
        example.createCriteria().andEqualTo("itemCode", code);
        List<SysDictItem> list = basicDictItemMapper.selectByExample(example);
        if (list.isEmpty()) {
            return true;
        }
        // 编辑
        // 判断某字符串是否不为空且长度不为0且不由空白符(whitespace)构成，等于!isBlank(String str)
        if (!StringUtils.isEmpty(id)) {
            for (SysDictItem bean : list) {
                if (!bean.getId().equals(id)) {
                    return false;
                }
            }
        } else {
            return false;
        }
        return true;
    }

    /**
     * @Description: 删除数据字典分类
     * @param id 系统数据字典分类ID
     * @return void
     * @Author: linqiang
     */
    @Override
    @Transactional(readOnly = false)
    public void deleteDictById(String id, boolean whetherAdmin) {
        Example example = new Example(SysDictItem.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("parentCode", id);
        //查询是否存在子菜单
        int count = this.mapper.selectCountByExample(example);
        if (count > 0) {
            throw new ServiceException("存在子节点，操作失败");
        }
        //查询字典是否存在
        Example exampleCode = new Example(SysDictItem.class);
        exampleCode.createCriteria().andEqualTo("id", id).andEqualTo("yn", CommonConstant.FLAG_YES);
        SysDictItem dbSysDict = basicDictItemMapper.selectOneByExample(exampleCode);
        if (dbSysDict == null) {
            throw new ServiceException("当前字典不存在，操作失败");
        }
        String parentCode = dbSysDict.getParentCode();
        // 如果parentCode不为空并且不为0
        if (!StringUtils.isEmpty(parentCode) && !CommonConstant.ROOT.equals(parentCode)) {
            Example parentExample = new Example(SysDictItem.class);
            // 根据parentCode查询该父节点是否还存在
            parentExample.createCriteria().andEqualTo("parentCode", parentCode);
            int childCount = this.mapper.selectCountByExample(parentExample);
            if (childCount <= 1) {
            	Example exampleparentCode = new Example(SysDictItem.class);
            	exampleparentCode.createCriteria().andEqualTo("itemCode", parentCode).andEqualTo("yn", CommonConstant.FLAG_YES);
                SysDictItem parentDictItem = basicDictItemMapper.selectOneByExample(exampleparentCode);
                if (parentDictItem != null) {
                    parentDictItem.setIsLeaf(1);
                    if (parentDictItem.getItemCode() != null) {
                        redisUtil.del(RedisConstant.DICT_TYPE_CODE + parentDictItem.getItemCode());
                    }
                    this.mapper.updateByPrimaryKeySelective(parentDictItem);
                }
            }
        }

        Example exampleDelCode = new Example(SysDictItem.class);
        exampleDelCode.createCriteria().andEqualTo("id", id).andEqualTo("yn", CommonConstant.FLAG_YES);
        this.mapper.deleteByExample(exampleDelCode);
    }

    /**
     * 根据字典code得到字典的值
     */
    @Override
    public SysDictItem findDictItem(String code) {
        Example example = new Example(SysDictItem.class);
        example.createCriteria().andEqualTo("itemCode", code).andEqualTo("yn", CommonConstant.FLAG_YES);
        return basicDictItemMapper.selectOneByExample(example);
    }

    /**
     * 根据字典CODE得到下级结点列表一级
     */
    @Override
    @SuppressWarnings("unchecked")
    public List<SysDictItem> findDictItemByDictType(String typeCode) {
        List<SysDictItem> list;
        list = (List<SysDictItem>) redisUtil.get(RedisConstant.DICT_TYPE_CODE + typeCode);
        if (CollectionUtils.isEmpty(list)) {
            list = basicDictItemMapper.findDictItemByDictType(typeCode);
            redisUtil.set(RedisConstant.DICT_TYPE_CODE + typeCode, list, RedisConstant.REDIS_EXPIRE_ONE_DAY);
        }
        return list;
    }

    /**
     *@Description: 保存或更新系统数据字典分类
     *@param basicDictItem 系统数据字典分类对象
     *@return String 系统数据字典分类ID
     *@Author: linqiang
     */
    @Override
    @Transactional(readOnly = false)
    public String saveOrUpdateBasicDictItem(SysDictItem basicDictItem) {
        if (basicDictItem == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(basicDictItem.getId())) {
            //新增
            basicDictItem.setId(UUIDUtils.getUUID());
            basicDictItemMapper.insertSelective(basicDictItem);
        } else {
            //避免页面传入修改
            basicDictItem.setYn(null);
            basicDictItemMapper.updateByPrimaryKeySelective(basicDictItem);
        }
        return basicDictItem.getId();
    }


    /**
     *@Description: 查询系统数据字典分类详情
     *@param id
     *@return BasicDictItem
     *@Author: linqiang
     */
    @Override
    public SysDictItem findById(String id) {
    	Example example = new Example(SysDictItem.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("itemCode", id);

        return basicDictItemMapper.selectOneByExample(example);
    }

    /**
     *@Description: 分页查询系统数据字典分类
     *@param basicDictItemVo
     *@return PageInfo<BasicDictItem>
     *@Author: linqiang
     */
    @Override
    public PageInfo<SysDictItem> findPageByQuery(SysDictItemVo basicDictItemVo) {
        PageHelper.startPage(basicDictItemVo.getPageNum(), basicDictItemVo.getPageSize());
        Example example = new Example(SysDictItem.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(basicDictItemVo.getName())){
        //	criteria.andEqualTo(basicDictItemVo.getName());
        //}
        List<SysDictItem> basicDictItemList = basicDictItemMapper.selectByExample(example);
        return new PageInfo<SysDictItem>(basicDictItemList);
    }

	@Override
	/**
	 * 得到字典 treeDrop
	 */
	@Transactional(readOnly = false)
	public List<TreeDropVo> getTreeDrop(SysDictItem dictItem) {
		 List<TreeDropVo> treeDropList=basicDictItemMapper.getTreeDrop(dictItem);
		return treeDropList;
	}

	@Override
	public PageInfo<SysDictItem> findDictItemByCodes(SysDictItemVo vo) {
		PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
		List<String> codeList = Arrays.asList(vo.getCodes().split(","));
		vo.setCodeList(codeList);
		List<SysDictItem> dictList = basicDictItemMapper.findDictItemByLikeDictType(vo);
		return new PageInfo<SysDictItem>(dictList);
	}

}
