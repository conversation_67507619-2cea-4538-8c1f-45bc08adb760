package com.fd.stdp.service.sys;

import com.fd.stdp.beans.sys.SysFileInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

public interface FileService {

    /**
     * 上传文件
     *
     * @param file
     * @return
     * @throws Exception
     */
    SysFileInfo upload(MultipartFile file) throws Exception;

    /**
     * 上传文件
     * 
     * @param file
     * @return
     * @throws Exception
     */
    SysFileInfo upload(MultipartFile file, SysFileInfo fileInfo) throws Exception;

    /**
     * 上传文件
     * 
     * @param file
     * @return
     * @throws Exception
     */
    SysFileInfo upload(MultipartFile file, SysFileInfo fileInfo, String type) throws Exception;

    /**
     * 删除文件
     *
     * @param fileInfo
     */
    void delete(SysFileInfo fileInfo);

    /**
     * 文件下载
     * 
     * @param key
     * @param response
     */
    void down(String key, HttpServletResponse response);
    
    boolean copyFile(String source,String taraget) ;
}
