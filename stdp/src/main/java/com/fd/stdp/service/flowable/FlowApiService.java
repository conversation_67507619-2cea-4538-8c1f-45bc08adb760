package com.fd.stdp.service.flowable;

import java.util.List;
import java.util.Map;

import com.fd.stdp.beans.flow.FlowNodeVo;
import org.flowable.bpmn.model.UserTask;

import com.fd.stdp.beans.flowable.FlowTaskDto;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.flowable.FlowViewerDto;
import com.fd.stdp.beans.flowable.vo.FlwTaskBukeyVo;
import com.github.pagehelper.PageInfo;

/**
 * 工作人流操作 对外调用api 所有业务流程都调用此类来操作flowable工作流
 * 发现没有对应的方法请自行在此添加操作，不要在其它地方操作
 * <AUTHOR>
 *
 */
public interface FlowApiService {
	
	/**
	 * 启动一个流程    使用流程KEY进行启动
	 *  processInstanceKey 流程定义KEY
	 *  businessKey 业务唯一值
	 *  userId 流程发起人
	 *  userName 姓名
	 * @param flowTaskVo
	 * @return taskId
	 */
	String startProcessInstanceByKey(FlowTaskVo flowTaskVo);
	
	/**
	 * 完成当前任务
	 * taskId 任务id
	 * instanceid 流程实例id
	 * comment 说明
	 * userId 下级办理人
	 * values 流程变量
	 * @param flowTaskVo
	 */
	void completeTask(FlowTaskVo flowTaskVo);
	/**
	 * 完成当前任务
	 * commentType 指定的完成类型
	 */
	void completeTask(FlowTaskVo flowTaskVo, String commentType);
	
	/**
	 * 撤回流程 TODO 未完成
	 * InstanceId 流程实例id
	 * userid 用户id
	 * @param flowTaskVo
	 */
	void revokeProcess(FlowTaskVo flowTaskVo);
	/**
	 * 取消流程   
	 * instanceId 流程实例id
	 * userId 用户id
	 * @param flowTaskVo
	 */
	void stopProcess(FlowTaskVo flowTaskVo);
	/**
	 * 驳回任务
	 *  taskId 任务id
	 * Comment 驳回意见
	 * @param flowTaskVo
	 */
	void taskReject(FlowTaskVo flowTaskVo);
	/**
	 * 退回任务
	 * taskId 任务id
	 * TargetKey 目标跳转节点key
	 * Comment 退回说明
	 * @param flowTaskVo
	 */
	void taskReturn(FlowTaskVo flowTaskVo);
	/**
	 * 删除任务
	 * taskId 任务id
	 * comment 删除说明
	 * @param flowTaskVo
	 */
	void deleteTask(FlowTaskVo flowTaskVo);
	
	/**
	 * 认领/签收任务
	 * taskId 任务id
	 * userId 认领/签收人
	 * @param flowTaskVo
	 */
	void claim(FlowTaskVo flowTaskVo);
	/**
	 * 取消认领/签收任务 
	 * taskId 任务id
	 * @param flowTaskVo
	 */
	void unClaim(FlowTaskVo flowTaskVo);
	/**
	 * 委派任务  
	 * taskId 任务id
	 * assignee 办理人
	 * @param flowTaskVo
	 */
	void delegateTask(FlowTaskVo flowTaskVo);
	/**
	 * 转办任务 
	 * taskId 任务id
	 * assignee 办理人
	 * @param flowTaskVo
	 */
	void assignTask(FlowTaskVo flowTaskVo);
	/**
	 * 获取所有可回退的节点
	 *  taskId 任务id
	 * @param flowTaskVo
	 * @return
	 */
	List<UserTask> findReturnTaskList(FlowTaskVo flowTaskVo);
	/**
	 * 我的待办
	 * @param userId 待办人
	 * @param pageNum 
	 * @param pageSize
	 * @return
	 */
	PageInfo<FlowTaskDto> todoList(String userId,Integer pageNum,Integer pageSize);
	
	/**
	 * 我的已办
	 * @param userId 办理人
	 * @param pageNum 
	 * @param pageSize
	 * @return
	 */
	PageInfo<FlowTaskDto> finishedList(String userId,Integer pageNum,Integer pageSize);
	
	/**
	 * 通过业务key得到表单配置
	 * @param businessKey
	 * @return
	 */
	Map<String, Object> getFormKey(String businessKey);
	/**
	 * 根据业务key得到流程实例id
	 * @param businessKey
	 * @return
	 */
	String getProcInsId(String businessKey);
	/**
	 * 根据业务key得到流程实例id 对应集合
	 * @param businessKeys
	 * @return
	 */
	List<FlwTaskBukeyVo> getProcInsId(List<String> businessKeys);

	/**
	 * 根据业务key 查询是否存在task
	 * @param businessKey
	 * @return
	 */
	Boolean hasTask(String businessKey);

	/**
	 * 根据业务key得到taskid
	 * @param businessKey
	 * @return
	 */
	String getTaskId(String businessKey);
	/**
	 * 根据业务key得到taskid 对应集合
	 * @param businessKeys
	 * @return
	 */
	List<FlwTaskBukeyVo> getTaskId(List<String> businessKeys); 
	
	/**
	 *  获取流程执行过程
	 * @param procInsId 流程实例id
	 * @return
	 */
	List<FlowViewerDto> getFlowViewer(String procInsId);
	/**
	 * 根据流程定义key得到流程定义xml
	 * @param processInstanceKey
	 * @return
	 */
	String readXml(String processInstanceKey);
	/**
	 * 流程历史流转记录  审批记录
	 * @param procInsId 流程实例id
	 * @param processInstanceKey
	 * @return
	 */
	List<FlowTaskDto> flowRecord(String procInsId, String processInstanceKey);

	/**
	 * 启动流程
	 * @param taskVo
	 * @return
	 */
	FlowNodeVo startProcessWithNext(FlowTaskVo taskVo, String currentUserId);
}
