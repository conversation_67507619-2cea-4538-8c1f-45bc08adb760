package com.fd.stdp.service.talent;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamContribute;
import com.fd.stdp.beans.talent.vo.TalentTeamContributeVo;
/**
 *@Description: 创新团队建设任务书
 *@Author: wangsh
 *@Date: 2022-02-14 10:22:12
 */
public interface TalentTeamContributeService {

	/**
	 *@Description: 保存或更新创新团队建设任务书
	 *@param talentTeamContribute 创新团队建设任务书对象
	 *@return String 创新团队建设任务书ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTalentTeamContribute(TalentTeamContributeVo talentTeamContribute);
	
	/**
	 *@Description: 删除创新团队建设任务书
	 *@param id void 创新团队建设任务书ID
	 *@Author: wangsh
	 */
	void deleteTalentTeamContribute(String id);

	/**
	 * @Description: 批量删除创新团队建设任务书
	 * @param ids
	 */
    void deleteMultiTalentTeamContribute(List<String> ids);

	/**
	 *@Description: 查询创新团队建设任务书详情
	 *@param id
	 *@return TalentTeamContribute
	 *@Author: wangsh
	 */
	TalentTeamContribute findById(String id);

	/**
	 *@Description: 分页查询创新团队建设任务书
	 *@param talentTeamContributeVo
	 *@return PageInfo<TalentTeamContribute>
	 *@Author: wangsh
	 */
	PageInfo<TalentTeamContribute> findPageByQuery(TalentTeamContributeVo talentTeamContributeVo);
	
	
	/**
	 * 提交
	 * @param talentTeamContributeVo
	 * @return
	 */
    String submitTalentTeamContribute(TalentTeamContributeVo talentTeamContributeVo);

	/**
	 * 审核
	 * @param talentTeamContributeVo
	 * @return
	 */
	String auditTalentTeamContribute(TalentTeamContributeVo talentTeamContributeVo);

	/**
	 * 退回
	 * @param talentTeamContributeVo
	 * @return
	 */
	String sendBackTalentTeamContribute(TalentTeamContributeVo talentTeamContributeVo);

	/**
	 * 任务书下达
	 * @param talentTeamContributeVo
	 * @return
	 */
	String releaseTalentTeamContribute(TalentTeamContributeVo talentTeamContributeVo);
	
	/**
	 * 待办列表
	*/
    PageInfo<TalentTeamContribute> todoList(TalentTeamContributeVo talentTeamContributeVo);

	/**
	 * 已办列表
	*/
	PageInfo<TalentTeamContribute> finishedList(TalentTeamContributeVo talentTeamContributeVo);
}
