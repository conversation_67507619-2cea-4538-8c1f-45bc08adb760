package com.fd.stdp.service.talent.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import com.fd.stdp.beans.basic.BasicRecommend;
import com.fd.stdp.beans.talent.TalentLeaderSubjectContractChange;
import com.fd.stdp.beans.talent.TalentLeaderSubjectExamine;
import com.fd.stdp.beans.talent.TalentTeamContributeTarget;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectContractChangeVo;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectExamineVo;
import com.fd.stdp.dao.basic.BasicRecommendMapper;
import com.fd.stdp.dao.talent.TalentLeaderSubjectContractChangeMapper;
import com.fd.stdp.dao.talent.TalentLeaderSubjectExamineMapper;
import com.fd.stdp.dao.talent.TalentTeamContributeTargetMapper;
import com.fd.stdp.service.talent.TalentLeaderSubjectContractChangeService;
import com.fd.stdp.service.talent.TalentLeaderSubjectExamineService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectContract;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectContractVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.talent.TalentLeaderSubjectContractMapper;
import com.fd.stdp.service.talent.TalentLeaderSubjectContractService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 学科带头人建设任务书
 *@Author: wangsh
 *@Date: 2022-02-16 09:00:47
 */
public class TalentLeaderSubjectContractServiceImpl extends BaseServiceImpl<TalentLeaderSubjectContractMapper, TalentLeaderSubjectContract> implements TalentLeaderSubjectContractService{

	public static final Logger logger = LoggerFactory.getLogger(TalentLeaderSubjectContractServiceImpl.class);
	
	@Autowired
	private TalentLeaderSubjectContractMapper talentLeaderSubjectContractMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private TalentTeamContributeTargetMapper talentTeamContributeTargetMapper;
	@Autowired
	private BasicRecommendMapper basicRecommendMapper;
	@Autowired
	private TalentLeaderSubjectContractChangeService talentLeaderSubjectContractChangeService;
	@Autowired
	private TalentLeaderSubjectContractChangeMapper talentLeaderSubjectContractChangeMapper;
	@Autowired
	private TalentLeaderSubjectExamineService talentLeaderSubjectExamineService;
	@Autowired
	private TalentLeaderSubjectExamineMapper talentLeaderSubjectExamineMapper;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新学科带头人建设任务书
	 *@param talentLeaderSubjectContract 学科带头人建设任务书对象
	 *@return String 学科带头人建设任务书ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTalentLeaderSubjectContract(TalentLeaderSubjectContractVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			talentLeaderSubjectContractMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			talentLeaderSubjectContractMapper.updateByPrimaryKeySelective(vo);
		}

		// 建设期间字段
		if(StringUtils.isBlank(vo.getContributeTimeEnd()) || StringUtils.isBlank(vo.getContributeTime())) {
			Example example = new Example(BasicRecommend.class);
			example.createCriteria().andEqualTo("yn", 1).andEqualTo("recommandType", "SUBJECT_LEADER_CONTRACT_RECOMMAND");
			List<BasicRecommend> recommends = basicRecommendMapper.selectByExample(example);
			if (recommends.size() > 0) {
				DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
				vo.setContributeTime(df.format(recommends.get(0).getBuildStartTime()));
				vo.setContributeTimeEnd(df.format(recommends.get(0).getBuildEndTime()));
			}
		}

		if(!CollectionUtils.isEmpty(vo.getContractTargets())){
			updateList(vo, vo.getContractTargets(), talentTeamContributeTargetMapper, "setContractId");
		}

		// 开启流程
		// flowCommonService.doFlowStart(FlowableConstant.FLOW_TALENT_TEAM_APPLY, vo, this.mapper, "开始创新团队建设任务书流程");

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除学科带头人建设任务书
	 *@param id void 学科带头人建设任务书ID
	 *@Author: wangsh
	 */
	public void deleteTalentLeaderSubjectContract(String id) {
		//TODO 做判断后方能执行删除
		TalentLeaderSubjectContract talentLeaderSubjectContract=talentLeaderSubjectContractMapper.selectByPrimaryKey(id);
		if(talentLeaderSubjectContract==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TalentLeaderSubjectContract temtalentLeaderSubjectContract=new TalentLeaderSubjectContract();
		temtalentLeaderSubjectContract.setYn(CommonConstant.FLAG_NO);
		temtalentLeaderSubjectContract.setId(talentLeaderSubjectContract.getId());
		talentLeaderSubjectContractMapper.updateByPrimaryKeySelective(temtalentLeaderSubjectContract);
	}

    /**
     * @Description: 批量删除学科带头人建设任务书
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTalentLeaderSubjectContract(List<String> ids) {
		ids.stream().forEach(id-> this.deleteTalentLeaderSubjectContract(id));
	}

	@Override
	/**
	 *@Description: 查询学科带头人建设任务书详情
	 *@param id
	 *@return TalentLeaderSubjectContract
	 *@Author: wangsh
	 */
	public TalentLeaderSubjectContract findById(String id) {
		TalentLeaderSubjectContract talentLeaderSubjectContract = talentLeaderSubjectContractMapper.selectByPrimaryKey(id);
		TalentLeaderSubjectContractVo vo = new TalentLeaderSubjectContractVo();
		BeanUtils.copyProperties(talentLeaderSubjectContract, vo);

		Example example = new Example(TalentTeamContributeTarget.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("contractId", id);
		vo.setContractTargets(talentTeamContributeTargetMapper.selectByExample(example));
		// 流程
		if(StringUtils.isNotBlank(vo.getFlowStatus())) {
			vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		}
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询学科带头人建设任务书
	 *@param talentLeaderSubjectContractVo
	 *@return PageInfo<TalentLeaderSubjectContract>
	 *@Author: wangsh
	 */
	public PageInfo<TalentLeaderSubjectContract> findPageByQuery(TalentLeaderSubjectContractVo talentLeaderSubjectContractVo) {
		PageHelper.startPage(talentLeaderSubjectContractVo.getPageNum(),talentLeaderSubjectContractVo.getPageSize());
		Example example=new Example(TalentLeaderSubjectContract.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		criteria.andEqualTo("flowStatus",FlowStatusEnum.END.getCode());
		//查询条件
		//if(!StringUtils.isEmpty(talentLeaderSubjectContractVo.getName())){
		//	criteria.andEqualTo(talentLeaderSubjectContractVo.getName());
		//}
		List<TalentLeaderSubjectContract> talentLeaderSubjectContractList=talentLeaderSubjectContractMapper.selectByExample(example);
		return new PageInfo<TalentLeaderSubjectContract>(talentLeaderSubjectContractList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitTalentLeaderSubjectContract(TalentLeaderSubjectContractVo talentLeaderSubjectContractVo) {
		talentLeaderSubjectContractVo.setApplyTime(new Date());
		String id = this.saveOrUpdateTalentLeaderSubjectContract(talentLeaderSubjectContractVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, talentLeaderSubjectContractVo, this.mapper,
				StringUtils.isNotBlank(talentLeaderSubjectContractVo.getAuditAdvice())?talentLeaderSubjectContractVo.getAuditAdvice():"提交学科带头人建设任务书");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditTalentLeaderSubjectContract(TalentLeaderSubjectContractVo talentLeaderSubjectContractVo) {
		if(StringUtils.equals(talentLeaderSubjectContractVo.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())){
			String id = talentLeaderSubjectContractVo.getId();
			TalentLeaderSubjectContractVo contractVo = (TalentLeaderSubjectContractVo) findById(id);
			Example example = new Example(TalentLeaderSubjectContractChange.class);
			example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("subjectLeaderId", id);
			if(CollectionUtils.isEmpty(talentLeaderSubjectContractChangeMapper.selectByExample(example))){
				TalentLeaderSubjectContractChangeVo vo = new TalentLeaderSubjectContractChangeVo();
				BeanUtils.copyProperties(contractVo, vo);
				vo.setId(null);
				vo.setSubjectLeaderId(id);
				vo.setOriginContractId(id);
				vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
				vo.setFlowUser(AssigneeConstant.ORG_HEAD_ROLE);
				talentLeaderSubjectContractChangeService.saveOrUpdateTalentLeaderSubjectContractChange(vo);
			}
			example = new Example(TalentLeaderSubjectExamine.class);
			example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("subjectLeaderId", id);
			if(CollectionUtils.isEmpty(talentLeaderSubjectExamineMapper.selectByExample(example))){
				TalentLeaderSubjectExamineVo vo = new TalentLeaderSubjectExamineVo();
				BeanUtils.copyProperties(contractVo, vo);
				vo.setId(null);
				vo.setSubjectLeaderId(id);
				vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
				vo.setFlowUser(AssigneeConstant.ORG_HEAD_ROLE);
				talentLeaderSubjectExamineService.saveOrUpdateTalentLeaderSubjectExamine(vo);
			}
		}
		flowCommonService.doFlowStepAudit(talentLeaderSubjectContractVo, this.mapper
				, StringUtils.isNotBlank(talentLeaderSubjectContractVo.getAuditAdvice()) ? talentLeaderSubjectContractVo.getAuditAdvice() : "学科带头人建设任务书审核通过"
				, FlowStatusEnum.EXPERTS_GRADE.getCode());
		return talentLeaderSubjectContractVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackTalentLeaderSubjectContract(TalentLeaderSubjectContractVo talentLeaderSubjectContractVo) {
		flowCommonService.doFlowStepSendBack(talentLeaderSubjectContractVo, this.mapper
				, StringUtils.isNotBlank(talentLeaderSubjectContractVo.getAuditAdvice())?talentLeaderSubjectContractVo.getAuditAdvice():"学科带头人建设任务书退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseTalentLeaderSubjectContract(TalentLeaderSubjectContractVo talentLeaderSubjectContractVo) {
		flowCommonService.doCompleteTask(talentLeaderSubjectContractVo, this.mapper
				, "学科带头人建设任务书任务书下达完成"
				, FlowStatusEnum.PROJECT_RELEASE.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<TalentLeaderSubjectContract> todoList(TalentLeaderSubjectContractVo vo) {

		Example example = new Example(TalentLeaderSubjectContract.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TalentLeaderSubjectContract> finishedList(TalentLeaderSubjectContractVo vo) {
		Example example = new Example(TalentLeaderSubjectContract.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<TalentLeaderSubjectContract> endList(TalentLeaderSubjectContractVo vo) {
        Example example = new Example(TalentLeaderSubjectContract.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(TalentLeaderSubjectContract vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}
		return criteria;
	}
}
