package com.fd.stdp.service.talent.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.fd.stdp.beans.basic.BasicGradeLinked;
import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import com.fd.stdp.beans.basic.vo.BasicPersonLinkedVo;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectLeader;
import com.fd.stdp.beans.talent.TalentTeamContribute;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectContractVo;
import com.fd.stdp.beans.talent.vo.TalentTeamContributeVo;
import com.fd.stdp.constant.ProcessConstants;
import com.fd.stdp.dao.basic.BasicGradeLinkedMapper;
import com.fd.stdp.enums.FlowComment;
import com.fd.stdp.service.basic.BasicGradeLinkedService;
import com.fd.stdp.service.basic.BasicPersonLinkedService;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.talent.TalentTeamContributeService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamApply;
import com.fd.stdp.beans.talent.vo.TalentTeamApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.talent.TalentTeamApplyMapper;
import com.fd.stdp.service.talent.TalentTeamApplyService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import static com.fd.stdp.common.BaseController.getCurrentUserId;
import static com.fd.stdp.common.BaseController.getCurrentUserName;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 科技创新团队申请书
 *@Author: wangsh
 *@Date: 2022-02-14 10:21:55
 */
public class TalentTeamApplyServiceImpl extends BaseServiceImpl<TalentTeamApplyMapper, TalentTeamApply> implements TalentTeamApplyService{

	public static final Logger logger = LoggerFactory.getLogger(TalentTeamApplyServiceImpl.class);
	private static final String NUMBER = "NUMBER";
	private static final String LEADER = "LEADER";
	private static final String EXPERT = "EXPERT";

	@Autowired
	private TalentTeamApplyMapper talentTeamApplyMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicPersonLinkedService basicPersonLinkedService;
	@Autowired
	private TalentTeamContributeService talentTeamContributeService;
	@Autowired
	private BasicGradeLinkedService basicGradeLinkedService;
	@Autowired
	private BasicGradeLinkedMapper basicGradeLinkedMapper;

	@Autowired
	private FlowApiService flowApiService;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新科技创新团队申请书
	 *@param talentTeamApply 科技创新团队申请书对象
	 *@return String 科技创新团队申请书ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTalentTeamApply(TalentTeamApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			talentTeamApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			talentTeamApplyMapper.updateByPrimaryKeySelective(vo);
		}

		// 团队成员
		List<BasicPersonLinkedVo> list = new ArrayList();
		if(!CollectionUtils.isEmpty(vo.getTeamNumbers())){
			vo.getTeamNumbers().stream().forEach(p->p.setPersonType(NUMBER));
			basicPersonLinkedService.clearAndUpdateBasicPersonLinked(vo.getId(), vo.getTeamNumbers(), NUMBER);
		}
		// 合作专家
		if(!CollectionUtils.isEmpty(vo.getTeamExperts())){
			vo.getTeamExperts().stream().forEach(p->p.setPersonType(EXPERT));
			basicPersonLinkedService.clearAndUpdateBasicPersonLinked(vo.getId(), vo.getTeamExperts(), EXPERT);
		}

		// 开启流程
		// flowCommonService.doFlowStart(FlowableConstant.FLOW_TALENT_TEAM_APPLY, vo, this.mapper, "开始创新团队申报流程");

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除科技创新团队申请书
	 *@param id void 科技创新团队申请书ID
	 *@Author: wangsh
	 */
	public void deleteTalentTeamApply(String id) {
		//TODO 做判断后方能执行删除
		TalentTeamApply talentTeamApply=talentTeamApplyMapper.selectByPrimaryKey(id);
		if(talentTeamApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TalentTeamApply temtalentTeamApply=new TalentTeamApply();
		temtalentTeamApply.setYn(CommonConstant.FLAG_NO);
		temtalentTeamApply.setId(talentTeamApply.getId());
		talentTeamApplyMapper.updateByPrimaryKeySelective(temtalentTeamApply);
	}

    /**
     * @Description: 批量删除科技创新团队申请书
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTalentTeamApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteTalentTeamApply(id));
	}

	@Override
	/**
	 *@Description: 查询科技创新团队申请书详情
	 *@param id
	 *@return TalentTeamApply
	 *@Author: wangsh
	 */
	public TalentTeamApply findById(String id) {
		TalentTeamApply talentTeamApply = talentTeamApplyMapper.selectByPrimaryKey(id);
		TalentTeamApplyVo vo = new TalentTeamApplyVo();
		BeanUtils.copyProperties(talentTeamApply, vo);
		List<BasicPersonLinkedVo> bv = basicPersonLinkedService.findByFormId(id);
		vo.setTeamExperts(bv.stream().filter(b->EXPERT.equals(b.getPersonType())).collect(Collectors.toList()));
		vo.setTeamNumbers(bv.stream().filter(b->NUMBER.equals(b.getPersonType())).collect(Collectors.toList()));
		vo.setGradeExperts(basicGradeLinkedService.findByFormId(id));
		// vo.setGradeResults(basicGradeLinkedService.findByFormId(id));
		// 流程
		if(StringUtils.isNotBlank(vo.getFlowStatus())) {
			vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		}
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询科技创新团队申请书
	 *@param talentTeamApplyVo
	 *@return PageInfo<TalentTeamApply>
	 *@Author: wangsh
	 */
	public PageInfo<TalentTeamApply> findPageByQuery(TalentTeamApplyVo talentTeamApplyVo) {
		PageHelper.startPage(talentTeamApplyVo.getPageNum(),talentTeamApplyVo.getPageSize());
		Example example=new Example(TalentTeamApply.class);
		Criteria criteria=getCriteria(talentTeamApplyVo, example);
		criteria.andEqualTo("flowStatus",FlowStatusEnum.END.getCode());
		//查询条件
		//if(!StringUtils.isEmpty(talentTeamApplyVo.getName())){
		//	criteria.andEqualTo(talentTeamApplyVo.getName());
		//}
		List<TalentTeamApply> talentTeamApplyList=talentTeamApplyMapper.selectByExample(example);
		return new PageInfo<TalentTeamApply>(talentTeamApplyList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitTalentTeamApply(TalentTeamApplyVo talentTeamApplyVo) {
		String id = this.saveOrUpdateTalentTeamApply(talentTeamApplyVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_SUBJECR_LEADER_APPLY, talentTeamApplyVo, this.mapper,
				StringUtils.isNotBlank(talentTeamApplyVo.getAuditAdvice())?talentTeamApplyVo.getAuditAdvice():"提交科技创新团队申请书");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditTalentTeamApply(TalentTeamApplyVo vo) {
		TalentTeamApply old = findById(vo.getId());
		if(StringUtils.equals(old.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())){
			if(StringUtils.equals("0", vo.getIsPass())){
				// 不合格
				vo.setFlowStatus(FlowStatusEnum.END.getCode());
				vo.setFlowUser(FlowStatusEnum.END.getRole());
				this.mapper.updateByPrimaryKeySelective(vo);

				String taskId = flowApiService.getTaskId(vo.getId());
				Map<String, Object> map = new HashMap<String, Object>();
				FlowTaskVo flowTaskVo = new FlowTaskVo();
				flowTaskVo.setTaskId(taskId);
				flowTaskVo.setBusinessKey(vo.getId());
				flowTaskVo.setUserId(getCurrentUserId());
				flowTaskVo.setUserName(getCurrentUserName());
				flowTaskVo.setComment(StringUtils.isBlank(vo.getAuditAdvice())?"省局审核不合格":vo.getAuditAdvice());
				flowTaskVo.setAssignee(AssigneeConstant.ORG_ADMIN_ROLE);
				flowTaskVo.setValues(map);
				map.put("ISPASS", 2);
				map.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());
				flowApiService.completeTask(flowTaskVo, FlowComment.REJECT.getType());
				return vo.getId();
			}
			if(CollectionUtils.isEmpty(vo.getGradeExperts())){
				throw new ServiceException("请选择专家");
			}
			boolean hasLeader = false;
			for (BasicGradeLinkedVo b:vo.getGradeExperts()
			) {
				if("组长".equals(b.getUserType())){
					hasLeader = true;
					break;
				}
			}
			if(!hasLeader){
				throw new ServiceException("请添加专家组长");
			}
			basicGradeLinkedService.clearAndUpdateBasicGradeLinked(vo.getId(), vo.getGradeExperts());
		}
//
//		if(StringUtils.equals(vo.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())
//			&& CollectionUtils.isEmpty(vo.getGradeExperts())){
//			throw new ServiceException("评审专家不得为空");
//		}
//		// 省局审核会选择专家
//		if(!CollectionUtils.isEmpty(vo.getGradeExperts())){
//			List<BasicGradeLinkedVo> basicGradeLinkedVoList = new ArrayList<>();
//			vo.getGradeExperts().stream().forEach(basicPersonLinkedVo -> {
//				BasicGradeLinkedVo b = new BasicGradeLinkedVo();
//				b.setUserId(basicPersonLinkedVo.getPersonId());
//				basicGradeLinkedVoList.add(b);
//			});
//			basicGradeLinkedService.clearAndUpdateBasicGradeLinked(vo.getId(), basicGradeLinkedVoList);
//		}
		flowCommonService.doFlowStepAudit(vo, this.mapper
				, StringUtils.isNotBlank(vo.getAuditAdvice()) ? vo.getAuditAdvice() : "科技创新团队申请书审核通过"
				, FlowStatusEnum.EXPERTS_GRADE.getCode(), AssigneeConstant.EXPERT_ROLE);

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackTalentTeamApply(TalentTeamApplyVo talentTeamApplyVo) {
		flowCommonService.doFlowStepSendBack(talentTeamApplyVo, this.mapper
				, StringUtils.isNotBlank(talentTeamApplyVo.getAuditAdvice())?talentTeamApplyVo.getAuditAdvice():"科技创新团队申请书退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseTalentTeamApply(TalentTeamApplyVo talentTeamApplyVo) {
		flowCommonService.doCompleteTask(talentTeamApplyVo, this.mapper
				, "科技创新团队申请书任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
//		TalentTeamContributeVo talentTeamContributeVo = new TalentTeamContributeVo();
//		BeanUtils.copyProperties(talentTeamApplyVo, talentTeamContributeVo);
//		talentTeamContributeVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
//		talentTeamContributeVo.setFlowUser(AssigneeConstant.ORG_HEAD_ROLE);
//		talentTeamContributeVo.setId(null);
//		talentTeamContributeService.saveOrUpdateTalentTeamContribute(talentTeamContributeVo);
		if(StringUtils.equals("0", talentTeamApplyVo.getIsPass())){
			// 不合格
			talentTeamApplyVo.setFlowStatus(FlowStatusEnum.CANCEL.getCode());
			this.mapper.updateByPrimaryKeySelective(talentTeamApplyVo);
		} else {
			// 审核合格修改合格状态
			talentTeamApplyVo.setFlowStatus(FlowStatusEnum.JUST_END.getCode());
			this.mapper.updateByPrimaryKeySelective(talentTeamApplyVo);
		}
		return null;
	}


	@Override
	public PageInfo<TalentTeamApply> todoList(TalentTeamApplyVo vo) {

		Example example = new Example(TalentTeamApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TalentTeamApply> finishedList(TalentTeamApplyVo vo) {
		Example example = new Example(TalentTeamApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
	@Transactional(readOnly = false)
	public String expertSubmit(TalentTeamApplyVo vo) {
//		String userId = getCurrentUserId();
//		Example example = new Example(BasicGradeLinked.class);
//		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
//				.andEqualTo("userId", userId)
//				.andEqualTo("formId", vo.getId());
//		List<BasicGradeLinked> gradeLinkeds = basicGradeLinkedMapper.selectByExample(example);
//		if(CollectionUtils.isEmpty(gradeLinkeds)){
//			throw new ServiceException("未找到打分记录");
//		}
//		BasicGradeLinked b = gradeLinkeds.get(0);
//		b.setGradeOption(vo.getGrade().getGradeOption());
//		b.setResultType(vo.getGrade().getResultType());
//		basicGradeLinkedMapper.updateByPrimaryKeySelective(b);
//
//		// 全部评审完成后进入下一步流程
//		List<BasicGradeLinkedVo> list = basicGradeLinkedService.findByFormId(vo.getId());
//		boolean allFinish = true;
//		for (BasicGradeLinkedVo bgv:list) {
//			if(StringUtils.isBlank(bgv.getResultType())){
//				allFinish = false;
//			}
//		}
//		if(allFinish) {
//			TalentTeamApplyVo talentTeamApplyVo = (TalentTeamApplyVo) this.findById(vo.getId());
//			flowCommonService.doCompleteTask(talentTeamApplyVo, this.mapper
//					, "科技创新团队申请专家评审完成"
//					, FlowStatusEnum.PROJECT_RELEASE.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
//		}
//		return vo.getId();
		String userId = getCurrentUserId();
		Boolean nextStep = false;
		vo.getGradeExperts().stream().filter(grade->StringUtils.equals(userId, grade.getUserId()))
			.forEach(grade->{
				basicGradeLinkedMapper.updateByPrimaryKeySelective(grade);
			});
		if(basicGradeLinkedService.formGradeAllFinished(vo.getId())) {
			List<BasicGradeLinkedVo> linkeds = basicGradeLinkedService.findByFormId(vo.getId());
			for (BasicGradeLinkedVo grade:linkeds) {
				if (StringUtils.equals(userId, grade.getUserId())) {
					nextStep = StringUtils.equals("组长", grade.getUserType());
				}
			}
		}
		if(nextStep){
			flowCommonService.doCompleteTask(vo, this.mapper, "全部专家评审完成"
					, FlowStatusEnum.PROJECT_RELEASE.getCode(), FlowStatusEnum.PROJECT_RELEASE.getRole(), getCurrentUserName());
		}
		return null;
	}

	@Override
	public PageInfo<TalentTeamApply> toContractList(TalentTeamApplyVo vo) {
		Example example = new Example(TalentTeamApply.class);
		Criteria criteria = getCriteria(vo, example);
		criteria.andEqualTo("flowStatus", FlowStatusEnum.JUST_END.getCode());
		PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}

	@Override
	@Transactional(readOnly = false)
	public void createContractTalentTeamApply(TalentTeamApplyVo vo) {
		if(vo.getStartDate() == null){
			throw new ServiceException("请输入填报起始时间");
		}
		if(vo.getEndDate() == null){
			throw new ServiceException("请输入填报起始时间");
		}
		if(CollectionUtils.isEmpty(vo.getIds())){
			throw new ServiceException("请至少选择一条记录");
		}
		vo.getIds().stream().forEach(id->{
			TalentTeamApplyVo old = (TalentTeamApplyVo) findById(id);
			old.setStartDate(vo.getStartDate());
			old.setEndDate(vo.getStartDate());
			// 添加任务书
			TalentTeamContributeVo talentTeamContributeVo = new TalentTeamContributeVo();
			BeanUtils.copyProperties(old, talentTeamContributeVo);
			talentTeamContributeVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			talentTeamContributeVo.setFlowUser(AssigneeConstant.ORG_HEAD_ROLE);
			talentTeamContributeVo.setId(null);
			talentTeamContributeService.saveOrUpdateTalentTeamContribute(talentTeamContributeVo);
			// 修改状态
			old.setFlowStatus(FlowStatusEnum.END.getCode());
			this.mapper.updateByPrimaryKeySelective(old);
		});
	}

	private Criteria getCriteria(TalentTeamApplyVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("teamLeader", "%" + vo.getName()+ "%");
		}
		return criteria;
	}
}
