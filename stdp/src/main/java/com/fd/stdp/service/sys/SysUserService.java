package com.fd.stdp.service.sys;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.fd.stdp.beans.basic.vo.BasicScienceOrgVo;
import com.fd.stdp.beans.rest.ExportSsoBody;
import com.fd.stdp.beans.rest.LoginBody;
import com.fd.stdp.beans.rest.SysUserYthoauth;
import com.fd.stdp.beans.rest.ZwwSsoBody;
import com.fd.stdp.beans.sys.SysRole;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.beans.sys.vo.SysUserVo;
import com.fd.stdp.common.RestApiResponse;
import com.github.pagehelper.PageInfo;

/**
 * @Description: 系统用户表
 * @Author: hzh
 * @Date: 2020-07-05 16:25:12
 */
public interface SysUserService {

    /**
     * @param sysUser 系统用户表对象
     * @return String 系统用户表ID
     * @Description: 保存或更新系统用户表
     * @Author: hzh
     */
    String saveOrUpdateSysUser(SysUserVo sysUser);

    /**
     * @param id void 系统用户表ID
     * @Description: 删除系统用户表
     * @Author: hzh
     */
    void deleteSysUser(List<String> id);

    /**
     * @param id
     * @return SysUser
     * @Description: 查询系统用户表详情
     * @Author: hzh
     */
    SysUser findById(String id);

    /**
     * @param sysUserVo
     * @return PageInfo<SysUser>
     * @Description: 分页查询系统用户表
     * @Author: hzh
     */
    PageInfo<SysUserVo> findPageByQuery(SysUserVo sysUserVo);

    /**
     * @param username
     * @return
     */
    SysUser getUser(String username);

    /**
     * @param id
     * @return
     */
    Set<String> listElementByUserId(String id);

    /**
     * @param id
     * @return
     */
    List<SysRole> listRolesByUserId(String id);

    /**
     * @param userId
     * @return
     */
    SysUser getUserById(String userId);

    /**
     * 修改密码
     *
     * @param sysUser
     */
    void resetPwd(SysUserVo sysUser);

    /**
     * 通过主键修改对应用户得密码
     *
     * @param
     * @return
     */
    int updatePasswordById(SysUserVo sysUserVo);

    /**
     * 保存用户信息
     */
    void saveUser(Map<String, String> map);

    List<SysUser> findAllUsers();


    /**
     * @return RestApiResponse<?>
     * @Description: 初始化用户
     * @Author: yujianfei
     */
    void initUser();


    /**
     * @param areaCode 角色CODE
     * @return RestApiResponse<?>
     * @Description: 根据角色CODE查询出所有系统用户表
     * @Author: yujianfei
     */
    PageInfo<SysUser> findPageByRoleCode(String areaCode);

    /**
     * 查询专家用户
     *
     * @param sysUserVo
     * @return
     */
    PageInfo<SysUserVo> findExpertPageByQuery(SysUserVo sysUserVo);

    /**
     * @return RestApiResponse<?><?>
     * @Description: 查询浙里检数据
     * @Author: 刘威
     */
    Object getZLJInfo();

    SysUser getUserByPhone(String mobile);

    // 获取手机下的所有用户
    List<SysUser> getUserListByPhone(String mobile);

    /**
     * 一体化平台登录
     *
     * @param sysUserYthoauth
     * @return
     */
    LoginUser ythLogin(SysUserYthoauth sysUserYthoauth);

    LoginUser zwwPersonLogin(ZwwSsoBody form);

    SysUser createExpertUsers(SysUserVo sysUserVo);

    SysUser createSuperviseUsers(SysUserVo sysUserVo);

    String saveOrUpdate(SysUserVo sysUser);

    SysUser getUserByUsername(String username, String userTypeCode);

    LoginUser zwwOrgLoginApi(ZwwSsoBody zwwSsoBody);

//    /**
//     * 根据用户名和密码查询用户
//     *
//     * @param loginBody
//     * @return
//     */
//    LoginUser getLoginUser(LoginBody loginBody);

    /**
     * 绑定一体化用户
     *
     * @param sysUserYthoauth
     */
    void ythBinding(SysUserYthoauth sysUserYthoauth);

    /**
     * 用户绑定机构
     *
     */
    void bindUserToOrg(BasicScienceOrgVo basicScienceOrgVo);

    /**
     * @return void * @throws
     * @description: 专家单点登录
     */
    LoginUser expertLogin(ExportSsoBody exportSsoBody);
}
