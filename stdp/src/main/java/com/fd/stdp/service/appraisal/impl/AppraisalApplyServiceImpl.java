package com.fd.stdp.service.appraisal.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.fd.stdp.beans.appraisal.AppraisalAdvices;
import com.fd.stdp.beans.appraisal.AppraisalApplyItem;
import com.fd.stdp.beans.appraisal.AppraisalBasicItems;
import com.fd.stdp.beans.appraisal.vo.*;
import com.fd.stdp.beans.basic.BasicManageOrg;
import com.fd.stdp.beans.basic.vo.BasicManageOrgVo;
import com.fd.stdp.beans.appraisal.AppraisalFileInfo;
import com.fd.stdp.beans.sys.SysFileInfo;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.beans.sys.SysUserRole;
import com.fd.stdp.beans.sys.vo.SysUserVo;
import com.fd.stdp.constant.Constant;
import com.fd.stdp.dao.appraisal.*;

import com.fd.stdp.dao.sys.BasicAreacodeMapper;
import com.fd.stdp.dao.sys.SysUserMapper;
import com.fd.stdp.dao.sys.SysUserRoleMapper;
import com.fd.stdp.util.AppUserUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.appraisal.AppraisalApply;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicManageOrgMapper;
import com.fd.stdp.service.appraisal.AppraisalApplyService;
import com.fd.stdp.service.appraisal.AppraisalBasicItemsService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 评价申请Service业务层处理
 * @date 2021-11-18
 */
@Service
@Transactional(readOnly = true)
public class AppraisalApplyServiceImpl extends BaseServiceImpl<AppraisalApplyMapper, AppraisalApply> implements AppraisalApplyService {

    private static final Logger logger = LoggerFactory.getLogger(AppraisalApplyServiceImpl.class);
    @Autowired
    private AppraisalApplyMapper appraisalApplyMapper;

    @Autowired
    private AppraisalApplyItemMapper appraisalApplyItemMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private AppraisalAdvicesMapper appraisalAdvicesMapper;

    @Autowired
    private BasicManageOrgMapper basicManageOrgMapper;

    @Autowired
    private AppraisalFileInfoMapper appraisalFileInfoMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private AppraisalBasicItemsService appraisalBasicItemsService;

    @Autowired
    private AppraisalBasicItemsMapper appraisalBasicItemsMapper;

    @Autowired
    private BasicAreacodeMapper basicAreacodeMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新评价申请
     *@param appraisalApplyVo 评价申请对象
     *@return String 评价申请ID
     *@Author: yujianfei
     */
    public String saveOrUpdateAppraisalApply(AppraisalApplyVo appraisalApplyVo) {
        if (appraisalApplyVo == null) {
            throw new ServiceException("数据异常");
        }
        SysUser loginAppUser = (SysUser) AppUserUtil.getLoginAppUser();
        assert loginAppUser != null;
        AppraisalApply appraisalApply = new AppraisalApply();
        //新增
        appraisalApply.setId(UUIDUtils.getUUID());

        // 判断当前用户是市局还是省局
        Example example = new Example(SysUserRole.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        criteria.andEqualTo("userId", loginAppUser.getId());
        List<SysUserRole> userRoleList = sysUserRoleMapper.selectByExample(example);

        AppraisalAdvices appraisalAdvices = new AppraisalAdvices();
        if (CommonConstant.FLAG_NO.equals(appraisalApplyVo.getIscommit())) {    // 保存0
            if (!CollectionUtils.isEmpty(userRoleList)) {
                for (SysUserRole sysUserRole : userRoleList) {
                    // 市局
                    if ("f194f7d4389642d1aa0fc943ca26f7da".equals(sysUserRole.getRoleId())) {
                        appraisalApply.setOrgType("市局考核");
                    }
                    // 县局
                    if ("275d8f49d38a413a97c8d3ed4066460a".equals(sysUserRole.getRoleId())) {
                        appraisalApply.setOrgType("县局考核");
                    }
                }
            }

            appraisalApply.setFlowStatus(Constant.FLOW_STATUS_DTJ);
            appraisalApply.setAuditGrade(CommonConstant.FLAG_NO);
            appraisalAdvices.setAdvicesContents(loginAppUser.getManageOrgName() + "保存");
            appraisalApply.setIscommit(appraisalApplyVo.getIscommit());
            appraisalApply.setFlowUser(loginAppUser.getId());
            appraisalApply.setFlowUserName(loginAppUser.getNickname());


        } else if (CommonConstant.FLAG_YES.equals(appraisalApplyVo.getIscommit())) {    // 提交1
//            appraisalApply.setManageOrgId(loginAppUser.getManageOrgId());
//            appraisalApply.setManageOrgName(loginAppUser.getManageOrgName());


            if (!CollectionUtils.isEmpty(userRoleList)) {
                for (SysUserRole sysUserRole : userRoleList) {
                    // 市局
                    if ("f194f7d4389642d1aa0fc943ca26f7da".equals(sysUserRole.getRoleId())) {
                        appraisalApply.setOrgType("市局考核");
                        appraisalApply.setFlowStatus(Constant.FLOW_STATUS_DPSH);
                        appraisalApply.setAuditGrade(Constant.FLAG_TWO);
                        appraisalApply.setIscommit(appraisalApplyVo.getIscommit());
                        appraisalApply.setFlowUser(sysUserMapper.findMorgByAreaCode(loginAppUser.getAreaCode().substring(0, 2) + "0000").getId());
                        appraisalApply.setFlowUserName(sysUserMapper.findMorgByAreaCode(loginAppUser.getAreaCode().substring(0, 2) + "0000").getNickname());
                    }
                    // 县局
                    if ("275d8f49d38a413a97c8d3ed4066460a".equals(sysUserRole.getRoleId())) {
                        appraisalApply.setOrgType("县局考核");
                        appraisalApply.setFlowStatus(Constant.FLOW_STATUS_DCSH);
                        appraisalApply.setAuditGrade(Constant.FLAG_YES);
                        appraisalApply.setIscommit(appraisalApplyVo.getIscommit());
                        appraisalApply.setFlowUser(sysUserMapper.findMorgByAreaCode(loginAppUser.getAreaCode().substring(0, 4) + "00").getId());
                        appraisalApply.setFlowUserName(sysUserMapper.findMorgByAreaCode(loginAppUser.getAreaCode().substring(0, 4) + "00").getNickname());
                    }
                }
            }
            appraisalAdvices.setAdvicesContents(loginAppUser.getManageOrgName() + "提交");
        }

        appraisalAdvices.setId(UUIDUtils.getUUID());
        appraisalAdvices.setApplyId(appraisalApplyVo.getId());
        appraisalAdvices.setClassifiles(Constant.FLAG_YES);
        appraisalAdvicesMapper.insertSelective(appraisalAdvices);

        if (!CollectionUtils.isEmpty(appraisalApplyVo.getDataList())) {
            BigDecimal totalScoreSelf = new BigDecimal("0");
            for (int i = 0; i < appraisalApplyVo.getDataList().size(); i++) {
                // 县、市自评分
                if (!StringUtils.isEmpty(appraisalApplyVo.getDataList().get(i).getScoreSelf())) {
                    if (i == 0) {
                        totalScoreSelf = totalScoreSelf.add(appraisalApplyVo.getDataList().get(i).getScoreSelf());
                    }
                    if (i > 0 && !appraisalApplyVo.getDataList().get(i).getSerial().equals(appraisalApplyVo.getDataList().get(i - 1).getSerial())) {
                        totalScoreSelf = totalScoreSelf.add(appraisalApplyVo.getDataList().get(i).getScoreSelf());
                    }
                }

                AppraisalApplyItem appraisalApplyItem = new AppraisalApplyItem();
                Example itemsExample = new Example(AppraisalBasicItems.class);
                Criteria itemsCriteria = itemsExample.createCriteria();
                itemsCriteria.andEqualTo("yn", Constant.FLAG_YES);
                itemsCriteria.andEqualTo("orderVal", appraisalApplyVo.getDataList().get(i).getOrderVal());
                List<AppraisalBasicItems> appraisalBasicItems = appraisalBasicItemsMapper.selectByExample(itemsExample);
                if (!CollectionUtils.isEmpty(appraisalBasicItems)) {
                    if (!StringUtils.isEmpty(appraisalBasicItems.get(0).getCriteria())) {
                        appraisalApplyItem.setCriteria(appraisalBasicItems.get(0).getCriteria());
                    }
                    if (!StringUtils.isEmpty(appraisalBasicItems.get(0).getIndicator())) {
                        appraisalApplyItem.setIndicator(appraisalBasicItems.get(0).getIndicator());
                    }
                    appraisalApplyItem.setMainPoints(appraisalBasicItems.get(0).getMainPoints());
                    appraisalApplyItem.setOrderVal(appraisalBasicItems.get(0).getOrderVal());
                    appraisalApplyItem.setSerial(appraisalBasicItems.get(0).getSerial());
                    appraisalApplyItem.setSocre(appraisalBasicItems.get(0).getSocre());
                    appraisalApplyItem.setCreateTime(appraisalBasicItems.get(0).getCreateTime());
                }

                //todo
                if (null != appraisalApplyVo.getDataList().get(i).getScoreSelf()) {
                    appraisalApplyItem.setScoreSelf(appraisalApplyVo.getDataList().get(i).getScoreSelf());
                }
                if (appraisalApplyVo.getDataList().get(i).getScoreFinal() != null) {
                    appraisalApplyItem.setScoreFinal(appraisalApplyVo.getDataList().get(i).getScoreFinal());
                }
                if (!StringUtils.isEmpty(appraisalApplyVo.getDataList().get(i).getScoreContens())) {
                    appraisalApplyItem.setScoreContens(appraisalApplyVo.getDataList().get(i).getScoreContens());
                }
                if (!StringUtils.isEmpty(appraisalApplyVo.getDataList().get(i).getCompletion())) {
                    appraisalApplyItem.setCompletion(appraisalApplyVo.getDataList().get(i).getCompletion());
                }

                appraisalApplyItem.setUpdateTime(new Date());
                appraisalApplyItem.setUpdateUser(loginAppUser.getUsername());
                appraisalApplyItem.setUpdateUserNikename(loginAppUser.getNickname());

                // 评价申请项
                if (StringUtils.isEmpty(appraisalApplyItem.getCreateTime())) {
                    appraisalApplyItem.setId(UUIDUtils.getUUID());
                    appraisalApplyItem.setItemId(appraisalApplyVo.getDataList().get(i).getId());
                    //appraisalApplyItem.setApplyId(appraisalApplyVo.getId());
                    appraisalApplyItem.setApplyId(appraisalApply.getId());
                    appraisalApplyItemMapper.insertSelective(appraisalApplyItem);
                } else {
                    //避免页面传入修改
                    appraisalApplyItem.setYn(null);
                    appraisalApplyItem.setId(appraisalApplyVo.getDataList().get(i).getId());
                    appraisalApplyItemMapper.updateItem(appraisalApplyItem);
                }

                //附件信息
                AppraisalFileInfo deleteAppraisalFileInfo = new AppraisalFileInfo();// 先清空
                deleteAppraisalFileInfo.setItemId(appraisalApplyItem.getId());
                appraisalFileInfoMapper.delete(deleteAppraisalFileInfo);

                if (!CollectionUtils.isEmpty(appraisalApplyVo.getDataList().get(i).getSysfileinfo())) {
                    for (SysFileInfo sysFileInfo : appraisalApplyVo.getDataList().get(i).getSysfileinfo()) {
                        AppraisalFileInfo appraisalFileInfo = new AppraisalFileInfo();
                        BeanUtils.copyProperties(sysFileInfo, appraisalFileInfo);
                        appraisalFileInfo.setId(UUIDUtils.getUUID());
                        appraisalFileInfo.setItemId(appraisalApplyItem.getId());
                        appraisalFileInfoMapper.insertSelective(appraisalFileInfo);     // 再添加
                    }
                }
            }
            appraisalApply.setTotalScoreSelf(totalScoreSelf);
        }


        //省局保存
        // 省局评分
        if (!CollectionUtils.isEmpty(appraisalApplyVo.getDataList()) && appraisalApplyVo.getFlowStatus().equals(Constant.FLOW_STATUS_DPSH)) {
            BigDecimal totalScoreFinal = new BigDecimal("0");
            for (AppraisalBasicItemsVo appraisalBasicItemsVo : appraisalApplyVo.getDataList()) {
                if (!StringUtils.isEmpty(appraisalBasicItemsVo.getScoreFinal()) && !appraisalBasicItemsVo.getSocre().equals(0L)) {
                    totalScoreFinal = totalScoreFinal.add(appraisalBasicItemsVo.getScoreFinal());
                }
            }
            appraisalApply.setTotalScoreExt(appraisalApplyVo.getTotalScoreExt());
            appraisalApply.setTotalScoreFinal(totalScoreFinal);
            appraisalApply.setFlowStatus(Constant.FLOW_STATUS_DPSH);
            appraisalApply.setAuditGrade(Constant.FLAG_TWO);
            appraisalApply.setIscommit(Constant.FLAG_YES);


            //县局
            double aDouble = new Double(String.valueOf(totalScoreFinal));
            BigDecimal reducedScoreSum = new BigDecimal("0");
            if (appraisalApplyVo.getOrgType().equals(Constant.INTERGRATE_COUNTY)) {
                double all = aDouble / 51 * 4;
                reducedScoreSum = new BigDecimal(all);
            }
            //市局
            if (appraisalApplyVo.getOrgType().equals(Constant.INTERGRATE_CITY)) {
                double reduceScoreFinal = aDouble / 110 * 60;
                appraisalApply.setReducedScoreFinal(new BigDecimal(reduceScoreFinal));
                //额外分
                BigDecimal totalScoreExt = new BigDecimal("0");
                if (!StringUtils.isEmpty(appraisalApplyVo.getTotalScoreExt())) {
                    totalScoreExt = appraisalApplyVo.getTotalScoreExt();
                    appraisalApply.setTotalScoreExt(totalScoreExt);
                }
                double totalScoreSum = reduceScoreFinal + new Double(String.valueOf(totalScoreExt));
                appraisalApply.setTotalScoreSum(new BigDecimal(totalScoreSum));

                double all = (totalScoreSum) * 0.04;
                reducedScoreSum = new BigDecimal(all);
//                if (reducedScoreSum.intValue() > 4) {
//                    reducedScoreSum = new BigDecimal("4.0");
//                }
            }
            appraisalApply.setReducedScoreSum(reducedScoreSum);
        }


        if (StringUtils.isEmpty(appraisalApplyVo.getId())) {
            appraisalApplyMapper.insertSelective(appraisalApply);
        } else {
            //避免页面传入修改
            appraisalApply.setYn(null);
            appraisalApply.setId(appraisalApplyVo.getId());
            appraisalApplyMapper.updateByPrimaryKeySelective(appraisalApply);
        }
        return appraisalApply.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除评价申请
     *@param id void 评价申请ID
     *@Author: yujianfei
     */
    public void deleteAppraisalApply(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            AppraisalApply appraisalApply = appraisalApplyMapper.selectByPrimaryKey(id);
            if (appraisalApply == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            AppraisalApply temappraisalApply = new AppraisalApply();
            temappraisalApply.setYn(CommonConstant.FLAG_NO);
            temappraisalApply.setId(appraisalApply.getId());
            appraisalApplyMapper.updateByPrimaryKeySelective(temappraisalApply);
        }
    }

    /**
     * @param id
     * @return AppraisalApplyVo
     * @Description: 查询评价申请详情
     * @Author: yujianfei
     */
    @Override
    public AppraisalApplyVo findById(String id) {
        AppraisalApply appraisalApply = appraisalApplyMapper.selectByPrimaryKey(id);
        Example example = new Example(AppraisalApplyItem.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        criteria.andEqualTo("applyId", appraisalApply.getId());
        example.orderBy("orderVal").asc();
        List<AppraisalApplyItem> list = appraisalApplyItemMapper.selectByExample(example);
        List<AppraisalBasicItemsVo> dataList = new LinkedList<>();
        for (AppraisalApplyItem appraisalApplyItem : list) {
            AppraisalBasicItemsVo appraisalBasicItemsVo = new AppraisalBasicItemsVo();
            appraisalBasicItemsVo.setCompletion(appraisalApplyItem.getCompletion());

            // 文件查询
            Example fileExample = new Example(AppraisalFileInfo.class);
            Criteria fileCriteria = fileExample.createCriteria();
            fileCriteria.andEqualTo("itemId", appraisalApplyItem.getId());
            List<AppraisalFileInfo> fileInfoList = appraisalFileInfoMapper.selectByExample(fileExample);
            if (!CollectionUtils.isEmpty(fileInfoList)) {
                List<SysFileInfo> sysFileInfoList = new LinkedList<>();
                for (AppraisalFileInfo appraisalFileInfo : fileInfoList) {
                    SysFileInfo sysFileInfo = new SysFileInfo();
                    BeanUtils.copyProperties(appraisalFileInfo, sysFileInfo);
                    sysFileInfoList.add(sysFileInfo);
                }
                appraisalBasicItemsVo.setSysfileinfo(sysFileInfoList);
            }

            if (!StringUtils.isEmpty(appraisalApplyItem.getScoreSelf())) {
                appraisalBasicItemsVo.setScoreSelf(appraisalApplyItem.getScoreSelf());
            }
            if (!StringUtils.isEmpty(appraisalApplyItem.getScoreFinal())) {
                appraisalBasicItemsVo.setScoreFinal(appraisalApplyItem.getScoreFinal());
            }
            BeanUtils.copyProperties(appraisalApplyItem, appraisalBasicItemsVo);

            dataList.add(appraisalBasicItemsVo);
        }
        AppraisalApplyVo appraisalApplyVo = new AppraisalApplyVo();
        appraisalApplyVo.setDataList(dataList);
        appraisalApplyVo.setFlowStatus(appraisalApply.getFlowStatus());
        appraisalApplyVo.setId(appraisalApply.getId());
        appraisalApplyVo.setOrgType(appraisalApply.getOrgType());
        appraisalApplyVo.setTotalScoreExt(appraisalApply.getTotalScoreExt());
        Example adviceExample = new Example(AppraisalAdvices.class);
        Criteria adviceCriteria = adviceExample.createCriteria();
        adviceCriteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        adviceCriteria.andEqualTo("applyId", appraisalApplyVo.getId());
        adviceExample.orderBy("createTime").desc();
        List<AppraisalAdvices> advicesList = appraisalAdvicesMapper.selectByExample(adviceExample);
        if (!CollectionUtils.isEmpty(advicesList)) {
            appraisalApplyVo.setAdvicesContents(advicesList.get(0).getAdvicesContents());
            appraisalApplyVo.setClassifiles(advicesList.get(0).getClassifiles());
            advicesList = advicesList.stream().filter(p -> !Constant.SAVE.equals(p.getAdvicesContents().substring(p.getAdvicesContents().length() - 2))).collect(Collectors.toList());
            appraisalApplyVo.setAdvicesList(advicesList);
        }


        return appraisalApplyVo;
    }


    /**
     * @param appraisalApplyVo
     * @return PageInfo<AppraisalApply>
     * @Description: 分页查询评价申请
     * @Author: yujianfei
     */
    @Override
    public PageInfo<AppraisalApplyVo> findPageByQuery(AppraisalApplyVo appraisalApplyVo) {
        if (null == appraisalApplyVo) {
            throw new ServiceException("参数异常");
        }
        PageHelper.startPage(appraisalApplyVo.getPageNum(), appraisalApplyVo.getPageSize());
        SysUser loginAppUser = (SysUser) AppUserUtil.getLoginAppUser();
        List<AppraisalApplyVo> applyList = appraisalApplyMapper.findPageByQuery(appraisalApplyVo, loginAppUser.getId());
        return new PageInfo<AppraisalApplyVo>(applyList);
    }


    /**
     * @param appraisalApplyVo 评价申请数据 json
     * @return RestApiResponse<?>
     * @Description: 审核评价申请
     * @Author: yujianfei
     */
    @Override
    @Transactional(readOnly = false)
    public void auditByProvince(AppraisalApplyVo appraisalApplyVo) {
        if (appraisalApplyVo == null) {
            throw new ServiceException("数据异常");
        }
        SysUser loginAppUser = (SysUser) AppUserUtil.getLoginAppUser();
        assert loginAppUser != null;
        AppraisalApply appraisalApply = new AppraisalApply();
        appraisalApply.setId(appraisalApplyVo.getId());
        appraisalApply.setIscommit(Constant.FLAG_YES);
        appraisalApply.setFlowUser(sysUserMapper.findOneProvinceUser().getId());
        appraisalApply.setFlowUserName(sysUserMapper.findOneProvinceUser().getNickname());
        // 省局评分
        if (!CollectionUtils.isEmpty(appraisalApplyVo.getDataList())) {
            BigDecimal totalScoreFinal = new BigDecimal("0");


            for (AppraisalBasicItemsVo appraisalBasicItemsVo : appraisalApplyVo.getDataList()) {
                if (!StringUtils.isEmpty(appraisalBasicItemsVo.getScoreFinal()) && !appraisalBasicItemsVo.getSocre().equals(0L)) {
                    totalScoreFinal = totalScoreFinal.add(appraisalBasicItemsVo.getScoreFinal());
                }
                AppraisalApplyItem appraisalApplyItem = new AppraisalApplyItem();
                BeanUtils.copyProperties(appraisalBasicItemsVo, appraisalApplyItem);


                AppraisalApply oldApply = appraisalApplyMapper.selectByPrimaryKey(appraisalApplyVo.getId());
                if (Constant.FINISH.equals(oldApply.getFlowStatus())) {
                    throw new ServiceException("数据异常,请刷新页面");
                } else {
                    if (!StringUtils.isEmpty(appraisalBasicItemsVo.getScoreFinal())) {
                        appraisalApplyItem.setScoreFinal(appraisalBasicItemsVo.getScoreFinal());
                    }
                    if (!StringUtils.isEmpty(appraisalBasicItemsVo.getScoreContens())) {
                        appraisalApplyItem.setScoreContens(appraisalBasicItemsVo.getScoreContens());
                    }
                }

                if (StringUtils.isEmpty(appraisalApplyItem.getId())) {
                    appraisalApplyItem.setId(UUIDUtils.getUUID());
                    appraisalApplyItemMapper.insertSelective(appraisalApplyItem);
                } else {
                    //避免页面传入修改
                    appraisalApplyItem.setYn(null);
                    appraisalApplyItem.setId(appraisalBasicItemsVo.getId());
                    appraisalApplyItemMapper.updateByPrimaryKeySelective(appraisalApplyItem);
                }
            }
            appraisalApply.setTotalScoreFinal(totalScoreFinal);
            //县局
            double aDouble = new Double(String.valueOf(totalScoreFinal));
            BigDecimal reducedScoreSum = new BigDecimal("0");
            if (appraisalApplyVo.getOrgType().equals(Constant.INTERGRATE_COUNTY)) {
                double all = aDouble / 51 * 4;
                reducedScoreSum = new BigDecimal(all);
            }
            //市局
            if (appraisalApplyVo.getOrgType().equals(Constant.INTERGRATE_CITY)) {
                double reduceScoreFinal = aDouble / 110 * 60;
                appraisalApply.setReducedScoreFinal(new BigDecimal(reduceScoreFinal));
                //额外分
                BigDecimal totalScoreExt = new BigDecimal("0");
                if (!StringUtils.isEmpty(appraisalApplyVo.getTotalScoreExt())) {
                    totalScoreExt = appraisalApplyVo.getTotalScoreExt();
                    appraisalApply.setTotalScoreExt(totalScoreExt);
                }
                double totalScoreSum = reduceScoreFinal + new Double(String.valueOf(totalScoreExt));
                appraisalApply.setTotalScoreSum(new BigDecimal(totalScoreSum));

                double all = (totalScoreSum) * 0.04;
                reducedScoreSum = new BigDecimal(all);
//                if (reducedScoreSum.intValue() > 4) {
//                    reducedScoreSum = new BigDecimal("4.0");
//                }

            }
            appraisalApply.setReducedScoreSum(reducedScoreSum);

        }

        AppraisalAdvices appraisalAdvices = new AppraisalAdvices();
        appraisalAdvices.setId(UUIDUtils.getUUID());
        appraisalAdvices.setApplyId(appraisalApplyVo.getId());
        if (appraisalApplyVo.getIscommit() == 0) {
            appraisalAdvices.setAdvicesContents(loginAppUser.getManageOrgName() + "保存");
        } else if (appraisalApplyVo.getIscommit() == 1) {
            appraisalApply.setFlowStatus(Constant.FLOW_STATUS_YES);
            appraisalApply.setAuditGrade(Constant.FLAG_THREE);
            appraisalAdvices.setAdvicesContents(loginAppUser.getManageOrgName() + "审核");
            appraisalApply.setFlowUser("无");
            appraisalApply.setFlowStatus(Constant.FINISH);
        }


        appraisalApplyMapper.updateByPrimaryKeySelective(appraisalApply);
        appraisalAdvices.setClassifiles(Constant.FLAG_YES);
        appraisalAdvicesMapper.insertSelective(appraisalAdvices);

        if (StringUtils.isEmpty(appraisalApplyVo.getId())) {
            //新增
            appraisalApply.setId(UUIDUtils.getUUID());
            appraisalApplyMapper.insertSelective(appraisalApply);
        } else {
            //避免页面传入修改
            appraisalApply.setYn(null);
            appraisalApply.setId(appraisalApplyVo.getId());
            appraisalApplyMapper.updateByPrimaryKeySelective(appraisalApply);
        }

    }


    /**
     * @param appraisalApplyVo 评价申请数据 json
     * @return RestApiResponse<?>
     * @Description: 市局审核评价申请
     * @Author: yujianfei
     */
    @Override
    @Transactional(readOnly = false)
    public void auditByCity(AppraisalApplyVo appraisalApplyVo) {
        if (appraisalApplyVo == null) {
            throw new ServiceException("数据异常");
        }
        SysUser loginAppUser = (SysUser) AppUserUtil.getLoginAppUser();
        AppraisalApply appraisalApply = new AppraisalApply();
        appraisalApply.setId(appraisalApplyVo.getId());
        appraisalApply.setFlowStatus(Constant.FLOW_STATUS_DPSH);
        appraisalApply.setAuditGrade(Constant.FLAG_TWO);
        appraisalApply.setIscommit(Constant.FLAG_YES);
        appraisalApply.setFlowUser(sysUserMapper.findOneProvinceUser().getId());
        appraisalApply.setFlowUserName(sysUserMapper.findOneProvinceUser().getNickname());

        BigDecimal totalScoreFinal = new BigDecimal("0");
        //县局
        double aDouble = new Double(String.valueOf(totalScoreFinal));
        BigDecimal reducedScoreSum = new BigDecimal("0");
        if (appraisalApplyVo.getOrgType().equals(Constant.INTERGRATE_COUNTY)) {
            double all = aDouble / 51 * 4;
            reducedScoreSum = new BigDecimal(all);
        }
        //市局
        if (appraisalApplyVo.getOrgType().equals(Constant.INTERGRATE_CITY)) {
            double reduceScoreFinal = aDouble / 110 * 60;
            appraisalApply.setReducedScoreFinal(new BigDecimal(reduceScoreFinal));
            //额外分
            BigDecimal totalScoreExt = new BigDecimal("0");
            if (!StringUtils.isEmpty(appraisalApplyVo.getTotalScoreExt())) {
                totalScoreExt = appraisalApplyVo.getTotalScoreExt();
                appraisalApply.setTotalScoreExt(totalScoreExt);
            }
            double totalScoreSum = reduceScoreFinal + new Double(String.valueOf(totalScoreExt));
            appraisalApply.setTotalScoreSum(new BigDecimal(totalScoreSum));

            double all = (totalScoreSum) * 0.04;
            reducedScoreSum = new BigDecimal(all);
        }
        appraisalApply.setReducedScoreSum(reducedScoreSum);

        appraisalApplyMapper.updateByPrimaryKeySelective(appraisalApply);

        AppraisalAdvices appraisalAdvices = new AppraisalAdvices();
        appraisalAdvices.setId(UUIDUtils.getUUID());
        appraisalAdvices.setApplyId(appraisalApplyVo.getId());
        if (!StringUtils.isEmpty(appraisalAdvices.getAdvicesContents())) {
            appraisalAdvices.setAdvicesContents(appraisalAdvices.getAdvicesContents());
        } else {
            appraisalAdvices.setAdvicesContents(loginAppUser.getManageOrgName() + "审核");
        }
        appraisalAdvices.setClassifiles(Constant.FLAG_YES);
        appraisalAdvicesMapper.insertSelective(appraisalAdvices);


    }

    /**
     * @param appraisalApplyVo 评价申请数据 json
     * @return RestApiResponse<?>
     * @Description: 退回评价申请
     * @Author: yujianfei
     */
    @Override
    @Transactional(readOnly = false)
    public void sendBack(AppraisalApplyVo appraisalApplyVo) {
        if (appraisalApplyVo == null) {
            throw new ServiceException("数据异常");
        }
        Example adviceExample = new Example(AppraisalAdvices.class);
        Criteria adviceCriteria = adviceExample.createCriteria();
        adviceCriteria.andEqualTo("yn", Constant.FLAG_YES);
        adviceCriteria.andEqualTo("applyId", appraisalApplyVo.getId());
        adviceExample.orderBy("createTime").desc();
        List<AppraisalAdvices> advicesList = appraisalAdvicesMapper.selectByExample(adviceExample);
        //将最近的一条设置为0
        //advicesList.get(0).setYn(Constant.FLAG_NO);
        //appraisalAdvicesMapper.updateByPrimaryKeySelective(advicesList.get(0));

        AppraisalApply appraisalApply = new AppraisalApply();
        appraisalApply.setId(appraisalApplyVo.getId());
        appraisalApply.setAuditGrade(CommonConstant.FLAG_NO);
        appraisalApply.setFlowStatus(Constant.FLOW_STATUS_DTJ);
        appraisalApply.setIscommit(CommonConstant.FLAG_NO);
        appraisalApply.setFlowUser(sysUserMapper.findByUsername(advicesList.get(0).getCreateUser()).getId());
        appraisalApply.setFlowUserName(sysUserMapper.findByUsername(advicesList.get(0).getCreateUser()).getNickname());
        appraisalApplyMapper.updateByPrimaryKeySelective(appraisalApply);

        AppraisalAdvices appraisalAdvices = new AppraisalAdvices();
        appraisalAdvices.setId(UUIDUtils.getUUID());
        appraisalAdvices.setApplyId(appraisalApplyVo.getId());
        appraisalAdvices.setAdvicesContents(appraisalApplyVo.getAdvicesContents());
        appraisalAdvices.setClassifiles(Constant.FLAG_NO);
        appraisalAdvicesMapper.insertSelective(appraisalAdvices);
//        appraisalAdvices.setYn(Constant.FLAG_NO);
//        appraisalAdvicesMapper.updateByPrimaryKeySelective(appraisalAdvices);


    }

    /**
     * @return RestApiResponse<?>
     * @Description: 初始化评价
     * @Author: liuwei
     */
    @Override
    @Transactional(readOnly = false)
    public void initApply(AppraisalApplyVo appraisalApplyVo) {
        if (appraisalApplyVo == null || appraisalApplyVo.getYearNo() == null) {
            throw new ServiceException("数据异常");
        }
        List<BasicManageOrgVo> listOrg = basicManageOrgMapper.findOrgNoPrivince(appraisalApplyVo.getYearNo());

        AppraisalBasicItemsVo appraisalBasicItemsVo = new AppraisalBasicItemsVo();
        appraisalBasicItemsVo.setOrgType(Constant.INTERGRATE_CITY);
        appraisalBasicItemsVo.setPageNum(Constant.FLAG_NO);
        appraisalBasicItemsVo.setPageSize(99);
        appraisalBasicItemsVo.setYearNo(appraisalApplyVo.getYearNo());
        List<AppraisalBasicItems> listCityItems = appraisalBasicItemsService.findPageByQuery(appraisalBasicItemsVo).getList();
        appraisalBasicItemsVo.setOrgType(Constant.INTERGRATE_COUNTY);
        List<AppraisalBasicItems> listCountyItems = appraisalBasicItemsService.findPageByQuery(appraisalBasicItemsVo).getList();

        listOrg.forEach(morg -> {
            SysUserVo sysUserVo = sysUserMapper.getUserByMorgId(morg.getId());

            if (sysUserVo != null) {
                AppraisalApply appraisalApply = new AppraisalApply();
                appraisalApply.setId(UUIDUtils.getUUID());
                appraisalApply.setYearNo(appraisalApplyVo.getYearNo());
                appraisalApply.setOrgType(sysUserVo.getAreaCode().endsWith("00") ? Constant.INTERGRATE_CITY : Constant.INTERGRATE_COUNTY);
                appraisalApply.setFlowStatus(Constant.FLOW_STATUS_DTJ);
                appraisalApply.setAuditGrade(Constant.FLAG_NO);
                appraisalApply.setIscommit(Constant.FLAG_NO);
                appraisalApply.setManageOrgId(morg.getId());
                appraisalApply.setManageOrgName(morg.getOrgName());
                appraisalApply.setFlowUser(sysUserVo.getId());
                appraisalApply.setFlowUserName(sysUserVo.getNickname());

                if (sysUserVo.getAreaCode().endsWith("00")) {
                    for (AppraisalBasicItems appraisalBasicItems : listCityItems) {
                        AppraisalApplyItem appraisalApplyItem = new AppraisalApplyItem();
                        BeanUtils.copyProperties(appraisalBasicItems, appraisalApplyItem);
                        appraisalApplyItem.setId(UUIDUtils.getUUID());
                        appraisalApplyItem.setItemId(appraisalBasicItems.getId());
                        appraisalApplyItem.setApplyId(appraisalApply.getId());
                        appraisalApplyItemMapper.insertSelective(appraisalApplyItem);
                    }
                } else {
                    for (AppraisalBasicItems appraisalBasicItems : listCountyItems) {
                        AppraisalApplyItem appraisalApplyItem = new AppraisalApplyItem();
                        BeanUtils.copyProperties(appraisalBasicItems, appraisalApplyItem);
                        appraisalApplyItem.setId(UUIDUtils.getUUID());
                        appraisalApplyItem.setItemId(appraisalBasicItems.getId());
                        appraisalApplyItem.setApplyId(appraisalApply.getId());
                        appraisalApplyItemMapper.insertSelective(appraisalApplyItem);
                    }
                }
                appraisalApplyMapper.insertSelective(appraisalApply);
            }
        });
    }


    /**
     * @return RestApiResponse<?>
     * @Description: 待办数据
     * @Author: yujianfei
     */
    @Override
    public int toDoCount(SysUser user) {
        Example example = new Example(AppraisalApply.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        int count = 0;
        SysUser loginAppUser = (SysUser) AppUserUtil.getLoginAppUser();
        Example countExample = new Example(AppraisalApply.class);
        Criteria countCriteria = countExample.createCriteria();
        countCriteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        countCriteria.andEqualTo("flowUser", loginAppUser.getId());
        List<AppraisalApply> applyList = appraisalApplyMapper.selectByExample(countExample);
        count = applyList.size();
        return count;
    }


    /**
     * @param appraisalApplyVo
     * @return PageInfo<AppraisalApply>
     * @Description: 分页查询评价申请
     * @Author: yujianfei
     */
    @Override
    public PageInfo<AppraisalApply> findFinish(AppraisalApplyVo appraisalApplyVo) {
        PageHelper.startPage(appraisalApplyVo.getPageNum(), appraisalApplyVo.getPageSize());
        SysUser loginAppUser = (SysUser) AppUserUtil.getLoginAppUser();
        assert loginAppUser != null;
        List<AppraisalApply> appraisalApplyList = appraisalApplyMapper.findFinish(loginAppUser.getUsername(), appraisalApplyVo.getManageOrgName());
        return new PageInfo<AppraisalApply>(appraisalApplyList);
    }

    /**
     * @param appraisalApplyVo 评价申请 查询条件
     * @return RestApiResponse<?>
     * @Description: 搜索
     * @Author: yujianfei
     */
    @Override
    public PageInfo<AppraisalApply> search(AppraisalApplyVo appraisalApplyVo) {
        PageHelper.startPage(appraisalApplyVo.getPageNum(), appraisalApplyVo.getPageSize());
        Example example = new Example(AppraisalApply.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        if (!StringUtils.isEmpty(appraisalApplyVo.getManageOrgName())) {
            criteria.andLike("manageOrgName", "%" + appraisalApplyVo.getManageOrgName() + "%");
            List<AppraisalApply> appraisalApplyList = appraisalApplyMapper.selectByExample(example);
            return new PageInfo<AppraisalApply>(appraisalApplyList);
        }
        return new PageInfo<AppraisalApply>();

    }

    /**
     * @return RestApiResponse<?>
     * @Description: 是否存在下属技术机构
     * @Author: yujianfei
     */
    @Override
    @Transactional(readOnly = false)
    public void noOrgName(String id) {
        Example example = new Example(AppraisalApply.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", Constant.FLAG_YES);
        criteria.andEqualTo("id", id);
        List<AppraisalApply> appraisalApplies = appraisalApplyMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(appraisalApplies)) {
            appraisalApplies.get(0).setAuditGrade(Constant.FLAG_THREE);
            appraisalApplies.get(0).setIscommit(Constant.FLAG_YES);
            appraisalApplies.get(0).setFlowStatus(Constant.FLOW_STATUS_YES);
            appraisalApplies.get(0).setNoScienceOrg(Constant.FLAG_YES);
            appraisalApplies.get(0).setFlowUser(Constant.WU);
            appraisalApplies.get(0).setFlowStatus(Constant.FINISH);
            appraisalApplyMapper.updateByPrimaryKey(appraisalApplies.get(0));

            //添加意见
            AppraisalAdvices appraisalAdvices = new AppraisalAdvices();
            appraisalAdvices.setId(UUIDUtils.getUUID());
            appraisalAdvices.setApplyId(id);
            appraisalAdvices.setAdvicesContents(appraisalApplies.get(0).getManageOrgName() + "提交确认：为无技术机构的县级局");
            appraisalAdvicesMapper.insertSelective(appraisalAdvices);

        }

    }


    /**
     * @param appraisalApplyVo 评价申请 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询考核统计表（县（市、区））
     * @Author: yujianfei
     * @Date: 2021/12/02
     */
    @Override
    public PageInfo<AppraisalApplyVo> findCountyStatistic(AppraisalApplyVo appraisalApplyVo) {
        if (null == appraisalApplyVo) {
            throw new ServiceException("参数异常");
        }
        List<AppraisalApplyVo> applyList = appraisalApplyMapper.findStatisticByOrgType(Constant.INTERGRATE_COUNTY);
        //地区搜索
        applyList = exportSearchCond(appraisalApplyVo.getAreaCode(), applyList);


        Map<String, List<AppraisalApplyVo>> map = new HashMap<>();
        for(AppraisalApplyVo vo : applyList) {
            String key = vo.getAreaCode().substring(0,4);
            if(map.containsKey(key)) {
                //map中存在以此id作为的key，将数据存放当前key的map中
                map.get(key).add(vo);
            } else {
                //map中不存在以此id作为的key，新建key用来存放数据
                List<AppraisalApplyVo> voList = new ArrayList<>();
                voList.add(vo);
                map.put(key, voList);
            }
        }

        if (!CollectionUtils.isEmpty(applyList)) {
            for (AppraisalApplyVo appraisalApply : applyList) {
                //计算平均分
                BigDecimal averageReducedScoreSum = new BigDecimal("0.00");
                int averageSize = 0;
                for (AppraisalApplyVo applyVo : map.get(appraisalApply.getAreaCode().substring(0,4))) {
                    if (!Constant.FLAG_YES.equals(applyVo.getNoScienceOrg())) {
                        if (!StringUtils.isEmpty(applyVo.getReducedScoreSum() )) {
                            averageReducedScoreSum = averageReducedScoreSum.add(applyVo.getReducedScoreSum());
                            averageSize++;
                        }
                    }
                }

                BasicManageOrg basicManageOrg = basicManageOrgMapper.selectByPrimaryKey(appraisalApply.getManageOrgId());
                appraisalApply.setCity(basicManageOrg.getAreaName().substring(0, 3));
                appraisalApply.setCounty(basicManageOrg.getAreaName().substring(3, 6));
                appraisalApply.setAreaCode(basicManageOrg.getAreaCode());
                if (Constant.FLAG_YES.equals(appraisalApply.getNoScienceOrg())) {
                    if (averageSize != Constant.FLAG_NO) {
                        appraisalApply.setReducedScoreSum((BigDecimal.valueOf(new Double(String.valueOf(new Double(String.valueOf(averageReducedScoreSum)) / averageSize)))).setScale(2, BigDecimal.ROUND_DOWN));
                    } else {
                        appraisalApply.setReducedScoreSum(null);
                    }
                    appraisalApply.setRemark("为无技术机构的县级局");
                    appraisalApply.setTotalScoreSelf(null);
                    appraisalApply.setTotalScoreFinal(null);
                }
            }
        }

        // 是否存在下属机构
        if (!StringUtils.isEmpty(appraisalApplyVo.getNoScienceOrg())) {
            applyList = applyList.stream().filter(
                    p -> p.getNoScienceOrg().equals(appraisalApplyVo.getNoScienceOrg())
            ).collect(Collectors.toList());
        }

        int pageNum = appraisalApplyVo.getPageNum();
        int pageSize = appraisalApplyVo.getPageSize();
        int start = (pageNum - 1) * pageSize;
        List<AppraisalApplyVo> collect = applyList.stream().skip(start).limit(pageSize).collect(Collectors.toList());
        PageInfo<AppraisalApplyVo> pageInfo = new PageInfo<>(collect);
        pageInfo.setTotal(applyList.size());
        return pageInfo;
    }


    /**
     * 搜索条件【地区】
     *
     * @param areaCode
     * @param appraisalApplyList
     * @Author: yujianfei
     * @Date: 2021/12/02
     */
    private List<AppraisalApplyVo> exportSearchCond(String areaCode, List<AppraisalApplyVo> appraisalApplyList) {
        //省
        if (!StringUtils.isEmpty(areaCode) && areaCode.endsWith("0000")) {
            appraisalApplyList = appraisalApplyList.stream().filter(
                    p -> p.getAreaCode().substring(0, 2).equals(areaCode.substring(0, 2))).collect(Collectors.toList());
        }
        //市
        if (!StringUtils.isEmpty(areaCode) && areaCode.endsWith("00") && !areaCode.endsWith("0000")) {
            if (!StringUtils.isEmpty(areaCode)) {
                appraisalApplyList = appraisalApplyList.stream().filter(
                        p -> p.getAreaCode().substring(0, 4).equals(areaCode.substring(0, 4))).collect(Collectors.toList());
            }
        }
        //区县
        if (!StringUtils.isEmpty(areaCode) && !areaCode.endsWith("00")) {
            appraisalApplyList = appraisalApplyList.stream().filter(
                    p -> p.getAreaCode().equals(areaCode)).collect(Collectors.toList());
        }
        return appraisalApplyList;
    }


    /**
     * @param appraisalApplyVo 评价申请 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询考核统计表【市】
     * @Author: yujianfei
     * @Date: 2021/12/03
     */
    @Override
    public PageInfo<AppraisalApplyVo> findCityStatistic(AppraisalApplyVo appraisalApplyVo) {
        if (null == appraisalApplyVo) {
            throw new ServiceException("参数异常");
        }
        PageHelper.startPage(appraisalApplyVo.getPageNum(), appraisalApplyVo.getPageSize());
        List<AppraisalApplyVo> applyList = appraisalApplyMapper.findStatisticByOrgType(Constant.INTERGRATE_CITY);
        if (!CollectionUtils.isEmpty(applyList)) {
            for (AppraisalApplyVo appraisalApply : applyList) {
                BasicManageOrg basicManageOrg = basicManageOrgMapper.selectByPrimaryKey(appraisalApply.getManageOrgId());
                appraisalApply.setCity(basicAreacodeMapper.findByAreaCode(basicManageOrg.getAreaCode()).getAreaName());
                appraisalApply.setAreaCode(basicManageOrg.getAreaCode());
            }
        }
        //地区搜索
        applyList = exportSearchCond(appraisalApplyVo.getAreaCode(), applyList);
        return new PageInfo<AppraisalApplyVo>(applyList);
    }


    /**
     * @param appraisalApplyVo void
     * @Description:导出考核统计表
     * @Author: yujianfei
     * @Date: 2021/12/02
     */
    @Override
    public List<AppraisalApplyCountyExportVo> exportCounty(AppraisalApplyVo appraisalApplyVo) {
        if (null == appraisalApplyVo) {
            throw new ServiceException("参数异常");
        }
        appraisalApplyVo.setPageSize(null);
        appraisalApplyVo.setPageNum(null);
        List<AppraisalApplyVo> applyList = appraisalApplyMapper.findStatisticByOrgType(Constant.INTERGRATE_COUNTY);

        //地区搜索
        applyList = exportSearchCond(appraisalApplyVo.getAreaCode(), applyList);

        Map<String, List<AppraisalApplyVo>> map = new HashMap<>();
        for(AppraisalApplyVo vo : applyList) {
            String key = vo.getAreaCode().substring(0,4);
            if(map.containsKey(key)) {
                //map中存在以此id作为的key，将数据存放当前key的map中
                map.get(key).add(vo);
            } else {
                //map中不存在以此id作为的key，新建key用来存放数据
                List<AppraisalApplyVo> voList = new ArrayList<>();
                voList.add(vo);
                map.put(key, voList);
            }
        }

        List<AppraisalApplyCountyExportVo> applyExportVoList = new LinkedList<>();
        if (!CollectionUtils.isEmpty(applyList)) {
            for (AppraisalApplyVo appraisalApplyvo : applyList) {
                BasicManageOrg basicManageOrg = basicManageOrgMapper.selectByPrimaryKey(appraisalApplyvo.getManageOrgId());
                appraisalApplyvo.setCity(basicManageOrg.getAreaName().substring(0, 3));
                appraisalApplyvo.setCounty(basicManageOrg.getAreaName().substring(3, 6));
                appraisalApplyvo.setAreaCode(basicManageOrg.getAreaCode());
            }
        }


        if (!CollectionUtils.isEmpty(applyList)) {

            for (int i = 0; i < applyList.size(); i++) {
                // 杭州市只计算下级所有区县
                //计算平均分
                BigDecimal averageReducedScoreSum = new BigDecimal("0.00");
                int averageSize = 0;
                for (AppraisalApplyVo applyVo : map.get(applyList.get(i).getAreaCode().substring(0,4))) {
                    if (!Constant.FLAG_YES.equals(applyVo.getNoScienceOrg())) {
                        if (!StringUtils.isEmpty(applyVo.getReducedScoreSum() )) {
                            averageReducedScoreSum = averageReducedScoreSum.add(applyVo.getReducedScoreSum());
                            averageSize++;
                        }
                    }
                }

                AppraisalApplyCountyExportVo appraisalApplyExportVo = new AppraisalApplyCountyExportVo();
                BeanUtils.copyProperties(applyList.get(i), appraisalApplyExportVo);
                appraisalApplyExportVo.setSort(i + 1);
                appraisalApplyExportVo.setReducedScoreSum(applyList.get(i).getReducedScoreSum());
                appraisalApplyExportVo.setTotalScoreSelfSecond(appraisalApplyExportVo.getTotalScoreSelf());
                if (Constant.FLAG_YES.equals(applyList.get(i).getNoScienceOrg())) {
                    //appraisalApplyExportVo.setReducedScoreSum(new BigDecimal(new Double(String.valueOf(new Double(String.valueOf(averageReducedScoreSum)) / averageSize))));
                    if (averageSize != 0) {
                        appraisalApplyExportVo.setReducedScoreSum(BigDecimal.valueOf(new Double(String.valueOf(new Double(String.valueOf(averageReducedScoreSum)) / averageSize))).setScale(2, BigDecimal.ROUND_DOWN));
                    } else {
                        appraisalApplyExportVo.setReducedScoreSum(null);
                    }
                    appraisalApplyExportVo.setRemark("为无技术机构的县级局");
                    appraisalApplyExportVo.setTotalScoreSelf(null);
                    appraisalApplyExportVo.setTotalScoreSelfSecond(null);
                    appraisalApplyExportVo.setTotalScoreFinal(null);
                }
                applyExportVoList.add(appraisalApplyExportVo);
            }
            // 是否存在下属机构
            if (!StringUtils.isEmpty(appraisalApplyVo.getNoScienceOrg())) {
                if (Constant.FLAG_YES.equals(appraisalApplyVo.getNoScienceOrg())) {
                    applyExportVoList = applyExportVoList.stream().filter(p -> !StringUtils.isEmpty(p.getRemark())).collect(Collectors.toList());
                } else if (Constant.FLAG_NO.equals(appraisalApplyVo.getNoScienceOrg())) {
                    applyExportVoList = applyExportVoList.stream().filter(p -> StringUtils.isEmpty(p.getRemark())).collect(Collectors.toList());
                }
                for (int i = 0; i < applyExportVoList.size(); i++) {
                    applyExportVoList.get(i).setSort(i + 1);
                }
            }
        }
        return applyExportVoList;
    }


    /**
     * @param appraisalApplyVo void
     * @Description:根据orgType查询所有导出考核统计
     * @Author: yujianfei
     * @Date: 2021/12/03
     */
    private List<AppraisalApply> getExportByOrgType(AppraisalApplyVo appraisalApplyVo, String orgType) {
        Example example = new Example(AppraisalApply.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", Constant.FLAG_YES);
        if (!StringUtils.isEmpty(appraisalApplyVo.getOrgType())) {
            criteria.andEqualTo("orgType", orgType);
        }
        example.orderBy("reducedScoreSum").desc();
        return appraisalApplyMapper.selectByExample(example);
    }


    /**
     * @param appraisalApplyVo void
     * @Description:导出考核统计表
     * @Author: yujianfei
     * @Date: 2021/12/02
     */
    @Override
    public List<AppraisalApplyCityExportVo> exportCity(AppraisalApplyVo appraisalApplyVo) {
        if (null == appraisalApplyVo) {
            throw new ServiceException("参数异常");
        }
        appraisalApplyVo.setPageSize(null);
        appraisalApplyVo.setPageNum(null);
        List<AppraisalApplyVo> applyVoList = null;
        List<AppraisalApply> applyList = getExportByOrgType(appraisalApplyVo, Constant.INTERGRATE_CITY);
        List<AppraisalApplyVo> appraisalApplyList = new LinkedList<>();
        if (!CollectionUtils.isEmpty(applyList)) {
            for (AppraisalApply appraisalApply : applyList) {
                AppraisalApplyVo addAppraisalApplyVo = new AppraisalApplyVo();
                BeanUtils.copyProperties(appraisalApply, addAppraisalApplyVo);
                BasicManageOrg basicManageOrg = basicManageOrgMapper.selectByPrimaryKey(appraisalApply.getManageOrgId());
                addAppraisalApplyVo.setCity(basicAreacodeMapper.findByAreaCode(basicManageOrg.getAreaCode()).getAreaName());
                addAppraisalApplyVo.setAreaCode(basicManageOrg.getAreaCode());
                appraisalApplyList.add(addAppraisalApplyVo);
            }
        }
        //地区搜索
        applyVoList = exportSearchCond(appraisalApplyVo.getAreaCode(), appraisalApplyList);
        List<AppraisalApplyCityExportVo> applyExportVoList = new LinkedList<>();
        if (!CollectionUtils.isEmpty(applyVoList)) {
            for (int i = 0; i < applyVoList.size(); i++) {
                AppraisalApplyCityExportVo appraisalApplyExportVo = new AppraisalApplyCityExportVo();
                BeanUtils.copyProperties(applyVoList.get(i), appraisalApplyExportVo);
                appraisalApplyExportVo.setReducedScoreSum(applyVoList.get(i).getReducedScoreSum());
                appraisalApplyExportVo.setSort(i + 1);
                appraisalApplyExportVo.setTotalScoreSelf(appraisalApplyExportVo.getTotalScoreSelf());
                applyExportVoList.add(appraisalApplyExportVo);
            }
        }
        for (int i = 0; i < applyExportVoList.size(); i++) {
            applyExportVoList.get(i).setSort(i + 1);
        }
        return applyExportVoList;
    }


    /**
     * @return RestApiResponse<?>
     * @Description: 撤回到上一步
     * @Author: yujianfei
     * @Date: 2021/12/03
     */
    @Override
    @Transactional(readOnly = false)
    public void recall(String id) {
        if (null == id) {
            throw new ServiceException("参数异常");
        }
        SysUser loginAppUser = (SysUser) AppUserUtil.getLoginAppUser();
        if (null == loginAppUser) {
            throw new ServiceException("参数异常");
        }
        AppraisalApply appraisalApply = appraisalApplyMapper.selectByPrimaryKey(id);

        Example adviceExample = new Example(AppraisalAdvices.class);
        Criteria adviceCriteria = adviceExample.createCriteria();
        adviceCriteria.andEqualTo("yn", Constant.FLAG_YES);
        adviceCriteria.andEqualTo("applyId", id);
        adviceExample.orderBy("createTime").desc();
        List<AppraisalAdvices> advicesList = appraisalAdvicesMapper.selectByExample(adviceExample);
        //将最近的一条设置为0
        advicesList.get(0).setYn(Constant.FLAG_NO);
        appraisalAdvicesMapper.updateByPrimaryKeySelective(advicesList.get(0));
        if (!CollectionUtils.isEmpty(advicesList)) {
            // 添加撤回意见
            AppraisalAdvices appraisalAdvices = new AppraisalAdvices();
            appraisalAdvices.setId(UUIDUtils.getUUID());
            appraisalAdvices.setApplyId(id);
            appraisalAdvices.setAdvicesContents(loginAppUser.getManageOrgName() + "撤回");

            appraisalAdvicesMapper.insertSelective(appraisalAdvices);
            appraisalAdvices.setYn(Constant.FLAG_NO);
            appraisalAdvicesMapper.updateByPrimaryKeySelective(appraisalAdvices);

            //县局考核
            if (Constant.INTERGRATE_COUNTY.equals(appraisalApply.getOrgType())) {
                //县局用户撤回
                if (Constant.FLAG_YES.equals(appraisalApply.getAuditGrade())) {
                    appraisalApply.setFlowStatus(Constant.FLOW_STATUS_DTJ);
                    appraisalApply.setAuditGrade(Constant.FLAG_NO);
                    appraisalApply.setIscommit(Constant.FLAG_NO);
                }
                // 无下属机构
                if (Constant.FLAG_YES.equals(appraisalApply.getNoScienceOrg())) {
                    appraisalApply.setFlowStatus(Constant.FLOW_STATUS_DTJ);
                    appraisalApply.setAuditGrade(Constant.FLAG_NO);
                    appraisalApply.setIscommit(Constant.FLAG_NO);
                    appraisalApply.setNoScienceOrg(Constant.FLAG_NO);
                }
                //市局用户撤回
                if (Constant.FLAG_TWO.equals(appraisalApply.getAuditGrade())) {
                    appraisalApply.setFlowStatus(Constant.FLOW_STATUS_DCSH);
                    appraisalApply.setAuditGrade(Constant.FLAG_YES);
                    appraisalApply.setIscommit(Constant.FLAG_YES);
                }
                //市局考核
            } else if (Constant.INTERGRATE_CITY.equals(appraisalApply.getOrgType())) {
                appraisalApply.setFlowStatus(Constant.FLOW_STATUS_DTJ);
                appraisalApply.setAuditGrade(Constant.FLAG_NO);
                appraisalApply.setIscommit(Constant.FLAG_NO);
            }
            appraisalApply.setFlowUser(loginAppUser.getId());
            appraisalApply.setFlowUserName(loginAppUser.getNickname());
            appraisalApplyMapper.updateByPrimaryKey(appraisalApply);
        }
    }

    /**
     * @Description: 考核进度
     * @Author: yujianfei
     * @Date: 2021-12-10
     */
    @Override
    public PageInfo<AppraisalApplyAssessmentExportVo> assessmentProgress(AppraisalApplyVo appraisalApplyVo) {
        if (null == appraisalApplyVo) {
            throw new ServiceException("参数异常");
        }
        appraisalApplyVo.setPageSize(null);
        appraisalApplyVo.setPageNum(null);
        List<AppraisalApplyAssessmentExportVo> applyList = appraisalApplyMapper.assessmentProgress();
        return new PageInfo<AppraisalApplyAssessmentExportVo>(applyList);
    }


    /**
     * @Description: 导出考核进度
     * @Author: yujianfei
     * @Date: 2021-12-10
     */
    @Override
    public List<AppraisalApplyAssessmentExportVo> exportAssessment(AppraisalApplyVo appraisalApplyVo) {
        if (null == appraisalApplyVo) {
            throw new ServiceException("参数异常");
        }
        appraisalApplyVo.setPageSize(null);
        appraisalApplyVo.setPageNum(null);
        List<AppraisalApplyAssessmentExportVo> applyList = appraisalApplyMapper.assessmentProgress();
        return applyList;
    }


    /**
     * @param appraisalApplyVo void
     * @Description:导出已处理的单条
     * @Author: yujianfei
     * @Date: 2021/12/13
     */
    @Override
    public List<AppraisalApplyFinishExportVo> exportOneFinish(AppraisalApplyVo appraisalApplyVo) {
        if (null == appraisalApplyVo) {
            throw new ServiceException("参数异常");
        }
        List<AppraisalApplyFinishExportVo> list = appraisalApplyItemMapper.exportOneFinish(appraisalApplyVo.getId());
        List<AppraisalApplyFinishExportVo> exportList = new LinkedList<>();
        for (int i = 0; i < list.size(); i++) {
            AppraisalApplyFinishExportVo appraisalApplyFinishExportVo = new AppraisalApplyFinishExportVo();
            BeanUtils.copyProperties(list.get(i), appraisalApplyFinishExportVo);
            if (i >0 && !StringUtils.isEmpty(list.get(i).getFileApplyId()) && list.get(i-1).getFileApplyId().equals(list.get(i).getFileApplyId())) {
//                list.get(i-1).setName(list.get(i-1).getName() + "\n" + list.get(i).getName());
//                list.get(i).setName(null);
                appraisalApplyFinishExportVo.setName(list.get(i-1).getName() + "\n" + list.get(i).getName());
            }
            exportList.add(appraisalApplyFinishExportVo);
        }

        return exportList;
    }
}
