package com.fd.stdp.service.innovation.impl;

import java.util.ArrayList;
import java.util.List;

import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import com.fd.stdp.beans.innovation.InnovationLaboratoryAcceptComment;
import com.fd.stdp.beans.innovation.InnovationLaboratoryApplyComment;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryApplyCommentVo;
import com.fd.stdp.beans.project.ProjectApplyExpertMumber;
import com.fd.stdp.dao.basic.BasicGradeLinkedMapper;
import com.fd.stdp.dao.innovation.InnovationLaboratoryApplyCommentMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.basic.BasicGradeLinkedService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationLaboratoryAccept;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryAcceptVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationLaboratoryAcceptMapper;
import com.fd.stdp.service.innovation.InnovationLaboratoryAcceptService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import static com.fd.stdp.common.BaseController.getCurrentUserId;
import static com.fd.stdp.common.BaseController.getCurrentUserName;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 重点实验室培育申报任务书验收
 *@Author: wangsh
 *@Date: 2022-03-09 15:53:01
 */
public class InnovationLaboratoryAcceptServiceImpl extends BaseServiceImpl<InnovationLaboratoryAcceptMapper, InnovationLaboratoryAccept> implements InnovationLaboratoryAcceptService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationLaboratoryAcceptServiceImpl.class);
	
	@Autowired
	private InnovationLaboratoryAcceptMapper innovationLaboratoryAcceptMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicGradeLinkedService basicGradeLinkedService;
	@Autowired
	private BasicGradeLinkedMapper basicGradeLinkedMapper;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	@Autowired
	private InnovationLaboratoryApplyCommentMapper innovationLaboratoryApplyCommentMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新重点实验室培育申报任务书验收
	 *@param innovationLaboratoryAccept 重点实验室培育申报任务书验收对象
	 *@return String 重点实验室培育申报任务书验收ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationLaboratoryAccept(InnovationLaboratoryAcceptVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationLaboratoryAcceptMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationLaboratoryAcceptMapper.updateByPrimaryKeySelective(vo);
		}

		if(!CollectionUtils.isEmpty(vo.getFiles())){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除重点实验室培育申报任务书验收
	 *@param id void 重点实验室培育申报任务书验收ID
	 *@Author: wangsh
	 */
	public void deleteInnovationLaboratoryAccept(String id) {
		//TODO 做判断后方能执行删除
		InnovationLaboratoryAccept innovationLaboratoryAccept=innovationLaboratoryAcceptMapper.selectByPrimaryKey(id);
		if(innovationLaboratoryAccept==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationLaboratoryAccept teminnovationLaboratoryAccept=new InnovationLaboratoryAccept();
		teminnovationLaboratoryAccept.setYn(CommonConstant.FLAG_NO);
		teminnovationLaboratoryAccept.setId(innovationLaboratoryAccept.getId());
		innovationLaboratoryAcceptMapper.updateByPrimaryKeySelective(teminnovationLaboratoryAccept);
	}

    /**
     * @Description: 批量删除重点实验室培育申报任务书验收
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationLaboratoryAccept(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationLaboratoryAccept(id));
	}

	@Override
	/**
	 *@Description: 查询重点实验室培育申报任务书验收详情
	 *@param id
	 *@return InnovationLaboratoryAccept
	 *@Author: wangsh
	 */
	public InnovationLaboratoryAccept findById(String id) {
		InnovationLaboratoryAccept accept = innovationLaboratoryAcceptMapper.selectByPrimaryKey(id);
		InnovationLaboratoryAcceptVo vo = new InnovationLaboratoryAcceptVo();
		BeanUtils.copyProperties(accept, vo);
		Example example = new Example(InnovationLaboratoryAcceptComment.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
		List<InnovationLaboratoryApplyComment> comments = innovationLaboratoryApplyCommentMapper.selectByExample(example);
		List<InnovationLaboratoryApplyComment> commentVos = new ArrayList<>();
		comments.forEach(c->{
			InnovationLaboratoryApplyCommentVo cvo = new InnovationLaboratoryApplyCommentVo();
			BeanUtils.copyProperties(c, cvo);
			cvo.setFiles(basicFileAppendixService.findByFormId(cvo.getId()));
			commentVos.add(cvo);
		});
		vo.setExperts(commentVos);
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询重点实验室培育申报任务书验收
	 *@param innovationLaboratoryAcceptVo
	 *@return PageInfo<InnovationLaboratoryAccept>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationLaboratoryAccept> findPageByQuery(InnovationLaboratoryAcceptVo innovationLaboratoryAcceptVo) {
		PageHelper.startPage(innovationLaboratoryAcceptVo.getPageNum(),innovationLaboratoryAcceptVo.getPageSize());
		Example example=new Example(InnovationLaboratoryAccept.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationLaboratoryAcceptVo.getName())){
		//	criteria.andEqualTo(innovationLaboratoryAcceptVo.getName());
		//}
		List<InnovationLaboratoryAccept> innovationLaboratoryAcceptList=innovationLaboratoryAcceptMapper.selectByExample(example);
		return new PageInfo<InnovationLaboratoryAccept>(innovationLaboratoryAcceptList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationLaboratoryAccept(InnovationLaboratoryAcceptVo innovationLaboratoryAcceptVo) {
		String id = this.saveOrUpdateInnovationLaboratoryAccept(innovationLaboratoryAcceptVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_INNOVATION_LABORATORY_ACCEPT, innovationLaboratoryAcceptVo, this.mapper,
				StringUtils.isNotBlank(innovationLaboratoryAcceptVo.getAuditAdvice())?innovationLaboratoryAcceptVo.getAuditAdvice():"提交重点实验室培育申报任务书验收");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationLaboratoryAccept(InnovationLaboratoryAcceptVo innovationLaboratoryAcceptVo) {
		// 省局通过需要选择专家
		InnovationLaboratoryAccept old = findById(innovationLaboratoryAcceptVo.getId());
		if(StringUtils.equals(FlowStatusEnum.PROVINCE_AUDIT.getCode(), old.getFlowStatus())){
			if(CollectionUtils.isEmpty(innovationLaboratoryAcceptVo.getExperts())){
				throw new ServiceException("请选择专家");
			}
			innovationLaboratoryApplyCommentMapper.clearByFormId(innovationLaboratoryAcceptVo.getId());
			innovationLaboratoryAcceptVo.getExperts().stream().forEach(c->{
				c.setResult(null);
				c.setOpinion(null);
				c.setId(UUIDUtils.getUUID());
				c.setApplyId(innovationLaboratoryAcceptVo.getId());
				c.setLaboratory(innovationLaboratoryAcceptVo.getLaboratory());
				c.setUserName(c.getName());
				innovationLaboratoryApplyCommentMapper.insertSelective(c);
			});
		}

		flowCommonService.doFlowStepAudit(innovationLaboratoryAcceptVo, this.mapper
				, StringUtils.isNotBlank(innovationLaboratoryAcceptVo.getAuditAdvice()) ? innovationLaboratoryAcceptVo.getAuditAdvice() : "重点实验室培育申报任务书验收审核通过"
				, FlowStatusEnum.EXPERTS_GRADE.getCode(), FlowStatusEnum.EXPERTS_GRADE.getRole());
		return innovationLaboratoryAcceptVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationLaboratoryAccept(InnovationLaboratoryAcceptVo innovationLaboratoryAcceptVo) {
		flowCommonService.doFlowStepSendBack(innovationLaboratoryAcceptVo, this.mapper
				, StringUtils.isNotBlank(innovationLaboratoryAcceptVo.getAuditAdvice())?innovationLaboratoryAcceptVo.getAuditAdvice():"重点实验室培育申报任务书验收退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationLaboratoryAccept(InnovationLaboratoryAcceptVo innovationLaboratoryAcceptVo) {
		flowCommonService.doCompleteTask(innovationLaboratoryAcceptVo, this.mapper
				, "重点实验室培育申报任务书验收任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnovationLaboratoryAccept> todoList(InnovationLaboratoryAcceptVo vo) {

		Example example = new Example(InnovationLaboratoryAccept.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationLaboratoryAccept> finishedList(InnovationLaboratoryAcceptVo vo) {
		Example example = new Example(InnovationLaboratoryAccept.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnovationLaboratoryAccept> endList(InnovationLaboratoryAcceptVo vo) {
        Example example = new Example(InnovationLaboratoryAccept.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	@Override
	@Transactional(readOnly = false)
	public String expertSubmitinnovationLaboratoryAccept(InnovationLaboratoryApplyCommentVo vo) {
		String applyId = vo.getId();
		String userId = getCurrentUserId();

		Example example = new Example(InnovationLaboratoryApplyComment.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("userId", userId)
				.andEqualTo("applyId", vo.getId());
		List<InnovationLaboratoryApplyComment> comments = innovationLaboratoryApplyCommentMapper.selectByExample(example);
		if(CollectionUtils.isEmpty(comments)){
			throw new ServiceException("未找到评审记录");
		}
		InnovationLaboratoryApplyComment comment = comments.get(0);
		vo.setId(comment.getId());
		vo.setApplyId(comment.getApplyId());

		innovationLaboratoryApplyCommentMapper.updateByPrimaryKeySelective(vo);
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}

		boolean allFinish = true; // 全部评审完成
		boolean isExpertLeader = true; // 是专家组组长

		example = new Example(InnovationLaboratoryApplyComment.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", vo.getApplyId()).andIsNull("opinion");
		if (!CollectionUtils.isEmpty(innovationLaboratoryApplyCommentMapper.selectByExample(example))){
			allFinish = false;
		}

		example = new Example(ProjectApplyExpertMumber.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", vo.getApplyId())
				.andEqualTo("userId", userId).andEqualTo("expertType", 1);
		if(CollectionUtils.isEmpty(innovationLaboratoryApplyCommentMapper.selectByExample(example))){
			isExpertLeader = false;
		}
		// 全部专家评审完毕,专家组长发起至下一级
		if (allFinish && isExpertLeader){
			InnovationLaboratoryAccept accept = findById(applyId);
			flowCommonService.doCompleteTask(accept, this.mapper, "全部专家评审完成"
					, FlowStatusEnum.PROJECT_REVIEW.getCode(), FlowStatusEnum.PROJECT_REVIEW.getRole(), getCurrentUserName());
		}
//
//		String userId = getCurrentUserId();
//		Boolean nextStep = false;
//		vo.getExperts().stream().filter(grade->StringUtils.equals(userId, grade.getUserId()))
//				.forEach(grade->{
//					basicGradeLinkedMapper.updateByPrimaryKeySelective(grade);
//				});
//		if(basicGradeLinkedService.formGradeAllFinished(vo.getId())) {
//			List<BasicGradeLinkedVo> linkeds = basicGradeLinkedService.findByFormId(vo.getId());
//			for (BasicGradeLinkedVo grade:linkeds) {
//				if (StringUtils.equals(userId, grade.getUserId())) {
//					nextStep = StringUtils.equals("组长", grade.getUserType());
//				}
//			}
//		}
//		if(nextStep){
//		}
		return null;
	}

	private Criteria getCriteria(InnovationLaboratoryAccept vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getLaboratory())){
			criteria.andLike("laboratory", "%" + vo.getLaboratory()+ "%");
		}
		return criteria;
	}
}
