package com.fd.stdp.service.main;

import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.basic.vo.BasicPersonVo;
import com.fd.stdp.beans.basic.vo.BasicScienceOrgVo;
import com.fd.stdp.beans.innovation.vo.InnovationGeneralInnovationApplyVo;
import com.fd.stdp.beans.innovation.vo.InnovationQualityChangeVo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.beans.project.vo.ProjectInfoStatistics;
import com.fd.stdp.beans.tech.vo.TechAwardsApplyVo;

public interface MainPageService {
    /**
     * 机构查询
     * @param basicScienceOrg
     * @return
     */
    Object queryScienceOrg(BasicScienceOrgVo basicScienceOrg);

    Object queryInnovation(InnovationGeneralInnovationApplyVo vo);

    Object queryProject(ProjectInfoStatistics statistics);

    Object queryPerson(BasicPersonVo vo);

    Object queryTech(TechAwardsApplyVo vo);
}
