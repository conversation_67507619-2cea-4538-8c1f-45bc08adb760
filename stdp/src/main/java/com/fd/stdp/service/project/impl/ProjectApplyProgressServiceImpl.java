package com.fd.stdp.service.project.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectApplyProgress;
import com.fd.stdp.beans.project.vo.ProjectApplyProgressVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectApplyProgressMapper;
import com.fd.stdp.service.project.ProjectApplyProgressService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 项目计划进度安排Service业务层处理
 * @date 2021-11-26
 */
@Service
@Transactional(readOnly = true)
public class ProjectApplyProgressServiceImpl extends BaseServiceImpl<ProjectApplyProgressMapper, ProjectApplyProgress> implements ProjectApplyProgressService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectApplyProgressServiceImpl.class);
    @Autowired
    private ProjectApplyProgressMapper projectApplyProgressMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新项目计划进度安排
     *@param projectApplyProgress 项目计划进度安排对象
     *@return String 项目计划进度安排ID
     *@Author: yujianfei
     */
    public String saveOrUpdateProjectApplyProgress(ProjectApplyProgress projectApplyProgress) {
        if (projectApplyProgress == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(projectApplyProgress.getId())) {
            //新增
            projectApplyProgress.setId(UUIDUtils.getUUID());
            projectApplyProgressMapper.insertSelective(projectApplyProgress);
        } else {
            //避免页面传入修改
            projectApplyProgress.setYn(null);
            projectApplyProgressMapper.updateByPrimaryKeySelective(projectApplyProgress);
        }
        return projectApplyProgress.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除项目计划进度安排
     *@param id void 项目计划进度安排ID
     *@Author: yujianfei
     */
    public void deleteProjectApplyProgress(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            ProjectApplyProgress projectApplyProgress = projectApplyProgressMapper.selectByPrimaryKey(id);
            if (projectApplyProgress == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            ProjectApplyProgress temprojectApplyProgress = new ProjectApplyProgress();
            temprojectApplyProgress.setYn(CommonConstant.FLAG_NO);
            temprojectApplyProgress.setId(projectApplyProgress.getId());
            projectApplyProgressMapper.updateByPrimaryKeySelective(temprojectApplyProgress);
        }
    }

    /**
     * @param id
     * @return ProjectApplyProgress
     * @Description: 查询项目计划进度安排详情
     * @Author: yujianfei
     */
    @Override
    public ProjectApplyProgress findById(String id) {
        return projectApplyProgressMapper.selectByPrimaryKey(id);
    }


    /**
     * @param projectApplyProgressVo
     * @return PageInfo<ProjectApplyProgress>
     * @Description: 分页查询项目计划进度安排
     * @Author: yujianfei
     */
    @Override
    public PageInfo<ProjectApplyProgress> findPageByQuery(ProjectApplyProgressVo projectApplyProgressVo) {
        PageHelper.startPage(projectApplyProgressVo.getPageNum(), projectApplyProgressVo.getPageSize());
        Example example = new Example(ProjectApplyProgress.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(projectApplyProgressVo.getName())){
        //	criteria.andEqualTo(projectApplyProgressVo.getName());
        //}
        List<ProjectApplyProgress> projectApplyProgressList = projectApplyProgressMapper.selectByExample(example);
        return new PageInfo<ProjectApplyProgress>(projectApplyProgressList);
    }
}
