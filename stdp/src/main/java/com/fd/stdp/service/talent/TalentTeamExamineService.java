package com.fd.stdp.service.talent;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamExamine;
import com.fd.stdp.beans.talent.vo.TalentTeamExamineVo;
/**
 *@Description: 创新团队考核
 *@Author: wangsh
 *@Date: 2022-02-14 10:22:36
 */
public interface TalentTeamExamineService {

	/**
	 *@Description: 保存或更新创新团队考核
	 *@param talentTeamExamine 创新团队考核对象
	 *@return String 创新团队考核ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTalentTeamExamine(TalentTeamExamineVo talentTeamExamine);

	String saveOrUpdateTalentTeamExamineNoFlow(TalentTeamExamineVo talentTeamExamineVo);
	
	/**
	 *@Description: 删除创新团队考核
	 *@param id void 创新团队考核ID
	 *@Author: wangsh
	 */
	void deleteTalentTeamExamine(String id);

	/**
	 * @Description: 批量删除创新团队考核
	 * @param ids
	 */
    void deleteMultiTalentTeamExamine(List<String> ids);

	/**
	 *@Description: 查询创新团队考核详情
	 *@param id
	 *@return TalentTeamExamine
	 *@Author: wangsh
	 */
	TalentTeamExamine findById(String id);

	/**
	 *@Description: 分页查询创新团队考核
	 *@param talentTeamExamineVo
	 *@return PageInfo<TalentTeamExamine>
	 *@Author: wangsh
	 */
	PageInfo<TalentTeamExamine> findPageByQuery(TalentTeamExamineVo talentTeamExamineVo);
	
	
	/**
	 * 提交
	 * @param talentTeamExamineVo
	 * @return
	 */
    String submitTalentTeamExamine(TalentTeamExamineVo talentTeamExamineVo);

	/**
	 * 审核
	 * @param talentTeamExamineVo
	 * @return
	 */
	String auditTalentTeamExamine(TalentTeamExamineVo talentTeamExamineVo);

	/**
	 * 退回
	 * @param talentTeamExamineVo
	 * @return
	 */
	String sendBackTalentTeamExamine(TalentTeamExamineVo talentTeamExamineVo);

	/**
	 * 任务书下达
	 * @param talentTeamExamineVo
	 * @return
	 */
	String releaseTalentTeamExamine(TalentTeamExamineVo talentTeamExamineVo);
	
	/**
	 * 待办列表
	*/
    PageInfo<TalentTeamExamine> todoList(TalentTeamExamineVo talentTeamExamineVo);

	/**
	 * 已办列表
	*/
	PageInfo<TalentTeamExamine> finishedList(TalentTeamExamineVo talentTeamExamineVo);
}
