package com.fd.stdp.service.sys.impl;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.fd.stdp.beans.sys.SysFileInfo;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.dao.sys.SysFileInfoMapper;
import com.fd.stdp.enums.FileSource;
import com.fd.stdp.util.FileUtil;
import com.fd.stdp.util.ThumbnailUtils;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.util.WebDavNTLMUtils;

/**
 * webDav方式文件存储
 * 
 * <AUTHOR>
 *
 */
@Service("webdavFileServiceImpl")
public class WebdavFileServiceImpl extends AbstractFileService {
    public static final Logger logger = LoggerFactory.getLogger(WebdavFileServiceImpl.class);
    @Autowired
    private SysFileInfoMapper fileDao;
    @Value("${file.webdav.rootPath}")
    private String rootPath;
    @Value("${file.webdav.username}")
    private String user;
    @Value("${file.webdav.password}")
    private String pwd;

    /**
     * 上传文件存储在本地的根路径
     */
    @Value("${file.local.path}")
    private String localFilePath;

    @Override
    protected SysFileInfoMapper getFileDao() {
        return fileDao;
    }

    @Override
    protected FileSource fileSource() {
        return FileSource.WEBDAV;
    }

    private void checkFile(WebDavNTLMUtils webdavUtil, String path) {
        WebDavNTLMUtils webDav = WebDavNTLMUtils.initialization(rootPath, user, pwd);
        path = path.substring(1, path.length());
        List<String> pathList = Arrays.asList(path.split("/"));
        String path1 = "";
        for (int i = 0; i < pathList.size(); i++) {
            path1 += "/" + pathList.get(i);
            // 存在文件
            if (webDav.checkDirExist(path1) == 200) {
                continue;
            } else {
                try {
                    webDav.createFolder(path1);
                } catch (Exception e) {
                    logger.error("创建文件夹失败", e);
                }
            }
        }
    }

    @Override
    protected void uploadFile(MultipartFile file, SysFileInfo fileInfo) throws Exception {
        WebDavNTLMUtils webdavUtil = WebDavNTLMUtils.initialization(rootPath, user, pwd);
        // 路径中的文件夹是否存在
        checkFile(webdavUtil, fileInfo.getPath());

        int index = fileInfo.getName().lastIndexOf(".");
        // 文件扩展名
        String fileSuffix = fileInfo.getName().substring(index);

        String fileName = UUIDUtils.getUUID();
        String suffix = "/" + LocalDate.now().toString() + "/" + fileName + fileSuffix;
        String url = fileInfo.getPath() + "/";
        String path = localFilePath + suffix;

        fileInfo.setPath(path);
        fileInfo.setUrl(url + fileName + fileSuffix);
        FileUtil.saveFile(file, path);

        if (CommonConstant.FLAG_YES.equals(fileInfo.getIsimg())) {
            // 如果是图片压缩后再次上传 默认不压缩上传
            if (fileInfo.isThumbnail()) {
                ThumbnailUtils.thumbnail(path, path + "2.jpg");
                webdavUtil.upload(path + "2.jpg", url + fileName + ".compress.jpg");
                FileUtil.deleteFile(path + "2.jpg");
            }
        }
        webdavUtil.upload(path, url + fileName + fileSuffix);
        FileUtil.deleteFile(path);

    }

    public Boolean isImg(String fileSuffix) {
        switch (fileSuffix) {
            case "jpg":
            case "png":
            case "gif":
                return true;
            default:
                return false;
        }
    }

    @Override
    public void down(String key, HttpServletResponse response) {
        // webdav 的可以直接下载不用这里写
    }

    @Override
    protected boolean deleteFile(SysFileInfo fileInfo) {
        WebDavNTLMUtils webdavUtil = WebDavNTLMUtils.initialization(rootPath, user, pwd);
        try {
            webdavUtil.delete(fileInfo.getPath());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * @Description:
     * @param file
     * @param fileInfo
     * @param type
     * @throws Exception
     * @see com.fd.stdp.service.sys.impl.AbstractFileService#uploadFile(org.springframework.web.multipart.MultipartFile,
     *      com.fd.stdp.beans.sys.SysFileInfo, java.lang.String)
     * @Author: szx
     */
    @Override
    protected void uploadFile(MultipartFile file, SysFileInfo fileInfo, String type) throws Exception {
        // TODO Auto-generated method stub

    }

	@Override
	public boolean copyFile(String source, String taraget) {
		// TODO Auto-generated method stub
		return false;
	}

}
