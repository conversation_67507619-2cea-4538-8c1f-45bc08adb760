package com.fd.stdp.service.reward;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.reward.RewordKeepOn;
import com.fd.stdp.beans.reward.vo.RewordKeepOnVo;
/**
 *@Description: 科技成果备案
 *@Author: wangsh
 *@Date: 2022-07-05 16:59:11
 */
public interface RewordKeepOnService {

	/**
	 *@Description: 保存或更新科技成果备案
	 *@param rewordKeepOn 科技成果备案对象
	 *@return String 科技成果备案ID
	 *@Author: wangsh
	 */
	String saveOrUpdateRewordKeepOn(RewordKeepOnVo rewordKeepOn);
	
	/**
	 *@Description: 删除科技成果备案
	 *@param id void 科技成果备案ID
	 *@Author: wangsh
	 */
	void deleteRewordKeepOn(String id);

	/**
	 * @Description: 批量删除科技成果备案
	 * @param ids
	 */
    void deleteMultiRewordKeepOn(List<String> ids);

	/**
	 *@Description: 查询科技成果备案详情
	 *@param id
	 *@return RewordKeepOn
	 *@Author: wangsh
	 */
	RewordKeepOn findById(String id);

	/**
	 *@Description: 分页查询科技成果备案
	 *@param rewordKeepOnVo
	 *@return PageInfo<RewordKeepOn>
	 *@Author: wangsh
	 */
	PageInfo<RewordKeepOn> findPageByQuery(RewordKeepOnVo rewordKeepOnVo);
	
	
	/**
	 * 提交
	 * @param rewordKeepOnVo
	 * @return
	 */
    String submitRewordKeepOn(RewordKeepOnVo rewordKeepOnVo);

	/**
	 * 审核
	 * @param rewordKeepOnVo
	 * @return
	 */
	String auditRewordKeepOn(RewordKeepOnVo rewordKeepOnVo);

	/**
	 * 退回
	 * @param rewordKeepOnVo
	 * @return
	 */
	String sendBackRewordKeepOn(RewordKeepOnVo rewordKeepOnVo);

	/**
	 * 任务书下达
	 * @param rewordKeepOnVo
	 * @return
	 */
	String releaseRewordKeepOn(RewordKeepOnVo rewordKeepOnVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<RewordKeepOn> todoList(RewordKeepOnVo rewordKeepOnVo);

	/**
	 * 已办列表
	 */
	PageInfo<RewordKeepOn> finishedList(RewordKeepOnVo rewordKeepOnVo);

	/**
	 * 已完成列表
	 */
	PageInfo<RewordKeepOn> endList(RewordKeepOnVo rewordKeepOnVo);
}
