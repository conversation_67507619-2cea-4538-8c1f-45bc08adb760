package com.fd.stdp.service.sys;

import java.util.List;

import com.fd.stdp.beans.sys.SysUserRole;
import com.fd.stdp.beans.sys.vo.SysUserInfoVO;
import com.fd.stdp.common.BaseService;
import com.fd.stdp.common.exception.ServiceException;
import com.github.pagehelper.PageInfo;

/**
 * 用户角色service
 * 
 * <AUTHOR>
 * @date 2018/03/12
 */
public interface UserRoleService extends BaseService<SysUserRole> {

	/**
	 * 保存或更新用户角色
	 * 
	 * @param sysRole
	 * @return
	 */
	void saveOrUpdateUserRole(String userId, String[] roleIds) throws ServiceException;

	/**
	 * 查询用户角色列表
	 * 
	 * @return
	 */
	List<SysUserRole> list(String userId);

	/**
	 * 根据角色编码查询同一个城市公司下的用户角色列表
	 * 
	 * @return
	 */
	List<SysUserRole> listByRoleCode(String roleCode);

	/**
	 * 根据角色编码查询同一个城市公司下的用户角色列表
	 * 
	 * @return
	 */
	PageInfo<SysUserInfoVO> pageByRoleCodeAndName(Integer pageNum, Integer pageSize, String roleCode, String userName);

	/**
	 * 根据角色id删除关联信息
	 * 
	 * @return
	 */
	void deleteByRoleId(String roleId);

	/**
	 * 根据用户id删除关联信息
	 * 
	 * @return
	 */
	void deleteByUserId(String userId);

	/**
	 * 根据角色id用户id删除关联信息
	 * 
	 * @return
	 */
	void deleteByRoleIdAndUserId(String roleId, String userId);

	/**
	 * 暂停开启用户角色权限
	 * 
	 * @return
	 */
	void swtichStatus(String roleId, String userId, String status);

	/**
	 * 用户下添加角色
	 * 
	 * @param userId
	 * @param roleIds
	 */
	void saveOrUpdateRole(String userId, List<String> roleIds);

	/**
	 * 保留之前的
	 * @param userId
	 * @param roleIds
	 */
	void addRoles(String userId, List<String> roleIds);
}
