package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityReport;
import com.fd.stdp.beans.innovation.vo.InnovationQualityReportVo;
/**
 *@Description: 省质检中心事项报告
 *@Author: wangsh
 *@Date: 2022-03-04 09:23:48
 */
public interface InnovationQualityReportService {

	/**
	 *@Description: 保存或更新省质检中心事项报告
	 *@param innovationQualityReport 省质检中心事项报告对象
	 *@return String 省质检中心事项报告ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationQualityReport(InnovationQualityReportVo innovationQualityReport);
	
	/**
	 *@Description: 删除省质检中心事项报告
	 *@param id void 省质检中心事项报告ID
	 *@Author: wangsh
	 */
	void deleteInnovationQualityReport(String id);

	/**
	 * @Description: 批量删除省质检中心事项报告
	 * @param ids
	 */
    void deleteMultiInnovationQualityReport(List<String> ids);

	/**
	 *@Description: 查询省质检中心事项报告详情
	 *@param id
	 *@return InnovationQualityReport
	 *@Author: wangsh
	 */
	InnovationQualityReport findById(String id);

	/**
	 *@Description: 分页查询省质检中心事项报告
	 *@param innovationQualityReportVo
	 *@return PageInfo<InnovationQualityReport>
	 *@Author: wangsh
	 */
	PageInfo<InnovationQualityReport> findPageByQuery(InnovationQualityReportVo innovationQualityReportVo);
	
	
	/**
	 * 提交
	 * @param innovationQualityReportVo
	 * @return
	 */
    String submitInnovationQualityReport(InnovationQualityReportVo innovationQualityReportVo);

	/**
	 * 审核
	 * @param innovationQualityReportVo
	 * @return
	 */
	String auditInnovationQualityReport(InnovationQualityReportVo innovationQualityReportVo);

	/**
	 * 退回
	 * @param innovationQualityReportVo
	 * @return
	 */
	String sendBackInnovationQualityReport(InnovationQualityReportVo innovationQualityReportVo);

	/**
	 * 任务书下达
	 * @param innovationQualityReportVo
	 * @return
	 */
	String releaseInnovationQualityReport(InnovationQualityReportVo innovationQualityReportVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnovationQualityReport> todoList(InnovationQualityReportVo innovationQualityReportVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationQualityReport> finishedList(InnovationQualityReportVo innovationQualityReportVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationQualityReport> endList(InnovationQualityReportVo innovationQualityReportVo);
}
