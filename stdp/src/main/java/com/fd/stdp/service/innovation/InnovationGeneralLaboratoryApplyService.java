package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationGeneralLaboratoryApply;
import com.fd.stdp.beans.innovation.vo.InnovationGeneralLaboratoryApplyVo;
/**
 *@Description: 总局重点实验室
 *@Author: wangsh
 *@Date: 2022-03-07 19:45:57
 */
public interface InnovationGeneralLaboratoryApplyService {

	/**
	 *@Description: 保存或更新总局重点实验室
	 *@param innovationGeneralLaboratoryApply 总局重点实验室对象
	 *@return String 总局重点实验室ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationGeneralLaboratoryApply(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApply);
	
	/**
	 *@Description: 删除总局重点实验室
	 *@param id void 总局重点实验室ID
	 *@Author: wangsh
	 */
	void deleteInnovationGeneralLaboratoryApply(String id);

	/**
	 * @Description: 批量删除总局重点实验室
	 * @param ids
	 */
    void deleteMultiInnovationGeneralLaboratoryApply(List<String> ids);

	/**
	 *@Description: 查询总局重点实验室详情
	 *@param id
	 *@return InnovationGeneralLaboratoryApply
	 *@Author: wangsh
	 */
	InnovationGeneralLaboratoryApply findById(String id);

	/**
	 *@Description: 分页查询总局重点实验室
	 *@param innovationGeneralLaboratoryApplyVo
	 *@return PageInfo<InnovationGeneralLaboratoryApply>
	 *@Author: wangsh
	 */
	PageInfo<InnovationGeneralLaboratoryApply> findPageByQuery(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApplyVo);
	
	
	/**
	 * 提交
	 * @param innovationGeneralLaboratoryApplyVo
	 * @return
	 */
    String submitInnovationGeneralLaboratoryApply(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApplyVo);

	/**
	 * 审核
	 * @param innovationGeneralLaboratoryApplyVo
	 * @return
	 */
	String auditInnovationGeneralLaboratoryApply(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApplyVo);

	/**
	 * 退回
	 * @param innovationGeneralLaboratoryApplyVo
	 * @return
	 */
	String sendBackInnovationGeneralLaboratoryApply(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApplyVo);

	/**
	 * 任务书下达
	 * @param innovationGeneralLaboratoryApplyVo
	 * @return
	 */
	String releaseInnovationGeneralLaboratoryApply(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApplyVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnovationGeneralLaboratoryApply> todoList(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApplyVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationGeneralLaboratoryApply> finishedList(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApplyVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationGeneralLaboratoryApply> endList(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApplyVo);
}
