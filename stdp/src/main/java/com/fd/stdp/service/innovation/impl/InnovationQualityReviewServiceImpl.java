package com.fd.stdp.service.innovation.impl;

import java.util.List;

import com.fd.stdp.beans.innovation.InnovationQualityReport;
import com.fd.stdp.beans.innovation.vo.InnovationQualityReportVo;
import com.fd.stdp.dao.innovation.InnovationQualityContractMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityReview;
import com.fd.stdp.beans.innovation.vo.InnovationQualityReviewVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationQualityReviewMapper;
import com.fd.stdp.service.innovation.InnovationQualityReviewService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 省质检中心年度总结
 *@Author: wangsh
 *@Date: 2022-03-04 09:23:43
 */
public class InnovationQualityReviewServiceImpl extends BaseServiceImpl<InnovationQualityReviewMapper, InnovationQualityReview> implements InnovationQualityReviewService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationQualityReviewServiceImpl.class);
	
	@Autowired
	private InnovationQualityReviewMapper innovationQualityReviewMapper;
	@Autowired
	private InnovationQualityContractMapper innovationQualityContractMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新省质检中心年度总结
	 *@param innovationQualityReview 省质检中心年度总结对象
	 *@return String 省质检中心年度总结ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationQualityReview(InnovationQualityReviewVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if (vo.getContract() != null){
			vo.setApplyUnitName(vo.getContract().getApplyUnitName());
			vo.setApplyCenterName(vo.getContract().getApplyCenterName());
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationQualityReviewMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationQualityReviewMapper.updateByPrimaryKeySelective(vo);
		}
		/**
		 * 附件
		 */
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除省质检中心年度总结
	 *@param id void 省质检中心年度总结ID
	 *@Author: wangsh
	 */
	public void deleteInnovationQualityReview(String id) {
		//TODO 做判断后方能执行删除
		InnovationQualityReview innovationQualityReview=innovationQualityReviewMapper.selectByPrimaryKey(id);
		if(innovationQualityReview==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationQualityReview teminnovationQualityReview=new InnovationQualityReview();
		teminnovationQualityReview.setYn(CommonConstant.FLAG_NO);
		teminnovationQualityReview.setId(innovationQualityReview.getId());
		innovationQualityReviewMapper.updateByPrimaryKeySelective(teminnovationQualityReview);
	}

    /**
     * @Description: 批量删除省质检中心年度总结
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationQualityReview(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationQualityReview(id));
	}

	@Override
	/**
	 *@Description: 查询省质检中心年度总结详情
	 *@param id
	 *@return InnovationQualityReview
	 *@Author: wangsh
	 */
	public InnovationQualityReview findById(String id) {
		InnovationQualityReview innovationQualityReview = innovationQualityReviewMapper.selectByPrimaryKey(id);
		InnovationQualityReviewVo vo = new InnovationQualityReviewVo();
		BeanUtils.copyProperties(innovationQualityReview, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		vo.setContract(innovationQualityContractMapper.selectByPrimaryKey(vo.getContractId()));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询省质检中心年度总结
	 *@param innovationQualityReviewVo
	 *@return PageInfo<InnovationQualityReview>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationQualityReview> findPageByQuery(InnovationQualityReviewVo innovationQualityReviewVo) {
		PageHelper.startPage(innovationQualityReviewVo.getPageNum(),innovationQualityReviewVo.getPageSize());
		Example example=new Example(InnovationQualityReview.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationQualityReviewVo.getName())){
		//	criteria.andEqualTo(innovationQualityReviewVo.getName());
		//}
		List<InnovationQualityReview> innovationQualityReviewList=innovationQualityReviewMapper.selectByExample(example);
		return new PageInfo<InnovationQualityReview>(innovationQualityReviewList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationQualityReview(InnovationQualityReviewVo innovationQualityReviewVo) {
		String id = this.saveOrUpdateInnovationQualityReview(innovationQualityReviewVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innovationQualityReviewVo, this.mapper,
				StringUtils.isNotBlank(innovationQualityReviewVo.getAuditAdvice())?innovationQualityReviewVo.getAuditAdvice():"提交省质检中心年度总结");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationQualityReview(InnovationQualityReviewVo innovationQualityReviewVo) {
		flowCommonService.doFlowStepAudit(innovationQualityReviewVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityReviewVo.getAuditAdvice()) ? innovationQualityReviewVo.getAuditAdvice() : "省质检中心年度总结审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationQualityReviewVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationQualityReview(InnovationQualityReviewVo innovationQualityReviewVo) {
		flowCommonService.doFlowStepSendBack(innovationQualityReviewVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityReviewVo.getAuditAdvice())?innovationQualityReviewVo.getAuditAdvice():"省质检中心年度总结退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationQualityReview(InnovationQualityReviewVo innovationQualityReviewVo) {
		flowCommonService.doCompleteTask(innovationQualityReviewVo, this.mapper
				, "省质检中心年度总结任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnovationQualityReview> todoList(InnovationQualityReviewVo vo) {

		Example example = new Example(InnovationQualityReview.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationQualityReview> finishedList(InnovationQualityReviewVo vo) {
		Example example = new Example(InnovationQualityReview.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnovationQualityReview> endList(InnovationQualityReviewVo vo) {
        Example example = new Example(InnovationQualityReview.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(InnovationQualityReview vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getApplyCenterName())){
			criteria.andLike("applyCenterName", "%" + vo.getApplyCenterName()+ "%");
		}
		return criteria;
	}
}
