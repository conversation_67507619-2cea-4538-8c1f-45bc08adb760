package com.fd.stdp.service.reward.impl;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.List;

import com.fd.stdp.beans.reward.*;
import com.fd.stdp.dao.reward.*;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.reward.vo.RewordRecordVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.service.reward.RewordRecordService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 科技成果登记
 *@Author: wangsh
 *@Date: 2022-07-05 16:58:41
 */
public class RewordRecordServiceImpl extends BaseServiceImpl<RewordRecordMapper, RewordRecord> implements RewordRecordService{

	public static final Logger logger = LoggerFactory.getLogger(RewordRecordServiceImpl.class);
	
	@Autowired
	private RewordRecordMapper rewordRecordMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private RewordCooperUnitMapper rewordCooperUnitMapper;
	@Autowired
	private RewordPatentMapper rewordPatentMapper;
	@Autowired
	private RewordPersonMapper rewordPersonMapper;
	@Autowired
	private RewordSoftMapper rewordSoftMapper;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新科技成果登记
	 *@param rewordRecord 科技成果登记对象
	 *@return String 科技成果登记ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateRewordRecord(RewordRecordVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			rewordRecordMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			rewordRecordMapper.updateByPrimaryKeySelective(vo);
		}

		// 设置查询字段
		StringBuilder stringBuilder = new StringBuilder();
		if(vo.getCooperUnitList() != null) {
			stringBuilder.append(vo.getUnitName()).append("；");
			for (RewordCooperUnit unit : vo.getCooperUnitList()) {
				stringBuilder.append(unit.getUnitName()).append("；");
			}
			vo.setRewordUnits(stringBuilder.toString());
		}
		if(vo.getRewordPersonList() != null) {
			stringBuilder = new StringBuilder();
			for (RewordPerson person : vo.getRewordPersonList()) {
				stringBuilder.append(person.getName()).append("；");
			}
			vo.setRewordPersons(stringBuilder.toString());
			rewordRecordMapper.updateByPrimaryKeySelective(vo);
		}

		// 处理关联关系
		updateList(vo, vo.getCooperUnitList(), rewordCooperUnitMapper, "setRewardId");
		updateList(vo, vo.getRewordPatentList(), rewordPatentMapper, "setRewardId");
		updateList(vo, vo.getRewordPersonList(), rewordPersonMapper, "setRewardId");
		updateList(vo, vo.getRewordSoftList(), rewordSoftMapper, "setRewardId");
		basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除科技成果登记
	 *@param id void 科技成果登记ID
	 *@Author: wangsh
	 */
	public void deleteRewordRecord(String id) {
		//TODO 做判断后方能执行删除
		RewordRecord rewordRecord=rewordRecordMapper.selectByPrimaryKey(id);
		if(rewordRecord==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		RewordRecord temrewordRecord=new RewordRecord();
		temrewordRecord.setYn(CommonConstant.FLAG_NO);
		temrewordRecord.setId(rewordRecord.getId());
		rewordRecordMapper.updateByPrimaryKeySelective(temrewordRecord);
	}

    /**
     * @Description: 批量删除科技成果登记
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiRewordRecord(List<String> ids) {
		ids.stream().forEach(id-> this.deleteRewordRecord(id));
	}

	@Override
	/**
	 *@Description: 查询科技成果登记详情
	 *@param id
	 *@return RewordRecord
	 *@Author: wangsh
	 */
	public RewordRecord findById(String id) {
		RewordRecord rewordRecord = rewordRecordMapper.selectByPrimaryKey(id);
		RewordRecordVo vo = new RewordRecordVo();
		BeanUtils.copyProperties(rewordRecord, vo);
		vo.setCooperUnitList(getList(RewordCooperUnit.class, "rewardId", vo.getId(), rewordCooperUnitMapper));
		vo.setRewordPatentList(getList(RewordPatent.class, "rewardId", vo.getId(), rewordPatentMapper));
		vo.setRewordPersonList(getList(RewordPerson.class, "rewardId", vo.getId(), rewordPersonMapper));
		vo.setRewordSoftList(getList(RewordSoft.class, "rewardId", vo.getId(), rewordSoftMapper));
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	public List getList(Class clazz, String linkKey, String linkValue, Object mapper) {
		try {
			Method methodSelectByExample = mapper.getClass().getMethod("selectByExample", Object.class);
			Example example = new Example(clazz);
			example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo(linkKey, linkValue);
			return (List) methodSelectByExample.invoke(mapper, example);
		} catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
			e.printStackTrace();
			throw new ServiceException("代码错误");
		}
	}

	@Override
	/**
	 *@Description: 分页查询科技成果登记
	 *@param rewordRecordVo
	 *@return PageInfo<RewordRecord>
	 *@Author: wangsh
	 */
	public PageInfo<RewordRecord> findPageByQuery(RewordRecordVo rewordRecordVo) {
		PageHelper.startPage(rewordRecordVo.getPageNum(),rewordRecordVo.getPageSize());
		Example example=new Example(RewordRecord.class);
		Criteria criteria=getCriteria(rewordRecordVo, example);
		criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		List<RewordRecord> rewordRecordList=rewordRecordMapper.selectByExample(example);
		return new PageInfo<RewordRecord>(rewordRecordList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitRewordRecord(RewordRecordVo rewordRecordVo) {
		String id = this.saveOrUpdateRewordRecord(rewordRecordVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_NORMAL_AUDIT, rewordRecordVo, this.mapper,
				StringUtils.isNotBlank(rewordRecordVo.getAuditAdvice())?rewordRecordVo.getAuditAdvice():"提交科技成果登记");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditRewordRecord(RewordRecordVo rewordRecordVo) {
		if(StringUtils.equals(rewordRecordVo.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())) {
			Example example = new Example(RewordRecord.class);
			example.createCriteria().andIsNotNull("rewardNo").andLike("rewardNo", "%-" + rewordRecordVo.getYaerNo() + "-%");
			example.orderBy("rewardNo").desc();

			List<RewordRecord> list = rewordRecordMapper.selectByExample(example);
			String num;
			int flowCount = 8;
			if(!CollectionUtils.isEmpty(list)) {
				RewordRecord old = list.get(0);
				String oldNo = old.getRewardNo();
				num = "" + (Integer.parseInt(oldNo.split("-")[2])+1);
			} else {
				num = "1";
			}
			num = StringUtils.leftPad(num, flowCount, "0");
			rewordRecordVo.setRewardNo("SJD-" + rewordRecordVo.getYaerNo() + "-" + num);
			rewordRecordVo.setAuditTime(new Date());
			mapper.updateByPrimaryKeySelective(rewordRecordVo);
		}
		flowCommonService.doFlowStepAudit(rewordRecordVo, this.mapper
				, StringUtils.isNotBlank(rewordRecordVo.getAuditAdvice()) ? rewordRecordVo.getAuditAdvice() : "科技成果登记审核通过"
				, FlowStatusEnum.END.getCode());
		return rewordRecordVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackRewordRecord(RewordRecordVo rewordRecordVo) {
		flowCommonService.doFlowStepSendBack(rewordRecordVo, this.mapper
				, StringUtils.isNotBlank(rewordRecordVo.getAuditAdvice())?rewordRecordVo.getAuditAdvice():"科技成果登记退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseRewordRecord(RewordRecordVo rewordRecordVo) {
		flowCommonService.doCompleteTask(rewordRecordVo, this.mapper
				, "科技成果登记任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<RewordRecord> todoList(RewordRecordVo vo) {

		Example example = new Example(RewordRecord.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<RewordRecord> finishedList(RewordRecordVo vo) {
		Example example = new Example(RewordRecord.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<RewordRecord> endList(RewordRecordVo vo) {
        Example example=new Example(RewordRecord.class);
        example.orderBy("createTime").desc();
    	Criteria criteria=getCriteria(vo, example);
    	return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
    }

	private Criteria getCriteria(RewordRecordVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getRewardName())) {
			criteria.andLike("rewardName", "%" + vo.getRewardName() + "%");
		}
		if(StringUtils.isNotBlank(vo.getRewordUnits())) {
			criteria.andLike("rewordUnits", "%" + vo.getRewordUnits() + "%");
		}
		if(StringUtils.isNotBlank(vo.getRewardTypeText())) {
			criteria.andEqualTo("rewardTypeText", vo.getRewardTypeText());
		}
		if(StringUtils.isNotBlank(vo.getRewordPersons())) {
			criteria.andLike("rewordPersons", "%" + vo.getRewordPersons() + "%");
		}
		if(StringUtils.isNotBlank(vo.getYaerNo())) {
			criteria.andEqualTo("yaerNo", vo.getYaerNo());
		}
		if(StringUtils.isNotBlank(vo.getRewardNo())) {
			criteria.andLike("rewardNo", "%" + vo.getRewardNo() + "%");
		}
		return criteria;
	}
}
