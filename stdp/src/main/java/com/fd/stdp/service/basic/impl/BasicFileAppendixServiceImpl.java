package com.fd.stdp.service.basic.impl;

import java.util.ArrayList;
import java.util.List;

import com.fd.stdp.beans.project.vo.ProjectApplyExpertScoreVo;
import com.fd.stdp.beans.sys.SysFileInfo;
import com.fd.stdp.common.BaseEntity;
import com.fd.stdp.dao.sys.SysFileInfoMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicFileAppendix;
import com.fd.stdp.beans.basic.vo.BasicFileAppendixVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicFileAppendixMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 附件表
 *@Author: wangsh
 *@Date: 2022-01-12 18:13:39
 */
public class BasicFileAppendixServiceImpl extends BaseServiceImpl<BasicFileAppendixMapper, BasicFileAppendix> implements BasicFileAppendixService{

	public static final Logger logger = LoggerFactory.getLogger(BasicFileAppendixServiceImpl.class);

	@Autowired
	private BasicFileAppendixMapper basicFileAppendixMapper;
	@Autowired
	private SysFileInfoMapper sysFileInfoMapper;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新附件表
	 *@param basicFileAppendix 附件表对象
	 *@return String 附件表ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateBasicFileAppendix(BasicFileAppendixVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			basicFileAppendixMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			basicFileAppendixMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除附件表
	 *@param id void 附件表ID
	 *@Author: wangsh
	 */
	public void deleteBasicFileAppendix(String id) {
		//TODO 做判断后方能执行删除
		BasicFileAppendix basicFileAppendix=basicFileAppendixMapper.selectByPrimaryKey(id);
		if(basicFileAppendix==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		BasicFileAppendix tembasicFileAppendix=new BasicFileAppendix();
		tembasicFileAppendix.setYn(CommonConstant.FLAG_NO);
		tembasicFileAppendix.setId(basicFileAppendix.getId());
		basicFileAppendixMapper.updateByPrimaryKeySelective(tembasicFileAppendix);
	}

    /**
     * @Description: 批量删除附件表
     * @param basicFileAppendixVo
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiBasicFileAppendix(BasicFileAppendixVo basicFileAppendixVo) {
		basicFileAppendixVo.getIds().stream().forEach(id-> this.deleteBasicFileAppendix(id));
	}

	@Override
	/**
	 *@Description: 查询附件表详情
	 *@param id
	 *@return BasicFileAppendix
	 *@Author: wangsh
	 */
	public BasicFileAppendix findById(String id) {
		return basicFileAppendixMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询附件表
	 *@param basicFileAppendixVo
	 *@return PageInfo<BasicFileAppendix>
	 *@Author: wangsh
	 */
	public PageInfo<BasicFileAppendix> findPageByQuery(BasicFileAppendixVo basicFileAppendixVo) {
		PageHelper.startPage(basicFileAppendixVo.getPageNum(),basicFileAppendixVo.getPageSize());
		Example example=new Example(BasicFileAppendix.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(basicFileAppendixVo.getName())){
		//	criteria.andEqualTo(basicFileAppendixVo.getName());
		//}
		List<BasicFileAppendix> basicFileAppendixList=basicFileAppendixMapper.selectByExample(example);
		return new PageInfo<BasicFileAppendix>(basicFileAppendixList);
	}

	@Override
	public List<BasicFileAppendixVo> findByFormId(String formId) {
		Example example = new Example(BasicFileAppendix.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("formId", formId);
		List<BasicFileAppendix> basicFileAppendixList = basicFileAppendixMapper.selectByExample(example);
		List<BasicFileAppendixVo> basicFileAppendixVoList = new ArrayList<>();
		for (BasicFileAppendix b:basicFileAppendixList) {
			SysFileInfo sysFileInfo = sysFileInfoMapper.selectByPrimaryKey(b.getFileId());
			if(sysFileInfo != null) {
				BasicFileAppendixVo basicFileAppendixVo = new BasicFileAppendixVo();
				BeanUtils.copyProperties(b, basicFileAppendixVo);
				BeanUtils.copyProperties(sysFileInfo, basicFileAppendixVo);
				basicFileAppendixVo.setId(b.getId());
				basicFileAppendixVoList.add(basicFileAppendixVo);
			}
		}
		return basicFileAppendixVoList;
	}

	@Override
	public void clearAndUpdateBasicFileAppendix(String formId, List<BasicFileAppendixVo> basicFileAppendixVoList) {
		if(StringUtils.isBlank(formId)){
			throw new ServiceException("为保存的表单无法保存附件");
		}
		basicFileAppendixMapper.clearByFormId(formId);
		if (!CollectionUtils.isEmpty(basicFileAppendixVoList)) {
			for (BasicFileAppendix b:basicFileAppendixVoList) {
				if(b.getFormId() != null) {
					b.setYn(CommonConstant.FLAG_YES);
					basicFileAppendixMapper.updateByPrimaryKeySelective(b);
				} else {
					b.setFormId(formId);
					b.setFileId(b.getId());
					b.setId(UUIDUtils.getUUID());
					basicFileAppendixMapper.insertSelective(b);
				}
			}
		}
	}

}
