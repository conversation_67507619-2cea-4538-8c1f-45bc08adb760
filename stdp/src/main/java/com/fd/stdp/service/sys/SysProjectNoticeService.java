package com.fd.stdp.service.sys;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.sys.SysProjectNotice;
import com.fd.stdp.beans.sys.vo.SysProjectNoticeVo;

/**
 * 公告信息Service接口
 *
 * <AUTHOR>
 * @date 2021-11-15
 */
public interface SysProjectNoticeService {
    /**
     * @param sysProjectNoticeVo 公告信息对象
     * @return String 公告信息ID
     * @Description: 保存或更新公告信息
     * @Author: yujianfei
     */
    String saveOrUpdateSysProjectNotice(SysProjectNoticeVo sysProjectNoticeVo);

    /**
     * @param ids void 公告信息ID
     * @Description: 删除公告信息
     * @Author: yujianfei
     */
    void deleteSysProjectNotice(List<String> ids);

    /**
     * @param id
     * @return SysProjectNotice
     * @Description: 查询公告信息详情
     * @Author: yujianfei
     */
    SysProjectNoticeVo findById(String id);

    /**
     * @param sysProjectNoticeVo
     * @return PageInfo<SysProjectNotice>
     * @Description: 分页查询公告信息
     * @Author: yujianfei
     */
    PageInfo<SysProjectNotice> findPageByQuery(SysProjectNoticeVo sysProjectNoticeVo);
}
