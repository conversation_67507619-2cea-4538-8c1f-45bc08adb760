package com.fd.stdp.service.talent;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectLeader;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectLeaderVo;
/**
 *@Description: 学科带头人申报书
 *@Author: wangsh
 *@Date: 2022-02-14 10:05:30
 */
public interface TalentLeaderSubjectLeaderService {

	/**
	 *@Description: 保存或更新学科带头人申报书
	 *@param talentLeaderSubjectLeader 学科带头人申报书对象
	 *@return String 学科带头人申报书ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTalentLeaderSubjectLeader(TalentLeaderSubjectLeaderVo talentLeaderSubjectLeader);
	
	/**
	 *@Description: 删除学科带头人申报书
	 *@param id void 学科带头人申报书ID
	 *@Author: wangsh
	 */
	void deleteTalentLeaderSubjectLeader(String id);

	/**
	 * @Description: 批量删除学科带头人申报书
	 * @param ids
	 */
    void deleteMultiTalentLeaderSubjectLeader(List<String> ids);

	/**
	 *@Description: 查询学科带头人申报书详情
	 *@param id
	 *@return TalentLeaderSubjectLeader
	 *@Author: wangsh
	 */
	TalentLeaderSubjectLeader findById(String id);

	/**
	 *@Description: 分页查询学科带头人申报书
	 *@param talentLeaderSubjectLeaderVo
	 *@return PageInfo<TalentLeaderSubjectLeader>
	 *@Author: wangsh
	 */
	PageInfo<TalentLeaderSubjectLeader> findPageByQuery(TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo);
	
	
	/**
	 * 提交
	 * @param talentLeaderSubjectLeaderVo
	 * @return
	 */
    String submitTalentLeaderSubjectLeader(TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo);

	/**
	 * 审核
	 * @param talentLeaderSubjectLeaderVo
	 * @return
	 */
	String auditTalentLeaderSubjectLeader(TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo);

	/**
	 * 退回
	 * @param talentLeaderSubjectLeaderVo
	 * @return
	 */
	String sendBackTalentLeaderSubjectLeader(TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo);

	/**
	 * 任务书下达
	 * @param talentLeaderSubjectLeaderVo
	 * @return
	 */
	String releaseTalentLeaderSubjectLeader(TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo);
	
	/**
	 * 待办列表
	*/
    PageInfo<TalentLeaderSubjectLeader> todoList(TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo);

	/**
	 * 已办列表
	*/
	PageInfo<TalentLeaderSubjectLeader> finishedList(TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo);

	/**
	 * 专家提交
	 * @param vo
	 * @return
	 */
	String expertSubmitTalentLeaderSubjectLeader(TalentLeaderSubjectLeaderVo vo);
}
