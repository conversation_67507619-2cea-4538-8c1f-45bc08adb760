package com.fd.stdp.service.sys.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fd.stdp.beans.sys.SysElement;
import com.fd.stdp.beans.sys.vo.SysElementVo;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.dao.sys.SysElementMapper;
import com.fd.stdp.dao.sys.SysMenuMapper;
import com.fd.stdp.service.sys.ElementService;
import com.fd.stdp.util.ExampleUtil;
import com.fd.stdp.util.UUIDUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import tk.mybatis.mapper.entity.Example;

@Service
@Transactional(rollbackFor = Exception.class)
public class ElementServiceImpl extends BaseServiceImpl<SysElementMapper, SysElement> implements ElementService {

	@Autowired
	SysElementMapper sysElementMapper;

	@Autowired
	SysMenuMapper sysMenuMapper;

	// @Cache(key="permission:ele:u{1}")
	@Override
	public List<SysElement> getAuthorityElementByUserId(String userId) {
		return null;// sysElementMapper.selectAuthorityElementByUserId(userId);
	}

	@Override
	public List<SysElement> getAuthorityElementByUserId(String userId, String menuId, Integer pcFlag, Integer appFlag) {
		return sysElementMapper.selectAuthorityElementByUserId(userId, menuId, pcFlag, appFlag);
	}

	@Override
	// @Cache(key="permission:ele")
	public List<SysElement> selectListAll() {
		return sysElementMapper.selectAll();
	}

	@Override
	// @CacheClear(keys={"permission:ele","permission"})
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void insertSelective(SysElement entity) {
		entity.setId(UUIDUtils.getUUID());
		sysElementMapper.insertSelective(entity);
	}

	@Override
	// @CacheClear(keys={"permission:ele","permission"})
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateSelectiveById(SysElementVo entity) {
		String menuId = entity.getMenuId();
		if (menuId == null) {
			throw new ServiceException("当前按钮无菜单，操作失败");
		}

		SysElement dbElement = this.sysElementMapper.selectByPrimaryKey(entity.getId());

		if (dbElement == null) {
			throw new ServiceException("无法查找到当前对象，操作失败");
		}

		dbElement.setCode(entity.getCode());
		dbElement.setName(entity.getName());
		dbElement.setType(entity.getType());
		dbElement.setUrl(entity.getUrl());
		dbElement.setMethod(entity.getMethod());
		dbElement.setOrderVal(entity.getOrderVal());
		dbElement.setRemark(entity.getRemark());

		return sysElementMapper.updateByPrimaryKeySelective(dbElement);
	}

	@Override
	public List<SysElement> selectByExample(Object example) {
		return sysElementMapper.selectByExample(example);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void addSysElement(SysElementVo vo) {
		String menuId = vo.getMenuId();
		if (menuId == null) {
			throw new ServiceException("当前按钮无菜单，操作失败");
		}

		String elementId = vo.getId();
		if (elementId == null || StringUtils.isEmpty(elementId)) {

			vo.setId(UUIDUtils.getUUID());
			this.mapper.insertSelective(vo);
		}

	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void delete(String id, boolean isAdmin) {
		SysElement dbElement = this.sysElementMapper.selectByPrimaryKey(id);

		if (dbElement == null) {
			throw new ServiceException("无法查找到当前对象，操作失败");
		}
		this.mapper.deleteByPrimaryKey(id);
	}

	@Override
	public List<SysElement> getPermissionByUsername(String userName) {
		return sysElementMapper.getPermissionByUsername(userName);
	}

	/**
	 * 分页查询权限数据
	 */
	@Override
	public PageInfo<SysElement> findPageList(Integer pageNum, Integer pageSize, String name, String code) {
		// 分页开始
		PageHelper.startPage(pageNum, pageSize);
		Example example = new Example(SysElement.class);
		Example.Criteria criteria = example.createCriteria();
		if (StringUtils.isNotBlank(name)) {
			criteria.andLike("name", "%" + name + "%");
		}
		if (StringUtils.isNotBlank(code)) {
			criteria.andLike("code", "%" + code + "%");
		}
//		PageInfo<SysElement> selectByExample = sysElementMapper.findPageList(name,code);
		List<SysElement> elementList = sysElementMapper.selectByExample(example);
		return new PageInfo<SysElement>(elementList);
	}

	/**
	 * 查询菜单下所有元素(分页)
	 * 
	 * @param sysElement
	 * @return
	 */
	@Override
	public PageInfo<SysElement> findElementByMenuId(SysElementVo sysElementVo) {
		PageHelper.startPage(sysElementVo.getPageNum(), sysElementVo.getPageSize());
		Example example = new Example(SysElement.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("menuId", sysElementVo.getMenuId());
		if (StringUtils.isNotBlank(sysElementVo.getName())) {
			criteria.andLike("name", "%" + sysElementVo.getName() + "%");
		}
		ExampleUtil.orderBy(example, sysElementVo.getSortField(), sysElementVo.getSortType());
		example.orderBy("createTime").desc();
		List<SysElement> elementList = sysElementMapper.selectByExample(example);
		return new PageInfo<SysElement>(elementList);
	}
}
