package com.fd.stdp.service.tech;

import java.util.List;

import com.fd.stdp.beans.tech.vo.TechAchievementVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.tech.TechAwardsAcquire;
import com.fd.stdp.beans.tech.vo.TechAwardsAcquireVo;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;

/**
 *@Description: 奖项获取
 *@Author: wangsh
 *@Date: 2022-01-04 13:49:48
 */
public interface TechAwardsAcquireService {

	/**
	 *@Description: 保存或更新奖项获取
	 *@param techAwardsAcquire 奖项获取对象
	 *@return String 奖项获取ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTechAwardsAcquire(TechAwardsAcquireVo techAwardsAcquire);
	
	/**
	 *@Description: 删除奖项获取
	 *@param id void 奖项获取ID
	 *@Author: wangsh
	 */
	void deleteTechAwardsAcquire(String id);

	/**
	 *@Description: 查询奖项获取详情
	 *@param id
	 *@return TechAwardsAcquire
	 *@Author: wangsh
	 */
	TechAwardsAcquireVo findById(String id);

	/**
	 *@Description: 分页查询奖项获取
	 *@param techAwardsAcquireVo
	 *@return PageInfo<TechAwardsAcquire>
	 *@Author: wangsh
	 */
	PageInfo<TechAwardsAcquire> findPageByQuery(TechAwardsAcquireVo techAwardsAcquireVo);

	/**
	 * 提交
	 * @param techAwardsAcquireVo
	 * @return
	 */
    String submitTechAwardsAcquire(TechAwardsAcquireVo techAwardsAcquireVo);

	/**
	 * 审核
	 * @param techAwardsAcquireVo
	 * @return
	 */
	String auditTechAwardsAcquire(TechAwardsAcquireVo techAwardsAcquireVo);

	/**
	 * 退回
	 * @param techAwardsAcquireVo
	 * @return
	 */
	String sendBackTechAwardsAcquire(TechAwardsAcquireVo techAwardsAcquireVo);

    void deleteMultiTechAwardsAcquire(List<String> ids);

    PageInfo<TechAwardsAcquire> todoList(TechAwardsAcquireVo techAwardsAcquireVo);

	PageInfo<TechAwardsAcquire> finishedList(TechAwardsAcquireVo techAwardsAcquireVo);

    void exportTechAchievement(TechAwardsAcquireVo vo, HttpServletResponse response);

    PageInfo endList(TechAwardsAcquireVo vo);
}
