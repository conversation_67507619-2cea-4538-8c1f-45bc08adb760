package com.fd.stdp.service.project.impl;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import cn.afterturn.easypoi.word.WordExportUtil;
import cn.afterturn.easypoi.word.entity.MyXWPFDocument;
import com.fd.stdp.beans.basic.BasicFileAppendix;
import com.fd.stdp.beans.basic.vo.BasicFileAppendixVo;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.project.*;
import com.fd.stdp.beans.project.vo.*;
import com.fd.stdp.common.BaseEntity;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.constant.ProcessConstants;
import com.fd.stdp.dao.project.*;
import com.fd.stdp.dao.sys.BasicAreacodeMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.project.*;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.servlet.http.HttpServletResponse;

import static com.fd.stdp.common.BaseController.getCurrentUserId;
import static com.fd.stdp.common.BaseController.getCurrentUserName;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 任务书申报表
 *@Author: wangsh
 *@Date: 2022-01-12 14:37:32
 */
public class ProjectContractApplyServiceImpl extends BaseServiceImpl<ProjectContractApplyMapper, ProjectContractApply> implements ProjectContractApplyService{

	public static final Logger logger = LoggerFactory.getLogger(ProjectContractApplyServiceImpl.class);

	private static final DateFormat dateFormat = new SimpleDateFormat("yyyy年MM月");
	private static final String format = "%s%s%d";

	@Autowired
	private ProjectContractApplyMapper projectContractApplyMapper;

	@Autowired
	private ProjectApplyInfoMapper projectApplyInfoMapper;

	@Autowired
	private ProjectApplyCostMapper projectApplyCostMapper;
	@Autowired
	private ProjectApplyDevicesMapper projectApplyDevicesMapper;
	@Autowired
	private ProjectApplyProgressMapper applyProgressMapper;
	@Autowired
	private ProjectApplyCooperationUnitMapper projectApplyCooperationUnitMapper;
	@Autowired
	private ProjectApplyTeamsMapper projectApplyTeamsMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private FlowApiService flowApiService;
	@Autowired
	private BasicAreacodeMapper basicAreacodeMapper;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;

	@Autowired
	private ProjectContractApplyChangeService projectContractApplyChangeService;
	@Autowired
	private ProjectContractInterimReportService projectContractInterimReportService;
	@Autowired
	private ProjectContractAcceptService projectContractAcceptService;
	@Autowired
	private ProjectApplyInfoService projectApplyInfoService;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新任务书申报表
	 *@param projectContractApply 任务书申报表对象
	 *@return String 任务书申报表ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateProjectContractApply(ProjectContractApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
/*
		if(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getApplyUnitAreaCode())){
			BasicAreacode basicAreacode = basicAreacodeMapper.findByAreaCode(vo.getApplyUnitAreaCode());
			if(basicAreacode != null) {
				vo.setApplyUnitArea(basicAreacode.getAreaName());
			}
		}*/

		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			projectContractApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			projectContractApplyMapper.updateByPrimaryKeySelective(vo);
		}

		/**
		 * 预算
		 */
		if(vo.getProjectApplyCost() != null || vo.getProjectAllowanceCost() != null) {
			List<ProjectApplyCost> costList = new ArrayList<>();
			if (vo.getProjectApplyCost() != null) {
				vo.getProjectApplyCost().setCostType("0");
				costList.add(vo.getProjectApplyCost());
			}
			if (vo.getProjectAllowanceCost() != null) {
				vo.getProjectAllowanceCost().setCostType("1");
				costList.add(vo.getProjectAllowanceCost());
			}
			if (vo.getProjectSelfCost() != null) {
				vo.getProjectSelfCost().setCostType("5");
				costList.add(vo.getProjectSelfCost());
			}
			updateList(vo, costList, projectApplyCostMapper,"setApplyId");
		}

		/**
		 * 设备
		 */
		if(vo.getDevicesList() != null){
			//updateDevice(vo);
			updateList(vo, vo.getDevicesList(), projectApplyDevicesMapper,"setApplyId");
		}
		/**
		 * 进度安排
		 */
		if(vo.getProgressList() != null){
			updateList(vo, vo.getProgressList(), applyProgressMapper,"setApplyId");
		}
		/**
		 * 合作单位
		 */
		if(vo.getUnitList() != null) {
			updateList(vo, vo.getUnitList(), projectApplyCooperationUnitMapper,"setApplyId");
		}
		/**
		 * 项目团队
		 */
		if(vo.getTeamsList() != null) {
			updateList(vo, vo.getTeamsList(), projectApplyTeamsMapper,"setApplyId");
		}

		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}

		// flowCommonService.doFlowStart(FlowableConstant.FLOW_CONTRACT_PROCESS, vo, this.mapper, "流程开始");
		return vo.getId();
	}

	@Override
	public String saveOrUpdateProjectContractApplyOpt(ProjectContractApplyVo projectContractApply) {
		return null;
	}

	/**
	 * 更新进度安排信息
	 * @param
	 */
//	private void updateList(ProjectContractApplyVo vo, List projectContractPlanList, Object mapper) {
//		try {
//			Method[] methods = mapper.getClass().getMethods();
//			Method methodClearByFormId = mapper.getClass().getMethod("clearByFormId", String.class);
//			Method methodUpdateByPrimaryKeySelective = mapper.getClass().getMethod("updateByPrimaryKeySelective", Object.class);
//			Method methodInsertSelective = mapper.getClass().getMethod("insertSelective", Object.class);
//			Method mSetContractId = null;
//			Method mSetYn = null;
//			methodClearByFormId.invoke(mapper, vo.getId());
//			for (Object p:projectContractPlanList) {
//				BaseEntity b = (BaseEntity) p;
//				mSetContractId = mSetContractId==null?p.getClass().getMethod("setApplyId", String.class):mSetContractId;
//				mSetYn = mSetYn==null?p.getClass().getMethod("setYn", Integer.class):mSetYn;
//				mSetContractId.invoke(b, vo.getId());
//				mSetYn.invoke(b, CommonConstant.FLAG_YES);
//				//if(b.getId() == null){
//				b.setId(UUIDUtils.getUUID());
//				methodInsertSelective.invoke(mapper, b);
//				/*} else {
//					methodUpdateByPrimaryKeySelective.invoke(mapper, b);
//				}*/
//			}
//		} catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
//			e.printStackTrace();
//			throw new ServiceException("代码错误");
//		}
//	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除任务书申报表
	 *@param id void 任务书申报表ID
	 *@Author: wangsh
	 */
	public void deleteProjectContractApply(String id) {
		//TODO 做判断后方能执行删除
		ProjectContractApply projectContractApply=projectContractApplyMapper.selectByPrimaryKey(id);
		if(projectContractApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		ProjectContractApply temprojectContractApply=new ProjectContractApply();
		temprojectContractApply.setYn(CommonConstant.FLAG_NO);
		temprojectContractApply.setId(projectContractApply.getId());
		projectContractApplyMapper.updateByPrimaryKeySelective(temprojectContractApply);
	}

    /**
     * @Description: 批量删除任务书申报表
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiProjectContractApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteProjectContractApply(id));
	}

	@Override
	/**
	 *@Description: 查询任务书申报表详情
	 *@param id
	 *@return ProjectContractApply
	 *@Author: wangsh
	 */
	public ProjectContractApplyVo findById(String id) {
		ProjectContractApply projectContractApply = projectContractApplyMapper.selectByPrimaryKey(id);
		ProjectContractApplyVo vo = new ProjectContractApplyVo();
		if(projectContractApply!=null){
			BeanUtils.copyProperties(projectContractApply, vo);
		}
		/**
		 * 预算
		 */
		Example example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "0");
		vo.setProjectApplyCost(projectApplyCostMapper.selectOneByExample(example));

		example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "1");
		vo.setProjectAllowanceCost(projectApplyCostMapper.selectOneByExample(example));

		example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "5");
		vo.setProjectSelfCost(projectApplyCostMapper.selectOneByExample(example));

		if(vo.getProjectApplyCost() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("0");
			vo.setProjectApplyCost(p);
		}
		if(vo.getProjectAllowanceCost() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("1");
			vo.setProjectAllowanceCost(p);
		}
		if(vo.getProjectSelfCost() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("5");
			vo.setProjectSelfCost(p);
		}

		/**
		 * 设备
		 */
		example = new Example(ProjectApplyDevices.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		example.orderBy("sort").asc();
		vo.setDevicesList(projectApplyDevicesMapper.selectByExample(example));
		/**
		 * 进度安排
		 */
		example = new Example(ProjectApplyProgress.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		example.orderBy("sort").asc();
		vo.setProgressList(applyProgressMapper.selectByExample(example));
		/**
		 * 合作单位
		 */
		example = new Example(ProjectApplyCooperationUnit.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		example.orderBy("sort").asc();
		vo.setUnitList(projectApplyCooperationUnitMapper.selectByExample(example));
		/**
		 * 项目团队
		 */
		example = new Example(ProjectApplyTeams.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		example.orderBy("sort").asc();
		vo.setTeamsList(projectApplyTeamsMapper.selectByExample(example));

		/**
		 * 流程
		 */
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		/**
		 * 附件
		 */
		vo.setFiles(basicFileAppendixService.findByFormId(vo.getId()));

		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询任务书申报表
	 *@param projectContractApplyVo
	 *@return PageInfo<ProjectContractApply>
	 *@Author: wangsh
	 */
	public PageInfo<ProjectContractApply> findPageByQuery(ProjectContractApplyVo projectContractApplyVo) {
		PageHelper.startPage(projectContractApplyVo.getPageNum(),projectContractApplyVo.getPageSize());
		Example example=new Example(ProjectContractApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(projectContractApplyVo.getName())){
		//	criteria.andEqualTo(projectContractApplyVo.getName());
		//}
		example.orderBy("createTime").desc();
		List<ProjectContractApply> projectContractApplyList=projectContractApplyMapper.selectByExample(example);
		return new PageInfo<ProjectContractApply>(projectContractApplyList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitProjectContractApply(ProjectContractApplyVo projectContractApply) {
		saveOrUpdateProjectContractApply(projectContractApply);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_CONTRACT_PROCESS, projectContractApply, this.mapper, "任务书提交");
		return projectContractApply.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String auditProjectContractApply(ProjectContractApplyVo projectContractApply) {
		flowCommonService.doFlowStepAudit(projectContractApply, this.mapper, projectContractApply.getAuditAdvice()
				, FlowStatusEnum.CONTRACT_T_UPLOAD.getCode(), FlowStatusEnum.CONTRACT_T_UPLOAD.getRole());
		return projectContractApply.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackProjectContractApply(ProjectContractApplyVo projectContractApply) {
		flowCommonService.doFlowStepSendBack(projectContractApply, this.mapper, projectContractApply.getAuditAdvice(), projectContractApply.getToUpper());
		return projectContractApply.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseProjectContractApply(ProjectContractApplyVo projectContractApply) {
		// 添加变更记录
		ProjectContractApplyVo vo = findById(projectContractApply.getId());
		ProjectContractApplyChangeVo changeVo = new ProjectContractApplyChangeVo();
		BeanUtils.copyProperties(vo, changeVo);
		changeVo.setId(null);
		changeVo.setOriginContractId(vo.getId());
		changeVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
		changeVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
		projectContractApplyChangeService.saveOrUpdateProjectContractApplyChange(changeVo);

		// 添加中期检查记录
		if (true) {
			ProjectContractInterimReportVo reportVo = new ProjectContractInterimReportVo();
			BeanUtils.copyProperties(vo, reportVo);
			reportVo.setId(null);
			reportVo.setContractId(vo.getId());
			reportVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			reportVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			projectContractInterimReportService.saveOrUpdateProjectContractInterimReport(reportVo);
		}

		// 添加验收记录
		ProjectApplyInfoVo projectApplyInfoVo = (ProjectApplyInfoVo) projectApplyInfoService.findById(vo.getApplyId());
		ProjectContractAcceptVo acceptVo = new ProjectContractAcceptVo();
		BeanUtils.copyProperties(projectApplyInfoVo, acceptVo);
		acceptVo.setId(null);
		acceptVo.setApplyId(projectApplyInfoVo.getId());
		acceptVo.setContractId(vo.getId());
		acceptVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
		acceptVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
		projectContractAcceptService.saveOrUpdateProjectContractAccept(acceptVo);

		flowCommonService.doCompleteTask(projectContractApply, this.mapper, "任务书下达", FlowStatusEnum.END.getCode(), AssigneeConstant.ORG_ADMIN_ROLE);

		return projectContractApply.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String uploadProjectContractApply(ProjectContractApplyVo projectContractApply) {
		ProjectContractApplyVo old = findById(projectContractApply.getId());
		if (projectContractApply.getFiles() != null){
			BasicFileAppendixVo basicFileAppendix = projectContractApply.getFiles().get(0);
			basicFileAppendix.setFileType("contract");
			old.setFiles(old.getFiles().stream().filter(f->!"contract".equals(f.getFileType())).collect(Collectors.toList()));
			old.getFiles().add(basicFileAppendix);
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(old.getId(), old.getFiles());

			ProjectContractApply contractApply = new ProjectContractApply();
			contractApply.setFlowStatus(FlowStatusEnum.UPLOAD_END.getCode());
			contractApply.setId(projectContractApply.getId());
			this.mapper.updateByPrimaryKeySelective(contractApply);
		} else {
			throw new ServiceException("请上传任务书");
		}

		// 添加变更记录
		ProjectContractApplyVo vo = findById(projectContractApply.getId());
		ProjectContractApplyChangeVo changeVo = new ProjectContractApplyChangeVo();
		BeanUtils.copyProperties(vo, changeVo);
		changeVo.setId(null);
		changeVo.setOriginContractId(vo.getId());
		changeVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
		changeVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
		projectContractApplyChangeService.saveOrUpdateProjectContractApplyChange(changeVo);

		// 添加中期检查记录
		if (true) {
			ProjectContractInterimReportVo reportVo = new ProjectContractInterimReportVo();
			BeanUtils.copyProperties(vo, reportVo);
			reportVo.setId(null);
			reportVo.setContractId(vo.getId());
			reportVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			reportVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			projectContractInterimReportService.saveOrUpdateProjectContractInterimReport(reportVo);
		}

		// 添加验收记录
		ProjectApplyInfoVo projectApplyInfoVo = (ProjectApplyInfoVo) projectApplyInfoService.findById(vo.getApplyId());
		ProjectContractAcceptVo acceptVo = new ProjectContractAcceptVo();
		BeanUtils.copyProperties(projectApplyInfoVo, acceptVo);
		acceptVo.setId(null);
		acceptVo.setApplyId(projectApplyInfoVo.getId());
		acceptVo.setContractId(vo.getId());
		acceptVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
		acceptVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
		projectContractAcceptService.saveOrUpdateProjectContractAccept(acceptVo);
		//flowCommonService.doCompleteTask(projectContractApply, this.mapper, "任务书完成上传", FlowStatusEnum.END.getCode(), "");
		return projectContractApply.getId();
	}

	@Override
	public PageInfo todoList(ProjectContractApplyVo projectContractApply) {
		Example example = new Example(ProjectContractApply.class);
		Criteria criteria = getCriteria(projectContractApply, example);
		example.orderBy("createTime").desc();
		PageHelper.startPage(projectContractApply.getPageNum(),projectContractApply.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(projectContractApply, this.mapper, example, criteria));
	}

	@Override
	public PageInfo finishedList(ProjectContractApplyVo projectContractApply) {
		Example example = new Example(ProjectContractApply.class);
		Criteria criteria = getCriteria(projectContractApply, example);
		example.orderBy("createTime").desc();
		PageHelper.startPage(projectContractApply.getPageNum(),projectContractApply.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(projectContractApply, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<ProjectContractApply> endList(ProjectContractApplyVo vo) {
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		Example example=new Example(ProjectContractApply.class);
		Criteria criteria = getCriteria(vo, example).andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		example.orderBy("createTime").desc();
		// List<ProjectContractApply> projectContractApplyList=projectContractApplyMapper.selectByExample(example);
		return new PageInfo<ProjectContractApply>(flowCommonService.endList(vo, this.mapper, example, criteria));
	}

	private Criteria getCriteria(ProjectContractApplyVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getProjectName())){
			criteria.andLike("projectName", "%" + vo.getProjectName()+ "%");
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getProjectTypeCode())){
			criteria.andEqualTo("projectTypeCode", vo.getProjectTypeCode());
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getProjectSecondTypeCode())){
			criteria.andEqualTo("projectSecondTypeCode", vo.getProjectSecondTypeCode());
		}
		if(null != vo.getSubmitDateStart()){
			criteria.andGreaterThanOrEqualTo("submitDate", vo.getSubmitDateStart());
		}
		if(null != vo.getSubmitDateEnd()){
			criteria.andLessThanOrEqualTo("submitDate", new Date(vo.getSubmitDateEnd().getTime() + 24 * 60 * 60 * 1000 - 1));
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getApplyUnitName())){
			criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName()+ "%");
		}
		return criteria;
	}

	@Override
	public void download(String id, HttpServletResponse response) {
		Map<String, Object> map = new HashMap<String, Object>();
		OutputStream out = null;
		try {
			ProjectContractApplyVo projectContractApplyVo = this.findById(id);

			if(projectContractApplyVo == null){
				throw new ServiceException("未找到项目申请");
			}
			Method[] methods = ProjectContractApplyVo.class.getMethods();
			for (Method method:methods){
				if(method.getName().startsWith("get")){
					Object o = method.invoke(projectContractApplyVo);
					String filedName = method.getName().substring(3, 4).toLowerCase() + method.getName().substring(4);
					if(o!=null) {
						if(o instanceof List){
							map.put(filedName, o);
						} else if(o instanceof Date){
							map.put(filedName, dateFormat.format((Date) o));
						} else {
							map.put(filedName, o.toString());
						}
					} else {
						map.put(filedName, "");
					}
				}
			}
			finishMap(map, projectContractApplyVo);
			String filename = "科研项目任务书";
			/*if(projectContractApplyVo.getContractType().contains("雏鹰")){
				filename = "雏鹰计划项目任务书";
			}else if(projectContractApplyVo.getContractType().contains("装备")){
				filename = "装备项目任务书";
			}else if(projectContractApplyVo.getContractType().contains("科研")){
				filename = "科研项目任务书";
			}*/
			String model = "word/"+filename+"模板.docx";
			ClassPathResource classPathResource = new ClassPathResource(model);
			InputStream inputStream = null;
			try {
				inputStream = classPathResource.getInputStream();
			} catch (IOException e) {
				throw new ServiceException("加载文件失败");
			}
			MyXWPFDocument doc = new MyXWPFDocument(inputStream);
//			XWPFDocument doc = WordExportUtil.exportWord07("src/main/resources/word/"+filename+"模板.docx", map);
			WordExportUtil.exportWord07(doc, map);
			response.setCharacterEncoding("UTF-8");
			response.setHeader("content-Type", "multipart/form-data");
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.setHeader("Content-Disposition",
					"attachment;filename=" + URLEncoder.encode(filename, "UTF-8") + ".docx");
			out = response.getOutputStream();
			doc.write(out);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
					logger.error("输出流关闭失败!", e);
				}
			}
		}
	}

	@Override
	public ProjectContractApplyVo findByApplyId(String id) {
		Example example = new Example(ProjectContractApply.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
		return findById(this.mapper.selectOneByExample(example).getId());
	}
	private void finishMap(Map<String, Object> map, ProjectContractApplyVo vo) {
		// 参与单位
		if(!CollectionUtils.isEmpty(vo.getUnitList())){
			finishMapChild(map, vo.getUnitList(), "pcru_");
		}
		// 项目组成员
		if(!CollectionUtils.isEmpty(vo.getTeamsList())){
			finishMapChild(map, vo.getTeamsList(), "pct_");
		}
		// 进度安排
		if(!CollectionUtils.isEmpty(vo.getProgressList())){
			finishMapChild(map, vo.getProgressList(), "pcp_");
		}
		// 预算
		if(vo.getProjectApplyCost() != null && vo.getProjectAllowanceCost() != null){
			List list = new ArrayList();
			list.add(vo.getProjectApplyCost());
			list.add(vo.getProjectAllowanceCost());
			finishMapChild(map, list, "pcc_");
		}

		// 设备
		if(!CollectionUtils.isEmpty(vo.getDevicesList())){
			finishMapChild(map, vo.getDevicesList(), "pcd_");
		}

	}

	private void finishMapChild(Map<String, Object> map, List list, String prefix) {
		try {
			Class clazz = list.get(0).getClass();
			Method[] methods = clazz.getMethods();
			for (int i = 0; i < list.size(); i++) {
				Object obj = list.get(i);
				for (Method method : methods) {
					if (method.getName().startsWith("get")) {
						Object o = method.invoke(obj);
						String filedName = method.getName().substring(3, 4).toLowerCase() + method.getName().substring(4);
						String key;
						if(filedName.equals("name") || filedName.equals("workPart")) {
							key = String.format(format, prefix, filedName, i + 1);
						} else {
							key = String.format(format, "", filedName, i + 1);
						}
						if (o != null) {
							if(o instanceof Date){
								map.put(key, dateFormat.format((Date) o));
							} else {
								map.put(key, o.toString());
							}
						} else {
							map.put(key, "");
						}
					}
				}
			}
		} catch (Exception e){
			e.printStackTrace();
		}
	}


	@Override
	@Transactional(readOnly = false)
	public void tempApplyToInterim() {
		Example example = new Example(ProjectApplyInfo.class);
		example.createCriteria().andEqualTo("yn", 1).andLessThanOrEqualTo("yearNo", 2022);
		List<ProjectApplyInfo> infos = projectApplyInfoMapper.selectByExample(example);

		example = new Example(ProjectContractApply.class);
		example.createCriteria().andEqualTo("yn", 1).andIn("applyId", infos.stream().map(info->info.getId()).collect(Collectors.toList()));
		List<ProjectContractApply> contracts = projectContractApplyMapper.selectByExample(example);

		infos.stream().filter(info->StringUtils.hasText(info.getProjectSecondTypeCode()) && StringUtils.hasText(info.getProjectTypeCode())).filter(info->{
			for (ProjectContractApply contract:contracts) {
				// 过滤已存在任务书的记录
				if(info.getId().equals(contract.getApplyId())){
					return false;
				}
			}
			return true;
		}).forEach(info->{
			ProjectApplyInfoVo vo = (ProjectApplyInfoVo) projectApplyInfoService.findById(info.getId());

			// 申请编号
			if(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getProjectTypeCode())
					&& org.apache.commons.lang3.StringUtils.isBlank(vo.getProjectNumber())
					&& null != vo.getYearNo()){
				vo.setProjectNumber(projectApplyInfoService.createProjectNumber(vo));
			}
			vo.setAttr1("已立项");
			vo.setFlowStatus("999");
			projectApplyInfoMapper.updateByPrimaryKeySelective(vo);

			// 转换成任务书
			ProjectContractApplyVo projectContractApplyVo = vo.toProjectContractApplyVo();
			projectContractApplyVo.setId(null);
			projectContractApplyVo.setFlowStatus(FlowStatusEnum.END.getCode());
			projectContractApplyVo.setFlowUser(FlowStatusEnum.END.getRole());
			projectContractApplyVo.setApplyId(vo.getId());
			projectContractApplyVo.setEntrustUnitName("浙江省市场监督管理局");
			saveOrUpdateProjectContractApply(projectContractApplyVo);


			// 添加中期检查记录
			if (true) {
				ProjectContractInterimReportVo reportVo = new ProjectContractInterimReportVo();
				BeanUtils.copyProperties(projectContractApplyVo, reportVo);
				reportVo.setId(null);
				reportVo.setContractId(projectContractApplyVo.getId());
				reportVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
				reportVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
				projectContractInterimReportService.saveOrUpdateProjectContractInterimReport(reportVo);
			}
		});
	}

}
