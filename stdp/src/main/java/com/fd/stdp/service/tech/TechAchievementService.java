package com.fd.stdp.service.tech;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.tech.TechAchievement;
import com.fd.stdp.beans.tech.vo.TechAchievementVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 *@Description: 成果统计
 *@Author: wangsh
 *@Date: 2022-01-04 13:49:38
 */
public interface TechAchievementService {

	/**
	 *@Description: 保存或更新成果统计
	 *@param techAchievement 成果统计对象
	 *@return String 成果统计ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTechAchievement(TechAchievementVo techAchievement);
	
	/**
	 *@Description: 删除成果统计
	 *@param id void 成果统计ID
	 *@Author: wangsh
	 */
	void deleteTechAchievement(String id);

	/**
	 *@Description: 查询成果统计详情
	 *@param id
	 *@return TechAchievement
	 *@Author: wangsh
	 */
	TechAchievement findById(String id);

	/**
	 *@Description: 分页查询成果统计
	 *@param techAchievementVo
	 *@return PageInfo<TechAchievement>
	 *@Author: wangsh
	 */
	PageInfo<TechAchievement> findPageByQuery(TechAchievementVo techAchievementVo);

	/**
	 * 成果统计任务提交
	 * @param techAchievement
	 * @return
	 */
    String submitTechAchievement(TechAchievementVo techAchievement);

	/**
	 * 成果统计审核
	 * @param techAchievementVo
	 * @return
	 */
	String auditTechAchievement(TechAchievementVo techAchievementVo);

	/**
	 * 成果统计退回
	 * @param techAchievementVo
	 * @return
	 */
	String sendBackTechAchievement(TechAchievementVo techAchievementVo);

	/**
	 * 批量删除
	 * @param ids
	 */
    void deleteMultiTechAchievement(List<String> ids);

	/**
	 * 待办列表
	 * @param techAchievementVo
     * @return
	 */
    PageInfo todoTechAchievement(TechAchievementVo techAchievementVo);

	/**
	 * 已办待办列表
	 * @param techAchievementVo
	 * @return
	 */
	PageInfo finishedTechAchievement(TechAchievementVo techAchievementVo);

	/**
	 * 已完成的列表
	 * @param techAchievementVo
	 * @return
	 */
	PageInfo<TechAchievement> endList(TechAchievementVo techAchievementVo);

	/**
	 * 撤回
	 * @param techAchievementVo
	 * @return
	 */
    String rejectTechAchievement(TechAchievementVo techAchievementVo);

    void exportTechAchievement(TechAchievementVo techAchievementVo, HttpServletResponse response);
}
