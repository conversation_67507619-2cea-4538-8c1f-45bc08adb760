package com.fd.stdp.service.audit.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import com.fd.stdp.beans.audit.InnerAuditOrg;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.dao.audit.InnerAuditOrgMapper;
import com.fd.stdp.service.user.SysUserUtilService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.audit.InnerAuditOpen;
import com.fd.stdp.beans.audit.vo.InnerAuditOpenVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.audit.InnerAuditOpenMapper;
import com.fd.stdp.service.audit.InnerAuditOpenService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import static com.fd.stdp.common.BaseController.getCurrentOrgName;
import static com.fd.stdp.common.BaseController.getLoginUser;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 内审开放功能配置
 *@Author: wangsh
 *@Date: 2022-02-23 15:56:20
 */
public class InnerAuditOpenServiceImpl extends BaseServiceImpl<InnerAuditOpenMapper, InnerAuditOpen> implements InnerAuditOpenService{

	public static final Logger logger = LoggerFactory.getLogger(InnerAuditOpenServiceImpl.class);
	
	@Autowired
	private InnerAuditOpenMapper innerAuditOpenMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private SysUserUtilService sysUserUtilService;
	@Autowired
	private InnerAuditOrgMapper innerAuditOrgMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新内审开放功能配置
	 *@param innerAuditOpen 内审开放功能配置对象
	 *@return String 内审开放功能配置ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnerAuditOpen(InnerAuditOpenVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			innerAuditOpenMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innerAuditOpenMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除内审开放功能配置
	 *@param id void 内审开放功能配置ID
	 *@Author: wangsh
	 */
	public void deleteInnerAuditOpen(String id) {
		//TODO 做判断后方能执行删除
		InnerAuditOpen innerAuditOpen=innerAuditOpenMapper.selectByPrimaryKey(id);
		if(innerAuditOpen==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnerAuditOpen teminnerAuditOpen=new InnerAuditOpen();
		teminnerAuditOpen.setYn(CommonConstant.FLAG_NO);
		teminnerAuditOpen.setId(innerAuditOpen.getId());
		innerAuditOpenMapper.updateByPrimaryKeySelective(teminnerAuditOpen);
	}

    /**
     * @Description: 批量删除内审开放功能配置
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnerAuditOpen(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnerAuditOpen(id));
	}

	@Override
	/**
	 *@Description: 查询内审开放功能配置详情
	 *@param id
	 *@return InnerAuditOpen
	 *@Author: wangsh
	 */
	public InnerAuditOpen findById(String id) {
		return innerAuditOpenMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询内审开放功能配置
	 *@param innerAuditOpenVo
	 *@return PageInfo<InnerAuditOpen>
	 *@Author: wangsh
	 */
	public PageInfo<InnerAuditOpen> findPageByQuery(InnerAuditOpenVo innerAuditOpenVo) {

		boolean isAuditOrg = false;
		Example example = new Example(InnerAuditOrg.class);
		example.createCriteria()
				.andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("orgName", getCurrentOrgName());
		if(!CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))) {
			isAuditOrg = true;
		}

		PageHelper.startPage(innerAuditOpenVo.getPageNum(),innerAuditOpenVo.getPageSize());
		example=new Example(InnerAuditOpen.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innerAuditOpenVo.getName())){
		//	criteria.andEqualTo(innerAuditOpenVo.getName());
		//}
		Integer operType = getOperType();
		if(operType == 2) {

		} else {
			criteria.andEqualTo("status", 1);
		}
		if(isAuditOrg){
			criteria.andLike("targetUnit", "%省局直属单位%");
		} else {
			if(operType == 2){
				//criteria.andLike("targetUnit", "%省局%");
			}else if(operType == 1){
				criteria.andLike("targetUnit", "%市局%");
			} else if (operType == 0){
				criteria.andLike("targetUnit", "%县区局%");
			}
		}
		example.orderBy("years").asc().orderBy("createTime").desc();
		List<InnerAuditOpen> innerAuditOpenList=innerAuditOpenMapper.selectByExample(example);
		return new PageInfo<InnerAuditOpen>(innerAuditOpenList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnerAuditOpen(InnerAuditOpenVo innerAuditOpenVo) {
		String id = this.saveOrUpdateInnerAuditOpen(innerAuditOpenVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innerAuditOpenVo, this.mapper,
				StringUtils.isNotBlank(innerAuditOpenVo.getAuditAdvice())?innerAuditOpenVo.getAuditAdvice():"提交内审开放功能配置");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnerAuditOpen(InnerAuditOpenVo innerAuditOpenVo) {
		flowCommonService.doFlowStepAudit(innerAuditOpenVo, this.mapper
				, StringUtils.isNotBlank(innerAuditOpenVo.getAuditAdvice()) ? innerAuditOpenVo.getAuditAdvice() : "内审开放功能配置审核通过"
				, FlowStatusEnum.END.getCode());
		return innerAuditOpenVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnerAuditOpen(InnerAuditOpenVo innerAuditOpenVo) {
		flowCommonService.doFlowStepSendBack(innerAuditOpenVo, this.mapper
				, StringUtils.isNotBlank(innerAuditOpenVo.getAuditAdvice())?innerAuditOpenVo.getAuditAdvice():"内审开放功能配置退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnerAuditOpen(InnerAuditOpenVo innerAuditOpenVo) {
		flowCommonService.doCompleteTask(innerAuditOpenVo, this.mapper
				, "内审开放功能配置任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnerAuditOpen> todoList(InnerAuditOpenVo vo) {

		Example example = new Example(InnerAuditOpen.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnerAuditOpen> finishedList(InnerAuditOpenVo vo) {
		Example example = new Example(InnerAuditOpen.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnerAuditOpen> endList(InnerAuditOpenVo vo) {
        Example example = new Example(InnerAuditOpen.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	@Override
	@Transactional(readOnly = false)
	public String open(InnerAuditOpenVo innerAuditOpen) {

		InnerAuditOpen open = this.mapper.selectByPrimaryKey(innerAuditOpen.getId());
		Date startDate = open.getStartTime();
		Date endDate = new Date(open.getEndTime().getTime() + 24*60*60*1000 - 1);

		Example example = new Example(InnerAuditOpen.class);
		example.createCriteria()
				.andEqualTo("status", 1).andEqualTo("yn", CommonConstant.FLAG_YES);
		List<InnerAuditOpen> openedList = this.mapper.selectByExample(example);
		for (InnerAuditOpen old:openedList) {
			if(old.getEndTime().getTime() < startDate.getTime() || old.getStartTime().getTime() > endDate.getTime()) {
				continue;
			}
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			throw new ServiceException("发布失败，与已发布记录的开放期限冲突" + df.format(old.getStartTime()) + "-" + df.format(old.getEndTime()));
		}

		open.setStatus("1");
		this.mapper.updateByPrimaryKeySelective(open);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String close(InnerAuditOpenVo innerAuditOpen) {
		InnerAuditOpen open = this.mapper.selectByPrimaryKey(innerAuditOpen.getId());
		open.setStatus("0");
		this.mapper.updateByPrimaryKeySelective(open);
		return null;
	}

	@Override
	public Object isOpen() {
		return null;
	}

	private Criteria getCriteria(InnerAuditOpen vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
		return criteria;
	}

	private Integer getOperType(){
		LoginUser loginUser = getLoginUser();
		Integer operType = sysUserUtilService.getUserOperType(loginUser);
		Example example = new Example(InnerAuditOrg.class);
		example.createCriteria().andEqualTo("orgName", getCurrentOrgName());
		if(CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))){
			return operType;
		}
		return 0;
	}
}
