package com.fd.stdp.service.project.impl;

import java.util.*;

import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.project.*;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertMumberVo;
import com.fd.stdp.constant.ProcessConstants;
import com.fd.stdp.dao.project.*;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.user.SysUserUtilService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.vo.ProjectContractAcceptVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.service.project.ProjectContractAcceptService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import static com.fd.stdp.common.BaseController.*;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 任务书验收表单
 *@Author: wangsh
 *@Date: 2022-03-03 10:16:28
 */
public class ProjectContractAcceptServiceImpl extends BaseServiceImpl<ProjectContractAcceptMapper, ProjectContractAccept> implements ProjectContractAcceptService{

	public static final Logger logger = LoggerFactory.getLogger(ProjectContractAcceptServiceImpl.class);

	@Autowired
	private ProjectContractAcceptMapper projectContractAcceptMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private ProjectApplyCostMapper projectApplyCostMapper;
	@Autowired
	private ProjectApplyDevicesMapper projectApplyDevicesMapper;
	@Autowired
	private ProjectApplyCooperationUnitMapper projectApplyCooperationUnitMapper;
	@Autowired
	private ProjectApplyTeamsMapper projectApplyTeamsMapper;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	@Autowired
	private ProjectApplyExpertMumberMapper projectApplyExpertMumberMapper;

	@Autowired
	private SysUserUtilService sysUserUtilService;
	@Autowired
	private FlowApiService flowApiService;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新任务书验收表单
	 *@param projectContractAccept 任务书验收表单对象
	 *@return String 任务书验收表单ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateProjectContractAccept(ProjectContractAcceptVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			projectContractAcceptMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			projectContractAcceptMapper.updateByPrimaryKeySelective(vo);
		}

		/**
		 * 预算
		 */
		if(vo.getProjectApplyCost() != null || vo.getProjectAllowanceCost() != null || vo.getProjectSelfCost() != null) {
			List<ProjectApplyCost> costList = new ArrayList<>();
			if (vo.getProjectApplyCost() != null) {
				vo.getProjectApplyCost().setCostType("0");
				costList.add(vo.getProjectApplyCost());
			}
			if (vo.getProjectAllowanceCost() != null) {
				vo.getProjectAllowanceCost().setCostType("1");
				costList.add(vo.getProjectAllowanceCost());
			}
			if (vo.getProjectSelfCost() != null) {
				vo.getProjectSelfCost().setCostType("5");
				costList.add(vo.getProjectSelfCost());
			}
			updateList(vo, costList, projectApplyCostMapper, "setApplyId");
		}

		/**
		 * 设备
		 */
		if(vo.getDevicesList() != null){
			//updateDevice(vo);
			updateList(vo, vo.getDevicesList(), projectApplyDevicesMapper, "setApplyId");
		}
		/**
		 * 合作单位
		 */
		if(vo.getUnitList() != null) {
			updateList(vo, vo.getUnitList(), projectApplyCooperationUnitMapper, "setApplyId");
		}
		/**
		 * 项目团队
		 */
		if(vo.getTeamsList() != null) {
			updateList(vo, vo.getTeamsList(), projectApplyTeamsMapper, "setApplyId");
		}
		// 附件
		if(vo.getFiles()!=null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}

		// 专家组
		if(vo.getExperts() != null){
			updateList(vo, vo.getExperts(), projectApplyExpertMumberMapper, "setApplyId");
			// basicGradeLinkedService.clearAndUpdateBasicGradeLinked(vo.getId(), vo.getGradeExperts());
		}

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除任务书验收表单
	 *@param id void 任务书验收表单ID
	 *@Author: wangsh
	 */
	public void deleteProjectContractAccept(String id) {
		//TODO 做判断后方能执行删除
		ProjectContractAccept projectContractAccept=projectContractAcceptMapper.selectByPrimaryKey(id);
		if(projectContractAccept==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		ProjectContractAccept temprojectContractAccept=new ProjectContractAccept();
		temprojectContractAccept.setYn(CommonConstant.FLAG_NO);
		temprojectContractAccept.setId(projectContractAccept.getId());
		projectContractAcceptMapper.updateByPrimaryKeySelective(temprojectContractAccept);
	}

    /**
     * @Description: 批量删除任务书验收表单
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiProjectContractAccept(List<String> ids) {
		ids.stream().forEach(id-> this.deleteProjectContractAccept(id));
	}

	@Override
	/**
	 *@Description: 查询任务书验收表单详情
	 *@param id
	 *@return ProjectContractAccept
	 *@Author: wangsh
	 */
	public ProjectContractAccept findById(String id) {
		ProjectContractAccept projectContractAccept = projectContractAcceptMapper.selectByPrimaryKey(id);
		ProjectContractAcceptVo vo = new ProjectContractAcceptVo();
		BeanUtils.copyProperties(projectContractAccept, vo);
		/**
		 * 预算
		 */
		Example example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "0");
		vo.setProjectApplyCost(projectApplyCostMapper.selectOneByExample(example));

		example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "1");
		vo.setProjectAllowanceCost(projectApplyCostMapper.selectOneByExample(example));

		example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "5");
		vo.setProjectSelfCost(projectApplyCostMapper.selectOneByExample(example));

		if(vo.getProjectApplyCost() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("0");
			vo.setProjectApplyCost(p);
		}
		if(vo.getProjectAllowanceCost() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("1");
			vo.setProjectAllowanceCost(p);
		}
		if(vo.getProjectSelfCost() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("5");
			vo.setProjectSelfCost(p);
		}

		/**
		 * 设备
		 */
		example = new Example(ProjectApplyDevices.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		example.orderBy("sort").asc();
		vo.setDevicesList(projectApplyDevicesMapper.selectByExample(example));
		/**
		 * 合作单位
		 */
		example = new Example(ProjectApplyCooperationUnit.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		example.orderBy("sort").asc();
		vo.setUnitList(projectApplyCooperationUnitMapper.selectByExample(example));
		/**
		 * 项目团队
		 */
		example = new Example(ProjectApplyTeams.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		example.orderBy("sort").asc();
		vo.setTeamsList(projectApplyTeamsMapper.selectByExample(example));

		/**
		 * 流程
		 */
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		/**
		 * 附件
		 */
		vo.setFiles(basicFileAppendixService.findByFormId(vo.getId()));

		/**
		 * 评审意见
		 */
		// vo.setGradeExperts(basicGradeLinkedService.findByFormId(vo.getId()));
		example = new Example(ProjectApplyExpertMumber.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		vo.setExperts(projectApplyExpertMumberMapper.selectByExample(example));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询任务书验收表单
	 *@param projectContractAcceptVo
	 *@return PageInfo<ProjectContractAccept>
	 *@Author: wangsh
	 */
	public PageInfo<ProjectContractAccept> findPageByQuery(ProjectContractAcceptVo projectContractAcceptVo) {
		PageHelper.startPage(projectContractAcceptVo.getPageNum(),projectContractAcceptVo.getPageSize());
		Example example=new Example(ProjectContractAccept.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(projectContractAcceptVo.getName())){
		//	criteria.andEqualTo(projectContractAcceptVo.getName());
		//}
		List<ProjectContractAccept> projectContractAcceptList=projectContractAcceptMapper.selectByExample(example);
		return new PageInfo<ProjectContractAccept>(projectContractAcceptList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitProjectContractAccept(ProjectContractAcceptVo projectContractAcceptVo) {
		String id = this.saveOrUpdateProjectContractAccept(projectContractAcceptVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_PROJECT_ACCEPT, projectContractAcceptVo, this.mapper,
				StringUtils.isNotBlank(projectContractAcceptVo.getAuditAdvice())?projectContractAcceptVo.getAuditAdvice():"提交任务书验收表单");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditProjectContractAccept(ProjectContractAcceptVo projectContractAcceptVo) {
		saveOrUpdateProjectContractAccept(projectContractAcceptVo);
		ProjectContractAccept old = findById(projectContractAcceptVo.getId());

		Integer operType = sysUserUtilService.getUserOperType(getLoginUser());

		String taskId = flowApiService.getTaskId(old.getId());
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("CODETYPE", operType);
		map.put("ISPASS", 1);
		FlowTaskVo flowTaskVo = new FlowTaskVo();
		flowTaskVo.setTaskId(taskId);
		flowTaskVo.setBusinessKey(old.getId());
		flowTaskVo.setUserId(getCurrentUserId());
		flowTaskVo.setUserName(getCurrentUserName());
		flowTaskVo.setComment(projectContractAcceptVo.getAuditAdvice());
		flowTaskVo.setValues(map);
		if(StringUtils.equals(old.getFlowStatus(), FlowStatusEnum.ORG_AUDIT.getCode())) {
			// 如果当前是是申请单位审核，设置下一级为相同等级
			switch (operType){
				case 0:
					// 设置下一级为县级
					flowTaskVo.setAssignee(AssigneeConstant.DEPT_COUNTY_ROLE);
					old.setFlowStatus(FlowStatusEnum.COUNTY_AUDIT.getCode());
					break;
				case 1:
					// 设置下一级为市级
					flowTaskVo.setAssignee(AssigneeConstant.DEPT_CITY_ROLE);
					old.setFlowStatus(FlowStatusEnum.CITY_AUDIT.getCode());
					break;
				case 2:
					// 设置下一级为省级技术审核
					flowTaskVo.setAssignee(AssigneeConstant.TEC_ORG_AGENT);
					old.setFlowStatus(FlowStatusEnum.PROVINCE_TECH_AUDIT.getCode());
					break;
				default:
			}
		} else if(StringUtils.equals(FlowStatusEnum.CITY_AUDIT.getCode(), old.getFlowStatus())){
			// 市局审核 设置下一级为省级技术审核
			flowTaskVo.setAssignee(AssigneeConstant.TEC_ORG_AGENT);
			old.setFlowStatus(FlowStatusEnum.PROVINCE_TECH_AUDIT.getCode());
		} else if(StringUtils.equals(FlowStatusEnum.PROVINCE_TECH_AUDIT.getCode(), old.getFlowStatus())){
			// 省级技术审核 设置下一级为省级
			flowTaskVo.setAssignee(AssigneeConstant.DEPT_PROVINCE_ROLE);
			old.setFlowStatus(FlowStatusEnum.PROVINCE_AUDIT.getCode());
		} else if(StringUtils.equals(FlowStatusEnum.PROVINCE_AUDIT.getCode(), old.getFlowStatus())) {
			// 省级审核 如果是雏鹰项目自然结项 直接完成 否则选择专家
			if(old.getProjectTypeCode().equals("CY") && StringUtils.equals(projectContractAcceptVo.getIsTheEnd(), "1")){
				map.put("ISPASS", 2);
			} else {
				if(CollectionUtils.isEmpty(projectContractAcceptVo.getExperts())){
					throw new ServiceException("请选择专家");
				}
				// 设置专家
				flowTaskVo.setAssignee(AssigneeConstant.EXPERT_ROLE);
				old.setFlowStatus(FlowStatusEnum.EXPERTS_GRADE.getCode());
			}
		} else {
			// 否则设置下一级
			switch (operType) {
				case 0:
					// 县级审核  设置下一级为市级
					flowTaskVo.setAssignee(AssigneeConstant.DEPT_CITY_ROLE);
					old.setFlowStatus(FlowStatusEnum.CITY_AUDIT.getCode());
					break;
				case 1:
					// 市级审核  设置下一级为省级
					flowTaskVo.setAssignee(AssigneeConstant.DEPT_PROVINCE_ROLE);
					old.setFlowStatus(FlowStatusEnum.PROVINCE_AUDIT.getCode());
					break;
				default:
			}
		}
		map.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());

		old.setFlowUser(flowTaskVo.getAssignee());
		mapper.updateByPrimaryKeySelective(old);
		flowApiService.completeTask(flowTaskVo);

		return projectContractAcceptVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackProjectContractAccept(ProjectContractAcceptVo projectContractAcceptVo) {
		flowCommonService.doFlowStepSendBack(projectContractAcceptVo, this.mapper
				, StringUtils.isNotBlank(projectContractAcceptVo.getAuditAdvice())?projectContractAcceptVo.getAuditAdvice():"任务书验收表单退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseProjectContractAccept(ProjectContractAcceptVo projectContractAcceptVo) {
		flowCommonService.doCompleteTask(projectContractAcceptVo, this.mapper
				, "任务书验收表单任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<ProjectContractAccept> todoList(ProjectContractAcceptVo vo) {

		Example example = new Example(ProjectContractAccept.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<ProjectContractAccept> finishedList(ProjectContractAcceptVo vo) {
		Example example = new Example(ProjectContractAccept.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
    public PageInfo<ProjectContractAccept> endList(ProjectContractAcceptVo vo) {
        Example example = new Example(ProjectContractAccept.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	@Override
	public String expertSubmitProjectContractAccept(ProjectApplyExpertMumberVo vo) {
		String userId = getCurrentUserId();
		Example example = new Example(ProjectApplyExpertMumber.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("userId", userId)
				.andEqualTo("applyId", vo.getId());
		List<ProjectApplyExpertMumber> gradeLinkeds = projectApplyExpertMumberMapper.selectByExample(example);
		if(CollectionUtils.isEmpty(gradeLinkeds)){
			throw new ServiceException("未找到评审记录");
		}
		ProjectApplyExpertMumber p = gradeLinkeds.get(0);
		p.setOpinion(vo.getOpinion());
		projectApplyExpertMumberMapper.updateByPrimaryKeySelective(p);

		// 全部评审完成后 专家组组长提交进入下一步流程
		boolean allFinish = true; // 全部评审完成
		boolean isExpertLeader = true; // 是专家组组长
		example = new Example(ProjectApplyExpertMumber.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", vo.getId())
				.andIsNull("opinion");
		if(!CollectionUtils.isEmpty(projectApplyExpertMumberMapper.selectByExample(example))){
			allFinish = false;
		}
		example = new Example(ProjectApplyExpertMumber.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", vo.getId())
				.andEqualTo("userId", userId).andEqualTo("expertType", 1);
		if(CollectionUtils.isEmpty(projectApplyExpertMumberMapper.selectByExample(example))){
			isExpertLeader = false;
		}
		if(allFinish && isExpertLeader) {
			ProjectContractAcceptVo obj = (ProjectContractAcceptVo) this.findById(vo.getId());
			flowCommonService.doCompleteTask(obj, this.mapper, "专家评审完成", FlowStatusEnum.PROJECT_REVIEW.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		}
		return null;
	}

	private Criteria getCriteria(ProjectContractAcceptVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getProjectName())){
			criteria.andLike("projectName", "%" + vo.getProjectName()+ "%");
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getProjectTypeCode())){
			criteria.andEqualTo("projectTypeCode", vo.getProjectTypeCode());
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getProjectSecondTypeCode())){
			criteria.andEqualTo("projectSecondTypeCode", vo.getProjectSecondTypeCode());
		}
		if(null != vo.getSubmitDateStart()){
			criteria.andGreaterThanOrEqualTo("submitDate", vo.getSubmitDateStart());
		}
		if(null != vo.getSubmitDateEnd()){
			criteria.andLessThanOrEqualTo("submitDate", new Date(vo.getSubmitDateEnd().getTime() + 24 * 60 * 60 * 1000 - 1));
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getApplyUnitName())){
			criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName()+ "%");
		}
		example.orderBy("createTime").desc();
		return criteria;
	}
}
