package com.fd.stdp.service.flowable;

import com.fd.stdp.beans.flowable.FlowTaskDto;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.beans.tech.vo.TechAchievementVo;
import com.fd.stdp.common.BaseEntity;
import com.fd.stdp.common.mybatis.BaseInfoMapper;
import com.fd.stdp.dao.project.ProjectApplyInfoMapper;
import com.fd.stdp.dao.tech.TechAchievementMapper;
import com.github.pagehelper.PageInfo;
import tk.mybatis.mapper.common.base.update.UpdateByPrimaryKeySelectiveMapper;
import tk.mybatis.mapper.common.example.SelectByExampleMapper;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Map;

/**
 *
 */
public interface FlowCommonService {
    /**
     * 创建并获取一个FlowTaskVo
     * @param taskId
     * @param businessKey
     * @param comment
     * @param assignee
     * @param values
     * @return
     */
    FlowTaskVo createFlowTaskVo(String taskId, String businessKey, String comment, String assignee, Map<String, Object> values);

    /**
     * 开始一个流程 会根据businessKey检查流程是否已存在，如已存在则什么都不会做，可以放心调用
     * @param processInstanceKey
     * @param obj
     * @param mapper
     * @param comment
     */
    String doFlowStart(String processInstanceKey, BaseEntity obj, BaseInfoMapper mapper, String comment);

    /**
     * 执行工作流的提交任务 通用
     * @param processInstanceKey 流程标识
     * @param obj 业务对象
     * @param mapper 业务mapper
     * @param comment 流程意见
     */
    void doFlowStepSubmit(String processInstanceKey, BaseEntity obj, BaseInfoMapper mapper, String comment);

    /**
     * 执行工作流的审核任务 通用
     * @param obj 业务对象
     * @param mapper 业务mapper
     * @param comment 流程意见
     * @param nextFlowStatus 审核结束后的下一个状态
     */
    void doFlowStepAudit(BaseEntity obj, BaseInfoMapper mapper, String comment, String nextFlowStatus);
    /**
     * 执行工作流的审核任务 通用
     * @param obj 业务对象
     * @param mapper 业务mapper
     * @param comment 流程意见
     * @param nextFlowStatus 审核结束后的下一个状态
     * @param nextFlowAssgine 审核结束后的下一个状态的角色
     */
    void doFlowStepAudit(BaseEntity obj, BaseInfoMapper mapper, String comment, String nextFlowStatus, String nextFlowAssgine);

    /**
     * 执行工作流的退回任务 通用
     * @param obj 业务对象
     * @param mapper 业务mapper
     * @param comment 流程意见
     * @param toUpper 是否退回到上一级
     */
    void doFlowStepSendBack(BaseEntity obj, BaseInfoMapper mapper, String comment, Boolean toUpper);

    /**
     * 工作流的完成当前任务 通用
     * @param obj 业务对象
     * @param mapper 业务mapper
     * @param comment 流程意见
     * @param nextFlowStatus 完成后该流程的状态
     * @param nextAssignee 完成后该流程的assignee
     * @param completeUserName 完成后该流程的签名
     * @param values 需求的参数map
     */
    void doCompleteTask(BaseEntity obj, BaseInfoMapper mapper, String comment, String nextFlowStatus, String nextAssignee, String completeUserName, Map values);
    void doCompleteTask(BaseEntity obj, BaseInfoMapper mapper, String comment, String nextFlowStatus, String nextAssignee, String completeUserName);
    void doCompleteTask(BaseEntity obj, BaseInfoMapper mapper, String comment, String nextFlowStatus, String nextAssignee);

    /**
     * 获取待办列表
     * @param obj
     * @param mapper
     * @return
     */
    List todoList(BaseEntity obj, BaseInfoMapper mapper, Example example, Example.Criteria criteria);
    List todoList(BaseEntity obj, BaseInfoMapper mapper, Example example, Example.Criteria criteria, Integer pageNum, Integer pageSize);
    PageInfo todoTaskList(BaseEntity obj, BaseInfoMapper mapper, Example example, Example.Criteria criteria, Integer pageNum, Integer pageSize);

    /**
     * 获取已办列表
     * @param obj
     * @param mapper
     * @return
     */
    List finishedList(BaseEntity obj, BaseInfoMapper mapper, Example example, Example.Criteria criteria);
    List finishedList(BaseEntity obj, BaseInfoMapper mapper, Example example, Example.Criteria criteria, Integer pageNum, Integer pageSize);

    FlowTaskDto findTaskDto(BaseEntity obj);


    /**
     * 特殊：提交到专家审批
     * @param processInstanceKey
     * @param obj
     * @param mapper
     * @param comment
     */
    void doFlowStepSubmitToExpert(String processInstanceKey, BaseEntity obj, BaseInfoMapper mapper, String comment);

    List endList(BaseEntity obj, BaseInfoMapper mapper, Example example, Example.Criteria criteria);
}
