package com.fd.stdp.service.audit;

import java.util.List;

import com.fd.stdp.beans.audit.InnerAuditWorkContacts;
import com.fd.stdp.beans.audit.vo.InnerAuditProjectInformationVo;
import com.fd.stdp.beans.audit.vo.InnerAuditWorkContactsVo;
import com.github.pagehelper.PageInfo;

/**
 *@Description: 省市场监管局直属单位内审工作联系表
 *@Author: sef
 *@Date: 2022-06-06 13:54:35
 */
public interface InnerAuditWorkContactsService {

	/**
	 *@Description: 保存或更新省市场监管局直属单位内审工作联系表
	 *@param innerAuditWorkContacts 省市场监管局直属单位内审工作联系表对象
	 *@return String 省市场监管局直属单位内审工作联系表ID
	 *@Author: sef
	 */
	String saveOrUpdateInnerAuditWorkContacts(InnerAuditWorkContactsVo innerAuditWorkContacts);
	
	/**
	 *@Description: 删除省市场监管局直属单位内审工作联系表
	 *@param id void 省市场监管局直属单位内审工作联系表ID
	 *@Author: sef
	 */
	void deleteInnerAuditWorkContacts(String id);

	/**
	 * @Description: 批量删除省市场监管局直属单位内审工作联系表
	 * @param ids
	 */
    void deleteMultiInnerAuditWorkContacts(List<String> ids);

	/**
	 *@Description: 查询省市场监管局直属单位内审工作联系表详情
	 *@param id
	 *@return InnerAuditWorkContacts
	 *@Author: sef
	 */
	InnerAuditWorkContacts findById(String id);

	/**
	 *@Description: 分页查询省市场监管局直属单位内审工作联系表
	 *@param innerAuditWorkContactsVo
	 *@return PageInfo<InnerAuditWorkContacts>
	 *@Author: sef
	 */
	PageInfo<InnerAuditWorkContacts> findPageByQuery(InnerAuditWorkContactsVo innerAuditWorkContactsVo);
	
	
	/**
	 * 提交
	 * @param innerAuditWorkContactsVo
	 * @return
	 */
    String submitInnerAuditWorkContacts(InnerAuditWorkContactsVo innerAuditWorkContactsVo);

	/**
	 * 审核
	 * @param innerAuditWorkContactsVo
	 * @return
	 */
	String auditInnerAuditWorkContacts(InnerAuditWorkContactsVo innerAuditWorkContactsVo);

	/**
	 * 退回
	 * @param innerAuditWorkContactsVo
	 * @return
	 */
	String sendBackInnerAuditWorkContacts(InnerAuditWorkContactsVo innerAuditWorkContactsVo);

	/**
	 * 任务书下达
	 * @param innerAuditWorkContactsVo
	 * @return
	 */
	String releaseInnerAuditWorkContacts(InnerAuditWorkContactsVo innerAuditWorkContactsVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnerAuditWorkContacts> todoList(InnerAuditWorkContactsVo innerAuditWorkContactsVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnerAuditWorkContacts> finishedList(InnerAuditWorkContactsVo innerAuditWorkContactsVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnerAuditWorkContacts> endList(InnerAuditWorkContactsVo innerAuditWorkContactsVo);

	void saveBatchInnerAuditWorkContacts(List<InnerAuditWorkContactsVo> innerAuditWorkContactsVoList);

	PageInfo findList(InnerAuditWorkContactsVo vo);

    List exportInnerAuditWorkContactsVo(InnerAuditWorkContactsVo vo);

	List exportInnerAuditWorkContactsVoAllUnit(InnerAuditWorkContactsVo vo);
}
