package com.fd.stdp.service.innovation.impl;

import java.util.ArrayList;
import java.util.List;

import com.fd.stdp.beans.innovation.InnovationLaboratoryAcceptComment;
import com.fd.stdp.beans.innovation.InnovationLaboratoryApplyComment;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryAcceptCommentVo;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryApplyCommentVo;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryContractVo;
import com.fd.stdp.beans.project.ProjectApplyExpertMumber;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.dao.innovation.InnovationLaboratoryApplyCommentMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.basic.BasicPersonLinkedService;
import com.fd.stdp.service.basic.BasicUnitLinkedService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.innovation.InnovationLaboratoryContractService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationLaboratoryApply;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationLaboratoryApplyMapper;
import com.fd.stdp.service.innovation.InnovationLaboratoryApplyService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.fd.stdp.common.BaseController.getCurrentUserId;
import static com.fd.stdp.common.BaseController.getCurrentUserName;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 省局重点实验室培育申报
 *@Author: wangsh
 *@Date: 2022-02-09 16:45:56
 */
public class InnovationLaboratoryApplyServiceImpl extends BaseServiceImpl<InnovationLaboratoryApplyMapper, InnovationLaboratoryApply> implements InnovationLaboratoryApplyService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationLaboratoryApplyServiceImpl.class);
	
	@Autowired
	private InnovationLaboratoryApplyMapper innovationLaboratoryApplyMapper;
	@Autowired
	private InnovationLaboratoryApplyCommentMapper innovationLaboratoryApplyCommentMapper;
	@Autowired
	private InnovationLaboratoryContractService innovationLaboratoryContractService;
	@Autowired
	private BasicPersonLinkedService basicPersonLinkedService;
	@Autowired
	private BasicUnitLinkedService basicUnitLinkedService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	@Autowired
	private FlowCommonService flowCommonService;

	// 人员类型 申报单位
	public static final String UNIT_TYPE_LEADER = "UNIT_TYPE_LEADER";
	// 单位类型 申报单位
	public static final String UNIT_TYPE_APPLY = "UNIT_TYPE_APPLY";
	// 单位类型 推荐单位
	public static final String UNIT_TYPE_RECOMMAND = "UNIT_TYPE_RECOMMAND";
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新省局重点实验室培育申报
	 *@param innovationLaboratoryApply 省局重点实验室培育申报对象
	 *@return String 省局重点实验室培育申报ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationLaboratoryApply(InnovationLaboratoryApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationLaboratoryApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationLaboratoryApplyMapper.updateByPrimaryKeySelective(vo);
		}
/*
		// 人员
		if(vo.getLeader() != null) {
			List<BasicPersonLinkedVo> list = new ArrayList();
			list.add(vo.getLeader());
			basicPersonLinkedService.clearAndUpdateBasicPersonLinked(vo.getId(), list);
		}
		// 申报单位 / 推荐单位
		List list = new ArrayList();
		if(null != vo.getApplyUnit()){
			vo.getApplyUnit().setUnitType("applyUnit");
			list.add(vo.getApplyUnit());
		}
		if(null != vo.getRecommandUnit()){
			vo.getApplyUnit().setUnitType("recommendUnit");
			list.add(vo.getRecommandUnit());
		}
		if(!CollectionUtils.isEmpty(list)){
			basicUnitLinkedService.clearAndUpdateBasicUnitLinked(vo.getId(), list);
		}*/
		if(!CollectionUtils.isEmpty(vo.getFiles())){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}

		// flowCommonService.doFlowStart(FlowableConstant.FLOW_INNOVATION_APPLY, vo, this.mapper, "创新载体申报流程开始");

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除省局重点实验室培育申报
	 *@param id void 省局重点实验室培育申报ID
	 *@Author: wangsh
	 */
	public void deleteInnovationLaboratoryApply(String id) {
		//TODO 做判断后方能执行删除
		InnovationLaboratoryApply innovationLaboratoryApply=innovationLaboratoryApplyMapper.selectByPrimaryKey(id);
		if(innovationLaboratoryApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationLaboratoryApply teminnovationLaboratoryApply=new InnovationLaboratoryApply();
		teminnovationLaboratoryApply.setYn(CommonConstant.FLAG_NO);
		teminnovationLaboratoryApply.setId(innovationLaboratoryApply.getId());
		innovationLaboratoryApplyMapper.updateByPrimaryKeySelective(teminnovationLaboratoryApply);
	}

    /**
     * @Description: 批量删除省局重点实验室培育申报
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationLaboratoryApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationLaboratoryApply(id));
	}

	@Override
	/**
	 *@Description: 查询省局重点实验室培育申报详情
	 *@param id
	 *@return InnovationLaboratoryApply
	 *@Author: wangsh
	 */
	public InnovationLaboratoryApply findById(String id) {
		InnovationLaboratoryApply innovationLaboratoryApply = innovationLaboratoryApplyMapper.selectByPrimaryKey(id);
		InnovationLaboratoryApplyVo vo = new InnovationLaboratoryApplyVo();
		BeanUtils.copyProperties(innovationLaboratoryApply, vo);
		/*vo.setLeader(basicPersonLinkedService.findByFormId(vo.getId()).stream().findAny().get());
		List<BasicUnitLinkedVo> list = basicUnitLinkedService.findByFormId(vo.getId());
		vo.setApplyUnit(list.stream().filter(b -> StringUtils.equals("applyUnit", b.getUnitType())).findAny().get());
		vo.setRecommandUnit(list.stream().filter(b -> StringUtils.equals("recommendUnit", b.getUnitType())).findAny().get());*/
		/**
		 * 附件
		 */
		vo.setFiles(basicFileAppendixService.findByFormId(id));

		/**
		 * 流程
		 */
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));

		/**
		 * 评审
		 */
		Example example = new Example(InnovationLaboratoryAcceptComment.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
		List<InnovationLaboratoryApplyComment> comments = innovationLaboratoryApplyCommentMapper.selectByExample(example);
		List<InnovationLaboratoryApplyComment> commentVos = new ArrayList<>();
		comments.forEach(c->{
			InnovationLaboratoryApplyCommentVo cvo = new InnovationLaboratoryApplyCommentVo();
			BeanUtils.copyProperties(c, cvo);
			cvo.setFiles(basicFileAppendixService.findByFormId(cvo.getId()));
			commentVos.add(cvo);
		});
		vo.setExperts(commentVos);
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询省局重点实验室培育申报
	 *@param innovationLaboratoryApplyVo
	 *@return PageInfo<InnovationLaboratoryApply>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationLaboratoryApply> findPageByQuery(InnovationLaboratoryApplyVo innovationLaboratoryApplyVo) {
		PageHelper.startPage(innovationLaboratoryApplyVo.getPageNum(),innovationLaboratoryApplyVo.getPageSize());
		Example example=new Example(InnovationLaboratoryApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES).andEqualTo("applyType", innovationLaboratoryApplyVo.getApplyType());
		//查询条件
		//if(!StringUtils.isEmpty(innovationLaboratoryApplyVo.getName())){
		//	criteria.andEqualTo(innovationLaboratoryApplyVo.getName());
		//}
		List<InnovationLaboratoryApply> innovationLaboratoryApplyList=innovationLaboratoryApplyMapper.selectByExample(example);
		return new PageInfo<InnovationLaboratoryApply>(innovationLaboratoryApplyList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationLaboratoryApply(InnovationLaboratoryApplyVo innovationLaboratoryApplyVo) {
		String id = this.saveOrUpdateInnovationLaboratoryApply(innovationLaboratoryApplyVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_INNOVATION_CONTRACT, innovationLaboratoryApplyVo, this.mapper,
				StringUtils.isNotBlank(innovationLaboratoryApplyVo.getAuditAdvice())?innovationLaboratoryApplyVo.getAuditAdvice():"提交创新载体申报");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationLaboratoryApply(InnovationLaboratoryApplyVo innovationLaboratoryApplyVo) {
		/* 0331 移除专家流程
		InnovationLaboratoryApplyVo old = (InnovationLaboratoryApplyVo) findById(innovationLaboratoryApplyVo.getId());

		// 省局审核通过 会同时选择专家
		if(StringUtils.equals(old.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())){
			if(CollectionUtils.isEmpty(innovationLaboratoryApplyVo.getExperts())){
				throw new ServiceException("请选择评审专家");
			}
			innovationLaboratoryApplyCommentMapper.clearByFormId(innovationLaboratoryApplyVo.getId());
			innovationLaboratoryApplyVo.getExperts().stream().forEach(c->{
				c.setId(UUIDUtils.getUUID());
				c.setApplyId(innovationLaboratoryApplyVo.getId());
				c.setLaboratory(innovationLaboratoryApplyVo.getLaboratory());
				c.setUserName(c.getName());
				innovationLaboratoryApplyCommentMapper.insertSelective(c);
			});
		}*/
		InnovationLaboratoryApplyVo old = (InnovationLaboratoryApplyVo) findById(innovationLaboratoryApplyVo.getId());
		// 省局审核通过 生成任务书
		if(StringUtils.equals(old.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())){
			InnovationLaboratoryContractVo vo = new InnovationLaboratoryContractVo();
			BeanUtils.copyProperties(old, vo);
			vo.setId(null);
			vo.setApplyId(vo.getId());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			innovationLaboratoryContractService.saveOrUpdateInnovationLaboratoryContract(vo);
		}

		flowCommonService.doFlowStepAudit(innovationLaboratoryApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationLaboratoryApplyVo.getAuditAdvice()) ? innovationLaboratoryApplyVo.getAuditAdvice() : "创新载体申报审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationLaboratoryApplyVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationLaboratoryApply(InnovationLaboratoryApplyVo innovationLaboratoryApplyVo) {
		flowCommonService.doFlowStepSendBack(innovationLaboratoryApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationLaboratoryApplyVo.getAuditAdvice())?innovationLaboratoryApplyVo.getAuditAdvice():"创新载体申报退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String expoertSubmitInnovationLaboratoryApply(InnovationLaboratoryApplyCommentVo vo) {
		String userId = getCurrentUserId();

		Example example = new Example(InnovationLaboratoryApplyComment.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("userId", userId)
				.andEqualTo("applyId", vo.getId());
		List<InnovationLaboratoryApplyComment> comments = innovationLaboratoryApplyCommentMapper.selectByExample(example);
		if(CollectionUtils.isEmpty(comments)){
			throw new ServiceException("未找到评审记录");
		}
		InnovationLaboratoryApplyComment comment = comments.get(0);
		vo.setId(comment.getId());
		vo.setApplyId(comment.getApplyId());

		innovationLaboratoryApplyCommentMapper.updateByPrimaryKeySelective(vo);
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}

		boolean allFinish = true; // 全部评审完成
		boolean isExpertLeader = true; // 是专家组组长

		example = new Example(InnovationLaboratoryApplyComment.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", vo.getApplyId()).andIsNull("opinion");
		if (!CollectionUtils.isEmpty(innovationLaboratoryApplyCommentMapper.selectByExample(example))){
			allFinish = false;
		}

		example = new Example(ProjectApplyExpertMumber.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", vo.getApplyId())
				.andEqualTo("userId", userId).andEqualTo("expertType", 1);
		if(CollectionUtils.isEmpty(innovationLaboratoryApplyCommentMapper.selectByExample(example))){
			isExpertLeader = false;
		}
		// 全部专家评审完毕,专家组长发起至下一级
		if (allFinish && isExpertLeader){
			InnovationLaboratoryApplyVo innovationLaboratoryApplyVo = (InnovationLaboratoryApplyVo) findById(vo.getApplyId());
			flowCommonService.doCompleteTask(innovationLaboratoryApplyVo, this.mapper, "全部专家完成审核", FlowStatusEnum.PROJECT_REVIEW.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		}

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String expoertSubmitIIInnovationLaboratoryApply(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo) {

		InnovationLaboratoryApplyVo innovationLaboratoryApplyVo = new InnovationLaboratoryApplyVo();
		flowCommonService.doCompleteTask(innovationLaboratoryApplyVo, this.mapper
				, "全部专家评审完成"
				, FlowStatusEnum.PROJECT_REVIEW.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE, getCurrentUserName() + "等" + 1 + "人");

		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationLaboratoryApply(InnovationLaboratoryApplyVo innovationLaboratoryApplyVo) {
		if(StringUtils.isBlank(innovationLaboratoryApplyVo.getResult())){
			throw new ServiceException("请选择项目是否通过");
		}

		InnovationLaboratoryApplyVo old = (InnovationLaboratoryApplyVo) findById(innovationLaboratoryApplyVo.getId());
		if(StringUtils.equals("通过", innovationLaboratoryApplyVo.getResult())
				&& StringUtils.equals(old.getFlowStatus(), FlowStatusEnum.PROJECT_REVIEW.getCode())) {
			// 添加任务书
			InnovationLaboratoryContractVo vo = new InnovationLaboratoryContractVo();
			BeanUtils.copyProperties(old, vo);
			vo.setId(null);
			vo.setApplyId(vo.getId());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			innovationLaboratoryContractService.saveOrUpdateInnovationLaboratoryContract(vo);
			flowCommonService.doCompleteTask(innovationLaboratoryApplyVo, this.mapper
					, "创新载体申报完成"
					, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		} else if(StringUtils.equals("不通过", innovationLaboratoryApplyVo.getResult())) {
			flowCommonService.doFlowStepSendBack(innovationLaboratoryApplyVo, this.mapper, "未能通过推荐"
					, false);
		}

		return null;
	}

	@Override
	public PageInfo<InnovationLaboratoryApply> todoList(InnovationLaboratoryApplyVo vo) {

		Example example = new Example(InnovationLaboratoryApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationLaboratoryApply> finishedList(InnovationLaboratoryApplyVo vo) {
		Example example = new Example(InnovationLaboratoryApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationLaboratoryApply> endList(InnovationLaboratoryApplyVo vo) {
		Example example = new Example(InnovationLaboratoryApply.class);
		Criteria criteria = getCriteria(vo, example);
		criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		example.orderBy("createTime").desc();
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}

	private Criteria getCriteria(InnovationLaboratoryApply vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getLaboratory())){
			criteria.andLike("laboratory", "%" + vo.getLaboratory()+ "%");
		}
		if(StringUtils.isNotBlank(vo.getFormType())){
			criteria.andEqualTo("formType", vo.getFormType()).andIsNotNull("formType");
		} else {
			criteria.andIsNull("formType");
		}
		return criteria;
	}
}
