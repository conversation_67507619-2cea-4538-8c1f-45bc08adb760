package com.fd.stdp.service.tech.impl;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.sys.CommonUtilService;
import com.fd.stdp.util.EasyExcelUtils;
import com.fd.stdp.util.ExcelUtils;
import com.fd.stdp.util.ExportUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.tech.TechAchievementSell;
import com.fd.stdp.beans.tech.vo.TechAchievementSellVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.tech.TechAchievementSellMapper;
import com.fd.stdp.service.tech.TechAchievementSellService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.servlet.http.HttpServletResponse;

import static com.fd.stdp.common.BaseController.getCurrentOrgName;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 表名
 *@Author: wangsh
 *@Date: 2022-01-04 13:49:55
 */
public class TechAchievementSellServiceImpl extends BaseServiceImpl<TechAchievementSellMapper, TechAchievementSell> implements TechAchievementSellService{

	public static final Logger logger = LoggerFactory.getLogger(TechAchievementSellServiceImpl.class);
	
	@Autowired
	private TechAchievementSellMapper techAchievementSellMapper;

	@Autowired
	private BasicFileAppendixService basicFileAppendixService;

	@Autowired
	private FlowCommonService flowCommonService;

	@Autowired
	private CommonUtilService commonUtilService;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新表名
	 *@param techAchievementSell 表名对象
	 *@return String 表名ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTechAchievementSell(TechAchievementSellVo techAchievementSell) {
		if(techAchievementSell==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(techAchievementSell.getId())){
			//新增
			// 完善申报单位信息
			if(techAchievementSell.getSellUnitName() == null) {
				BasicScienceOrg basicScienceOrg = commonUtilService.getLoginBasicScienceOrg();
				if (basicScienceOrg != null) {
					techAchievementSell.setSellUnitId(basicScienceOrg.getId());
					techAchievementSell.setSellUnitName(basicScienceOrg.getOrgName());
				}
			}
			techAchievementSell.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			techAchievementSell.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			techAchievementSell.setId(UUIDUtils.getUUID());
			techAchievementSellMapper.insertSelective(techAchievementSell);
		}else{
			//避免页面传入修改
			techAchievementSell.setYn(null);
			techAchievementSellMapper.updateByPrimaryKeySelective(techAchievementSell);
		}

		// 附件
		basicFileAppendixService.clearAndUpdateBasicFileAppendix(techAchievementSell.getId(), techAchievementSell.getFiles());
		// flowCommonService.doFlowStart(FlowableConstant.FLOW_TECH_ACHIEVEMENT, techAchievementSell, this.mapper, "开始成果转化流程");
		
		return techAchievementSell.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除表名
	 *@param id void 表名ID
	 *@Author: wangsh
	 */
	public void deleteTechAchievementSell(String id) {
		//TODO 做判断后方能执行删除
		TechAchievementSell techAchievementSell=techAchievementSellMapper.selectByPrimaryKey(id);
		if(techAchievementSell==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TechAchievementSell temtechAchievementSell=new TechAchievementSell();
		temtechAchievementSell.setYn(CommonConstant.FLAG_NO);
		temtechAchievementSell.setId(techAchievementSell.getId());
		techAchievementSellMapper.updateByPrimaryKeySelective(temtechAchievementSell);
	}

	@Override
	/**
	 *@Description: 查询表名详情
	 *@param id
	 *@return TechAchievementSell
	 *@Author: wangsh
	 */
	public TechAchievementSell findById(String id) {
		TechAchievementSell techAchievementSell = techAchievementSellMapper.selectByPrimaryKey(id);
		TechAchievementSellVo vo = new TechAchievementSellVo();
		BeanUtils.copyProperties(techAchievementSell, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(vo.getId()));
		// 流程
		if(StringUtils.isNotBlank(vo.getFlowStatus())) {
			vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		}
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询表名
	 *@param techAchievementSellVo
	 *@return PageInfo<TechAchievementSell>
	 *@Author: wangsh
	 */
	public PageInfo<TechAchievementSell> findPageByQuery(TechAchievementSellVo techAchievementSellVo) {
		PageHelper.startPage(techAchievementSellVo.getPageNum(),techAchievementSellVo.getPageSize());
		Example example=new Example(TechAchievementSell.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(techAchievementSellVo.getName())){
		//	criteria.andEqualTo(techAchievementSellVo.getName());
		//}
		List<TechAchievementSell> techAchievementSellList=techAchievementSellMapper.selectByExample(example);
		return new PageInfo<TechAchievementSell>(techAchievementSellList);
	}

	@Override
	@Transactional(readOnly = false)
	public String doImport(MultipartFile file) {
		String unitName = getCurrentOrgName();
		// 新增用列表
		List<TechAchievementSellVo> techAchievementSells = new ArrayList<>();
		// 去重用列表
		List<String> names = new ArrayList<>();
		List<List<String>> fileValues = ExcelUtils.praseExcelToList(file);
		fileValues.stream().forEach(values->{
			if(values.size() >= 8) {
				int index = 0;
				TechAchievementSellVo techAchievementSell = new TechAchievementSellVo();
				techAchievementSell.setSellUnitName(unitName);
				techAchievementSell.setSellProjectName(values.get(index++));
				techAchievementSell.setTypeCodeText(values.get(index++));
				techAchievementSell.setLeaderName(values.get(index++));
				techAchievementSell.setTypeSecondCodeText(values.get(index++));
				techAchievementSell.setPrice(values.get(index++));
				techAchievementSell.setTypeThirdCodeText(values.get(index++));
				techAchievementSell.setSellTarget(values.get(index++));
				techAchievementSell.setContent(values.get(index++));
				techAchievementSells.add(techAchievementSell);
			}
		});
		techAchievementSells.stream().forEach(t->this.saveOrUpdateTechAchievementSell(t));
		return "操作成功";
	}

	@Override
	@Transactional(readOnly = false)
	public String submitTechAchievementSell(TechAchievementSellVo techAchievementSellVo) {
		String id = this.saveOrUpdateTechAchievementSell(techAchievementSellVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, techAchievementSellVo, this.mapper,
				StringUtils.isNotBlank(techAchievementSellVo.getAuditAdvice())?techAchievementSellVo.getAuditAdvice():"提交成果转化申请");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditTechAchievementSell(TechAchievementSellVo techAchievementSellVo) {
		String id = techAchievementSellVo.getId();
		flowCommonService.doFlowStepAudit(techAchievementSellVo, this.mapper
				, StringUtils.isNotBlank(techAchievementSellVo.getAuditAdvice())?techAchievementSellVo.getAuditAdvice():"成果转化审核通过"
				, FlowStatusEnum.END.getCode());
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackTechAchievementSell(TechAchievementSellVo techAchievementSellVo) {
		String id = techAchievementSellVo.getId();
		flowCommonService.doFlowStepSendBack(techAchievementSellVo, this.mapper
				, StringUtils.isNotBlank(techAchievementSellVo.getAuditAdvice())?techAchievementSellVo.getAuditAdvice():"成果转化退回"
				,techAchievementSellVo.getToUpper()!=null?techAchievementSellVo.getToUpper():false
		);
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTechAchievementSell(List<String> ids) {
		if(!CollectionUtils.isEmpty(ids)){
			ids.stream().forEach(id -> this.deleteTechAchievementSell(id));
		}
	}

	@Override
	public PageInfo todoList(TechAchievementSellVo techAchievementSellVo) {
		Example example = new Example(TechAchievementSell.class);
		Criteria criteria = getCriteria(techAchievementSellVo, example);
		return new PageInfo<>(flowCommonService.todoList(techAchievementSellVo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo finishedList(TechAchievementSellVo techAchievementSellVo) {
		Example example = new Example(TechAchievementSell.class);
		Criteria criteria = getCriteria(techAchievementSellVo, example);
		return new PageInfo<>(flowCommonService.finishedList(techAchievementSellVo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo endList(TechAchievementSellVo vo) {
		Example example = new Example(TechAchievementSell.class);
		Criteria criteria = getCriteria(vo, example);
		return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
	}

	private Criteria getCriteria(TechAchievementSellVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getSellUnitName())){
			criteria.andLike("sellUnitName", "%" + vo.getSellUnitName()+ "%");
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getSellProjectName())){
			criteria.andLike("sellProjectName", "%" + vo.getSellProjectName()+ "%");
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getLeaderName())){
			criteria.andLike("leaderName", "%" + vo.getLeaderName()+ "%");
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getSellTarget())){
			criteria.andLike("sellTarget", "%" + vo.getSellTarget()+ "%");
		}
		if(StringUtils.isNotBlank(vo.getTypeCode())){
			criteria.andEqualTo("typeCode", vo.getTypeCode());
		}
		if(StringUtils.isNotBlank(vo.getTypeSecondCode())){
			criteria.andEqualTo("typeSecondCode", vo.getTypeSecondCode());
		}
		if(StringUtils.isNotBlank(vo.getTypeThirdCode())){
			criteria.andEqualTo("typeThirdCode", vo.getTypeThirdCode());
		}
		example.orderBy("createTime").desc();
		return criteria;
	}

	@Override
	public void exportTechAchievement(TechAchievementSellVo vo, HttpServletResponse response) {
		vo.setPageNum(1);
		vo.setPageSize(Integer.MAX_VALUE);
		List exportList = null;
		if(StringUtils.isNotBlank(vo.getExportType())) {
			switch (vo.getExportType()) {
				case "todo":
					exportList = todoList(vo).getList();
					break;
				case "finished":
					exportList = finishedList(vo).getList();
					break;
				case "end":
					//exportList = end(vo).getList();
					//break;
				default:
					exportList = findPageByQuery(vo).getList();
			}
		}
		if(exportList == null){
			exportList = findPageByQuery(vo).getList();
		}

		InputStream inputStream = ExportUtil.getInputStream("成果转化导出.xlsx");
		EasyExcelUtils.exportTemplateFill(null, exportList, "Sheet1", "成果转化.xlsx", response, inputStream, true);
	}
}
