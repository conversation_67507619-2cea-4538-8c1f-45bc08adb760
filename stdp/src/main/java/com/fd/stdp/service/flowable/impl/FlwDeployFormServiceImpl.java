package com.fd.stdp.service.flowable.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.flowable.FlwDeployForm;
import com.fd.stdp.beans.flowable.FlwForm;
import com.fd.stdp.beans.flowable.vo.FlwDeployFormVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.StringUtils;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.flowable.FlwDeployFormMapper;
import com.fd.stdp.service.flowable.FlwDeployFormService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 *@Description: 流程实例关联表单
 *@Author: linqiang
 *@Date: 2021-10-15 08:59:48
 */
@Service
@Transactional(readOnly = true)
public class FlwDeployFormServiceImpl extends BaseServiceImpl<FlwDeployFormMapper, FlwDeployForm> implements FlwDeployFormService{

	public static final Logger logger = LoggerFactory.getLogger(FlwDeployFormServiceImpl.class);
	
	@Autowired
	private FlwDeployFormMapper flwDeployFormMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新流程实例关联表单
	 *@param flwDeployForm 流程实例关联表单对象
	 *@return String 流程实例关联表单ID
	 *@Author: linqiang
	 */
	public String saveOrUpdateFlwDeployForm(FlwDeployForm flwDeployForm) {
		if(flwDeployForm==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(flwDeployForm.getId())){
			//新增
			flwDeployForm.setId(UUIDUtils.getUUID());
			flwDeployFormMapper.insertSelective(flwDeployForm);
		}else{
			//避免页面传入修改
			flwDeployForm.setYn(null);
			flwDeployFormMapper.updateByPrimaryKeySelective(flwDeployForm);
		}
		return flwDeployForm.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除流程实例关联表单
	 *@param id void 流程实例关联表单ID
	 *@Author: linqiang
	 */
	public void deleteFlwDeployForm(String id) {
		//TODO 做判断后方能执行删除
		FlwDeployForm flwDeployForm=flwDeployFormMapper.selectByPrimaryKey(id);
		if(flwDeployForm==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		FlwDeployForm temflwDeployForm=new FlwDeployForm();
		temflwDeployForm.setYn(CommonConstant.FLAG_NO);
		temflwDeployForm.setId(flwDeployForm.getId());
		flwDeployFormMapper.updateByPrimaryKeySelective(temflwDeployForm);
	}

	/**
	 *@Description: 查询流程实例关联表单详情
	 *@param id
	 *@return FlwDeployForm
	 *@Author: linqiang
	 */
    @Override
	public FlwDeployForm findById(String id) {
		return flwDeployFormMapper.selectByPrimaryKey(id);
	}


	/**
	 *@Description: 分页查询流程实例关联表单
	 *@param flwDeployFormVo
	 *@return PageInfo<FlwDeployForm>
	 *@Author: linqiang
	 */
	@Override
	public PageInfo<FlwDeployForm> findPageByQuery(FlwDeployFormVo flwDeployFormVo) {
		PageHelper.startPage(flwDeployFormVo.getPageNum(),flwDeployFormVo.getPageSize());
		Example example=new Example(FlwDeployForm.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if(!StringUtils.isEmpty(flwDeployFormVo.getFormId())) {
    		criteria.andEqualTo("formId", flwDeployFormVo.getFormId());
    	}
    	if(!StringUtils.isEmpty(flwDeployFormVo.getDeployId())) {
    		criteria.andEqualTo("deployId", flwDeployFormVo.getDeployId());
    	}
		List<FlwDeployForm> flwDeployFormList=flwDeployFormMapper.selectByExample(example);
		return new PageInfo<FlwDeployForm>(flwDeployFormList);
	}

	@Override
	public List<FlwDeployForm> findFlwDeployFormList(FlwDeployForm flwDeployForm) {
		Example example =new Example(FlwDeployForm.class);
    	Criteria  criteria = example.createCriteria();
    	if(!StringUtils.isEmpty(flwDeployForm.getFormId())) {
    		criteria.andEqualTo("formId", flwDeployForm.getFormId());
    	}
    	if(!StringUtils.isEmpty(flwDeployForm.getDeployId())) {
    		criteria.andEqualTo("deployId", flwDeployForm.getDeployId());
    	}
    	return flwDeployFormMapper.selectByExample(example);
	}

	/**
	 * 批量删除
	 */
	@Override
	@Transactional(readOnly = false)
	public int deleteFlwDeployFormByIds(String[] ids) {
		if(ids==null||ids.length==0) {
			throw new ServiceException("数据异常");
		}
		int count=0;
		for(String id:ids) {
			count+=flwDeployFormMapper.deleteByPrimaryKey(id);
		}
		return count;
	}

	/**
	 * 根据部属的id查询表单
	 */
	@Override
	public FlwForm findFlwDeployFormByDeployId(String deployId) {
		
		return flwDeployFormMapper.findFlwDeployFormByDeployId(deployId);
	}

	/**
	 * 根据部属的id查询表单
	 */
	@Override
	public List<FlwForm> findFlwDeployFormByDeployIds(List deployIds) {

		return flwDeployFormMapper.findFlwDeployFormByDeployIds(deployIds);
	}
}
