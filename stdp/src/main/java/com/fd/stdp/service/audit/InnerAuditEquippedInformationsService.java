package com.fd.stdp.service.audit;

import java.util.List;

import com.fd.stdp.beans.audit.InnerAuditEquippedInformation;
import com.fd.stdp.beans.audit.vo.InnerAuditEquippedInformationExportVo;
import com.fd.stdp.beans.audit.vo.InnerAuditEquippedInformationVo;
import com.github.pagehelper.PageInfo;

/**
 *@Description: 浙江省内部审计总审计师配备情况表
 *@Author: sef
 *@Date: 2022-06-06 13:56:29
 */
public interface InnerAuditEquippedInformationsService {

	/**
	 *@Description: 保存或更新浙江省内部审计总审计师配备情况表
	 *@param innerAuditEquippedInformation 浙江省内部审计总审计师配备情况表对象
	 *@return String 浙江省内部审计总审计师配备情况表ID
	 *@Author: sef
	 */
	String saveOrUpdateInnerAuditEquippedInformation(InnerAuditEquippedInformationVo innerAuditEquippedInformation);
	
	/**
	 *@Description: 删除浙江省内部审计总审计师配备情况表
	 *@param id void 浙江省内部审计总审计师配备情况表ID
	 *@Author: sef
	 */
	void deleteInnerAuditEquippedInformation(String id);

	/**
	 * @Description: 批量删除浙江省内部审计总审计师配备情况表
	 * @param ids
	 */
    void deleteMultiInnerAuditEquippedInformation(List<String> ids);

	/**
	 *@Description: 查询浙江省内部审计总审计师配备情况表详情
	 *@param id
	 *@return InnerAuditEquippedInformation
	 *@Author: sef
	 */
	InnerAuditEquippedInformation findById(String id);

	/**
	 * @param innerAuditEquippedInformationVo
	 * @return PageInfo<InnerAuditEquippedInformation>
	 * @Description: 分页查询浙江省内部审计总审计师配备情况表
	 * @Author: sef
	 */
	PageInfo<InnerAuditEquippedInformation> findPageByQuery(InnerAuditEquippedInformationVo innerAuditEquippedInformationVo);
	
	
	/**
	 * 提交
	 * @param innerAuditEquippedInformationVo
	 * @return
	 */
    String submitInnerAuditEquippedInformation(InnerAuditEquippedInformationVo innerAuditEquippedInformationVo);

	/**
	 * 审核
	 * @param innerAuditEquippedInformationVo
	 * @return
	 */
	String auditInnerAuditEquippedInformation(InnerAuditEquippedInformationVo innerAuditEquippedInformationVo);

	/**
	 * 退回
	 * @param innerAuditEquippedInformationVo
	 * @return
	 */
	String sendBackInnerAuditEquippedInformation(InnerAuditEquippedInformationVo innerAuditEquippedInformationVo);

	/**
	 * 任务书下达
	 * @param innerAuditEquippedInformationVo
	 * @return
	 */
	String releaseInnerAuditEquippedInformation(InnerAuditEquippedInformationVo innerAuditEquippedInformationVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnerAuditEquippedInformation> todoList(InnerAuditEquippedInformationVo innerAuditEquippedInformationVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnerAuditEquippedInformation> finishedList(InnerAuditEquippedInformationVo innerAuditEquippedInformationVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnerAuditEquippedInformation> endList(InnerAuditEquippedInformationVo innerAuditEquippedInformationVo);

    void saveBatchInnerAuditWorkContacts(List<InnerAuditEquippedInformationVo> contactList);

	PageInfo findList(InnerAuditEquippedInformationVo vo);

	List<InnerAuditEquippedInformationExportVo> exportInnerAuditEquippedInformation(InnerAuditEquippedInformationVo vo);

    List exportInnerAuditEquippedInformationAllUnit(InnerAuditEquippedInformationVo vo);
}
