package com.fd.stdp.service.gen.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.gen.GenTableColumn;
import com.fd.stdp.beans.gen.vo.GenTableColumnVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.gen.GenTableColumnMapper;
import com.fd.stdp.service.gen.GenTableColumnService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 *@Description: 代码生成业务表字段
 *@Author: linqiang
 *@Date: 2021-10-15 09:59:26
 */
@Service
@Transactional(readOnly = true)
public class GenTableColumnServiceImpl extends BaseServiceImpl<GenTableColumnMapper, GenTableColumn> implements GenTableColumnService{

	public static final Logger logger = LoggerFactory.getLogger(GenTableColumnServiceImpl.class);
	
	@Autowired
	private GenTableColumnMapper genTableColumnMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新代码生成业务表字段
	 *@param genTableColumn 代码生成业务表字段对象
	 *@return String 代码生成业务表字段ID
	 *@Author: linqiang
	 */
	public String saveOrUpdateGenTableColumn(GenTableColumn genTableColumn) {
		if(genTableColumn==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(genTableColumn.getId())){
			//新增
			genTableColumn.setId(UUIDUtils.getUUID());
			genTableColumnMapper.insertSelective(genTableColumn);
		}else{
			//避免页面传入修改
			genTableColumn.setYn(null);
			genTableColumnMapper.updateByPrimaryKeySelective(genTableColumn);
		}
		return genTableColumn.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除代码生成业务表字段
	 *@param id void 代码生成业务表字段ID
	 *@Author: linqiang
	 */
	public void deleteGenTableColumn(String id) {
		//TODO 做判断后方能执行删除
		GenTableColumn genTableColumn=genTableColumnMapper.selectByPrimaryKey(id);
		if(genTableColumn==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		GenTableColumn temgenTableColumn=new GenTableColumn();
		temgenTableColumn.setYn(CommonConstant.FLAG_NO);
		temgenTableColumn.setId(genTableColumn.getId());
		genTableColumnMapper.updateByPrimaryKeySelective(temgenTableColumn);
	}

	/**
	 *@Description: 查询代码生成业务表字段详情
	 *@param id
	 *@return GenTableColumn
	 *@Author: linqiang
	 */
    @Override
	public GenTableColumn findById(String id) {
		return genTableColumnMapper.selectByPrimaryKey(id);
	}


	/**
	 *@Description: 分页查询代码生成业务表字段
	 *@param genTableColumnVo
	 *@return PageInfo<GenTableColumn>
	 *@Author: linqiang
	 */
	@Override
	public PageInfo<GenTableColumn> findPageByQuery(GenTableColumnVo genTableColumnVo) {
		PageHelper.startPage(genTableColumnVo.getPageNum(),genTableColumnVo.getPageSize());
		Example example=new Example(GenTableColumn.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(genTableColumnVo.getName())){
		//	criteria.andEqualTo(genTableColumnVo.getName());
		//}
		List<GenTableColumn> genTableColumnList=genTableColumnMapper.selectByExample(example);
		return new PageInfo<GenTableColumn>(genTableColumnList);
	}

	@Override
	public List<GenTableColumn> findGenTableColumnListByTableId(String tableId) {
		Example example=new Example(GenTableColumn.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if(!StringUtils.isEmpty(tableId)){
			criteria.andEqualTo("tableId",tableId);
		}
		List<GenTableColumn> genTableColumnList=genTableColumnMapper.selectByExample(example);
		return genTableColumnList;
	}

}
