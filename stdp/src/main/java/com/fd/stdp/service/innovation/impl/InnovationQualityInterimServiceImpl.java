package com.fd.stdp.service.innovation.impl;

import java.util.List;

import com.fd.stdp.beans.innovation.InnovationQualityChange;
import com.fd.stdp.beans.innovation.vo.InnovationQualityChangeVo;
import com.fd.stdp.dao.innovation.InnovationQualityContractMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityInterim;
import com.fd.stdp.beans.innovation.vo.InnovationQualityInterimVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationQualityInterimMapper;
import com.fd.stdp.service.innovation.InnovationQualityInterimService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 省质检中心中期检查
 *@Author: wangsh
 *@Date: 2022-03-04 09:23:38
 */
public class InnovationQualityInterimServiceImpl extends BaseServiceImpl<InnovationQualityInterimMapper, InnovationQualityInterim> implements InnovationQualityInterimService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationQualityInterimServiceImpl.class);
	
	@Autowired
	private InnovationQualityInterimMapper innovationQualityInterimMapper;
	@Autowired
	private InnovationQualityContractMapper innovationQualityContractMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新省质检中心中期检查
	 *@param innovationQualityInterim 省质检中心中期检查对象
	 *@return String 省质检中心中期检查ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationQualityInterim(InnovationQualityInterimVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if (vo.getContract() != null){
			vo.setApplyUnitName(vo.getContract().getApplyUnitName());
			vo.setApplyCenterName(vo.getContract().getApplyCenterName());
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationQualityInterimMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationQualityInterimMapper.updateByPrimaryKeySelective(vo);
		}
		/**
		 * 附件
		 */
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除省质检中心中期检查
	 *@param id void 省质检中心中期检查ID
	 *@Author: wangsh
	 */
	public void deleteInnovationQualityInterim(String id) {
		//TODO 做判断后方能执行删除
		InnovationQualityInterim innovationQualityInterim=innovationQualityInterimMapper.selectByPrimaryKey(id);
		if(innovationQualityInterim==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationQualityInterim teminnovationQualityInterim=new InnovationQualityInterim();
		teminnovationQualityInterim.setYn(CommonConstant.FLAG_NO);
		teminnovationQualityInterim.setId(innovationQualityInterim.getId());
		innovationQualityInterimMapper.updateByPrimaryKeySelective(teminnovationQualityInterim);
	}

    /**
     * @Description: 批量删除省质检中心中期检查
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationQualityInterim(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationQualityInterim(id));
	}

	@Override
	/**
	 *@Description: 查询省质检中心中期检查详情
	 *@param id
	 *@return InnovationQualityInterim
	 *@Author: wangsh
	 */
	public InnovationQualityInterim findById(String id) {
		InnovationQualityInterim innovationQualityInterim = innovationQualityInterimMapper.selectByPrimaryKey(id);
		InnovationQualityInterimVo vo = new InnovationQualityInterimVo();
		BeanUtils.copyProperties(innovationQualityInterim, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		vo.setContract(innovationQualityContractMapper.selectByPrimaryKey(vo.getContractId()));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询省质检中心中期检查
	 *@param innovationQualityInterimVo
	 *@return PageInfo<InnovationQualityInterim>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationQualityInterim> findPageByQuery(InnovationQualityInterimVo innovationQualityInterimVo) {
		PageHelper.startPage(innovationQualityInterimVo.getPageNum(),innovationQualityInterimVo.getPageSize());
		Example example=new Example(InnovationQualityInterim.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationQualityInterimVo.getName())){
		//	criteria.andEqualTo(innovationQualityInterimVo.getName());
		//}
		List<InnovationQualityInterim> innovationQualityInterimList=innovationQualityInterimMapper.selectByExample(example);
		return new PageInfo<InnovationQualityInterim>(innovationQualityInterimList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationQualityInterim(InnovationQualityInterimVo innovationQualityInterimVo) {
		String id = this.saveOrUpdateInnovationQualityInterim(innovationQualityInterimVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innovationQualityInterimVo, this.mapper,
				StringUtils.isNotBlank(innovationQualityInterimVo.getAuditAdvice())?innovationQualityInterimVo.getAuditAdvice():"提交省质检中心中期检查");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationQualityInterim(InnovationQualityInterimVo innovationQualityInterimVo) {
		flowCommonService.doFlowStepAudit(innovationQualityInterimVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityInterimVo.getAuditAdvice()) ? innovationQualityInterimVo.getAuditAdvice() : "省质检中心中期检查审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationQualityInterimVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationQualityInterim(InnovationQualityInterimVo innovationQualityInterimVo) {
		flowCommonService.doFlowStepSendBack(innovationQualityInterimVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityInterimVo.getAuditAdvice())?innovationQualityInterimVo.getAuditAdvice():"省质检中心中期检查退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationQualityInterim(InnovationQualityInterimVo innovationQualityInterimVo) {
		flowCommonService.doCompleteTask(innovationQualityInterimVo, this.mapper
				, "省质检中心中期检查任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnovationQualityInterim> todoList(InnovationQualityInterimVo vo) {

		Example example = new Example(InnovationQualityInterim.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationQualityInterim> finishedList(InnovationQualityInterimVo vo) {
		Example example = new Example(InnovationQualityInterim.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnovationQualityInterim> endList(InnovationQualityInterimVo vo) {
        Example example = new Example(InnovationQualityInterim.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(InnovationQualityInterim vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getApplyCenterName())){
			criteria.andLike("applyCenterName", "%" + vo.getApplyCenterName()+ "%");
		}
		return criteria;
	}
}
