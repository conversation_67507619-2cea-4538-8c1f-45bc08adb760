package com.fd.stdp.service.project;


import com.fd.stdp.beans.project.ProjectApplyExpertScore;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertScoreVo;

/**
 * 项目申请专家评审意见Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface ProjectApplyExpertScoreService{

	/**
	 * 保存项目申请专家评审意见
	 *
	 * @param vo
	 * @param userName
	 * @param RoleName
	 * @return
	 */
	String save(ProjectApplyExpertScoreVo vo, String userName, String RoleName);
}
