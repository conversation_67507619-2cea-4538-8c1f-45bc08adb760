package com.fd.stdp.service.work.impl;

import java.util.*;

import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.project.ProjectContractApply;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.beans.sys.vo.BasicAreacodeVo;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.beans.work.WorkActivityWeek;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.constant.ProcessConstants;
import com.fd.stdp.dao.basic.BasicManageOrgMapper;
import com.fd.stdp.dao.sys.BasicAreacodeMapper;
import com.fd.stdp.dao.work.WorkActivityWeekMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.user.SysUserUtilService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.work.WorkReport;
import com.fd.stdp.beans.work.vo.WorkReportVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.work.WorkReportMapper;
import com.fd.stdp.service.work.WorkReportService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.fd.stdp.common.BaseController.*;

/**
 * <AUTHOR>
 * @Description: 工作交流Service业务层处理
 * @date 2021-11-04
 */
@Service
@Transactional(readOnly = true)
public class WorkReportServiceImpl extends BaseServiceImpl<WorkReportMapper, WorkReport> implements WorkReportService {

    private static final Logger logger = LoggerFactory.getLogger(WorkReportServiceImpl.class);
    @Autowired
    private WorkReportMapper workReportMapper;

    @Autowired
    private WorkActivityWeekMapper workActivityWeekMapper;

    @Autowired
    private BasicAreacodeMapper basicAreacodeMapper;

    @Autowired
    private BasicFileAppendixService basicFileAppendixService;

    @Autowired
    private FlowCommonService flowCommonService;
    @Autowired
    private FlowApiService flowApiService;
    @Autowired
    private SysUserUtilService sysUserUtilService;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新工作交流
     *@param workReportVo 工作交流对象
     *@return String 工作交流ID
     *@Author: yujianfei
     */
    public String saveOrUpdateWorkReport(WorkReportVo workReportVo) {
        if (workReportVo == null) {
            throw new ServiceException("数据异常");
        }
        WorkActivityWeek workActivityWeek = new WorkActivityWeek();
        if(workReportVo.getId() != null) {
            workActivityWeek.setReportId(workReportVo.getId());
            List<WorkActivityWeek> activityWeeks = workActivityWeekMapper.select(workActivityWeek);
            if(activityWeeks.size() > 0) {
                workActivityWeek.setId(activityWeeks.get(0).getId());
            }
        }
        BeanUtils.copyProperties(workReportVo, workActivityWeek, new String[]{"id"});
        // 行政区划名称AREA_NAME和AREA_CODE
//        if (!CollectionUtils.isEmpty(workReportVo.getAreaCodeList())) {
//            if (workReportVo.getAreaCodeList().size() == 1) {
//                workActivityWeek.setAreaCode(workReportVo.getAreaCodeList().get(0));
//                BasicAreacodeVo province = basicAreacodeMapper.findByAreaCode(workReportVo.getAreaCodeList().get(0));
//                workActivityWeek.setAreaName(province.getAreaName());
//            } else if (workReportVo.getAreaCodeList().size() == 2)  {
//                workActivityWeek.setAreaCode(workReportVo.getAreaCodeList().get(1));
//                BasicAreacodeVo province = basicAreacodeMapper.findByAreaCode(workReportVo.getAreaCodeList().get(0));
//                BasicAreacodeVo city = basicAreacodeMapper.findByAreaCode(workReportVo.getAreaCodeList().get(1));
//                workActivityWeek.setAreaName(city.getAreaName());
//            } else if (workReportVo.getAreaCodeList().size() == 3) {
//                workActivityWeek.setAreaCode(workReportVo.getAreaCodeList().get(2));
//                BasicAreacodeVo city = basicAreacodeMapper.findByAreaCode(workReportVo.getAreaCodeList().get(1));
//                BasicAreacodeVo county = basicAreacodeMapper.findByAreaCode(workReportVo.getAreaCodeList().get(2));
//                workActivityWeek.setAreaName(city.getAreaName());
//            }
//        }
        
        LoginUser loginUser = getLoginUser();
        if(StringUtils.isNotBlank(loginUser.getAreaCode())) {
        	if(loginUser.getAreaCode().equals("330000")) {
        		if(StringUtils.isNotBlank(loginUser.getManageOrgId())) {
        			workActivityWeek.setAreaType("SJ");
        		}
        		else {
        			workActivityWeek.setAreaType("SZ");	
        		}            		
        	}
        	else {
        		workActivityWeek.setAreaType("GD");
        	}
            BasicAreacodeVo city = basicAreacodeMapper.findByAreaCode(loginUser.getAreaCode().substring(0,4) + "00");
            if (city == null) {
                workActivityWeek.setAreaName(null);
            } else {
                workActivityWeek.setAreaName(city.getAreaName());
            }
            workActivityWeek.setAreaCode(loginUser.getAreaCode());
        }


        
        if (StringUtils.isEmpty(workReportVo.getId())) {
            //新增
            workReportVo.setId(UUIDUtils.getUUID());
            workReportVo.setIsOpen(CommonConstant.FLAG_YES);
            workReportMapper.insertSelective(workReportVo);
            workActivityWeek.setId(UUIDUtils.getUUID());
            workActivityWeek.setReportId(workReportVo.getId());
            
            workActivityWeekMapper.insertSelective(workActivityWeek);
        } else {
            //避免页面传入修改
            workReportVo.setYn(null);
            workReportMapper.updateByPrimaryKeySelective(workReportVo);
            workActivityWeekMapper.updateByPrimaryKeySelective(workActivityWeek);
        }

        // 附件
        if(workReportVo.getFiles() != null){
            basicFileAppendixService.clearAndUpdateBasicFileAppendix(workReportVo.getId(), workReportVo.getFiles());
        }
        return workReportVo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除工作交流
     *@param id void 工作交流ID
     *@Author: yujianfei
     */
    public void deleteWorkReport(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            WorkReport workReport = workReportMapper.selectByPrimaryKey(id);
            if (workReport == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            WorkReport temworkReport = new WorkReport();
            temworkReport.setYn(CommonConstant.FLAG_NO);
            temworkReport.setId(workReport.getId());
            workReportMapper.updateByPrimaryKeySelective(temworkReport);

            Example example = new Example(WorkActivityWeek.class);
            Criteria criteria = example.createCriteria();
            criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
            criteria.andEqualTo("reportId", id);
            List<WorkActivityWeek> list = workActivityWeekMapper.selectByExample(example);
            WorkReportVo workReportVo = new WorkReportVo();
            if (!CollectionUtils.isEmpty(list)) {
                WorkActivityWeek workActivityWeek = new WorkActivityWeek();
                workActivityWeek.setYn(CommonConstant.FLAG_NO);
                workActivityWeekMapper.updateByPrimaryKeySelective(workActivityWeek);
            }
        }
    }

    /**
     * @param id
     * @return WorkReport
     * @Description: 查询工作交流详情
     * @Author: yujianfei
     */
    @Override
    public WorkReportVo findById(String id) {
        Example example = new Example(WorkActivityWeek.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        criteria.andEqualTo("reportId", id);
        List<WorkActivityWeek> list = workActivityWeekMapper.selectByExample(example);
        WorkReportVo workReportVo = new WorkReportVo();
        if (!CollectionUtils.isEmpty(list)) {
            BeanUtils.copyProperties(list.get(0), workReportVo);
        }
        WorkReport workReport = workReportMapper.selectByPrimaryKey(id);
        workReportVo.setId(workReport.getId());
        workReportVo.setOrgName(workReport.getOrgName());
        workReportVo.setUserName(workReport.getUserName());
        workReportVo.setUploadDate(workReport.getUploadDate());
        workReportVo.setReportType(workReport.getReportType());
        workReportVo.setReportTitle(workReport.getReportTitle());
        workReportVo.setReportContent(workReport.getReportContent());
        workReportVo.setItmeType(workReport.getItmeType());
        workReportVo.setYearNo(workReport.getYearNo());
        workReportVo.setIsOpen(workReport.getIsOpen());
        workReportVo.setFlowStatus(workReport.getFlowStatus());
        workReportVo.setFlowUser(workReport.getFlowUser());
        workReportVo.setFlowUserName(workReport.getFlowUserName());
        workReportVo.setFiles(basicFileAppendixService.findByFormId(id));
        workReportVo.setFlowTaskDto(flowCommonService.findTaskDto(workReport));
        return workReportVo;
    }


    /**
     * @param workReportVo
     * @return PageInfo<WorkReport>
     * @Description: 分页查询工作交流
     * @Author: yujianfei
     */
    @Override
    public PageInfo<WorkReport> findPageByQuery(WorkReportVo workReportVo) {
        PageHelper.startPage(workReportVo.getPageNum(), workReportVo.getPageSize());
        Example example = new Example(WorkReport.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        // 类别
        if (StringUtils.isNotBlank(workReportVo.getItmeType())) {
        	if("35".equals(workReportVo.getItmeType())) {        		
    			criteria.andCondition(" ITME_TYPE in (3,5) ");
    		}
        	else {
        		criteria.andEqualTo("itmeType", workReportVo.getItmeType());
        	}
        }
        //报告类型
        if (StringUtils.isNotBlank(workReportVo.getReportType())) {
            criteria.andEqualTo("reportType", workReportVo.getReportType());
        }
        //填报人
        if (StringUtils.isNotBlank(workReportVo.getUserName())) {
            criteria.andLike("userName", "%" + workReportVo.getUserName() + "%");
        }
        //标题
        if (StringUtils.isNotBlank(workReportVo.getReportTitle())) {
            criteria.andLike("reportTitle", "%" + workReportVo.getReportTitle() + "%");
        }
        //标题
        if (StringUtils.isNotBlank(workReportVo.getOrgName())) {
            criteria.andLike("orgName", "%" + workReportVo.getOrgName() + "%");
        }
        //上传日期
        if (null != workReportVo.getUploadDate()) {
            criteria.andEqualTo("uploadDate", workReportVo.getUploadDate());
        }
        //是否开放
        if (null != workReportVo.getIsOpen()) {
            criteria.andEqualTo("isOpen", workReportVo.getIsOpen());
        }
        //省局 SJ 省直  SZ 各地动态 GD
        if (StringUtils.isNotBlank(workReportVo.getAreaType())) {
        	criteria.andEqualTo("areaType", workReportVo.getAreaType());
        }

        // 流程状态
        if (StringUtils.isNotBlank(workReportVo.getFlowStatus())) {
            criteria.andEqualTo("flowStatus", workReportVo.getFlowStatus());
        }
        example.setOrderByClause(" UPLOAD_DATE desc");
        
        List<WorkReport> workReportList = workReportMapper.selectByExample(example);
        return new PageInfo<WorkReport>(workReportList);
    }

    @Override
    @Transactional(readOnly = false)
    public String submitWorkReport(WorkReportVo workReportVo) {

        switch (workReportVo.getItmeType()) {
            case "1":
            case "2":
                // 工作交流直接设置end
                workReportVo.setFlowStatus(FlowStatusEnum.END.getCode());
                workReportVo.setFlowUser(FlowStatusEnum.END.getRole());
                this.saveOrUpdateWorkReport(workReportVo);
                return workReportVo.getId();
            default:
        }

        Map map = new HashMap();
        Integer operType = sysUserUtilService.getUserOperType(getLoginUser());
//        if(operType == 2){
//            workReportVo.setFlowStatus(FlowStatusEnum.PROVINCE_AUDIT.getCode());
//            workReportVo.setFlowUser(FlowStatusEnum.PROVINCE_AUDIT.getRole());
//        } else if (operType == 1) {
//            workReportVo.setFlowStatus(FlowStatusEnum.CITY_AUDIT.getCode());
//            workReportVo.setFlowUser(FlowStatusEnum.CITY_AUDIT.getRole());
//        } else {
//            workReportVo.setFlowStatus(FlowStatusEnum.COUNTY_AUDIT.getCode());
//            workReportVo.setFlowUser(FlowStatusEnum.COUNTY_AUDIT.getRole());
//        }

        this.saveOrUpdateWorkReport(workReportVo);

        String taskId = flowApiService.getTaskId(workReportVo.getId());
        if (org.apache.commons.lang3.StringUtils.isBlank(taskId)) {
            // 不存在任务，创建任务
            flowCommonService.doFlowStart(FlowableConstant.FLOW_WORK_REPORT, workReportVo, this.mapper, "流程开启");
            taskId = flowApiService.getTaskId(workReportVo.getId());
        }

        if(operType == 2){
            workReportVo.setFlowStatus(FlowStatusEnum.PROVINCE_AUDIT.getCode());
            workReportVo.setFlowUser(FlowStatusEnum.PROVINCE_AUDIT.getRole());
            map.put("TYPE", 2);
        } else {
            switch (workReportVo.getItmeType()) {
                case "5":
                    workReportVo.setFlowStatus(FlowStatusEnum.CITY_AUDIT.getCode());
                    workReportVo.setFlowUser(FlowStatusEnum.CITY_AUDIT.getRole());
                    map.put("TYPE", 1);
                    map.put(ProcessConstants.PROCESS_INITIATOR, AssigneeConstant.DEPT_CITY_ROLE);
                    break;
                default:
                    workReportVo.setFlowStatus(FlowStatusEnum.PROVINCE_AUDIT.getCode());
                    workReportVo.setFlowUser(FlowStatusEnum.PROVINCE_AUDIT.getRole());
                    map.put("TYPE", 2);
                    map.put(ProcessConstants.PROCESS_INITIATOR, AssigneeConstant.DEPT_PROVINCE_ROLE);
                    break;
            }
        }

        this.mapper.updateByPrimaryKeySelective(workReportVo);

        FlowTaskVo flowTaskVo = new FlowTaskVo();
        flowTaskVo.setTaskId(taskId);
        flowTaskVo.setBusinessKey(workReportVo.getId());
        flowTaskVo.setUserId(getCurrentUserId());
        flowTaskVo.setUserName(getCurrentUserName());
        flowTaskVo.setComment("提交");
        flowTaskVo.setAssignee((String) map.get(ProcessConstants.PROCESS_INITIATOR));
        flowTaskVo.setValues(map);
        flowApiService.completeTask(flowTaskVo);

        return workReportVo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    public String auditWorkReport(WorkReportVo workReportVo) {
        WorkReport workReport = this.mapper.selectByPrimaryKey(workReportVo.getId());
        if(StringUtils.equals(workReport.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())) {
            // 省局设置下一级为结束
            workReportVo.setFlowStatus(FlowStatusEnum.END.getCode());
        } else {
            // 否则设置下一级为省级
            workReportVo.setFlowStatus(FlowStatusEnum.PROVINCE_AUDIT.getCode());
            workReportVo.setFlowUser(FlowStatusEnum.PROVINCE_AUDIT.getRole());
        }
        this.mapper.updateByPrimaryKeySelective(workReportVo);

        String taskId = flowApiService.getTaskId(workReportVo.getId());
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("ISPASS", 1);
        map.put(ProcessConstants.PROCESS_INITIATOR, AssigneeConstant.DEPT_PROVINCE_ROLE);
        FlowTaskVo flowTaskVo = new FlowTaskVo();
        flowTaskVo.setTaskId(taskId);
        flowTaskVo.setBusinessKey(workReportVo.getId());
        flowTaskVo.setUserId(getCurrentUserId());
        flowTaskVo.setUserName(getCurrentUserName());
        flowTaskVo.setComment("审核通过");
        flowTaskVo.setValues(map);
        flowApiService.completeTask(flowTaskVo);
        return workReportVo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    public String sendBackWorkReport(WorkReportVo workReportVo) {
        flowCommonService.doFlowStepSendBack(workReportVo, this.mapper
                , "审核不通过"
                , false
        );
        return workReportVo.getId();
    }

    @Override
    public PageInfo<WorkReport> todoList(WorkReportVo vo) {
        boolean isManage = true; //是否是管理单位
        LoginUser loginUser = getLoginUser();
        if(StringUtils.isBlank(loginUser.getManageOrgId())){
            isManage = false;
        }
        Integer operType = sysUserUtilService.getUserOperType(getLoginUser());
        if(StringUtils.equals("5", vo.getItmeType())) {
            // 总局科普基地待办 走正常待办逻辑
            Example example = new Example(WorkReport.class);
            Criteria criteria = getCriteria(vo, example);
            example.orderBy("createTime").desc();
            return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria, vo.getPageNum(), vo.getPageSize()));
        } else {
            // 其它 市局 或省级机构可以获取待填报  省局走正常待办逻辑
            if ((isManage && operType == 1) || (!isManage && operType == 2)) {
                Example example = new Example(WorkReport.class);
                Criteria criteria = getCriteria(vo, example);
                criteria.andEqualTo("orgName", getCurrentOrgName());
                criteria.andCondition("(flow_status = 1 or flow_status is null)");
                PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
                return new PageInfo<>(this.mapper.selectByExample(example));
            } else if(isManage && operType == 2) {
                Example example = new Example(WorkReport.class);
                Criteria criteria = getCriteria(vo, example);
                example.orderBy("createTime").desc();
                return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria, vo.getPageNum(), vo.getPageSize()));
            }
        }
        return new PageInfo<>(new ArrayList<>());
    }

    @Override
    public PageInfo<WorkReport> finishedList(WorkReportVo vo) {
        Example example = new Example(WorkReport.class);
        Criteria criteria = getCriteria(vo, example);
        example.orderBy("createTime").desc();
        return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria, vo.getPageNum(),vo.getPageSize()));
    }

    @Override
    public PageInfo<WorkReport> endList(WorkReportVo vo) {
        Example example = new Example(WorkReport.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", "999");
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

    private Criteria getCriteria(WorkReportVo workReportVo, Example example) {
        Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        // 类别
        if (StringUtils.isNotBlank(workReportVo.getItmeType())) {
            if("35".equals(workReportVo.getItmeType())) {
                criteria.andCondition(" ITME_TYPE in (3,5) ");
            }
            else {
                criteria.andEqualTo("itmeType", workReportVo.getItmeType());
            }
        }
        //报告类型
        if (StringUtils.isNotBlank(workReportVo.getReportType())) {
            criteria.andEqualTo("reportType", workReportVo.getReportType());
        }
        //填报人
        if (StringUtils.isNotBlank(workReportVo.getUserName())) {
            criteria.andLike("userName", "%" + workReportVo.getUserName() + "%");
        }
        //标题
        if (StringUtils.isNotBlank(workReportVo.getReportTitle())) {
            criteria.andLike("reportTitle", "%" + workReportVo.getReportTitle() + "%");
        }
        //标题
        if (StringUtils.isNotBlank(workReportVo.getOrgName())) {
            criteria.andLike("orgName", "%" + workReportVo.getOrgName() + "%");
        }
        //上传日期
        if (null != workReportVo.getUploadDate()) {
            criteria.andEqualTo("uploadDate", workReportVo.getUploadDate());
        }
        //是否开放
        if (null != workReportVo.getIsOpen()) {
            criteria.andEqualTo("isOpen", workReportVo.getIsOpen());
        }
        //省局 SJ 省直  SZ 各地动态 GD
        if (StringUtils.isNotBlank(workReportVo.getAreaType())) {
            criteria.andEqualTo("areaType", workReportVo.getAreaType());
        }
        // 流程状态
        if (StringUtils.isNotBlank(workReportVo.getFlowStatus())) {
            criteria.andEqualTo("flowStatus", workReportVo.getFlowStatus());
        }
        example.setOrderByClause(" UPLOAD_DATE desc");
        return criteria;
    }

}
