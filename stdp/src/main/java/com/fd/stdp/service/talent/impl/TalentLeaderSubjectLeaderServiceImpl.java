package com.fd.stdp.service.talent.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fd.stdp.beans.basic.BasicGradeLinked;
import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.talent.*;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectContractVo;
import com.fd.stdp.constant.ProcessConstants;
import com.fd.stdp.dao.basic.BasicGradeLinkedMapper;
import com.fd.stdp.dao.talent.*;
import com.fd.stdp.enums.FlowComment;
import com.fd.stdp.service.basic.BasicGradeLinkedService;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.talent.TalentLeaderSubjectContractService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectLeaderVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.service.talent.TalentLeaderSubjectLeaderService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import static com.fd.stdp.common.BaseController.getCurrentUserId;
import static com.fd.stdp.common.BaseController.getCurrentUserName;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 学科带头人申报书
 *@Author: wangsh
 *@Date: 2022-02-14 10:05:30
 */
public class TalentLeaderSubjectLeaderServiceImpl extends BaseServiceImpl<TalentLeaderSubjectLeaderMapper, TalentLeaderSubjectLeader> implements TalentLeaderSubjectLeaderService{

	public static final Logger logger = LoggerFactory.getLogger(TalentLeaderSubjectLeaderServiceImpl.class);

	@Autowired
	private TalentLeaderSubjectContractService talentLeaderSubjectContractService;
	@Autowired
	private TalentLeaderSubjectContractMapper talentLeaderSubjectContractMapper;
	@Autowired
	private TalentLeaderSubjectLeaderMapper talentLeaderSubjectLeaderMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private TalentLeaderInOfficeMapper talentLeaderInOfficeMapper;
	@Autowired
	private TalentLeaderLearningExperienceMapper talentLeaderLearningExperienceMapper;
	@Autowired
	private TalentLeaderMainAwardMapper talentLeaderMainAwardMapper;
	@Autowired
	private TalentLeaderMainFrameMapper talentLeaderMainFrameMapper;
	@Autowired
	private TalentLeaderMainPaperMapper talentLeaderMainPaperMapper;
	@Autowired
	private TalentLeaderMainPatentMapper talentLeaderMainPatentMapper;
	@Autowired
	private TalentLeaderMainProjectMapper talentLeaderMainProjectMapper;
	@Autowired
	private TalentLeaderMainReportMapper talentLeaderMainReportMapper;
	@Autowired
	private TalentLeaderMainStandardMapper talentLeaderMainStandardMapper;
	@Autowired
	private TalentLeaderMainTeamMapper talentLeaderMainTeamMapper;
	@Autowired
	private TalentLeaderWorkExperienceMapper talentLeaderWorkExperienceMapper;
	@Autowired
	private BasicGradeLinkedService basicGradeLinkedService;
	@Autowired
	private BasicGradeLinkedMapper basicGradeLinkedMapper;

	@Autowired
	private FlowApiService flowApiService;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新学科带头人申报书
	 *@param talentLeaderSubjectLeader 学科带头人申报书对象
	 *@return String 学科带头人申报书ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTalentLeaderSubjectLeader(TalentLeaderSubjectLeaderVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isBlank(vo.getPersonId())){
			throw new ServiceException("请选择人员");
		}

		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			talentLeaderSubjectLeaderMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			talentLeaderSubjectLeaderMapper.updateByPrimaryKeySelective(vo);
		}


		/**
		 * 学习经历
		 */
		if(!CollectionUtils.isEmpty(vo.getLearningExperiences())){
			updateList(vo, vo.getLearningExperiences(), talentLeaderLearningExperienceMapper, "setSubjectLeaderId");
		}

		/**
		 * 工作经历
		 */
		if(!CollectionUtils.isEmpty(vo.getWorkExperiences())){
			updateList(vo, vo.getWorkExperiences(), talentLeaderWorkExperienceMapper, "setSubjectLeaderId");
		}

		/**
		 * 国内外技术组织和重要学术期刊任职情况
		 */
		if(!CollectionUtils.isEmpty(vo.getInOffices())){
			updateList(vo, vo.getInOffices(), talentLeaderInOfficeMapper, "setSubjectLeaderId");
		}

		/**
		 * 主要科研项目担任情况
		 */
		if(!CollectionUtils.isEmpty(vo.getMainProjects())){
			updateList(vo, vo.getMainProjects(), talentLeaderMainProjectMapper, "setSubjectLeaderId");
		}

		/**
		 * 领衔国家或省级重点科技创新团队情况
		 */
		if(!CollectionUtils.isEmpty(vo.getMainTeams())){
			updateList(vo, vo.getMainTeams(), talentLeaderMainTeamMapper, "setSubjectLeaderId");
		}

		/**
		 * 领衔国家或省级重点创新载体情况
		 */
		if(!CollectionUtils.isEmpty(vo.getMainFrames())){
			updateList(vo, vo.getMainFrames(), talentLeaderMainFrameMapper, "setSubjectLeaderId");
		}

		/**
		 * 获得主要科学技术奖励情况
		 */
		if(!CollectionUtils.isEmpty(vo.getMainAwards())){
			updateList(vo, vo.getMainAwards(), talentLeaderMainAwardMapper, "setSubjectLeaderId");
		}

		/**
		 * 代表性论文
		 */
		if(!CollectionUtils.isEmpty(vo.getMainPapers())){
			updateList(vo, vo.getMainPapers(), talentLeaderMainPaperMapper, "setSubjectLeaderId");
		}

		/**
		 * 在重要国际学术会议报告情况
		 */
		if(!CollectionUtils.isEmpty(vo.getMainReports())){
			updateList(vo, vo.getMainReports(), talentLeaderMainReportMapper, "setSubjectLeaderId");
		}

		/**
		 * 发明（实用新型）专利授权情况
		 */
		if(!CollectionUtils.isEmpty(vo.getMainPatents())){
			updateList(vo, vo.getMainPatents(), talentLeaderMainPatentMapper, "setSubjectLeaderId");
		}

		/**
		 * 标准制定情况
		 */
		if(!CollectionUtils.isEmpty(vo.getMainStandards())){
			updateList(vo, vo.getMainStandards(), talentLeaderMainStandardMapper, "setSubjectLeaderId");
		}

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除学科带头人申报书
	 *@param id void 学科带头人申报书ID
	 *@Author: wangsh
	 */
	public void deleteTalentLeaderSubjectLeader(String id) {
		//TODO 做判断后方能执行删除
		TalentLeaderSubjectLeader talentLeaderSubjectLeader=talentLeaderSubjectLeaderMapper.selectByPrimaryKey(id);
		if(talentLeaderSubjectLeader==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TalentLeaderSubjectLeader temtalentLeaderSubjectLeader=new TalentLeaderSubjectLeader();
		temtalentLeaderSubjectLeader.setYn(CommonConstant.FLAG_NO);
		temtalentLeaderSubjectLeader.setId(talentLeaderSubjectLeader.getId());
		talentLeaderSubjectLeaderMapper.updateByPrimaryKeySelective(temtalentLeaderSubjectLeader);
	}

    /**
     * @Description: 批量删除学科带头人申报书
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTalentLeaderSubjectLeader(List<String> ids) {
		ids.stream().forEach(id-> this.deleteTalentLeaderSubjectLeader(id));
	}

	@Override
	/**
	 *@Description: 查询学科带头人申报书详情
	 *@param id
	 *@return TalentLeaderSubjectLeader
	 *@Author: wangsh
	 */
	public TalentLeaderSubjectLeader findById(String id) {
		TalentLeaderSubjectLeader talentLeaderSubjectLeader = talentLeaderSubjectLeaderMapper.selectByPrimaryKey(id);
		TalentLeaderSubjectLeaderVo vo = new TalentLeaderSubjectLeaderVo();
		BeanUtils.copyProperties(talentLeaderSubjectLeader, vo);
		Example example = new Example(TalentLeaderInOffice.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("subjectLeaderId", id);

		/**
		 * 学习经历
		 */
		vo.setLearningExperiences(talentLeaderLearningExperienceMapper.selectByExample(example));

		/**
		 * 工作经历
		 */
		vo.setWorkExperiences(talentLeaderWorkExperienceMapper.selectByExample(example));

		/**
		 * 国内外技术组织和重要学术期刊任职情况
		 */
		vo.setInOffices(talentLeaderInOfficeMapper.selectByExample(example));

		/**
		 * 主要科研项目担任情况
		 */
		vo.setMainProjects(talentLeaderMainProjectMapper.selectByExample(example));

		/**
		 * 领衔国家或省级重点科技创新团队情况
		 */
		vo.setMainTeams(talentLeaderMainTeamMapper.selectByExample(example));

		/**
		 * 领衔国家或省级重点创新载体情况
		 */
		vo.setMainFrames(talentLeaderMainFrameMapper.selectByExample(example));

		/**
		 * 获得主要科学技术奖励情况
		 */
		vo.setMainAwards(talentLeaderMainAwardMapper.selectByExample(example));

		/**
		 * 代表性论文
		 */
		vo.setMainPapers(talentLeaderMainPaperMapper.selectByExample(example));

		/**
		 * 在重要国际学术会议报告情况
		 */
		vo.setMainReports(talentLeaderMainReportMapper.selectByExample(example));

		/**
		 * 发明（实用新型）专利授权情况
		 */
		vo.setMainPatents(talentLeaderMainPatentMapper.selectByExample(example));

		/**
		 * 标准制定情况
		 */
		vo.setMainStandards(talentLeaderMainStandardMapper.selectByExample(example));

		/**
		 * 流程对象
		 */
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));

		/**
		 * 专家评价
		 */
		vo.setExperts(basicGradeLinkedService.findByFormId(vo.getId()));

		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询学科带头人申报书
	 *@param talentLeaderSubjectLeaderVo
	 *@return PageInfo<TalentLeaderSubjectLeader>
	 *@Author: wangsh
	 */
	public PageInfo<TalentLeaderSubjectLeader> findPageByQuery(TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo) {
		PageHelper.startPage(talentLeaderSubjectLeaderVo.getPageNum(),talentLeaderSubjectLeaderVo.getPageSize());
		Example example=new Example(TalentLeaderSubjectLeader.class);
		Criteria criteria=getCriteria(talentLeaderSubjectLeaderVo, example);
		criteria.andEqualTo("flowStatus",FlowStatusEnum.END.getCode());
		//查询条件
		//if(!StringUtils.isEmpty(talentLeaderSubjectLeaderVo.getName())){
		//	criteria.andEqualTo(talentLeaderSubjectLeaderVo.getName());
		//}
		List<TalentLeaderSubjectLeader> talentLeaderSubjectLeaderList=talentLeaderSubjectLeaderMapper.selectByExample(example);
		return new PageInfo<TalentLeaderSubjectLeader>(talentLeaderSubjectLeaderList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitTalentLeaderSubjectLeader(TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo) {
		String id = this.saveOrUpdateTalentLeaderSubjectLeader(talentLeaderSubjectLeaderVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_SUBJECR_LEADER_APPLY, talentLeaderSubjectLeaderVo, this.mapper,
				StringUtils.isNotBlank(talentLeaderSubjectLeaderVo.getAuditAdvice())?talentLeaderSubjectLeaderVo.getAuditAdvice():"提交学科带头人申报书");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditTalentLeaderSubjectLeader(TalentLeaderSubjectLeaderVo vo) {
		TalentLeaderSubjectLeader old = findById(vo.getId());
		if(StringUtils.equals(old.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())){
			if(StringUtils.equals("0", vo.getIsPass())){
				// 不合格
				vo.setFlowStatus(FlowStatusEnum.END.getCode());
				vo.setFlowUser(FlowStatusEnum.END.getRole());
				this.mapper.updateByPrimaryKeySelective(vo);

				String taskId = flowApiService.getTaskId(vo.getId());
				Map<String, Object> map = new HashMap<String, Object>();
				FlowTaskVo flowTaskVo = new FlowTaskVo();
				flowTaskVo.setTaskId(taskId);
				flowTaskVo.setBusinessKey(vo.getId());
				flowTaskVo.setUserId(getCurrentUserId());
				flowTaskVo.setUserName(getCurrentUserName());
				flowTaskVo.setComment(StringUtils.isBlank(vo.getAuditAdvice())?"省局审核不合格":vo.getAuditAdvice());
				flowTaskVo.setAssignee(AssigneeConstant.ORG_ADMIN_ROLE);
				flowTaskVo.setValues(map);
				map.put("ISPASS", 2);
				map.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());
				flowApiService.completeTask(flowTaskVo, FlowComment.REJECT.getType());
				return vo.getId();
			}
			if(CollectionUtils.isEmpty(vo.getExperts())){
				throw new ServiceException("请选择专家");
			}
			boolean hasLeader = false;
			for (BasicGradeLinkedVo b:vo.getExperts()
				 ) {
				if("组长".equals(b.getUserType())){
					hasLeader = true;
					break;
				}
			}
			if(!hasLeader){
				throw new ServiceException("请添加专家组长");
			}
			basicGradeLinkedService.clearAndUpdateBasicGradeLinked(vo.getId(), vo.getExperts());
		}

		flowCommonService.doFlowStepAudit(vo, this.mapper
				, StringUtils.isNotBlank(vo.getAuditAdvice()) ? vo.getAuditAdvice() : "学科带头人申报书审核通过"
				, FlowStatusEnum.EXPERTS_GRADE.getCode(), FlowStatusEnum.EXPERTS_GRADE.getRole());
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackTalentLeaderSubjectLeader(TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo) {
		flowCommonService.doFlowStepSendBack(talentLeaderSubjectLeaderVo, this.mapper
				, StringUtils.isNotBlank(talentLeaderSubjectLeaderVo.getAuditAdvice())?talentLeaderSubjectLeaderVo.getAuditAdvice():"学科带头人申报书退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseTalentLeaderSubjectLeader(TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo) {
		if(StringUtils.equals("0", talentLeaderSubjectLeaderVo.getIsPass())){
			// 不合格
		} else {
			// 审核合格添加任务书
			TalentLeaderSubjectLeader old = findById(talentLeaderSubjectLeaderVo.getId());
			TalentLeaderSubjectContractVo contractVo = new TalentLeaderSubjectContractVo();
			BeanUtils.copyProperties(old, contractVo);
			contractVo.setId(null);
			contractVo.setSubjectLeaderId(old.getId());
			contractVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			contractVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			talentLeaderSubjectContractService.saveOrUpdateTalentLeaderSubjectContract(contractVo);
		}
		flowCommonService.doCompleteTask(talentLeaderSubjectLeaderVo, this.mapper
				, "学科带头人申报书任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}

	private void insertIntoContract(String id){
		Example example = new Example(TalentLeaderSubjectContract.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("subjectLeaderId", id);
		if(CollectionUtils.isEmpty(talentLeaderSubjectContractMapper.selectByExample(example))){
			TalentLeaderSubjectLeaderVo talentLeaderSubjectLeaderVo = (TalentLeaderSubjectLeaderVo) findById(id);
			TalentLeaderSubjectContractVo vo = new TalentLeaderSubjectContractVo();
			BeanUtils.copyProperties(talentLeaderSubjectLeaderVo, vo);
			vo.setId(null);
			vo.setSubjectLeaderId(id);
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(AssigneeConstant.ORG_HEAD_ROLE);
			talentLeaderSubjectContractService.saveOrUpdateTalentLeaderSubjectContract(vo);
		}
	}

	@Override
	public PageInfo<TalentLeaderSubjectLeader> todoList(TalentLeaderSubjectLeaderVo vo) {

		Example example = new Example(TalentLeaderSubjectLeader.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TalentLeaderSubjectLeader> finishedList(TalentLeaderSubjectLeaderVo vo) {
		Example example = new Example(TalentLeaderSubjectLeader.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
	@Transactional(readOnly = false)
	public String expertSubmitTalentLeaderSubjectLeader(TalentLeaderSubjectLeaderVo vo) {
		String userId = getCurrentUserId();
		Boolean nextStep = false;
		vo.getExperts().forEach(grade->{
			if(StringUtils.equals(userId, grade.getUserId())){
				basicGradeLinkedMapper.updateByPrimaryKeySelective(grade);
			}
		});
		if(basicGradeLinkedService.formGradeAllFinished(vo.getId())) {
			List<BasicGradeLinkedVo> linkeds = basicGradeLinkedService.findByFormId(vo.getId());
			for (BasicGradeLinkedVo grade:linkeds) {
				if (StringUtils.equals(userId, grade.getUserId())) {
					nextStep = StringUtils.equals("组长", grade.getUserType());
				}
			}
		}
		if(nextStep){
			flowCommonService.doCompleteTask(vo, this.mapper, "全部专家评审完成"
					, FlowStatusEnum.PROJECT_RELEASE.getCode(), FlowStatusEnum.PROJECT_RELEASE.getRole(), getCurrentUserName());
		}
		return null;
	}

	private Criteria getCriteria(TalentLeaderSubjectLeader vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}
		return criteria;
	}
}
