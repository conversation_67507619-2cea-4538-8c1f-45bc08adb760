package com.fd.stdp.service.user;

import java.util.List;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.user.UserLeave;
import com.fd.stdp.beans.user.vo.UserLeaveVo;
/**
 * 员工请假Service接口
 * 
 * <AUTHOR>
 * @date 2021-10-25
 */
public interface UserLeaveService{
	/**
	 *@Description: 保存或更新员工请假
	 *@param userLeave 员工请假对象
	 *@return String 员工请假ID
	 *@Author: linqiang
	 */
	String saveOrUpdateUserLeave(UserLeave userLeave);
	
	/**
	 *@Description: 删除员工请假
	 *@param ids void 员工请假ID
	 *@Author: linqiang
	 */
	void deleteUserLeave(List<String> ids);

	/**
	 *@Description: 查询员工请假详情
	 *@param id
	 *@return UserLeave
	 *@Author: linqiang
	 */
	UserLeave findById(String id);

	/**
	 *@Description: 分页查询员工请假
	 *@param userLeaveVo
	 *@return PageInfo<UserLeave>
	 *@Author: linqiang
	 */
	PageInfo<UserLeave> findPageByQuery(UserLeaveVo userLeaveVo);
}
