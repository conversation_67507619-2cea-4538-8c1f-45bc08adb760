package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityApply;
import com.fd.stdp.beans.innovation.vo.InnovationQualityApplyVo;
/**
 *@Description: 省质检中心申报
 *@Author: wangsh
 *@Date: 2022-02-11 11:18:37
 */
public interface InnovationQualityApplyService {

	/**
	 *@Description: 保存或更新省质检中心申报
	 *@param innovationQualityApply 省质检中心申报对象
	 *@return String 省质检中心申报ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationQualityApply(InnovationQualityApplyVo innovationQualityApply);
	
	/**
	 *@Description: 删除省质检中心申报
	 *@param id void 省质检中心申报ID
	 *@Author: wangsh
	 */
	void deleteInnovationQualityApply(String id);

	/**
	 * @Description: 批量删除省质检中心申报
	 * @param ids
	 */
    void deleteMultiInnovationQualityApply(List<String> ids);

	/**
	 *@Description: 查询省质检中心申报详情
	 *@param id
	 *@return InnovationQualityApply
	 *@Author: wangsh
	 */
	InnovationQualityApply findById(String id);

	/**
	 *@Description: 分页查询省质检中心申报
	 *@param innovationQualityApplyVo
	 *@return PageInfo<InnovationQualityApply>
	 *@Author: wangsh
	 */
	PageInfo<InnovationQualityApply> findPageByQuery(InnovationQualityApplyVo innovationQualityApplyVo);

	/**
	 * 提交
	 * @param innovationQualityApplyVo
	 * @return
	 */
    String submitInnovationQualityApply(InnovationQualityApplyVo innovationQualityApplyVo);

	/**
	 * 审核
	 * @param innovationQualityApplyVo
	 * @return
	 */
	String auditInnovationQualityApply(InnovationQualityApplyVo innovationQualityApplyVo);

	/**
	 * 退回
	 * @param innovationQualityApplyVo
	 * @return
	 */
	String sendBackInnovationQualityApply(InnovationQualityApplyVo innovationQualityApplyVo);

	/**
	 * 选择专家
	 * @param innovationQualityApplyVo
	 * @return
	 */
	String choseExpertInnovationQualityApply(InnovationQualityApplyVo innovationQualityApplyVo);

	/**
	 * 专家评审提交
	 * @param innovationQualityApplyVo
	 * @return
	 */
	String expoertSubmitInnovationQualityApply(InnovationQualityApplyVo innovationQualityApplyVo);

	/**
	 * 任务书下达
	 * @param innovationQualityApplyVo
	 * @return
	 */
	String releaseInnovationQualityApply(InnovationQualityApplyVo innovationQualityApplyVo);

	/**
	 * 待办列表
	 */
	PageInfo<InnovationQualityApply> todoList(InnovationQualityApplyVo innovationQualityApplyVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationQualityApply> finishedList(InnovationQualityApplyVo innovationQualityApplyVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationQualityApply> endList(InnovationQualityApplyVo innovationQualityApplyVo);
}
