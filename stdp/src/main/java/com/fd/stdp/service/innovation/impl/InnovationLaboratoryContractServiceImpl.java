package com.fd.stdp.service.innovation.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.fd.stdp.beans.innovation.InnovationLaboratoryContractCost;
import com.fd.stdp.beans.innovation.InnovationLaboratoryContractFundplan;
import com.fd.stdp.beans.innovation.vo.*;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.dao.innovation.InnovationLaboratoryContractCostMapper;
import com.fd.stdp.dao.innovation.InnovationLaboratoryContractFundplanMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.basic.BasicPersonLinkedService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.innovation.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationLaboratoryContract;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationLaboratoryContractMapper;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 重点实验室培育申报任务书
 *@Author: wangsh
 *@Date: 2022-02-09 16:46:10
 */
public class InnovationLaboratoryContractServiceImpl extends BaseServiceImpl<InnovationLaboratoryContractMapper, InnovationLaboratoryContract> implements InnovationLaboratoryContractService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationLaboratoryContractServiceImpl.class);
	
	@Autowired
	private InnovationLaboratoryContractMapper innovationLaboratoryContractMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicPersonLinkedService basicPersonLinkedService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	@Autowired
	private InnovationLaboratoryContractFundplanMapper innovationLaboratoryContractFundplanMapper;
	@Autowired
	private InnovationLaboratoryContractCostMapper innovationLaboratoryContractCostMapper;


	@Autowired
	private InnovationQualityChangeService changeService;
	@Autowired
	private InnovationQualityInterimService interimService;
	@Autowired
	private InnovationQualityReportService reportService;
	@Autowired
	private InnovationQualityReviewService reviewService;
	@Autowired
	private InnovationLaboratoryAcceptService acceptService;
	@Autowired
	private InnovationLaboratoryApplyService applyService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新重点实验室培育申报任务书
	 *@param innovationLaboratoryContract 重点实验室培育申报任务书对象
	 *@return String 重点实验室培育申报任务书ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationLaboratoryContract(InnovationLaboratoryContractVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationLaboratoryContractMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationLaboratoryContractMapper.updateByPrimaryKeySelective(vo);
		}

		/**
		 * 重点实验室组织结构、人员条件
		 */
		basicPersonLinkedService.clearAndUpdateBasicPersonLinked(vo.getId(), vo.getTeams());

		/**
		 * 经费计划
		 */
//		List<InnovationLaboratoryContractFundplan> fundplanList = new ArrayList<>();
//		if(!CollectionUtils.isEmpty(vo.getFundplanProvinceList())){
//			vo.getFundplanProvinceList().forEach(f->{
//				f.setFundType("prov");
//				fundplanList.add(f);
//			});
//		}
//		if(!CollectionUtils.isEmpty(vo.getFundplanCityList())){
//			vo.getFundplanCityList().forEach(f->{
//				f.setFundType("city");
//				fundplanList.add(f);
//			});
//		}
//		if(!CollectionUtils.isEmpty(vo.getFundplanSelfList())){
//			vo.getFundplanSelfList().forEach(f->{
//				f.setFundType("self");
//				fundplanList.add(f);
//			});
//		}
//		updateList(vo, fundplanList, innovationLaboratoryContractFundplanMapper, "setContractId");
//
		/**
		 * 预算
		 */
		if(!CollectionUtils.isEmpty(vo.getCostList())){
			int sort = 1;
			for (InnovationLaboratoryContractCost cost: vo.getCostList()){
				cost.setSort(sort++);
			}
			updateList(vo, vo.getCostList(), innovationLaboratoryContractCostMapper,"setContractId");
		}
		if(vo.getFiles() != null) {
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}

		// flowCommonService.doFlowStart(FlowableConstant.FLOW_INNOVATION_CONTRACT, vo, this.mapper, "创新载体任务书流程开始");

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除重点实验室培育申报任务书
	 *@param id void 重点实验室培育申报任务书ID
	 *@Author: wangsh
	 */
	public void deleteInnovationLaboratoryContract(String id) {
		//TODO 做判断后方能执行删除
		InnovationLaboratoryContract innovationLaboratoryContract=innovationLaboratoryContractMapper.selectByPrimaryKey(id);
		if(innovationLaboratoryContract==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationLaboratoryContract teminnovationLaboratoryContract=new InnovationLaboratoryContract();
		teminnovationLaboratoryContract.setYn(CommonConstant.FLAG_NO);
		teminnovationLaboratoryContract.setId(innovationLaboratoryContract.getId());
		innovationLaboratoryContractMapper.updateByPrimaryKeySelective(teminnovationLaboratoryContract);
	}

    /**
     * @Description: 批量删除重点实验室培育申报任务书
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationLaboratoryContract(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationLaboratoryContract(id));
	}

	@Override
	/**
	 *@Description: 查询重点实验室培育申报任务书详情
	 *@param id
	 *@return InnovationLaboratoryContract
	 *@Author: wangsh
	 */
	public InnovationLaboratoryContract findById(String id) {
		InnovationLaboratoryContract contract = innovationLaboratoryContractMapper.selectByPrimaryKey(id);
		InnovationLaboratoryContractVo vo = new InnovationLaboratoryContractVo();
		BeanUtils.copyProperties(contract, vo);
		/**
		 * 人员
		 */
		vo.setTeams(basicPersonLinkedService.findByFormId(id));

		/**
		 * 流程
		 */
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		/**
		 * 经费
		 */
//		Example example = new Example(InnovationLaboratoryContractFundplan.class);
//		example.createCriteria().andEqualTo("yn", 1).andEqualTo("contractId", id);
//		List<InnovationLaboratoryContractFundplan> fundplanList = innovationLaboratoryContractFundplanMapper.selectByExample(example);
//
//		vo.setFundplanProvinceList(fundplanList.stream().filter(f->StringUtils.equals(f.getFundType(), "prov")).collect(Collectors.toList()));
//		vo.setFundplanCityList(fundplanList.stream().filter(f->StringUtils.equals(f.getFundType(), "city")).collect(Collectors.toList()));
//		vo.setFundplanSelfList(fundplanList.stream().filter(f->StringUtils.equals(f.getFundType(), "self")).collect(Collectors.toList()));
		/**
		 * 预算
		 */
		Example example = new Example(InnovationLaboratoryContractCost.class);
		example.createCriteria().andEqualTo("yn", 1).andEqualTo("contractId", id);
		example.orderBy("sort");
		vo.setCostList(innovationLaboratoryContractCostMapper.selectByExample(example));

		vo.setFiles(basicFileAppendixService.findByFormId(id));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询重点实验室培育申报任务书
	 *@param innovationLaboratoryContractVo
	 *@return PageInfo<InnovationLaboratoryContract>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationLaboratoryContract> findPageByQuery(InnovationLaboratoryContractVo innovationLaboratoryContractVo) {
		PageHelper.startPage(innovationLaboratoryContractVo.getPageNum(),innovationLaboratoryContractVo.getPageSize());
		Example example=new Example(InnovationLaboratoryContract.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationLaboratoryContractVo.getName())){
		//	criteria.andEqualTo(innovationLaboratoryContractVo.getName());
		//}
		List<InnovationLaboratoryContract> innovationLaboratoryContractList=innovationLaboratoryContractMapper.selectByExample(example);
		return new PageInfo<InnovationLaboratoryContract>(innovationLaboratoryContractList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationLaboratoryContract(InnovationLaboratoryContractVo innovationLaboratoryContractVo) {
		String id = this.saveOrUpdateInnovationLaboratoryContract(innovationLaboratoryContractVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_INNOVATION_CONTRACT, innovationLaboratoryContractVo, this.mapper,
				StringUtils.isNotBlank(innovationLaboratoryContractVo.getAuditAdvice())?innovationLaboratoryContractVo.getAuditAdvice():"提交创新载体任务书申报");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationLaboratoryContract(InnovationLaboratoryContractVo innovationLaboratoryContractVo) {
		InnovationLaboratoryContractVo old = (InnovationLaboratoryContractVo) findById(innovationLaboratoryContractVo.getId());
		if(StringUtils.equals(old.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())) {

			// 添加变更
			InnovationQualityChangeVo changeVo = new InnovationQualityChangeVo();
			BeanUtils.copyProperties(old, changeVo);
			changeVo.setId(null);
			changeVo.setApplyId(old.getApplyId());
			changeVo.setContractId(old.getId());
			changeVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			changeVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			changeVo.setApplyUnit(changeVo.getApplyUnitName());
			changeVo.setApplyType("laboratory_change");
			changeService.saveOrUpdateInnovationQualityChange(changeVo);
			// 中期检查
			changeVo.setId(null);
			changeVo.setApplyType("laboratory_interim");
			changeService.saveOrUpdateInnovationQualityChange(changeVo);
			// 年度总结
			changeVo.setId(null);
			changeVo.setApplyType("laboratory_review");
			changeService.saveOrUpdateInnovationQualityChange(changeVo);
			// 事项报告
			changeVo.setId(null);
			changeVo.setApplyType("laboratory_report");
			changeService.saveOrUpdateInnovationQualityChange(changeVo);
			// 审查
			changeVo.setId(null);
			changeVo.setApplyType("laboratory_grade");
			changeService.saveOrUpdateInnovationQualityChange(changeVo);

/*
			// 添加变更
			InnovationQualityChangeVo changeVo = new InnovationQualityChangeVo();
			BeanUtils.copyProperties(old, changeVo);
			changeVo.setId(null);
			changeVo.setApplyId(old.getApplyId());
			changeVo.setContractId(old.getId());
			changeVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			changeVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			changeVo.setApplyUnit(changeVo.getApplyUnitName());
			changeVo.setApplyType("laboratory");
			changeService.saveOrUpdateInnovationQualityChange(changeVo);

			// 添加中期检测
			InnovationQualityInterimVo interimVo = new InnovationQualityInterimVo();
			BeanUtils.copyProperties(old, interimVo);
			interimVo.setId(null);
			interimVo.setApplyId(old.getApplyId());
			interimVo.setContractId(old.getId());
			interimVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			interimVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			interimVo.setApplyUnit(interimVo.getApplyUnitName());
			changeVo.setApplyType("laboratory");
			interimService.saveOrUpdateInnovationQualityInterim(interimVo);

			// 添加年度总结
			InnovationQualityReviewVo reviewVo = new InnovationQualityReviewVo();
			BeanUtils.copyProperties(old, reviewVo);
			reviewVo.setId(null);
			reviewVo.setApplyId(old.getApplyId());
			reviewVo.setContractId(old.getId());
			reviewVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			reviewVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			reviewVo.setApplyUnit(reviewVo.getApplyUnitName());
			changeVo.setApplyType("laboratory");
			reviewService.saveOrUpdateInnovationQualityReview(reviewVo);

			// 添加事项报告
			InnovationQualityReportVo reportVo = new InnovationQualityReportVo();
			BeanUtils.copyProperties(old, reportVo);
			reportVo.setId(null);
			reportVo.setApplyId(old.getApplyId());
			reportVo.setContractId(old.getId());
			reportVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			reportVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			reportVo.setApplyUnit(reportVo.getApplyUnitName());
			changeVo.setApplyType("laboratory");
			reportService.saveOrUpdateInnovationQualityReport(reportVo);
*/

			// 添加验收
			InnovationLaboratoryAcceptVo acceptVo = new InnovationLaboratoryAcceptVo();
			BeanUtils.copyProperties(old, acceptVo);
			acceptVo.setId(null);
			acceptVo.setApplyId(old.getApplyId());
			acceptVo.setContractId(old.getId());
			acceptVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			acceptVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			acceptService.saveOrUpdateInnovationLaboratoryAccept(acceptVo);

			// 添加评估
			InnovationLaboratoryApplyVo applyVo = new InnovationLaboratoryApplyVo();
			BeanUtils.copyProperties(old, applyVo);
			applyVo.setId(null);
			applyVo.setContractId(old.getId());
			applyVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			applyVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			applyService.saveOrUpdateInnovationLaboratoryApply(applyVo);
		}

		flowCommonService.doFlowStepAudit(innovationLaboratoryContractVo, this.mapper
				, StringUtils.isNotBlank(innovationLaboratoryContractVo.getAuditAdvice()) ? innovationLaboratoryContractVo.getAuditAdvice() : "创新载体任务书审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationLaboratoryContractVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationLaboratoryContract(InnovationLaboratoryContractVo innovationLaboratoryContractVo) {
		flowCommonService.doFlowStepSendBack(innovationLaboratoryContractVo, this.mapper
				, StringUtils.isNotBlank(innovationLaboratoryContractVo.getAuditAdvice())?innovationLaboratoryContractVo.getAuditAdvice():"创新载体任务书退回"
				, false
		);
		return null;
	}

	@Override
	public PageInfo<InnovationLaboratoryContract> todoList(InnovationLaboratoryContractVo vo) {

		Example example = new Example(InnovationLaboratoryContract.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationLaboratoryContract> finishedList(InnovationLaboratoryContractVo vo) {
		Example example = new Example(InnovationLaboratoryContract.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationLaboratoryContract> endList(InnovationLaboratoryContractVo vo) {
		Example example = new Example(InnovationLaboratoryContract.class);
		Criteria criteria = getCriteria(vo, example);
		criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		example.orderBy("createTime").desc();
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}

	private Criteria getCriteria(InnovationLaboratoryContract vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getLaboratory())){
			criteria.andLike("laboratory", "%" + vo.getLaboratory()+ "%");
		}
		return criteria;
	}
}
