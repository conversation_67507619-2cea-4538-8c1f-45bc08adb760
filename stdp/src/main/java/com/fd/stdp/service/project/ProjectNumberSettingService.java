package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectNumberSetting;
import com.fd.stdp.beans.project.vo.ProjectNumberSettingVo;
/**
 *@Description: 申报编号配置
 *@Author: wangsh
 *@Date: 2022-01-06 10:06:23
 */
public interface ProjectNumberSettingService {

	/**
	 *@Description: 保存或更新申报编号配置
	 *@param projectNumberSetting 申报编号配置对象
	 *@return String 申报编号配置ID
	 *@Author: wangsh
	 */
	String saveOrUpdateProjectNumberSetting(ProjectNumberSetting projectNumberSetting);
	
	/**
	 *@Description: 删除申报编号配置
	 *@param id void 申报编号配置ID
	 *@Author: wangsh
	 */
	void deleteProjectNumberSetting(String id);

	/**
	 *@Description: 查询申报编号配置详情
	 *@param id
	 *@return ProjectNumberSetting
	 *@Author: wangsh
	 */
	ProjectNumberSetting findById(String id);

	/**
	 *@Description: 分页查询申报编号配置
	 *@param projectNumberSettingVo
	 *@return PageInfo<ProjectNumberSetting>
	 *@Author: wangsh
	 */
	PageInfo<ProjectNumberSetting> findPageByQuery(ProjectNumberSettingVo projectNumberSettingVo);

	/**
	 * 批量删除
	 * @param vo
	 */
	void deleteMultiProjectNumberSetting(List<String> ids);

	/**
	 * 创建申报流水号
	 * @param vo
	 * @return
	 */
	String createProjectNumber(ProjectNumberSettingVo vo);
}
