package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectApplyExperts;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertsVo;

/**
 * 项目专家信息Service接口
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
public interface ProjectApplyExpertsService {
    /**
     * @param projectApplyExperts 项目专家信息对象
     * @return String 项目专家信息ID
     * @Description: 保存或更新项目专家信息
     * @Author: yujianfei
     */
    String saveOrUpdateProjectApplyExperts(ProjectApplyExperts projectApplyExperts);

    /**
     * @param ids void 项目专家信息ID
     * @Description: 删除项目专家信息
     * @Author: yujianfei
     */
    void deleteProjectApplyExperts(List<String> ids);

    /**
     * @param id
     * @return ProjectApplyExperts
     * @Description: 查询项目专家信息详情
     * @Author: yujianfei
     */
    ProjectApplyExperts findById(String id);

    /**
     * @param projectApplyExpertsVo
     * @return PageInfo<ProjectApplyExperts>
     * @Description: 分页查询项目专家信息
     * @Author: yujianfei
     */
    PageInfo<ProjectApplyExperts> findPageByQuery(ProjectApplyExpertsVo projectApplyExpertsVo);

    /**
     * 专家提交评审
     * @param projectApplyExpertsVo
     * @return
     */
    String submitProjectApplyExperts(ProjectApplyExpertsVo projectApplyExpertsVo);

    void submitProjectApplyExpertsMulti(List<String> ids, String currentUserId);
}
