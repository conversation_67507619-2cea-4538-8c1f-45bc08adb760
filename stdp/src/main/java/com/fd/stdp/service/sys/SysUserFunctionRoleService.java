package com.fd.stdp.service.sys;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.sys.SysUserFunctionRole;
import com.fd.stdp.beans.sys.vo.SysUserFunctionRoleVo;
/**
 *@Description: 用户角色关联
 *@Author: qyj
 *@Date: 2020-07-12 15:58:19
 */
public interface SysUserFunctionRoleService {

	/**
	 *@Description: 保存或更新用户角色关联
	 *@param sysUserFunctionRole 用户角色关联对象
	 *@return String 用户角色关联ID
	 *@Author: qyj
	 */
	String saveOrUpdateSysUserFunctionRole(SysUserFunctionRole sysUserFunctionRole);
	
	/**
	 *@Description: 删除用户角色关联
	 *@param id void 用户角色关联ID
	 *@Author: qyj
	 */
	void deleteSysUserFunctionRole(String id);

	/**
	 *@Description: 查询用户角色关联详情
	 *@param id
	 *@return SysUserFunctionRole
	 *@Author: qyj
	 */
	SysUserFunctionRole findById(String id);

	/**
	 *@Description: 分页查询用户角色关联
	 *@param sysUserFunctionRoleVo
	 *@return PageInfo<SysUserFunctionRole>
	 *@Author: qyj
	 */
	PageInfo<SysUserFunctionRole> findPageByQuery(SysUserFunctionRoleVo sysUserFunctionRoleVo);
}
