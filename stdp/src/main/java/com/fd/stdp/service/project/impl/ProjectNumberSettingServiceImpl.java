package com.fd.stdp.service.project.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import com.fd.stdp.enums.DateEncodeEnum;
import liquibase.pro.packaged.E;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectNumberSetting;
import com.fd.stdp.beans.project.vo.ProjectNumberSettingVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectNumberSettingMapper;
import com.fd.stdp.service.project.ProjectNumberSettingService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 申报编号配置
 *@Author: wangsh
 *@Date: 2022-01-06 10:06:23
 */
public class ProjectNumberSettingServiceImpl extends BaseServiceImpl<ProjectNumberSettingMapper, ProjectNumberSetting> implements ProjectNumberSettingService{

	public static final Logger logger = LoggerFactory.getLogger(ProjectNumberSettingServiceImpl.class);

	private static final String formatStr = "%s%s%s%s";
	private static final SimpleDateFormat y = new SimpleDateFormat(DateEncodeEnum.YYYY.getValue());
	private static final SimpleDateFormat ym = new SimpleDateFormat(DateEncodeEnum.YYYYMM.getValue());
	private static final SimpleDateFormat ymd = new SimpleDateFormat(DateEncodeEnum.YYYYMMDD.getValue());
	
	@Autowired
	private ProjectNumberSettingMapper projectNumberSettingMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新申报编号配置
	 *@param projectNumberSetting 申报编号配置对象
	 *@return String 申报编号配置ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateProjectNumberSetting(ProjectNumberSetting projectNumberSetting) {
		if(projectNumberSetting==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(projectNumberSetting.getId())){
			ProjectNumberSetting tmp = new ProjectNumberSetting();
			tmp.setTypeCode(projectNumberSetting.getTypeCode());
			tmp.setTypeSecondCode(projectNumberSetting.getTypeSecondCode());
			if(null!=this.mapper.selectOne(tmp)){
				throw new ServiceException("该类别编号规则已存在");
			}

			//新增
			projectNumberSetting.setId(UUIDUtils.getUUID());
			projectNumberSettingMapper.insertSelective(projectNumberSetting);
		}else{
			//避免页面传入修改
			projectNumberSetting.setYn(null);
			projectNumberSettingMapper.updateByPrimaryKeySelective(projectNumberSetting);
		}
		return projectNumberSetting.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除申报编号配置
	 *@param id void 申报编号配置ID
	 *@Author: wangsh
	 */
	public void deleteProjectNumberSetting(String id) {
		//TODO 做判断后方能执行删除
		ProjectNumberSetting projectNumberSetting=projectNumberSettingMapper.selectByPrimaryKey(id);
		if(projectNumberSetting==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		ProjectNumberSetting temprojectNumberSetting=new ProjectNumberSetting();
		temprojectNumberSetting.setYn(CommonConstant.FLAG_NO);
		temprojectNumberSetting.setId(projectNumberSetting.getId());
		projectNumberSettingMapper.updateByPrimaryKeySelective(temprojectNumberSetting);
	}

	@Override
	/**
	 *@Description: 查询申报编号配置详情
	 *@param id
	 *@return ProjectNumberSetting
	 *@Author: wangsh
	 */
	public ProjectNumberSetting findById(String id) {
		return projectNumberSettingMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询申报编号配置
	 *@param projectNumberSettingVo
	 *@return PageInfo<ProjectNumberSetting>
	 *@Author: wangsh
	 */
	public PageInfo<ProjectNumberSetting> findPageByQuery(ProjectNumberSettingVo projectNumberSettingVo) {
		PageHelper.startPage(projectNumberSettingVo.getPageNum(),projectNumberSettingVo.getPageSize());
		Example example=new Example(ProjectNumberSetting.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if(!StringUtils.isEmpty(projectNumberSettingVo.getTypeCode())){
			criteria.andEqualTo("typeCode", projectNumberSettingVo.getTypeCode());
		}
		List<ProjectNumberSetting> projectNumberSettingList=projectNumberSettingMapper.selectByExample(example);
		return new PageInfo<ProjectNumberSetting>(projectNumberSettingList);
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteMultiProjectNumberSetting(List<String> ids) {
		if(!CollectionUtils.isEmpty(ids)){
			ids.stream().forEach(id -> this.deleteProjectNumberSetting(id));
		}
	}

	@Override
	public synchronized String createProjectNumber(ProjectNumberSettingVo vo) {
		Example example = new Example(ProjectNumberSetting.class);
		example.createCriteria()
				.andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("typeCode", vo.getTypeCode())
				.andEqualTo("typeSecondCode", vo.getTypeSecondCode());
		ProjectNumberSetting projectNumberSetting = this.mapper.selectOneByExample(example);
		if(projectNumberSetting == null){
			throw new ServiceException("未配置申报编码");
		}
		Integer lastNo = projectNumberSetting.getLastNo()==null?projectNumberSetting.getLastNo():0;
		SimpleDateFormat sdf = null;
		if(projectNumberSetting.getTimeCode().equals(DateEncodeEnum.YYYY.getValue())){
			sdf = y;
		} else if(projectNumberSetting.getTimeCode().equals(DateEncodeEnum.YYYYMM.getValue())){
			sdf = ym;
		} else if(projectNumberSetting.getTimeCode().equals(DateEncodeEnum.YYYYMMDD.getValue())){
			sdf = ymd;
		}
		projectNumberSetting.setLastNo(lastNo + 1);
		this.saveOrUpdateProjectNumberSetting(projectNumberSetting);

		return String.format(formatStr, projectNumberSetting.getTypeEncode()
				, sdf.format(new Date())
				, projectNumberSetting.getTypeSecondEncode()
				, lastNo);
	}

}
