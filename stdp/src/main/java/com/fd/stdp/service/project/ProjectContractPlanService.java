package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractPlan;
import com.fd.stdp.beans.project.vo.ProjectContractPlanVo;
/**
 *@Description: 项目分年度(阶段)进度安排
 *@Author: wangsh
 *@Date: 2022-01-12 14:18:41
 */
public interface ProjectContractPlanService {

	/**
	 *@Description: 保存或更新项目分年度(阶段)进度安排
	 *@param projectContractPlan 项目分年度(阶段)进度安排对象
	 *@return String 项目分年度(阶段)进度安排ID
	 *@Author: wangsh
	 */
	String saveOrUpdateProjectContractPlan(ProjectContractPlan projectContractPlan);
	
	/**
	 *@Description: 删除项目分年度(阶段)进度安排
	 *@param id void 项目分年度(阶段)进度安排ID
	 *@Author: wangsh
	 */
	void deleteProjectContractPlan(String id);

	/**
	 * @Description: 批量删除项目分年度(阶段)进度安排
	 * @param projectContractPlanVo
	 */
    void deleteMultiProjectContractPlan(ProjectContractPlanVo projectContractPlanVo);

	/**
	 *@Description: 查询项目分年度(阶段)进度安排详情
	 *@param id
	 *@return ProjectContractPlan
	 *@Author: wangsh
	 */
	ProjectContractPlan findById(String id);

	/**
	 *@Description: 分页查询项目分年度(阶段)进度安排
	 *@param projectContractPlanVo
	 *@return PageInfo<ProjectContractPlan>
	 *@Author: wangsh
	 */
	PageInfo<ProjectContractPlan> findPageByQuery(ProjectContractPlanVo projectContractPlanVo);
}
