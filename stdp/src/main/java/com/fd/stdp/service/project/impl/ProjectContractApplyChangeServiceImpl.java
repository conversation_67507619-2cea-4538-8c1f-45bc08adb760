package com.fd.stdp.service.project.impl;

import java.util.*;

import com.fd.stdp.beans.basic.BasicGradeLinked;
import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.project.*;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertMumberVo;
import com.fd.stdp.beans.talent.vo.TalentTeamApplyVo;
import com.fd.stdp.constant.ProcessConstants;
import com.fd.stdp.dao.basic.BasicGradeLinkedMapper;
import com.fd.stdp.dao.project.*;
import com.fd.stdp.dao.sys.BasicAreacodeMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.basic.BasicGradeLinkedService;
import com.fd.stdp.service.flowable.FlowApiService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.vo.ProjectContractApplyChangeVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.service.project.ProjectContractApplyChangeService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import static com.fd.stdp.common.BaseController.getCurrentUserId;
import static com.fd.stdp.common.BaseController.getCurrentUserName;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 任务书申报表变更
 *@Author: wangsh
 *@Date: 2022-02-15 15:33:35
 */
public class ProjectContractApplyChangeServiceImpl extends BaseServiceImpl<ProjectContractApplyChangeMapper, ProjectContractApplyChange> implements ProjectContractApplyChangeService{

	public static final Logger logger = LoggerFactory.getLogger(ProjectContractApplyChangeServiceImpl.class);

	@Autowired
	private ProjectContractApplyChangeMapper projectContractApplyChangeMapper;
	@Autowired
	private FlowCommonService flowCommonService;

	@Autowired
	private ProjectApplyCostMapper projectApplyCostMapper;
	@Autowired
	private ProjectApplyDevicesMapper projectApplyDevicesMapper;
	@Autowired
	private ProjectApplyProgressMapper applyProgressMapper;
	@Autowired
	private ProjectApplyCooperationUnitMapper projectApplyCooperationUnitMapper;
	@Autowired
	private ProjectApplyTeamsMapper projectApplyTeamsMapper;
	@Autowired
	private ProjectApplyExpertMumberMapper projectApplyExpertMumberMapper;
	@Autowired
	private BasicGradeLinkedService basicGradeLinkedService;
	@Autowired
	private BasicGradeLinkedMapper basicGradeLinkedMapper;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;

	@Autowired
	private FlowApiService flowApiService;
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新任务书申报表变更
	 *@param projectContractApplyChange 任务书申报表变更对象
	 *@return String 任务书申报表变更ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateProjectContractApplyChange(ProjectContractApplyChangeVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			projectContractApplyChangeMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			projectContractApplyChangeMapper.updateByPrimaryKeySelective(vo);
		}

		/**
		 * 预算
		 */
		if(vo.getProjectApplyCost() != null || vo.getProjectAllowanceCost() != null || vo.getProjectSelfCost() != null) {
			List<ProjectApplyCost> costList = new ArrayList<>();
			if (vo.getProjectApplyCost() != null) {
				vo.getProjectApplyCost().setCostType("0");
				costList.add(vo.getProjectApplyCost());
			}
			if (vo.getProjectAllowanceCost() != null) {
				vo.getProjectAllowanceCost().setCostType("1");
				costList.add(vo.getProjectAllowanceCost());
			}
			if (vo.getProjectSelfCost() != null) {
				vo.getProjectSelfCost().setCostType("5");
				costList.add(vo.getProjectSelfCost());
			}
			updateList(vo, costList, projectApplyCostMapper, "setApplyId");
		}

		/**
		 * 设备
		 */
		if(vo.getDevicesList() != null){
			//updateDevice(vo);
			updateList(vo, vo.getDevicesList(), projectApplyDevicesMapper, "setApplyId");
		}
		/**
		 * 进度安排
		 */
		if(vo.getProgressList() != null){
			updateList(vo, vo.getProgressList(), applyProgressMapper, "setApplyId");
		}
		/**
		 * 合作单位
		 */
		if(vo.getUnitList() != null) {
			updateList(vo, vo.getUnitList(), projectApplyCooperationUnitMapper, "setApplyId");
		}
		/**
		 * 项目团队
		 */
		if(vo.getTeamsList() != null) {
			updateList(vo, vo.getTeamsList(), projectApplyTeamsMapper, "setApplyId");
		}
		// 附件
		if(vo.getFiles()!=null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		// 专家组
		if(vo.getGradeExperts() != null){
			updateList(vo, vo.getGradeExperts(), projectApplyExpertMumberMapper, "setApplyId");
			// basicGradeLinkedService.clearAndUpdateBasicGradeLinked(vo.getId(), vo.getGradeExperts());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除任务书申报表变更
	 *@param id void 任务书申报表变更ID
	 *@Author: wangsh
	 */
	public void deleteProjectContractApplyChange(String id) {
		//TODO 做判断后方能执行删除
		ProjectContractApplyChange projectContractApplyChange=projectContractApplyChangeMapper.selectByPrimaryKey(id);
		if(projectContractApplyChange==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		ProjectContractApplyChange temprojectContractApplyChange=new ProjectContractApplyChange();
		temprojectContractApplyChange.setYn(CommonConstant.FLAG_NO);
		temprojectContractApplyChange.setId(projectContractApplyChange.getId());
		projectContractApplyChangeMapper.updateByPrimaryKeySelective(temprojectContractApplyChange);
	}

    /**
     * @Description: 批量删除任务书申报表变更
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiProjectContractApplyChange(List<String> ids) {
		ids.stream().forEach(id-> this.deleteProjectContractApplyChange(id));
	}

	@Override
	/**
	 *@Description: 查询任务书申报表变更详情
	 *@param id
	 *@return ProjectContractApplyChange
	 *@Author: wangsh
	 */
	public ProjectContractApplyChange findById(String id) {
		ProjectContractApplyChange projectContractApplyChange = projectContractApplyChangeMapper.selectByPrimaryKey(id);
		ProjectContractApplyChangeVo vo = new ProjectContractApplyChangeVo();
		BeanUtils.copyProperties(projectContractApplyChange, vo);
		/**
		 * 预算
		 */
		Example example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "0");
		vo.setProjectApplyCost(projectApplyCostMapper.selectOneByExample(example));

		example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "1");
		vo.setProjectAllowanceCost(projectApplyCostMapper.selectOneByExample(example));

		example = new Example(ProjectApplyCost.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id).andEqualTo("costType", "5");
		vo.setProjectSelfCost(projectApplyCostMapper.selectOneByExample(example));

		if(vo.getProjectApplyCost() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("0");
			vo.setProjectApplyCost(p);
		}
		if(vo.getProjectAllowanceCost() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("1");
			vo.setProjectAllowanceCost(p);
		}
		if(vo.getProjectSelfCost() == null){
			ProjectApplyCost p = new ProjectApplyCost();
			p.setCostType("5");
			vo.setProjectSelfCost(p);
		}

		/**
		 * 设备
		 */
		example = new Example(ProjectApplyDevices.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		example.orderBy("sort").asc();
		vo.setDevicesList(projectApplyDevicesMapper.selectByExample(example));
		/**
		 * 进度安排
		 */
		example = new Example(ProjectApplyProgress.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		example.orderBy("sort").asc();
		vo.setProgressList(applyProgressMapper.selectByExample(example));
		/**
		 * 合作单位
		 */
		example = new Example(ProjectApplyCooperationUnit.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		example.orderBy("sort").asc();
		vo.setUnitList(projectApplyCooperationUnitMapper.selectByExample(example));
		/**
		 * 项目团队
		 */
		example = new Example(ProjectApplyTeams.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		example.orderBy("sort").asc();
		vo.setTeamsList(projectApplyTeamsMapper.selectByExample(example));

		/**
		 * 流程
		 */
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		/**
		 * 附件
		 */
		vo.setFiles(basicFileAppendixService.findByFormId(vo.getId()));

		/**
		 * 评审意见
		 */
		// vo.setGradeExperts(basicGradeLinkedService.findByFormId(vo.getId()));
		example = new Example(ProjectApplyExpertMumber.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", id);
		vo.setGradeExperts(projectApplyExpertMumberMapper.selectByExample(example));

		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询任务书申报表变更
	 *@param projectContractApplyChangeVo
	 *@return PageInfo<ProjectContractApplyChange>
	 *@Author: wangsh
	 */
	public PageInfo<ProjectContractApplyChange> findPageByQuery(ProjectContractApplyChangeVo projectContractApplyChangeVo) {
		PageHelper.startPage(projectContractApplyChangeVo.getPageNum(),projectContractApplyChangeVo.getPageSize());
		Example example=new Example(ProjectContractApplyChange.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(projectContractApplyChangeVo.getName())){
		//	criteria.andEqualTo(projectContractApplyChangeVo.getName());
		//}
		List<ProjectContractApplyChange> projectContractApplyChangeList=projectContractApplyChangeMapper.selectByExample(example);
		return new PageInfo<ProjectContractApplyChange>(projectContractApplyChangeList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitProjectContractApplyChange(ProjectContractApplyChangeVo projectContractApplyChangeVo) {
		String id = this.saveOrUpdateProjectContractApplyChange(projectContractApplyChangeVo);
		String changeTypes[] = projectContractApplyChangeVo.getChangeType().split(",");
		boolean techChange = false;
		for (String changeType:changeTypes
			 ) {
			if(StringUtils.equals("技术指标变更", changeType)){
				techChange =  true;
				break;
			}
		}
		if(techChange){
			if(CollectionUtils.isEmpty(projectContractApplyChangeVo.getGradeExperts())){
				throw new ServiceException("请添加论证专家");
			}
			// basicGradeLinkedService.clearAndUpdateBasicGradeLinked(projectContractApplyChangeVo.getId(), projectContractApplyChangeVo.getGradeExperts());
			flowCommonService.doFlowStepSubmitToExpert(FlowableConstant.FLOW_PROJECT_CONTRACT_CHANGE, projectContractApplyChangeVo, this.mapper,
					StringUtils.isNotBlank(projectContractApplyChangeVo.getAuditAdvice()) ? projectContractApplyChangeVo.getAuditAdvice() : "提交任务书申报表变更");
		} else {
			flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_PROJECT_CONTRACT_CHANGE, projectContractApplyChangeVo, this.mapper,
					StringUtils.isNotBlank(projectContractApplyChangeVo.getAuditAdvice()) ? projectContractApplyChangeVo.getAuditAdvice() : "提交任务书申报表变更");
		}
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditProjectContractApplyChange(ProjectContractApplyChangeVo projectContractApplyChangeVo) {
		flowCommonService.doFlowStepAudit(projectContractApplyChangeVo, this.mapper
				, StringUtils.isNotBlank(projectContractApplyChangeVo.getAuditAdvice()) ? projectContractApplyChangeVo.getAuditAdvice() : "任务书申报表变更审核通过"
				, FlowStatusEnum.CHOOSE_EXPERTS.getCode());
		return projectContractApplyChangeVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackProjectContractApplyChange(ProjectContractApplyChangeVo projectContractApplyChangeVo) {
		flowCommonService.doFlowStepSendBack(projectContractApplyChangeVo, this.mapper
				, StringUtils.isNotBlank(projectContractApplyChangeVo.getAuditAdvice())?projectContractApplyChangeVo.getAuditAdvice():"任务书申报表变更退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseProjectContractApplyChange(ProjectContractApplyChangeVo projectContractApplyChangeVo) {
		flowCommonService.doCompleteTask(projectContractApplyChangeVo, this.mapper
				, "任务书申报表变更任务书下达完成"
				, FlowStatusEnum.PROJECT_RELEASE.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<ProjectContractApplyChange> todoList(ProjectContractApplyChangeVo vo) {

		Example example = new Example(ProjectContractApplyChange.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<ProjectContractApplyChange> finishedList(ProjectContractApplyChangeVo vo) {
		Example example = new Example(ProjectContractApplyChange.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
    public PageInfo<ProjectContractApplyChange> endList(ProjectContractApplyChangeVo vo) {
        Example example = new Example(ProjectContractApplyChange.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	@Override
	@Transactional(readOnly = false)
	public String expertSubmitProjectContractApplyChange(ProjectApplyExpertMumberVo vo) {
		String userId = getCurrentUserId();
		Example example = new Example(ProjectApplyExpertMumber.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("userId", userId)
				.andEqualTo("applyId", vo.getId());
		List<ProjectApplyExpertMumber> gradeLinkeds = projectApplyExpertMumberMapper.selectByExample(example);
		if(CollectionUtils.isEmpty(gradeLinkeds)){
			throw new ServiceException("未找到评审记录");
		}
		ProjectApplyExpertMumber p = gradeLinkeds.get(0);
		p.setOpinion(vo.getOpinion());
		projectApplyExpertMumberMapper.updateByPrimaryKeySelective(p);

		// 全部评审完成后 专家组组长提交进入下一步流程
		boolean allFinish = true; // 全部评审完成
		boolean isExpertLeader = true; // 是专家组组长
		example = new Example(ProjectApplyExpertMumber.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", vo.getId())
				.andIsNull("opinion");
		if(!CollectionUtils.isEmpty(projectApplyExpertMumberMapper.selectByExample(example))){
			allFinish = false;
		}
		example = new Example(ProjectApplyExpertMumber.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("applyId", vo.getId())
				.andEqualTo("userId", userId).andEqualTo("expertType", 1);
		if(CollectionUtils.isEmpty(projectApplyExpertMumberMapper.selectByExample(example))){
			isExpertLeader = false;
		}
		if(allFinish && isExpertLeader) {
			try{
				ProjectContractApplyChangeVo obj = (ProjectContractApplyChangeVo) this.findById(vo.getId());
				obj.setFlowStatus(FlowStatusEnum.ORG_AUDIT.getCode());
				obj.setFlowUser(FlowStatusEnum.ORG_AUDIT.getRole());
				this.saveOrUpdateProjectContractApplyChange(obj);

				String taskId = flowApiService.getTaskId(obj.getId());
				Map<String, Object> map = new HashMap<String, Object>();
				FlowTaskVo flowTaskVo = new FlowTaskVo();
				flowTaskVo.setTaskId(taskId);
				flowTaskVo.setBusinessKey(obj.getId());
				flowTaskVo.setUserId(getCurrentUserId());
				flowTaskVo.setUserName(getCurrentUserName());
				flowTaskVo.setComment(vo.getOpinion());
				flowTaskVo.setAssignee(AssigneeConstant.ORG_ADMIN_ROLE);
				flowTaskVo.setValues(map);
				map.put("ISPASS", 1);
				map.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());
				flowApiService.completeTask(flowTaskVo);
			} catch (Exception e) {
				e.printStackTrace();
				throw new ServiceException("流程错误，流程执行异常");
			}
		}
		return vo.getId();
	}

	private Criteria getCriteria(ProjectContractApplyChangeVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getProjectName())){
			criteria.andLike("projectName", "%" + vo.getProjectName()+ "%");
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getProjectTypeCode())){
			criteria.andEqualTo("projectTypeCode", vo.getProjectTypeCode());
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getProjectSecondTypeCode())){
			criteria.andEqualTo("projectSecondTypeCode", vo.getProjectSecondTypeCode());
		}
		if(null != vo.getSubmitDateStart()){
			criteria.andGreaterThanOrEqualTo("submitDate", vo.getSubmitDateStart());
		}
		if(null != vo.getSubmitDateEnd()){
			criteria.andLessThanOrEqualTo("submitDate", new Date(vo.getSubmitDateEnd().getTime() + 24 * 60 * 60 * 1000 - 1));
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getApplyUnitName())){
			criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName()+ "%");
		}
		example.orderBy("createTime").desc();
		return criteria;
	}
}
