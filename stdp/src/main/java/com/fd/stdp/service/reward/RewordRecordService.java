package com.fd.stdp.service.reward;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.reward.RewordRecord;
import com.fd.stdp.beans.reward.vo.RewordRecordVo;
/**
 *@Description: 科技成果登记
 *@Author: wangsh
 *@Date: 2022-07-05 16:58:41
 */
public interface RewordRecordService {

	/**
	 *@Description: 保存或更新科技成果登记
	 *@param rewordRecord 科技成果登记对象
	 *@return String 科技成果登记ID
	 *@Author: wangsh
	 */
	String saveOrUpdateRewordRecord(RewordRecordVo rewordRecord);
	
	/**
	 *@Description: 删除科技成果登记
	 *@param id void 科技成果登记ID
	 *@Author: wangsh
	 */
	void deleteRewordRecord(String id);

	/**
	 * @Description: 批量删除科技成果登记
	 * @param ids
	 */
    void deleteMultiRewordRecord(List<String> ids);

	/**
	 *@Description: 查询科技成果登记详情
	 *@param id
	 *@return RewordRecord
	 *@Author: wangsh
	 */
	RewordRecord findById(String id);

	/**
	 *@Description: 分页查询科技成果登记
	 *@param rewordRecordVo
	 *@return PageInfo<RewordRecord>
	 *@Author: wangsh
	 */
	PageInfo<RewordRecord> findPageByQuery(RewordRecordVo rewordRecordVo);
	
	
	/**
	 * 提交
	 * @param rewordRecordVo
	 * @return
	 */
    String submitRewordRecord(RewordRecordVo rewordRecordVo);

	/**
	 * 审核
	 * @param rewordRecordVo
	 * @return
	 */
	String auditRewordRecord(RewordRecordVo rewordRecordVo);

	/**
	 * 退回
	 * @param rewordRecordVo
	 * @return
	 */
	String sendBackRewordRecord(RewordRecordVo rewordRecordVo);

	/**
	 * 任务书下达
	 * @param rewordRecordVo
	 * @return
	 */
	String releaseRewordRecord(RewordRecordVo rewordRecordVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<RewordRecord> todoList(RewordRecordVo rewordRecordVo);

	/**
	 * 已办列表
	 */
	PageInfo<RewordRecord> finishedList(RewordRecordVo rewordRecordVo);

	/**
	 * 已完成列表
	 */
	PageInfo<RewordRecord> endList(RewordRecordVo rewordRecordVo);
}
