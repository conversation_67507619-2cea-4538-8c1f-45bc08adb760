package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectExtra;
import com.fd.stdp.beans.project.vo.ProjectExtraVo;
/**
 *@Description: 外部项目
 *@Author: wangsh
 *@Date: 2022-03-28 19:32:44
 */
public interface ProjectExtraService {

	/**
	 *@Description: 保存或更新外部项目
	 *@param projectExtra 外部项目对象
	 *@return String 外部项目ID
	 *@Author: wangsh
	 */
	String saveOrUpdateProjectExtra(ProjectExtraVo projectExtra);
	
	/**
	 *@Description: 删除外部项目
	 *@param id void 外部项目ID
	 *@Author: wangsh
	 */
	void deleteProjectExtra(String id);

	/**
	 * @Description: 批量删除外部项目
	 * @param ids
	 */
    void deleteMultiProjectExtra(List<String> ids);

	/**
	 *@Description: 查询外部项目详情
	 *@param id
	 *@return ProjectExtra
	 *@Author: wangsh
	 */
	ProjectExtra findById(String id);

	/**
	 *@Description: 分页查询外部项目
	 *@param projectExtraVo
	 *@return PageInfo<ProjectExtra>
	 *@Author: wangsh
	 */
	PageInfo<ProjectExtra> findPageByQuery(ProjectExtraVo projectExtraVo);
	
	
	/**
	 * 提交
	 * @param projectExtraVo
	 * @return
	 */
    String submitProjectExtra(ProjectExtraVo projectExtraVo);

	/**
	 * 审核
	 * @param projectExtraVo
	 * @return
	 */
	String auditProjectExtra(ProjectExtraVo projectExtraVo);

	/**
	 * 退回
	 * @param projectExtraVo
	 * @return
	 */
	String sendBackProjectExtra(ProjectExtraVo projectExtraVo);

	/**
	 * 任务书下达
	 * @param projectExtraVo
	 * @return
	 */
	String releaseProjectExtra(ProjectExtraVo projectExtraVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<ProjectExtra> todoList(ProjectExtraVo projectExtraVo);

	/**
	 * 已办列表
	 */
	PageInfo<ProjectExtra> finishedList(ProjectExtraVo projectExtraVo);

	/**
	 * 已完成列表
	 */
	PageInfo<ProjectExtra> endList(ProjectExtraVo projectExtraVo);
}
