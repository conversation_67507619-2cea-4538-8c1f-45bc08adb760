package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectDemandCollectionGuid;
import com.fd.stdp.beans.project.vo.ProjectDemandCollectionGuidVo;

/**
 * 需求征集指南Service接口
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
public interface ProjectDemandCollectionGuidService {
    /**
     * @param projectDemandCollectionGuid 需求征集指南对象
     * @return String 需求征集指南ID
     * @Description: 保存或更新需求征集指南
     * @Author: yujianfei
     */
    String saveOrUpdateProjectDemandCollectionGuid(ProjectDemandCollectionGuid projectDemandCollectionGuid);

    /**
     * @param ids void 需求征集指南ID
     * @Description: 删除需求征集指南
     * @Author: yujianfei
     */
    void deleteProjectDemandCollectionGuid(List<String> ids);

    /**
     * @param id
     * @return ProjectDemandCollectionGuid
     * @Description: 查询需求征集指南详情
     * @Author: yujianfei
     */
    ProjectDemandCollectionGuid findById(String id);

    /**
     * @param projectDemandCollectionGuidVo
     * @return PageInfo<ProjectDemandCollectionGuid>
     * @Description: 分页查询需求征集指南
     * @Author: yujianfei
     */
    PageInfo<ProjectDemandCollectionGuid> findPageByQuery(ProjectDemandCollectionGuidVo projectDemandCollectionGuidVo);

    String open(ProjectDemandCollectionGuidVo projectDemandCollectionGuidVo);

    PageInfo<ProjectDemandCollectionGuid> todoList(ProjectDemandCollectionGuidVo projectDemandCollectionGuidVo);

    PageInfo<ProjectDemandCollectionGuid> finishedList(ProjectDemandCollectionGuidVo projectDemandCollectionGuidVo);
}
