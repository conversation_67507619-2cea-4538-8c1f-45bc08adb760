package com.fd.stdp.service.appraisal;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.appraisal.AppraisalFileInfo;
import com.fd.stdp.beans.appraisal.vo.AppraisalFileInfoVo;

/**
 * 评价佐证材料Service接口
 *
 * <AUTHOR>
 * @date 2021-11-18
 */
public interface AppraisalFileInfoService {
    /**
     * @param appraisalFileInfo 评价佐证材料对象
     * @return String 评价佐证材料ID
     * @Description: 保存或更新评价佐证材料
     * @Author: yujianfei
     */
    String saveOrUpdateAppraisalFileInfo(AppraisalFileInfo appraisalFileInfo);

    /**
     * @param ids void 评价佐证材料ID
     * @Description: 删除评价佐证材料
     * @Author: yujianfei
     */
    void deleteAppraisalFileInfo(List<String> ids);

    /**
     * @param id
     * @return AppraisalFileInfo
     * @Description: 查询评价佐证材料详情
     * @Author: yujianfei
     */
    AppraisalFileInfo findById(String id);

    /**
     * @param appraisalFileInfoVo
     * @return PageInfo<AppraisalFileInfo>
     * @Description: 分页查询评价佐证材料
     * @Author: yujianfei
     */
    PageInfo<AppraisalFileInfo> findPageByQuery(AppraisalFileInfoVo appraisalFileInfoVo);
}
