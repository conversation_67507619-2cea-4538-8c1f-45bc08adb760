package com.fd.stdp.service.notice;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.notice.NoticeApply;
import com.fd.stdp.beans.notice.vo.NoticeApplyVo;
/**
 *@Description: 
 *@Author: wangsh
 *@Date: 2022-03-15 09:45:58
 */
public interface NoticeApplyService {

	/**
	 *@Description: 保存或更新
	 *@param noticeApply 对象
	 *@return String ID
	 *@Author: wangsh
	 */
	String saveOrUpdateNoticeApply(NoticeApplyVo noticeApply);
	
	/**
	 *@Description: 删除
	 *@param id void ID
	 *@Author: wangsh
	 */
	void deleteNoticeApply(String id);

	/**
	 * @Description: 批量删除
	 * @param ids
	 */
    void deleteMultiNoticeApply(List<String> ids);

	/**
	 *@Description: 查询详情
	 *@param id
	 *@return NoticeApply
	 *@Author: wangsh
	 */
	NoticeApply findById(String id);

	/**
	 *@Description: 分页查询
	 *@param noticeApplyVo
	 *@return PageInfo<NoticeApply>
	 *@Author: wangsh
	 */
	PageInfo<NoticeApply> findPageByQuery(NoticeApplyVo noticeApplyVo);

	void open(String id);

	void close(String id);
}
