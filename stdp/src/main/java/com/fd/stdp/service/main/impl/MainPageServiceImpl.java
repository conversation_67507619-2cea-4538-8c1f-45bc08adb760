package com.fd.stdp.service.main.impl;

import com.fd.stdp.beans.basic.BasicPerson;
import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.basic.vo.BasicPersonVo;
import com.fd.stdp.beans.basic.vo.BasicScienceOrgVo;
import com.fd.stdp.beans.innovation.*;
import com.fd.stdp.beans.innovation.vo.InnovationGeneralInnovationApplyVo;
import com.fd.stdp.beans.innovation.vo.InnovationQualityChangeVo;
import com.fd.stdp.beans.project.*;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.beans.project.vo.ProjectInfoStatistics;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.beans.tech.TechAwardsApply;
import com.fd.stdp.beans.tech.vo.TechAwardsApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.constant.InnovationConstant;
import com.fd.stdp.dao.basic.BasicPersonMapper;
import com.fd.stdp.dao.basic.BasicScienceOrgMapper;
import com.fd.stdp.dao.innovation.*;
import com.fd.stdp.dao.project.*;
import com.fd.stdp.dao.tech.TechAwardsApplyMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.basic.BasicPersonService;
import com.fd.stdp.service.main.MainPageService;
import com.fd.stdp.service.project.ProjectApplyInfoService;
import com.fd.stdp.service.sys.QueryConditionService;
import com.fd.stdp.service.tech.TechAwardsApplyService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.fd.stdp.common.BaseController.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/4/19 10:00
 */
@Service
@Transactional(readOnly = true)
public class MainPageServiceImpl implements MainPageService {

    @Autowired
    private BasicScienceOrgMapper basicScienceOrgMapper;

    @Autowired
    private InnovationLaboratoryContractMapper innovationLaboratoryContractMapper;
    @Autowired
    private InnovationLaboratoryAcceptMapper innovationLaboratoryAcceptMapper;
    @Autowired
    private InnovationQualityContractMapper innovationQualityContractMapper;
    @Autowired
    private InnovationGeneralLaboratoryApplyMapper innovationGeneralLaboratoryApplyMapper;
    @Autowired
    private InnovationGeneralInnovationApplyMapper innovationGeneralInnovationApplyMapper;
    @Autowired
    private InnovationPrivinceLaboratoryApplyMapper innovationPrivinceLaboratoryApplyMapper;
    @Autowired
    private InnovationPrivinceEngineApplyMapper innovationPrivinceEngineApplyMapper;
    @Autowired
    private InnovationOtherInnovationApplyMapper innovationOtherInnovationApplyMapper;
    @Autowired
    private InnovationNationalCenterApplyMapper innovationNationalCenterApplyMapper;
    @Autowired
    private InnovationProvinceCenterApplyMapper innovationProvinceCenterApplyMapper;
    @Autowired
    private BasicPersonMapper basicPersonMapper;
    @Autowired
    private TechAwardsApplyMapper techAwardsApplyMapper;

    @Autowired
    private ProjectApplyInfoMapper projectApplyInfoMapper;
    @Autowired
    private ProjectContractApplyMapper projectContractApplyMapper;
    @Autowired
    private ProjectContractApplyChangeMapper projectContractApplyChangeMapper;
    @Autowired
    private ProjectContractInterimReportMapper projectContractInterimReportMapper;
    @Autowired
    private ProjectContractAcceptMapper projectContractAcceptMapper;

    @Autowired
    private ProjectApplyInfoService projectApplyInfoService;

    @Autowired
    private BasicPersonService basicPersonService;

    @Autowired
    private TechAwardsApplyService techAwardsApplyService;

    @Autowired
    private QueryConditionService queryConditionService;

    /**
     * 获取当前用户的角色
     * @return
     */
    private List<String> getUserRole(){
        return getUserRoleList().stream().map(role->role.getRoleCode() + "@ROLE").collect(Collectors.toList());
    }

    /**
     * 是否是机构用户
     * @param roleList
     * @return
     */
    private boolean isOrgUser(List<String> roleList){
        return roleList.size() == 1
                && (StringUtils.equals(roleList.get(0), AssigneeConstant.ORG_HEAD_ROLE)
                    || StringUtils.equals(roleList.get(0), AssigneeConstant.ORG_ADMIN_ROLE)
                    || StringUtils.equals(roleList.get(0), AssigneeConstant.TEC_ORG_AGENT));
    }

    /**
     * 是机构负责人用户
     * @param roleList
     * @return
     */
    private boolean isOrgHead(List<String> roleList){
        return roleList.size() == 1
                && StringUtils.equals(roleList.get(0), AssigneeConstant.ORG_HEAD_ROLE);
    }

    @Override
    public Object queryScienceOrg(BasicScienceOrgVo basicScienceOrg) {
        Example example = new Example(BasicScienceOrg.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        if(StringUtils.isNotBlank(basicScienceOrg.getOrgType())) {
            criteria.andEqualTo("orgType", basicScienceOrg.getOrgType());
        }
        if(StringUtils.isNotBlank(basicScienceOrg.getAreaCode())) {
            criteria.andEqualTo("areaCode", basicScienceOrg.getAreaCode());
        }
        if(StringUtils.isNotBlank(basicScienceOrg.getCity())) {
            criteria.andEqualTo("city", basicScienceOrg.getCity());
        }
        if(StringUtils.isNotBlank(basicScienceOrg.getCounty())) {
            criteria.andEqualTo("county", basicScienceOrg.getCounty());
        }
        if(StringUtils.isNotBlank(basicScienceOrg.getOrgName())) {
            criteria.andLike("orgName", "%" + basicScienceOrg.getOrgName() + "%");
        }
//        if(isOrgUser(getUserRole())) {
//            criteria.andEqualTo("id", getCurrentScienceOrgId());
//        }
        queryConditionService.querySelfDataOlny(criteria, "id");
        PageHelper.startPage(basicScienceOrg.getPageNum(), basicScienceOrg.getPageSize());
        return new PageInfo<>(basicScienceOrgMapper.selectByExample(example));
    }

    @Override
    public Object queryInnovation(InnovationGeneralInnovationApplyVo vo) {
        List resList = new ArrayList();
        Example example;
        boolean isOrgUser = isOrgUser(getUserRole());
        boolean isOrgHead = isOrgHead(getUserRole());
        String orgName = getCurrentOrgName();
        String orgId = getCurrentScienceOrgId();
        if(StringUtils.isBlank(vo.getInnovationType()) || StringUtils.equals(vo.getInnovationType(), InnovationConstant.INNOVATION_LABORATORY)) {
            // 省局重点实验室
            example = new Example(InnovationLaboratoryContract.class);
            Example.Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
            if (isOrgHead) {
                criteria.andEqualTo("createUser", getCurrentUserName());
            }
            if(StringUtils.isNotBlank(vo.getInnovationName())) {
                criteria.andLike("laboratory", "%" + vo.getInnovationName() + "%");
            }
            if(StringUtils.isNotBlank(vo.getContributeProgress())) {
                criteria.andEqualTo("yn", CommonConstant.FLAG_NO);
            }
            if(StringUtils.isNotBlank(vo.getLocationArea())) {
                criteria.andEqualTo("yn", CommonConstant.FLAG_NO);
            }
            if(StringUtils.isNotBlank(vo.getApplyUnitName())) {
                criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName() + "%");
            }
//            if(isOrgUser) {
//                criteria.andCondition("(org_code = '" + orgId +"' or org_name = '" + orgName + "')");
//            }
            queryConditionService.querySelfDataOlny(criteria, "orgCode");
            List<InnovationLaboratoryAccept> laboratoryAcceptList = innovationLaboratoryAcceptMapper.selectByExample(example);
            laboratoryAcceptList.forEach(contract -> {
                InnovationGeneralInnovationApplyVo tvo = new InnovationGeneralInnovationApplyVo();
                BeanUtils.copyProperties(contract, tvo);
                tvo.setInnovationName(contract.getLaboratory());
                tvo.setInnovationType(InnovationConstant.INNOVATION_LABORATORY_TEXT);
                tvo.setApplyUnitName(contract.getApplyUnitName());
                resList.add(tvo);
            });
        }
        if(StringUtils.isBlank(vo.getInnovationType())
                || StringUtils.equals(vo.getInnovationType(), InnovationConstant.INNOVATION_QUALITY)
                || StringUtils.equals(vo.getInnovationType(), InnovationConstant.PROVINCE_CENTER)) {
            // 省质检中心
            example = new Example(InnovationQualityContract.class);
            Example.Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
            if (isOrgHead) {
                criteria.andEqualTo("createUser", getCurrentUserName());
            }
            if(StringUtils.isNotBlank(vo.getInnovationName())) {
                criteria.andLike("applyCenterName", "%" + vo.getInnovationName() + "%");
            }
            if(StringUtils.isNotBlank(vo.getContributeProgress())) {
                criteria.andEqualTo("yn", CommonConstant.FLAG_NO);
            }
            if(StringUtils.isNotBlank(vo.getLocationArea())) {
                criteria.andEqualTo("yn", CommonConstant.FLAG_NO);
            }
            if(StringUtils.isNotBlank(vo.getApplyUnitName())) {
                criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName() + "%");
            }
//            if(isOrgUser) {
//                criteria.andCondition("(org_code = '" + orgId +"' or org_name = '" + orgName + "')");
//            }
            queryConditionService.querySelfDataOlny(criteria, "orgCode");
            List<InnovationQualityContract> laboratoryContractList = innovationQualityContractMapper.selectByExample(example);
            laboratoryContractList.forEach(contract -> {
                InnovationGeneralInnovationApplyVo tvo = new InnovationGeneralInnovationApplyVo();
                BeanUtils.copyProperties(contract, tvo);
                tvo.setInnovationName(contract.getApplyCenterName());
                tvo.setInnovationType(InnovationConstant.INNOVATION_QUALITY_TEXT);
                tvo.setApplyUnitName(contract.getApplyUnitName());
                resList.add(tvo);
            });
        }
        if(StringUtils.isBlank(vo.getInnovationType()) || StringUtils.equals(vo.getInnovationType(), InnovationConstant.GENERAL_LABORATORY)) {
            // 总局重点实验室
            example = new Example(InnovationGeneralLaboratoryApply.class);
            Example.Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
            if (isOrgHead) {
                criteria.andEqualTo("createUser", getCurrentUserName());
            }
            if(StringUtils.isNotBlank(vo.getInnovationName())) {
                criteria.andLike("laboratory", "%" + vo.getInnovationName() + "%");
            }
            if(StringUtils.isNotBlank(vo.getContributeProgress())) {
                criteria.andEqualTo("contributeProgress", vo.getContributeProgress());
            }
            if(StringUtils.isNotBlank(vo.getLocationArea())) {
                criteria.andEqualTo("locationArea", vo.getLocationArea());
            }
            if(StringUtils.isNotBlank(vo.getApplyUnitName())) {
                criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName() + "%");
            }
//            if(isOrgUser) {
//                criteria.andCondition("(org_code = '" + orgId +"' or org_name = '" + orgName + "')");
//            }
            queryConditionService.querySelfDataOlny(criteria, "orgCode");
            List<InnovationGeneralLaboratoryApply> laboratoryContractList = innovationGeneralLaboratoryApplyMapper.selectByExample(example);
            laboratoryContractList.forEach(contract -> {
                InnovationGeneralInnovationApplyVo tvo = new InnovationGeneralInnovationApplyVo();
                BeanUtils.copyProperties(contract, tvo);
                tvo.setInnovationName(contract.getLaboratory());
                tvo.setInnovationType(InnovationConstant.GENERAL_LABORATORY_TEXT);
                tvo.setApplyUnitName(contract.getApplyUnitName());
                resList.add(tvo);
            });
        }
        if(StringUtils.isBlank(vo.getInnovationType()) || StringUtils.equals(vo.getInnovationType(), InnovationConstant.GENERAL_INNOVATION)) {
            // 总局技术创新中心
            example = new Example(InnovationGeneralInnovationApply.class);
            Example.Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
            if (isOrgHead) {
                criteria.andEqualTo("createUser", getCurrentUserName());
            }
            if(StringUtils.isNotBlank(vo.getInnovationName())) {
                criteria.andLike("laboratory", "%" + vo.getInnovationName() + "%");
            }
            if(StringUtils.isNotBlank(vo.getContributeProgress())) {
                criteria.andEqualTo("contributeProgress", vo.getContributeProgress());
            }
            if(StringUtils.isNotBlank(vo.getLocationArea())) {
                criteria.andEqualTo("locationArea", vo.getLocationArea());
            }
            if(StringUtils.isNotBlank(vo.getApplyUnitName())) {
                criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName() + "%");
            }
//            if(isOrgUser) {
//                criteria.andCondition("(org_code = '" + orgId +"' or org_name = '" + orgName + "')");
//            }
            queryConditionService.querySelfDataOlny(criteria, "orgCode");
            List<InnovationGeneralInnovationApply> laboratoryContractList = innovationGeneralInnovationApplyMapper.selectByExample(example);
            laboratoryContractList.forEach(contract -> {
                InnovationGeneralInnovationApplyVo tvo = new InnovationGeneralInnovationApplyVo();
                BeanUtils.copyProperties(contract, tvo);
                tvo.setInnovationName(contract.getLaboratory());
                tvo.setInnovationType(InnovationConstant.GENERAL_INNOVATION_TEXT);
                tvo.setApplyUnitName(contract.getApplyUnitName());
                resList.add(tvo);
            });
        }
        if(StringUtils.isBlank(vo.getInnovationType()) || StringUtils.equals(vo.getInnovationType(), InnovationConstant.PROVINCE_LABORATORY)) {
            // 省重点实验室
            example = new Example(InnovationPrivinceLaboratoryApply.class);
            Example.Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
            if (isOrgHead) {
                criteria.andEqualTo("createUser", getCurrentUserName());
            }
            if(StringUtils.isNotBlank(vo.getInnovationName())) {
                criteria.andLike("laboratory", "%" + vo.getInnovationName() + "%");
            }
            if(StringUtils.isNotBlank(vo.getContributeProgress())) {
                criteria.andEqualTo("yn", CommonConstant.FLAG_NO);
            }
            if(StringUtils.isNotBlank(vo.getLocationArea())) {
                criteria.andEqualTo("yn", CommonConstant.FLAG_NO);
            }
            if(StringUtils.isNotBlank(vo.getApplyUnitName())) {
                criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName() + "%");
            }
//            if(isOrgUser) {
//                criteria.andCondition("(org_code = '" + orgId +"' or org_name = '" + orgName + "')");
//            }
            queryConditionService.querySelfDataOlny(criteria, "orgCode");
            List<InnovationPrivinceLaboratoryApply> laboratoryContractList = innovationPrivinceLaboratoryApplyMapper.selectByExample(example);
            laboratoryContractList.forEach(contract -> {
                InnovationGeneralInnovationApplyVo tvo = new InnovationGeneralInnovationApplyVo();
                BeanUtils.copyProperties(contract, tvo);
                tvo.setInnovationName(contract.getLaboratory());
                tvo.setInnovationType(InnovationConstant.PROVINCE_LABORATORY_TEXT);
                tvo.setApplyUnitName(contract.getApplyUnitName());
                resList.add(tvo);
            });
        }
        if(StringUtils.isBlank(vo.getInnovationType()) || StringUtils.equals(vo.getInnovationType(), InnovationConstant.PROVINCE_ENGINE)) {
            // 省级工程研究中心
            example = new Example(InnovationPrivinceEngineApply.class);
            Example.Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
            if (isOrgHead) {
                criteria.andEqualTo("createUser", getCurrentUserName());
            }
            if(isOrgUser) {
                criteria.andCondition("(org_code = '" + orgId +"' or org_name = '" + orgName + "')");
            }
            if(StringUtils.isNotBlank(vo.getInnovationName())) {
                criteria.andLike("centerName", "%" + vo.getInnovationName() + "%");
            }
            if(StringUtils.isNotBlank(vo.getContributeProgress())) {
                criteria.andEqualTo("yn", CommonConstant.FLAG_NO);
            }
            if(StringUtils.isNotBlank(vo.getLocationArea())) {
                criteria.andEqualTo("yn", CommonConstant.FLAG_NO);
            }
            if(StringUtils.isNotBlank(vo.getApplyUnitName())) {
                criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName() + "%");
            }
//            if(isOrgUser) {
//                criteria.andCondition("(org_code = '" + orgId +"' or org_name = '" + orgName + "')");
//            }
            queryConditionService.querySelfDataOlny(criteria, "orgCode");
            List<InnovationPrivinceEngineApply> laboratoryContractList = innovationPrivinceEngineApplyMapper.selectByExample(example);
            laboratoryContractList.forEach(contract -> {
                InnovationGeneralInnovationApplyVo tvo = new InnovationGeneralInnovationApplyVo();
                BeanUtils.copyProperties(contract, tvo);
                tvo.setInnovationName(contract.getCenterName());
                tvo.setInnovationType(InnovationConstant.PROVINCE_ENGINE_TEXT);
                tvo.setApplyUnitName(contract.getApplyUnitName());
                resList.add(tvo);
            });
        }
        if(StringUtils.isBlank(vo.getInnovationType()) || StringUtils.equals(vo.getInnovationType(), InnovationConstant.OTHER_INNOVATION)) {
            // 其它创新载体
            example = new Example(InnovationOtherInnovationApply.class);
            Example.Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
            if (isOrgHead) {
                criteria.andEqualTo("createUser", getCurrentUserName());
            }
            if(StringUtils.isNotBlank(vo.getInnovationName())) {
                criteria.andLike("centerName", "%" + vo.getInnovationName() + "%");
            }
            if(StringUtils.isNotBlank(vo.getContributeProgress())) {
                criteria.andEqualTo("yn", CommonConstant.FLAG_NO);
            }
            if(StringUtils.isNotBlank(vo.getLocationArea())) {
                criteria.andEqualTo("yn", CommonConstant.FLAG_NO);
            }
            if(StringUtils.isNotBlank(vo.getApplyUnitName())) {
                criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName() + "%");
            }
//            if(isOrgUser) {
//                criteria.andCondition("(org_code = '" + orgId +"' or org_name = '" + orgName + "')");
//            }
            queryConditionService.querySelfDataOlny(criteria, "orgCode");
            List<InnovationOtherInnovationApply> laboratoryContractList = innovationOtherInnovationApplyMapper.selectByExample(example);
            laboratoryContractList.forEach(contract -> {
                InnovationGeneralInnovationApplyVo tvo = new InnovationGeneralInnovationApplyVo();
                BeanUtils.copyProperties(contract, tvo);
                tvo.setInnovationName(contract.getCenterName());
                tvo.setInnovationType(InnovationConstant.OTHER_INNOVATION_TEXT);
                tvo.setApplyUnitName(contract.getApplyUnitName());
                resList.add(tvo);
            });
        }

        if(StringUtils.isBlank(vo.getInnovationType()) || StringUtils.equals(vo.getInnovationType(), InnovationConstant.NATIONAL_CENTER)) {
            // 国家中心
            example = new Example(InnovationNationalCenterApply.class);
            Example.Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
            if (isOrgHead) {
                criteria.andEqualTo("createUser", getCurrentUserName());
            }
            if(StringUtils.isNotBlank(vo.getInnovationName())) {
                criteria.andLike("centerName", "%" + vo.getInnovationName() + "%");
            }
            if(StringUtils.isNotBlank(vo.getContributeProgress())) {
                criteria.andEqualTo("contributeProgress", vo.getContributeProgress());
            }
            if(StringUtils.isNotBlank(vo.getLocationArea())) {
                criteria.andEqualTo("locationArea", vo.getLocationArea());
            }
            if(StringUtils.isNotBlank(vo.getApplyUnitName())) {
                criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName() + "%");
            }
//            if(isOrgUser) {
//                criteria.andCondition("(org_code = '" + orgId +"' or org_name = '" + orgName + "')");
//            }
            queryConditionService.querySelfDataOlny(criteria, "orgCode");
            List<InnovationNationalCenterApply> laboratoryContractList = innovationNationalCenterApplyMapper.selectByExample(example);
            laboratoryContractList.forEach(contract -> {
                InnovationGeneralInnovationApplyVo tvo = new InnovationGeneralInnovationApplyVo();
                BeanUtils.copyProperties(contract, tvo);
                tvo.setInnovationName(contract.getCenterName());
                tvo.setInnovationType(InnovationConstant.NATIONAL_CENTER_TEXT);
                tvo.setApplyUnitName(contract.getApplyUnitName());
                resList.add(tvo);
            });
        }


        if(StringUtils.isBlank(vo.getInnovationType())
                || StringUtils.equals(vo.getInnovationType(), InnovationConstant.PROVINCE_CENTER)
                || StringUtils.equals(vo.getInnovationType(), InnovationConstant.INNOVATION_QUALITY)) {
            // 省质检中心
            example = new Example(InnovationProvinceCenterApply.class);
            Example.Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
            if (isOrgHead) {
                criteria.andEqualTo("createUser", getCurrentUserName());
            }
            if(StringUtils.isNotBlank(vo.getInnovationName())) {
                criteria.andLike("centerName", "%" + vo.getInnovationName() + "%");
            }
            if(StringUtils.isNotBlank(vo.getContributeProgress())) {
                criteria.andEqualTo("contributeProgress", vo.getContributeProgress());
            }
            if(StringUtils.isNotBlank(vo.getLocationArea())) {
                criteria.andEqualTo("locationArea", vo.getLocationArea());
            }
            if(StringUtils.isNotBlank(vo.getApplyUnitName())) {
                criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName() + "%");
            }
//            if(isOrgUser) {
//                criteria.andCondition("(org_code = '" + orgId +"' or org_name = '" + orgName + "')");
//            }
            queryConditionService.querySelfDataOlny(criteria, "orgCode");
            List<InnovationProvinceCenterApply> laboratoryContractList = innovationProvinceCenterApplyMapper.selectByExample(example);
            laboratoryContractList.forEach(contract -> {
                InnovationGeneralInnovationApplyVo tvo = new InnovationGeneralInnovationApplyVo();
                BeanUtils.copyProperties(contract, tvo);
                tvo.setInnovationName(contract.getCenterName());
                tvo.setInnovationType(InnovationConstant.PROVINCE_CENTER_TEXT);
                tvo.setApplyUnitName(contract.getApplyUnitName());
                resList.add(tvo);
            });
        }

        List list = resList.subList((vo.getPageNum()-1) * vo.getPageSize(), Math.min(resList.size(), vo.getPageNum() * vo.getPageSize()));
        PageInfo pageInfo = new PageInfo(list);
        pageInfo.setPageSize(vo.getPageSize());
        pageInfo.setPageNum(vo.getPageNum());
        pageInfo.setTotal(resList.size());
        return pageInfo;
    }

    @Override
    public Object queryProject(ProjectInfoStatistics statistics) {

        Example example = new Example(ProjectApplyInfo.class);
        Example.Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);

        //获取当前用户的角色【多角色】
        List<String> roleList = getUserRoleList().stream().map(sysRole -> sysRole.getRoleCode() + "@ROLE").collect(Collectors.toList());
        if(roleList.size() == 1 && roleList.get(0).equals(AssigneeConstant.ORG_HEAD_ROLE)){
            criteria.andEqualTo("createUser", getCurrentUserName());
        }

        if(StringUtils.isNotBlank(statistics.getProjectName())){
            criteria.andLike("projectName", "%" + statistics.getProjectName() + "%");
            // pass = s.getProjectApplyInfo().getProjectName()!=null && s.getProjectApplyInfo().getProjectName().contains(statistics.getProjectName());
        }
        if(StringUtils.isNotBlank(statistics.getProjectTypeCode())){
            criteria.andEqualTo("projectTypeCode", statistics.getProjectTypeCode());
            // pass = s.getProjectApplyInfo().getProjectTypeCode()!=null && statistics.getProjectTypeCode().contains(s.getProjectApplyInfo().getProjectTypeCode());
        }
        if(StringUtils.isNotBlank(statistics.getProjectSecondTypeCode())){
            criteria.andEqualTo("projectSecondTypeCode", statistics.getProjectSecondTypeCode());
        }
        if(statistics.getYearNoSearch() != null){
            criteria.andIn("yearNo", Arrays.asList(statistics.getYearNoSearch().split(",")));
            // pass = s.getProjectApplyInfo().getYearNo()!=null && statistics.getYearNo().equals(s.getProjectApplyInfo().getYearNo());
        }
        if(StringUtils.isNotBlank(statistics.getApplyUnitName())){
            criteria.andLike("applyUnitName", "%" + statistics.getApplyUnitName() + "%");
            // pass = s.getProjectApplyInfo().getApplyUnitName()!=null && s.getProjectApplyInfo().getApplyUnitName().contains(statistics.getApplyUnitName());
        }
        if(StringUtils.isNotBlank(statistics.getLeaderName())){
            criteria.andLike("leaderName", "%" + statistics.getLeaderName() + "%");
            // pass = s.getProjectApplyInfo().getLeaderName()!=null && s.getProjectApplyInfo().getLeaderName().contains(statistics.getLeaderName());
        }
        queryConditionService.querySelfDataOlny(criteria, "orgCode");
        example.orderBy("createTime").desc();
        List<ProjectApplyInfo> projects = this.projectApplyInfoMapper.selectByExample(example);

        List<ProjectContractApply> contracts = projectContractApplyMapper.selectAll();

        List<ProjectContractApplyChange> changes = projectContractApplyChangeMapper.selectAll();

        List<ProjectContractInterimReport> reports = projectContractInterimReportMapper.selectAll();

        List<ProjectContractAccept> accepts = projectContractAcceptMapper.selectAll();

        List<ProjectInfoStatistics> statisticsList = new ArrayList<>();

        projects.stream().filter(p->CommonConstant.FLAG_YES.equals(p.getYn())).forEach(project -> {
            ProjectInfoStatistics s = new ProjectInfoStatistics();
            s.setProjectApplyInfo(project);
            BeanUtils.copyProperties(project, s);
            ProjectContractApply contract = contracts.stream().filter(c->CommonConstant.FLAG_YES.equals(c.getYn())&&project.getId().equals(c.getApplyId())).findAny().orElse(null);
            if(contract != null){
                ProjectContractApplyChange change = changes.stream().filter(c->CommonConstant.FLAG_YES.equals(c.getYn())&&contract.getId().equals(c.getOriginContractId())).findAny().orElse(null);
                ProjectContractInterimReport report = reports.stream().filter(c->CommonConstant.FLAG_YES.equals(c.getYn())&&contract.getId().equals(c.getContractId())).findAny().orElse(null);
                ProjectContractAccept accept = accepts.stream().filter(c->CommonConstant.FLAG_YES.equals(c.getYn())&&contract.getId().equals(c.getContractId())).findAny().orElse(null);
                s.setProjectContractApply(contract);
                s.setProjectContractApplyChange(change);
                s.setProjectContractInterimReport(report);
                s.setProjectContractAccept(accept);
            }
            s.setStoreProject(s.getProjectContractApply() == null?"是":"否");
            s.setApplyTimes(s.getProjectApplyInfo().getApplyCount());
            String status = "";
            if(FlowStatusEnum.WAIT_APPLY.getCode().equals(s.getProjectApplyInfo().getFlowStatus())) {
                status = "储备";
            } else if(!FlowStatusEnum.END.getCode().equals(s.getProjectApplyInfo().getFlowStatus())){
                status = "申报中";
            } else if(s.getProjectContractAccept() != null
                    && FlowStatusEnum.END.getCode().equals(s.getProjectContractAccept().getFlowStatus())){
                status = "已验收";
            } else if(s.getProjectContractApplyChange() != null
                    && "中止变更".equals(s.getProjectContractApplyChange().getChangeType())
                    && FlowStatusEnum.END.getCode().equals(s.getProjectContractApplyChange().getFlowStatus())){
                status = "已终止";
            } else if(s.getProjectContractApply() != null && s.getProjectContractApply().getEndDate() != null
                    && s.getProjectContractApply().getEndDate().getTime() < System.currentTimeMillis()){
                status = "在研（延期）";
            } else {
                status = "在研（正常）";
            }
            s.setProjectStatus(status);
            statisticsList.add(s);
        });

        List<ProjectInfoStatistics> resList = statisticsList.stream().filter(s -> {
            boolean pass = true;
            if(StringUtils.equals("是",statistics.getStoreProject())){
                pass = s.getProjectContractApply() == null;
            } else if(StringUtils.equals("否",statistics.getStoreProject())){
                pass = s.getProjectContractApply() != null;
            }

            if (pass && StringUtils.isNotBlank(statistics.getProjectStatus())) {
                boolean proStatusRes = false;
                if(statistics.getProjectStatus().contains("储备")) {
                    proStatusRes = proStatusRes || FlowStatusEnum.WAIT_APPLY.getCode().equals(s.getProjectApplyInfo().getFlowStatus());
                }
                if(statistics.getProjectStatus().contains("申报中")) {
                    proStatusRes = proStatusRes ||
                            (!FlowStatusEnum.WAIT_APPLY.getCode().equals(s.getProjectApplyInfo().getFlowStatus())
                                    && !FlowStatusEnum.END.getCode().equals(s.getProjectApplyInfo().getFlowStatus()));
                }
                if(statistics.getProjectStatus().contains("正常")) {
                    proStatusRes = proStatusRes ||
                            (s.getProjectContractApply() != null && s.getProjectContractApply().getEndDate() != null
                                    && s.getProjectContractApply().getEndDate().getTime() >= System.currentTimeMillis());
                }
                if(statistics.getProjectStatus().contains("延期")) {
                    proStatusRes = proStatusRes ||
                            (s.getProjectContractApply() != null && s.getProjectContractApply().getEndDate() != null
                                    && s.getProjectContractApply().getEndDate().getTime() < System.currentTimeMillis());
                }
                if(statistics.getProjectStatus().contains("已验收")) {
                    proStatusRes = proStatusRes ||
                            (s.getProjectContractAccept() != null
                                    && FlowStatusEnum.END.getCode().equals(s.getProjectContractAccept().getFlowStatus()));
                }
                if(statistics.getProjectStatus().contains("已结题")) {

                }
                if(statistics.getProjectStatus().contains("已终止")) {
                    proStatusRes = proStatusRes ||
                            (s.getProjectContractApplyChange() != null
                                    && "中止变更".equals(s.getProjectContractApplyChange().getChangeType())
                                    && FlowStatusEnum.END.getCode().equals(s.getProjectContractApplyChange().getFlowStatus()));
                }
                pass = pass && proStatusRes;
            }
            return pass;
        }).collect(Collectors.toList());
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(statistics.getPageNum());
        pageInfo.setPageSize(statistics.getPageSize());
        pageInfo.setList(resList);
        return pageInfo;
    }

    @Override
    public Object queryPerson(BasicPersonVo basicPersonVo) {
        PageHelper.startPage(basicPersonVo.getPageNum(),basicPersonVo.getPageSize());
        Example example=new Example(BasicPerson.class);
        Example.Criteria criteria=example.createCriteria();
        criteria.andEqualTo("yn",CommonConstant.FLAG_YES);

        //查询条件
        if(StringUtils.isNotBlank(basicPersonVo.getAreaCode())){
            criteria.andEqualTo("areaCode", basicPersonVo.getAreaCode());
        }
        if(StringUtils.isNotBlank(basicPersonVo.getName())){
            criteria.andLike("name", "%" + basicPersonVo.getName() + "%");
        }
        if(StringUtils.isNotBlank(basicPersonVo.getUnitName())){
            criteria.andLike("unitName", "%" + basicPersonVo.getUnitName() + "%");
        }
        if(StringUtils.isNotBlank(basicPersonVo.getPhone())){
            criteria.andLike("phone", "%" + basicPersonVo.getPhone() + "%");
        }
        if(StringUtils.isNotBlank(basicPersonVo.getEducation())){
            criteria.andEqualTo("education", basicPersonVo.getEducation());
        }
        if(StringUtils.isNotBlank(basicPersonVo.getDegree())){
            criteria.andEqualTo("degree", basicPersonVo.getDegree());
        }
        if(StringUtils.isNotBlank(basicPersonVo.getProField())){
            criteria.andEqualTo("proField", basicPersonVo.getProField());
        }
        if(StringUtils.isNotBlank(basicPersonVo.getTitleLevel())){
            criteria.andEqualTo("titleLevel", basicPersonVo.getTitleLevel());
        }
        if(basicPersonVo.getIsSystem() != null){
            criteria.andEqualTo("isSystem", basicPersonVo.getIsSystem());
        }
        if(StringUtils.equals("0", basicPersonVo.getIsExpert())){
            criteria.andCondition("(id not in (select b.person_id from basic_person_expert b where b.yn = 1 and b.flow_status = 999 and b.PERSON_ID is not null))");
        }
        if(basicPersonVo.getIsInternatinoalPro() != null){
            criteria.andEqualTo("isInternatinoalPro", basicPersonVo.getIsInternatinoalPro());
        }
        if(basicPersonVo.getIsNationPro() != null){
            criteria.andEqualTo("isNationPro", basicPersonVo.getIsNationPro());
        }
        if(basicPersonVo.getIsProvincePro() != null){
            criteria.andEqualTo("isProvincePro", basicPersonVo.getIsProvincePro());
        }
        if(basicPersonVo.getIsInternatinoalReviewPro() != null){
            criteria.andEqualTo("isInternatinoalReviewPro", basicPersonVo.getIsInternatinoalReviewPro());
        }
        if(basicPersonVo.getIsNationReviewPro() != null){
            criteria.andEqualTo("isNationReviewPro", basicPersonVo.getIsNationReviewPro());
        }
        if(basicPersonVo.getIsProvinceReviewPro() != null){
            criteria.andEqualTo("isProvinceReviewPro", basicPersonVo.getIsProvinceReviewPro());
        }
        if(basicPersonVo.getIsProvinceSubjectLeader() != null){
            criteria.andEqualTo("isProvinceSubjectLeader", basicPersonVo.getIsProvinceSubjectLeader());
        }
        if(basicPersonVo.getIsNationTalent() != null){
            criteria.andEqualTo("isNationTalent", basicPersonVo.getIsNationTalent());
        }
        if(basicPersonVo.getIsProvinceTalent() != null){
            criteria.andEqualTo("isProvinceTalent", basicPersonVo.getIsProvinceTalent());
        }
        if(basicPersonVo.getIsCityTalent() != null){
            criteria.andEqualTo("isCityTalent", basicPersonVo.getIsCityTalent());
        }
        if(basicPersonVo.getIsDoctorTutor() != null){
            criteria.andEqualTo("isDoctorTutor", basicPersonVo.getIsDoctorTutor());
        }
        if(basicPersonVo.getIsMasterTutor() != null){
            criteria.andEqualTo("isMasterTutor", basicPersonVo.getIsMasterTutor());
        }
        if(StringUtils.isNotBlank(basicPersonVo.getOnDuty())){
            criteria.andEqualTo("onDuty", basicPersonVo.getOnDuty());
        }

        queryConditionService.querySelfDataOlny(criteria, "orgId");

        example.orderBy("createTime").desc();
        List<BasicPerson> basicPersonList=basicPersonMapper.selectByExample(example);
        return new PageInfo<BasicPerson>(basicPersonList);
    }

    @Override
    public Object queryTech(TechAwardsApplyVo techAwardsApplyVo) {
        PageHelper.startPage(techAwardsApplyVo.getPageNum(),techAwardsApplyVo.getPageSize());
        Example example=new Example(TechAwardsApply.class);
        Example.Criteria criteria=example.createCriteria();
        criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
        //查询条件
        if(StringUtils.isNotBlank(techAwardsApplyVo.getApplyUnitName())){
            criteria.andLike("applyUnitName", "%" + techAwardsApplyVo.getApplyUnitName() + "%");
        }
        if(StringUtils.isNotBlank(techAwardsApplyVo.getApplyProjectName())){
            criteria.andLike("applyProjectName", "%" + techAwardsApplyVo.getApplyProjectName() + "%");
        }
        if(StringUtils.isNotBlank(techAwardsApplyVo.getApplyAwardName())){
            criteria.andLike("applyAwardName", "%" + techAwardsApplyVo.getApplyAwardName() + "%");
        }
        if(StringUtils.isNotBlank(techAwardsApplyVo.getTypeCode())){
            criteria.andEqualTo("typeCode", techAwardsApplyVo.getTypeCode());
        }
        if(StringUtils.isNotBlank(techAwardsApplyVo.getTypeSecondCode())){
            criteria.andEqualTo("typeSecondCode", techAwardsApplyVo.getTypeSecondCode());
        }
        if(techAwardsApplyVo.getStartTime() != null){
            criteria.andGreaterThanOrEqualTo("applyTime", techAwardsApplyVo.getStartTime());
        }
        if(techAwardsApplyVo.getEndTime() != null){
            criteria.andLessThanOrEqualTo("applyTime", techAwardsApplyVo.getEndTime());
        }
        queryConditionService.querySelfDataOlny(criteria, "orgCode");
        List<TechAwardsApply> techAwardsApplyList=techAwardsApplyMapper.selectByExample(example);
        return new PageInfo<TechAwardsApply>(techAwardsApplyList);
    }
}
