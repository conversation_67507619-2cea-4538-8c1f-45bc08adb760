package com.fd.stdp.service.project.impl;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import cn.afterturn.easypoi.word.WordExportUtil;
import com.alibaba.fastjson.JSONObject;
import com.fd.stdp.beans.basic.BasicManageOrg;
import com.fd.stdp.beans.basic.BasicPerson;
import com.fd.stdp.beans.flow.FlowNodeVo;
import com.fd.stdp.beans.project.*;
import com.fd.stdp.beans.project.vo.*;
import com.fd.stdp.beans.sys.*;
import com.fd.stdp.common.BaseController;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.constant.ProjectDeclarationFlowConstants;
import com.fd.stdp.dao.basic.BasicManageOrgMapper;
import com.fd.stdp.dao.basic.BasicPersonMapper;
import com.fd.stdp.dao.basic.BasicScienceOrgMapper;
import com.fd.stdp.dao.project.*;
import com.fd.stdp.dao.sys.SysFileInfoMapper;
import com.fd.stdp.dao.sys.SysRoleMapper;
import com.fd.stdp.dao.sys.SysUserMapper;
import com.fd.stdp.dao.sys.SysUserRoleMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.enums.ProjectApplyFlowStatus;
import com.fd.stdp.enums.ProjectSecondTypeEnum;
import com.fd.stdp.enums.ProjectTypeEnum;
import com.fd.stdp.process.ProcessNode;
import com.fd.stdp.process.projectApply.FlowNodeTask;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.project.*;
import com.fd.stdp.service.sys.SysDictItemService;
import com.fd.stdp.service.user.SysUserUtilService;
import com.fd.stdp.util.*;
import com.fd.stdp.util.expertExtract.ExpertApi;
import com.fd.stdp.util.expertExtract.beans.fegn.ExpertSendSms;
import com.github.pagehelper.Page;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.servlet.http.HttpServletResponse;

import static com.fd.stdp.common.BaseController.*;

/**
 * <AUTHOR>
 * @Description: 项目基本信息Service业务层处理
 * @date 2021-11-26
 */
@Service
//@Transactional(readOnly = true)
public class ProjectApplyInfoServiceImpl extends BaseServiceImpl<ProjectApplyInfoMapper, ProjectApplyInfo> implements ProjectApplyInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectApplyInfoServiceImpl.class);
    @Autowired
    private ProjectApplyInfoMapper projectApplyInfoMapper;

    @Autowired
    private ProjectApplyCooperationUnitMapper projectApplyCooperationUnitMapper;

    @Autowired
    private ProjectApplyCostMapper projectApplyCostMapper;

    @Autowired
    private ProjectApplyTeamsMapper projectApplyTeamsMapper;

    @Autowired
    private ProjectApplyDevicesMapper projectApplyDevicesMapper;

    @Autowired
    private ProjectApplyProgressMapper projectApplyProgressMapper;
    @Autowired
    private ProjectApplyExpertMumberMapper projectApplyExpertMumberMapper;

    @Autowired
    private ProjectApplyExpertsMapper projectApplyExpertsMapper;
    @Autowired
    private ProjectApplyExpertsService projectApplyExpertsService;

    @Autowired
    private FlowApiService flowApiService;

    @Autowired
    private ProjectNumberSettingService projectNumberSettingService;

    @Autowired
    private BasicFileAppendixService basicFileAppendixService;
    @Autowired
    private SysFileInfoMapper sysFileInfoMapper;

    @Autowired
    private ProjectContractApplyService projectContractApplyService;

    @Autowired
    private FlowCommonService flowCommonService;
    @Autowired
    private SysDictItemService sysDictItemService;
    @Autowired
    private ProjectNumberSettingMapper projectNumberSettingMapper;

    @Autowired
    private SysUserUtilService sysUserUtilService;
    @Autowired
    private BasicScienceOrgMapper scienceOrgMapper;
    @Autowired
    private BasicManageOrgMapper manageOrgMapper;
    @Autowired
    private BasicPersonMapper basicPersonMapper;

    @Autowired
    private ProjectContractApplyMapper projectContractApplyMapper;

    @Autowired
    private ProjectContractApplyChangeMapper projectContractApplyChangeMapper;
    @Autowired
    private ProjectContractInterimReportMapper projectContractInterimReportMapper;
    @Autowired
    private ProjectContractAcceptMapper projectContractAcceptMapper;

    @Autowired
    private ProjectApplyResearchEquipmentMapper projectApplyResearchEquipmentMapper;

    @Autowired
    private ProjectApplyInfoZbFundsPlanMapper projectApplyInfoZbFundsPlanMapper;

    @Autowired
    private ProjectApplyExpertScoreMapper scoreMapper;

    @Autowired
    private ProjectApplyRecommendUnitMapper recommendUnitMapper;

    @Autowired
    private ProjectApplyTalentShareMapper talentShareMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    FlowNodeTask flowNodeTask;

    @Autowired
    ExpertApi expertApi;

    private BCryptPasswordEncoder bp = new BCryptPasswordEncoder();

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新项目基本信息
     *@param projectApplyInfoVo 项目基本信息对象
     *@return String 项目基本信息ID
     *@Author: yujianfei
     */
    public String saveOrUpdateProjectApplyInfo(ProjectApplyInfoVo projectApplyInfoVo) {
        if (projectApplyInfoVo == null) {
            throw new ServiceException("数据异常");
        }

        if (StringUtils.isEmpty(projectApplyInfoVo.getId())) {
            //新增
            projectApplyInfoVo.setId(UUIDUtils.getUUID());
            projectApplyInfoVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
            projectApplyInfoVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
            projectApplyInfoMapper.insertSelective(projectApplyInfoVo);
        } else {
            //避免页面传入修改
            projectApplyInfoVo.setYn(null);
            projectApplyInfoMapper.updateByPrimaryKeySelective(projectApplyInfoVo);
        }

        // 合作单位
        if (projectApplyInfoVo.getUnitList() != null) {
            int sort = 1;
            for (ProjectApplyCooperationUnit val : projectApplyInfoVo.getUnitList()) {
                val.setSort(sort++);
            }
            updateUnitList(projectApplyInfoVo);
            StringBuilder stringBuilder = new StringBuilder();
            for (ProjectApplyCooperationUnit unit : projectApplyInfoVo.getUnitList()) {
                stringBuilder.append(unit.getUnitName()).append(",");
            }
            projectApplyInfoVo.setCooperationUnitText(stringBuilder.toString());
        }

        // 项目团队
        if (projectApplyInfoVo.getTeamsList() != null) {
            int index = 1;
            for (ProjectApplyTeams t : projectApplyInfoVo.getTeamsList()) {
                t.setSort(index++);
            }
            updateTeamsList(projectApplyInfoVo);
        }

        // 预算
        if (projectApplyInfoVo.getProjectApplyCost() != null || projectApplyInfoVo.getProjectAllowanceCost() != null) {
            projectApplyCostMapper.clearByFormId(projectApplyInfoVo.getId());
            // 科研经费 合计预算
            if (projectApplyInfoVo.getProjectApplyCost() != null) {
                updateProjectApplyCost(projectApplyInfoVo);
            }
            // 科研经费 财政补助
            if (projectApplyInfoVo.getProjectAllowanceCost() != null) {
                updateProjectAllowanceCost(projectApplyInfoVo);
            }
            // 科研经费 自筹经费
            if (projectApplyInfoVo.getProjectSelfCost() != null) {
                updateProjectSelfCost(projectApplyInfoVo);
            }
        }
        //设备购置预算明细
        if (projectApplyInfoVo.getDevicesList() != null) {
            int sort = 1;
            for (ProjectApplyDevices val : projectApplyInfoVo.getDevicesList()) {
                val.setSort(sort++);
            }
            updateProjectApplyDevices(projectApplyInfoVo);
        }

        //项目计划进度安排
        if (projectApplyInfoVo.getProgressList() != null) {
            int sort = 1;
            for (ProjectApplyProgress val : projectApplyInfoVo.getProgressList()) {
                val.setSort(sort++);
            }
            updateProjectApplyProgress(projectApplyInfoVo);
        }

        // 附件
        if (projectApplyInfoVo.getFiles() != null) {
            updateFileAppendixList(projectApplyInfoVo);
        }

        /**
         * 计算分数
         */
        calculateScore(projectApplyInfoVo);

        //参与单位列表
        if (projectApplyInfoVo.getRecommendList() != null) {
            recommendUnitMapper.deleteRecommendUnit(projectApplyInfoVo.getId());
            Integer sort = 0;
            for (ProjectApplyRecommendUnit projectApplyRecommendUnit : projectApplyInfoVo.getRecommendList()) {
                ProjectApplyRecommendUnit recommendUnit = new ProjectApplyRecommendUnit();
                BeanUtils.copyProperties(projectApplyRecommendUnit, recommendUnit);
                recommendUnit.setId(UUIDUtils.getUUID());
                recommendUnit.setApplyId(projectApplyInfoVo.getId());
                recommendUnit.setSort(sort++);
                recommendUnitMapper.insertSelective(recommendUnit);
            }
        }

        //科研仪器及设备
        if (!CollectionUtils.isEmpty(projectApplyInfoVo.getResearchEquipmentList())) {
            projectApplyResearchEquipmentMapper.deleteEquipment(projectApplyInfoVo.getId());
            Integer sort = 0;
            for (ProjectApplyResearchEquipment projectApplyResearchEquipment : projectApplyInfoVo.getResearchEquipmentList()) {
                ProjectApplyResearchEquipment equipment = new ProjectApplyResearchEquipment();
                BeanUtils.copyProperties(projectApplyResearchEquipment, equipment);
                equipment.setId(UUIDUtils.getUUID());
                equipment.setApplyId(projectApplyInfoVo.getId());
                equipment.setSort(sort++);
                projectApplyResearchEquipmentMapper.insertSelective(equipment);
            }
        }

        //装备项目资金安排
        if (!CollectionUtils.isEmpty(projectApplyInfoVo.getFundsPlanList())) {
            projectApplyInfoZbFundsPlanMapper.deleteZbFundsPlan(projectApplyInfoVo.getId());
            Integer sort = 0;
            for (ProjectApplyInfoZbFundsPlan projectApplyInfoZbFundsPlan : projectApplyInfoVo.getFundsPlanList()) {
                ProjectApplyInfoZbFundsPlan zbFundsPlan = new ProjectApplyInfoZbFundsPlan();
                BeanUtils.copyProperties(projectApplyInfoZbFundsPlan, zbFundsPlan);
                zbFundsPlan.setId(UUIDUtils.getUUID());
                zbFundsPlan.setApplyId(projectApplyInfoVo.getId());
                zbFundsPlan.setSort(sort++);
                projectApplyInfoZbFundsPlanMapper.insertSelective(zbFundsPlan);
            }
        }

        //人才计划
        if (!CollectionUtils.isEmpty(projectApplyInfoVo.getTalentShareList())) {
            talentShareMapper.deleteTalentShare(projectApplyInfoVo.getId());
            Integer sort = 0;
            for (ProjectApplyTalentShare projectApplyTalentShare : projectApplyInfoVo.getTalentShareList()) {
                ProjectApplyTalentShare talentShare = new ProjectApplyTalentShare();
                BeanUtils.copyProperties(projectApplyTalentShare, talentShare);
                talentShare.setId(UUIDUtils.getUUID());
                talentShare.setApplyId(projectApplyInfoVo.getId());
                talentShare.setSort(sort++);
                talentShareMapper.insertSelective(talentShare);
            }
        }

        projectApplyInfoMapper.updateByPrimaryKeySelective(projectApplyInfoVo);

        return projectApplyInfoVo.getId();
    }

    /**
     * 外部项目的保存
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(readOnly = false)
    public String saveOutProjectApplyInfo(ProjectApplyInfoVo vo) {
        this.saveOrUpdateProjectApplyInfo(vo);
        vo.setFlowStatus(FlowStatusEnum.OUT_END.getCode());
        projectApplyInfoMapper.updateByPrimaryKeySelective(vo);
        return null;
    }

    // 根据 成果计算项目分数
    private void calculateScore(ProjectApplyInfoVo projectApplyInfoVo) {
        if (projectApplyInfoVo.getIntellectualProperty() != null) {
            Integer score = 0;
            // 专利
            if (projectApplyInfoVo.getInventionPatent() != null) {
                score += 15 * projectApplyInfoVo.getInventionPatent();
            }
            if (projectApplyInfoVo.getInventionApply() != null) {
                score += 10 * projectApplyInfoVo.getInventionApply();
            }
            if (projectApplyInfoVo.getNewModelPatent() != null) {
                score += 5 * projectApplyInfoVo.getNewModelPatent();
            }
            // 软著
            if (projectApplyInfoVo.getSoftwareCopyright() != null) {
                score += 5 * projectApplyInfoVo.getSoftwareCopyright();
            }
            // 论文
            if (projectApplyInfoVo.getSciPaper() != null) {
                score += 20 * projectApplyInfoVo.getSciPaper();
            }
            if (projectApplyInfoVo.getEiPaper() != null) {
                score += 15 * projectApplyInfoVo.getEiPaper();
            }
            if (projectApplyInfoVo.getCorePaper() != null) {
                score += 10 * projectApplyInfoVo.getCorePaper();
            }
            if (projectApplyInfoVo.getNormalPaper() != null) {
                score += 5 * projectApplyInfoVo.getNormalPaper();
            }
            if (projectApplyInfoVo.getWorks() != null) {
                score += 20 * projectApplyInfoVo.getWorks();
            }
            // 标准
            if (projectApplyInfoVo.getNationalStandard() != null) {
                score += 20 * projectApplyInfoVo.getNationalStandard();
            }
            if (projectApplyInfoVo.getIndustryStandard() != null) {
                score += 15 * projectApplyInfoVo.getIndustryStandard();
            }
            if (projectApplyInfoVo.getLocalStandard() != null) {
                score += 10 * projectApplyInfoVo.getLocalStandard();
            }
            if (projectApplyInfoVo.getGroupStandard() != null) {
                score += 5 * projectApplyInfoVo.getGroupStandard();
            }
            projectApplyInfoVo.setIntellectualScore(score.toString());
        }


        if (projectApplyInfoVo.getTechnicalLevelProperty() != null) {
            List<SysDictItem> sysDictItems = sysDictItemService.findDictItemByDictType("TECHNICAL_LEVEL");
            for (SysDictItem sdi : sysDictItems) {
                if (StringUtils.equals(projectApplyInfoVo.getTechnicalLevelProperty(), sdi.getItemValue())) {
                    projectApplyInfoVo.setTechnicalLevelScore(String.valueOf(sdi.getOrderVal()));
                    break;
                }
            }
        }
        if (projectApplyInfoVo.getApplicationProperty() != null) {
            List<SysDictItem> sysDictItems = sysDictItemService.findDictItemByDictType("APPLICATION_INDICATORS");
            for (SysDictItem sdi : sysDictItems) {
                if (StringUtils.equals(projectApplyInfoVo.getApplicationProperty(), sdi.getItemValue())) {
                    projectApplyInfoVo.setApplicationScore(String.valueOf(sdi.getOrderVal()));
                    break;
                }
            }
        }

    }

    // 项目分数 的判断
    private void gradeScore(ProjectApplyInfoVo vo) {
        if (StringUtils.equals(ProjectTypeEnum.CY.getCode(), vo.getProjectTypeCode())) {
            int score = Integer.valueOf(vo.getIntellectualScore()) + Integer.valueOf(vo.getTechnicalLevelScore()) + Integer.valueOf(vo.getApplicationScore());
            if (StringUtils.equals(ProjectSecondTypeEnum.CY_04.getCode(), vo.getProjectSecondTypeCode())) {
                if (score < 80) {
                    throw new ServiceException(String.format("项目主要绩效目标分值(%d)未达到所申报项目要求(80)", score));
                }
            } else {
                if (score < 100) {
                    throw new ServiceException(String.format("项目主要绩效目标分值(%d)未达到所申报项目要求(100)", score));
                }
            }
        }
    }

    /**
     * 历史项目不申报验证
     *
     * @param projectApplyInfoVo
     */
    private void oldProjectCheck(ProjectApplyInfoVo projectApplyInfoVo) {
        if (StringUtils.equals(projectApplyInfoVo.getProjectTypeCode(), ProjectTypeEnum.ZCZB.getCode())) {
            return;
        }
        if (projectApplyInfoVo.getYearNo() != null && projectApplyInfoVo.getYearNo() < 2022) {
            if (FileResourceUtil.project2020 == null && FileResourceUtil.project2021 == null) {
                FileResourceUtil.loadProjectFile();
            }
            String orgName = projectApplyInfoVo.getOrgName();
            ProjectApplyTeams leader = projectApplyInfoVo.getTeamsList().stream().filter(t -> "0".equals(t.getIsLeader())).findAny().orElse(null);
            if (leader != null) {
                String leaderName = leader.getName();
                FileResourceUtil.project2020.stream().forEach(p -> {
                    if (StringUtils.equals(p.get(2).trim(), orgName) && StringUtils.equals(p.get(3).trim(), leaderName)) {
                        throw new ServiceException("已存在2020年立项项目中");
                    }
                });
                FileResourceUtil.project2021.stream().forEach(p -> {
                    if (StringUtils.equals(p.get(2).trim(), orgName) && StringUtils.equals(p.get(3).trim(), leaderName)) {
                        throw new ServiceException("已存在2021年立项项目中");
                    }
                });
            }
        }
    }

    /**
     * 青年科技专项，项目负责人年龄35周岁以下
     *
     * @param projectApplyInfoVo
     */
    private void ageCheck(ProjectApplyInfoVo projectApplyInfoVo) {
        if (ProjectTypeEnum.YBKYKY.getCode().equals(projectApplyInfoVo.getProjectTypeCode())
                && ProjectSecondTypeEnum.YBKYKY_QINGNIANKEJIPROJECT.getCode().equals(projectApplyInfoVo.getProjectSecondTypeCode())) {
            projectApplyInfoVo.getTeamsList().stream().filter(t -> "0".equals(t.getIsLeader())).forEach(t -> {
                BasicPerson person = new BasicPerson();
                person.setId(t.getPersonId());
                person = basicPersonMapper.selectOne(person);
                if (person == null) {
                    throw new ServiceException("未找到项目负责人信息，请重新选择");
                }
                if (person.getIdCardNumber() == null) {
                    throw new ServiceException("获取项目负责人身份证失败，请完善基础信息");
                }
                int yearb = Integer.parseInt(person.getIdCardNumber().substring(6, 10));
                int monthb = Integer.parseInt(person.getIdCardNumber().substring(10, 12));
                int dayb = Integer.parseInt(person.getIdCardNumber().substring(12, 14));
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                if (calendar.get(Calendar.YEAR) - yearb > 36
                        || (calendar.get(Calendar.YEAR) - yearb == 36 && calendar.get(Calendar.MONTH) + 1 > monthb)
                        || (calendar.get(Calendar.YEAR) - yearb == 36 && calendar.get(Calendar.MONTH) + 1 == monthb && calendar.get(Calendar.DAY_OF_MONTH) >= dayb)) {
                    throw new ServiceException("年龄超过青年专项限制");
                }
            });
        }
    }

    /**
     * 项目负责人、项目骨干 校验
     */
    private void leaderCheck(ProjectApplyInfoVo projectApplyInfoVo) {
        // 校验：在没走完的流程中，一个人只能同时担任一个项目的负责人，两个项目的项目骨干
        List<String> typeCodes;
        if (ProjectTypeEnum.ZCZB.getCode().equals(projectApplyInfoVo.getProjectTypeCode())) {
            // 装备项目
            typeCodes = Arrays.asList(new String[]{ProjectTypeEnum.ZCZB.getCode()});
        } else {
            // 科研/雏鹰项目
            typeCodes = Arrays.asList(new String[]{ProjectTypeEnum.CY.getCode(), ProjectTypeEnum.YBKYKY.getCode()});
        }

        List<ProjectApplyTeams> teamsList = projectApplyInfoVo.getTeamsList().stream()
                .filter(t -> "0".equals(t.getIsLeader()) || "1".equals(t.getIsLeader()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(teamsList)) {
            List<String> personIdList = teamsList.stream().map(t -> t.getPersonId()).distinct().collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(personIdList)) {
                ProjectApplyTeamsVo vo = new ProjectApplyTeamsVo();
                vo.setApplyId(projectApplyInfoVo.getId());
                vo.setPersonIdList(personIdList);
                vo.setTypeCodes(typeCodes);
                List<ProjectApplyTeamsVo> teamsVos = projectApplyTeamsMapper.countTeamNumberCharactor(vo);
                for (ProjectApplyTeamsVo tv : teamsVos) {
                    teamsList.stream().filter(t -> StringUtils.equals(t.getPersonId(), tv.getPersonId())).forEach(t -> {
                        if ("0".equals(t.getIsLeader())) {
                            if (tv.getLeaderCount() > 0) {
                                throw new ServiceException(tv.getName() + "担任项目负责人角色数达到上限");
                            }
                        } else if ("1".equals(t.getIsLeader())) {
                            if (tv.getLeaderCount() + tv.getMainCount() > 1) {
                                throw new ServiceException(tv.getName() + "担任项目骨干角色数达到上限");
                            }
                        }
                    });
                }
            }
        }
        // 校验结束
    }

    /**
     * 成果总数校验
     *
     * @param projectApplyInfoVo
     */
    private void awardCheck(ProjectApplyInfoVo projectApplyInfoVo) {
        try {
            if (projectApplyInfoVo.getPatent() != projectApplyInfoVo.getInventionPatent() + projectApplyInfoVo.getInventionApply()
                    + projectApplyInfoVo.getNewModelPatent()) {
                throw new ServiceException("专利总数不符");
            }
            if (projectApplyInfoVo.getPaper() != projectApplyInfoVo.getSciPaper() + projectApplyInfoVo.getEiPaper()
                    + projectApplyInfoVo.getCorePaper() + projectApplyInfoVo.getNormalPaper()
                    + projectApplyInfoVo.getWorks()) {
                throw new ServiceException("论文总数不符");
            }
        } catch (NullPointerException e) {
            throw new ServiceException("请填报完整成果信息");
        }
    }


    /**
     * 经费校验
     *
     * @param projectApplyInfoVo
     */
    private void fundsCheck(ProjectApplyInfoVo projectApplyInfoVo) {
        // 装备项目不进行经费校验
        if (!StringUtils.equals(projectApplyInfoVo.getProjectTypeCode(), ProjectTypeEnum.ZCZB.getCode())) {
//            try {
            if (projectApplyInfoVo.getTotalFunds() == null) {
                throw new ServiceException("请输入总经费");
            }
            if (projectApplyInfoVo.getFiscalFunds() == null) {
                throw new ServiceException("请输入财政经费");
            }
            if (projectApplyInfoVo.getSelfRaisedFunds() == null) {
                throw new ServiceException("请输入自筹经费");
            }
            if (projectApplyInfoVo.getProjectApplyCost() != null && projectApplyInfoVo.getProjectAllowanceCost() != null) {
                ProjectApplyCost applyCost = projectApplyInfoVo.getProjectApplyCost();
                ProjectApplyCost allowanceCost = projectApplyInfoVo.getProjectAllowanceCost();
                ProjectApplyCost projectSelfCost = projectApplyInfoVo.getProjectSelfCost();
                projectApplyInfoVo.getProjectApplyCost().setTatalCost(
                        applyCost.getEquipment()
                                .add(applyCost.getBusinessCost())
                                .add(applyCost.getLabor())
                                .add(applyCost.getIndirectCost())
                );
                projectApplyInfoVo.getProjectAllowanceCost().setTatalCost(
                        allowanceCost.getEquipment()
                                .add(allowanceCost.getBusinessCost())
                                .add(allowanceCost.getLabor())
                                .add(allowanceCost.getIndirectCost())
                );
                projectApplyInfoVo.getProjectSelfCost().setTatalCost(
                        projectSelfCost.getEquipment()
                                .add(projectSelfCost.getBusinessCost())
                                .add(projectSelfCost.getLabor())
                                .add(projectSelfCost.getIndirectCost())
                );
                if (applyCost.getTatalCost().add(allowanceCost.getTatalCost()).add(projectSelfCost.getTatalCost()).compareTo(projectApplyInfoVo.getTotalFunds()) != 0) {
                    throw new ServiceException("合计预算不等于项目总经费");
                }
                if (allowanceCost.getTatalCost().compareTo(applyCost.getTatalCost()) == 1) {
                    throw new ServiceException("财政补助合计不得超过预算合计");
                }
                if (projectSelfCost.getTatalCost().compareTo(applyCost.getTatalCost()) == 1) {
                    throw new ServiceException("自筹经费合计不得超过预算合计");
                }
                if (allowanceCost.getEquipment().compareTo(applyCost.getEquipment()) == 1) {
                    throw new ServiceException("财政补助设备费不得超过预算");
                }
            }
            if (projectApplyInfoVo.getProgressList() != null) {
                BigDecimal totalSelf = new BigDecimal(0);
                BigDecimal totalFiscal = new BigDecimal(0);
                for (ProjectApplyProgress progress : projectApplyInfoVo.getProgressList()) {
                    totalSelf = totalSelf.add(progress.getSelfRaised());
                    totalFiscal = totalFiscal.add(progress.getFiscal());
                }
                if (totalFiscal.compareTo(projectApplyInfoVo.getFiscalFunds()) == 1) {
                    throw new ServiceException("项目计划总财政经费不得超过项目财政经费");
                }
                if (totalSelf.compareTo(projectApplyInfoVo.getSelfRaisedFunds()) == 1) {
                    throw new ServiceException("项目计划总自筹经费不得超过项目自筹经费");
                }
                if (totalFiscal.add(totalSelf).compareTo(projectApplyInfoVo.getTotalFunds()) == 1) {
                    throw new ServiceException("项目计划总经费不得超过项目总经费");
                }
            }
        }
    }

    private void updateExperts(ProjectApplyInfoVo projectApplyInfoVo) {
        projectApplyExpertMumberMapper.clearByFormId(projectApplyInfoVo.getId());
        if (!CollectionUtils.isEmpty(projectApplyInfoVo.getExperts())) {
            for (ProjectApplyExpertMumberVo expertMumberVo : projectApplyInfoVo.getExperts()) {
                // 根据手机号判断是否存在该专家用户
                SysUser sysUser = sysUserMapper.findUserByPhone(expertMumberVo.getPhone());
                String uuid = UUIDUtils.getUUID();
                if (sysUser == null) {
                    //创建专家用户
                    SysUser user = new SysUser();
                    user.setId(uuid);
                    user.setPhone(expertMumberVo.getPhone());
                    user.setType("PERSONAL");
                    user.setTypeName("个人用户");
                    user.setEnabled(CommonConstant.FLAG_YES);
                    user.setNickname(expertMumberVo.getName());
                    user.setUsername("zj" + expertMumberVo.getPhone());
                    user.setPassword(bp.encode("1234567@Jh"));
                    sysUserMapper.insertSelective(user);
                    //添加角色
                    String roleId = sysRoleMapper.findRoleByCode("EXPERT");
                    if (roleId == null) {
                        throw new ServiceException("未找到指定的角色代码：EXPERT");
                    }
                    SysUserRole sysUserRole = new SysUserRole();
                    sysUserRole.setId(UUIDUtils.getUUID());
                    sysUserRole.setUserId(user.getId());
                    sysUserRole.setRoleId(roleId);
                    sysUserRoleMapper.insertSelective(sysUserRole);
                    ExpertSendSms expertSendSms = new ExpertSendSms();
                    expertSendSms.setPhone(expertMumberVo.getPhone());
                    expertSendSms.setContent("您的账号为：zj" + expertMumberVo.getPhone() + "，密码：1234567@Jh，请登录系统修改密码。");
                    expertSendSms.setAreaCode("330000");
                    expertSendSms.setName("科技处专家用户创建短信");
                    expertApi.sendSms(expertSendSms);
                }
                ProjectApplyExpertMumber applyExpertMumber = new ProjectApplyExpertMumber();
                BeanUtils.copyProperties(expertMumberVo, applyExpertMumber);
                applyExpertMumber.setUserId(sysUser != null ? sysUser.getId() : uuid);
                applyExpertMumber.setApplyId(projectApplyInfoVo.getId());
                applyExpertMumber.setYn(CommonConstant.FLAG_YES);
                if (StringUtils.isEmpty(applyExpertMumber.getId())) {
                    applyExpertMumber.setId(UUIDUtils.getUUID());
                    this.projectApplyExpertMumberMapper.insertSelective(applyExpertMumber);
                } else {
                    this.projectApplyExpertMumberMapper.updateByPrimaryKeySelective(applyExpertMumber);
                }
            }
        }
    }

    @Override
    public String createProjectNumber(ProjectApplyInfoVo projectApplyInfoVo) {
        if (StringUtils.isBlank(projectApplyInfoVo.getProjectTypeCode()) || StringUtils.isBlank(projectApplyInfoVo.getProjectSecondTypeCode())) {
            throw new ServiceException("请选择项目类别与项目子类别");
        }
        ProjectNumberSetting projectNumberSetting = new ProjectNumberSetting();
        projectNumberSetting.setTypeCode(projectApplyInfoVo.getProjectTypeCode());
        projectNumberSetting.setTypeSecondCode(projectApplyInfoVo.getProjectSecondTypeCode());
        projectNumberSetting.setYn(CommonConstant.FLAG_YES);
        ProjectNumberSetting setting = projectNumberSettingMapper.selectOne(projectNumberSetting);
        String number = "";
        if (setting != null) {
            number = setting.getTypeEncode();
        } else {

        }

        String nost = projectApplyInfoVo.getYearNo().toString();
        Example example = new Example(ProjectApplyInfo.class);
        example.createCriteria().andLike("projectNumber", number + nost + "%");
        example.orderBy("projectNumber").desc();
        List<ProjectApplyInfo> list = this.mapper.selectByExample(example);
        if (list.size() > 0) {
            String projectNumber = list.get(0).getProjectNumber();
            Integer index = Integer.parseInt(projectNumber.substring(projectNumber.length() - 4));
            String fix = "";
            if (index < 10) {
                fix = "000";
            } else if (index < 100) {
                fix = "00";
            } else if (index < 1000) {
                fix = "0";
            } else if (index < 10000) {
                fix = "";
            }
            return projectNumber.substring(0, projectNumber.length() - 4) + fix + (index + 1);
        } else {
            return number + nost + "00001";
        }
    }

    // 附件
    private void updateFileAppendixList(ProjectApplyInfoVo projectApplyInfoVo) {
        basicFileAppendixService.clearAndUpdateBasicFileAppendix(projectApplyInfoVo.getId(), projectApplyInfoVo.getFiles());
    }

    //项目计划进度安排
    private void updateProjectApplyProgress(ProjectApplyInfoVo projectApplyInfoVo) {
        projectApplyProgressMapper.clearByFormId(projectApplyInfoVo.getId());
        if (!CollectionUtils.isEmpty(projectApplyInfoVo.getProgressList())) {
            for (ProjectApplyProgress projectApplyProgress : projectApplyInfoVo.getProgressList()) {
                ProjectApplyProgress addProjectApplyProgress = new ProjectApplyProgress();
                BeanUtils.copyProperties(projectApplyProgress, addProjectApplyProgress);
                addProjectApplyProgress.setApplyId(projectApplyInfoVo.getId());
                addProjectApplyProgress.setYn(CommonConstant.FLAG_YES);
                if (StringUtils.isEmpty(addProjectApplyProgress.getId())) {
                    addProjectApplyProgress.setId(UUIDUtils.getUUID());
                    projectApplyProgressMapper.insertSelective(addProjectApplyProgress);
                } else {
                    projectApplyProgressMapper.updateByPrimaryKeySelective(addProjectApplyProgress);
                }
            }
        }
    }

    //设备购置预算明细
    private void updateProjectApplyDevices(ProjectApplyInfoVo projectApplyInfoVo) {
        projectApplyDevicesMapper.clearByFormId(projectApplyInfoVo.getId());
        if (!CollectionUtils.isEmpty(projectApplyInfoVo.getDevicesList())) {
            for (ProjectApplyDevices projectApplyDevices : projectApplyInfoVo.getDevicesList()) {
                ProjectApplyDevices addProjectApplyDevices = new ProjectApplyDevices();
                BeanUtils.copyProperties(projectApplyDevices, addProjectApplyDevices);
                addProjectApplyDevices.setApplyId(projectApplyInfoVo.getId());
                addProjectApplyDevices.setYn(CommonConstant.FLAG_YES);
                //TODO INFORMATION_LEVEL【信息化水平（是否配备标准的计算机网络接口）】
                if (StringUtils.isEmpty(addProjectApplyDevices.getId())) {
                    addProjectApplyDevices.setId(UUIDUtils.getUUID());
                    projectApplyDevicesMapper.insertSelective(addProjectApplyDevices);
                } else {
                    projectApplyDevicesMapper.updateByPrimaryKeySelective(addProjectApplyDevices);
                }
            }
        }
    }

    // 自筹经费
    private void updateProjectSelfCost(ProjectApplyInfoVo projectApplyInfoVo) {
        if (projectApplyInfoVo.getProjectSelfCost() != null) {
            projectApplyInfoVo.getProjectSelfCost().calculateTotal();
            ProjectApplyCost projectSelfCost = new ProjectApplyCost();
            BeanUtils.copyProperties(projectApplyInfoVo.getProjectSelfCost(), projectSelfCost);
            projectSelfCost.setApplyId(projectApplyInfoVo.getId());
            projectSelfCost.setYn(CommonConstant.FLAG_YES);
            projectSelfCost.setIsAllowance(CommonConstant.FLAG_II);
            if (StringUtils.isEmpty(projectSelfCost.getId())) {
                projectSelfCost.setId(UUIDUtils.getUUID());
                projectApplyCostMapper.insertSelective(projectSelfCost);
            } else {
                projectApplyCostMapper.updateByPrimaryKeySelective(projectSelfCost);
            }
        }
    }

    // 财政补助
    private void updateProjectAllowanceCost(ProjectApplyInfoVo projectApplyInfoVo) {
        if (projectApplyInfoVo.getProjectAllowanceCost() != null) {
            //TODO 任务书ID【contractId】、经费开支科目【costType】、
            // 购置设备费【buyEquipment】、试制设备费【makeEquipment】、leaseEquipment【设置租赁费】
            projectApplyInfoVo.getProjectAllowanceCost().calculateTotal();
            ProjectApplyCost projectAllowanceCost = new ProjectApplyCost();
            BeanUtils.copyProperties(projectApplyInfoVo.getProjectAllowanceCost(), projectAllowanceCost);
            projectAllowanceCost.setApplyId(projectApplyInfoVo.getId());
            projectAllowanceCost.setYn(CommonConstant.FLAG_YES);
            projectAllowanceCost.setIsAllowance(CommonConstant.FLAG_YES);
            if (StringUtils.isEmpty(projectAllowanceCost.getId())) {
                projectAllowanceCost.setId(UUIDUtils.getUUID());
                projectApplyCostMapper.insertSelective(projectAllowanceCost);
            } else {
                projectApplyCostMapper.updateByPrimaryKeySelective(projectAllowanceCost);
            }
        }
    }

    // 科研经费
    private void updateProjectApplyCost(ProjectApplyInfoVo projectApplyInfoVo) {
        if (projectApplyInfoVo.getProjectApplyCost() != null) {
            // 计算总数
            projectApplyInfoVo.getProjectApplyCost().calculateTotal();
            //TODO 任务书ID【contractId】、经费开支科目【costType】、
            // 购置设备费【buyEquipment】、试制设备费【makeEquipment】、leaseEquipment【设置租赁费】
            ProjectApplyCost projectApplyCost = new ProjectApplyCost();
            BeanUtils.copyProperties(projectApplyInfoVo.getProjectApplyCost(), projectApplyCost);
            projectApplyCost.setApplyId(projectApplyInfoVo.getId());
            projectApplyCost.setYn(CommonConstant.FLAG_YES);
            if (StringUtils.isEmpty(projectApplyCost.getId())) {
                projectApplyCost.setId(UUIDUtils.getUUID());
                projectApplyCostMapper.insertSelective(projectApplyCost);
            } else {
                System.out.println("projectApplyCost:" + projectApplyCost.getDirectCost());
                projectApplyCostMapper.updateByPrimaryKeySelective(projectApplyCost);
            }
        }
    }

    // 合作单位
    private void updateUnitList(ProjectApplyInfoVo projectApplyInfoVo) {
        projectApplyCooperationUnitMapper.clearByFormId(projectApplyInfoVo.getId());
        if (!CollectionUtils.isEmpty(projectApplyInfoVo.getUnitList())) {
            for (ProjectApplyCooperationUnit applyCooperationUnit : projectApplyInfoVo.getUnitList()) {
                ProjectApplyCooperationUnit addProjectApplyCooperationUnit = new ProjectApplyCooperationUnit();
                BeanUtils.copyProperties(applyCooperationUnit, addProjectApplyCooperationUnit);
                addProjectApplyCooperationUnit.setApplyId(projectApplyInfoVo.getId());
                addProjectApplyCooperationUnit.setYn(CommonConstant.FLAG_YES);
                if (StringUtils.isEmpty(addProjectApplyCooperationUnit.getId())) {
                    addProjectApplyCooperationUnit.setId(UUIDUtils.getUUID());
                    projectApplyCooperationUnitMapper.insertSelective(addProjectApplyCooperationUnit);
                } else {
                    projectApplyCooperationUnitMapper.updateByPrimaryKeySelective(addProjectApplyCooperationUnit);
                }
            }
        }
    }

    // 项目团队
    private void updateTeamsList(ProjectApplyInfoVo projectApplyInfoVo) {

        projectApplyTeamsMapper.clearByFormId(projectApplyInfoVo.getId());
        if (!CollectionUtils.isEmpty(projectApplyInfoVo.getTeamsList())) {
            if (StringUtils.isBlank(projectApplyInfoVo.getProjectTypeCode())) {
                throw new ServiceException("请先选择项目类别才能保存团队成员");
            }
            for (ProjectApplyTeams projectApplyTeams : projectApplyInfoVo.getTeamsList()) {
                ProjectApplyTeams addProjectApplyTeams = new ProjectApplyTeams();
                BeanUtils.copyProperties(projectApplyTeams, addProjectApplyTeams);
                addProjectApplyTeams.setApplyId(projectApplyInfoVo.getId());
                addProjectApplyTeams.setYn(CommonConstant.FLAG_YES);
                if (StringUtils.isEmpty(addProjectApplyTeams.getId())) {
                    addProjectApplyTeams.setId(UUIDUtils.getUUID());
                    projectApplyTeamsMapper.insertSelective(addProjectApplyTeams);
                } else {
                    projectApplyTeamsMapper.updateByPrimaryKeySelective(addProjectApplyTeams);
                }
            }
        }
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除项目基本信息
     *@param id void 项目基本信息ID
     *@Author: yujianfei
     */
    public void deleteProjectApplyInfo(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            ProjectApplyInfo projectApplyInfo = projectApplyInfoMapper.selectByPrimaryKey(id);
            if (projectApplyInfo == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            ProjectApplyInfo temprojectApplyInfo = new ProjectApplyInfo();
            temprojectApplyInfo.setYn(CommonConstant.FLAG_NO);
            temprojectApplyInfo.setId(projectApplyInfo.getId());
            projectApplyInfoMapper.updateByPrimaryKeySelective(temprojectApplyInfo);
        }
    }

    /**
     * @param id
     * @return ProjectApplyInfo
     * @Description: 查询项目基本信息详情
     * @Author: yujianfei
     */
    @Override
    public ProjectApplyInfo findById(String id) {
        ProjectApplyInfoVo vo = new ProjectApplyInfoVo();
        ProjectApplyInfo projectApplyInfo = projectApplyInfoMapper.selectByPrimaryKey(id);
        BeanUtils.copyProperties(projectApplyInfo, vo);

        Example example = new Example(ProjectApplyProgress.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
        example.orderBy("sort").asc();
        vo.setProgressList(projectApplyProgressMapper.selectByExample(example));

        example = new Example(ProjectApplyDevices.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
        example.orderBy("sort").asc();
        vo.setDevicesList(projectApplyDevicesMapper.selectByExample(example));

        example = new Example(ProjectApplyTeams.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
        example.orderBy("sort").asc();
        vo.setTeamsList(projectApplyTeamsMapper.selectByExample(example));

        example = new Example(ProjectApplyCooperationUnit.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
        example.orderBy("sort").asc();
        vo.setUnitList(projectApplyCooperationUnitMapper.selectByExample(example));

        example = new Example(ProjectApplyCost.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
                .andEqualTo("applyId", id).andEqualTo("isAllowance", CommonConstant.FLAG_NO);
        vo.setProjectApplyCost(projectApplyCostMapper.selectOneByExample(example));

        example = new Example(ProjectApplyCost.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
                .andEqualTo("applyId", id).andEqualTo("isAllowance", CommonConstant.FLAG_YES);
        vo.setProjectAllowanceCost(projectApplyCostMapper.selectOneByExample(example));

        example = new Example(ProjectApplyCost.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
                .andEqualTo("applyId", id).andEqualTo("isAllowance", CommonConstant.FLAG_II);
        vo.setProjectSelfCost(projectApplyCostMapper.selectOneByExample(example));

        if (vo.getProjectApplyCost() == null) {
            vo.setProjectApplyCost(new ProjectApplyCost());
        }
        if (vo.getProjectAllowanceCost() == null) {
            vo.setProjectAllowanceCost(new ProjectApplyCost());
        }
        if (vo.getProjectSelfCost() == null) {
            vo.setProjectSelfCost(new ProjectApplyCost());
        }

        /**
         * 专家及其评审结果
         */
        example = new Example(ProjectApplyExpertMumber.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
                .andEqualTo("applyId", id).andEqualTo("status", "1");
        example.orderBy("createTime").desc();
        List<ProjectApplyExpertMumber> projectApplyExpertMumbers = projectApplyExpertMumberMapper.selectByExample(example);
        List<ProjectApplyExpertMumberVo> voList = new ArrayList<>();
        for (ProjectApplyExpertMumber projectApplyExpertMumber : projectApplyExpertMumbers) {
            ProjectApplyExpertMumberVo expertMumberVo = new ProjectApplyExpertMumberVo();
            BeanUtils.copyProperties(projectApplyExpertMumber, expertMumberVo);
            //专家评审详情
            ProjectApplyExpertScoreVo scoreVo = scoreMapper.selectExpertScore(projectApplyExpertMumber);
            //专家评审上传的附件
            List<SysFileInfo> sysFileInfos = new ArrayList<>();
            if (scoreVo != null && !StringUtils.isEmpty(scoreVo.getFileIds())){
                for (String s : Arrays.asList(scoreVo.getFileIds().split(","))) {
                    SysFileInfo fileInfo = sysFileInfoMapper.selectByPrimaryKey(s);
                    sysFileInfos.add(fileInfo);
                }
                scoreVo.setFileList(sysFileInfos);
            }
            expertMumberVo.setScoreVo(scoreVo);
            voList.add(expertMumberVo);
        }
        vo.setExperts(voList);

        /**
         * 专家评审结果
         */
        example = new Example(ProjectApplyExperts.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
                .andEqualTo("applyId", id);
        example.orderBy("createTime").desc();
        vo.setExpertsReviews(projectApplyExpertsMapper.selectByExample(example));

        /**
         * 当前登录专家评审结果
         */
        ProjectApplyExpertScoreVo nowExpertScoreVo = new ProjectApplyExpertScoreVo();
        if (CommonConstant.EXPERT_ROLE.equals(BaseController.getLoginUser().getType())) {
            nowExpertScoreVo = scoreMapper.selectNowExpertScore(id, BaseController.getCurrentUserId());
        }
        vo.setNowExpertScore(nowExpertScoreVo);

        /**
         * 附件
         */
        vo.setFiles(basicFileAppendixService.findByFormId(vo.getId()));

        /**
         * 流程
         */
        vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));

        //参与单位列表
        Example recommendUnitExample = new Example(ProjectApplyRecommendUnit.class);
        recommendUnitExample.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
        recommendUnitExample.orderBy("sort");
        List<ProjectApplyRecommendUnit> projectApplyRecommendUnits = recommendUnitMapper.selectByExample(recommendUnitExample);
        vo.setRecommendList(projectApplyRecommendUnits != null ? projectApplyRecommendUnits : Collections.emptyList());

        //科研项目仪器及设备
        Example devicesExample = new Example(ProjectApplyDevices.class);
        devicesExample.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
        devicesExample.orderBy("sort");
        List<ProjectApplyResearchEquipment> equipmentList = projectApplyResearchEquipmentMapper.selectByExample(devicesExample);
        vo.setResearchEquipmentList(equipmentList != null ? equipmentList : Collections.emptyList());

        //装备项目资金安排
        List<ProjectApplyInfoZbFundsPlan> projectApplyInfoZbFundsPlans = projectApplyInfoZbFundsPlanMapper.findFundsPlan(id);
        vo.setFundsPlanList(projectApplyInfoZbFundsPlans != null ? projectApplyInfoZbFundsPlans : Collections.emptyList());

        //人才共享情况
        Example talentShareExample = new Example(ProjectApplyTalentShare.class);
        talentShareExample.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
        talentShareExample.orderBy("sort");
        List<ProjectApplyTalentShare> projectApplyTalentShares = talentShareMapper.selectByExample(talentShareExample);
        vo.setTalentShareList(projectApplyTalentShares != null ? projectApplyTalentShares : Collections.emptyList());

        return vo;
    }


    /**
     * @param projectApplyInfoVo
     * @return PageInfo<ProjectApplyInfo>
     * @Description: 分页查询项目基本信息
     * @Author: yujianfei
     */
    @Override
    public PageInfo<ProjectApplyInfo> findPageByQuery(ProjectApplyInfoVo projectApplyInfoVo) {
        PageHelper.startPage(projectApplyInfoVo.getPageNum(), projectApplyInfoVo.getPageSize());
        Example example = new Example(ProjectApplyInfo.class);
        Criteria criteria = getCriteria(projectApplyInfoVo, example);
        if (StringUtils.isNotBlank(projectApplyInfoVo.getFlowStatus())) {
            criteria.andEqualTo("flowStatus", projectApplyInfoVo.getFlowStatus());
        }
        example.orderBy("createTime").desc();
        List<ProjectApplyInfo> projectApplyInfoList = projectApplyInfoMapper.selectByExample(example);//findPageByQuery(projectApplyInfoVo);

        PageInfo pageInfo = new PageInfo<ProjectApplyInfo>(projectApplyInfoList);

        setExpertDetail(pageInfo);
        if (StringUtils.isNotBlank(projectApplyInfoVo.getProCode())) {
            Collections.sort(pageInfo.getList(), new Comparator<ProjectApplyInfoVo>() {
                @Override
                public int compare(ProjectApplyInfoVo o1, ProjectApplyInfoVo o2) {
                    int num = o1.getAverage().subtract(o2.getAverage()).intValue();
                    if (num > 0) {
                        return 1;
                    } else if (num < 0) {
                        return -1;
                    }
                    return 0;
                }
            });
        }
//        pageInfo.setList(list);
        return pageInfo;
    }

    /**
     * 项目信息提交
     *
     * @param projectApplyInfoVo
     * @return
     */
    @Override
    @Transactional(readOnly = false)
    public String submitProjectApplyInfo(ProjectApplyInfoVo projectApplyInfoVo) {
//        fundsCheck(projectApplyInfoVo);
//        awardCheck(projectApplyInfoVo);
        projectApplyInfoVo.setApplyCount(projectApplyInfoVo.getApplyCount() == null ? 1 : projectApplyInfoVo.getApplyCount() + 1);
        String id = this.saveOrUpdateProjectApplyInfo(projectApplyInfoVo);
        gradeScore(projectApplyInfoVo);
//        gradeScore(projectApplyInfoVo);
//        ageCheck(projectApplyInfoVo);
//        teamNumberCheck(projectApplyInfoVo);
        leaderCheck(projectApplyInfoVo);
        ageCheck(projectApplyInfoVo);
        oldProjectCheck(projectApplyInfoVo);


//        //TODO
//        //项目负责人限制条件
//        leaderLimitCheck(projectApplyInfoVo);
//        //项目主要参与人员限制条件
//        memberLimitCheck(projectApplyInfoVo);
//        //尖峰、尖兵项目负责人学位限制；
//        if (ProjectTypeEnum.JF.getCode().equals(projectApplyInfoVo.getProjectSecondTypeCode())
//                || ProjectTypeEnum.JB.getCode().equals(projectApplyInfoVo.getProjectSecondTypeCode())){
//            degreeLimitCheck(projectApplyInfoVo);
//        }
//        //领雁计划专项及以上项目应有不少于3家不同层级或类型机构联合申报；
//        if(ProjectTypeEnum.LY.getCode().equals(projectApplyInfoVo.getProjectSecondTypeCode())){
//            unitTypeLimitCheck(projectApplyInfoVo);
//        }

        projectApplyInfoVo.setAssigner(projectApplyInfoVo.getUserId());
//        teamNumberCheck(projectApplyInfoVo);

        if (StringUtils.isBlank(projectApplyInfoVo.getActNodeKey())) {
            projectApplyInfoVo.setAssignerGroup(ProjectDeclarationFlowConstants.ORG_TASK_ROLE);
            flowNodeTask.start(projectApplyInfoVo);
        } else {
            applyAudit(projectApplyInfoVo, getCurrentUserName(), getCurrentRealName());
        }

//        flowNodeTask.next(projectApplyInfoVo);
//        flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_PROJECT_APPLY, projectApplyInfoVo, this.mapper,
//                StringUtils.isNotBlank(projectApplyInfoVo.getAuditAdvice()) ? projectApplyInfoVo.getAuditAdvice() : "提交项目申报申请");


        return id;
    }


    /**
     * 领雁计划专项及以上项目应有不少于3家不同层级或类型机构联合申报
     *
     * @param projectApplyInfoVo
     */
    private void unitTypeLimitCheck(ProjectApplyInfoVo projectApplyInfoVo) {

    }


    /**
     * 项目负责人限制
     *
     * @param projectApplyInfoVo
     */
    private void leaderLimitCheck(ProjectApplyInfoVo projectApplyInfoVo) {
        List<ProjectApplyTeams> teamsList = projectApplyInfoVo.getTeamsList().stream()
                .filter(t -> "0".equals(t.getIsLeader()))
                .collect(Collectors.toList());
        //获取项目团队负责人
        if (!CollectionUtils.isEmpty(teamsList)) {
            ProjectApplyTeams proLeader = teamsList.get(0);
            //查询项目负责人已经参与的在研项目数（排除本项目）
            int count = projectApplyTeamsMapper.countProLeaderJoinProject(proLeader);
            if (count > 0) {
                throw new ServiceException("项目负责人在研项目不得超过1项，且无超期在研项目。当前项目团队项目负责人已参与其他在研项目");
            }
        }
    }

    /**
     * 项目主要参与人限制
     *
     * @param projectApplyInfoVo
     */
    private void memberLimitCheck(ProjectApplyInfoVo projectApplyInfoVo) {
        List<ProjectApplyTeams> teamsList = projectApplyInfoVo.getTeamsList().stream()
                .filter(t -> "0".equals(t.getIsLeader()) || "1".equals(t.getIsLeader()))
                .collect(Collectors.toList());
        //获取项目团队负责人
        if (!CollectionUtils.isEmpty(teamsList)) {
            List<String> idNumberList = teamsList.stream().map(t -> t.getIdNumber()).distinct().collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(idNumberList)) {
                ProjectApplyTeamsVo teamsVo = new ProjectApplyTeamsVo();
                teamsVo.setApplyId(projectApplyInfoVo.getId());
                teamsVo.setIdCardList(idNumberList);
                List<ProjectApplyTeamsVo> teamsVoList = projectApplyTeamsMapper.memberLimitCheck(teamsVo);

                if (!CollectionUtils.isEmpty(teamsVoList)) {
                    for (ProjectApplyTeamsVo tv : teamsVoList) {
                        teamsList.stream().filter(t -> StringUtils.equals(t.getIdNumber(), tv.getIdNumber())).forEach(t -> {
                            if ("0".equals(t.getIsLeader())) {
                                if (tv.getLeaderCount() > 0) {
                                    throw new ServiceException(tv.getName() + "担任项目负责人角色数达到上限");
                                }
                            } else if ("1".equals(t.getIsLeader())) {
                                if (tv.getLeaderCount() + tv.getMainCount() > 1) {
                                    throw new ServiceException(tv.getName() + "担任项目骨干角色数达到上限");
                                }
                            }
                        });
                    }
                }
            }
        }
    }

    /**
     *
     * 尖峰、尖兵项目负责人原则上要求在申报专业领域内具有副高及以上职称或博士学位
     *
     * @param projectApplyInfoVo
     */
    private void degreeLimitCheck(ProjectApplyInfoVo projectApplyInfoVo) {
        List<ProjectApplyTeams> teamsList = projectApplyInfoVo.getTeamsList().stream()
                .filter(t -> "0".equals(t.getIsLeader()))
                .collect(Collectors.toList());
        //获取项目团队负责人
        if (!CollectionUtils.isEmpty(teamsList)) {
            ProjectApplyTeams proLeader = teamsList.get(0);
            if (!"博士".equals(proLeader.getDegree())
                    || !"副高".equals(proLeader.getTitle())
                    || !"正高".equals(proLeader.getTitle())
                    || !"高级（副高）".equals(proLeader.getTitle())) {
                throw new ServiceException("尖峰、尖兵项目负责人原则上要求在申报专业领域内具有副高及以上职称或博士学位");
            }
        }
    }



    /**
     * 校验 项目团队的成员必须是本单位或合作单位的人
     *
     * @param projectApplyInfoVo
     */
    private void teamNumberCheck(ProjectApplyInfoVo projectApplyInfoVo) {
        // 装备项目不校验
        if (StringUtils.equals(projectApplyInfoVo.getProjectTypeCode(), ProjectTypeEnum.ZCZB.getCode())) {
            return;
        }
        List<String> units = projectApplyInfoVo.getUnitList().stream().map(u -> u.getUnitName()).collect(Collectors.toList());
        units.add(getCurrentOrgName());
        units.add(projectApplyInfoVo.getApplyUnitName());

        for (ProjectApplyTeams team : projectApplyInfoVo.getTeamsList()) {
            boolean in = false;
            for (String unit : units) {
                if (StringUtils.equals(team.getWorkCompany(), unit)) {
                    in = true;
                }
            }
            if (!in) {
                throw new ServiceException("提交失败，项目团队人员" + team.getName() + "不属于本单位或填报的推荐单位中");
            }
        }
    }

    /**
     * 项目信息审核
     *
     * @param projectApplyInfoVo
     * @return
     */
    @Override
    @Transactional(readOnly = false)
    public String auditProjectApplyInfo(ProjectApplyInfoVo projectApplyInfoVo) {

        // 省局审核时更新提交时间
        // 省局审核通过 会同时选择专家
        ProjectApplyInfo info = this.mapper.selectByPrimaryKey(projectApplyInfoVo.getId());
        if (StringUtils.equals(FlowStatusEnum.PROVINCE_AUDIT.getCode(), info.getFlowStatus())) {
            if (CollectionUtils.isEmpty(projectApplyInfoVo.getExperts())) {
                throw new ServiceException("请选择评审专家");
            }
            info.setSubmitDate(new Date());
            // 专家
            if (projectApplyInfoVo.getExperts() != null) {
                updateExperts(projectApplyInfoVo);

                StringBuilder stringBuilder = new StringBuilder();
                for (ProjectApplyExpertMumber munber : projectApplyInfoVo.getExperts()) {
                    stringBuilder.append(munber.getName()).append(",");
                }
                info.setExpertsText(stringBuilder.toString());
            }

            // 更新专家名称与提交时间
            this.mapper.updateByPrimaryKeySelective(info);

            /**
             * 为每一个专家添加一条打分记录
             */
            projectApplyExpertsMapper.clearByFormId(projectApplyInfoVo.getId());
            List<ProjectApplyExpertMumberVo> experts = projectApplyExpertMumberMapper.findByApplyId(projectApplyInfoVo.getId());
            if (CollectionUtils.isEmpty(experts)) {
                throw new ServiceException("专家数据异常");
            }
            experts.stream().forEach(e -> {
                ProjectApplyExperts projectApplyExperts = new ProjectApplyExperts();
                projectApplyExperts.setApplyId(projectApplyInfoVo.getId());
                projectApplyExperts.setUserId(e.getUserId());
                projectApplyExperts.setUserName(e.getName());
                projectApplyExperts.setIsSubmit(0);
                projectApplyExperts.setExpertType(e.getExpertType());
                projectApplyExpertsService.saveOrUpdateProjectApplyExperts(projectApplyExperts);
                if (projectApplyExperts.getUserId() == null) {
                    throw new ServiceException("未关联人员");
                }
            });
            flowCommonService.doFlowStepAudit(projectApplyInfoVo, this.mapper
                    , StringUtils.isNotBlank(projectApplyInfoVo.getAuditAdvice()) ? projectApplyInfoVo.getAuditAdvice() : "项目申报审核通过"
                    , FlowStatusEnum.CHOOSE_EXPERTS.getCode());
            flowCommonService.doCompleteTask(projectApplyInfoVo, this.mapper
                    , "选择了评审专家"
                    , FlowStatusEnum.EXPERTS_GRADE.getCode(), AssigneeConstant.EXPERT_ROLE);
        } else {
            flowCommonService.doFlowStepAudit(projectApplyInfoVo, this.mapper
                    , StringUtils.isNotBlank(projectApplyInfoVo.getAuditAdvice()) ? projectApplyInfoVo.getAuditAdvice() : "项目申报审核通过"
                    , FlowStatusEnum.CHOOSE_EXPERTS.getCode());
        }
        try {
            // 若流程到省局审核则更新提交时间
            info = this.mapper.selectByPrimaryKey(projectApplyInfoVo.getId());
            if (StringUtils.equals(FlowStatusEnum.PROVINCE_AUDIT.getCode(), info.getFlowStatus())) {
                info.setSubmitDate(new Date());
                this.mapper.updateByPrimaryKeySelective(info);
            }
        } catch (Exception e) {
            logger.error("项目申报更新提交时间失败");
        }
        return projectApplyInfoVo.getId();
    }

    /**
     * 省局批量审核
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(readOnly = false)
    public String multiProvinceAudit(ProjectApplyInfoVo vo) {
        vo.getIds().forEach(id -> {
            ProjectApplyInfo projectApplyInfo = this.mapper.selectByPrimaryKey(id);
            if (StringUtils.equals(projectApplyInfo.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())) {
                ProjectApplyInfoVo applyInfoVo = new ProjectApplyInfoVo();
                applyInfoVo.setId(id);
                applyInfoVo.setExperts(vo.getExperts());
                this.auditProjectApplyInfo(applyInfoVo);
            }
        });
        return null;
    }

    @Override
    public void exportPageByQuery(ProjectApplyInfoVo projectApplyInfoVo, HttpServletResponse response) {
        projectApplyInfoVo.setPageNum(1);
        projectApplyInfoVo.setPageSize(Integer.MAX_VALUE);
        List<ProjectApplyInfoVo> list = findPageByQuery(projectApplyInfoVo).getList().stream().map(p -> {
            ProjectApplyInfoVo vo = new ProjectApplyInfoVo();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList());

        List exportList = list;
        String model = "word/导出已报项目统计表单.xlsx";
        ClassPathResource classPathResource = new ClassPathResource(model);
        InputStream inputStream = null;
        try {
            inputStream = classPathResource.getInputStream();
        } catch (IOException e) {
            throw new ServiceException("加载文件失败");
        }
        EasyExcelUtils.exportTemplateFill(null, exportList, "Sheet1", "已报项目.xlsx", response, inputStream, true);
    }

    @Override
    public void exportReviewPageByQuery(ProjectApplyInfoVo projectApplyInfoVo, HttpServletResponse response) {
        List exportList = findPageByQuery(projectApplyInfoVo).getList();
        String model = "word/处室评议导出.xlsx";
        ClassPathResource classPathResource = new ClassPathResource(model);
        InputStream inputStream = null;
        try {
            inputStream = classPathResource.getInputStream();
        } catch (IOException e) {
            throw new ServiceException("加载文件失败");
        }
        EasyExcelUtils.exportTemplateFill(null, exportList, "Sheet1", "处室评议.xlsx", response, inputStream, true);
    }

    private InputStream getInputStream(String s) {
        ClassPathResource classPathResource = new ClassPathResource(s);
        InputStream inputStream = null;
        try {
            inputStream = classPathResource.getInputStream();
        } catch (IOException e) {
            throw new ServiceException("加载文件失败");
        }
        return inputStream;
    }

    @Override
    @Transactional(readOnly = false)
    public String sendBackProjectApplyInfo(ProjectApplyInfoVo projectApplyInfoVo) {
        ProjectApplyInfo info = this.mapper.selectByPrimaryKey(projectApplyInfoVo);
        if (StringUtils.equals(info.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())) {
            // 标记为省局退回状态
            info.setProvinceSendback(1);
            this.mapper.updateByPrimaryKeySelective(info);
        }
        flowCommonService.doFlowStepSendBack(projectApplyInfoVo, this.mapper
                , StringUtils.isNotBlank(projectApplyInfoVo.getAuditAdvice()) ? projectApplyInfoVo.getAuditAdvice() : "项目申报退回"
                , false
        );
        return null;
    }

    @Override
    public void expertsAuditFinished(ProjectApplyInfoVo projectApplyInfoVo) {
        Example example = new Example(ProjectApplyExperts.class);
        example.createCriteria().andEqualTo("yn", 1).andEqualTo("applyId", projectApplyInfoVo.getId());
        Integer count = projectApplyExpertsMapper.selectCountByExample(example);
        flowCommonService.doCompleteTask(projectApplyInfoVo, this.mapper
                , "全部专家评审完成"
                , FlowStatusEnum.PROJECT_REVIEW.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE, getCurrentUserName() + "等" + count + "人");
    }

    /**
     * 处室评议 未通过
     *
     * @param vo
     * @return
     */
    @Override
    @Transactional(readOnly = false)
    public String reviewProjectApplyInfo(ProjectApplyInfoVo vo) {
        // Map<String, Object> map = new HashMap<>();

        if (StringUtils.isBlank(vo.getAttr1())) {
            vo.setAttr1("未立项");
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("ISPASS", 0);
        flowCommonService.doCompleteTask(vo, this.mapper
                , StringUtils.isNotBlank(vo.getAuditAdvice()) ? vo.getAuditAdvice() : "处室评议未通过"
                , FlowStatusEnum.WAIT_APPLY.getCode(), AssigneeConstant.ORG_HEAD_ROLE, null, map);
        return vo.getId();
    }

    /**
     * 处室评议通过 项目下达
     *
     * @param projectApplyInfoVo
     * @return
     */
    @Override
    @Transactional(readOnly = false)
    public String releaseProjectApplyInfo(ProjectApplyInfoVo projectApplyInfoVo) {

        ProjectApplyInfoVo vo = (ProjectApplyInfoVo) findById(projectApplyInfoVo.getId());

        // 申请编号
        if (StringUtils.isNotBlank(vo.getProjectTypeCode())
                && StringUtils.isBlank(vo.getProjectNumber())
                && null != vo.getYearNo()) {
            vo.setProjectNumber(createProjectNumber(vo));
        }
        this.mapper.updateByPrimaryKeySelective(vo);

        // 转换成任务书
        ProjectContractApplyVo projectContractApplyVo = vo.toProjectContractApplyVo();
        projectContractApplyVo.setId(null);
        projectContractApplyVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
        projectContractApplyVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
        projectContractApplyVo.setApplyId(vo.getId());
        projectContractApplyService.saveOrUpdateProjectContractApply(projectContractApplyVo);

        vo.setAttr1("已立项");
        this.mapper.updateByPrimaryKeySelective(vo);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("ISPASS", 1);
        flowCommonService.doCompleteTask(vo, this.mapper
                , StringUtils.isNotBlank(vo.getAuditAdvice()) ? vo.getAuditAdvice() : "处室评议通过"
                , FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE, null, map);

        return null;
    }

    @Override
    public PageInfo<ProjectApplyInfo> todoList(ProjectApplyInfoVo vo) {
        Example example = new Example(ProjectContractApply.class);
        Criteria criteria = getCriteria(vo, example);
        example.orderBy("createTime").desc();
        PageInfo<ProjectApplyInfo> pageInfo = new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria, vo.getPageNum(), vo.getPageSize()));
        return pageInfo;
    }

    private void setExpertDetail(PageInfo<ProjectApplyInfo> pageInfo) {
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return;
        }
        List<ProjectApplyInfo> resList = new ArrayList<>();
        Example example = new Example(ProjectApplyExperts.class);
        example.createCriteria().andEqualTo("yn", 1).andIn("applyId", pageInfo.getList().stream().map(p -> p.getId()).collect(Collectors.toList()));
        List<ProjectApplyExperts> expertsList = projectApplyExpertsMapper.selectByExample(example);
        pageInfo.getList().forEach(p -> {
            ProjectApplyInfoVo pvo = new ProjectApplyInfoVo();
            BeanUtils.copyProperties(p, pvo);
            pvo.setExpertPointText("");
            pvo.setAverage(new BigDecimal(0));
            pvo.setSecondTypeChangedText("");
            // 设置专家评分内容
            expertsList.stream().filter(e -> StringUtils.equals(e.getApplyId(), p.getId())).forEach(e -> {
                if (e.getTotalScore() != null) {
                    pvo.setExpertPointText(pvo.getExpertPointText() + e.getUserName() + ":" + e.getTotalScore() + ";");
                } else {
                    pvo.setExpertPointText(pvo.getExpertPointText() + e.getUserName() + ":未评分;");
                }
            });
            // 设置专家变更内容
            expertsList.stream().filter(e -> StringUtils.equals(e.getApplyId(), p.getId())).forEach(e -> {
                if (StringUtils.isNotBlank(e.getProjectSecondTypeText()) && !StringUtils.equals(e.getProjectSecondTypeText(), p.getProjectSecondTypeText())) {
                    pvo.setSecondTypeChangedText(pvo.getSecondTypeChangedText() + "是:变更为" + e.getProjectSecondTypeText() + ";");
                } else {
                    pvo.setSecondTypeChangedText(pvo.getSecondTypeChangedText() + "否;");
                }
            });
            // 设置专家平均分内容
            expertsList.stream().filter(e -> e.getTotalScore() != null && StringUtils.equals(e.getApplyId(), p.getId())).forEach(e -> {
                pvo.setAverage(pvo.getAverage().add(e.getTotalScore()));
            });
            long expertCount = expertsList.stream().filter(e -> e.getTotalScore() != null && StringUtils.equals(e.getApplyId(), p.getId())).count();
            if (expertCount > 0 && pvo.getAverage() != null) {
                pvo.setAverage(pvo.getAverage().divide(new BigDecimal(expertCount), 2, BigDecimal.ROUND_HALF_UP));
            }
            // 设置专家意见内容
            int index = 0;
            String adviceModel = "%s：修改意见：%s；评审意见：%s；";
            for (ProjectApplyExperts expert : expertsList) {
                if (StringUtils.equals(expert.getApplyId(), pvo.getId())) {
                    index++;
                    switch (index) {
                        case 1:
                            pvo.setExpertAdvice1(String.format(adviceModel, expert.getUserName(), expert.getOptions(), expert.getAdvice()));
                            break;
                        case 2:
                            pvo.setExpertAdvice2(String.format(adviceModel, expert.getUserName(), expert.getOptions(), expert.getAdvice()));
                            break;
                        case 3:
                            pvo.setExpertAdvice3(String.format(adviceModel, expert.getUserName(), expert.getOptions(), expert.getAdvice()));
                            break;
                        default:
                    }
                }
            }
            resList.add(pvo);
        });
        pageInfo.setList(resList);
    }

    @Override
    public PageInfo<ProjectApplyInfo> finishedList(ProjectApplyInfoVo vo) {
        Example example = new Example(ProjectContractApply.class);
        Criteria criteria = getCriteria(vo, example);
        example.orderBy("createTime").desc();
        return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria, vo.getPageNum(), vo.getPageSize()));
    }

    @Override
    public PageInfo<ProjectApplyInfo> endList(ProjectApplyInfoVo projectApplyInfoVo) {
        PageHelper.startPage(projectApplyInfoVo.getPageNum(), projectApplyInfoVo.getPageSize());
        Example example = new Example(ProjectApplyInfo.class);
        Criteria criteria = getCriteria(projectApplyInfoVo, example);

        //获取当前用户的角色【多角色】
        List<String> roleList = getUserRoleList().stream().map(sysRole -> sysRole.getRoleCode() + "@ROLE").collect(Collectors.toList());
        if (roleList.size() == 1 && roleList.get(0).equals(AssigneeConstant.ORG_HEAD_ROLE)) {
            criteria.andEqualTo("createUser", getCurrentUserName());
        }
        example.orderBy("createTime").desc();
        List<ProjectApplyInfo> projectApplyInfoList = projectApplyInfoMapper.selectByExample(example);//findPageByQuery(projectApplyInfoVo);
        return new PageInfo<ProjectApplyInfo>(projectApplyInfoList);
    }

    @Override
    public Object download(String id, HttpServletResponse response) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Map<String, Object> map = new HashMap<String, Object>();
        OutputStream out = null;
        try {
            ProjectApplyInfo projectApplyInfo = this.mapper.selectByPrimaryKey(id);
            if (projectApplyInfo == null) {
                throw new ServiceException("未找到项目申请");
            }
            Field[] fields = ProjectApplyInfo.class.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                field.getName();
                map.put(field.getName(), "");
                if (null != field.get(projectApplyInfo)) {
                    if (field.getType() == String.class) {
                        map.put(field.getName(), field.get(projectApplyInfo));
                    } else if (field.getType() == Date.class) {
                        Date date = (Date) field.get(projectApplyInfo);
                        map.put(field.getName(), dateFormat.format(date));
                    }
                }
            }

            String filename = "项目申请表";
            XWPFDocument doc = WordExportUtil.exportWord07("src/main/resources/word/421" + filename + "模板.docx", map);
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "multipart/form-data");
            response.setHeader("Set-Cookie", "fileDownload=true; path=/");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(filename, "UTF-8") + ".docx");
            out = response.getOutputStream();
            doc.write(out);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    logger.error("输出流关闭失败!", e);
                }
            }
        }
        return null;
    }

    @Override
    public PageInfo<ProjectApplyInfo> findReservePageByQuery(ProjectApplyInfoVo vo) {
        Example example = new Example(ProjectApplyInfo.class);
        Criteria criteria = getCriteria(vo, example);
        if (StringUtils.isNotBlank(vo.getFlowStatus())) {
            criteria.andEqualTo("flowStatus", vo.getFlowStatus());
        } else {
            criteria.andEqualTo("flowStatus", FlowStatusEnum.WAIT_APPLY.getCode());
        }
        boolean isPriv = false;
        List<SysRole> roles = getUserRoleList();
        for (SysRole sr : roles) {
            if (StringUtils.equals(sr.getRoleCode(), AssigneeConstant.DEPT_PROVINCE_ROLE)) {
                isPriv = true;
            }
        }
        if (!isPriv) {
            criteria.andEqualTo("orgName", getCurrentOrgName());
        }

        //获取当前用户的角色【多角色】
        List<String> roleList = getUserRoleList().stream().map(sysRole -> sysRole.getRoleCode() + "@ROLE").collect(Collectors.toList());
        if (roleList.size() == 1 && roleList.get(0).equals(AssigneeConstant.ORG_HEAD_ROLE)) {
            criteria.andEqualTo("createUser", getCurrentUserName());
        }

        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

    @Override
    public Object getRecommandUnit() {
        // 市县技术机构推荐单位为市局，省直单位的推荐单位与申报单位相同
        Integer operType = sysUserUtilService.getUserOperType();
        if (operType == 2) {
            String sid = getCurrentScienceOrgId();
            if (sid != null) {
                return scienceOrgMapper.selectByPrimaryKey(sid);
            }
        } else {
            Example example = new Example(BasicManageOrg.class);
            example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
                    .andEqualTo("areaCode", getLoginUser().getAreaCode().substring(0, 4) + "00");
            List<BasicManageOrg> list = manageOrgMapper.selectByExample(example);
            return list.size() > 0 ? list.get(0) : null;
        }
        return null;
    }

    @Override
    public PageInfo statisticsProjectInfo(ProjectInfoStatistics statistics) {

        Example example = new Example(ProjectApplyInfo.class);
        Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);

        //获取当前用户的角色【多角色】
        List<String> roleList = getUserRoleList().stream().map(sysRole -> sysRole.getRoleCode() + "@ROLE").collect(Collectors.toList());
        if (roleList.size() == 1 && roleList.get(0).equals(AssigneeConstant.ORG_HEAD_ROLE)) {
            criteria.andEqualTo("createUser", getCurrentUserName());
        }
        if (roleList.size() == 1 && (roleList.get(0).equals(AssigneeConstant.ORG_HEAD_ROLE) || roleList.get(0).equals(AssigneeConstant.ORG_ADMIN_ROLE))) {
            criteria.andEqualTo("orgName", getCurrentOrgName());
        }

        if (StringUtils.isNotBlank(statistics.getProjectName())) {
            criteria.andLike("projectName", "%" + statistics.getProjectName() + "%");
            // pass = s.getProjectApplyInfo().getProjectName()!=null && s.getProjectApplyInfo().getProjectName().contains(statistics.getProjectName());
        }
        if (StringUtils.isNotBlank(statistics.getProjectTypeCode())) {
            criteria.andEqualTo("projectTypeCode", statistics.getProjectTypeCode());
            // pass = s.getProjectApplyInfo().getProjectTypeCode()!=null && statistics.getProjectTypeCode().contains(s.getProjectApplyInfo().getProjectTypeCode());
        }
        if (StringUtils.isNotBlank(statistics.getProjectSecondTypeCode())) {
            criteria.andEqualTo("projectSecondTypeCode", statistics.getProjectSecondTypeCode());
        }
        if (statistics.getYearNoSearch() != null) {
            criteria.andIn("yearNo", Arrays.asList(statistics.getYearNoSearch().split(",")));
            // pass = s.getProjectApplyInfo().getYearNo()!=null && statistics.getYearNo().equals(s.getProjectApplyInfo().getYearNo());
        }
        if (StringUtils.isNotBlank(statistics.getApplyUnitName())) {
            criteria.andLike("applyUnitName", "%" + statistics.getApplyUnitName() + "%");
            // pass = s.getProjectApplyInfo().getApplyUnitName()!=null && s.getProjectApplyInfo().getApplyUnitName().contains(statistics.getApplyUnitName());
        }
        if (StringUtils.isNotBlank(statistics.getLeaderName())) {
            criteria.andLike("leaderName", "%" + statistics.getLeaderName() + "%");
            // pass = s.getProjectApplyInfo().getLeaderName()!=null && s.getProjectApplyInfo().getLeaderName().contains(statistics.getLeaderName());
        }
        example.orderBy("createTime").desc();
        List<ProjectApplyInfo> projects = this.mapper.selectByExample(example);

        List<ProjectContractApply> contracts = projectContractApplyMapper.selectAll();

        List<ProjectContractApplyChange> changes = projectContractApplyChangeMapper.selectAll();

        List<ProjectContractInterimReport> reports = projectContractInterimReportMapper.selectAll();

        List<ProjectContractAccept> accepts = projectContractAcceptMapper.selectAll();

        List<ProjectInfoStatistics> statisticsList = new ArrayList<>();

        projects.stream().filter(p -> CommonConstant.FLAG_YES.equals(p.getYn())).forEach(project -> {
            ProjectInfoStatistics s = new ProjectInfoStatistics();
            s.setProjectApplyInfo(project);
            BeanUtils.copyProperties(project, s);
            ProjectContractApply contract = contracts.stream().filter(c -> CommonConstant.FLAG_YES.equals(c.getYn()) && project.getId().equals(c.getApplyId())).findAny().orElse(null);
            if (contract != null) {
                ProjectContractApplyChange change = changes.stream().filter(c -> CommonConstant.FLAG_YES.equals(c.getYn()) && contract.getId().equals(c.getOriginContractId())).findAny().orElse(null);
                ProjectContractInterimReport report = reports.stream().filter(c -> CommonConstant.FLAG_YES.equals(c.getYn()) && contract.getId().equals(c.getContractId())).findAny().orElse(null);
                ProjectContractAccept accept = accepts.stream().filter(c -> CommonConstant.FLAG_YES.equals(c.getYn()) && contract.getId().equals(c.getContractId())).findAny().orElse(null);
                s.setProjectContractApply(contract);
                s.setProjectContractApplyChange(change);
                s.setProjectContractInterimReport(report);
                s.setProjectContractAccept(accept);
            }
            s.setStoreProject(s.getProjectContractApply() == null ? "是" : "否");
            s.setApplyTimes(s.getProjectApplyInfo().getApplyCount());
            String status = "";
            if (FlowStatusEnum.WAIT_APPLY.getCode().equals(s.getProjectApplyInfo().getFlowStatus())) {
                status = "储备";
            } else if (!FlowStatusEnum.END.getCode().equals(s.getProjectApplyInfo().getFlowStatus())) {
                status = "申报中";
            } else if (s.getProjectContractAccept() != null
                    && FlowStatusEnum.END.getCode().equals(s.getProjectContractAccept().getFlowStatus())) {
                status = "已验收";
            } else if (s.getProjectContractApplyChange() != null
                    && "中止变更".equals(s.getProjectContractApplyChange().getChangeType())
                    && FlowStatusEnum.END.getCode().equals(s.getProjectContractApplyChange().getFlowStatus())) {
                status = "已终止";
            } else if (s.getProjectContractApply() != null && s.getProjectContractApply().getEndDate() != null
                    && s.getProjectContractApply().getEndDate().getTime() < System.currentTimeMillis()) {
                status = "在研（延期）";
            } else {
                status = "在研（正常）";
            }
            s.setProjectStatus(status);
            statisticsList.add(s);
        });

        List<ProjectInfoStatistics> resList = statisticsList.stream().filter(s -> {
            boolean pass = true;
            if (StringUtils.equals("是", statistics.getStoreProject())) {
                pass = s.getProjectContractApply() == null;
            } else if (StringUtils.equals("否", statistics.getStoreProject())) {
                pass = s.getProjectContractApply() != null;
            }

            if (pass && StringUtils.isNotBlank(statistics.getProjectStatus())) {
                boolean proStatusRes = false;
                if (statistics.getProjectStatus().contains("储备")) {
                    proStatusRes = proStatusRes || FlowStatusEnum.WAIT_APPLY.getCode().equals(s.getProjectApplyInfo().getFlowStatus());
                }
                if (statistics.getProjectStatus().contains("申报中")) {
                    proStatusRes = proStatusRes ||
                            (!FlowStatusEnum.WAIT_APPLY.getCode().equals(s.getProjectApplyInfo().getFlowStatus())
                                    && !FlowStatusEnum.END.getCode().equals(s.getProjectApplyInfo().getFlowStatus()));
                }
                if (statistics.getProjectStatus().contains("正常")) {
                    proStatusRes = proStatusRes ||
                            (s.getProjectContractApply() != null
                                    && s.getProjectContractApply().getEndDate().getTime() >= System.currentTimeMillis());
                }
                if (statistics.getProjectStatus().contains("延期")) {
                    proStatusRes = proStatusRes ||
                            (s.getProjectContractApply() != null
                                    && s.getProjectContractApply().getEndDate().getTime() < System.currentTimeMillis());
                }
                if (statistics.getProjectStatus().contains("已验收")) {
                    proStatusRes = proStatusRes ||
                            (s.getProjectContractAccept() != null
                                    && FlowStatusEnum.END.getCode().equals(s.getProjectContractAccept().getFlowStatus()));
                }
                if (statistics.getProjectStatus().contains("已结题")) {

                }
                if (statistics.getProjectStatus().contains("已终止")) {
                    proStatusRes = proStatusRes ||
                            (s.getProjectContractApplyChange() != null
                                    && "中止变更".equals(s.getProjectContractApplyChange().getChangeType())
                                    && FlowStatusEnum.END.getCode().equals(s.getProjectContractApplyChange().getFlowStatus()));
                }
                pass = pass && proStatusRes;
            }
            return pass;
        }).collect(Collectors.toList());
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(statistics.getPageNum());
        pageInfo.setPageSize(statistics.getPageSize());
        pageInfo.setList(resList);
        return pageInfo;
    }

    private Criteria getCriteria(ProjectApplyInfoVo vo, Example example) {
        Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        if (StringUtils.isNotBlank(vo.getProjectName())) {
            criteria.andLike("projectName", "%" + vo.getProjectName() + "%");
        }
        if (StringUtils.isNotBlank(vo.getProCode())) {
            criteria.andEqualTo("proCode", vo.getProCode());
        }
        if (StringUtils.isNotBlank(vo.getProjectTypeCode())) {
            criteria.andEqualTo("projectTypeCode", vo.getProjectTypeCode());
        }
        if (StringUtils.isNotBlank(vo.getProjectSecondTypeCode())) {
            criteria.andEqualTo("projectSecondTypeCode", vo.getProjectSecondTypeCode());
        }
        if (null != vo.getSubmitDateStart()) {
            criteria.andGreaterThanOrEqualTo("submitDate", vo.getSubmitDateStart());
        }
        if (null != vo.getSubmitDateEnd()) {
            criteria.andLessThanOrEqualTo("submitDate", new Date(vo.getSubmitDateEnd().getTime() + 24 * 60 * 60 * 1000 - 1));
        }
        if (StringUtils.isNotBlank(vo.getApplyUnitName())) {
            criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName() + "%");
        }
        if (StringUtils.isNotBlank(vo.getRecommendName())) {
            criteria.andLike("recommendName", "%" + vo.getRecommendName() + "%");
        }
        if (StringUtils.isNotBlank(vo.getLeaderName())) {
            criteria.andLike("leaderName", "%" + vo.getLeaderName() + "%");
        }
        if (StringUtils.isNotBlank(vo.getExpertsText())) {
            criteria.andLike("expertsText", "%" + vo.getExpertsText() + "%");
        }
        if (StringUtils.isNotBlank(vo.getProjectFieldSecondCode())) {
            criteria.andEqualTo("projectFieldSecondCode", vo.getProjectFieldSecondCode());
        }
        if (StringUtils.isNotBlank(vo.getProjectFieldThirdCode())) {
            criteria.andEqualTo("projectFieldThirdCode", vo.getProjectFieldThirdCode());
        }

        if (StringUtils.isNotBlank(vo.getApplyUnitName())) {
            criteria.andLike("applyUnitName", "%" + vo.getApplyUnitName() + "%");
        }

        if (StringUtils.isNotBlank(vo.getActNodeKey())) {
            if ("principalTask".equals(vo.getActNodeKey())){
                // 查询actNodeKey=principalTask 或者 actNodeKey是null 或者是空的数据
                criteria.andCondition("(act_node_key = 'principalTask' OR act_node_key IS NULL OR act_node_key = '')");
            } else {
                criteria.andLike("actNodeKey", "%" + vo.getActNodeKey() + "%");
            }
        }

        return criteria;
    }

    /**
     * 审核操作 （流程）
     *
     * @param applyVo         审核操作 参数
     * @param currentUserName 当前用户名
     * @param currentRealName 当前用户姓名
     */
    @Override
    @Transactional(readOnly = false)
    public void applyAudit(ProjectApplyInfoVo applyVo, String currentUserName, String currentRealName) {
        validateApplyBean(applyVo);
        try {
            ProcessNode bean = SpringUtils.getBean(FlowableConstant.TECK_APPLY_FLOW + applyVo.getActNodeKey(), ProcessNode.class);
            FlowNodeVo next = bean.next(applyVo);
        } catch (NoSuchBeanDefinitionException e) {
            try {
                ProcessNode bean = SpringUtils.getBean(FlowableConstant.TECK_APPLY_FLOW + "task", ProcessNode.class);
                FlowNodeVo next = bean.next(applyVo);
            } catch (NoSuchBeanDefinitionException ex) {
                e.printStackTrace();
                logger.error("获取不到当前节点:{},流程ID：{}，流程错误：{}", applyVo.getActNodeKey(), applyVo.getId(), e.getMessage());
                throw new ServiceException("处理失败");
            }
        }
    }

    /**
     * 项目申报代办列表查询
     *
     * @param vo
     * @return
     */
    @Override
    public List<ProjectApplyInfoVo> findTodoList(ProjectApplyInfoVo vo) {
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        String userAreaCode = vo.getUserAreaCode();
        if (!isAdmin() && StringUtils.isNotEmpty(userAreaCode)) {
            if (userAreaCode.endsWith("0000")) {
                // 省级
                userAreaCode = userAreaCode.substring(0, 2);
            } else if (userAreaCode.endsWith("00")) {
                // 市级
                userAreaCode = userAreaCode.substring(0, 4);
            }
            vo.setUserAreaCode(userAreaCode);
        } else {
            vo.setUserAreaCode(null);
        }

        //单位审核去除areaCode限制，仅根据uscc匹配
        if (ProjectDeclarationFlowConstants.ORG_TASK_CODE.equals(vo.getActNodeKey())) {
            vo.setUserAreaCode(null);
        }

        if (ProjectDeclarationFlowConstants.CHOOSE_EXPERT_TASK_CODE.equals(vo.getActNodeKey())) {
            vo.setExtractIdentification(ProjectDeclarationFlowConstants.ORG_TASK_CODE);
        }
        return projectApplyInfoMapper.findTodoList(vo);
    }

    /**
     * 项目负责人查询储备项目信息
     *
     * @param projectApplyInfoVo
     * @return
     */
    @Override
    public List<ProjectApplyInfo> findSelfReservePro(ProjectApplyInfoVo projectApplyInfoVo) {
        PageHelper.startPage(projectApplyInfoVo.getPageNum(), projectApplyInfoVo.getPageSize());
        Example example = new Example(ProjectApplyInfo.class);
        Criteria criteria = getCriteria(projectApplyInfoVo, example);
        //获取用户角色，判断是否包含管理员
        boolean isAdmin = getUserRoleList().stream()
                .anyMatch(role -> role.getRoleCode().equals(CommonConstant.SUPER_ADMIN_ROLE));
        if (!isAdmin) {
            criteria.andEqualTo("userId", projectApplyInfoVo.getUserId());
        }
        criteria.andCondition("(mark != 1 OR mark IS null)");

//        // mark = 1时表示查询历史数据
//        if (projectApplyInfoVo.getMark() == null) {
//            criteria.andCondition("(mark != 1 OR mark IS null)");
//        } else {
//            criteria.andEqualTo("mark", 1);
//        }
        example.orderBy("createTime").desc();
        List<ProjectApplyInfo> projectApplyInfoList = projectApplyInfoMapper.selectByExample(example);

        return projectApplyInfoList;
    }

    /**
     * 导出储备项目信息
     *
     * @param projectApplyInfoVo
     * @param response
     */
    @Override
    public void exportSelfReservePro(ProjectApplyInfoVo projectApplyInfoVo, HttpServletResponse response) {
        // 设置不分页，获取所有数据
        projectApplyInfoVo.setPageNum(1);
        projectApplyInfoVo.setPageSize(Integer.MAX_VALUE);

        // 调用findSelfReservePro方法获取数据
        List<ProjectApplyInfo> list = findSelfReservePro(projectApplyInfoVo);

        // 转换为VO对象列表，并转换为List<Object>
        List<Object> exportList = list.stream().map(p -> {
            ProjectApplyInfoVo vo = new ProjectApplyInfoVo();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList());

        // 使用Excel模板导出
        String model = "word/储备项目导出.xlsx";
        ClassPathResource classPathResource = new ClassPathResource(model);
        InputStream inputStream = null;
        try {
            inputStream = classPathResource.getInputStream();
        } catch (IOException e) {
            throw new ServiceException("加载文件失败");
        }
        EasyExcelUtils.exportTemplateFill(null, exportList, "Sheet1", "储备项目.xlsx", response, inputStream, true);
    }

    @Override
    public List<ProjectApplyInfoVo> todoNew(ProjectApplyInfoVo vo) {
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());

        //获取用户角色代码
        List<String> userRoleCodes = getUserRoleList().stream().map(sysRole -> sysRole.getRoleCode()).collect(Collectors.toList());

        // 定义角色-任务映射
        Map<String, String> roleTaskMap = new HashMap<>();
        roleTaskMap.put(ProjectDeclarationFlowConstants.ORG_TASK_ROLE, ProjectDeclarationFlowConstants.ORG_TASK_CODE);
        roleTaskMap.put(ProjectDeclarationFlowConstants.COUNTY_CHECK_TASK_ROLE, ProjectDeclarationFlowConstants.COUNTY_CHECK_TASK_CODE);
        roleTaskMap.put(ProjectDeclarationFlowConstants.COUNTY_APPROVAL_TASK_ROLE, ProjectDeclarationFlowConstants.COUNTY_APPROVAL_TASK_CODE);
        roleTaskMap.put(ProjectDeclarationFlowConstants.CITY_CHECK_TASK_ROLE, ProjectDeclarationFlowConstants.CITY_CHECK_TASK_CODE);
        roleTaskMap.put(ProjectDeclarationFlowConstants.CITY_APPROVAL_TASK_ROLE, ProjectDeclarationFlowConstants.CITY_APPROVAL_TASK_CODE);
        roleTaskMap.put(ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_ROLE, ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_CODE);
        roleTaskMap.put(ProjectDeclarationFlowConstants.DIRECTOR_AUDIT_TASK_ROLE, ProjectDeclarationFlowConstants.DIRECTOR_AUDIT_TASK_CODE);
        roleTaskMap.put(ProjectDeclarationFlowConstants.PROVINCE_APPROVAL_TASK_ROLE, ProjectDeclarationFlowConstants.PROVINCE_APPROVAL_TASK_CODE);

        // 过滤并添加对应的任务节点代码
        List<String> proNodeList = userRoleCodes.stream()
                .filter(roleTaskMap::containsKey)
                .map(roleTaskMap::get)
                .collect(Collectors.toList());

        //  特殊处理：如果用户有省局经办人角色，额外添加chooseExpertTask节点
        if (userRoleCodes.contains(ProjectDeclarationFlowConstants.PROVINCE_CHECK_TASK_ROLE)) {
            // 避免重复添加（如果已存在则不重复加）
            if (!proNodeList.contains(ProjectDeclarationFlowConstants.CHOOSE_EXPERT_TASK_CODE)) {
                proNodeList.add(ProjectDeclarationFlowConstants.CHOOSE_EXPERT_TASK_CODE);
            }
        }

        String userAreaCode = vo.getUserAreaCode();
        if (!isAdmin() && StringUtils.isNotEmpty(userAreaCode)) {
            if (userAreaCode.endsWith("0000")) {
                // 省级
                userAreaCode = userAreaCode.substring(0, 2);
            } else if (userAreaCode.endsWith("00")) {
                // 市级
                userAreaCode = userAreaCode.substring(0, 4);
            }
            vo.setUserAreaCode(userAreaCode);
        } else {
            vo.setUserAreaCode(null);
        }

        if (proNodeList.isEmpty()) {
            return Collections.emptyList();
        }
        List<ProjectApplyInfoVo> projectApplyInfoVoList = projectApplyInfoMapper.todoNew(vo, proNodeList);
        return projectApplyInfoVoList;
    }

    /**
     * 待专家审核数据查询
     *
     * @param applyVo
     * @return
     */
    @Override
    public List<ProjectApplyInfoVo> awaitingExpertReview(ProjectApplyInfoVo applyVo) {
        PageHelper.startPage(applyVo.getPageNum(), applyVo.getPageSize());
        List<ProjectApplyInfoVo> projectApplyInfoVoList = projectApplyInfoMapper.awaitingExpertReview(applyVo);
        return projectApplyInfoVoList;
    }

    /**
     * 验证申请对象
     *
     * @param applyVo 申请Vo
     * @return 结果
     */
    private ProjectApplyInfo validateApplyBean(ProjectApplyInfoVo applyVo) {
        AssertUtil.notNull(applyVo.getId());
        AssertUtil.notNull(applyVo.getActNodeKey());
        // 获取申请信息
        ProjectApplyInfo apply = mapper.selectByPrimaryKey(applyVo.getId());
        AssertUtil.notNull(apply, "流程不存在");
        // 判断流程是否已审核
        if (!applyVo.getActNodeKey().equals(apply.getActNodeKey())) {
            throw new ServiceException("流程已审核");
        }
        return apply;
    }

    @Override
    @Transactional(readOnly = false)
    public String saveHistoryProjectApplyInfo(ProjectApplyInfoVo projectApplyInfoVo) {
        if (projectApplyInfoVo == null) {
            throw new ServiceException("数据异常");
        }

        projectApplyInfoVo.setMark(CommonConstant.FLAG_YES);
        if (StringUtils.isEmpty(projectApplyInfoVo.getId())) {
            //新增
            projectApplyInfoVo.setId(UUIDUtils.getUUID());
            projectApplyInfoVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
            projectApplyInfoVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
            projectApplyInfoMapper.insertSelective(projectApplyInfoVo);
        } else {
            //避免页面传入修改
            projectApplyInfoVo.setYn(null);
            projectApplyInfoMapper.updateByPrimaryKeySelective(projectApplyInfoVo);
        }

        // 项目团队
        if (projectApplyInfoVo.getTeamsList() != null) {
            int index = 1;
            for (ProjectApplyTeams t : projectApplyInfoVo.getTeamsList()) {
                t.setSort(index++);
            }
            updateTeamsList(projectApplyInfoVo);
        }

        // 附件
        if (projectApplyInfoVo.getFiles() != null) {
            updateFileAppendixList(projectApplyInfoVo);
        }

        projectApplyInfoMapper.updateByPrimaryKeySelective(projectApplyInfoVo);

        return projectApplyInfoVo.getId();
    }

    /**
     * 专家评审过的项目列表
     *
     * @param vo
     * @return
     */
    @Override
    public List<ProjectApplyInfoVo> expertReviewedList(ProjectApplyInfoVo vo) {
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<ProjectApplyInfoVo> voList = projectApplyInfoMapper.expertReviewedList(vo);
        return voList;
    }

    /**
     * 历史申报项目查询
     *
     * @param vo
     * @return
     */
    @Override
    public List<ProjectApplyInfoVo> findHistoryDeclarationPro(ProjectApplyInfoVo vo) {
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<ProjectApplyInfoVo> voList = projectApplyInfoMapper.findHistoryDeclarationPro(vo);
        return voList;
    }

    /**
     * 统计历史项目提交数量
     * @param vo
     * @return
     */
    @Override
    public int countCommitNumber(ProjectApplyInfoVo vo) {
        int count = 0;
        if (vo.getIsCommit() != null && (vo.getIsCommit() == 0 || vo.getIsCommit() == 2 )){
            count  = 0;
        } else {
            count = projectApplyInfoMapper.countCommitNumber(vo);
        }

        return count;
    }
}

