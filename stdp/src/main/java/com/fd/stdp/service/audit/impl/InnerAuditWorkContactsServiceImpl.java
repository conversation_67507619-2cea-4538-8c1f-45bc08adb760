package com.fd.stdp.service.audit.impl;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.fd.stdp.beans.audit.InnerAuditEquippedInformation;
import com.fd.stdp.beans.audit.InnerAuditInformationCollection;
import com.fd.stdp.beans.audit.InnerAuditOrg;
import com.fd.stdp.beans.audit.InnerAuditWorkContacts;
import com.fd.stdp.beans.audit.vo.InnerAuditEquippedInformationExportVo;
import com.fd.stdp.beans.audit.vo.InnerAuditEquippedInformationVo;
import com.fd.stdp.beans.audit.vo.InnerAuditWorkContactsExportVo;
import com.fd.stdp.beans.audit.vo.InnerAuditWorkContactsVo;
import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.constant.InnerAuditBasicConstant;
import com.fd.stdp.dao.audit.InnerAuditOrgMapper;
import com.fd.stdp.dao.basic.BasicScienceOrgMapper;
import com.fd.stdp.service.user.SysUserUtilService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.audit.InnerAuditWorkContactsMapper;
import com.fd.stdp.service.audit.InnerAuditWorkContactsService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import static com.fd.stdp.common.BaseController.getCurrentOrgName;
import static com.fd.stdp.common.BaseController.getLoginUser;
import static com.fd.stdp.service.audit.impl.InnerAuditUtil.FANGYUAN;
import static com.fd.stdp.util.AppUserUtil.getCurrentRealName;
import static com.fd.stdp.util.AppUserUtil.getCurrentUserName;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 省市场监管局直属单位内审工作联系表
 *@Author: sef
 *@Date: 2022-06-06 13:54:35
 */
public class InnerAuditWorkContactsServiceImpl extends BaseServiceImpl<InnerAuditWorkContactsMapper, InnerAuditWorkContacts> implements InnerAuditWorkContactsService {

    public static final Logger logger = LoggerFactory.getLogger(InnerAuditWorkContactsServiceImpl.class);

    @Autowired
    private InnerAuditWorkContactsMapper innerAuditWorkContactsMapper;
    @Autowired
    private FlowCommonService flowCommonService;
    @Autowired
    private BasicScienceOrgMapper basicScienceOrgMapper;

    @Autowired
    private SysUserUtilService sysUserUtilService;

    @Autowired
    private InnerAuditOrgMapper innerAuditOrgMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新省市场监管局直属单位内审工作联系表
     *@param innerAuditWorkContacts 省市场监管局直属单位内审工作联系表对象
     *@return String 省市场监管局直属单位内审工作联系表ID
     *@Author: sef
     */
    public String saveOrUpdateInnerAuditWorkContacts(InnerAuditWorkContactsVo vo) {
        if (vo == null) {
            throw new ServiceException("数据异常");
        }
        setUnitUscc(vo);
        if (StringUtils.isEmpty(vo.getId())) {
            //新增
            vo.setId(UUIDUtils.getUUID());
            vo.setFillState(InnerAuditBasicConstant.FILL_STATE_ESCALATED);
            innerAuditWorkContactsMapper.insertSelective(vo);
        } else {
            //避免页面传入修改
            vo.setYn(null);
            innerAuditWorkContactsMapper.updateByPrimaryKeySelective(vo);
        }
        return vo.getId();
    }

    /**
     * 设置社会统一信用代码
     *
     * @param vo
     */

    private void setUnitUscc(InnerAuditWorkContactsVo vo) {
        // 补充社会统一信用代码
        Example example = new Example(BasicScienceOrg.class);
        example.createCriteria().andEqualTo("orgName", vo.getUnitName());
        List<BasicScienceOrg> list = basicScienceOrgMapper.selectByExample(example);
        if (list.size() > 0) {
            vo.setUnitUscc(list.get(0).getOrgUscc());
        }
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除省市场监管局直属单位内审工作联系表
     *@param id void 省市场监管局直属单位内审工作联系表ID
     *@Author: sef
     */
    public void deleteInnerAuditWorkContacts(String id) {
        InnerAuditWorkContacts innerAuditWorkContacts = innerAuditWorkContactsMapper.selectByPrimaryKey(id);
        if (innerAuditWorkContacts == null) {
            throw new ServiceException("非法请求");
        }
        //逻辑删除
        InnerAuditWorkContacts teminnerAuditWorkContacts = new InnerAuditWorkContacts();
        teminnerAuditWorkContacts.setYn(CommonConstant.FLAG_NO);
        teminnerAuditWorkContacts.setId(innerAuditWorkContacts.getId());
        innerAuditWorkContactsMapper.updateByPrimaryKeySelective(teminnerAuditWorkContacts);
    }

    /**
     * @param ids
     * @Description: 批量删除省市场监管局直属单位内审工作联系表
     */
    @Override
    @Transactional(readOnly = false)
    public void deleteMultiInnerAuditWorkContacts(List<String> ids) {
        ids.stream().forEach(id -> this.deleteInnerAuditWorkContacts(id));
    }

    @Override
    /**
     *@Description: 查询省市场监管局直属单位内审工作联系表详情
     *@param id
     *@return InnerAuditWorkContacts
     *@Author: sef
     */
    public InnerAuditWorkContacts findById(String id) {
        return innerAuditWorkContactsMapper.selectByPrimaryKeyAndYn(id);
    }

    @Override
    /**
     *@Description: 分页查询省市场监管局直属单位内审工作联系表
     *@param innerAuditWorkContactsVo
     *@return PageInfo<InnerAuditWorkContacts>
     *@Author: sef
     */
    public PageInfo<InnerAuditWorkContacts> findPageByQuery(InnerAuditWorkContactsVo innerAuditWorkContactsVo) {
        Example example = new Example(InnerAuditWorkContacts.class);
        example.setOrderByClause(" ORDER_VAL");
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);

        boolean hasUnit = false;
        if (StringUtils.isEmpty(innerAuditWorkContactsVo.getUnitName())) {
            InnerAuditUtil.OrgTypeEnum orgTypeEnum = getFillType(getCurrentOrgName());
            switch (orgTypeEnum) {
                case PROVINCE:
                    break;
                default:
                    hasUnit = true;
                    criteria.andEqualTo("unitName", getCurrentOrgName());
            }
        } else {
            criteria.andLike("unitName", '%' + innerAuditWorkContactsVo.getUnitName() + '%');
        }

        //查询条件
        if (!StringUtils.isEmpty(innerAuditWorkContactsVo.getPersonnelRoles())) {
            criteria.andLike("personnelRoles", '%' + innerAuditWorkContactsVo.getPersonnelRoles() + '%');
        }
        if (!StringUtils.isEmpty(innerAuditWorkContactsVo.getPersonnelNames())) {
            criteria.andLike("personnelNames", '%' + innerAuditWorkContactsVo.getPersonnelNames() + '%');
        }
        if (!StringUtils.isEmpty(innerAuditWorkContactsVo.getDepartmentsPositions())) {
            criteria.andLike("departmentsPositions", '%' + innerAuditWorkContactsVo.getDepartmentsPositions() + '%');
        }
        if (!StringUtils.isEmpty(innerAuditWorkContactsVo.getTel())) {
            criteria.andLike("tel", '%' + innerAuditWorkContactsVo.getTel() + '%');
        }
        if (!StringUtils.isEmpty(innerAuditWorkContactsVo.getPhone())) {
            criteria.andLike("phone", '%' + innerAuditWorkContactsVo.getPhone() + '%');
        }
        if (!ObjectUtils.isEmpty(innerAuditWorkContactsVo.getFillState())) {
            criteria.andEqualTo("fillState", innerAuditWorkContactsVo.getFillState());
        }
        if (!ObjectUtils.isEmpty(innerAuditWorkContactsVo.getUnitName()) && !hasUnit) {
            criteria.andLike("unitName", '%' + innerAuditWorkContactsVo.getUnitName() + '%');
        }
        List<InnerAuditWorkContacts> innerAuditWorkContactsList = innerAuditWorkContactsMapper.selectByExample(example);


        PageInfo pageInfo = new PageInfo(innerAuditWorkContactsList.isEmpty() ? Collections.emptyList() : innerAuditWorkContactsList);
        pageInfo.setList(innerAuditWorkContactsList.isEmpty() ? Collections.emptyList() : startPage(innerAuditWorkContactsList, innerAuditWorkContactsVo.getPageNum() == null ? 1 : innerAuditWorkContactsVo.getPageNum(), innerAuditWorkContactsVo.getPageSize() == null ? 10 : innerAuditWorkContactsVo.getPageSize()));
        pageInfo.setPageNum(innerAuditWorkContactsVo.getPageNum());
        pageInfo.setPageSize(innerAuditWorkContactsVo.getPageSize());
        return pageInfo;
    }


    @Override
    @Transactional(readOnly = false)
    public String submitInnerAuditWorkContacts(InnerAuditWorkContactsVo innerAuditWorkContactsVo) {
        List<InnerAuditWorkContactsVo> infomationList = innerAuditWorkContactsVo.getContactList();
        infomationList.stream().forEach(item -> item.setFillState(InnerAuditBasicConstant.FILL_STATE_PROVINCIAL_BUREAU_MODIFICATION));
        this.saveBatchInnerAuditWorkContacts(infomationList);
        return "success";
    }

    @Override
    @Transactional(readOnly = false)
    public String auditInnerAuditWorkContacts(InnerAuditWorkContactsVo innerAuditWorkContactsVo) {
        flowCommonService.doFlowStepAudit(innerAuditWorkContactsVo, this.mapper
                , StringUtils.isNotBlank(innerAuditWorkContactsVo.getAuditAdvice()) ? innerAuditWorkContactsVo.getAuditAdvice() : "省市场监管局直属单位内审工作联系表审核通过"
                , FlowStatusEnum.END.getCode());
        return innerAuditWorkContactsVo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    public String sendBackInnerAuditWorkContacts(InnerAuditWorkContactsVo innerAuditWorkContactsVo) {
        //innerAuditWorkContactsVo.setFillState(InnerAuditBasicConstant.FILL_STATE_PROVINCIAL_BUREAU_RETURN);
        if (StringUtils.isNotBlank(innerAuditWorkContactsVo.getUnitName())) {
            innerAuditWorkContactsMapper.sendBackInnerAuditWorkContacts(innerAuditWorkContactsVo.getUnitName());
            return "success";
        }
        return "缺少单位名称！";
    }

    @Override
    @Transactional(readOnly = false)
    public String releaseInnerAuditWorkContacts(InnerAuditWorkContactsVo innerAuditWorkContactsVo) {
        flowCommonService.doCompleteTask(innerAuditWorkContactsVo, this.mapper
                , "省市场监管局直属单位内审工作联系表任务书下达完成"
                , FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
        return null;
    }


    @Override
    public PageInfo<InnerAuditWorkContacts> todoList(InnerAuditWorkContactsVo vo) {

        Example example = new Example(InnerAuditWorkContacts.class);
        Criteria criteria = getCriteria(vo, example);
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
    }

    @Override
    public PageInfo<InnerAuditWorkContacts> finishedList(InnerAuditWorkContactsVo vo) {
        Example example = new Example(InnerAuditWorkContacts.class);
        Criteria criteria = getCriteria(vo, example);
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
    }

    @Override
    public PageInfo<InnerAuditWorkContacts> endList(InnerAuditWorkContactsVo vo) {
        Example example = new Example(InnerAuditWorkContacts.class);
        example.orderBy("createTime").desc();
        Criteria criteria = getCriteria(vo, example);
        return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
    }

    @Override
    @Transactional(readOnly = false)
    public void saveBatchInnerAuditWorkContacts(List<InnerAuditWorkContactsVo> innerAuditWorkContactsVoList) {
        if (CollectionUtils.isEmpty(innerAuditWorkContactsVoList)) {
            throw new ServiceException("请添加内容后重试");
        }
        // 已存在(单位)
        List<InnerAuditWorkContactsVo> collect = innerAuditWorkContactsVoList.stream().filter(item ->
                org.springframework.util.StringUtils.hasText(org.apache.commons.lang.ObjectUtils.toString(innerAuditWorkContactsMapper.findByUnitIsOrNotExist(item), ""))).collect(Collectors.toList());

        // 批量删除 逻辑删除
        if (!collect.isEmpty()) {
            innerAuditWorkContactsMapper.deleteByUnit(collect.get(0).getUnitName());
        }

        InnerAuditWorkContactsVo old = collect.get(0);
        innerAuditWorkContactsVoList.stream().forEach(item -> {
                    if (StringUtils.isBlank(item.getOrgName())) {
                        item.setOrgName(getCurrentOrgName());
                        item.setOrgCode(getLoginUser().getAreaCode());
                    }
                    item.setId(UUIDUtils.getUUID());
                    item.setFillState(old.getFillState() == null ? InnerAuditBasicConstant.FILL_STATE_ESCALATED : old.getFillState());
                    item.setFillTime(old.getFillTime() == null ? new Date() : old.getFillTime());
                    item.setUnitName(old.getUnitName() == null ? getCurrentOrgName() : old.getUnitName());
                    innerAuditWorkContactsMapper.insert(item);
                }
        );
    }

    private Criteria getCriteria(InnerAuditWorkContactsVo vo, Example example) {
        Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
        return criteria;
    }


    private Integer getOperType(String orgName) {
        LoginUser loginUser = getLoginUser();
        Integer operType = sysUserUtilService.getUserOperType(loginUser);
        Example example = new Example(InnerAuditOrg.class);
        example.createCriteria().andEqualTo("orgName", orgName);
        if (CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))) {
            return operType;
        }
        return 0;
    }

    private boolean getIsProOrg(String org) {
        Example example = new Example(InnerAuditOrg.class);
        example.createCriteria().andLike("orgName", "%" + org + "%");
        if (CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))) {
            return false;
        }
        return true;
    }

    // 0 省局 1 直属单位 2市局 3 县局
    private InnerAuditUtil.OrgTypeEnum getFillType(String org) {
        if (getIsProOrg(org)) {
            if (FANGYUAN.equals(org)) {
                return InnerAuditUtil.OrgTypeEnum.PROVINCE_GO_EM;
            }
            return InnerAuditUtil.OrgTypeEnum.PROVINCE_GO;
        }
        int orgType = getOperType(org);
        if (2 == orgType) {
            return InnerAuditUtil.OrgTypeEnum.PROVINCE;
        } else if (1 == orgType) {
            return InnerAuditUtil.OrgTypeEnum.CITY;
        }
        return InnerAuditUtil.OrgTypeEnum.COUNTRY;
    }

    @Override
    public PageInfo findList(InnerAuditWorkContactsVo vo) {
        InnerAuditUtil.OrgTypeEnum orgTypeEnum = getFillType(getCurrentOrgName());
        Boolean provinceFlag = new Boolean(false);
        switch (orgTypeEnum) {
            case PROVINCE:
                provinceFlag = true;
                break;
        }
        if (provinceFlag) {
            PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
            PageInfo pageInfo = new PageInfo(innerAuditWorkContactsMapper.getProvinceList(vo));
            return pageInfo;
        }

        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        PageInfo pageInfo = new PageInfo(innerAuditWorkContactsMapper.selectUnitName('%' + getCurrentOrgName() + '%'));
        return pageInfo;
    }

    @Override
    public List exportInnerAuditWorkContactsVo(InnerAuditWorkContactsVo vo) {
        List<InnerAuditWorkContacts> innerAudit = innerAuditWorkContactsMapper.selectUnitName('%' + getCurrentOrgName() + '%');
        List<InnerAuditWorkContactsExportVo> list = new ArrayList<>();
        AtomicInteger orderValCn = new AtomicInteger();
        innerAudit.stream().forEach(item -> {
            InnerAuditWorkContactsExportVo innerAuditVo = new InnerAuditWorkContactsExportVo();
            BeanUtils.copyProperties(item, innerAuditVo);
            innerAuditVo.setOrderVal(orderValCn.incrementAndGet());
            list.add(innerAuditVo);
        });
        return list;
    }

    @Override
    public List exportInnerAuditWorkContactsVoAllUnit(InnerAuditWorkContactsVo vo) {
        List<InnerAuditWorkContacts> innerAudit = innerAuditWorkContactsMapper.selectSubmit();
        List<InnerAuditWorkContactsExportVo> list = new ArrayList<>();
        // 多个单位导出一起时序号重编
        AtomicInteger orderValCn = new AtomicInteger();
        innerAudit.stream().forEach(item -> {
            InnerAuditWorkContactsExportVo innerAuditVo = new InnerAuditWorkContactsExportVo();
            BeanUtils.copyProperties(item, innerAuditVo);
            innerAuditVo.setOrderVal(orderValCn.incrementAndGet());
            list.add(innerAuditVo);
        });
        return list;
    }
}
