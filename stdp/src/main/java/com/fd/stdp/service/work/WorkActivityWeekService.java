package com.fd.stdp.service.work;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.work.WorkActivityWeek;
import com.fd.stdp.beans.work.vo.WorkActivityWeekVo;

/**
 * 科技活动周开展情况Service接口
 *
 * <AUTHOR>
 * @date 2021-11-12
 */
public interface WorkActivityWeekService {
    /**
     * @param workActivityWeek 科技活动周开展情况对象
     * @return String 科技活动周开展情况ID
     * @Description: 保存或更新科技活动周开展情况
     * @Author: yujianfei
     */
    String saveOrUpdateWorkActivityWeek(WorkActivityWeek workActivityWeek);

    /**
     * @param ids void 科技活动周开展情况ID
     * @Description: 删除科技活动周开展情况
     * @Author: yujianfei
     */
    void deleteWorkActivityWeek(List<String> ids);

    /**
     * @param id
     * @return WorkActivityWeek
     * @Description: 查询科技活动周开展情况详情
     * @Author: yujianfei
     */
    WorkActivityWeek findById(String id);

    /**
     * @param workActivityWeekVo
     * @return PageInfo<WorkActivityWeek>
     * @Description: 分页查询科技活动周开展情况
     * @Author: yujianfei
     */
    PageInfo<WorkActivityWeek> findPageByQuery(WorkActivityWeekVo workActivityWeekVo);

    PageInfo<WorkActivityWeekVo> findStatisticByQuery(WorkActivityWeekVo workActivityWeekVo);
}
