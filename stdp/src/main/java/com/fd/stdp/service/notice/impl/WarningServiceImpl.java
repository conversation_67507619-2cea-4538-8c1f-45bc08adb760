package com.fd.stdp.service.notice.impl;

import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.innovation.InnovationLaboratoryContract;
import com.fd.stdp.beans.innovation.InnovationQualityContract;
import com.fd.stdp.beans.innovation.InnovationQualityReview;
import com.fd.stdp.beans.notice.WarningBean;
import com.fd.stdp.beans.project.ProjectContractAccept;
import com.fd.stdp.beans.project.ProjectContractApply;
import com.fd.stdp.beans.project.ProjectContractInterimReport;
import com.fd.stdp.beans.talent.*;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.dao.basic.BasicScienceOrgMapper;
import com.fd.stdp.dao.innovation.InnovationLaboratoryContractMapper;
import com.fd.stdp.dao.innovation.InnovationQualityContractMapper;
import com.fd.stdp.dao.innovation.InnovationQualityReviewMapper;
import com.fd.stdp.dao.project.ProjectApplyInfoMapper;
import com.fd.stdp.dao.project.ProjectContractAcceptMapper;
import com.fd.stdp.dao.project.ProjectContractApplyMapper;
import com.fd.stdp.dao.project.ProjectContractInterimReportMapper;
import com.fd.stdp.dao.talent.*;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.notice.WarningService;
import com.fd.stdp.service.project.ProjectApplyInfoService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/3/21 17:56
 */

@Service
@Transactional(readOnly = true)
public class WarningServiceImpl implements WarningService {

    @Autowired
    private ProjectApplyInfoMapper applyInfoMapper;
    @Autowired
    private ProjectContractApplyMapper contractApplyMapper;
    @Autowired
    private ProjectContractAcceptMapper acceptMapper;
    @Autowired
    private ProjectContractInterimReportMapper interimReportMapper;

    @Autowired
    private InnovationLaboratoryContractMapper innovationLaboratoryContractMapper;
    @Autowired
    private InnovationQualityContractMapper innovationQualityContractMapper;
    @Autowired
    private InnovationQualityReviewMapper innovationQualityReviewMapper;
    @Autowired
    private TalentLeaderSubjectContractMapper talentLeaderSubjectContractMapper;
    @Autowired
    private TalentLeaderSubjectExamineMapper talentLeaderSubjectExamineMapper;

    @Autowired
    private TalentTeamContributeMapper talentTeamContributeMapper;
    @Autowired
    private TalentTeamExamineMapper talentTeamExamineMapper;

    @Autowired
    private BasicScienceOrgMapper basicScienceOrgMapper;

    @Override
    public PageInfo<WarningBean> findProjectWarning(WarningBean warning) {
        List<WarningBean> resultList = new ArrayList<>();
        Example example = new Example(ProjectContractApply.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.WAIT_APPLY.getCode());
        contractApplyMapper.selectByExample(example).forEach(contract->{
            WarningBean wb = new WarningBean(contract);
            wb.setProjectNumber(contract.getProjectNumber());
            wb.setProjectName(contract.getProjectName());
            wb.setApplyUnitName(contract.getApplyUnitName());
            wb.setArea(contract.getApplyUnitArea());
            wb.setStartDate(contract.getStratDate());
            wb.setEndDate(contract.getEndDate());
            wb.setStatus("任务书待办理");
            resultList.add(wb);
        });

        example = new Example(ProjectContractInterimReport.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
                .andLessThanOrEqualTo("endDate", new Date(System.currentTimeMillis() + 30*24*3600*1000));
        interimReportMapper.selectByExample(example).forEach(interimReport->{
            WarningBean wb = new WarningBean(interimReport);
            wb.setProjectNumber(interimReport.getProjectNumber());
            wb.setProjectName(interimReport.getProjectName());
            wb.setApplyUnitName(interimReport.getApplyUnitName());
            wb.setArea(interimReport.getContractId());
            wb.setStartDate(interimReport.getStratDate());
            wb.setEndDate(interimReport.getEndDate());
            wb.setStatus("中期检查（到期前1个月）");
            try {
                ProjectContractApply contractApply = new ProjectContractApply();
                contractApply.setId(interimReport.getContractId());
                wb.setArea(contractApplyMapper.selectOne(contractApply).getApplyUnitArea());
            } catch (Exception e){

            }
            resultList.add(wb);
        });

        example = new Example(ProjectContractAccept.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
                .andLessThanOrEqualTo("endDate", new Date(System.currentTimeMillis() + 30*24*3600*1000));
        acceptMapper.selectByExample(example).forEach(accept->{
            WarningBean wb = new WarningBean(accept);
            wb.setProjectNumber(accept.getProjectNumber());
            wb.setProjectName(accept.getProjectName());
            wb.setApplyUnitName(accept.getApplyUnitName());
            // wb.setArea(accept.());
            wb.setStartDate(accept.getStratDate());
            wb.setEndDate(accept.getEndDate());
            wb.setStatus("项目验收（到期前1个月）");

            try {
                ProjectContractApply contractApply = new ProjectContractApply();
                contractApply.setId(accept.getContractId());
                wb.setArea(contractApplyMapper.selectOne(contractApply).getApplyUnitArea());
            } catch (Exception e){

            }
            resultList.add(wb);
        });
        return new PageInfo<WarningBean>(resultList);
    }

    @Override
    public PageInfo<WarningBean> findInnovationWarning(WarningBean warning) {
        List<WarningBean> resultList = new ArrayList<>();
        // 实验室
        Example example = new Example(InnovationLaboratoryContract.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.WAIT_APPLY.getCode())
                .andIsNull("formType");
        innovationLaboratoryContractMapper.selectByExample(example).forEach(contract->{
            WarningBean wb = new WarningBean(contract);
            wb.setName(contract.getLaboratory());
            wb.setOrgName(contract.getOrgName());
            wb.setStatus("任务书未提交");

            try {
                BasicScienceOrg basicScienceOrg = new BasicScienceOrg();
                basicScienceOrg.setOrgName(contract.getOrgName());
                basicScienceOrg.setYn(CommonConstant.FLAG_YES);
                wb.setArea(basicScienceOrgMapper.selectOne(basicScienceOrg).getAreaName());
            } catch (Exception e){

            }
            resultList.add(wb);
        });

        // 质检中心
        example = new Example(InnovationQualityContract.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.WAIT_APPLY.getCode());
        innovationQualityContractMapper.selectByExample(example).forEach(contract->{
            WarningBean wb = new WarningBean(contract);
            wb.setName(contract.getApplyCenterName());
            wb.setOrgName(contract.getOrgName());
            wb.setStatus("任务书未提交");

            try {
                BasicScienceOrg basicScienceOrg = new BasicScienceOrg();
                basicScienceOrg.setOrgName(contract.getOrgName());
                basicScienceOrg.setYn(CommonConstant.FLAG_YES);
                wb.setArea(basicScienceOrgMapper.selectOne(basicScienceOrg).getAreaName());
            } catch (Exception e){

            }
            resultList.add(wb);
        });

        // 实验室
        example = new Example(InnovationLaboratoryContract.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.WAIT_APPLY.getCode())
                .andEqualTo("formType", "laboratory_grade");
        innovationLaboratoryContractMapper.selectByExample(example).forEach(contract->{
            WarningBean wb = new WarningBean(contract);
            wb.setName(contract.getLaboratory());
            wb.setOrgName(contract.getOrgName());
            wb.setStatus("考核材料未提交");
            try {
                BasicScienceOrg basicScienceOrg = new BasicScienceOrg();
                basicScienceOrg.setOrgName(contract.getOrgName());
                basicScienceOrg.setYn(CommonConstant.FLAG_YES);
                wb.setArea(basicScienceOrgMapper.selectOne(basicScienceOrg).getAreaName());
            } catch (Exception e){

            }
            resultList.add(wb);
        });

        // 实验室
        example = new Example(InnovationLaboratoryContract.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.END.getCode())
                .andEqualTo("formType", "laboratory_grade");
        innovationLaboratoryContractMapper.selectByExample(example).forEach(contract->{
            WarningBean wb = new WarningBean(contract);
            wb.setName(contract.getLaboratory());
            wb.setOrgName(contract.getOrgName());
            wb.setStatus("考核结果接收");
            try {
                BasicScienceOrg basicScienceOrg = new BasicScienceOrg();
                basicScienceOrg.setOrgName(contract.getOrgName());
                basicScienceOrg.setYn(CommonConstant.FLAG_YES);
                wb.setArea(basicScienceOrgMapper.selectOne(basicScienceOrg).getAreaName());
            } catch (Exception e){

            }
            resultList.add(wb);
        });

        // 质检中心
        example = new Example(InnovationQualityReview.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.WAIT_APPLY.getCode());
        innovationQualityReviewMapper.selectByExample(example).forEach(contract->{
            WarningBean wb = new WarningBean(contract);
            wb.setName(contract.getApplyCenterName());
            wb.setOrgName(contract.getOrgName());
            wb.setStatus("考核材料未提交");
            try {
                BasicScienceOrg basicScienceOrg = new BasicScienceOrg();
                basicScienceOrg.setOrgName(contract.getOrgName());
                basicScienceOrg.setYn(CommonConstant.FLAG_YES);
                wb.setArea(basicScienceOrgMapper.selectOne(basicScienceOrg).getAreaName());
            } catch (Exception e){

            }
            resultList.add(wb);
        });
        // 质检中心
        example = new Example(InnovationQualityReview.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        innovationQualityReviewMapper.selectByExample(example).forEach(contract->{
            WarningBean wb = new WarningBean(contract);
            wb.setName(contract.getApplyCenterName());
            wb.setOrgName(contract.getOrgName());
            wb.setStatus("考核结果接收");
            try {
                BasicScienceOrg basicScienceOrg = new BasicScienceOrg();
                basicScienceOrg.setOrgName(contract.getOrgName());
                basicScienceOrg.setYn(CommonConstant.FLAG_YES);
                wb.setArea(basicScienceOrgMapper.selectOne(basicScienceOrg).getAreaName());
            } catch (Exception e){

            }
            resultList.add(wb);
        });
        return new PageInfo<WarningBean>(resultList);
    }

    @Override
    public PageInfo<WarningBean> findTalentWarning(WarningBean warning) {
        List<WarningBean> resultList = new ArrayList<>();
        Example example = new Example(TalentLeaderSubjectContract.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.WAIT_APPLY.getCode());
        talentLeaderSubjectContractMapper.selectByExample(example).forEach(contract->{
            WarningBean wb = new WarningBean(contract);
            wb.setName(contract.getName());
            wb.setOrgName(contract.getUnitName());
            wb.setStatus("任务书未提交");

            try {
                BasicScienceOrg basicScienceOrg = new BasicScienceOrg();
                basicScienceOrg.setOrgName(contract.getOrgName());
                basicScienceOrg.setYn(CommonConstant.FLAG_YES);
                wb.setArea(basicScienceOrgMapper.selectOne(basicScienceOrg).getAreaName());
            } catch (Exception e){

            }
            resultList.add(wb);
        });

        example = new Example(TalentLeaderSubjectExamine.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.WAIT_APPLY.getCode());
        talentLeaderSubjectExamineMapper.selectByExample(example).forEach(contract->{
            WarningBean wb = new WarningBean(contract);
            wb.setName(contract.getName());
            wb.setOrgName(contract.getUnitName());
            wb.setStatus("考核材料提交预警");

            try {
                BasicScienceOrg basicScienceOrg = new BasicScienceOrg();
                basicScienceOrg.setOrgName(contract.getOrgName());
                basicScienceOrg.setYn(CommonConstant.FLAG_YES);
                wb.setArea(basicScienceOrgMapper.selectOne(basicScienceOrg).getAreaName());
            } catch (Exception e){

            }
            resultList.add(wb);
        });

        example = new Example(TalentLeaderSubjectExamine.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        talentLeaderSubjectExamineMapper.selectByExample(example).forEach(contract->{
            WarningBean wb = new WarningBean(contract);
            wb.setName(contract.getName());
            wb.setOrgName(contract.getUnitName());
            wb.setStatus("考核结果接收预警");

            try {
                BasicScienceOrg basicScienceOrg = new BasicScienceOrg();
                basicScienceOrg.setOrgName(contract.getOrgName());
                basicScienceOrg.setYn(CommonConstant.FLAG_YES);
                wb.setArea(basicScienceOrgMapper.selectOne(basicScienceOrg).getAreaName());
            } catch (Exception e){

            }
            resultList.add(wb);
        });

        example = new Example(TalentTeamContribute.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.WAIT_APPLY.getCode());
        talentLeaderSubjectExamineMapper.selectByExample(example).forEach(contract->{
            WarningBean wb = new WarningBean(contract);
            wb.setName(contract.getName());
            wb.setOrgName(contract.getUnitName());
            wb.setStatus("任务书未提交");

            try {
                BasicScienceOrg basicScienceOrg = new BasicScienceOrg();
                basicScienceOrg.setOrgName(contract.getOrgName());
                basicScienceOrg.setYn(CommonConstant.FLAG_YES);
                wb.setArea(basicScienceOrgMapper.selectOne(basicScienceOrg).getAreaName());
            } catch (Exception e){

            }
            resultList.add(wb);
        });

        example = new Example(TalentTeamExamine.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.WAIT_APPLY.getCode());
        talentTeamExamineMapper.selectByExample(example).forEach(contract->{
            WarningBean wb = new WarningBean(contract);
            wb.setName(contract.getTeamName());
            wb.setOrgName(contract.getUnitName());
            wb.setStatus("考核材料提交预警");

            try {
                BasicScienceOrg basicScienceOrg = new BasicScienceOrg();
                basicScienceOrg.setOrgName(contract.getOrgName());
                basicScienceOrg.setYn(CommonConstant.FLAG_YES);
                wb.setArea(basicScienceOrgMapper.selectOne(basicScienceOrg).getAreaName());
            } catch (Exception e){

            }
            resultList.add(wb);
        });

        example = new Example(TalentTeamExamine.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        talentTeamExamineMapper.selectByExample(example).forEach(contract->{
            WarningBean wb = new WarningBean(contract);
            wb.setName(contract.getTeamName());
            wb.setOrgName(contract.getUnitName());
            wb.setStatus("考核结果接收预警");

            try {
                BasicScienceOrg basicScienceOrg = new BasicScienceOrg();
                basicScienceOrg.setOrgName(contract.getOrgName());
                basicScienceOrg.setYn(CommonConstant.FLAG_YES);
                wb.setArea(basicScienceOrgMapper.selectOne(basicScienceOrg).getAreaName());
            } catch (Exception e){

            }
            resultList.add(wb);
        });
        return new PageInfo<WarningBean>(resultList);
    }
}
