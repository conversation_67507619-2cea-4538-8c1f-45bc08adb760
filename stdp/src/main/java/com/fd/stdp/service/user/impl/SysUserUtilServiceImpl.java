package com.fd.stdp.service.user.impl;

import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.service.user.SysUserUtilService;
import com.fd.stdp.util.AppUserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/1/6 11:04
 */
@Service
public class SysUserUtilServiceImpl implements SysUserUtilService {
    /**
     * 获取用户的所属类型
     * 0 县 1 市 2 省 x 其它
     * @return
     */
    @Override
    public Integer getUserOperType(SysUser sysUser){
        return getOperTypeByAreaCode(sysUser.getAreaCode());
    }
    /**
     * 获取areaCode的所属类型
     * 0 县 1 市 2 省 x 其它
     * @return
     */
    @Override
    public Integer getOperTypeByAreaCode(String areaCode){
        if(StringUtils.isNotBlank(areaCode) && areaCode.length() == 6){
            if("0000".equals(areaCode.substring(2,6))){
                return 2;
            }
            if("00".equals(areaCode.substring(4,6))){
                return 1;
            }
            return 0;
        }
        return -1;
    }

    /**
     * 获取用户的所属类型
     * 0 县 1 市 2 省 x 其它
     * @return
     */
    @Override
    public Integer getUserOperType(){
        //return 2;
        return getUserOperType((SysUser) AppUserUtil.getLoginAppUser());
    }
}
