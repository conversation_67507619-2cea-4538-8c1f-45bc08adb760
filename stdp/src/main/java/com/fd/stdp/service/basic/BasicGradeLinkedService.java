package com.fd.stdp.service.basic;

import java.util.List;

import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicGradeLinked;
import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
/**
 *@Description: 评审意见关联表
 *@Author: wangsh
 *@Date: 2022-02-15 11:26:40
 */
public interface BasicGradeLinkedService {

	/**
	 *@Description: 保存或更新评审意见关联表
	 *@param basicGradeLinked 评审意见关联表对象
	 *@return String 评审意见关联表ID
	 *@Author: wangsh
	 */
	String saveOrUpdateBasicGradeLinked(BasicGradeLinkedVo basicGradeLinked);
	
	/**
	 *@Description: 删除评审意见关联表
	 *@param id void 评审意见关联表ID
	 *@Author: wangsh
	 */
	void deleteBasicGradeLinked(String id);

	/**
	 * @Description: 批量删除评审意见关联表
	 * @param ids
	 */
    void deleteMultiBasicGradeLinked(List<String> ids);

	/**
	 *@Description: 查询评审意见关联表详情
	 *@param id
	 *@return BasicGradeLinked
	 *@Author: wangsh
	 */
	BasicGradeLinked findById(String id);

	/**
	 *@Description: 分页查询评审意见关联表
	 *@param basicGradeLinkedVo
	 *@return PageInfo<BasicGradeLinked>
	 *@Author: wangsh
	 */
	PageInfo<BasicGradeLinked> findPageByQuery(BasicGradeLinkedVo basicGradeLinkedVo);
	
	
	/**
	 * 提交
	 * @param basicGradeLinkedVo
	 * @return
	 */
    String submitBasicGradeLinked(BasicGradeLinkedVo basicGradeLinkedVo);

	/**
	 * 审核
	 * @param basicGradeLinkedVo
	 * @return
	 */
	String auditBasicGradeLinked(BasicGradeLinkedVo basicGradeLinkedVo);

	/**
	 * 退回
	 * @param basicGradeLinkedVo
	 * @return
	 */
	String sendBackBasicGradeLinked(BasicGradeLinkedVo basicGradeLinkedVo);

	/**
	 * 任务书下达
	 * @param basicGradeLinkedVo
	 * @return
	 */
	String releaseBasicGradeLinked(BasicGradeLinkedVo basicGradeLinkedVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<BasicGradeLinked> todoList(BasicGradeLinkedVo basicGradeLinkedVo);

	/**
	 * 已办列表
	 */
	PageInfo<BasicGradeLinked> finishedList(BasicGradeLinkedVo basicGradeLinkedVo);

	/**
	 * 已完成列表
	 */
	PageInfo<BasicGradeLinked> endList(BasicGradeLinkedVo basicGradeLinkedVo);

	/**
	 * 获取表单对应的人员列表
	 * @param formId
	 * @return
	 */
	List<BasicGradeLinkedVo> findByFormId(String formId);

	/**
	 * 清除并更新表单对应的人员信息
	 * @param formId 表单id basicFileAppendixVoList人员信息
	 * @param list
	 */
	void clearAndUpdateBasicGradeLinked(String formId, List<BasicGradeLinkedVo> list);

	/**
	 * 获取表单评审是否全部完成
	 * @param formId
	 * @return
	 */
	Boolean formGradeAllFinished(String formId);
}
