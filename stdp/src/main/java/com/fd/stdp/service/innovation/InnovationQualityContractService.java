package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityContract;
import com.fd.stdp.beans.innovation.vo.InnovationQualityContractVo;
/**
 *@Description: 省质检中心筹建任务书
 *@Author: wangsh
 *@Date: 2022-02-11 13:56:23
 */
public interface InnovationQualityContractService {

	/**
	 *@Description: 保存或更新省质检中心筹建任务书
	 *@param innovationQualityContract 省质检中心筹建任务书对象
	 *@return String 省质检中心筹建任务书ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationQualityContract(InnovationQualityContractVo innovationQualityContract);
	
	/**
	 *@Description: 删除省质检中心筹建任务书
	 *@param id void 省质检中心筹建任务书ID
	 *@Author: wangsh
	 */
	void deleteInnovationQualityContract(String id);

	/**
	 * @Description: 批量删除省质检中心筹建任务书
	 * @param ids
	 */
    void deleteMultiInnovationQualityContract(List<String> ids);

	/**
	 *@Description: 查询省质检中心筹建任务书详情
	 *@param id
	 *@return InnovationQualityContract
	 *@Author: wangsh
	 */
	InnovationQualityContract findById(String id);

	/**
	 *@Description: 分页查询省质检中心筹建任务书
	 *@param innovationQualityContractVo
	 *@return PageInfo<InnovationQualityContract>
	 *@Author: wangsh
	 */
	PageInfo<InnovationQualityContract> findPageByQuery(InnovationQualityContractVo innovationQualityContractVo);
	
	
	/**
	 * 提交
	 * @param innovationQualityContractVo
	 * @return
	 */
    String submitInnovationQualityContract(InnovationQualityContractVo innovationQualityContractVo);

	/**
	 * 审核
	 * @param innovationQualityContractVo
	 * @return
	 */
	String auditInnovationQualityContract(InnovationQualityContractVo innovationQualityContractVo);

	/**
	 * 退回
	 * @param innovationQualityContractVo
	 * @return
	 */
	String sendBackInnovationQualityContract(InnovationQualityContractVo innovationQualityContractVo);

	/**
	 * 任务书下达
	 * @param innovationQualityContractVo
	 * @return
	 */
	String releaseInnovationQualityContract(InnovationQualityContractVo innovationQualityContractVo);

	/**
	 * 待办列表
	 */
	PageInfo<InnovationQualityContract> todoList(InnovationQualityContractVo innovationQualityContractVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationQualityContract> finishedList(InnovationQualityContractVo innovationQualityContractVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationQualityContract> endList(InnovationQualityContractVo innovationQualityContractVo);

	/**
	 * 可变更列表
	 * @param innovationQualityContractVo
	 * @return
	 */
	PageInfo<InnovationQualityContract> changeAbleList(InnovationQualityContractVo innovationQualityContractVo);

	/**
	 * 已验收列表
	 * @param innovationQualityContractVo
	 * @return
	 */
	PageInfo<InnovationQualityContract> acceptedList(InnovationQualityContractVo innovationQualityContractVo);
}
