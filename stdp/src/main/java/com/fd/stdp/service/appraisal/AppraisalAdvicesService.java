package com.fd.stdp.service.appraisal;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.appraisal.AppraisalAdvices;
import com.fd.stdp.beans.appraisal.vo.AppraisalAdvicesVo;

/**
 * 评价退回Service接口
 *
 * <AUTHOR>
 * @date 2021-11-19
 */
public interface AppraisalAdvicesService {
    /**
     * @param appraisalAdvices 评价退回对象
     * @return String 评价退回ID
     * @Description: 保存或更新评价退回
     * @Author: yujianfei
     */
    String saveOrUpdateAppraisalAdvices(AppraisalAdvices appraisalAdvices);

    /**
     * @param ids void 评价退回ID
     * @Description: 删除评价退回
     * @Author: yujianfei
     */
    void deleteAppraisalAdvices(List<String> ids);

    /**
     * @param id
     * @return AppraisalAdvices
     * @Description: 查询评价退回详情
     * @Author: yujianfei
     */
    AppraisalAdvices findById(String id);

    /**
     * @param appraisalAdvicesVo
     * @return PageInfo<AppraisalAdvices>
     * @Description: 分页查询评价退回
     * @Author: yujianfei
     */
    PageInfo<AppraisalAdvices> findPageByQuery(AppraisalAdvicesVo appraisalAdvicesVo);
}
