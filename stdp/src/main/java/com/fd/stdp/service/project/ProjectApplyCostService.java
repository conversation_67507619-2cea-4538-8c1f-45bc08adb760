package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectApplyCost;
import com.fd.stdp.beans.project.vo.ProjectApplyCostVo;

/**
 * 项目科研经费预算Service接口
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
public interface ProjectApplyCostService {
    /**
     * @param projectApplyCost 项目科研经费预算对象
     * @return String 项目科研经费预算ID
     * @Description: 保存或更新项目科研经费预算
     * @Author: yujianfei
     */
    String saveOrUpdateProjectApplyCost(ProjectApplyCost projectApplyCost);

    /**
     * @param ids void 项目科研经费预算ID
     * @Description: 删除项目科研经费预算
     * @Author: yujianfei
     */
    void deleteProjectApplyCost(List<String> ids);

    /**
     * @param id
     * @return ProjectApplyCost
     * @Description: 查询项目科研经费预算详情
     * @Author: yujianfei
     */
    ProjectApplyCost findById(String id);

    /**
     * @param projectApplyCostVo
     * @return PageInfo<ProjectApplyCost>
     * @Description: 分页查询项目科研经费预算
     * @Author: yujianfei
     */
    PageInfo<ProjectApplyCost> findPageByQuery(ProjectApplyCostVo projectApplyCostVo);
}
