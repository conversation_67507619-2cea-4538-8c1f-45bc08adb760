package com.fd.stdp.service.appraisal;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.appraisal.AppraisalBasicItems;
import com.fd.stdp.beans.appraisal.vo.AppraisalBasicItemsVo;

/**
 * 评价基础项Service接口
 *
 * <AUTHOR>
 * @date 2021-11-18
 */
public interface AppraisalBasicItemsService {
    /**
     * @param appraisalBasicItems 评价基础项对象
     * @return String 评价基础项ID
     * @Description: 保存或更新评价基础项
     * @Author: yujianfei
     */
    String saveOrUpdateAppraisalBasicItems(AppraisalBasicItems appraisalBasicItems);

    /**
     * @param ids void 评价基础项ID
     * @Description: 删除评价基础项
     * @Author: yujianfei
     */
    void deleteAppraisalBasicItems(List<String> ids);

    /**
     * @param id
     * @return AppraisalBasicItems
     * @Description: 查询评价基础项详情
     * @Author: yujianfei
     */
    AppraisalBasicItems findById(String id);

    /**
     * @param appraisalBasicItemsVo
     * @return PageInfo<AppraisalBasicItems>
     * @Description: 分页查询评价基础项
     * @Author: yujianfei
     */
    PageInfo<AppraisalBasicItems> findPageByQuery(AppraisalBasicItemsVo appraisalBasicItemsVo);
}
