package com.fd.stdp.service.innovation.impl;

import java.util.List;

import com.fd.stdp.dao.innovation.InnovationQualityContractMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityChange;
import com.fd.stdp.beans.innovation.vo.InnovationQualityChangeVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationQualityChangeMapper;
import com.fd.stdp.service.innovation.InnovationQualityChangeService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 省质检中心变更
 *@Author: wangsh
 *@Date: 2022-03-04 09:23:32
 */
public class InnovationQualityChangeServiceImpl extends BaseServiceImpl<InnovationQualityChangeMapper, InnovationQualityChange> implements InnovationQualityChangeService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationQualityChangeServiceImpl.class);
	
	@Autowired
	private InnovationQualityChangeMapper innovationQualityChangeMapper;
	@Autowired
	private InnovationQualityContractMapper innovationQualityContractMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新省质检中心变更
	 *@param innovationQualityChange 省质检中心变更对象
	 *@return String 省质检中心变更ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationQualityChange(InnovationQualityChangeVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if (vo.getContract() != null){
			vo.setApplyUnitName(vo.getContract().getApplyUnitName());
			vo.setApplyCenterName(vo.getContract().getApplyCenterName());
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationQualityChangeMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationQualityChangeMapper.updateByPrimaryKeySelective(vo);
		}
		/**
		 * 附件
		 */
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除省质检中心变更
	 *@param id void 省质检中心变更ID
	 *@Author: wangsh
	 */
	public void deleteInnovationQualityChange(String id) {
		//TODO 做判断后方能执行删除
		InnovationQualityChange innovationQualityChange=innovationQualityChangeMapper.selectByPrimaryKey(id);
		if(innovationQualityChange==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationQualityChange teminnovationQualityChange=new InnovationQualityChange();
		teminnovationQualityChange.setYn(CommonConstant.FLAG_NO);
		teminnovationQualityChange.setId(innovationQualityChange.getId());
		innovationQualityChangeMapper.updateByPrimaryKeySelective(teminnovationQualityChange);
	}

    /**
     * @Description: 批量删除省质检中心变更
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationQualityChange(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationQualityChange(id));
	}

	@Override
	/**
	 *@Description: 查询省质检中心变更详情
	 *@param id
	 *@return InnovationQualityChange
	 *@Author: wangsh
	 */
	public InnovationQualityChange findById(String id) {
		InnovationQualityChange innovationQualityChange = innovationQualityChangeMapper.selectByPrimaryKey(id);
		InnovationQualityChangeVo vo = new InnovationQualityChangeVo();
		BeanUtils.copyProperties(innovationQualityChange, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		vo.setContract(innovationQualityContractMapper.selectByPrimaryKey(vo.getContractId()));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询省质检中心变更
	 *@param innovationQualityChangeVo
	 *@return PageInfo<InnovationQualityChange>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationQualityChange> findPageByQuery(InnovationQualityChangeVo innovationQualityChangeVo) {
		PageHelper.startPage(innovationQualityChangeVo.getPageNum(),innovationQualityChangeVo.getPageSize());
		Example example=new Example(InnovationQualityChange.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationQualityChangeVo.getName())){
		//	criteria.andEqualTo(innovationQualityChangeVo.getName());
		//}
		List<InnovationQualityChange> innovationQualityChangeList=innovationQualityChangeMapper.selectByExample(example);
		return new PageInfo<InnovationQualityChange>(innovationQualityChangeList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationQualityChange(InnovationQualityChangeVo innovationQualityChangeVo) {
		String id = this.saveOrUpdateInnovationQualityChange(innovationQualityChangeVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innovationQualityChangeVo, this.mapper,
				StringUtils.isNotBlank(innovationQualityChangeVo.getAuditAdvice())?innovationQualityChangeVo.getAuditAdvice():"提交省质检中心变更");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationQualityChange(InnovationQualityChangeVo innovationQualityChangeVo) {
		flowCommonService.doFlowStepAudit(innovationQualityChangeVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityChangeVo.getAuditAdvice()) ? innovationQualityChangeVo.getAuditAdvice() : "省质检中心变更审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationQualityChangeVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationQualityChange(InnovationQualityChangeVo innovationQualityChangeVo) {
		flowCommonService.doFlowStepSendBack(innovationQualityChangeVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityChangeVo.getAuditAdvice())?innovationQualityChangeVo.getAuditAdvice():"省质检中心变更退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationQualityChange(InnovationQualityChangeVo innovationQualityChangeVo) {
		flowCommonService.doCompleteTask(innovationQualityChangeVo, this.mapper
				, "省质检中心变更任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnovationQualityChange> todoList(InnovationQualityChangeVo vo) {

		Example example = new Example(InnovationQualityChange.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationQualityChange> finishedList(InnovationQualityChangeVo vo) {
		Example example = new Example(InnovationQualityChange.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnovationQualityChange> endList(InnovationQualityChangeVo vo) {
        Example example = new Example(InnovationQualityChange.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(InnovationQualityChange vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		if(StringUtils.isNotBlank(vo.getApplyType())){
			criteria.andIsNotNull("applyType").andEqualTo("applyType", vo.getApplyType());
		} else {
			criteria.andIsNull("applyType");
		}
		//查询条件
		if(StringUtils.isNotBlank(vo.getApplyCenterName())){
			criteria.andLike("applyCenterName", "%" + vo.getApplyCenterName()+ "%");
		}
		return criteria;
	}
}
