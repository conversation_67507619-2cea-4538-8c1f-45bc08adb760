package com.fd.stdp.service.sys.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fd.stdp.beans.sys.SysLogRecord;
import com.fd.stdp.beans.sys.vo.SysLogRecordExportVo;
import com.fd.stdp.beans.sys.vo.SysLogRecordVo;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.dao.sys.SysLogMapper;
import com.fd.stdp.service.sys.SysLogService;
import com.fd.stdp.util.UUIDUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 * @Description: 系统日志
 * @Author: hzh
 * @Date: 2019-11-04 14:33:24
 */
public class SysLogServiceImpl extends BaseServiceImpl<SysLogMapper, SysLogRecord> implements SysLogService {
	public static final Logger logger = LoggerFactory.getLogger(SysLogServiceImpl.class);

	@Autowired
	private SysLogMapper sysLogMapper;

	@Override
	@Transactional(readOnly = false)
	public String saveOrUpdateLogRecord(SysLogRecord sysLogRecord) {
		if (sysLogRecord == null) {
			throw new ServiceException("数据异常");
		}
		if (StringUtils.isEmpty(sysLogRecord.getId())) {
			// 新增
			sysLogRecord.setId(UUIDUtils.getUUID());
			sysLogMapper.insertSelective(sysLogRecord);
		}
		return sysLogRecord.getId();
	}

	/**
	 * 
	 * @Description: 查询日志记录
	 * @param id 日志ID
	 * @return SysLogRecord
	 * @Author: hzh
	 */
	@Override
	public SysLogRecord findById(String id) {
		if (StringUtils.isEmpty(id)) {
			throw new ServiceException("数据异常");
		}
		return sysLogMapper.selectByPrimaryKey(id);
	}

	@Override
	public PageInfo<SysLogRecord> findAllPage(SysLogRecordVo logRecordvo) {
		PageHelper.startPage(logRecordvo.getPageNum(), logRecordvo.getPageSize());
		Example example = new Example(SysLogRecord.class);
		Criteria criteria = example.createCriteria();

		String[] operTime = logRecordvo.getOperTime();
		String sortField = logRecordvo.getSortField();

		if (!StringUtils.isEmpty(logRecordvo.getModule())) {
			criteria.andLike("module", "%" + logRecordvo.getModule() + "%");
		}
		if (!StringUtils.isEmpty(logRecordvo.getUserName())) {
			criteria.andLike("userName", "%" + logRecordvo.getUserName() + "%");
		}
		if (logRecordvo.getFlag() != null) {
			criteria.andEqualTo("flag", logRecordvo.getFlag());
		}

		if (operTime != null && operTime.length == 2) {
			criteria.andBetween("createTime", operTime[0], operTime[1]);
		}

		if (!StringUtils.isEmpty(sortField)) {
			if ("desc".equals(logRecordvo.getSortType())) {
				example.orderBy(sortField).desc();
			} else {
				example.orderBy(sortField).asc();
			}
		} else {
			example.orderBy("createTime").desc();
		}

		List<SysLogRecord> logRecordList = sysLogMapper.selectByExample(example);
		return new PageInfo<>(logRecordList);
	}

	/**
	 * @Description: 导出日志信息
	 * @param sysLogRecordVo
	 * @return
	 * @see com.fd.stdp.service.sys.SysLogService#export(com.fd.stdp.beans.sys.vo.SysLogRecordVo)
	 * @Author: szx
	 */
	@Override
	public List<SysLogRecordExportVo> export(SysLogRecordVo sysLogRecordVo) {
		return sysLogMapper.selectExportData(sysLogRecordVo);
	}
}
