package com.fd.stdp.service.basic;

import java.util.List;

import com.fd.stdp.beans.basic.vo.BasicFileAppendixVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonLinked;
import com.fd.stdp.beans.basic.vo.BasicPersonLinkedVo;
/**
 *@Description: 人员关联表
 *@Author: wangsh
 *@Date: 2022-01-25 15:48:05
 */
public interface BasicPersonLinkedService {

	/**
	 *@Description: 保存或更新人员关联表
	 *@param basicPersonLinked 人员关联表对象
	 *@return String 人员关联表ID
	 *@Author: wangsh
	 */
	String saveOrUpdateBasicPersonLinked(BasicPersonLinkedVo basicPersonLinked);
	
	/**
	 *@Description: 删除人员关联表
	 *@param id void 人员关联表ID
	 *@Author: wangsh
	 */
	void deleteBasicPersonLinked(String id);

	/**
	 * @Description: 批量删除人员关联表
	 * @param ids
	 */
    void deleteMultiBasicPersonLinked(List<String> ids);

	/**
	 *@Description: 查询人员关联表详情
	 *@param id
	 *@return BasicPersonLinked
	 *@Author: wangsh
	 */
	BasicPersonLinked findById(String id);

	/**
	 *@Description: 分页查询人员关联表
	 *@param basicPersonLinkedVo
	 *@return PageInfo<BasicPersonLinked>
	 *@Author: wangsh
	 */
	PageInfo<BasicPersonLinked> findPageByQuery(BasicPersonLinkedVo basicPersonLinkedVo);

	/**
	 * 获取表单对应的人员列表
	 * @param formId
	 * @return
	 */
	List<BasicPersonLinkedVo> findByFormId(String formId);

	/**
	 * 清除并更新表单对应的人员信息
	 * @param formId 表单id basicFileAppendixVoList人员信息
	 * @param list
	 */
	void clearAndUpdateBasicPersonLinked(String formId, List<BasicPersonLinkedVo> list);


	/**
	 * 清除并更新表单对应的人员信息
	 * @param formId 表单id basicFileAppendixVoList人员信息
	 * @param list
	 * @param type 指定type类型
	 */
	void clearAndUpdateBasicPersonLinked(String formId, List<BasicPersonLinkedVo> list, String type);
}
