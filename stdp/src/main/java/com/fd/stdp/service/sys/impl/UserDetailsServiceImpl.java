package com.fd.stdp.service.sys.impl;

import com.fd.stdp.beans.sys.SysRole;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.redis.RedisUtil;
import com.fd.stdp.constant.RedisConstant;
import com.fd.stdp.constant.SysConstant;
import com.fd.stdp.service.sys.LoginAttemptService;
import com.fd.stdp.service.sys.SysUserService;
import com.fd.stdp.util.RSAEncrypt;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * spring security登陆处理<br>
 * <p>
 *
 * <AUTHOR>
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private SysUserService userService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private LoginAttemptService loginAttemptService;

    @Autowired
    private HttpServletRequest request;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        String ip = getCurrentIp(request);
        if (loginAttemptService.isBlocked(ip)) {
            throw new ServiceException("登录次数已达上限，请十分钟之后再试！");
        }
        String[] utem;
        try {
            utem = RSAEncrypt.privateKeyDecrypt(username, RSAEncrypt.PRIVATE_KEY).split("@fd@");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        username = utem[0];
        String validCode = utem[1];
        String guid = utem[2];
        Object codeObj = redisUtil.get(RedisConstant.VALID_CODE_KEY + SysConstant.LOGIN_VALID_CODE_FLAG + ":" + guid);

        if (codeObj == null) {
            throw new AuthenticationCredentialsNotFoundException("验证码已过期");
        }
        redisUtil.del(RedisConstant.VALID_CODE_KEY + SysConstant.LOGIN_VALID_CODE_FLAG + ":" + guid);
        if (!validCode.equals(codeObj.toString())) {
            throw new AuthenticationCredentialsNotFoundException("验证码错误");
        }

        SysUser sysUser = userService.getUser(username);

        if (sysUser == null) {
            throw new AuthenticationCredentialsNotFoundException("用户名或密码错误");
        } else if (sysUser.getEnabled() == 0) {
            throw new DisabledException("用户已停用");
        }

        LoginUser loginUser = new LoginUser();
        BeanUtils.copyProperties(sysUser, loginUser);
        Set<String> permissions = userService.listElementByUserId(sysUser.getId());
        List<SysRole> sysRoles = userService.listRolesByUserId(sysUser.getId());
        loginUser.setPermissions(permissions);
        loginUser.setSysRoles(sysRoles);
        return loginUser;
    }

    public String getCurrentIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    public LoginUser getLoginUser(SysUser sysUser){
        // 校验用户合法性
        validateUser(sysUser);

        LoginUser loginUser = new LoginUser();
        BeanUtils.copyProperties(sysUser, loginUser);
        // 获取角色
        List<SysRole> sysRoles = userService.listRolesByUserId(sysUser.getId());
        loginUser.setSysRoles(sysRoles);
        // 获取权限
        Set<String> permissions = userService.listElementByUserId(sysUser.getId());
        loginUser.setPermissions(permissions);

        return loginUser;
    }

    /**
     * 校验用户合法性
     *
     * @param sysUser 登录用户
     */
    private void validateUser(SysUser sysUser) {
        if (sysUser == null) {
            throw new AuthenticationCredentialsNotFoundException("用户名或密码错误");
        } else if (sysUser.getEnabled() == 0) {
            throw new DisabledException("用户已作废");
        }
    }

}
