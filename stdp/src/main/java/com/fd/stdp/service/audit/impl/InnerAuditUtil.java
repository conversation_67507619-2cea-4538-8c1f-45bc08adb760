package com.fd.stdp.service.audit.impl;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022/3/17 19:03
 */
public class InnerAuditUtil {
    public static final String JIGUAN = "30 机关";
    public static final String QIYE = "10 企业";
    public static final String SHIYEDANWEI = "20 事业单位";
    public static final String FANGYUAN = "浙江方圆检测集团股份有限公司";
    public static final String SHENGJUZHISHUDANWEI = "省局直属单位";
    public static final String SHIJU = "市局";
    public static final String QUXIANJU = "县区局";
    public static final String SHENGJU = "省局";


    public enum FillTypeEnum{
        CITY("0"),//自填
        PROVINCE_CITY("1"), // 省局下发
        PROVINCE("2"),//省局自填
        PROVINCE_GOVEMENT("3"),//事业单位自填
        PROVINCE_OFFICE("4"),//省局下发处室
        OFFICE("5"); // 处室自填
        String type;

        FillTypeEnum(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }
    }


    public enum OrgTypeEnum {
        // 省级 直属事业单位 市 县 直属事业企业 处室
        PROVINCE, PROVINCE_GO, CITY, COUNTRY, PROVINCE_GO_EM, OFFICE;
    }

}
