package com.fd.stdp.service.basic.impl;

import java.util.List;

import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.sys.BasicAreacode;
import com.fd.stdp.beans.sys.vo.BasicAreacodeVo;
import com.fd.stdp.constant.Constant;
import com.fd.stdp.dao.sys.BasicAreacodeMapper;
import com.fd.stdp.dao.sys.SysUserMapper;

import com.fd.stdp.util.RSAEncrypt;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicManageOrg;
import com.fd.stdp.beans.basic.vo.BasicManageOrgVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicManageOrgMapper;
import com.fd.stdp.service.basic.BasicManageOrgService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 监管单位Service业务层处理
 * @date 2021-11-08
 */
@Service
@Transactional(readOnly = true)
public class BasicManageOrgServiceImpl extends BaseServiceImpl<BasicManageOrgMapper, BasicManageOrg> implements BasicManageOrgService {

    private static final Logger logger = LoggerFactory.getLogger(BasicManageOrgServiceImpl.class);
    @Autowired
    private BasicManageOrgMapper basicManageOrgMapper;

    @Autowired
    private BasicAreacodeMapper basicAreacodeMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新监管单位
     *@param basicManageOrg 监管单位对象
     *@return String 监管单位ID
     *@Author: yujianfei
     */
    public String saveOrUpdateBasicManageOrg(BasicManageOrgVo basicManageOrgVo) {
        if (basicManageOrgVo == null) {
            throw new ServiceException("数据异常");
        }

        // 行政区划名称AREA_NAME和AREA_CODE
        if (!CollectionUtils.isEmpty(basicManageOrgVo.getAreaCodeList())) {
            if (basicManageOrgVo.getAreaCodeList().size() == 1) {
                basicManageOrgVo.setAreaCode(basicManageOrgVo.getAreaCodeList().get(0));
                BasicAreacodeVo province = basicAreacodeMapper.findByAreaCode(basicManageOrgVo.getAreaCodeList().get(0));
                basicManageOrgVo.setAreaName(province.getAreaName());
            } else if (basicManageOrgVo.getAreaCodeList().size() == 2)  {
                basicManageOrgVo.setAreaCode(basicManageOrgVo.getAreaCodeList().get(1));
                BasicAreacodeVo province = basicAreacodeMapper.findByAreaCode(basicManageOrgVo.getAreaCodeList().get(0));
                BasicAreacodeVo city = basicAreacodeMapper.findByAreaCode(basicManageOrgVo.getAreaCodeList().get(1));
                basicManageOrgVo.setAreaName(province.getAreaName() + city.getAreaName());
            } else if (basicManageOrgVo.getAreaCodeList().size() == 3) {
                basicManageOrgVo.setAreaCode(basicManageOrgVo.getAreaCodeList().get(2));
                BasicAreacodeVo city = basicAreacodeMapper.findByAreaCode(basicManageOrgVo.getAreaCodeList().get(1));
                BasicAreacodeVo county = basicAreacodeMapper.findByAreaCode(basicManageOrgVo.getAreaCodeList().get(2));
                basicManageOrgVo.setAreaName(city.getAreaName() + county.getAreaName());
            }
        }
        if (StringUtils.isEmpty(basicManageOrgVo.getId())) {
            if (!StringUtils.isEmpty(basicManageOrgVo.getOrgCode())) {
                Example example = new Example(BasicScienceOrg.class);
                Criteria criteria = example.createCriteria();
                criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
                criteria.andEqualTo("orgCode", basicManageOrgVo.getOrgCode());
                List<BasicManageOrg> list = basicManageOrgMapper.selectByExample(example);
                if (!CollectionUtils.isEmpty(list)) {
                    throw new ServiceException("机构代码重复");
                }
            }
            //新增
            basicManageOrgVo.setId(UUIDUtils.getUUID());
            basicManageOrgMapper.insertSelective(basicManageOrgVo);
        } else {
            //避免页面传入修改
            basicManageOrgVo.setYn(null);
            basicManageOrgMapper.updateByPrimaryKeySelective(basicManageOrgVo);
            
            if(!StringUtils.isEmpty(basicManageOrgVo.getAreaName())) {
            	sysUserMapper.updateAreaByScienceId(basicManageOrgVo.getId(), basicManageOrgVo.getAreaCode(), basicManageOrgVo.getAreaName());
            }
        }
        return basicManageOrgVo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除监管单位
     *@param id void 监管单位ID
     *@Author: yujianfei
     */
    public void deleteBasicManageOrg(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            BasicManageOrg basicManageOrg = basicManageOrgMapper.selectByPrimaryKey(id);
            if (basicManageOrg == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            BasicManageOrg tembasicManageOrg = new BasicManageOrg();
            tembasicManageOrg.setYn(CommonConstant.FLAG_NO);
            tembasicManageOrg.setId(basicManageOrg.getId());
            basicManageOrgMapper.updateByPrimaryKeySelective(tembasicManageOrg);
        }
    }

    /**
     * @param id
     * @return BasicManageOrg
     * @Description: 查询监管单位详情
     * @Author: yujianfei
     */
    @Override
    public BasicManageOrg findById(String id) {
        return basicManageOrgMapper.selectByPrimaryKey(id);
    }


    /**
     * @param basicManageOrgVo
     * @return PageInfo<BasicManageOrg>
     * @Description: 分页查询监管单位
     * @Author: yujianfei
     */
    @Override
    public PageInfo<BasicManageOrgVo> findPageByQuery(BasicManageOrgVo basicManageOrgVo) {
        PageHelper.startPage(basicManageOrgVo.getPageNum(), basicManageOrgVo.getPageSize());
        if (!StringUtils.isEmpty(basicManageOrgVo.getAreaCode())) {
            String code = null;
            if ("0000".equals(basicManageOrgVo.getAreaCode().substring(2, 6))) {
                code = basicManageOrgVo.getAreaCode().substring(0, 2);
            } else if ("00".equals(basicManageOrgVo.getAreaCode().substring(4, 6))) {
                code = basicManageOrgVo.getAreaCode().substring(0, 4);
            } else {
                code = basicManageOrgVo.getAreaCode();
            }
            basicManageOrgVo.setAreaCode(code);
        }
        List<BasicManageOrgVo> basicManageOrgList = basicManageOrgMapper.findPageByQuery(basicManageOrgVo);
        return new PageInfo<BasicManageOrgVo>(basicManageOrgList);
    }

    /**
     * @return RestApiResponse<?>
     * @Description: 查询所有市级监管单位
     * @Author: yujianfei
     */
    @Override
    public List<BasicManageOrg> findAllCity() {
        Example example = new Example(BasicManageOrg.class);
        Criteria criteria = example.createCriteria();
        criteria.andLike("areaCode", "%" + "00");
        // 排除省级
        criteria.andNotEqualTo("areaCode", "330000");
        List<BasicManageOrg> list = basicManageOrgMapper.selectByExample(example);
        return list;
    }


    /**
     * @return RestApiResponse<?>
     * @Description: 查询所有县级监管单位
     * @Author: yujianfei
     */
    @Override
    public List<BasicManageOrg> findAllCounty() {
        Example example = new Example(BasicManageOrg.class);
        Criteria criteria = example.createCriteria();
        criteria.andNotLike("areaCode", "%" + "00");
        List<BasicManageOrg> list = basicManageOrgMapper.selectByExample(example);
        return list;
    }
}
