package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationNationalCenterApply;
import com.fd.stdp.beans.innovation.vo.InnovationNationalCenterApplyVo;
/**
 *@Description: 国家中心
 *@Author: wangsh
 *@Date: 2022-03-07 19:46:19
 */
public interface InnovationNationalCenterApplyService {

	/**
	 *@Description: 保存或更新国家中心
	 *@param innovationNationalCenterApply 国家中心对象
	 *@return String 国家中心ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationNationalCenterApply(InnovationNationalCenterApplyVo innovationNationalCenterApply);
	
	/**
	 *@Description: 删除国家中心
	 *@param id void 国家中心ID
	 *@Author: wangsh
	 */
	void deleteInnovationNationalCenterApply(String id);

	/**
	 * @Description: 批量删除国家中心
	 * @param ids
	 */
    void deleteMultiInnovationNationalCenterApply(List<String> ids);

	/**
	 *@Description: 查询国家中心详情
	 *@param id
	 *@return InnovationNationalCenterApply
	 *@Author: wangsh
	 */
	InnovationNationalCenterApply findById(String id);

	/**
	 *@Description: 分页查询国家中心
	 *@param innovationNationalCenterApplyVo
	 *@return PageInfo<InnovationNationalCenterApply>
	 *@Author: wangsh
	 */
	PageInfo<InnovationNationalCenterApply> findPageByQuery(InnovationNationalCenterApplyVo innovationNationalCenterApplyVo);
	
	
	/**
	 * 提交
	 * @param innovationNationalCenterApplyVo
	 * @return
	 */
    String submitInnovationNationalCenterApply(InnovationNationalCenterApplyVo innovationNationalCenterApplyVo);

	/**
	 * 审核
	 * @param innovationNationalCenterApplyVo
	 * @return
	 */
	String auditInnovationNationalCenterApply(InnovationNationalCenterApplyVo innovationNationalCenterApplyVo);

	/**
	 * 退回
	 * @param innovationNationalCenterApplyVo
	 * @return
	 */
	String sendBackInnovationNationalCenterApply(InnovationNationalCenterApplyVo innovationNationalCenterApplyVo);

	/**
	 * 任务书下达
	 * @param innovationNationalCenterApplyVo
	 * @return
	 */
	String releaseInnovationNationalCenterApply(InnovationNationalCenterApplyVo innovationNationalCenterApplyVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnovationNationalCenterApply> todoList(InnovationNationalCenterApplyVo innovationNationalCenterApplyVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationNationalCenterApply> finishedList(InnovationNationalCenterApplyVo innovationNationalCenterApplyVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationNationalCenterApply> endList(InnovationNationalCenterApplyVo innovationNationalCenterApplyVo);
}
