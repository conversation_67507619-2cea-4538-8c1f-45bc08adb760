package com.fd.stdp.service.appraisal;

import java.util.List;

import com.fd.stdp.beans.appraisal.vo.*;
import com.fd.stdp.beans.sys.SysUser;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.appraisal.AppraisalApply;

/**
 * 评价申请Service接口
 *
 * <AUTHOR>
 * @date 2021-11-18
 */
public interface AppraisalApplyService {
    /**
     * @param appraisalApplyVo 评价申请对象
     * @return String 评价申请ID
     * @Description: 保存或更新评价申请
     * @Author: yujianfei
     */
    String saveOrUpdateAppraisalApply(AppraisalApplyVo appraisalApplyVo);

    /**
     * @param ids void 评价申请ID
     * @Description: 删除评价申请
     * @Author: yujianfei
     */
    void deleteAppraisalApply(List<String> ids);

    /**
     * @param id
     * @return AppraisalApplyVo
     * @Description: 查询评价申请详情
     * @Author: yujianfei
     */
    AppraisalApplyVo findById(String id);

    /**
     * @param appraisalApplyVo
     * @return PageInfo<AppraisalApply>
     * @Description: 分页查询评价申请
     * @Author: yujianfei
     */
    PageInfo<AppraisalApplyVo> findPageByQuery(AppraisalApplyVo appraisalApplyVo);

    /**
     * @param appraisalApplyVo 评价申请数据 json
     * @return RestApiResponse<?>
     * @Description: 审核评价申请
     * @Author: yujianfei
     */
    void auditByProvince(AppraisalApplyVo appraisalApplyVo);


    /**
     * @param appraisalApplyVo 评价申请数据 json
     * @return RestApiResponse<?>
     * @Description: 退回评价申请
     * @Author: yujianfei
     */
    void sendBack(AppraisalApplyVo appraisalApplyVo);


    /**
     * @param appraisalApplyVo 评价申请数据 json
     * @return RestApiResponse<?>
     * @Description: 市局审核评价申请
     * @Author: yujianfei
     */
    void auditByCity(AppraisalApplyVo appraisalApplyVo);


    /**
     * @return RestApiResponse<?>
     * @Description: 初始化考评数据
     * @Author: liuwei
     */
    void initApply(AppraisalApplyVo appraisalApplyVo);

    /**
     * @return RestApiResponse<?>
     * @Description: 县级待办数据
     * @Author: yujianfei
     */
    int toDoCount(SysUser user);


    /**
     * @param appraisalApplyVo 评价申请 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询已处理评价申请
     * @Author: yujianfei
     */
    PageInfo<AppraisalApply> findFinish(AppraisalApplyVo appraisalApplyVo);


    /**
     * @param appraisalApplyVo 评价申请 查询条件
     * @return RestApiResponse<?>
     * @Description: 搜索
     * @Author: yujianfei
     */
    PageInfo<AppraisalApply> search(AppraisalApplyVo appraisalApplyVo);

    /**
     * @return RestApiResponse<?>
     * @Description: 是否存在下属技术机构
     * @Author: yujianfei
     */
    void noOrgName(String id);


    /**
     * @param appraisalApplyVo 评价申请 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询考核统计表（县（市、区））
     * @Author: yujianfei
     */
    PageInfo<AppraisalApplyVo> findCountyStatistic(AppraisalApplyVo appraisalApplyVo);


    /**
     * @param appraisalApplyVo 评价申请 查询条件
     * @return RestApiResponse<?>
     * @Description: 分页查询考核统计表【市】
     * @Author: yujianfei
     */
    PageInfo<AppraisalApplyVo> findCityStatistic(AppraisalApplyVo appraisalApplyVo);


    /**
     * @param appraisalApplyVo void
     * @Description:导出考核统计表【区县】
     * @Author: yujianfei
     */
    List<AppraisalApplyCityExportVo> exportCity(AppraisalApplyVo appraisalApplyVo);

    /**
     * @param appraisalApplyVo void
     * @Description:导出考核统计表【市局】
     * @Author: yujianfei
     */
    List<AppraisalApplyCountyExportVo> exportCounty(AppraisalApplyVo appraisalApplyVo);

    /**
     * @return RestApiResponse<?>
     * @Description: 撤回到上一步
     * @Author: yujianfei
     * @Date: 2021/12/03
     */
    void recall(String id);


    /**
     * @Description: 考核进度
     * @Author: yujianfei
     * @Date: 2021-12-10
     */
    PageInfo<AppraisalApplyAssessmentExportVo> assessmentProgress(AppraisalApplyVo appraisalApplyVo);

    /**
     * @param appraisalApplyVo void
     * @Description:导出考核进度表
     * @Author: yujianfei
     * @Date: 2021/12/10
     */
    List<AppraisalApplyAssessmentExportVo> exportAssessment(AppraisalApplyVo appraisalApplyVo);


    /**
     * @param appraisalApplyVo void
     * @Description:导出已处理的单条
     * @Author: yujianfei
     * @Date: 2021/12/13
     */
    List<AppraisalApplyFinishExportVo> exportOneFinish(AppraisalApplyVo appraisalApplyVo);
}
