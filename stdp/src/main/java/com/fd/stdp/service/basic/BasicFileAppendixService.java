package com.fd.stdp.service.basic;

import java.util.List;

import com.fd.stdp.beans.project.vo.ProjectApplyExpertScoreVo;
import com.fd.stdp.common.BaseEntity;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicFileAppendix;
import com.fd.stdp.beans.basic.vo.BasicFileAppendixVo;
/**
 *@Description: 附件表
 *@Author: wangsh
 *@Date: 2022-01-12 18:13:39
 */
public interface BasicFileAppendixService {

	/**
	 *@Description: 保存或更新附件表
	 *@param basicFileAppendix 附件表对象
	 *@return String 附件表ID
	 *@Author: wangsh
	 */
	String saveOrUpdateBasicFileAppendix(BasicFileAppendixVo basicFileAppendix);

	/**
	 *@Description: 删除附件表
	 *@param id void 附件表ID
	 *@Author: wangsh
	 */
	void deleteBasicFileAppendix(String id);

	/**
	 * @Description: 批量删除附件表
	 * @param basicFileAppendixVo
	 */
    void deleteMultiBasicFileAppendix(BasicFileAppendixVo basicFileAppendixVo);

	/**
	 *@Description: 查询附件表详情
	 *@param id
	 *@return BasicFileAppendix
	 *@Author: wangsh
	 */
	BasicFileAppendix findById(String id);

	/**
	 *@Description: 分页查询附件表
	 *@param basicFileAppendixVo
	 *@return PageInfo<BasicFileAppendix>
	 *@Author: wangsh
	 */
	PageInfo<BasicFileAppendix> findPageByQuery(BasicFileAppendixVo basicFileAppendixVo);

	/**
	 * 获取表单对应的附件列表
	 * @param formId
	 * @return
	 */
	List<BasicFileAppendixVo> findByFormId(String formId);

	/**
	 * 清除并更新表单对应的附件信息
	 * @param formId 表单id basicFileAppendixVoList附件信息
	 * @param basicFileAppendixVoList
	 */
	void clearAndUpdateBasicFileAppendix(String formId, List<BasicFileAppendixVo> basicFileAppendixVoList);
}
