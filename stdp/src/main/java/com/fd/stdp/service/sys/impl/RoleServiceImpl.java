package com.fd.stdp.service.sys.impl;

import com.fd.stdp.beans.sys.SysRole;
import com.fd.stdp.beans.sys.SysRolePermission;
import com.fd.stdp.beans.sys.SysUserRole;
import com.fd.stdp.beans.sys.vo.*;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.constant.CommonConstant;
import com.fd.stdp.dao.sys.SysRoleMapper;
import com.fd.stdp.dao.sys.SysRolePermissionMapper;
import com.fd.stdp.dao.sys.SysUserRoleMapper;
import com.fd.stdp.service.sys.RoleService;
import com.fd.stdp.util.UUIDUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional(readOnly = true)
public class RoleServiceImpl extends BaseServiceImpl<SysRoleMapper, SysRole> implements RoleService {

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private SysRolePermissionMapper sysRolePermissionMapper;
    /**
     * 保存或更新角色
     *
     * @param sysRole
     * @return
     */
    @Override
    @Transactional(readOnly = false)
    public SysRole saveOrUpdateRole(SysRoleVo sysRole) throws Exception {

        String sysRoleId = sysRole.getId();

        // 校验角色名称和编码不能重复
        if (!checkCodeAndName(sysRole.getRoleCode(), sysRole.getRoleName(), sysRoleId)) {
            throw new ServiceException("角色名称或角色编码已经存在");
        }

        if (StringUtils.isEmpty(sysRoleId)) {// 新增
            sysRole.setStatus(1);
            sysRole.setStatusTxt("启用");
            sysRole.setId(UUIDUtils.getUUID());
            this.mapper.insertSelective(sysRole);
        } else {
            this.mapper.updateByPrimaryKeySelective(sysRole);
        }
        saveRoleMenu(sysRole.getRoleCode(), sysRole.getMenuIds());
        return sysRole;
    }
    
    /**
     * 保存菜单和角色关联
     * @param roleId
     * @param menus
     */
    private void saveRoleMenu(String roleCode,List<String> menus) {
    	Example example=new Example(SysRolePermission.class);
    	example.createCriteria().andEqualTo("roleCode", roleCode);
    	sysRolePermissionMapper.deleteByExample(example);
    	if(CollectionUtils.isEmpty(menus)) {
    		return;
    	}
    	List<SysRolePermission> rolePerList=new ArrayList<>();
    	for(String menu:menus) {
    		SysRolePermission rolePer=new SysRolePermission();
    		rolePer.setId(UUIDUtils.getUUID());
    		rolePer.setRoleCode(roleCode);
    		rolePer.setResourceId(menu);
            rolePer.setResourceType("menu");
    		rolePerList.add(rolePer);
    	}
    	if(!CollectionUtils.isEmpty(rolePerList)) {
    		sysRolePermissionMapper.insertList(rolePerList);
    	}
    }
    
    /**
     * 根据角色查询详情
     */
    @Override
	public SysRoleVo findRoleById(String id) {
    	SysRole role=	this.mapper.selectByPrimaryKey(id);
    	if(role==null) {
    		throw new ServiceException("非法请求");
    	}
    	SysRoleVo roleVo=new SysRoleVo();
    	BeanUtils.copyProperties(role, roleVo);
    	Example example=new Example(SysRolePermission.class);
    	example.createCriteria().andEqualTo("roleCode", role.getRoleCode());
    	List<SysRolePermission> rolePerList=sysRolePermissionMapper.selectByExample(example);
    	if(!CollectionUtils.isEmpty(rolePerList)) {
    		List<String> menuList=rolePerList.stream().map(rolePer->rolePer.getResourceId()).collect(Collectors.toList());
    		roleVo.setMenuIds(menuList);
    	}
		return roleVo;
	}
    boolean checkCodeAndName(String roleCode, String roleName, String id) {
        Example example = new Example(SysRole.class);
        Map<String, String> map = new HashMap<>();
        map.put("roleCode", roleCode);
        map.put("roleName", roleName);
        example.createCriteria().orEqualTo(map);
        List<SysRole> list = mapper.selectByExample(example);
        if (list.isEmpty()) {
            return true;
        }
        // 编辑
        if (id != null) {
            for (SysRole bean : list) {
                if (!bean.getId().equals(id)) {
                    return false;
                }
            }
        } else {
            return false;
        }

        return true;
    }

    @Override
    public PageInfo<SysRole> listPage(Integer pageNum, Integer pageSize, String roleName, String roleCode) {

        // 分页开始
        PageHelper.startPage(pageNum, pageSize);

        Example example = new Example(SysRole.class);
        Criteria criteria = example.createCriteria();

        if (!StringUtils.isEmpty(roleName)) {
            criteria.andLike("roleName", "%" + roleName + "%");
        }
        if (!StringUtils.isEmpty(roleCode)) {
            criteria.andEqualTo("roleCode", roleCode);
        }
        List<SysRole> roleList = this.sysRoleMapper.selectByExample(example);
        return new PageInfo<SysRole>(roleList);
    }

    /**
     * 改变状态
     */
    @Override
    @Transactional(readOnly = false)
    public void changeStatus(String roleIds, Integer status) {
        String[] ids = roleIds.split(",");
        String rids[] = new String[ids.length];
        for (int i = 0; i < ids.length; i++) {
            rids[i] = ids[i];
        }

        for (String id : rids) {
            SysRole role = this.mapper.selectByPrimaryKey(id);
            if (role != null) {
                role.setStatus(status);
                this.mapper.updateByPrimaryKeySelective(role);
            }
        }

    }

    @Override
    @Transactional(readOnly = false)
    public void deleteRole(String roleIds) {
        // 删除之前 判断用户是否存在
        if (StringUtils.isBlank(roleIds)) {
            throw new ServiceException("角色id不能为空!");
        }
        String[] ids = roleIds.split(",");
        String rids[] = new String[ids.length];
        for (int i = 0; i < ids.length; i++) {
            rids[i] = ids[i];
        }

        List<SysUserRole> sysUserRoleList = sysUserRoleMapper.selectUserByRoleIds(rids);
        if (sysUserRoleList == null || sysUserRoleList.size() == 0) {
            // 逻辑删除
            this.mapper.deleteRoleByIds(rids);
        } else {
            throw new ServiceException("角色下存在用户不允许删除");
        }
    }

    @Override
    public PageInfo<RoleUserVO> roleUserPage(Integer pageNum, Integer pageSize, String userName) {
        PageHelper.startPage(pageNum, pageSize);
        Example example = new Example(SysRole.class);
        example.orderBy("updateTime").desc();
        List<SysRole> roles = this.sysRoleMapper.selectByExample(example);
        PageInfo<SysRole> rolePageInfo = new PageInfo<>(roles);
        // 给每个角色封装用户
        ArrayList<RoleUserVO> roleUserVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(rolePageInfo.getList())) {
            return new PageInfo<>(roleUserVos);
        }
        for (SysRole role : rolePageInfo.getList()) {
            RoleUserVO roleUserVO = new RoleUserVO();
            BeanUtils.copyProperties(role, roleUserVO);
            // List<SysUserInfoVO> userInfos =
            // this.sysUserInfoMapper.findUsersByRoleIdAndUserName(role.getId(),userName);
            // if (!CollectionUtils.isEmpty(userInfos)){
            // roleUserVO.setUserList(userInfos);
            // }
            roleUserVos.add(roleUserVO);
        }
        PageInfo<RoleUserVO> roleUserVOPageInfo = new PageInfo<>(roleUserVos);
        BeanUtils.copyProperties(rolePageInfo, roleUserVOPageInfo, "list");
        roleUserVOPageInfo.setList(roleUserVos);
        return roleUserVOPageInfo;
    }

    @Override
    public List<SysRole> findRolesByUserId(String userId) {
        List<SysRole> roles = this.mapper.getByUserId(userId);
        return roles;
    }

    @Override
    public PageInfo<RoleAndRoleTypesVO> listPageRoleAndRoleTypeVo(SysRoleVo sysRoleVo) {
        // 分页开始
        PageHelper.startPage(sysRoleVo.getPageNum(), sysRoleVo.getPageSize());
        return new PageInfo<RoleAndRoleTypesVO>(this.sysRoleMapper.listRoleAndRoleNameVo(sysRoleVo));
    }

    /**
     * 查询当前用户添加的角色列表
     *
     * @param sysRoleVo
     * @return
     */
    @Override
    public List<SysRole> listRoleByLoginUser(SysRoleVo sysRoleVo) {
        return this.sysRoleMapper.listRoleByLoginUser(sysRoleVo);
    }

    /**
     * 获取组织架构
     *
     * @return
     */
    @Override
    public List<OrganizationalTree> getOrgTree() {
        List<OrganizationalTree> orgTree = this.mapper.getOrgTree();
        return orgTree;
    }

    /**
     * 导出角色
     *
     * @param roleName
     * @return
     */
    @Override
    public List<SysRoleExportVo> export(SysRoleVo sysRoleVo) {
        List<SysRoleExportVo> roleExportList = this.mapper.export(sysRoleVo);
        return roleExportList;
    }

    @Override
    public List<SysRole> listAll() {
        return this.mapper.selectAll();
    }

    /**
     * 根据角色code返回name
     *
     * @param code
     * @return
     */
    @Override
    public String fingByCode(String code) {
        String name = "";
        if (!StringUtils.isEmpty(code)) {
            SysRole record = new SysRole();
            record.setYn(CommonConstant.FLAG_YES);
            record.setRoleCode(code);
            List<SysRole> listRoles = sysRoleMapper.select(record);

            if (!CollectionUtils.isEmpty(listRoles)) {
                name = listRoles.get(0).getRoleName();
            }
        }
        return name;
    }

    /**
     * 根据ROLE_CODE返回
     * @param roleCode
     * @return
     */
    @Override
    public SysRole findRoleByRoleCode(String roleCode) {
        SysRole role = null;
        if (!StringUtils.isEmpty(roleCode)) {
            Example example = new Example(SysRole.class);
            Criteria criteria = example.createCriteria();
            criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
            criteria.andEqualTo("roleCode", roleCode);
            List<SysRole> sysRoleList = sysRoleMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(sysRoleList)) {
                role = sysRoleList.get(0);
            }
        }
        return role;
    }
}
