package com.fd.stdp.service.project.impl;


import com.fd.stdp.beans.basic.vo.BasicFileAppendixVo;
import com.fd.stdp.beans.project.ProjectApplyExpertMumber;
import com.fd.stdp.beans.project.ProjectApplyExpertScore;
import com.fd.stdp.beans.project.ProjectApplyInfo;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertScoreVo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.beans.sys.SysFileInfo;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.constant.ProjectDeclarationFlowConstants;
import com.fd.stdp.dao.project.ProjectApplyExpertMumberMapper;
import com.fd.stdp.dao.project.ProjectApplyExpertScoreMapper;
import com.fd.stdp.dao.project.ProjectApplyInfoMapper;
import com.fd.stdp.dao.sys.SysFileInfoMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.project.ProjectApplyExpertScoreService;
import com.fd.stdp.service.project.ProjectApplyInfoService;
import com.fd.stdp.util.AssertUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.constant.CommonConstant;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description: 项目申请专家评审意见Service业务层处理
 * @date 2025-07-24
 */
@Service
@Transactional(readOnly = true)
public class ProjectApplyExpertScoreServiceImpl extends BaseServiceImpl<ProjectApplyExpertScoreMapper, ProjectApplyExpertScore> implements ProjectApplyExpertScoreService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectApplyExpertScoreServiceImpl.class);

    @Autowired
    private ProjectApplyExpertScoreMapper projectApplyExpertScoreMapper;

    @Autowired
    private ProjectApplyInfoMapper projectApplyInfoMapper;

    @Autowired
    private ProjectApplyExpertMumberMapper projectApplyExpertMumberMapper;

    @Autowired
    private ProjectApplyInfoService projectApplyInfoService;

    @Autowired
    private SysFileInfoMapper fileInfoMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存 项目申请专家评审意见
     *@param projectApplyExpertScore 项目申请专家评审意见对象
     *@return String 项目申请专家评审意见ID
     *@Author: zhangYu
     */
    public String save(ProjectApplyExpertScoreVo vo, String userName, String RoleName) {
        AssertUtil.notNull(vo, "数据异常");
        ProjectApplyInfo info = projectApplyInfoMapper.selectByPrimaryKey(vo.getApplyId());
        AssertUtil.notNull(info, "项目数据异常");

        if (!StringUtils.hasText(vo.getId())) {
            //新增
            vo.setId(UUIDUtils.getUUID());
            projectApplyExpertScoreMapper.insertSelective(vo);

            //更新项目评审状态
            ProjectApplyExpertMumber mumber = projectApplyExpertMumberMapper.selectMumberByApplyIdAndExpertName(vo);
            AssertUtil.notNull(mumber, "专家数据异常");
            mumber.setReviewStatus(1);
            projectApplyExpertMumberMapper.updateByPrimaryKeySelective(mumber);
        }

        //查询需评审数量
        int i = projectApplyExpertMumberMapper.countExpertMumber(vo.getApplyId());

        //查询项目评审数量
        int j = projectApplyExpertScoreMapper.countExpertScore(vo.getApplyId());

        //如果本轮评审为最后一名专家，推动项目流程
        if (i == j) {
            //推动流程
            ProjectApplyInfoVo applyVo = new ProjectApplyInfoVo();
            applyVo.setId(info.getId());
            applyVo.setActNodeKey(ProjectDeclarationFlowConstants.EXPERT_REVIEW_TASK_CODE);
            applyVo.setFlowResult(CommonConstant.FLAG_YES.toString());
            applyVo.setAdvice("");
            projectApplyInfoService.applyAudit(applyVo, userName, RoleName);
        }

        return vo.getId();
    }

}
