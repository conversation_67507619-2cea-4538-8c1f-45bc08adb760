package com.fd.stdp.service.talent.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamExamineRegister;
import com.fd.stdp.beans.talent.vo.TalentTeamExamineRegisterVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.talent.TalentTeamExamineRegisterMapper;
import com.fd.stdp.service.talent.TalentTeamExamineRegisterService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 创新团队考核登记
 *@Author: wangsh
 *@Date: 2022-02-14 10:22:40
 */
public class TalentTeamExamineRegisterServiceImpl extends BaseServiceImpl<TalentTeamExamineRegisterMapper, TalentTeamExamineRegister> implements TalentTeamExamineRegisterService{

	public static final Logger logger = LoggerFactory.getLogger(TalentTeamExamineRegisterServiceImpl.class);
	
	@Autowired
	private TalentTeamExamineRegisterMapper talentTeamExamineRegisterMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新创新团队考核登记
	 *@param talentTeamExamineRegister 创新团队考核登记对象
	 *@return String 创新团队考核登记ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTalentTeamExamineRegister(TalentTeamExamineRegisterVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			talentTeamExamineRegisterMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			talentTeamExamineRegisterMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除创新团队考核登记
	 *@param id void 创新团队考核登记ID
	 *@Author: wangsh
	 */
	public void deleteTalentTeamExamineRegister(String id) {
		//TODO 做判断后方能执行删除
		TalentTeamExamineRegister talentTeamExamineRegister=talentTeamExamineRegisterMapper.selectByPrimaryKey(id);
		if(talentTeamExamineRegister==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TalentTeamExamineRegister temtalentTeamExamineRegister=new TalentTeamExamineRegister();
		temtalentTeamExamineRegister.setYn(CommonConstant.FLAG_NO);
		temtalentTeamExamineRegister.setId(talentTeamExamineRegister.getId());
		talentTeamExamineRegisterMapper.updateByPrimaryKeySelective(temtalentTeamExamineRegister);
	}

    /**
     * @Description: 批量删除创新团队考核登记
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTalentTeamExamineRegister(List<String> ids) {
		ids.stream().forEach(id-> this.deleteTalentTeamExamineRegister(id));
	}

	@Override
	/**
	 *@Description: 查询创新团队考核登记详情
	 *@param id
	 *@return TalentTeamExamineRegister
	 *@Author: wangsh
	 */
	public TalentTeamExamineRegister findById(String id) {
		return talentTeamExamineRegisterMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询创新团队考核登记
	 *@param talentTeamExamineRegisterVo
	 *@return PageInfo<TalentTeamExamineRegister>
	 *@Author: wangsh
	 */
	public PageInfo<TalentTeamExamineRegister> findPageByQuery(TalentTeamExamineRegisterVo talentTeamExamineRegisterVo) {
		PageHelper.startPage(talentTeamExamineRegisterVo.getPageNum(),talentTeamExamineRegisterVo.getPageSize());
		Example example=new Example(TalentTeamExamineRegister.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(talentTeamExamineRegisterVo.getName())){
		//	criteria.andEqualTo(talentTeamExamineRegisterVo.getName());
		//}
		List<TalentTeamExamineRegister> talentTeamExamineRegisterList=talentTeamExamineRegisterMapper.selectByExample(example);
		return new PageInfo<TalentTeamExamineRegister>(talentTeamExamineRegisterList);
	}

	@Override
	public String submitTalentTeamExamineRegister(TalentTeamExamineRegisterVo talentTeamExamineRegisterVo) {
		String id = this.saveOrUpdateTalentTeamExamineRegister(talentTeamExamineRegisterVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_INNOVATION_APPLY, talentTeamExamineRegisterVo, this.mapper,
				StringUtils.isNotBlank(talentTeamExamineRegisterVo.getAuditAdvice())?talentTeamExamineRegisterVo.getAuditAdvice():"提交创新团队考核登记");
		return id;
	}

	@Override
	public String auditTalentTeamExamineRegister(TalentTeamExamineRegisterVo talentTeamExamineRegisterVo) {
		flowCommonService.doFlowStepAudit(talentTeamExamineRegisterVo, this.mapper
				, StringUtils.isNotBlank(talentTeamExamineRegisterVo.getAuditAdvice()) ? talentTeamExamineRegisterVo.getAuditAdvice() : "创新团队考核登记审核通过"
				, FlowStatusEnum.CHOOSE_EXPERTS.getCode());
		return talentTeamExamineRegisterVo.getId();
	}

	@Override
	public String sendBackTalentTeamExamineRegister(TalentTeamExamineRegisterVo talentTeamExamineRegisterVo) {
		flowCommonService.doFlowStepSendBack(talentTeamExamineRegisterVo, this.mapper
				, StringUtils.isNotBlank(talentTeamExamineRegisterVo.getAuditAdvice())?talentTeamExamineRegisterVo.getAuditAdvice():"创新团队考核登记退回"
				, false
		);
		return null;
	}

	@Override
	public String releaseTalentTeamExamineRegister(TalentTeamExamineRegisterVo talentTeamExamineRegisterVo) {
		flowCommonService.doCompleteTask(talentTeamExamineRegisterVo, this.mapper
				, "创新团队考核登记任务书下达完成"
				, FlowStatusEnum.PROJECT_RELEASE.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<TalentTeamExamineRegister> todoList(TalentTeamExamineRegisterVo vo) {

		Example example = new Example(TalentTeamExamineRegister.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TalentTeamExamineRegister> finishedList(TalentTeamExamineRegisterVo vo) {
		Example example = new Example(TalentTeamExamineRegister.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	private Criteria getCriteria(TalentTeamExamineRegister vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
		return criteria;
	}
}
