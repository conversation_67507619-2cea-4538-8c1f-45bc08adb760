package com.fd.stdp.service.audit.impl;

import java.io.IOException;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.fd.stdp.beans.audit.InnerAuditOrg;
import com.fd.stdp.beans.audit.InnerAuditStatisticsRecord;
import com.fd.stdp.beans.basic.BasicManageOrg;
import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.sys.SysDictItem;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.beans.sys.vo.SysUserVo;
import com.fd.stdp.constant.Constant;
import com.fd.stdp.dao.audit.InnerAuditOrgMapper;
import com.fd.stdp.dao.audit.InnerAuditStatisticsRecordMapper;
import com.fd.stdp.dao.basic.BasicManageOrgMapper;
import com.fd.stdp.dao.basic.BasicScienceOrgMapper;
import com.fd.stdp.dao.sys.SysUserMapper;
import com.fd.stdp.service.sys.SysDictItemService;
import com.fd.stdp.service.user.SysUserUtilService;
import com.fd.stdp.util.EasyExcelUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.audit.InnerAuditStatistics;
import com.fd.stdp.beans.audit.vo.InnerAuditStatisticsVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.audit.InnerAuditStatisticsMapper;
import com.fd.stdp.service.audit.InnerAuditStatisticsService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import javax.servlet.http.HttpServletResponse;

import static com.fd.stdp.common.BaseController.*;
import static com.fd.stdp.service.audit.impl.InnerAuditUtil.*;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 内审统计表2
 *@Author: wangsh
 *@Date: 2022-02-22 17:30:52
 */
public class InnerAuditStatisticsServiceImpl extends BaseServiceImpl<InnerAuditStatisticsMapper, InnerAuditStatistics> implements InnerAuditStatisticsService{

	public static final Logger logger = LoggerFactory.getLogger(InnerAuditStatisticsServiceImpl.class);
	
	@Autowired
	private InnerAuditStatisticsMapper innerAuditStatisticsMapper;
	@Autowired
	private InnerAuditOrgMapper innerAuditOrgMapper;
	@Autowired
	private BasicScienceOrgMapper basicScienceOrgMapper;
	@Autowired
	private BasicManageOrgMapper basicManageOrgMapper;
	@Autowired
	private InnerAuditStatisticsRecordMapper recordMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private SysUserUtilService sysUserUtilService;
	@Autowired
	private SysUserMapper sysUserMapper;
	@Autowired
	private SysDictItemService sysDictItemService;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新内审统计表2
	 *@param innerAuditStatistics 内审统计表2对象
	 *@return String 内审统计表2ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnerAuditStatistics(InnerAuditStatisticsVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}

		if(StringUtils.equals("1", vo.getAttr1())){
			// 省局下发的新增
			if(StringUtils.equals("1", vo.getAttr2())){
				// 省局下发处室
				vo.setFillType(FillTypeEnum.PROVINCE_OFFICE.type);
			} else {
				// 省局下发
				vo.setFillType(FillTypeEnum.PROVINCE_CITY.type);
			}
		} else {
			vo.setAttr1("0");
			if(getIsOfficeUser()){
				// 处室新增
				vo.setFillType(FillTypeEnum.OFFICE.type);
				vo.setOrgName(getLoginUser().getNickname());
			} else {
				// 普通新增
				switch (getFillType(getCurrentOrgName())) {
					case PROVINCE:
						switch (getFillType(vo.getLeadingDepartment())) {
							case PROVINCE:
								vo.setFillType(FillTypeEnum.PROVINCE.type);
								break;
							default:
								vo.setFillType(FillTypeEnum.PROVINCE_CITY.type);
						}
						break;
					case COUNTRY:
					case CITY:
						vo.setFillType(FillTypeEnum.CITY.type);
						break;
					case PROVINCE_GO:
					case PROVINCE_GO_EM:
						vo.setFillType(FillTypeEnum.PROVINCE_GOVEMENT.type);
						break;
					default:
				}
			}
		}

		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			// 新增的默认状态为0
			vo.setFlowStatus("0");
			//vo.setPersonInvolved(-1);
			innerAuditStatisticsMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innerAuditStatisticsMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除内审统计表2
	 *@param id void 内审统计表2ID
	 *@Author: wangsh
	 */
	public void deleteInnerAuditStatistics(String id) {
		//TODO 做判断后方能执行删除
		InnerAuditStatistics innerAuditStatistics=innerAuditStatisticsMapper.selectByPrimaryKey(id);
		if(innerAuditStatistics==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnerAuditStatistics teminnerAuditStatistics=new InnerAuditStatistics();
		teminnerAuditStatistics.setYn(CommonConstant.FLAG_NO);
		teminnerAuditStatistics.setId(innerAuditStatistics.getId());
		innerAuditStatisticsMapper.updateByPrimaryKeySelective(teminnerAuditStatistics);
	}

    /**
     * @Description: 批量删除内审统计表2
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnerAuditStatistics(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnerAuditStatistics(id));
	}

	@Override
	/**
	 *@Description: 查询内审统计表2详情
	 *@param id
	 *@return InnerAuditStatistics
	 *@Author: wangsh
	 */
	public InnerAuditStatistics findById(String id) {
		InnerAuditStatistics innerAuditStatistics = innerAuditStatisticsMapper.selectByPrimaryKey(id);
		InnerAuditStatisticsVo vo = new InnerAuditStatisticsVo();
		BeanUtils.copyProperties(innerAuditStatistics, vo);

		List times = new ArrayList();
		InnerAuditStatisticsRecord record = new InnerAuditStatisticsRecord();
		record.setApplyId(id);
		record.setYn(CommonConstant.FLAG_YES);
		recordMapper.select(record).stream().forEach(r->{
			times.add(r);
		});
		vo.setApplyTimeList(times);
		vo.setHasSubmitThisMonth(applyCountThisMonth(vo.getId()) < 0);
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询内审统计表2
	 *@param innerAuditStatisticsVo
	 *@return PageInfo<InnerAuditStatistics>
	 *@Author: wangsh
	 */
	public PageInfo<InnerAuditStatistics> findPageByQuery(InnerAuditStatisticsVo vo) {
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		Example example=new Example(InnerAuditStatistics.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getFlowStatus())){
			if(StringUtils.equals("0", vo.getFlowStatus())){

			} else if(StringUtils.equals("1", vo.getFlowStatus())){
				criteria.andNotEqualTo("flowStatus", FlowStatusEnum.END.getCode());
			} else if(StringUtils.equals("2", vo.getFlowStatus())){
				criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
			}
		}

		if(StringUtils.isNotBlank(vo.getLeadingDepartment())){
			criteria.andLike("leadingDepartment", "%" + vo.getLeadingDepartment() + "%");
		}

		if(StringUtils.isNotBlank(vo.getYearNo())){
			criteria.andIn("yearNo", Arrays.asList(vo.getYearNo().split(",")));
		}

		if(StringUtils.isNotBlank(vo.getFillType())){
			criteria.andIn("fillType", Arrays.asList(vo.getFillType().split(",")));
		} else {
			// 过滤处室填报的与下发处室的数据
			criteria.andLessThan("fillType", 4).andIsNull("attr2");
		}

		List<InnerAuditStatistics> innerAuditStatisticsList=innerAuditStatisticsMapper.selectByExample(example);
		// innerAuditStatisticsMapper.findPageByQuery(vo);
		return new PageInfo<InnerAuditStatistics>(innerAuditStatisticsList);
	}

	@Override
	public PageInfo<InnerAuditStatistics> findOfficePageByQuery(InnerAuditStatisticsVo vo) {
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		Example example=new Example(InnerAuditStatistics.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getFlowStatus())){
			if(StringUtils.equals("0", vo.getFlowStatus())){

			} else if(StringUtils.equals("1", vo.getFlowStatus())){
				criteria.andNotEqualTo("flowStatus", FlowStatusEnum.END.getCode());
			} else if(StringUtils.equals("2", vo.getFlowStatus())){
				criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
			}

		}
		if(StringUtils.isNotBlank(vo.getYearNo())){
			criteria.andIn("yearNo", Arrays.asList(vo.getYearNo().split(",")));
		}

		if(StringUtils.isNotBlank(vo.getFillType())){
			criteria.andIn("fillType", Arrays.asList(new String[]{"4", "5"}));
		}
		List<InnerAuditStatistics> innerAuditStatisticsList=innerAuditStatisticsMapper.selectByExample(example);
		return new PageInfo<InnerAuditStatistics>(innerAuditStatisticsList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnerAuditStatistics(InnerAuditStatisticsVo vo) {
		// 先保存当前填报信息
		String id = this.saveOrUpdateInnerAuditStatistics(vo);
		Integer operType = getOperType(getCurrentOrgName());
		if(StringUtils.equals("1", vo.getAttr1())){
			if(operType == 2) {
				// 省局提交给牵头部门填报
				vo.setFlowStatus("1");
			} else {
				// 下发提交
				int applyCount = 0;
				SysDictItem item = sysDictItemService.findDictItem("AUDIT_FIX_OPEN");
				if(item == null || item.getStatus() == 0) {
					// 每月提交一次的校验
					// 补录时不校验
					applyCount = applyCountThisMonth(vo.getId());
					if(applyCount < 0){
						throw new ServiceException("本月整改记录已提交，下月可再次提交");
					}
				}
				// 添加提交记录
				InnerAuditStatisticsRecord record = new InnerAuditStatisticsRecord();
				BeanUtils.copyProperties(vo, record);
				record.setApplyId(vo.getId());
				record.setApplyTime(new Date());
				record.setPersonInvolved(++applyCount);
				record.setId(UUIDUtils.getUUID());
				recordMapper.insertSelective(record);

				vo.setPersonInvolved(vo.getPersonInvolved()==null?0:vo.getPersonInvolved() + 1);
			}
		} else {
			// 新增提交
			vo.setFlowStatus(FlowStatusEnum.END.getCode());
			// 添加提交记录
			InnerAuditStatisticsRecord record = new InnerAuditStatisticsRecord();
			BeanUtils.copyProperties(vo, record);
			record.setApplyId(vo.getId());
			record.setApplyTime(new Date());
			record.setPersonInvolved(1);
			record.setId(UUIDUtils.getUUID());
			recordMapper.insertSelective(record);
		}

		this.mapper.updateByPrimaryKeySelective(vo);

		return id;
	}

	/**
	 * 查询本月改记录的提交次数
	 * 若未提交 返回总提交次数
	 * 若已提交 返回-1
	 * @param id
	 * @return
	 */
	private Integer applyCountThisMonth(String id){
		// 每月提交一次的校验
		Example example = new Example(InnerAuditStatisticsRecord.class);
		example.orderBy("applyTime").desc();
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
		List<InnerAuditStatisticsRecord> records = recordMapper.selectByExample(example);
		if (!CollectionUtils.isEmpty(records)) {
			InnerAuditStatisticsRecord record = records.get(0);
			Calendar calendar = Calendar.getInstance();
			int yearN = calendar.get(Calendar.YEAR);
			int monthN = calendar.get(Calendar.MONTH);
			calendar.setTime(record.getApplyTime());
			int yearA = calendar.get(Calendar.YEAR);
			int monthA = calendar.get(Calendar.MONTH);
			if (yearN == yearA && monthN == monthA) {
				return -1;
			}
			return record.getPersonInvolved();
		}
		return 0;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnerAuditStatistics(InnerAuditStatisticsVo innerAuditStatisticsVo) {
		innerAuditStatisticsVo.setFlowStatus(FlowStatusEnum.END.getCode());
		this.mapper.updateByPrimaryKeySelective(innerAuditStatisticsVo);
		return innerAuditStatisticsVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnerAuditStatistics(InnerAuditStatisticsVo innerAuditStatisticsVo) {
		innerAuditStatisticsVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
		this.mapper.updateByPrimaryKeySelective(innerAuditStatisticsVo);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnerAuditStatistics(InnerAuditStatisticsVo innerAuditStatisticsVo) {
		flowCommonService.doCompleteTask(innerAuditStatisticsVo, this.mapper
				, "内审统计表2任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnerAuditStatistics> todoList(InnerAuditStatisticsVo vo) {
		Example example = new Example(InnerAuditStatistics.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("yn", CommonConstant.FLAG_YES).andNotEqualTo("flowStatus", FlowStatusEnum.END.getCode());

		if(getIsOfficeUser()){
			String nickName = getLoginUser().getNickname();
			criteria.andCondition("((flow_status = 0 and org_name = '" + nickName + "') or (attr1 = 1 and flow_status = 1 and (LEADING_DEPARTMENT = '" + nickName + "' or org_name = '" + nickName + "')))");
		} else {
			switch (getFillType(getCurrentOrgName())) {
				case PROVINCE:
					criteria.andCondition("(attr1 = 1 or org_name = '" + getCurrentOrgName() + "')");
					break;
				default:
					criteria.andCondition("((flow_status = 0 and org_name = '" + getCurrentOrgName() + "') or (attr1 = 1 and flow_status = 1 and (LEADING_DEPARTMENT = '" + getCurrentOrgName() + "' or org_name = '" + getCurrentOrgName() + "')))");
			}
		}
//		Integer operType = getOperType();
//
//		if(operType == 2) {
//			// 省局 org_name=省局 or 下发
//			criteria.andCondition("(attr1 = 1 or org_name = '" + getCurrentOrgName() + "')");
//		} else {
//			// 非省局
//			// 下发？ -> leadName = orgname & status = 1
//			// 非下发？ -> orgName = orgName & status = 0 < 3
//			criteria.andCondition("((flow_status = 0 and org_name = '" + getCurrentOrgName() + "') or (attr1 = 1 and flow_status = 1 and (LEADING_DEPARTMENT = '" + getCurrentOrgName() + "' or org_name = '" + getCurrentOrgName() + "')))")
//					.andLessThan("personInvolved", 3);
//		}
		if(StringUtils.isNotBlank(vo.getReportSericalNumber())){
			criteria.andLike("reportSericalNumber", "%" + vo.getReportSericalNumber() + "%");
		}
		if(StringUtils.isNotBlank(vo.getFirstTitle())){
			criteria.andLike("firstTitle", "%" + vo.getFirstTitle() + "%");
		}
		if(StringUtils.isNotBlank(vo.getSecondTitle())){
			criteria.andLike("secondTitle", "%" + vo.getSecondTitle() + "%");
		}
		if(StringUtils.isNotBlank(vo.getThirdTitle())){
			criteria.andLike("thirdTitle", "%" + vo.getThirdTitle() + "%");
		}
		if(StringUtils.isNotBlank(vo.getYearNo())){
			criteria.andEqualTo("yearNo", vo.getYearNo());
		}
		if(StringUtils.isNotBlank(vo.getOrgName())){
			criteria.andEqualTo("leadingDepartment", vo.getOrgName());
		}
		if(StringUtils.isNotBlank(vo.getFillType())){
			criteria.andIn("fillType", Arrays.asList(vo.getFillType().split(",")));
		}
		example.orderBy("createTime").desc();
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}

	@Override
	public PageInfo<InnerAuditStatistics> finishedList(InnerAuditStatisticsVo vo) {
//		Example example = new Example(InnerAuditStatistics.class);
//		Criteria criteria = getCriteria(vo, example);
//		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
//		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));

		Example example = new Example(InnerAuditStatistics.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("flowStatus", 999);
		if(getIsOfficeUser()){
			criteria.andEqualTo("yn", CommonConstant.FLAG_YES)
					.andCondition("(LEADING_DEPARTMENT = '" + getLoginUser().getNickname() + "' or org_name = '" + getLoginUser().getNickname() + "')");
		} else {
			switch (getFillType(getCurrentOrgName())) {
				case PROVINCE:
					criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
					break;
				default:
					criteria.andEqualTo("yn", CommonConstant.FLAG_YES)
							.andCondition("(LEADING_DEPARTMENT = '" + getCurrentOrgName() + "' or org_name = '" + getCurrentOrgName() + "')");
			}
		}
//		Integer operType = getOperType();
//		if(operType == 2) {
//			criteria.andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", 999);
//		} else {
//			criteria.andEqualTo("yn", CommonConstant.FLAG_YES)
//					.andCondition("(LEADING_DEPARTMENT = '" + getCurrentOrgName() + "' or org_name = '" + getCurrentOrgName() + "')")
//					.andCondition("(person_involved >= 3 or flow_status = 999)");
//		}

		if(StringUtils.isNotBlank(vo.getReportSericalNumber())){
			criteria.andLike("reportSericalNumber", "%" + vo.getReportSericalNumber() + "%");
		}
		if(StringUtils.isNotBlank(vo.getFirstTitle())){
			criteria.andLike("firstTitle", "%" + vo.getFirstTitle() + "%");
		}
		if(StringUtils.isNotBlank(vo.getSecondTitle())){
			criteria.andLike("secondTitle", "%" + vo.getSecondTitle() + "%");
		}
		if(StringUtils.isNotBlank(vo.getThirdTitle())){
			criteria.andLike("thirdTitle", "%" + vo.getThirdTitle() + "%");
		}
		if(StringUtils.isNotBlank(vo.getYearNo())){
			criteria.andEqualTo("yearNo", vo.getYearNo());
		}
		if(StringUtils.isNotBlank(vo.getOrgName())){
			criteria.andEqualTo("leadingDepartment", vo.getOrgName());
		}
		if(StringUtils.isNotBlank(vo.getFillType())){
			criteria.andIn("fillType", Arrays.asList(vo.getFillType().split(",")));
		}
		example.orderBy("createTime").desc();
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}
	
	@Override
    public PageInfo<InnerAuditStatistics> endList(InnerAuditStatisticsVo vo) {
        Example example = new Example(InnerAuditStatistics.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	@Override
	public PageInfo<BasicManageOrg> applyOrg(InnerAuditStatisticsVo innerAuditStatisticsVo) {
		List<BasicManageOrg> manageOrgs = basicManageOrgMapper.selectAll();
		List<BasicScienceOrg> scienceOrgs = basicScienceOrgMapper.selectProvinceOrg();
		scienceOrgs.forEach(s->{
			BasicManageOrg basicManageOrg = new BasicManageOrg();
			BeanUtils.copyProperties(s, basicManageOrg);
			manageOrgs.add(basicManageOrg);
		});
		return new PageInfo<>(manageOrgs);
	}

	@Override
    public PageInfo applyDepartment(InnerAuditStatisticsVo innerAuditStatisticsVo) {
        PageHelper.startPage(innerAuditStatisticsVo.getPageNum(), innerAuditStatisticsVo.getPageSize());
        List<SysUserVo> list = sysUserMapper.listDepartmentRoleUsers();
        
        // 将list中的password字段设置为null
        if (list != null && !list.isEmpty()) {
            list.forEach(user -> {
                if (user != null) {
                    user.setPassword(null);
					user.setUsername(null);
					user.setPhone(null);
					user.setUsername(null);
					user.setYthUserId(null);
					user.setNickname(null);
                }
            });
        }
        
        return new PageInfo<>(list);
    }

	@Override
	public void export(InnerAuditStatisticsVo vo, HttpServletResponse response) {
		List statisticsList;
		if(StringUtils.isBlank(vo.getExportDateType())){
			vo.setExportDateType("default");
		}
		switch (vo.getExportDateType()){
			case "todo":
				vo.setPageNum(1);
				vo.setPageSize(Integer.MAX_VALUE);
				statisticsList = todoList(vo).getList();
				break;
			case "finished":
				vo.setPageNum(1);
				vo.setPageSize(Integer.MAX_VALUE);
				statisticsList = finishedList(vo).getList();
				break;
			default:

				vo.setPageNum(1);
				vo.setPageSize(Integer.MAX_VALUE);
				statisticsList = this.findPageByQuery(vo).getList();
		}
		List tmpList = new ArrayList();
		DateFormat df = new SimpleDateFormat("yyyy年MM月dd日");
		for (int i = 0; i < statisticsList.size(); i++) {
			InnerAuditStatistics statistics = (InnerAuditStatistics) statisticsList.get(i);
			InnerAuditStatisticsVo statisticsVo = new InnerAuditStatisticsVo();
			BeanUtils.copyProperties(statistics, statisticsVo);
			tmpList.add(statisticsVo);
			try {
				if(FlowStatusEnum.END.getCode().equals(statisticsVo.getFlowStatus())){
					statisticsVo.setStatusStr("已完成");
				} else {
					statisticsVo.setStatusStr("整改中");
				}
				statisticsVo.setApplyTimeStr(df.format(statistics.getApplyTime1()));
			} catch (Exception e){

			}
		}
		statisticsList = tmpList;

		String model = "";
		switch (vo.getExportType()){
			case "0":
				model = "word/数据采集表导出1.xlsx";
				break;
			case "1":
				model = "word/数据采集表导出1-1.xlsx";
				break;
			case "2":
				model = "word/数据采集表导出1-2.xlsx";
				break;
			case "3":
				model = "word/数据采集表导出1-3.xlsx";
				break;
			case "4":
				model = "word/数据采集表导出1-4.xlsx";
				break;
			case "5":
				model = "word/数据采集表导出s.xlsx";
				break;
			default:
				throw new ServiceException("未知的导出类型");
		}
		ClassPathResource classPathResource = new ClassPathResource(model);
		InputStream inputStream = null;
		try {
			inputStream = classPathResource.getInputStream();
		} catch (IOException e) {
			throw new ServiceException("加载文件失败");
		}
		EasyExcelUtils.exportTemplateFill(null, statisticsList, "Sheet1", "数据采集表.xlsx", response, inputStream, true);
	}

	private Criteria getCriteria(InnerAuditStatistics vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
		return criteria;
	}

	private Integer getOperType(String org){
		LoginUser loginUser = getLoginUser();
		Integer operType = sysUserUtilService.getUserOperType(loginUser);
		Example example = new Example(InnerAuditOrg.class);
		example.createCriteria().andEqualTo("orgName", org);
		if(CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))){
			return operType;
		}
		return 0;
	}

	private boolean getIsProOrg(String org){
		Example example = new Example(InnerAuditOrg.class);
		example.createCriteria().andEqualTo("orgName", org);
		if(CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))){
			return false;
		}
		return true;
	}

	private boolean getIsOfficeUser(){
		List<String> roles = getUserRoleList().stream().map(r->r.getRoleCode()).collect(Collectors.toList());
		boolean isOffice = false;
		for (String role:roles) {
			if(StringUtils.equals(role, Constant.PRICINCE_OFFICE)){
				isOffice = true;
				break;
			}
		}
		return isOffice;
	}

	// 0 省局 1 直属单位 2市局 3 县局 处室
	private OrgTypeEnum getFillType(String org){
		if(getIsProOrg(org)){
			if(FANGYUAN.equals(org)){
				return OrgTypeEnum.PROVINCE_GO_EM;
			}
			return OrgTypeEnum.PROVINCE_GO;
		}
		BasicManageOrg basicManageOrg = new BasicManageOrg();
		basicManageOrg.setOrgName(org);
		basicManageOrg.setYn(CommonConstant.FLAG_YES);
		List<BasicManageOrg> manageOrgs = basicManageOrgMapper.select(basicManageOrg);
		if(!CollectionUtils.isEmpty(manageOrgs)){
			BasicManageOrg b = manageOrgs.get(0);
			int orgType = sysUserUtilService.getOperTypeByAreaCode(b.getAreaCode());
			if(2 == orgType){
				return OrgTypeEnum.PROVINCE;
			} else if(1== orgType){
				return OrgTypeEnum.CITY;
			}
			return OrgTypeEnum.COUNTRY;
		}
		int orgType = getOperType(org);
		if(2 == orgType){
			return OrgTypeEnum.PROVINCE;
		} else if(1== orgType){
			return OrgTypeEnum.CITY;
		}
		return OrgTypeEnum.COUNTRY;
	}
}
