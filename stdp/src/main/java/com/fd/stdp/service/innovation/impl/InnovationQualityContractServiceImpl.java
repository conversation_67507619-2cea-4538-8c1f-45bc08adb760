package com.fd.stdp.service.innovation.impl;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;

import com.fd.stdp.beans.innovation.InnovationQualityContractProduct;
import com.fd.stdp.beans.innovation.InnovationQualityContractWorkplan;
import com.fd.stdp.beans.innovation.vo.*;
import com.fd.stdp.beans.project.vo.ProjectContractApplyVo;
import com.fd.stdp.common.BaseEntity;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.dao.innovation.InnovationQualityContractProductMapper;
import com.fd.stdp.dao.innovation.InnovationQualityContractWorkplanMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.innovation.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityContract;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationQualityContractMapper;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 省质检中心筹建任务书
 *@Author: wangsh
 *@Date: 2022-02-11 13:56:23
 */
public class InnovationQualityContractServiceImpl extends BaseServiceImpl<InnovationQualityContractMapper, InnovationQualityContract> implements InnovationQualityContractService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationQualityContractServiceImpl.class);
	
	@Autowired
	private InnovationQualityContractMapper innovationQualityContractMapper;
	@Autowired
	InnovationQualityContractProductMapper innovationQualityContractProductMapper;
	@Autowired
	InnovationQualityContractWorkplanMapper innovationQualityContractWorkplanMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;

	@Autowired
	private InnovationQualityChangeService changeService;
	@Autowired
	private InnovationQualityInterimService interimService;
	@Autowired
	private InnovationQualityReportService reportService;
	@Autowired
	private InnovationQualityReviewService reviewService;
	@Autowired
	private InnovationQualityAcceptService acceptService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新省质检中心筹建任务书
	 *@param innovationQualityContract 省质检中心筹建任务书对象
	 *@return String 省质检中心筹建任务书ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationQualityContract(InnovationQualityContractVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationQualityContractMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationQualityContractMapper.updateByPrimaryKeySelective(vo);
		}

		/**
		 * 产品范围列表
		 */
		if(!CollectionUtils.isEmpty(vo.getProductList())){
			int sort = 1;
			for (InnovationQualityContractProduct p:vo.getProductList()
				 ) {
				p.setSort(String.valueOf(sort++));
			}
			updateList(vo, vo.getProductList(), innovationQualityContractProductMapper,"setContractId");
		}

		/**
		 * 筹建工作进度
		 */
		if(!CollectionUtils.isEmpty(vo.getWorkPlans())){
			updateList(vo, vo.getWorkPlans(), innovationQualityContractWorkplanMapper,"setContractId");
		}
		/**
		 * 附件
		 */
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除省质检中心筹建任务书
	 *@param id void 省质检中心筹建任务书ID
	 *@Author: wangsh
	 */
	public void deleteInnovationQualityContract(String id) {
		//TODO 做判断后方能执行删除
		InnovationQualityContract innovationQualityContract=innovationQualityContractMapper.selectByPrimaryKey(id);
		if(innovationQualityContract==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationQualityContract teminnovationQualityContract=new InnovationQualityContract();
		teminnovationQualityContract.setYn(CommonConstant.FLAG_NO);
		teminnovationQualityContract.setId(innovationQualityContract.getId());
		innovationQualityContractMapper.updateByPrimaryKeySelective(teminnovationQualityContract);
	}

    /**
     * @Description: 批量删除省质检中心筹建任务书
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationQualityContract(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationQualityContract(id));
	}

	@Override
	/**
	 *@Description: 查询省质检中心筹建任务书详情
	 *@param id
	 *@return InnovationQualityContract
	 *@Author: wangsh
	 */
	public InnovationQualityContract findById(String id) {
		InnovationQualityContract innovationQualityContract = innovationQualityContractMapper.selectByPrimaryKey(id);
		InnovationQualityContractVo vo = new InnovationQualityContractVo();
		BeanUtils.copyProperties(innovationQualityContract, vo);

		/**
		 * 产品
		 */
		Example example = new Example(InnovationQualityContractProduct.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("contractId", id);
		vo.setProductList(innovationQualityContractProductMapper.selectByExample(example));
		example.orderBy("sort");

		/**
		 * 工作进度
		 */
		example = new Example(InnovationQualityContractWorkplan.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("contractId", id);
		example.orderBy("startTime");
		vo.setWorkPlans(innovationQualityContractWorkplanMapper.selectByExample(example));

		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询省质检中心筹建任务书
	 *@param innovationQualityContractVo
	 *@return PageInfo<InnovationQualityContract>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationQualityContract> findPageByQuery(InnovationQualityContractVo innovationQualityContractVo) {
		PageHelper.startPage(innovationQualityContractVo.getPageNum(),innovationQualityContractVo.getPageSize());
		Example example=new Example(InnovationQualityContract.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationQualityContractVo.getName())){
		//	criteria.andEqualTo(innovationQualityContractVo.getName());
		//}
		List<InnovationQualityContract> innovationQualityContractList=innovationQualityContractMapper.selectByExample(example);
		return new PageInfo<InnovationQualityContract>(innovationQualityContractList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationQualityContract(InnovationQualityContractVo innovationQualityContractVo) {
		String id = this.saveOrUpdateInnovationQualityContract(innovationQualityContractVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innovationQualityContractVo, this.mapper,
				StringUtils.isNotBlank(innovationQualityContractVo.getAuditAdvice())?innovationQualityContractVo.getAuditAdvice():"提交省质检中心筹建任务书");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationQualityContract(InnovationQualityContractVo innovationQualityContractVo) {
		InnovationQualityContractVo old = (InnovationQualityContractVo) findById(innovationQualityContractVo.getId());
		if(StringUtils.equals(old.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())){
			// 添加变更
//			InnovationQualityChangeVo changeVo = new InnovationQualityChangeVo();
//			BeanUtils.copyProperties(old, changeVo);
//			changeVo.setId(null);
//			changeVo.setApplyId(old.getApplyId());
//			changeVo.setContractId(old.getId());
//			changeVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
//			changeVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
//			changeVo.setApplyUnit(changeVo.getApplyUnitName());
//			changeService.saveOrUpdateInnovationQualityChange(changeVo);
//
//			// 添加中期检测
//			InnovationQualityInterimVo interimVo = new InnovationQualityInterimVo();
//			BeanUtils.copyProperties(old, interimVo);
//			interimVo.setId(null);
//			interimVo.setApplyId(old.getApplyId());
//			interimVo.setContractId(old.getId());
//			interimVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
//			interimVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
//			interimVo.setApplyUnit(interimVo.getApplyUnitName());
//			interimService.saveOrUpdateInnovationQualityInterim(interimVo);
//
//			// 添加年度总结
//			InnovationQualityReviewVo reviewVo = new InnovationQualityReviewVo();
//			BeanUtils.copyProperties(old, reviewVo);
//			reviewVo.setId(null);
//			reviewVo.setApplyId(old.getApplyId());
//			reviewVo.setContractId(old.getId());
//			reviewVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
//			reviewVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
//			reviewVo.setApplyUnit(reviewVo.getApplyUnitName());
//			reviewService.saveOrUpdateInnovationQualityReview(reviewVo);
//
//			// 添加事项报告
//			InnovationQualityReportVo reportVo = new InnovationQualityReportVo();
//			BeanUtils.copyProperties(old, reportVo);
//			reportVo.setId(null);
//			reportVo.setApplyId(old.getApplyId());
//			reportVo.setContractId(old.getId());
//			reportVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
//			reportVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
//			reportVo.setApplyUnit(reportVo.getApplyUnitName());
//			reportService.saveOrUpdateInnovationQualityReport(reportVo);

			// 添加验收
			InnovationQualityAcceptApplyVo acceptApplyVo = new InnovationQualityAcceptApplyVo();
			BeanUtils.copyProperties(old, acceptApplyVo);
			acceptApplyVo.setId(null);
			acceptApplyVo.setApplyId(old.getApplyId());
			acceptApplyVo.setContractId(old.getId());
			acceptApplyVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			acceptApplyVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			acceptService.saveOrUpdateInnovationQualityAcceptApply(acceptApplyVo);
		}

		flowCommonService.doFlowStepAudit(innovationQualityContractVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityContractVo.getAuditAdvice()) ? innovationQualityContractVo.getAuditAdvice() : "省质检中心筹建任务书审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationQualityContractVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationQualityContract(InnovationQualityContractVo innovationQualityContractVo) {
		flowCommonService.doFlowStepSendBack(innovationQualityContractVo, this.mapper
				, StringUtils.isNotBlank(innovationQualityContractVo.getAuditAdvice())?innovationQualityContractVo.getAuditAdvice():"省质检中心筹建任务书退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationQualityContract(InnovationQualityContractVo innovationQualityContractVo) {
		flowCommonService.doCompleteTask(innovationQualityContractVo, this.mapper
				, "省质检中心筹建任务书任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}

	@Override
	public PageInfo<InnovationQualityContract> todoList(InnovationQualityContractVo vo) {

		Example example = new Example(InnovationQualityContract.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationQualityContract> finishedList(InnovationQualityContractVo vo) {
		Example example = new Example(InnovationQualityContract.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationQualityContract> endList(InnovationQualityContractVo vo) {
		Example example = new Example(InnovationQualityContract.class);
		Criteria criteria = getCriteria(vo, example);
		criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		example.orderBy("createTime").desc();
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}

	@Override
	public PageInfo<InnovationQualityContract> changeAbleList(InnovationQualityContractVo vo) {
		Example example = new Example(InnovationQualityContract.class);
		Criteria criteria = getCriteria(vo, example);
		// 任务书已通过的可变更
		criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		// 未验收的可变更
		criteria.andCondition("id in (select contract_id from INNOVATION_QUALITY_ACCEPT_APPLY where yn = 1 and flow_status < 999)");
		example.orderBy("createTime").desc();
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}

	@Override
	public PageInfo<InnovationQualityContract> acceptedList(InnovationQualityContractVo vo) {
		Example example = new Example(InnovationQualityContract.class);
		Criteria criteria = getCriteria(vo, example);
		// 任务书已通过的可变更
		criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		// 已验收的可变更
		criteria.andCondition("id in (select contract_id from INNOVATION_QUALITY_ACCEPT_APPLY where yn = 1 and flow_status = 999)");
		example.orderBy("createTime").desc();
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}

	private Criteria getCriteria(InnovationQualityContract vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getApplyCenterName())){
			criteria.andLike("applyCenterName", "%" + vo.getApplyCenterName()+ "%");
		}
		return criteria;
	}

}
