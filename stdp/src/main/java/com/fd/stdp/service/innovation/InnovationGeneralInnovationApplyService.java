package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationGeneralInnovationApply;
import com.fd.stdp.beans.innovation.vo.InnovationGeneralInnovationApplyVo;
/**
 *@Description: 总局技术创新中心
 *@Author: wangsh
 *@Date: 2022-03-07 19:46:04
 */
public interface InnovationGeneralInnovationApplyService {

	/**
	 *@Description: 保存或更新总局技术创新中心
	 *@param innovationGeneralInnovationApply 总局技术创新中心对象
	 *@return String 总局技术创新中心ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationGeneralInnovationApply(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApply);
	
	/**
	 *@Description: 删除总局技术创新中心
	 *@param id void 总局技术创新中心ID
	 *@Author: wangsh
	 */
	void deleteInnovationGeneralInnovationApply(String id);

	/**
	 * @Description: 批量删除总局技术创新中心
	 * @param ids
	 */
    void deleteMultiInnovationGeneralInnovationApply(List<String> ids);

	/**
	 *@Description: 查询总局技术创新中心详情
	 *@param id
	 *@return InnovationGeneralInnovationApply
	 *@Author: wangsh
	 */
	InnovationGeneralInnovationApply findById(String id);

	/**
	 *@Description: 分页查询总局技术创新中心
	 *@param innovationGeneralInnovationApplyVo
	 *@return PageInfo<InnovationGeneralInnovationApply>
	 *@Author: wangsh
	 */
	PageInfo<InnovationGeneralInnovationApply> findPageByQuery(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApplyVo);
	
	
	/**
	 * 提交
	 * @param innovationGeneralInnovationApplyVo
	 * @return
	 */
    String submitInnovationGeneralInnovationApply(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApplyVo);

	/**
	 * 审核
	 * @param innovationGeneralInnovationApplyVo
	 * @return
	 */
	String auditInnovationGeneralInnovationApply(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApplyVo);

	/**
	 * 退回
	 * @param innovationGeneralInnovationApplyVo
	 * @return
	 */
	String sendBackInnovationGeneralInnovationApply(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApplyVo);

	/**
	 * 任务书下达
	 * @param innovationGeneralInnovationApplyVo
	 * @return
	 */
	String releaseInnovationGeneralInnovationApply(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApplyVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnovationGeneralInnovationApply> todoList(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApplyVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationGeneralInnovationApply> finishedList(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApplyVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationGeneralInnovationApply> endList(InnovationGeneralInnovationApplyVo innovationGeneralInnovationApplyVo);
}
