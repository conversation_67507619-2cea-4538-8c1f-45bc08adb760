package com.fd.stdp.service.project.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractRelevantUnits;
import com.fd.stdp.beans.project.vo.ProjectContractRelevantUnitsVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectContractRelevantUnitsMapper;
import com.fd.stdp.service.project.ProjectContractRelevantUnitsService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 参与单位
 *@Author: wangsh
 *@Date: 2022-01-12 14:18:16
 */
public class ProjectContractRelevantUnitsServiceImpl extends BaseServiceImpl<ProjectContractRelevantUnitsMapper, ProjectContractRelevantUnits> implements ProjectContractRelevantUnitsService{

	public static final Logger logger = LoggerFactory.getLogger(ProjectContractRelevantUnitsServiceImpl.class);
	
	@Autowired
	private ProjectContractRelevantUnitsMapper projectContractRelevantUnitsMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新参与单位
	 *@param projectContractRelevantUnits 参与单位对象
	 *@return String 参与单位ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateProjectContractRelevantUnits(ProjectContractRelevantUnits projectContractRelevantUnits) {
		if(projectContractRelevantUnits==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(projectContractRelevantUnits.getId())){
			//新增
			projectContractRelevantUnits.setId(UUIDUtils.getUUID());
			projectContractRelevantUnitsMapper.insertSelective(projectContractRelevantUnits);
		}else{
			//避免页面传入修改
			projectContractRelevantUnits.setYn(null);
			projectContractRelevantUnitsMapper.updateByPrimaryKeySelective(projectContractRelevantUnits);
		}
		return projectContractRelevantUnits.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除参与单位
	 *@param id void 参与单位ID
	 *@Author: wangsh
	 */
	public void deleteProjectContractRelevantUnits(String id) {
		//TODO 做判断后方能执行删除
		ProjectContractRelevantUnits projectContractRelevantUnits=projectContractRelevantUnitsMapper.selectByPrimaryKey(id);
		if(projectContractRelevantUnits==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		ProjectContractRelevantUnits temprojectContractRelevantUnits=new ProjectContractRelevantUnits();
		temprojectContractRelevantUnits.setYn(CommonConstant.FLAG_NO);
		temprojectContractRelevantUnits.setId(projectContractRelevantUnits.getId());
		projectContractRelevantUnitsMapper.updateByPrimaryKeySelective(temprojectContractRelevantUnits);
	}

    /**
     * @Description: 批量删除参与单位
     * @param projectContractRelevantUnitsVo
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiProjectContractRelevantUnits(ProjectContractRelevantUnitsVo projectContractRelevantUnitsVo) {
		projectContractRelevantUnitsVo.getIds().stream().forEach(id-> this.deleteProjectContractRelevantUnits(id));
	}

	@Override
	/**
	 *@Description: 查询参与单位详情
	 *@param id
	 *@return ProjectContractRelevantUnits
	 *@Author: wangsh
	 */
	public ProjectContractRelevantUnits findById(String id) {
		return projectContractRelevantUnitsMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询参与单位
	 *@param projectContractRelevantUnitsVo
	 *@return PageInfo<ProjectContractRelevantUnits>
	 *@Author: wangsh
	 */
	public PageInfo<ProjectContractRelevantUnits> findPageByQuery(ProjectContractRelevantUnitsVo projectContractRelevantUnitsVo) {
		PageHelper.startPage(projectContractRelevantUnitsVo.getPageNum(),projectContractRelevantUnitsVo.getPageSize());
		Example example=new Example(ProjectContractRelevantUnits.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(projectContractRelevantUnitsVo.getName())){
		//	criteria.andEqualTo(projectContractRelevantUnitsVo.getName());
		//}
		List<ProjectContractRelevantUnits> projectContractRelevantUnitsList=projectContractRelevantUnitsMapper.selectByExample(example);
		return new PageInfo<ProjectContractRelevantUnits>(projectContractRelevantUnitsList);
	}

}
