package com.fd.stdp.service.innovation;

import java.util.List;

import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryAcceptCommentVo;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryApplyCommentVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationLaboratoryApply;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryApplyVo;
/**
 *@Description: 省局重点实验室培育申报
 *@Author: wangsh
 *@Date: 2022-02-09 16:45:56
 */
public interface InnovationLaboratoryApplyService {

	/**
	 *@Description: 保存或更新省局重点实验室培育申报
	 *@param innovationLaboratoryApply 省局重点实验室培育申报对象
	 *@return String 省局重点实验室培育申报ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationLaboratoryApply(InnovationLaboratoryApplyVo innovationLaboratoryApply);
	
	/**
	 *@Description: 删除省局重点实验室培育申报
	 *@param id void 省局重点实验室培育申报ID
	 *@Author: wangsh
	 */
	void deleteInnovationLaboratoryApply(String id);

	/**
	 * @Description: 批量删除省局重点实验室培育申报
	 * @param ids
	 */
    void deleteMultiInnovationLaboratoryApply(List<String> ids);

	/**
	 *@Description: 查询省局重点实验室培育申报详情
	 *@param id
	 *@return InnovationLaboratoryApply
	 *@Author: wangsh
	 */
	InnovationLaboratoryApply findById(String id);

	/**
	 *@Description: 分页查询省局重点实验室培育申报
	 *@param innovationLaboratoryApplyVo
	 *@return PageInfo<InnovationLaboratoryApply>
	 *@Author: wangsh
	 */
	PageInfo<InnovationLaboratoryApply> findPageByQuery(InnovationLaboratoryApplyVo innovationLaboratoryApplyVo);

	/**
	 * 提交
	 * @param innovationLaboratoryApply
	 * @return
	 */
    String submitInnovationLaboratoryApply(InnovationLaboratoryApplyVo innovationLaboratoryApply);

	/**
	 * 审核
	 * @param innovationLaboratoryApply
	 * @return
	 */
	String auditInnovationLaboratoryApply(InnovationLaboratoryApplyVo innovationLaboratoryApply);

	/**
	 * 退回
	 * @param innovationLaboratoryApply
	 * @return
	 */
	String sendBackInnovationLaboratoryApply(InnovationLaboratoryApplyVo innovationLaboratoryApply);

	/**
	 * 专家评审
	 * @param innovationLaboratoryApplyCommentVo
	 * @return
	 */
	String expoertSubmitInnovationLaboratoryApply(InnovationLaboratoryApplyCommentVo innovationLaboratoryApplyCommentVo);

	/**
	 * 专家评审2
	 * @param innovationLaboratoryAcceptCommentVo
	 * @return
	 */
	String expoertSubmitIIInnovationLaboratoryApply(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo);

	/**
	 * 任务书下达
	 * @param innovationLaboratoryApply
	 * @return
	 */
	String releaseInnovationLaboratoryApply(InnovationLaboratoryApplyVo innovationLaboratoryApply);

	/**
	 * 待办列表
	 */
	PageInfo<InnovationLaboratoryApply> todoList(InnovationLaboratoryApplyVo innovationLaboratoryApplyVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationLaboratoryApply> finishedList(InnovationLaboratoryApplyVo innovationLaboratoryApplyVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationLaboratoryApply> endList(InnovationLaboratoryApplyVo innovationLaboratoryApplyVo);
}
