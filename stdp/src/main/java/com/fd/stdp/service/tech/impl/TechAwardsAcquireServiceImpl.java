package com.fd.stdp.service.tech.impl;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.project.ProjectContractApply;
import com.fd.stdp.beans.tech.TechAchievement;
import com.fd.stdp.beans.tech.TechAwardsAchievement;
import com.fd.stdp.beans.tech.TechAwardsAcquire;
import com.fd.stdp.beans.tech.vo.TechAchievementSellVo;
import com.fd.stdp.beans.tech.vo.TechAwardsApplyVo;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.dao.tech.TechAchievementMapper;
import com.fd.stdp.dao.tech.TechAwardsAchievementMapper;
import com.fd.stdp.dao.tech.TechAwardsApplyMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.sys.CommonUtilService;
import com.fd.stdp.service.tech.TechAwardsAchievementService;
import com.fd.stdp.util.EasyExcelUtils;
import com.fd.stdp.util.ExportUtil;
import com.github.pagehelper.PageSerializable;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.tech.vo.TechAwardsAcquireVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.tech.TechAwardsAcquireMapper;
import com.fd.stdp.service.tech.TechAwardsAcquireService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.servlet.http.HttpServletResponse;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 奖项获取
 *@Author: wangsh
 *@Date: 2022-01-04 13:49:48
 */
public class TechAwardsAcquireServiceImpl extends BaseServiceImpl<TechAwardsAcquireMapper, TechAwardsAcquire> implements TechAwardsAcquireService{

	public static final Logger logger = LoggerFactory.getLogger(TechAwardsAcquireServiceImpl.class);
	
	@Autowired
	private TechAwardsAcquireMapper techAwardsAcquireMapper;

	@Autowired
	private BasicFileAppendixService basicFileAppendixService;

	@Autowired
	private TechAwardsApplyMapper techAwardsApplyMapper;
	@Autowired
	private TechAchievementMapper techAchievementMapper;
	@Autowired
	private TechAwardsAchievementService techAwardsAchievementService;
	@Autowired
	private TechAwardsAchievementMapper techAwardsAchievementMapper;

	@Autowired
	private CommonUtilService commonUtilService;
	@Autowired
	private FlowCommonService flowCommonService;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新奖项获取
	 *@param techAwardsAcquire 奖项获取对象
	 *@return String 奖项获取ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTechAwardsAcquire(TechAwardsAcquireVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isBlank(vo.getId())){
			//新增
			// 完善申报单位信息
			if(vo.getApplyUnitName() == null) {
				BasicScienceOrg basicScienceOrg = commonUtilService.getLoginBasicScienceOrg();
				if (basicScienceOrg != null) {
					vo.setApplyUnitId(basicScienceOrg.getId());
					vo.setApplyUnitName(basicScienceOrg.getOrgName());
				}
			}
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			techAwardsAcquireMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			techAwardsAcquireMapper.updateByPrimaryKeySelective(vo);
		}
		// 附件
		basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());

		// 关联成果
		if(!CollectionUtils.isEmpty(vo.getAchievements())) {
			techAwardsAchievementService.clearByAwardId(vo.getId());
			vo.getAchievements().forEach(a->{
				TechAwardsAchievement techAwardsAchievement = new TechAwardsAchievement();
				techAwardsAchievement.setAchievementId(a.getId());
				techAwardsAchievement.setAwordId(vo.getId());
				techAwardsAchievementService.saveOrUpdateTechAwardsAchievement(techAwardsAchievement);
			});
		}

		// 流程
		// flowCommonService.doFlowStart(FlowableConstant.FLOW_TECH_ACHIEVEMENT, vo, this.mapper, "开始奖项获取流程");

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除奖项获取
	 *@param id void 奖项获取ID
	 *@Author: wangsh
	 */
	public void deleteTechAwardsAcquire(String id) {
		//TODO 做判断后方能执行删除
		TechAwardsAcquire techAwardsAcquire=techAwardsAcquireMapper.selectByPrimaryKey(id);
		if(techAwardsAcquire==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TechAwardsAcquire temtechAwardsAcquire=new TechAwardsAcquire();
		temtechAwardsAcquire.setYn(CommonConstant.FLAG_NO);
		temtechAwardsAcquire.setId(techAwardsAcquire.getId());
		techAwardsAcquireMapper.updateByPrimaryKeySelective(temtechAwardsAcquire);
	}

	@Override
	/**
	 *@Description: 查询奖项获取详情
	 *@param id
	 *@return TechAwardsAcquire
	 *@Author: wangsh
	 */
	public TechAwardsAcquireVo findById(String id) {
		TechAwardsAcquire techAwardsAcquire = techAwardsAcquireMapper.selectByPrimaryKey(id);
		TechAwardsAcquireVo vo = new TechAwardsAcquireVo();
		BeanUtils.copyProperties(techAwardsAcquire, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(vo.getId()));

		// 关联成果
		Example example = new Example(TechAwardsAchievement.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("awordId", id);
		List<TechAwardsAchievement> techAwardsAchievements = techAwardsAchievementMapper.selectByExample(example);
		if(!CollectionUtils.isEmpty(techAwardsAchievements)){
			List ids = new ArrayList();
			techAwardsAchievements.forEach(techAwardsAchievement -> ids.add(techAwardsAchievement.getAchievementId()));
			example = new Example(TechAchievement.class);
			example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andIn("id", ids);
			vo.setAchievements(techAchievementMapper.selectByExample(example));
		}
		else {
			vo.setAchievements(new ArrayList<>());
		}

		// 流程
		if(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getFlowStatus())) {
			vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		}
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询奖项获取
	 *@param techAwardsAcquireVo
	 *@return PageInfo<TechAwardsAcquire>
	 *@Author: wangsh
	 */
	public PageInfo<TechAwardsAcquire> findPageByQuery(TechAwardsAcquireVo techAwardsAcquireVo) {
		PageHelper.startPage(techAwardsAcquireVo.getPageNum(),techAwardsAcquireVo.getPageSize());
		Example example=new Example(TechAwardsAcquire.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(techAwardsAcquireVo.getName())){
		//	criteria.andEqualTo(techAwardsAcquireVo.getName());
		//}
		List<TechAwardsAcquire> techAwardsAcquireList=techAwardsAcquireMapper.selectByExample(example);
		return new PageInfo<TechAwardsAcquire>(techAwardsAcquireList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitTechAwardsAcquire(TechAwardsAcquireVo techAwardsAcquireVo) {
		String id = this.saveOrUpdateTechAwardsAcquire(techAwardsAcquireVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, techAwardsAcquireVo, this.mapper,
				org.apache.commons.lang3.StringUtils.isNotBlank(techAwardsAcquireVo.getAuditAdvice())?techAwardsAcquireVo.getAuditAdvice():"提交成果统计申请"
				);
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditTechAwardsAcquire(TechAwardsAcquireVo techAwardsAcquireVo) {
		String id = techAwardsAcquireVo.getId();

		flowCommonService.doFlowStepAudit(techAwardsAcquireVo, this.mapper
				, org.apache.commons.lang3.StringUtils.isNotBlank(techAwardsAcquireVo.getAuditAdvice())?techAwardsAcquireVo.getAuditAdvice():"成果统计审核通过"
				, FlowStatusEnum.END.getCode());
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackTechAwardsAcquire(TechAwardsAcquireVo techAwardsAcquireVo) {
		String id = techAwardsAcquireVo.getId();
		flowCommonService.doFlowStepSendBack(techAwardsAcquireVo, this.mapper
				, org.apache.commons.lang3.StringUtils.isNotBlank(techAwardsAcquireVo.getAuditAdvice())?techAwardsAcquireVo.getAuditAdvice():"成果统计退回"
				, techAwardsAcquireVo.getToUpper()!=null?techAwardsAcquireVo.getToUpper():false
		);
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTechAwardsAcquire(List<String> ids) {
		if(!CollectionUtils.isEmpty(ids)){
			ids.stream().forEach(id -> this.deleteTechAwardsAcquire(id));
		}
	}

	@Override
	public PageInfo<TechAwardsAcquire> todoList(TechAwardsAcquireVo techAwardsAcquireVo) {
		Example example = new Example(TechAwardsAcquire.class);
		Criteria criteria = getCriteria(techAwardsAcquireVo, example);
		return new PageInfo<>(flowCommonService.todoList(techAwardsAcquireVo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TechAwardsAcquire> finishedList(TechAwardsAcquireVo techAwardsAcquireVo) {
		Example example = new Example(TechAwardsAcquire.class);
		Criteria criteria = getCriteria(techAwardsAcquireVo, example);
		return new PageInfo<>(flowCommonService.finishedList(techAwardsAcquireVo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<Object> endList(TechAwardsAcquireVo vo) {
		Example example = new Example(TechAwardsAcquire.class);
		Criteria criteria = getCriteria(vo, example);
		return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
	}

	private Criteria getCriteria(TechAwardsAcquireVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getApplyAwardName())){
			criteria.andLike("applyAwardName", "%" + vo.getApplyAwardName()+ "%");
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getApplyProjectName())){
			criteria.andLike("applyProjectName", "%" + vo.getApplyProjectName()+ "%");
		}
		if(StringUtils.isNotBlank(vo.getLeaderName())){
			criteria.andLike("leaderName", "%" + vo.getLeaderName()+ "%");
			// criteria.andCondition("id in (select form_id from basic_person_linked b where b.form_id = id and b.name like '%" + vo.getLeaderName() + "%;)")
		}
		if(StringUtils.isNotBlank(vo.getTypeCode())){
			criteria.andEqualTo("typeCode", vo.getTypeCode());
		}
		if(StringUtils.isNotBlank(vo.getTypeSecondCode())){
			criteria.andEqualTo("typeSecondCode", vo.getTypeSecondCode());
		}
		if(null != vo.getStartTime()){
			criteria.andGreaterThanOrEqualTo("achievementTime", vo.getStartTime());
		}
		if(null != vo.getEndTime()){
			criteria.andLessThanOrEqualTo("achievementTime", new Date(vo.getEndTime().getTime() + 24*3600*1000L-1));
		}
		example.orderBy("createTime").desc();
		return criteria;
	}

	@Override
	public void exportTechAchievement(TechAwardsAcquireVo vo, HttpServletResponse response) {
		vo.setPageNum(1);
		vo.setPageSize(Integer.MAX_VALUE);
		List exportList = null;
		if(StringUtils.isNotBlank(vo.getExportType())) {
			switch (vo.getExportType()) {
				case "todo":
					exportList = todoList(vo).getList();
					break;
				case "finished":
					exportList = finishedList(vo).getList();
					break;
				case "end":
					exportList = endList(vo).getList();
					break;
				default:
					exportList = findPageByQuery(vo).getList();
			}
		}
		if(exportList == null){
			exportList = findPageByQuery(vo).getList();
		}

		InputStream inputStream = ExportUtil.getInputStream("获奖备案导出.xlsx");
		EasyExcelUtils.exportTemplateFill(null, exportList, "Sheet1", "获奖备案.xlsx", response, inputStream, true);
	}
}
