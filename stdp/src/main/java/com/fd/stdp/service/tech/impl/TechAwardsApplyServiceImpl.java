package com.fd.stdp.service.tech.impl;

import java.io.InputStream;
import java.util.*;

import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.project.ProjectContractApply;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.beans.tech.TechAchievement;
import com.fd.stdp.beans.tech.TechAwardsAchievement;
import com.fd.stdp.beans.tech.TechAwardsApply;
import com.fd.stdp.beans.tech.vo.TechAchievementVo;
import com.fd.stdp.beans.tech.vo.TechAwardsAcquireVo;
import com.fd.stdp.beans.tech.vo.TechAwardsApplyVo;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.constant.ProcessConstants;
import com.fd.stdp.dao.tech.TechAchievementMapper;
import com.fd.stdp.dao.tech.TechAwardsAchievementMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.basic.BasicPersonLinkedService;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.sys.CommonUtilService;
import com.fd.stdp.service.user.SysUserUtilService;
import com.fd.stdp.util.EasyExcelUtils;
import com.fd.stdp.util.ExportUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.tech.TechAwardsApplyMapper;
import com.fd.stdp.service.tech.TechAwardsApplyService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.servlet.http.HttpServletResponse;

import static com.fd.stdp.common.BaseController.*;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 奖项申报
 *@Author: wangsh
 *@Date: 2022-01-04 13:49:20
 */
public class TechAwardsApplyServiceImpl extends BaseServiceImpl<TechAwardsApplyMapper, TechAwardsApply> implements TechAwardsApplyService{

	public static final Logger logger = LoggerFactory.getLogger(TechAwardsApplyServiceImpl.class);
	
	@Autowired
	private TechAwardsApplyMapper techAwardsApplyMapper;
	@Autowired
	private TechAwardsAchievementMapper techAwardsAchievementMapper;
	@Autowired
	private TechAchievementMapper techAchievementMapper;

	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	@Autowired
	private BasicPersonLinkedService basicPersonLinkedService;

	@Autowired
	private CommonUtilService commonUtilService;

	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private FlowApiService flowApiService;
	@Autowired
	private SysUserUtilService sysUserUtilService;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新奖项申报
	 *@param techAwardsApply 奖项申报对象
	 *@return String 奖项申报ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTechAwardsApply(TechAwardsApplyVo techAwardsApplyVo) {
		if(techAwardsApplyVo==null){
			throw new ServiceException("数据异常");
		}

		// 设置负责人
		if(techAwardsApplyVo.getPersons() != null){
			techAwardsApplyVo.getPersons().forEach(person -> {
				if("项目负责人".equals(person.getPersonType())){
					techAwardsApplyVo.setLeaderName(person.getName());
				}
			});
		}

		if(StringUtils.isEmpty(techAwardsApplyVo.getId())){
			//新增
			// 完善申报单位信息
			if(techAwardsApplyVo.getApplyUnitName() == null) {
				BasicScienceOrg basicScienceOrg = commonUtilService.getLoginBasicScienceOrg();
				if (basicScienceOrg != null) {
					techAwardsApplyVo.setApplyUnitId(basicScienceOrg.getId());
					techAwardsApplyVo.setApplyUnitName(basicScienceOrg.getOrgName());
				}
			}
			techAwardsApplyVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			techAwardsApplyVo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			techAwardsApplyVo.setId(UUIDUtils.getUUID());
			techAwardsApplyMapper.insertSelective(techAwardsApplyVo);
		}else{
			//避免页面传入修改
			techAwardsApplyVo.setYn(null);
			techAwardsApplyMapper.updateByPrimaryKeySelective(techAwardsApplyVo);
		}

		// 成果
		if(techAwardsApplyVo.getAchievements() != null){
			techAwardsAchievementMapper.clearByAwardId(techAwardsApplyVo.getId());
			techAwardsApplyVo.getAchievements().stream().forEach(techAchievement -> {
				TechAwardsAchievement techAwardsAchievement = new TechAwardsAchievement();
				techAwardsAchievement.setAwordId(techAwardsApplyVo.getId());
				techAwardsAchievement.setAchievementId(techAchievement.getId());
				techAwardsAchievement.setId(UUIDUtils.getUUID());
				techAwardsAchievementMapper.insertSelective(techAwardsAchievement);
			});
		}

		// 附件
		basicFileAppendixService.clearAndUpdateBasicFileAppendix(techAwardsApplyVo.getId(), techAwardsApplyVo.getFiles());
		// 人员
		basicPersonLinkedService.clearAndUpdateBasicPersonLinked(techAwardsApplyVo.getId(), techAwardsApplyVo.getPersons());
		// flowCommonService.doFlowStart(FlowableConstant.FLOW_TECH_ACHIEVEMENT, techAwardsApplyVo, this.mapper, "开始奖项申报流程");
		// 不走流程 直接设置end
		// techAwardsApplyVo.setFlowStatus(FlowStatusEnum.END.getCode());
		// techAwardsApplyMapper.updateByPrimaryKeySelective(techAwardsApplyVo);

		return techAwardsApplyVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除奖项申报
	 *@param id void 奖项申报ID
	 *@Author: wangsh
	 */
	public void deleteTechAwardsApply(String id) {
		//TODO 做判断后方能执行删除
		TechAwardsApply techAwardsApply=techAwardsApplyMapper.selectByPrimaryKey(id);
		if(techAwardsApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TechAwardsApply temtechAwardsApply=new TechAwardsApply();
		temtechAwardsApply.setYn(CommonConstant.FLAG_NO);
		temtechAwardsApply.setId(techAwardsApply.getId());
		techAwardsApplyMapper.updateByPrimaryKeySelective(temtechAwardsApply);
	}

	@Override
	/**
	 *@Description: 查询奖项申报详情
	 *@param id
	 *@return TechAwardsApply
	 *@Author: wangsh
	 */
	public TechAwardsApplyVo findById(String id) {
		TechAwardsApply techAwardsApply = techAwardsApplyMapper.selectByPrimaryKey(id);
		TechAwardsApplyVo vo = new TechAwardsApplyVo();
		BeanUtils.copyProperties(techAwardsApply, vo);

		// 附件
		vo.setFiles(basicFileAppendixService.findByFormId(vo.getId()));
		// 人员
		vo.setPersons(basicPersonLinkedService.findByFormId(vo.getId()));
		// 关联成果
		Example example = new Example(TechAwardsAchievement.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("awordId", id);
		List<TechAwardsAchievement> techAwardsAchievements = techAwardsAchievementMapper.selectByExample(example);
		if(!CollectionUtils.isEmpty(techAwardsAchievements)){
			List ids = new ArrayList();
			techAwardsAchievements.forEach(techAwardsAchievement -> ids.add(techAwardsAchievement.getAchievementId()));
			example = new Example(TechAchievement.class);
			example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andIn("id", ids);
			vo.setAchievements(techAchievementMapper.selectByExample(example));
		}

		// 流程
		if(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getFlowStatus())) {
			vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		}
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询奖项申报
	 *@param techAwardsApplyVo
	 *@return PageInfo<TechAwardsApply>
	 *@Author: wangsh
	 */
	public PageInfo<TechAwardsApply> findPageByQuery(TechAwardsApplyVo techAwardsApplyVo) {
		PageHelper.startPage(techAwardsApplyVo.getPageNum(),techAwardsApplyVo.getPageSize());
		Example example=new Example(TechAwardsApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(techAwardsApplyVo.getApplyUnitName())){
			criteria.andLike("applyUnitName", "%" + techAwardsApplyVo.getApplyUnitName() + "%");
		}
		if(StringUtils.isNotBlank(techAwardsApplyVo.getApplyProjectName())){
			criteria.andLike("applyProjectName", "%" + techAwardsApplyVo.getApplyProjectName() + "%");
		}
		if(StringUtils.isNotBlank(techAwardsApplyVo.getApplyAwardName())){
			criteria.andLike("applyAwardName", "%" + techAwardsApplyVo.getApplyAwardName() + "%");
		}
		if(StringUtils.isNotBlank(techAwardsApplyVo.getTypeCode())){
			criteria.andEqualTo("typeCode", techAwardsApplyVo.getTypeCode());
		}
		if(StringUtils.isNotBlank(techAwardsApplyVo.getTypeSecondCode())){
			criteria.andEqualTo("typeSecondCode", techAwardsApplyVo.getTypeSecondCode());
		}
		if(techAwardsApplyVo.getStartTime() != null){
			criteria.andGreaterThanOrEqualTo("applyTime", techAwardsApplyVo.getStartTime());
		}
		if(techAwardsApplyVo.getEndTime() != null){
			criteria.andLessThanOrEqualTo("applyTime", techAwardsApplyVo.getEndTime());
		}
		List<TechAwardsApply> techAwardsApplyList=techAwardsApplyMapper.selectByExample(example);
		return new PageInfo<TechAwardsApply>(techAwardsApplyList);
	}


	@Override
	@Transactional(readOnly = false)
	public String submitTechAwardsApply(TechAwardsApplyVo techAwardsApplyVo) {
		String id = this.saveOrUpdateTechAwardsApply(techAwardsApplyVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_AWARD_APPLY, techAwardsApplyVo, this.mapper,
				org.apache.commons.lang3.StringUtils.isNotBlank(techAwardsApplyVo.getAuditAdvice())?techAwardsApplyVo.getAuditAdvice():"提交报奖备案申请");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditTechAwardsApply(TechAwardsApplyVo techAwardsApplyVo) {
		String id = techAwardsApplyVo.getId();
		TechAwardsApplyVo old = findById(id);

		String taskId = flowApiService.getTaskId(old.getId());
		if(taskId == null) {
			throw  new ServiceException("未找到流程");
		}
		Map<String, Object> map = new HashMap<String, Object>();
		FlowTaskVo flowTaskVo = new FlowTaskVo();
		flowTaskVo.setTaskId(taskId);
		flowTaskVo.setBusinessKey(id);
		flowTaskVo.setUserId(getCurrentUserId());
		flowTaskVo.setUserName(getCurrentUserName());
		flowTaskVo.setComment(StringUtils.isNotBlank(techAwardsApplyVo.getAuditAdvice())?techAwardsApplyVo.getAuditAdvice():"审核通过");

		Integer operType = sysUserUtilService.getUserOperType(getLoginUser());
		if(StringUtils.equals(old.getFlowStatus(), FlowStatusEnum.ORG_AUDIT.getCode())) {
			// 如果当前是是申请单位审核，设置下一级为相同等级
			switch (operType){
				case 0:
					// 设置下一级为县级
					flowTaskVo.setAssignee(AssigneeConstant.DEPT_COUNTY_ROLE);
					old.setFlowStatus(FlowStatusEnum.COUNTY_AUDIT.getCode());
					break;
				case 1:
					// 设置下一级为市级
					flowTaskVo.setAssignee(AssigneeConstant.DEPT_CITY_ROLE);
					old.setFlowStatus(FlowStatusEnum.CITY_AUDIT.getCode());
					break;
				case 2:
					// 无省级 直接结束
					flowTaskVo.setAssignee(AssigneeConstant.DEPT_PROVINCE_ROLE);
					old.setFlowStatus(FlowStatusEnum.END.getCode());
					break;
				default:
			}
		} else {
			// 否则设置下一级
			switch (operType) {
				case 0:
					// 县级审核  设置下一级为市级
					flowTaskVo.setAssignee(AssigneeConstant.DEPT_CITY_ROLE);
					old.setFlowStatus(FlowStatusEnum.CITY_AUDIT.getCode());
					break;
				case 1:
					// 市级审核
					flowTaskVo.setAssignee(AssigneeConstant.DEPT_PROVINCE_ROLE);
					old.setFlowStatus(FlowStatusEnum.END.getCode());
					break;
				case 2:
					// 无省级 直接结束
					flowTaskVo.setAssignee(AssigneeConstant.DEPT_PROVINCE_ROLE);
					old.setFlowStatus(FlowStatusEnum.END.getCode());
					break;
				default:
			}
		}
		map.put("CODETYPE", operType);
		map.put("ISPASS", 1);
		map.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());
		flowTaskVo.setValues(map);

		old.setFlowUser(flowTaskVo.getAssignee());
		this.mapper.updateByPrimaryKeySelective(old);
		flowApiService.completeTask(flowTaskVo);

//		flowCommonService.doFlowStepAudit(techAwardsApplyVo, this.mapper
//				, org.apache.commons.lang3.StringUtils.isNotBlank(techAwardsApplyVo.getAuditAdvice())?techAwardsApplyVo.getAuditAdvice():"报奖备案审核通过"
//				, FlowStatusEnum.END.getCode());
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackTechAwardsApply(TechAwardsApplyVo techAwardsApplyVo) {
		String id = techAwardsApplyVo.getId();
		flowCommonService.doFlowStepSendBack(techAwardsApplyVo, this.mapper
				, org.apache.commons.lang3.StringUtils.isNotBlank(techAwardsApplyVo.getAuditAdvice())?techAwardsApplyVo.getAuditAdvice():"报奖备案退回"
				, techAwardsApplyVo.getToUpper()!=null?techAwardsApplyVo.getToUpper():false
		);
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTechAwardsApply(List<String> ids) {
		if(!CollectionUtils.isEmpty(ids)){
			ids.stream().forEach(id -> this.deleteTechAwardsApply(id));
		}
	}

	@Override
	public PageInfo<TechAwardsApply> todoList(TechAwardsApplyVo techAwardsApplyVo) {
		Example example = new Example(TechAwardsApply.class);
		Criteria criteria = getCriteria(techAwardsApplyVo, example);
		return new PageInfo<>(flowCommonService.todoList(techAwardsApplyVo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TechAwardsApply> finishedList(TechAwardsApplyVo techAwardsApplyVo) {
		Example example = new Example(TechAwardsApply.class);
		Criteria criteria = getCriteria(techAwardsApplyVo, example);
		return new PageInfo<>(flowCommonService.finishedList(techAwardsApplyVo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo endList(TechAwardsApplyVo techAwardsApplyVo) {
		PageHelper.startPage(techAwardsApplyVo.getPageNum(),techAwardsApplyVo.getPageSize());
		Example example=new Example(TechAwardsApply.class);
		Criteria criteria= getCriteria(techAwardsApplyVo, example);

		return new PageInfo<>(flowCommonService.endList(techAwardsApplyVo, this.mapper, example, criteria));
//		criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
//		//查询条件
//		//if(!StringUtils.isEmpty(techAwardsApplyVo.getName())){
//		//	criteria.andEqualTo(techAwardsApplyVo.getName());
//		//}
//		List<TechAwardsApply> techAwardsApplyList=techAwardsApplyMapper.selectByExample(example);
//		List<TechAwardsApply> techAwardsApplyVos = new ArrayList<>();
//		techAwardsApplyList.stream().forEach(techAwardsApply -> {
//			techAwardsApplyVos.add(this.findById(techAwardsApply.getId()));
//		});
//		return new PageInfo<>(techAwardsApplyVos);
	}

	private Criteria getCriteria(TechAwardsApplyVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getApplyAwardName())){
			criteria.andLike("applyAwardName", "%" + vo.getApplyAwardName()+ "%");
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getApplyProjectName())){
			criteria.andLike("applyProjectName", "%" + vo.getApplyProjectName()+ "%");
		}
		if(StringUtils.isNotBlank(vo.getLeaderName())){
			criteria.andLike("leaderName", "%" + vo.getLeaderName()+ "%");
			// criteria.andCondition("id in (select form_id from basic_person_linked b where b.form_id = id and b.name like '%" + vo.getLeaderName() + "%;)")
		}
		if(StringUtils.isNotBlank(vo.getTypeCode())){
			criteria.andEqualTo("typeCode", vo.getTypeCode());
		}
		if(StringUtils.isNotBlank(vo.getTypeSecondCode())){
			criteria.andEqualTo("typeSecondCode", vo.getTypeSecondCode());
		}
		if(null != vo.getStartTime()){
			criteria.andGreaterThanOrEqualTo("applyTime", vo.getStartTime());
		}
		if(null != vo.getEndTime()){
			criteria.andLessThanOrEqualTo("applyTime", new Date(vo.getEndTime().getTime() + 24*3600*1000L-1));
		}
		example.orderBy("createTime").desc();
		return criteria;
	}

	@Override
	public void exportTechAchievement(TechAwardsApplyVo vo, HttpServletResponse response) {
		vo.setPageNum(1);
		vo.setPageSize(Integer.MAX_VALUE);
		List exportList = null;
		if(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getExportType())) {
			switch (vo.getExportType()) {
				case "todo":
					exportList = todoList(vo).getList();
					break;
				case "finished":
					exportList = finishedList(vo).getList();
					break;
				case "end":
					//exportList = end(vo).getList();
					//break;
				default:
					exportList = findPageByQuery(vo).getList();
			}
		}
		if(exportList == null){
			exportList = findPageByQuery(vo).getList();
		}

		InputStream inputStream = ExportUtil.getInputStream("报奖备案导出.xlsx");
		EasyExcelUtils.exportTemplateFill(null, exportList, "Sheet1", "报奖备案.xlsx", response, inputStream, true);
	}
}
