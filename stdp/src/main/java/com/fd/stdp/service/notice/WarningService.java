package com.fd.stdp.service.notice;

import com.fd.stdp.beans.notice.NoticeApply;
import com.fd.stdp.beans.notice.WarningBean;
import com.fd.stdp.beans.notice.vo.NoticeApplyVo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 *@Description: 
 *@Author: wangsh
 *@Date: 2022-03-15 09:45:58
 */
public interface WarningService {

	PageInfo<WarningBean> findProjectWarning(WarningBean warning);

    PageInfo<WarningBean> findInnovationWarning(WarningBean warning);

    PageInfo<WarningBean> findTalentWarning(WarningBean warning);
}
