package com.fd.stdp.service.basic.impl;

import java.util.ArrayList;
import java.util.List;

import com.fd.stdp.beans.basic.BasicGradeLinked;
import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicGradeLinked;
import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicGradeLinkedMapper;
import com.fd.stdp.service.basic.BasicGradeLinkedService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 评审意见关联表
 *@Author: wangsh
 *@Date: 2022-02-15 11:26:40
 */
public class BasicGradeLinkedServiceImpl extends BaseServiceImpl<BasicGradeLinkedMapper, BasicGradeLinked> implements BasicGradeLinkedService{

	public static final Logger logger = LoggerFactory.getLogger(BasicGradeLinkedServiceImpl.class);
	
	@Autowired
	private BasicGradeLinkedMapper basicGradeLinkedMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新评审意见关联表
	 *@param basicGradeLinked 评审意见关联表对象
	 *@return String 评审意见关联表ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateBasicGradeLinked(BasicGradeLinkedVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			basicGradeLinkedMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			basicGradeLinkedMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除评审意见关联表
	 *@param id void 评审意见关联表ID
	 *@Author: wangsh
	 */
	public void deleteBasicGradeLinked(String id) {
		//TODO 做判断后方能执行删除
		BasicGradeLinked basicGradeLinked=basicGradeLinkedMapper.selectByPrimaryKey(id);
		if(basicGradeLinked==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		BasicGradeLinked tembasicGradeLinked=new BasicGradeLinked();
		tembasicGradeLinked.setYn(CommonConstant.FLAG_NO);
		tembasicGradeLinked.setId(basicGradeLinked.getId());
		basicGradeLinkedMapper.updateByPrimaryKeySelective(tembasicGradeLinked);
	}

    /**
     * @Description: 批量删除评审意见关联表
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiBasicGradeLinked(List<String> ids) {
		ids.stream().forEach(id-> this.deleteBasicGradeLinked(id));
	}

	@Override
	/**
	 *@Description: 查询评审意见关联表详情
	 *@param id
	 *@return BasicGradeLinked
	 *@Author: wangsh
	 */
	public BasicGradeLinked findById(String id) {
		return basicGradeLinkedMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询评审意见关联表
	 *@param basicGradeLinkedVo
	 *@return PageInfo<BasicGradeLinked>
	 *@Author: wangsh
	 */
	public PageInfo<BasicGradeLinked> findPageByQuery(BasicGradeLinkedVo basicGradeLinkedVo) {
		PageHelper.startPage(basicGradeLinkedVo.getPageNum(),basicGradeLinkedVo.getPageSize());
		Example example=new Example(BasicGradeLinked.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(basicGradeLinkedVo.getName())){
		//	criteria.andEqualTo(basicGradeLinkedVo.getName());
		//}
		List<BasicGradeLinked> basicGradeLinkedList=basicGradeLinkedMapper.selectByExample(example);
		return new PageInfo<BasicGradeLinked>(basicGradeLinkedList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitBasicGradeLinked(BasicGradeLinkedVo basicGradeLinkedVo) {
		String id = this.saveOrUpdateBasicGradeLinked(basicGradeLinkedVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_INNOVATION_APPLY, basicGradeLinkedVo, this.mapper,
				StringUtils.isNotBlank(basicGradeLinkedVo.getAuditAdvice())?basicGradeLinkedVo.getAuditAdvice():"提交评审意见关联表");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditBasicGradeLinked(BasicGradeLinkedVo basicGradeLinkedVo) {
		flowCommonService.doFlowStepAudit(basicGradeLinkedVo, this.mapper
				, StringUtils.isNotBlank(basicGradeLinkedVo.getAuditAdvice()) ? basicGradeLinkedVo.getAuditAdvice() : "评审意见关联表审核通过"
				, FlowStatusEnum.CHOOSE_EXPERTS.getCode());
		return basicGradeLinkedVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackBasicGradeLinked(BasicGradeLinkedVo basicGradeLinkedVo) {
		flowCommonService.doFlowStepSendBack(basicGradeLinkedVo, this.mapper
				, StringUtils.isNotBlank(basicGradeLinkedVo.getAuditAdvice())?basicGradeLinkedVo.getAuditAdvice():"评审意见关联表退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseBasicGradeLinked(BasicGradeLinkedVo basicGradeLinkedVo) {
		flowCommonService.doCompleteTask(basicGradeLinkedVo, this.mapper
				, "评审意见关联表任务书下达完成"
				, FlowStatusEnum.PROJECT_RELEASE.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<BasicGradeLinked> todoList(BasicGradeLinkedVo vo) {

		Example example = new Example(BasicGradeLinked.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<BasicGradeLinked> finishedList(BasicGradeLinkedVo vo) {
		Example example = new Example(BasicGradeLinked.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<BasicGradeLinked> endList(BasicGradeLinkedVo vo) {
        Example example = new Example(BasicGradeLinked.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(BasicGradeLinked vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
		return criteria;
	}

	@Override
	public List<BasicGradeLinkedVo> findByFormId(String formId) {
		Example example = new Example(BasicGradeLinked.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("formId", formId);
		example.orderBy("sort");
		List<BasicGradeLinked> BasicGradeLinkeds = this.mapper.selectByExample(example);
		List<BasicGradeLinkedVo> BasicGradeLinkedVos = new ArrayList<>();
		for (BasicGradeLinked b:BasicGradeLinkeds) {
			BasicGradeLinkedVo linkedVo = new BasicGradeLinkedVo();
			BeanUtils.copyProperties(b, linkedVo);
			BasicGradeLinkedVos.add(linkedVo);
		}
		return BasicGradeLinkedVos;
	}

	@Override
	public void clearAndUpdateBasicGradeLinked(String formId, List<BasicGradeLinkedVo> BasicGradeLinkedVoList) {
		if(StringUtils.isBlank(formId)){
			throw new ServiceException("未保存的表单无法关联人员");
		}
		this.mapper.clearByFormId(formId);
		int sort = 1;
		if (!CollectionUtils.isEmpty(BasicGradeLinkedVoList)) {
			for (BasicGradeLinked b:BasicGradeLinkedVoList) {
				b.setSort(sort++);
				if(b.getFormId() != null) {
					b.setYn(CommonConstant.FLAG_YES);
					this.mapper.updateByPrimaryKey(b);
				} else {
					b.setFormId(formId);
					b.setId(UUIDUtils.getUUID());
					this.mapper.insertSelective(b);
				}
			}
		}
	}

	@Override
	public Boolean formGradeAllFinished(String formId) {
		BasicGradeLinked gradeLinked = new BasicGradeLinked();
		gradeLinked.setFormId(formId);
		gradeLinked.setYn(CommonConstant.FLAG_YES);
		List<BasicGradeLinked> linkeds = this.mapper.select(gradeLinked);
		if(!CollectionUtils.isEmpty(linkeds)){
			for (BasicGradeLinked b:linkeds) {
				if(b.getResultType() == null){
					return false;
				}
			}
		}
		return true;
	}
}
