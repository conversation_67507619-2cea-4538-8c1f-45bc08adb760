package com.fd.stdp.service.project.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectContractCost;
import com.fd.stdp.beans.project.vo.ProjectContractCostVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectContractCostMapper;
import com.fd.stdp.service.project.ProjectContractCostService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 经费预算
 *@Author: wangsh
 *@Date: 2022-01-12 14:18:49
 */
public class ProjectContractCostServiceImpl extends BaseServiceImpl<ProjectContractCostMapper, ProjectContractCost> implements ProjectContractCostService{

	public static final Logger logger = LoggerFactory.getLogger(ProjectContractCostServiceImpl.class);
	
	@Autowired
	private ProjectContractCostMapper projectContractCostMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新经费预算
	 *@param projectContractCost 经费预算对象
	 *@return String 经费预算ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateProjectContractCost(ProjectContractCost projectContractCost) {
		if(projectContractCost==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(projectContractCost.getId())){
			//新增
			projectContractCost.setId(UUIDUtils.getUUID());
			projectContractCostMapper.insertSelective(projectContractCost);
		}else{
			//避免页面传入修改
			projectContractCost.setYn(null);
			projectContractCostMapper.updateByPrimaryKeySelective(projectContractCost);
		}
		return projectContractCost.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除经费预算
	 *@param id void 经费预算ID
	 *@Author: wangsh
	 */
	public void deleteProjectContractCost(String id) {
		//TODO 做判断后方能执行删除
		ProjectContractCost projectContractCost=projectContractCostMapper.selectByPrimaryKey(id);
		if(projectContractCost==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		ProjectContractCost temprojectContractCost=new ProjectContractCost();
		temprojectContractCost.setYn(CommonConstant.FLAG_NO);
		temprojectContractCost.setId(projectContractCost.getId());
		projectContractCostMapper.updateByPrimaryKeySelective(temprojectContractCost);
	}

    /**
     * @Description: 批量删除经费预算
     * @param projectContractCostVo
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiProjectContractCost(ProjectContractCostVo projectContractCostVo) {
		projectContractCostVo.getIds().stream().forEach(id-> this.deleteProjectContractCost(id));
	}

	@Override
	/**
	 *@Description: 查询经费预算详情
	 *@param id
	 *@return ProjectContractCost
	 *@Author: wangsh
	 */
	public ProjectContractCost findById(String id) {
		return projectContractCostMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询经费预算
	 *@param projectContractCostVo
	 *@return PageInfo<ProjectContractCost>
	 *@Author: wangsh
	 */
	public PageInfo<ProjectContractCost> findPageByQuery(ProjectContractCostVo projectContractCostVo) {
		PageHelper.startPage(projectContractCostVo.getPageNum(),projectContractCostVo.getPageSize());
		Example example=new Example(ProjectContractCost.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(projectContractCostVo.getName())){
		//	criteria.andEqualTo(projectContractCostVo.getName());
		//}
		List<ProjectContractCost> projectContractCostList=projectContractCostMapper.selectByExample(example);
		return new PageInfo<ProjectContractCost>(projectContractCostList);
	}

}
