package com.fd.stdp.service.reward.impl;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.fd.stdp.beans.reward.RewordRecord;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.reward.RewordKeepOn;
import com.fd.stdp.beans.reward.vo.RewordKeepOnVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.reward.RewordKeepOnMapper;
import com.fd.stdp.service.reward.RewordKeepOnService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 科技成果备案
 *@Author: wangsh
 *@Date: 2022-07-05 16:59:11
 */
public class RewordKeepOnServiceImpl extends BaseServiceImpl<RewordKeepOnMapper, RewordKeepOn> implements RewordKeepOnService{

	public static final Logger logger = LoggerFactory.getLogger(RewordKeepOnServiceImpl.class);
	
	@Autowired
	private RewordKeepOnMapper rewordKeepOnMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新科技成果备案
	 *@param rewordKeepOn 科技成果备案对象
	 *@return String 科技成果备案ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateRewordKeepOn(RewordKeepOnVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			rewordKeepOnMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			rewordKeepOnMapper.updateByPrimaryKeySelective(vo);
		}
		basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除科技成果备案
	 *@param id void 科技成果备案ID
	 *@Author: wangsh
	 */
	public void deleteRewordKeepOn(String id) {
		//TODO 做判断后方能执行删除
		RewordKeepOn rewordKeepOn=rewordKeepOnMapper.selectByPrimaryKey(id);
		if(rewordKeepOn==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		RewordKeepOn temrewordKeepOn=new RewordKeepOn();
		temrewordKeepOn.setYn(CommonConstant.FLAG_NO);
		temrewordKeepOn.setId(rewordKeepOn.getId());
		rewordKeepOnMapper.updateByPrimaryKeySelective(temrewordKeepOn);
	}

    /**
     * @Description: 批量删除科技成果备案
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiRewordKeepOn(List<String> ids) {
		ids.stream().forEach(id-> this.deleteRewordKeepOn(id));
	}

	@Override
	/**
	 *@Description: 查询科技成果备案详情
	 *@param id
	 *@return RewordKeepOn
	 *@Author: wangsh
	 */
	public RewordKeepOn findById(String id) {
		RewordKeepOn rewordKeepOn = rewordKeepOnMapper.selectByPrimaryKey(id);
		RewordKeepOnVo vo = new RewordKeepOnVo();
		BeanUtils.copyProperties(rewordKeepOn, vo);
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询科技成果备案
	 *@param rewordKeepOnVo
	 *@return PageInfo<RewordKeepOn>
	 *@Author: wangsh
	 */
	public PageInfo<RewordKeepOn> findPageByQuery(RewordKeepOnVo rewordKeepOnVo) {
		PageHelper.startPage(rewordKeepOnVo.getPageNum(),rewordKeepOnVo.getPageSize());
		Example example=new Example(RewordKeepOn.class);
		Criteria criteria=getCriteria(rewordKeepOnVo, example);
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		List<RewordKeepOn> rewordKeepOnList=rewordKeepOnMapper.selectByExample(example);
		return new PageInfo<RewordKeepOn>(rewordKeepOnList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitRewordKeepOn(RewordKeepOnVo rewordKeepOnVo) {
		String id = this.saveOrUpdateRewordKeepOn(rewordKeepOnVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_NORMAL_AUDIT, rewordKeepOnVo, this.mapper,
				StringUtils.isNotBlank(rewordKeepOnVo.getAuditAdvice())?rewordKeepOnVo.getAuditAdvice():"提交科技成果备案");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditRewordKeepOn(RewordKeepOnVo rewordKeepOnVo) {

		if(StringUtils.equals(rewordKeepOnVo.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())) {
			int year = Calendar.getInstance().get(Calendar.YEAR);
			Example example = new Example(RewordRecord.class);
			example.createCriteria().andIsNotNull("rewardNo").andLike("rewardNo", "%-" + year + "-%");
			example.orderBy("rewardNo").desc();

			List<RewordKeepOn> list = rewordKeepOnMapper.selectByExample(example);
			String num;
			final int flowCount = 8;
			if(!CollectionUtils.isEmpty(list)) {
				RewordKeepOn old = list.get(0);
				String oldNo = old.getRewardNo();
				num = "" + (Integer.parseInt(oldNo.split("-")[2])+1);
			} else {
				num = "1";
			}
			num = StringUtils.leftPad(num, flowCount, "0");
			rewordKeepOnVo.setRewardNo("SJB-" + year + "-" + num);
			rewordKeepOnVo.setAuditTime(new Date());
			mapper.updateByPrimaryKeySelective(rewordKeepOnVo);
		}
		flowCommonService.doFlowStepAudit(rewordKeepOnVo, this.mapper
				, StringUtils.isNotBlank(rewordKeepOnVo.getAuditAdvice()) ? rewordKeepOnVo.getAuditAdvice() : "科技成果备案审核通过"
				, FlowStatusEnum.END.getCode());
		return rewordKeepOnVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackRewordKeepOn(RewordKeepOnVo rewordKeepOnVo) {
		flowCommonService.doFlowStepSendBack(rewordKeepOnVo, this.mapper
				, StringUtils.isNotBlank(rewordKeepOnVo.getAuditAdvice())?rewordKeepOnVo.getAuditAdvice():"科技成果备案退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseRewordKeepOn(RewordKeepOnVo rewordKeepOnVo) {
		flowCommonService.doCompleteTask(rewordKeepOnVo, this.mapper
				, "科技成果备案任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<RewordKeepOn> todoList(RewordKeepOnVo vo) {

		Example example = new Example(RewordKeepOn.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<RewordKeepOn> finishedList(RewordKeepOnVo vo) {
		Example example = new Example(RewordKeepOn.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<RewordKeepOn> endList(RewordKeepOnVo vo) {
        Example example=new Example(RewordKeepOn.class);
        example.orderBy("createTime").desc();
    	Criteria criteria=getCriteria(vo, example);
    	return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
    }

	private Criteria getCriteria(RewordKeepOnVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getRewardName())) {
			criteria.andLike("rewardName", "%" + vo.getRewardName() + "%");
		}
		if(StringUtils.isNotBlank(vo.getRewardUnitName())) {
			criteria.andLike("rewardUnitName", "%" + vo.getRewardUnitName() + "%");
		}
		if(StringUtils.isNotBlank(vo.getRewardTypeText())) {
			criteria.andEqualTo("rewardTypeText", vo.getRewardTypeText());
		}
		if(StringUtils.isNotBlank(vo.getLinkMan())) {
			criteria.andLike("linkMan", "%" + vo.getLinkMan() + "%");
		}
		if(vo.getStartTime() != null) {
			criteria.andGreaterThanOrEqualTo("auditTime", vo.getStartTime());
		}
		if(vo.getEndTime() != null) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(vo.getEndTime());
			calendar.add(Calendar.MONTH, 1);
			criteria.andLessThan("auditTime", calendar.getTime());
		}
		if(StringUtils.isNotBlank(vo.getRewardNo())) {
			criteria.andLike("rewardNo", "%" + vo.getRewardNo() + "%");
		}
		return criteria;
	}
}
