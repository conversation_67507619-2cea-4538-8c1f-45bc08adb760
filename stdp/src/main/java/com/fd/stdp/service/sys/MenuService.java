package com.fd.stdp.service.sys;

import java.util.List;

import com.fd.stdp.beans.sys.SysMenu;
import com.fd.stdp.beans.sys.vo.RouterVo;
import com.fd.stdp.beans.sys.vo.SysMenuVo;
import com.fd.stdp.beans.sys.vo.TreeSelect;
import com.fd.stdp.common.BaseService;

public interface MenuService extends BaseService<SysMenu> {

	public List<SysMenu> selectList(SysMenu sysMenu);

	public SysMenu selectById(Object id);

	public List<SysMenu> selectListAll();

	public void insertSelective(SysMenu entity);

	public int updateById(SysMenuVo vo);

	public int updateSelectiveById(SysMenu entity);

	public List<SysMenu> getUserAuthorityMenuByUserId(String userId);

	public List<SysMenu> getUserAuthorityMenuByRoleUserId(String roleCode);

	public List<SysMenu> selectByExample(Object example);

	public void addSysMenu(SysMenuVo vo);


	/**
	 * 删除菜单
	 * 
	 * @param id
	 */
	public void deleteMenuById(String id);

	/**
	 * 角色id查询权限
	 * 
	 * @param roleId
	 * @return
	 */
	public List<SysMenu> findRoleId(String roleId);


	/**
	 * 根据用户查询菜单列表不带分页
	 * @param menu
	 * @param userId
	 * @return
	 */
	public List<SysMenuVo> selectMenuList(SysMenuVo menu, String userId);

	/**
	 * 菜单转树形
	 * @param menus
	 * @return
	 */
	public List<TreeSelect> buildMenuTreeSelect(List<SysMenuVo> menus);
	 /**
	  * 构建前端路由所需要的菜单
     * 
     * @param menus 菜单列表
     * @return 路由列表
     */
	public List<RouterVo> buildMenus(List<SysMenuVo> menus);

	/**
	 * 递归
	 * @param menus
	 * @param p
	 * @return
	 */
	public List<SysMenuVo> getChildPerms(List<SysMenuVo> menus, String p);

}
