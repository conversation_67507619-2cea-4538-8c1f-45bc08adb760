package com.fd.stdp.service.basic.impl;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import com.fd.stdp.beans.basic.BasicPerson;
import com.fd.stdp.beans.basic.BasicPersonExpertRecommend;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertRecommendVo;
import com.fd.stdp.dao.basic.BasicPersonExpertRecommendMapper;
import com.fd.stdp.dao.basic.BasicPersonMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.basic.BasicPersonExpertRecommendService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonExpertChange;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertChangeVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicPersonExpertChangeMapper;
import com.fd.stdp.service.basic.BasicPersonExpertChangeService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 专家变更表
 *@Author: wangsh
 *@Date: 2022-06-23 15:07:54
 */
public class BasicPersonExpertChangeServiceImpl extends BaseServiceImpl<BasicPersonExpertChangeMapper, BasicPersonExpertChange> implements BasicPersonExpertChangeService{

	public static final Logger logger = LoggerFactory.getLogger(BasicPersonExpertChangeServiceImpl.class);
	
	@Autowired
	private BasicPersonExpertChangeMapper basicPersonExpertChangeMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicPersonMapper basicPersonMapper;
	@Autowired
	private BasicPersonExpertRecommendMapper basicPersonExpertRecommendMapper;
	@Autowired
	private BasicPersonExpertRecommendService basicPersonExpertRecommendService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新专家变更表
	 *@param basicPersonExpertChange 专家变更表对象
	 *@return String 专家变更表ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateBasicPersonExpertChange(BasicPersonExpertChangeVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			basicPersonExpertChangeMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			basicPersonExpertChangeMapper.updateByPrimaryKeySelective(vo);
		}

		// 附件
		basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除专家变更表
	 *@param id void 专家变更表ID
	 *@Author: wangsh
	 */
	public void deleteBasicPersonExpertChange(String id) {
		//TODO 做判断后方能执行删除
		BasicPersonExpertChange basicPersonExpertChange=basicPersonExpertChangeMapper.selectByPrimaryKey(id);
		if(basicPersonExpertChange==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		BasicPersonExpertChange tembasicPersonExpertChange=new BasicPersonExpertChange();
		tembasicPersonExpertChange.setYn(CommonConstant.FLAG_NO);
		tembasicPersonExpertChange.setId(basicPersonExpertChange.getId());
		basicPersonExpertChangeMapper.updateByPrimaryKeySelective(tembasicPersonExpertChange);
	}

    /**
     * @Description: 批量删除专家变更表
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiBasicPersonExpertChange(List<String> ids) {
		ids.stream().forEach(id-> this.deleteBasicPersonExpertChange(id));
	}

	@Override
	/**
	 *@Description: 查询专家变更表详情
	 *@param id
	 *@return BasicPersonExpertChange
	 *@Author: wangsh
	 */
	public BasicPersonExpertChange findById(String id) {
		BasicPersonExpertChange change = basicPersonExpertChangeMapper.selectByPrimaryKey(id);
		BasicPersonExpertChangeVo vo = new BasicPersonExpertChangeVo();
		BeanUtils.copyProperties(change, vo);
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询专家变更表
	 *@param basicPersonExpertChangeVo
	 *@return PageInfo<BasicPersonExpertChange>
	 *@Author: wangsh
	 */
	public PageInfo<BasicPersonExpertChange> findPageByQuery(BasicPersonExpertChangeVo basicPersonExpertChangeVo) {
		PageHelper.startPage(basicPersonExpertChangeVo.getPageNum(),basicPersonExpertChangeVo.getPageSize());
		Example example=new Example(BasicPersonExpertChange.class);
		Criteria criteria=getCriteria(basicPersonExpertChangeVo, example);
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		List<BasicPersonExpertChange> basicPersonExpertChangeList=basicPersonExpertChangeMapper.selectByExample(example);
		return new PageInfo<BasicPersonExpertChange>(basicPersonExpertChangeList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitBasicPersonExpertChange(BasicPersonExpertChangeVo basicPersonExpertChangeVo) {
		String id = this.saveOrUpdateBasicPersonExpertChange(basicPersonExpertChangeVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_NORMAL_AUDIT, basicPersonExpertChangeVo, this.mapper,
				StringUtils.isNotBlank(basicPersonExpertChangeVo.getAuditAdvice())?basicPersonExpertChangeVo.getAuditAdvice():"提交专家变更表");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditBasicPersonExpertChange(BasicPersonExpertChangeVo basicPersonExpertChangeVo) {
		if(StringUtils.equals(FlowStatusEnum.PROVINCE_AUDIT.getCode(), basicPersonExpertChangeVo.getFlowStatus())) {
			// 省局审核通过，更新人员表项
			BasicPerson person = basicPersonMapper.selectByPrimaryKey(basicPersonExpertChangeVo.getPersonId());
			if(person == null) {
				throw new ServiceException("未找到人员信息");
			}
			// 更新可修改的字段
			beanCopyField(basicPersonExpertChangeVo, person,
					new String[]{"Province", "Area", "UnitName", "DeptName", "Post", "ProField", "StartFieldTime"
							, "degree", "Education", "phone", "mail", "address", "mainResearchExperience", "awards"
							, "socialWorks", "beExpert"});
			basicPersonMapper.updateByPrimaryKeySelective(person);

			BasicPersonExpertChangeVo change = (BasicPersonExpertChangeVo) findById(basicPersonExpertChangeVo.getId());
			BasicPersonExpertRecommend recommend = new BasicPersonExpertRecommend();
			recommend.setYn(CommonConstant.FLAG_YES);
			recommend.setPersonId(basicPersonExpertChangeVo.getPersonId());
			recommend.setFlowStatus(FlowStatusEnum.END.getCode());
			basicPersonExpertRecommendMapper.select(recommend).forEach(basicPersonExpertRecommend -> {
				BasicPersonExpertRecommendVo recommendVo = (BasicPersonExpertRecommendVo) basicPersonExpertRecommendService.findById(basicPersonExpertRecommend.getId());
				beanCopyField(change, recommendVo,
						new String[]{"Province", "Area", "UnitName", "DeptName", "Post", "ProField", "StartFieldTime"
								, "degree", "Education", "phone", "mail", "address", "mainResearchExperience", "awards"
								, "socialWorks", "beExpert"});
				recommendVo.setFiles(change.getFiles());
				recommendVo.getFiles().forEach(f->{
					f.setFormId(null);
					f.setId(f.getFileId());
				});
				basicPersonExpertRecommendService.saveOrUpdateBasicPersonExpertRecommend(recommendVo);
			});
		}

		flowCommonService.doFlowStepAudit(basicPersonExpertChangeVo, this.mapper
				, StringUtils.isNotBlank(basicPersonExpertChangeVo.getAuditAdvice()) ? basicPersonExpertChangeVo.getAuditAdvice() : "专家变更表审核通过"
				, FlowStatusEnum.END.getCode());
		return basicPersonExpertChangeVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackBasicPersonExpertChange(BasicPersonExpertChangeVo basicPersonExpertChangeVo) {
		flowCommonService.doFlowStepSendBack(basicPersonExpertChangeVo, this.mapper
				, StringUtils.isNotBlank(basicPersonExpertChangeVo.getAuditAdvice())?basicPersonExpertChangeVo.getAuditAdvice():"专家变更表退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseBasicPersonExpertChange(BasicPersonExpertChangeVo basicPersonExpertChangeVo) {
		flowCommonService.doCompleteTask(basicPersonExpertChangeVo, this.mapper
				, "专家变更表任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<BasicPersonExpertChange> todoList(BasicPersonExpertChangeVo vo) {

		Example example = new Example(BasicPersonExpertChange.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<BasicPersonExpertChange> finishedList(BasicPersonExpertChangeVo vo) {
		Example example = new Example(BasicPersonExpertChange.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<BasicPersonExpertChange> endList(BasicPersonExpertChangeVo vo) {
        Example example=new Example(BasicPersonExpertChange.class);
        example.orderBy("createTime").desc();
    	Criteria criteria=getCriteria(vo, example);
    	return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
    }

	private Criteria getCriteria(BasicPersonExpertChangeVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}
		if(StringUtils.isNotBlank(vo.getUnitName())){
			criteria.andLike("unitName", "%" + vo.getUnitName()+ "%");
		}
		return criteria;
	}
}
