package com.fd.stdp.service.innovation.impl;

import java.util.List;

import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationProvinceCenterApply;
import com.fd.stdp.beans.innovation.vo.InnovationProvinceCenterApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationProvinceCenterApplyMapper;
import com.fd.stdp.service.innovation.InnovationProvinceCenterApplyService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 省质检中心
 *@Author: wangsh
 *@Date: 2022-03-11 16:42:02
 */
public class InnovationProvinceCenterApplyServiceImpl extends BaseServiceImpl<InnovationProvinceCenterApplyMapper, InnovationProvinceCenterApply> implements InnovationProvinceCenterApplyService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationProvinceCenterApplyServiceImpl.class);
	
	@Autowired
	private InnovationProvinceCenterApplyMapper innovationProvinceCenterApplyMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新省质检中心
	 *@param innovationProvinceCenterApply 省质检中心对象
	 *@return String 省质检中心ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationProvinceCenterApply(InnovationProvinceCenterApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationProvinceCenterApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationProvinceCenterApplyMapper.updateByPrimaryKeySelective(vo);
		}
		if(null!= vo.getFiles()){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除省质检中心
	 *@param id void 省质检中心ID
	 *@Author: wangsh
	 */
	public void deleteInnovationProvinceCenterApply(String id) {
		//TODO 做判断后方能执行删除
		InnovationProvinceCenterApply innovationProvinceCenterApply=innovationProvinceCenterApplyMapper.selectByPrimaryKey(id);
		if(innovationProvinceCenterApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationProvinceCenterApply teminnovationProvinceCenterApply=new InnovationProvinceCenterApply();
		teminnovationProvinceCenterApply.setYn(CommonConstant.FLAG_NO);
		teminnovationProvinceCenterApply.setId(innovationProvinceCenterApply.getId());
		innovationProvinceCenterApplyMapper.updateByPrimaryKeySelective(teminnovationProvinceCenterApply);
	}

    /**
     * @Description: 批量删除省质检中心
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationProvinceCenterApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationProvinceCenterApply(id));
	}

	@Override
	/**
	 *@Description: 查询省质检中心详情
	 *@param id
	 *@return InnovationProvinceCenterApply
	 *@Author: wangsh
	 */
	public InnovationProvinceCenterApply findById(String id) {
		InnovationProvinceCenterApply centerApply = innovationProvinceCenterApplyMapper.selectByPrimaryKey(id);
		InnovationProvinceCenterApplyVo applyVo = new InnovationProvinceCenterApplyVo();
		BeanUtils.copyProperties(centerApply, applyVo);
		applyVo.setFiles(basicFileAppendixService.findByFormId(id));
		applyVo.setFlowTaskDto(flowCommonService.findTaskDto(applyVo));
		return applyVo;
	}

	@Override
	/**
	 *@Description: 分页查询省质检中心
	 *@param innovationProvinceCenterApplyVo
	 *@return PageInfo<InnovationProvinceCenterApply>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationProvinceCenterApply> findPageByQuery(InnovationProvinceCenterApplyVo innovationProvinceCenterApplyVo) {
		PageHelper.startPage(innovationProvinceCenterApplyVo.getPageNum(),innovationProvinceCenterApplyVo.getPageSize());
		Example example=new Example(InnovationProvinceCenterApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationProvinceCenterApplyVo.getName())){
		//	criteria.andEqualTo(innovationProvinceCenterApplyVo.getName());
		//}
		List<InnovationProvinceCenterApply> innovationProvinceCenterApplyList=innovationProvinceCenterApplyMapper.selectByExample(example);
		return new PageInfo<InnovationProvinceCenterApply>(innovationProvinceCenterApplyList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationProvinceCenterApply(InnovationProvinceCenterApplyVo innovationProvinceCenterApplyVo) {
		String id = this.saveOrUpdateInnovationProvinceCenterApply(innovationProvinceCenterApplyVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innovationProvinceCenterApplyVo, this.mapper,
				StringUtils.isNotBlank(innovationProvinceCenterApplyVo.getAuditAdvice())?innovationProvinceCenterApplyVo.getAuditAdvice():"提交省质检中心");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationProvinceCenterApply(InnovationProvinceCenterApplyVo innovationProvinceCenterApplyVo) {
		flowCommonService.doFlowStepAudit(innovationProvinceCenterApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationProvinceCenterApplyVo.getAuditAdvice()) ? innovationProvinceCenterApplyVo.getAuditAdvice() : "省质检中心审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationProvinceCenterApplyVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationProvinceCenterApply(InnovationProvinceCenterApplyVo innovationProvinceCenterApplyVo) {
		flowCommonService.doFlowStepSendBack(innovationProvinceCenterApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationProvinceCenterApplyVo.getAuditAdvice())?innovationProvinceCenterApplyVo.getAuditAdvice():"省质检中心退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationProvinceCenterApply(InnovationProvinceCenterApplyVo innovationProvinceCenterApplyVo) {
		flowCommonService.doCompleteTask(innovationProvinceCenterApplyVo, this.mapper
				, "省质检中心任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnovationProvinceCenterApply> todoList(InnovationProvinceCenterApplyVo vo) {

		Example example = new Example(InnovationProvinceCenterApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationProvinceCenterApply> finishedList(InnovationProvinceCenterApplyVo vo) {
		Example example = new Example(InnovationProvinceCenterApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnovationProvinceCenterApply> endList(InnovationProvinceCenterApplyVo vo) {
        Example example = new Example(InnovationProvinceCenterApply.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(InnovationProvinceCenterApply vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getCenterName())){
			criteria.andLike("centerName", "%" + vo.getCenterName()+ "%");
		}
		return criteria;
	}
}
