package com.fd.stdp.service.sys;

import java.util.List;

import com.fd.stdp.beans.sys.SysRole;
import com.fd.stdp.beans.sys.vo.OrganizationalTree;
import com.fd.stdp.beans.sys.vo.RoleAndRoleTypesVO;
import com.fd.stdp.beans.sys.vo.RoleUserVO;
import com.fd.stdp.beans.sys.vo.SysRoleExportVo;
import com.fd.stdp.beans.sys.vo.SysRoleVo;
import com.fd.stdp.common.BaseService;
import com.github.pagehelper.PageInfo;

public interface RoleService extends BaseService<SysRole> {

    /**
     * 保存或更新角色
     *
     * @param sysRole
     * @return
     */
    SysRole saveOrUpdateRole(SysRoleVo sysRole) throws Exception;

    /**
     * 查询角色列表(分页)
     *
     * @param pageNum
     * @param pageSize
     * @param roleName
     * @param roleCode
     * @return
     */
    PageInfo<SysRole> listPage(Integer pageNum, Integer pageSize, String roleName, String roleCode);

    /**
     * 改变角色状态
     *
     * @param status
     */
    void changeStatus(String roleIds, Integer status);

    /**
     * 批量删除角色
     *
     * @param roleIds void
     * @Description: TODO
     * @Author: linqiang
     */
    void deleteRole(String roleIds);

    PageInfo<RoleUserVO> roleUserPage(Integer pageNum, Integer pageSize, String userName);

    /**
     * @Description:查询用户下的所有角色
     * @param: userId
     * @return:
     * @throws:
     * @Author: linqiang
     */
    List<SysRole> findRolesByUserId(String userId);

    /**
     * 分页查询(包含角色所属角色类型)
     *
     * @param
     * @return
     */
    PageInfo<RoleAndRoleTypesVO> listPageRoleAndRoleTypeVo(SysRoleVo sysRoleVo);

    /**
     * 查询当前用户添加的角色列表
     *
     * @param sysRoleVo
     * @return
     */
    List<SysRole> listRoleByLoginUser(SysRoleVo sysRoleVo);

    /**
     * 获取组织架构
     *
     * @return
     */
    List<OrganizationalTree> getOrgTree();

    /**
     * 导出角色
     *
     * @param roleName
     * @return
     */
    List<SysRoleExportVo> export(SysRoleVo sysRoleVo);

    /**
     * 获取所有的角色
     *
     * @return
     */
    List<SysRole> listAll();


    /**
     * 根据角色code返回name
     *
     * @param code
     * @return
     */
    String fingByCode(String code);

    /**
     * 查询角色详情
     * @param id
     * @return
     */
	SysRoleVo findRoleById(String id);

    /**
     * 根据ROLE_CODE返回
     * @param roleCode
     * @return
     */
    SysRole findRoleByRoleCode(String roleCode);


}
