package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectApplyDevices;
import com.fd.stdp.beans.project.vo.ProjectApplyDevicesVo;

/**
 * 项目设备购置预算明细Service接口
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
public interface ProjectApplyDevicesService {
    /**
     * @param projectApplyDevices 项目设备购置预算明细对象
     * @return String 项目设备购置预算明细ID
     * @Description: 保存或更新项目设备购置预算明细
     * @Author: yujianfei
     */
    String saveOrUpdateProjectApplyDevices(ProjectApplyDevices projectApplyDevices);

    /**
     * @param ids void 项目设备购置预算明细ID
     * @Description: 删除项目设备购置预算明细
     * @Author: yujianfei
     */
    void deleteProjectApplyDevices(List<String> ids);

    /**
     * @param id
     * @return ProjectApplyDevices
     * @Description: 查询项目设备购置预算明细详情
     * @Author: yujianfei
     */
    ProjectApplyDevices findById(String id);

    /**
     * @param projectApplyDevicesVo
     * @return PageInfo<ProjectApplyDevices>
     * @Description: 分页查询项目设备购置预算明细
     * @Author: yujianfei
     */
    PageInfo<ProjectApplyDevices> findPageByQuery(ProjectApplyDevicesVo projectApplyDevicesVo);
}
