package com.fd.stdp.service.project.impl;

import java.util.List;

import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectDemandCollectionGuid;
import com.fd.stdp.beans.project.vo.ProjectDemandCollectionGuidVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectDemandCollectionGuidMapper;
import com.fd.stdp.service.project.ProjectDemandCollectionGuidService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 需求征集指南Service业务层处理
 * @date 2021-11-16
 */
@Service
@Transactional(readOnly = true)
public class ProjectDemandCollectionGuidServiceImpl extends BaseServiceImpl<ProjectDemandCollectionGuidMapper, ProjectDemandCollectionGuid> implements ProjectDemandCollectionGuidService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectDemandCollectionGuidServiceImpl.class);
    @Autowired
    private ProjectDemandCollectionGuidMapper projectDemandCollectionGuidMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新需求征集指南
     *@param projectDemandCollectionGuid 需求征集指南对象
     *@return String 需求征集指南ID
     *@Author: yujianfei
     */
    public String saveOrUpdateProjectDemandCollectionGuid(ProjectDemandCollectionGuid projectDemandCollectionGuid) {
        if (projectDemandCollectionGuid == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(projectDemandCollectionGuid.getId())) {
            //新增
            projectDemandCollectionGuid.setId(UUIDUtils.getUUID());
            projectDemandCollectionGuidMapper.insertSelective(projectDemandCollectionGuid);
        } else {
            //避免页面传入修改
            projectDemandCollectionGuid.setYn(null);
            projectDemandCollectionGuidMapper.updateByPrimaryKeySelective(projectDemandCollectionGuid);
        }
        return projectDemandCollectionGuid.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除需求征集指南
     *@param id void 需求征集指南ID
     *@Author: yujianfei
     */
    public void deleteProjectDemandCollectionGuid(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            ProjectDemandCollectionGuid projectDemandCollectionGuid = projectDemandCollectionGuidMapper.selectByPrimaryKey(id);
            if (projectDemandCollectionGuid == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            ProjectDemandCollectionGuid temprojectDemandCollectionGuid = new ProjectDemandCollectionGuid();
            temprojectDemandCollectionGuid.setYn(CommonConstant.FLAG_NO);
            temprojectDemandCollectionGuid.setId(projectDemandCollectionGuid.getId());
            projectDemandCollectionGuidMapper.updateByPrimaryKeySelective(temprojectDemandCollectionGuid);
        }
    }

    /**
     * @param id
     * @return ProjectDemandCollectionGuid
     * @Description: 查询需求征集指南详情
     * @Author: yujianfei
     */
    @Override
    public ProjectDemandCollectionGuid findById(String id) {
        return projectDemandCollectionGuidMapper.selectByPrimaryKey(id);
    }


    /**
     * @param projectDemandCollectionGuidVo
     * @return PageInfo<ProjectDemandCollectionGuid>
     * @Description: 分页查询需求征集指南
     * @Author: yujianfei
     */
    @Override
    public PageInfo<ProjectDemandCollectionGuid> findPageByQuery(ProjectDemandCollectionGuidVo projectDemandCollectionGuidVo) {
        PageHelper.startPage(projectDemandCollectionGuidVo.getPageNum(), projectDemandCollectionGuidVo.getPageSize());
        List<ProjectDemandCollectionGuid> projectDemandCollectionGuidList = projectDemandCollectionGuidMapper.findPageByQuery(projectDemandCollectionGuidVo);
        return new PageInfo<ProjectDemandCollectionGuid>(projectDemandCollectionGuidList);
    }

    @Override
    public String open(ProjectDemandCollectionGuidVo projectDemandCollectionGuidVo) {
        ProjectDemandCollectionGuid projectDemandCollectionGuid = new ProjectDemandCollectionGuid();
        projectDemandCollectionGuid.setId(projectDemandCollectionGuidVo.getId());
        projectDemandCollectionGuid.setStatus(projectDemandCollectionGuidVo.getStatus());
        this.mapper.updateByPrimaryKeySelective(projectDemandCollectionGuid);
        return null;
    }

    @Override
    public PageInfo<ProjectDemandCollectionGuid> todoList(ProjectDemandCollectionGuidVo projectDemandCollectionGuidVo) {
        Example example = new Example(ProjectDemandCollectionGuid.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("status", 0);
        PageHelper.startPage(projectDemandCollectionGuidVo.getPageNum(), projectDemandCollectionGuidVo.getPageSize());
        return new PageInfo(this.mapper.selectByExample(example));
    }

    @Override
    public PageInfo<ProjectDemandCollectionGuid> finishedList(ProjectDemandCollectionGuidVo projectDemandCollectionGuidVo) {
        Example example = new Example(ProjectDemandCollectionGuid.class);
        example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andNotEqualTo("status", 0);
        PageHelper.startPage(projectDemandCollectionGuidVo.getPageNum(), projectDemandCollectionGuidVo.getPageSize());
        return new PageInfo(this.mapper.selectByExample(example));
    }
}
