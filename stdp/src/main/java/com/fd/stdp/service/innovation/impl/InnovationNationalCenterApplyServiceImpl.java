package com.fd.stdp.service.innovation.impl;

import java.util.List;

import com.fd.stdp.beans.innovation.vo.InnovationGeneralLaboratoryApplyVo;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationNationalCenterApply;
import com.fd.stdp.beans.innovation.vo.InnovationNationalCenterApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationNationalCenterApplyMapper;
import com.fd.stdp.service.innovation.InnovationNationalCenterApplyService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 国家中心
 *@Author: wangsh
 *@Date: 2022-03-07 19:46:19
 */
public class InnovationNationalCenterApplyServiceImpl extends BaseServiceImpl<InnovationNationalCenterApplyMapper, InnovationNationalCenterApply> implements InnovationNationalCenterApplyService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationNationalCenterApplyServiceImpl.class);
	
	@Autowired
	private InnovationNationalCenterApplyMapper innovationNationalCenterApplyMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新国家中心
	 *@param innovationNationalCenterApply 国家中心对象
	 *@return String 国家中心ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationNationalCenterApply(InnovationNationalCenterApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationNationalCenterApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationNationalCenterApplyMapper.updateByPrimaryKeySelective(vo);
		}
		/**
		 * 附件
		 */
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除国家中心
	 *@param id void 国家中心ID
	 *@Author: wangsh
	 */
	public void deleteInnovationNationalCenterApply(String id) {
		//TODO 做判断后方能执行删除
		InnovationNationalCenterApply innovationNationalCenterApply=innovationNationalCenterApplyMapper.selectByPrimaryKey(id);
		if(innovationNationalCenterApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationNationalCenterApply teminnovationNationalCenterApply=new InnovationNationalCenterApply();
		teminnovationNationalCenterApply.setYn(CommonConstant.FLAG_NO);
		teminnovationNationalCenterApply.setId(innovationNationalCenterApply.getId());
		innovationNationalCenterApplyMapper.updateByPrimaryKeySelective(teminnovationNationalCenterApply);
	}

    /**
     * @Description: 批量删除国家中心
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationNationalCenterApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationNationalCenterApply(id));
	}

	@Override
	/**
	 *@Description: 查询国家中心详情
	 *@param id
	 *@return InnovationNationalCenterApply
	 *@Author: wangsh
	 */
	public InnovationNationalCenterApply findById(String id) {
		InnovationNationalCenterApply apply = innovationNationalCenterApplyMapper.selectByPrimaryKey(id);
		InnovationNationalCenterApplyVo vo = new InnovationNationalCenterApplyVo();
		BeanUtils.copyProperties(apply, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询国家中心
	 *@param innovationNationalCenterApplyVo
	 *@return PageInfo<InnovationNationalCenterApply>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationNationalCenterApply> findPageByQuery(InnovationNationalCenterApplyVo innovationNationalCenterApplyVo) {
		PageHelper.startPage(innovationNationalCenterApplyVo.getPageNum(),innovationNationalCenterApplyVo.getPageSize());
		Example example=new Example(InnovationNationalCenterApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationNationalCenterApplyVo.getName())){
		//	criteria.andEqualTo(innovationNationalCenterApplyVo.getName());
		//}
		List<InnovationNationalCenterApply> innovationNationalCenterApplyList=innovationNationalCenterApplyMapper.selectByExample(example);
		return new PageInfo<InnovationNationalCenterApply>(innovationNationalCenterApplyList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationNationalCenterApply(InnovationNationalCenterApplyVo innovationNationalCenterApplyVo) {
		String id = this.saveOrUpdateInnovationNationalCenterApply(innovationNationalCenterApplyVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innovationNationalCenterApplyVo, this.mapper,
				StringUtils.isNotBlank(innovationNationalCenterApplyVo.getAuditAdvice())?innovationNationalCenterApplyVo.getAuditAdvice():"提交国家中心");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationNationalCenterApply(InnovationNationalCenterApplyVo innovationNationalCenterApplyVo) {
		flowCommonService.doFlowStepAudit(innovationNationalCenterApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationNationalCenterApplyVo.getAuditAdvice()) ? innovationNationalCenterApplyVo.getAuditAdvice() : "国家中心审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationNationalCenterApplyVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationNationalCenterApply(InnovationNationalCenterApplyVo innovationNationalCenterApplyVo) {
		flowCommonService.doFlowStepSendBack(innovationNationalCenterApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationNationalCenterApplyVo.getAuditAdvice())?innovationNationalCenterApplyVo.getAuditAdvice():"国家中心退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationNationalCenterApply(InnovationNationalCenterApplyVo innovationNationalCenterApplyVo) {
		flowCommonService.doCompleteTask(innovationNationalCenterApplyVo, this.mapper
				, "国家中心任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnovationNationalCenterApply> todoList(InnovationNationalCenterApplyVo vo) {

		Example example = new Example(InnovationNationalCenterApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationNationalCenterApply> finishedList(InnovationNationalCenterApplyVo vo) {
		Example example = new Example(InnovationNationalCenterApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnovationNationalCenterApply> endList(InnovationNationalCenterApplyVo vo) {
        Example example = new Example(InnovationNationalCenterApply.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(InnovationNationalCenterApply vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件

		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getCenterName())){
			criteria.andLike("centerName", "%" + vo.getCenterName()+ "%");
		}
		return criteria;
	}
}
