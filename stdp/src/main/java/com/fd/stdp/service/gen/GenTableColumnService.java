package com.fd.stdp.service.gen;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.gen.GenTableColumn;
import com.fd.stdp.beans.gen.vo.GenTableColumnVo;
/**
 *@Description: 代码生成业务表字段
 *@Author: linqiang
 *@Date: 2021-10-15 09:59:26
 */
public interface GenTableColumnService {

	/**
	 *@Description: 保存或更新代码生成业务表字段
	 *@param genTableColumn 代码生成业务表字段对象
	 *@return String 代码生成业务表字段ID
	 *@Author: linqiang
	 */
	String saveOrUpdateGenTableColumn(GenTableColumn genTableColumn);
	
	/**
	 *@Description: 删除代码生成业务表字段
	 *@param id void 代码生成业务表字段ID
	 *@Author: linqiang
	 */
	void deleteGenTableColumn(String id);

	/**
	 *@Description: 查询代码生成业务表字段详情
	 *@param id
	 *@return GenTableColumn
	 *@Author: linqiang
	 */
	GenTableColumn findById(String id);

	/**
	 *@Description: 分页查询代码生成业务表字段
	 *@param genTableColumnVo
	 *@return PageInfo<GenTableColumn>
	 *@Author: linqiang
	 */
	PageInfo<GenTableColumn> findPageByQuery(GenTableColumnVo genTableColumnVo);
	
	
	/**
     * 查询业务字段列表
     * 
     * @param tableId 业务字段编号
     * @return 业务字段集合
     */
    public List<GenTableColumn> findGenTableColumnListByTableId(String tableId);
}
