package com.fd.stdp.service.appraisal;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.appraisal.AppraisalApplyItem;
import com.fd.stdp.beans.appraisal.vo.AppraisalApplyItemVo;

/**
 * 评价申请项Service接口
 *
 * <AUTHOR>
 * @date 2021-11-18
 */
public interface AppraisalApplyItemService {
    /**
     * @param appraisalApplyItem 评价申请项对象
     * @return String 评价申请项ID
     * @Description: 保存或更新评价申请项
     * @Author: linqiang
     */
    String saveOrUpdateAppraisalApplyItem(AppraisalApplyItem appraisalApplyItem);

    /**
     * @param ids void 评价申请项ID
     * @Description: 删除评价申请项
     * @Author: linqiang
     */
    void deleteAppraisalApplyItem(List<String> ids);

    /**
     * @param id
     * @return AppraisalApplyItem
     * @Description: 查询评价申请项详情
     * @Author: linqiang
     */
    AppraisalApplyItem findById(String id);

    /**
     * @param appraisalApplyItemVo
     * @return PageInfo<AppraisalApplyItem>
     * @Description: 分页查询评价申请项
     * @Author: linqiang
     */
    PageInfo<AppraisalApplyItem> findPageByQuery(AppraisalApplyItemVo appraisalApplyItemVo);
}
