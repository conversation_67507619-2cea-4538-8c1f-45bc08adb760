package com.fd.stdp.service.talent.impl;

import java.util.List;

import com.fd.stdp.beans.talent.TalentTeamContributeTarget;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.dao.talent.TalentTeamContributeTargetMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.flowable.FlowCommonService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectExamine;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectExamineVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.talent.TalentLeaderSubjectExamineMapper;
import com.fd.stdp.service.talent.TalentLeaderSubjectExamineService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 学科带头人考核表
 *@Author: wangsh
 *@Date: 2022-02-14 10:05:09
 */
public class TalentLeaderSubjectExamineServiceImpl extends BaseServiceImpl<TalentLeaderSubjectExamineMapper, TalentLeaderSubjectExamine> implements TalentLeaderSubjectExamineService{

	public static final Logger logger = LoggerFactory.getLogger(TalentLeaderSubjectExamineServiceImpl.class);
	
	@Autowired
	private TalentLeaderSubjectExamineMapper talentLeaderSubjectExamineMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private TalentTeamContributeTargetMapper talentTeamContributeTargetMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新学科带头人考核表
	 *@param talentLeaderSubjectExamine 学科带头人考核表对象
	 *@return String 学科带头人考核表ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTalentLeaderSubjectExamine(TalentLeaderSubjectExamineVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			talentLeaderSubjectExamineMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			talentLeaderSubjectExamineMapper.updateByPrimaryKeySelective(vo);
		}

		if(!CollectionUtils.isEmpty(vo.getContractTargets())){
			updateList(vo, vo.getContractTargets(), talentTeamContributeTargetMapper, "setContractId");
		}

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除学科带头人考核表
	 *@param id void 学科带头人考核表ID
	 *@Author: wangsh
	 */
	public void deleteTalentLeaderSubjectExamine(String id) {
		//TODO 做判断后方能执行删除
		TalentLeaderSubjectExamine talentLeaderSubjectExamine=talentLeaderSubjectExamineMapper.selectByPrimaryKey(id);
		if(talentLeaderSubjectExamine==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TalentLeaderSubjectExamine temtalentLeaderSubjectExamine=new TalentLeaderSubjectExamine();
		temtalentLeaderSubjectExamine.setYn(CommonConstant.FLAG_NO);
		temtalentLeaderSubjectExamine.setId(talentLeaderSubjectExamine.getId());
		talentLeaderSubjectExamineMapper.updateByPrimaryKeySelective(temtalentLeaderSubjectExamine);
	}

    /**
     * @Description: 批量删除学科带头人考核表
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTalentLeaderSubjectExamine(List<String> ids) {
		ids.stream().forEach(id-> this.deleteTalentLeaderSubjectExamine(id));
	}

	@Override
	/**
	 *@Description: 查询学科带头人考核表详情
	 *@param id
	 *@return TalentLeaderSubjectExamine
	 *@Author: wangsh
	 */
	public TalentLeaderSubjectExamine findById(String id) {
		TalentLeaderSubjectExamine subjectExamine = talentLeaderSubjectExamineMapper.selectByPrimaryKey(id);
		TalentLeaderSubjectExamineVo vo = new TalentLeaderSubjectExamineVo();
		BeanUtils.copyProperties(subjectExamine, vo);

		Example example = new Example(TalentTeamContributeTarget.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("contractId", id);
		vo.setContractTargets(talentTeamContributeTargetMapper.selectByExample(example));
		// 流程
		if(StringUtils.isNotBlank(vo.getFlowStatus())) {
			vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		}
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询学科带头人考核表
	 *@param talentLeaderSubjectExamineVo
	 *@return PageInfo<TalentLeaderSubjectExamine>
	 *@Author: wangsh
	 */
	public PageInfo<TalentLeaderSubjectExamine> findPageByQuery(TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo) {
		PageHelper.startPage(talentLeaderSubjectExamineVo.getPageNum(),talentLeaderSubjectExamineVo.getPageSize());
		Example example=new Example(TalentLeaderSubjectExamine.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		criteria.andEqualTo("flowStatus",FlowStatusEnum.END.getCode());
		//查询条件
		//if(!StringUtils.isEmpty(talentLeaderSubjectExamineVo.getName())){
		//	criteria.andEqualTo(talentLeaderSubjectExamineVo.getName());
		//}
		List<TalentLeaderSubjectExamine> talentLeaderSubjectExamineList=talentLeaderSubjectExamineMapper.selectByExample(example);
		return new PageInfo<TalentLeaderSubjectExamine>(talentLeaderSubjectExamineList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitTalentLeaderSubjectExamine(TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo) {
		String id = this.saveOrUpdateTalentLeaderSubjectExamine(talentLeaderSubjectExamineVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, talentLeaderSubjectExamineVo, this.mapper,
				StringUtils.isNotBlank(talentLeaderSubjectExamineVo.getAuditAdvice())?talentLeaderSubjectExamineVo.getAuditAdvice():"提交学科带头人考核表");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditTalentLeaderSubjectExamine(TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo) {
		flowCommonService.doFlowStepAudit(talentLeaderSubjectExamineVo, this.mapper
				, StringUtils.isNotBlank(talentLeaderSubjectExamineVo.getAuditAdvice()) ? talentLeaderSubjectExamineVo.getAuditAdvice() : "学科带头人考核表审核通过"
				, FlowStatusEnum.END.getCode());
		return talentLeaderSubjectExamineVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackTalentLeaderSubjectExamine(TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo) {
		flowCommonService.doFlowStepSendBack(talentLeaderSubjectExamineVo, this.mapper
				, StringUtils.isNotBlank(talentLeaderSubjectExamineVo.getAuditAdvice())?talentLeaderSubjectExamineVo.getAuditAdvice():"学科带头人考核表退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseTalentLeaderSubjectExamine(TalentLeaderSubjectExamineVo talentLeaderSubjectExamineVo) {
		flowCommonService.doCompleteTask(talentLeaderSubjectExamineVo, this.mapper
				, "学科带头人考核表任务书下达完成"
				, FlowStatusEnum.PROJECT_RELEASE.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<TalentLeaderSubjectExamine> todoList(TalentLeaderSubjectExamineVo vo) {

		Example example = new Example(TalentLeaderSubjectExamine.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TalentLeaderSubjectExamine> finishedList(TalentLeaderSubjectExamineVo vo) {
		Example example = new Example(TalentLeaderSubjectExamine.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	private Criteria getCriteria(TalentLeaderSubjectExamine vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}
		return criteria;
	}
}
