package com.fd.stdp.service.appraisal.impl;

import java.util.List;

import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.beans.sys.SysUserRole;
import com.fd.stdp.constant.Constant;
import com.fd.stdp.dao.sys.SysUserRoleMapper;
import com.fd.stdp.util.AppUserUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.appraisal.AppraisalBasicItems;
import com.fd.stdp.beans.appraisal.vo.AppraisalBasicItemsVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.appraisal.AppraisalBasicItemsMapper;
import com.fd.stdp.service.appraisal.AppraisalBasicItemsService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 评价基础项Service业务层处理
 * @date 2021-11-18
 */
@Service
@Transactional(readOnly = true)
public class AppraisalBasicItemsServiceImpl extends BaseServiceImpl<AppraisalBasicItemsMapper, AppraisalBasicItems> implements AppraisalBasicItemsService {

    private static final Logger logger = LoggerFactory.getLogger(AppraisalBasicItemsServiceImpl.class);
    @Autowired
    private AppraisalBasicItemsMapper appraisalBasicItemsMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新评价基础项
     *@param appraisalBasicItems 评价基础项对象
     *@return String 评价基础项ID
     *@Author: yujianfei
     */
    public String saveOrUpdateAppraisalBasicItems(AppraisalBasicItems appraisalBasicItems) {
        if (appraisalBasicItems == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(appraisalBasicItems.getId())) {
            //新增
            appraisalBasicItems.setId(UUIDUtils.getUUID());
            appraisalBasicItemsMapper.insertSelective(appraisalBasicItems);
        } else {
            //避免页面传入修改
            appraisalBasicItems.setYn(null);
            appraisalBasicItemsMapper.updateByPrimaryKeySelective(appraisalBasicItems);
        }
        return appraisalBasicItems.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除评价基础项
     *@param id void 评价基础项ID
     *@Author: yujianfei
     */
    public void deleteAppraisalBasicItems(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            AppraisalBasicItems appraisalBasicItems = appraisalBasicItemsMapper.selectByPrimaryKey(id);
            if (appraisalBasicItems == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            AppraisalBasicItems temappraisalBasicItems = new AppraisalBasicItems();
            temappraisalBasicItems.setYn(CommonConstant.FLAG_NO);
            temappraisalBasicItems.setId(appraisalBasicItems.getId());
            appraisalBasicItemsMapper.updateByPrimaryKeySelective(temappraisalBasicItems);
        }
    }

    /**
     * @param id
     * @return AppraisalBasicItems
     * @Description: 查询评价基础项详情
     * @Author: yujianfei
     */
    @Override
    public AppraisalBasicItems findById(String id) {
        return appraisalBasicItemsMapper.selectByPrimaryKey(id);
    }


    /**
     * @param appraisalBasicItemsVo
     * @return PageInfo<AppraisalBasicItems>
     * @Description: 分页查询评价基础项
     * @Author: yujianfei
     */
    @Override
    public PageInfo<AppraisalBasicItems> findPageByQuery(AppraisalBasicItemsVo appraisalBasicItemsVo) {
        PageHelper.startPage(appraisalBasicItemsVo.getPageNum(), appraisalBasicItemsVo.getPageSize());
        Example example = new Example(AppraisalBasicItems.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        example.orderBy("orderVal").asc();


        SysUser loginAppUser = (SysUser) AppUserUtil.getLoginAppUser();
        System.out.println(loginAppUser);
        Example userRoleExample = new Example(SysUserRole.class);
        Criteria userRoleCriteria = userRoleExample.createCriteria();
        userRoleCriteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        userRoleCriteria.andEqualTo("userId", loginAppUser.getId());
        List<SysUserRole> userRoleList = sysUserRoleMapper.selectByExample(userRoleExample);
        //Boolean cityBureau;
        if (!CollectionUtils.isEmpty(userRoleList)) {
            for (SysUserRole sysUserRole : userRoleList) {
                // 市局
                if ("f194f7d4389642d1aa0fc943ca26f7da".equals(sysUserRole.getRoleId())) {
                    criteria.andEqualTo("orgType", Constant.INTERGRATE_CITY);
                }
                // 县局
                else if ("275d8f49d38a413a97c8d3ed4066460a".equals(sysUserRole.getRoleId())) {
                    criteria.andEqualTo("orgType", Constant.INTERGRATE_COUNTY);
                }
            }
        }

        //查询条件
        if(!StringUtils.isEmpty(appraisalBasicItemsVo.getYearNo())){
        	criteria.andEqualTo("yearNo",appraisalBasicItemsVo.getYearNo());
        }if(!StringUtils.isEmpty(appraisalBasicItemsVo.getOrgType())){
        	criteria.andEqualTo("orgType",appraisalBasicItemsVo.getOrgType());
        }
        List<AppraisalBasicItems> appraisalBasicItemsList = appraisalBasicItemsMapper.selectByExample(example);
        return new PageInfo<AppraisalBasicItems>(appraisalBasicItemsList);
    }
}
