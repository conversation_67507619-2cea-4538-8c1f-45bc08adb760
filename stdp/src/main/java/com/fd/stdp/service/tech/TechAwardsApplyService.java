package com.fd.stdp.service.tech;

import java.util.List;

import com.fd.stdp.beans.tech.vo.TechAwardsApplyVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.tech.TechAwardsApply;
import com.fd.stdp.beans.tech.vo.TechAwardsApplyVo;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;

/**
 *@Description: 奖项申报
 *@Author: wangsh
 *@Date: 2022-01-04 13:49:20
 */
public interface TechAwardsApplyService {

	/**
	 *@Description: 保存或更新奖项申报
	 *@param techAwardsApply 奖项申报对象
	 *@return String 奖项申报ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTechAwardsApply(TechAwardsApplyVo techAwardsApplyVo);
	
	/**
	 *@Description: 删除奖项申报
	 *@param id void 奖项申报ID
	 *@Author: wangsh
	 */
	void deleteTechAwardsApply(String id);

	/**
	 *@Description: 查询奖项申报详情
	 *@param id
	 *@return TechAwardsApply
	 *@Author: wangsh
	 */
	TechAwardsApplyVo findById(String id);

	/**
	 *@Description: 分页查询奖项申报
	 *@param techAwardsApplyVo
	 *@return PageInfo<TechAwardsApply>
	 *@Author: wangsh
	 */
	PageInfo<TechAwardsApply> findPageByQuery(TechAwardsApplyVo techAwardsApplyVo);

	/**
	 * 提交
	 * @param techAwardsApplyVo
	 * @return
	 */
	String submitTechAwardsApply(TechAwardsApplyVo techAwardsApplyVo);

	/**
	 * 审核
	 * @param techAwardsApplyVo
	 * @return
	 */
	String auditTechAwardsApply(TechAwardsApplyVo techAwardsApplyVo);

	/**
	 * 退回
	 * @param techAwardsApplyVo
	 * @return
	 */
	String sendBackTechAwardsApply(TechAwardsApplyVo techAwardsApplyVo);

	void deleteMultiTechAwardsApply(List<String> ids);

    PageInfo<TechAwardsApply> todoList(TechAwardsApplyVo techAwardsApplyVo);

	PageInfo<TechAwardsApply> finishedList(TechAwardsApplyVo techAwardsApplyVo);

	PageInfo endList(TechAwardsApplyVo techAwardsApplyVo);

	void exportTechAchievement(TechAwardsApplyVo vo, HttpServletResponse response);
}
