package com.fd.stdp.service.tech;

import java.util.List;

import com.fd.stdp.beans.tech.vo.TechAchievementVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.tech.TechAchievementSell;
import com.fd.stdp.beans.tech.vo.TechAchievementSellVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 *@Description: 表名
 *@Author: wangsh
 *@Date: 2022-01-04 13:49:55
 */
public interface TechAchievementSellService {

	/**
	 *@Description: 保存或更新表名
	 *@param techAchievementSell 表名对象
	 *@return String 表名ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTechAchievementSell(TechAchievementSellVo techAchievementSell);
	
	/**
	 *@Description: 删除表名
	 *@param id void 表名ID
	 *@Author: wangsh
	 */
	void deleteTechAchievementSell(String id);

	/**
	 *@Description: 查询表名详情
	 *@param id
	 *@return TechAchievementSell
	 *@Author: wangsh
	 */
	TechAchievementSell findById(String id);

	/**
	 *@Description: 分页查询表名
	 *@param techAchievementSellVo
	 *@return PageInfo<TechAchievementSell>
	 *@Author: wangsh
	 */
	PageInfo<TechAchievementSell> findPageByQuery(TechAchievementSellVo techAchievementSellVo);

	/**
	 * 上传
	 * @param file
	 * @return
	 */
    String doImport(MultipartFile file);

	/**
	 * 提交
	 * @param techAchievementSellVo
	 * @return
	 */
    String submitTechAchievementSell(TechAchievementSellVo techAchievementSellVo);

	/**
	 * 审核
	 * @param techAchievementSellVo
	 * @return
	 */
	String auditTechAchievementSell(TechAchievementSellVo techAchievementSellVo);

	/**
	 * 退回
	 * @param techAchievementSellVo
	 * @return
	 */
	String sendBackTechAchievementSell(TechAchievementSellVo techAchievementSellVo);

    void deleteMultiTechAchievementSell(List<String> ids);

    PageInfo todoList(TechAchievementSellVo techAchievementSellVo);

	PageInfo finishedList(TechAchievementSellVo techAchievementSellVo);

    void exportTechAchievement(TechAchievementSellVo vo, HttpServletResponse response);

	PageInfo endList(TechAchievementSellVo vo);
}
