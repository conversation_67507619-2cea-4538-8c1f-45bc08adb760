package com.fd.stdp.service.tech.impl;

import java.io.InputStream;
import java.util.Date;
import java.util.List;

import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import com.fd.stdp.service.basic.BasicPersonLinkedService;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.flowable.FlowTaskService;
import com.fd.stdp.service.sys.CommonUtilService;
import com.fd.stdp.service.sys.SysUserService;
import com.fd.stdp.util.EasyExcelUtils;
import com.fd.stdp.util.ExportUtil;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.tech.TechAchievement;
import com.fd.stdp.beans.tech.vo.TechAchievementVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.tech.TechAchievementMapper;
import com.fd.stdp.service.tech.TechAchievementService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.servlet.http.HttpServletResponse;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 成果统计
 *@Author: wangsh
 *@Date: 2022-01-04 13:49:38
 */
public class TechAchievementServiceImpl extends BaseServiceImpl<TechAchievementMapper, TechAchievement> implements TechAchievementService{

	public static final Logger logger = LoggerFactory.getLogger(TechAchievementServiceImpl.class);
	
	@Autowired
	private TechAchievementMapper techAchievementMapper;

	@Autowired
	private FlowApiService flowApiService;

	@Autowired
	private FlowCommonService techFlowService;

	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	@Autowired
	private BasicPersonLinkedService basicPersonLinkedService;

	@Autowired
	private FlowCommonService flowCommonService;

	@Autowired
	protected TaskService taskService;
	@Autowired
	protected RuntimeService runtimeService;
	@Autowired
	protected RepositoryService repositoryService;
	@Autowired
	protected HistoryService historyService;
	@Autowired
	private SysUserService sysUserService;

	@Autowired
	private CommonUtilService commonUtilService;

	@Autowired
	private FlowTaskService flowTaskService;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新成果统计
	 *@param techAchievement 成果统计对象
	 *@return String 成果统计ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTechAchievement(TechAchievementVo techAchievement) {
		if(techAchievement==null){
			throw new ServiceException("数据异常");
		}
		// 设置负责人
		if(techAchievement.getPersons() != null){
			techAchievement.getPersons().forEach(person -> {
				if("项目负责人".equals(person.getPersonType())){
					techAchievement.setLeaderName(person.getName());
				}
			});
		}

		if(StringUtils.isEmpty(techAchievement.getId())){
			//新增
			// 完善申报单位信息
			if(techAchievement.getApplyUnitName() == null) {
				BasicScienceOrg basicScienceOrg = commonUtilService.getLoginBasicScienceOrg();
				if (basicScienceOrg != null) {
					techAchievement.setApplyUnitId(basicScienceOrg.getId());
					techAchievement.setApplyUnitName(basicScienceOrg.getOrgName());
				}
			}

			techAchievement.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			techAchievement.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			techAchievement.setId(UUIDUtils.getUUID());
			techAchievementMapper.insertSelective(techAchievement);
		}else{
			//避免页面传入修改
			techAchievement.setYn(null);
			techAchievementMapper.updateByPrimaryKeySelective(techAchievement);
		}

		// 附件
		basicFileAppendixService.clearAndUpdateBasicFileAppendix(techAchievement.getId(), techAchievement.getFiles());
		// 人员
		basicPersonLinkedService.clearAndUpdateBasicPersonLinked(techAchievement.getId(), techAchievement.getPersons());

		// flowCommonService.doFlowStart(FlowableConstant.FLOW_TECH_ACHIEVEMENT, techAchievement, this.mapper, "开始成果统计流程");

		return techAchievement.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除成果统计
	 *@param id void 成果统计ID
	 *@Author: wangsh
	 */
	public void deleteTechAchievement(String id) {
		//TODO 做判断后方能执行删除
		TechAchievement techAchievement=techAchievementMapper.selectByPrimaryKey(id);
		if(techAchievement==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TechAchievement temtechAchievement=new TechAchievement();
		temtechAchievement.setYn(CommonConstant.FLAG_NO);
		temtechAchievement.setId(techAchievement.getId());
		techAchievementMapper.updateByPrimaryKeySelective(temtechAchievement);
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTechAchievement(List<String> ids) {
		if(!CollectionUtils.isEmpty(ids)){
			ids.stream().forEach(id -> this.deleteTechAchievement(id));
		}
	}

	@Override
	/**
	 *@Description: 查询成果统计详情
	 *@param id
	 *@return TechAchievement
	 *@Author: wangsh
	 */
	public TechAchievement findById(String id) {
		TechAchievement techAchievement = techAchievementMapper.selectByPrimaryKey(id);
		TechAchievementVo techAchievementVo = new TechAchievementVo();
		BeanUtils.copyProperties(techAchievement, techAchievementVo);
		// 附件
		techAchievementVo.setFiles(basicFileAppendixService.findByFormId(techAchievement.getId()));
		// 人员
		techAchievementVo.setPersons(basicPersonLinkedService.findByFormId(techAchievement.getId()));
		// 流程
		if(StringUtils.isNotBlank(techAchievementVo.getFlowStatus())) {
			techAchievementVo.setFlowTaskDto(flowCommonService.findTaskDto(techAchievementVo));
		}
		return techAchievementVo;
	}

	@Override
	/**
	 *@Description: 分页查询成果统计
	 *@param techAchievementVo
	 *@return PageInfo<TechAchievement>
	 *@Author: wangsh
	 */
	public PageInfo<TechAchievement> findPageByQuery(TechAchievementVo techAchievementVo) {
		PageHelper.startPage(techAchievementVo.getPageNum(),techAchievementVo.getPageSize());
		Example example=new Example(TechAchievement.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(techAchievementVo.getName())){
		//	criteria.andEqualTo(techAchievementVo.getName());
		//}
		List<TechAchievement> techAchievementList=techAchievementMapper.selectByExample(example);
		return new PageInfo<TechAchievement>(techAchievementList);
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 成果统计信息提交
	 */
	public String submitTechAchievement(TechAchievementVo techAchievementVo) {
		String id = this.saveOrUpdateTechAchievement(techAchievementVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, techAchievementVo, this.mapper,
				StringUtils.isNotBlank(techAchievementVo.getAuditAdvice())?techAchievementVo.getAuditAdvice():"提交成果统计申请");
		/*TechAchievement achievement = this.findById(id);
		SysUser loginAppUser = (SysUser) AppUserUtil.getLoginAppUser();
		String taskId = null;
		if(StringUtils.isNotBlank(achievement.getFlowStatus())){
			taskId = flowApiService.getTaskId(id);
		}

		FlowTaskVo flowTaskVo = techFlowService.createFlowTaskVo(taskId, id, "提交成果统计申请", null, null);
		Integer operType = SysUserUtil.getUserOperType();
		switch (operType){
			case 0:
				flowTaskVo.setAssignee(AssigneeConstant.DEPT_COUNTY_ROLE);
				techAchievementVo.setFlowStatus(FlowStatusEnum.COUNTY_AUDIT.getCode());
				break;
			case 1:
				flowTaskVo.setAssignee(AssigneeConstant.DEPT_CITY_ROLE);
				techAchievementVo.setFlowStatus(FlowStatusEnum.CITY_AUDIT.getCode());
				break;
			case 2:
				flowTaskVo.setAssignee(AssigneeConstant.DEPT_PROVINCE_ROLE);
				techAchievementVo.setFlowStatus(FlowStatusEnum.PROVINCE_AUDIT.getCode());
				break;
			default:
		}
		techAchievementVo.setId(id);
		this.saveOrUpdateTechAchievement(techAchievementVo);

		if(StringUtils.isBlank(taskId)){
			// 不存在任务，创建任务
			flowTaskVo.setProcessInstanceKey(FlowableConstant.FLOW_TECH_ACHIEVEMENT);
			flowTaskVo.setUserId(loginAppUser.getId()); //当前流程发起者的用户信息
			flowTaskVo.setUserName(loginAppUser.getNickname());
			taskId = flowApiService.startProcessInstanceByKey(flowTaskVo);
		}

		Map<String, Object> map = new HashMap<String, Object>();
		map.put("CODETYPE", operType);
		map.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());
		flowTaskVo.setValues(map);
		flowTaskVo.setTaskId(taskId);
		flowApiService.completeTask(flowTaskVo);*/

		return id;
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 审核
	 */
	public String auditTechAchievement(TechAchievementVo techAchievementVo) {
		String id = techAchievementVo.getId();
		flowCommonService.doFlowStepAudit(techAchievementVo, this.mapper
				, StringUtils.isNotBlank(techAchievementVo.getAuditAdvice())?techAchievementVo.getAuditAdvice():"成果统计审核通过"
				, FlowStatusEnum.END.getCode());
		/*String taskId = flowApiService.getTaskId(id);
		FlowTaskVo flowTaskVo = techFlowService.createFlowTaskVo(taskId, id, "通过审核成果统计", null, null);
		// Integer codeType = techAchievementVo.getOperType();
		Integer codeType = SysUserUtil.getUserOperType();
		switch (codeType){
			case 0: // 县级审核  设置下一级为市级
				flowTaskVo.setAssignee(AssigneeConstant.DEPT_CITY_ROLE);
				techAchievementVo.setFlowStatus(FlowStatusEnum.CITY_AUDIT.getCode());
				break;
			case 1: // 市级审核  设置下一级为省级
				flowTaskVo.setAssignee(AssigneeConstant.DEPT_PROVINCE_ROLE);
				techAchievementVo.setFlowStatus(FlowStatusEnum.PROVINCE_AUDIT.getCode());
				break;
			case 2: // 省级审核 审核完后结束
				//flowTaskVo.setAssignee(AssigneeConstant.DEPT_PROVINCE_ROLE);
				techAchievementVo.setFlowStatus(FlowStatusEnum.END.getCode());
				break;
			default:
		}
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("ISPASS", 1);
		map.put(ProcessConstants.PROCESS_INITIATOR, flowTaskVo.getAssignee());
		flowTaskVo.setValues(map);

		this.saveOrUpdateTechAchievement(techAchievementVo);
		flowApiService.completeTask(flowTaskVo);*/
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * 退回
	 */
	public String sendBackTechAchievement(TechAchievementVo techAchievementVo) {
		String id = techAchievementVo.getId();
		flowCommonService.doFlowStepSendBack(techAchievementVo, this.mapper
				, StringUtils.isNotBlank(techAchievementVo.getAuditAdvice())?techAchievementVo.getAuditAdvice():"成果统计退回"
				, techAchievementVo.getToUpper()!=null?techAchievementVo.getToUpper():false
		);
		return id;
	}

	@Override
	public PageInfo todoTechAchievement(TechAchievementVo techAchievementVo) {
		Example example = new Example(TechAchievement.class);
		Criteria criteria = getCriteria(techAchievementVo, example);
		//PageHelper.startPage(techAchievementVo.getPageNum(),techAchievementVo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(techAchievementVo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo finishedTechAchievement(TechAchievementVo techAchievementVo) {
		Example example = new Example(TechAchievement.class);
		Criteria criteria = getCriteria(techAchievementVo, example);
		//PageHelper.startPage(techAchievementVo.getPageNum(),techAchievementVo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(techAchievementVo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TechAchievement> endList(TechAchievementVo techAchievementVo) {
		Example example=new Example(TechAchievement.class);
		example.orderBy("createTime").desc();
		Criteria criteria=getCriteria(techAchievementVo, example);
		return new PageInfo<>(flowCommonService.endList(techAchievementVo, this.mapper, example, criteria));
	}

	private Criteria getCriteria(TechAchievementVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getAchievementName())){
			criteria.andLike("achievementName", "%" + vo.getAchievementName()+ "%");
		}
		if(StringUtils.isNotBlank(vo.getLeaderName())){
			criteria.andLike("leaderName", "%" + vo.getLeaderName()+ "%");
			// criteria.andCondition("id in (select form_id from basic_person_linked b where b.form_id = id and b.name like '%" + vo.getLeaderName() + "%;)")
		}
		if(StringUtils.isNotBlank(vo.getTypeCode())){
			criteria.andEqualTo("typeCode", vo.getTypeCode());
		}
		if(StringUtils.isNotBlank(vo.getTypeSecondCode())){
			criteria.andEqualTo("typeSecondCode", vo.getTypeSecondCode());
		}
		if(null != vo.getIsProvince()){
			criteria.andEqualTo("isProvince", vo.getIsProvince());
		}
		if(null != vo.getStartTime()){
			criteria.andGreaterThanOrEqualTo("achievementTime", vo.getStartTime());
		}
		if(null != vo.getEndTime()){
			criteria.andLessThanOrEqualTo("achievementTime", new Date(vo.getEndTime().getTime() + 24*3600*1000L-1));
		}
		example.orderBy("createTime").desc();
		return criteria;
	}

	@Override
	@Transactional(readOnly = false)
	public String rejectTechAchievement(TechAchievementVo vo) {
		/*vo = (TechAchievementVo) findById(vo.getId());
		List<FlowTaskDto> hisFlowList = (List<FlowTaskDto>) ((HashMap) flowTaskService.flowRecord(vo.getFlowTaskDto().getProcInsId(), null).getData()).get("flowList");

		// 上一个任务节点
		FlowTaskDto backFlowTaskDto = hisFlowList.get(1);

		String backstatus = String.valueOf(Integer.valueOf(vo.getFlowStatus())-1);
		String backRole = null;
		for (FlowStatusEnum fse:FlowStatusEnum.values()
			 ) {
			if (fse.getFlowName().equals(backFlowTaskDto.getActivityName())){
				backRole = fse.getRole();
				backstatus = fse.getCode();
				break;
			}
		}
		if(StringUtils.isBlank(backRole)){
			throw new ServiceException("流程退回异常");
		}
		vo.setFlowStatus(backstatus);
		vo.setFlowUser(backRole);
		this.saveOrUpdateTechAchievement(vo);

		String taskId = flowApiService.getTaskId(vo.getId());
		FlowTaskVo flowTaskVo = new FlowTaskVo();
		flowTaskVo.setTaskId(taskId);
		flowTaskVo.setBusinessKey(vo.getId());
		flowTaskVo.setUserId("");
		flowTaskVo.setUserName("测试名");
		flowTaskVo.setComment("撤销测试");
		flowTaskVo.setAssignee(AssigneeConstant.ORG_HEAD_ROLE);
		flowTaskVo.setTargetKey(backFlowTaskDto.getActivityId());
		flowApiService.taskReturn(flowTaskVo);*/
		return null;
	}

	@Override
	public void exportTechAchievement(TechAchievementVo techAchievementVo, HttpServletResponse response) {
		techAchievementVo.setPageNum(1);
		techAchievementVo.setPageSize(Integer.MAX_VALUE);
		List exportList = null;
		if(StringUtils.isNotBlank(techAchievementVo.getExportType())) {
			switch (techAchievementVo.getExportType()) {
				case "todo":
					exportList = todoTechAchievement(techAchievementVo).getList();
					break;
				case "finished":
					exportList = finishedTechAchievement(techAchievementVo).getList();
					break;
				case "end":
					exportList = endList(techAchievementVo).getList();
					break;
				default:
					exportList = findPageByQuery(techAchievementVo).getList();
			}
		}
		if(exportList == null){
			exportList = findPageByQuery(techAchievementVo).getList();
		}

		InputStream inputStream = ExportUtil.getInputStream("成果统计导出.xlsx");
		EasyExcelUtils.exportTemplateFill(null, exportList, "Sheet1", "成果统计.xlsx", response, inputStream, true);
	}

}
