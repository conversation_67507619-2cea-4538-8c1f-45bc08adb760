package com.fd.stdp.service.talent;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentLeaderSubjectContract;
import com.fd.stdp.beans.talent.vo.TalentLeaderSubjectContractVo;
/**
 *@Description: 学科带头人建设任务书
 *@Author: wangsh
 *@Date: 2022-02-16 09:00:47
 */
public interface TalentLeaderSubjectContractService {

	/**
	 *@Description: 保存或更新学科带头人建设任务书
	 *@param talentLeaderSubjectContract 学科带头人建设任务书对象
	 *@return String 学科带头人建设任务书ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTalentLeaderSubjectContract(TalentLeaderSubjectContractVo talentLeaderSubjectContract);
	
	/**
	 *@Description: 删除学科带头人建设任务书
	 *@param id void 学科带头人建设任务书ID
	 *@Author: wangsh
	 */
	void deleteTalentLeaderSubjectContract(String id);

	/**
	 * @Description: 批量删除学科带头人建设任务书
	 * @param ids
	 */
    void deleteMultiTalentLeaderSubjectContract(List<String> ids);

	/**
	 *@Description: 查询学科带头人建设任务书详情
	 *@param id
	 *@return TalentLeaderSubjectContract
	 *@Author: wangsh
	 */
	TalentLeaderSubjectContract findById(String id);

	/**
	 *@Description: 分页查询学科带头人建设任务书
	 *@param talentLeaderSubjectContractVo
	 *@return PageInfo<TalentLeaderSubjectContract>
	 *@Author: wangsh
	 */
	PageInfo<TalentLeaderSubjectContract> findPageByQuery(TalentLeaderSubjectContractVo talentLeaderSubjectContractVo);
	
	
	/**
	 * 提交
	 * @param talentLeaderSubjectContractVo
	 * @return
	 */
    String submitTalentLeaderSubjectContract(TalentLeaderSubjectContractVo talentLeaderSubjectContractVo);

	/**
	 * 审核
	 * @param talentLeaderSubjectContractVo
	 * @return
	 */
	String auditTalentLeaderSubjectContract(TalentLeaderSubjectContractVo talentLeaderSubjectContractVo);

	/**
	 * 退回
	 * @param talentLeaderSubjectContractVo
	 * @return
	 */
	String sendBackTalentLeaderSubjectContract(TalentLeaderSubjectContractVo talentLeaderSubjectContractVo);

	/**
	 * 任务书下达
	 * @param talentLeaderSubjectContractVo
	 * @return
	 */
	String releaseTalentLeaderSubjectContract(TalentLeaderSubjectContractVo talentLeaderSubjectContractVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<TalentLeaderSubjectContract> todoList(TalentLeaderSubjectContractVo talentLeaderSubjectContractVo);

	/**
	 * 已办列表
	 */
	PageInfo<TalentLeaderSubjectContract> finishedList(TalentLeaderSubjectContractVo talentLeaderSubjectContractVo);

	/**
	 * 已完成列表
	 */
	PageInfo<TalentLeaderSubjectContract> endList(TalentLeaderSubjectContractVo talentLeaderSubjectContractVo);
}
