package com.fd.stdp.service.talent.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import com.fd.stdp.beans.basic.vo.BasicPersonLinkedVo;
import com.fd.stdp.beans.talent.TalentTeamContributePlan;
import com.fd.stdp.beans.talent.TalentTeamContributeTarget;
import com.fd.stdp.beans.talent.vo.TalentTeamContributeVo;
import com.fd.stdp.dao.talent.TalentTeamContributePlanMapper;
import com.fd.stdp.dao.talent.TalentTeamContributeTargetMapper;
import com.fd.stdp.service.basic.BasicGradeLinkedService;
import com.fd.stdp.service.basic.BasicPersonLinkedService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamExamine;
import com.fd.stdp.beans.talent.vo.TalentTeamExamineVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.talent.TalentTeamExamineMapper;
import com.fd.stdp.service.talent.TalentTeamExamineService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 创新团队考核
 *@Author: wangsh
 *@Date: 2022-02-14 10:22:36
 */
public class TalentTeamExamineServiceImpl extends BaseServiceImpl<TalentTeamExamineMapper, TalentTeamExamine> implements TalentTeamExamineService{

	public static final Logger logger = LoggerFactory.getLogger(TalentTeamExamineServiceImpl.class);

	private static final String NUMBER = "NUMBER";
	private static final String EXPERT = "EXPERT";
	@Autowired
	private TalentTeamExamineMapper talentTeamExamineMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private TalentTeamContributePlanMapper talentTeamContributePlanMapper;
	@Autowired
	private TalentTeamContributeTargetMapper talentTeamContributeTargetMapper;
	@Autowired
	private BasicPersonLinkedService basicPersonLinkedService;
	@Autowired
	private BasicGradeLinkedService basicGradeLinkedService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新创新团队考核
	 *@param talentTeamExamine 创新团队考核对象
	 *@return String 创新团队考核ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTalentTeamExamine(TalentTeamExamineVo vo) {
		saveOrUpdateTalentTeamExamineNoFlow(vo);
		flowCommonService.doFlowStart("FLOW_TALENT_TEAM_APPLY", vo, this.mapper, "创新团队考核流程开始");
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String saveOrUpdateTalentTeamExamineNoFlow(TalentTeamExamineVo vo) {

		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			talentTeamExamineMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			talentTeamExamineMapper.updateByPrimaryKeySelective(vo);
		}

		// 团队成员
		List<BasicPersonLinkedVo> list = new ArrayList();
		if(!CollectionUtils.isEmpty(vo.getTeamNumbers())){
			vo.getTeamNumbers().stream().forEach(p->p.setPersonType(NUMBER));
			basicPersonLinkedService.clearAndUpdateBasicPersonLinked(vo.getId(), vo.getTeamNumbers(), NUMBER);
		}
		// 合作专家
		if(!CollectionUtils.isEmpty(vo.getTeamExperts())){
			vo.getTeamExperts().stream().forEach(p->p.setPersonType(EXPERT));
			basicPersonLinkedService.clearAndUpdateBasicPersonLinked(vo.getId(), vo.getTeamExperts(), EXPERT);
		}

		if(!CollectionUtils.isEmpty(vo.getContractPlans())){
			updateList(vo, vo.getContractPlans(), talentTeamContributePlanMapper,"setContractId");
		}

		if(!CollectionUtils.isEmpty(vo.getContractTargets())){
			updateList(vo, vo.getContractTargets(), talentTeamContributeTargetMapper,"setContractId");
		}

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除创新团队考核
	 *@param id void 创新团队考核ID
	 *@Author: wangsh
	 */
	public void deleteTalentTeamExamine(String id) {
		//TODO 做判断后方能执行删除
		TalentTeamExamine talentTeamExamine=talentTeamExamineMapper.selectByPrimaryKey(id);
		if(talentTeamExamine==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TalentTeamExamine temtalentTeamExamine=new TalentTeamExamine();
		temtalentTeamExamine.setYn(CommonConstant.FLAG_NO);
		temtalentTeamExamine.setId(talentTeamExamine.getId());
		talentTeamExamineMapper.updateByPrimaryKeySelective(temtalentTeamExamine);
	}

    /**
     * @Description: 批量删除创新团队考核
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTalentTeamExamine(List<String> ids) {
		ids.stream().forEach(id-> this.deleteTalentTeamExamine(id));
	}

	@Override
	/**
	 *@Description: 查询创新团队考核详情
	 *@param id
	 *@return TalentTeamExamine
	 *@Author: wangsh
	 */
	public TalentTeamExamine findById(String id) {
		TalentTeamExamine talentTeamExamine = talentTeamExamineMapper.selectByPrimaryKey(id);
		TalentTeamExamineVo vo = new TalentTeamExamineVo();
		BeanUtils.copyProperties(talentTeamExamine, vo);
		List<BasicPersonLinkedVo> bv = basicPersonLinkedService.findByFormId(id);
		vo.setTeamExperts(bv.stream().filter(b->EXPERT.equals(b.getPersonType())).collect(Collectors.toList()));
		vo.setTeamNumbers(bv.stream().filter(b->NUMBER.equals(b.getPersonType())).collect(Collectors.toList()));

		Example example = new Example(TalentTeamContributePlan.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("contractId", id);
		vo.setContractPlans(talentTeamContributePlanMapper.selectByExample(example));

		example = new Example(TalentTeamContributeTarget.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("contractId", id);
		vo.setContractTargets(talentTeamContributeTargetMapper.selectByExample(example));

		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询创新团队考核
	 *@param talentTeamExamineVo
	 *@return PageInfo<TalentTeamExamine>
	 *@Author: wangsh
	 */
	public PageInfo<TalentTeamExamine> findPageByQuery(TalentTeamExamineVo talentTeamExamineVo) {
		PageHelper.startPage(talentTeamExamineVo.getPageNum(),talentTeamExamineVo.getPageSize());
		Example example=new Example(TalentTeamExamine.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		criteria.andEqualTo("flowStatus",FlowStatusEnum.END.getCode());
		//查询条件
		//if(!StringUtils.isEmpty(talentTeamExamineVo.getName())){
		//	criteria.andEqualTo(talentTeamExamineVo.getName());
		//}
		List<TalentTeamExamine> talentTeamExamineList=talentTeamExamineMapper.selectByExample(example);
		return new PageInfo<TalentTeamExamine>(talentTeamExamineList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitTalentTeamExamine(TalentTeamExamineVo talentTeamExamineVo) {
		String id = this.saveOrUpdateTalentTeamExamine(talentTeamExamineVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TALENT_TEAM_APPLY, talentTeamExamineVo, this.mapper,
				StringUtils.isNotBlank(talentTeamExamineVo.getAuditAdvice())?talentTeamExamineVo.getAuditAdvice():"提交创新团队考核");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditTalentTeamExamine(TalentTeamExamineVo talentTeamExamineVo) {
		if(StringUtils.equals(talentTeamExamineVo.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())
				&& CollectionUtils.isEmpty(talentTeamExamineVo.getGradeExperts())){
			throw new ServiceException("评审专家不得为空");
		}
		// 省局审核会选择专家
		if(!CollectionUtils.isEmpty(talentTeamExamineVo.getGradeExperts())){
			List<BasicGradeLinkedVo> basicGradeLinkedVoList = new ArrayList<>();
			talentTeamExamineVo.getGradeExperts().stream().forEach(basicPersonLinkedVo -> {
				BasicGradeLinkedVo b = new BasicGradeLinkedVo();
				b.setUserId(basicPersonLinkedVo.getPersonId());
				basicGradeLinkedVoList.add(b);
			});
			basicGradeLinkedService.clearAndUpdateBasicGradeLinked(talentTeamExamineVo.getId(), basicGradeLinkedVoList);
		}
		flowCommonService.doFlowStepAudit(talentTeamExamineVo, this.mapper
				, StringUtils.isNotBlank(talentTeamExamineVo.getAuditAdvice()) ? talentTeamExamineVo.getAuditAdvice() : "创新团队考核审核通过"
				, FlowStatusEnum.CHOOSE_EXPERTS.getCode());
		return talentTeamExamineVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackTalentTeamExamine(TalentTeamExamineVo talentTeamExamineVo) {
		flowCommonService.doFlowStepSendBack(talentTeamExamineVo, this.mapper
				, StringUtils.isNotBlank(talentTeamExamineVo.getAuditAdvice())?talentTeamExamineVo.getAuditAdvice():"创新团队考核退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseTalentTeamExamine(TalentTeamExamineVo talentTeamExamineVo) {
		flowCommonService.doCompleteTask(talentTeamExamineVo, this.mapper
				, "创新团队考核任务书下达完成"
				, FlowStatusEnum.PROJECT_RELEASE.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<TalentTeamExamine> todoList(TalentTeamExamineVo vo) {

		Example example = new Example(TalentTeamExamine.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TalentTeamExamine> finishedList(TalentTeamExamineVo vo) {
		Example example = new Example(TalentTeamExamine.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	private Criteria getCriteria(TalentTeamExamineVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("teamLeader", "%" + vo.getName()+ "%");
		}
		return criteria;
	}
}
