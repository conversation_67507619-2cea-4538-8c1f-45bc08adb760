package com.fd.stdp.service.sys.impl;

import com.fd.stdp.common.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import com.fd.stdp.beans.sys.SysFileInfo;
import com.fd.stdp.config.FileSecurityConfig;
import com.fd.stdp.dao.sys.SysFileInfoMapper;
import com.fd.stdp.enums.FileSource;
import com.fd.stdp.service.sys.FileService;
import com.fd.stdp.util.FileUtil;
import com.fd.stdp.util.UUIDUtils;

import java.io.InputStream;

public abstract class AbstractFileService implements FileService {
    public static final Logger logger = LoggerFactory.getLogger(AbstractFileService.class);

    protected abstract SysFileInfoMapper getFileDao();

    @Override
    public SysFileInfo upload(MultipartFile file) throws Exception {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            throw new ServiceException("文件名不能为空");
        }
        
        // 使用统一的安全检查
        if (!FileSecurityConfig.isSecureFilename(originalFilename)) {
            throw new ServiceException("文件名包含非法字符");
        }
        
        // 检查文件大小
        if (FileSecurityConfig.isFileSizeExceeded(file.getSize())) {
            throw new ServiceException("文件大小超过限制（最大50MB）");
        }
        
        // 获取真实文件扩展名，统一转小写处理
        String fileExt = "";
        if (originalFilename.lastIndexOf('.') >= 0) {
            fileExt = originalFilename.substring(originalFilename.lastIndexOf('.')+1).toLowerCase();
        } else {
            throw new ServiceException("文件缺少扩展名");
        }
        
        // 检查是否为危险文件类型
        if (FileSecurityConfig.isDangerousExtension(fileExt)) {
            throw new ServiceException("禁止上传的危险文件类型: " + fileExt);
        }
        
        // 检查文件扩展名是否在允许列表中
        if (!FileSecurityConfig.isAllowedExtension(fileExt)) {
            throw new ServiceException("不支持的文件类型: " + fileExt);
        }
        
        // 上传文件的信息
        SysFileInfo fileInfo = FileUtil.getFileInfo(file);
        // 先根据文件md5查询记录
        SysFileInfo oldFileInfo = getFileDao().selectByPrimaryKey(fileInfo.getId());

        if (oldFileInfo != null) {// 如果已存在文件，避免重复上传同一个文件
            return oldFileInfo;
        }
        
        // 检查PDF文件XSS漏洞
        if(FileUtil.verificationXss(file)){
            throw new ServiceException("非法PDF");
        }
        
        // 检查文件内容与扩展名是否匹配
        try(InputStream inputStream = file.getInputStream()){
            if(!FileUtil.isFileType(inputStream, FileSecurityConfig.ALLOWED_EXTENSIONS)){
                throw new ServiceException("文件内容与扩展名不匹配，不支持的文件类型");
            }
        }
        
        uploadFile(file, fileInfo);
        fileInfo.setSource(fileSource().name());// 设置文件来源
        fileInfo.setId(UUIDUtils.getUUID());
        getFileDao().insertSelective(fileInfo);// 将文件信息保存到数据库
        logger.info("上传文件：{}", fileInfo);
        return fileInfo;
    }

    /**
     * 重载上传方法
     *
     * <AUTHOR>
     */
    @Override
    public SysFileInfo upload(MultipartFile file, SysFileInfo sysfileInfo) throws Exception {

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            throw new ServiceException("文件名不能为空");
        }
        
        // 使用统一的安全检查
        if (!FileSecurityConfig.isSecureFilename(originalFilename)) {
            throw new ServiceException("文件名包含非法字符");
        }
        
        // 检查文件大小
        if (FileSecurityConfig.isFileSizeExceeded(file.getSize())) {
            throw new ServiceException("文件大小超过限制（最大50MB）");
        }
        
        // 获取真实文件扩展名，统一转小写处理
        String fileExt = "";
        if (originalFilename.lastIndexOf('.') >= 0) {
            fileExt = originalFilename.substring(originalFilename.lastIndexOf('.')+1).toLowerCase();
        } else {
            throw new ServiceException("文件缺少扩展名");
        }
        
        // 检查是否为危险文件类型
        if (FileSecurityConfig.isDangerousExtension(fileExt)) {
            throw new ServiceException("禁止上传的危险文件类型: " + fileExt);
        }
        
        // 检查文件扩展名是否在允许列表中
        if (!FileSecurityConfig.isAllowedExtension(fileExt)) {
            throw new ServiceException("不支持的文件类型: " + fileExt);
        }
        
        // 检查PDF文件XSS漏洞
        if(FileUtil.verificationXss(file)){
            throw new ServiceException("非法PDF");
        }
        
        // 检查文件内容与扩展名是否匹配
        try(InputStream inputStream = file.getInputStream()){
            if(!FileUtil.isFileType(inputStream, FileSecurityConfig.ALLOWED_EXTENSIONS)){
                throw new ServiceException("文件内容与扩展名不匹配，不支持的文件类型");
            }
        }

        // 上传文件的信息
        SysFileInfo fileInfo = FileUtil.getFileInfo(file);
        // 先根据文件md5查询记录
        SysFileInfo oldFileInfo = getFileDao().selectByPrimaryKey(fileInfo.getId());
        if (oldFileInfo != null) {// 如果已存在文件，避免重复上传同一个文件
            return oldFileInfo;
        }
        
        uploadFile(file, fileInfo);
        fileInfo.setSource(fileSource().name());// 设置文件来源
        fileInfo.setId(UUIDUtils.getUUID());
        getFileDao().insertSelective(fileInfo);// 将文件信息保存到数据库
        logger.info("上传文件：{}", fileInfo);
        return fileInfo;
    }

    /**
     * 重载上传方法
     *
     * <AUTHOR>
     */
    @Override
    public SysFileInfo upload(MultipartFile file, SysFileInfo sysfileInfo, String markPath) throws Exception {
        // 上传文件的信息
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            throw new ServiceException("文件名不能为空");
        }
        
        // 使用统一的安全检查
        if (!FileSecurityConfig.isSecureFilename(originalFilename)) {
            throw new ServiceException("文件名包含非法字符");
        }
        
        // 检查文件大小
        if (FileSecurityConfig.isFileSizeExceeded(file.getSize())) {
            throw new ServiceException("文件大小超过限制（最大50MB）");
        }
        
        // 获取真实文件扩展名，统一转小写处理
        String fileExt = "";
        if (originalFilename.lastIndexOf('.') >= 0) {
            fileExt = originalFilename.substring(originalFilename.lastIndexOf('.')+1).toLowerCase();
        } else {
            throw new ServiceException("文件缺少扩展名");
        }
        
        // 检查是否为危险文件类型
        if (FileSecurityConfig.isDangerousExtension(fileExt)) {
            throw new ServiceException("禁止上传的危险文件类型: " + fileExt);
        }
        
        // 检查文件扩展名是否在允许列表中
        if (!FileSecurityConfig.isAllowedExtension(fileExt)) {
            throw new ServiceException("不支持的文件类型: " + fileExt);
        }
        
        SysFileInfo fileInfo = FileUtil.getFileInfo(file);
        // 先根据文件md5查询记录
        SysFileInfo oldFileInfo = getFileDao().selectByPrimaryKey(fileInfo.getId());
        if (oldFileInfo != null) {// 如果已存在文件，避免重复上传同一个文件
            return oldFileInfo;
        }

        // 检查PDF文件XSS漏洞
        if(FileUtil.verificationXss(file)){
            throw new ServiceException("非法PDF");
        }
        
        // 检查文件内容与扩展名是否匹配
        try(InputStream inputStream = file.getInputStream()){
            if(!FileUtil.isFileType(inputStream, FileSecurityConfig.ALLOWED_EXTENSIONS)){
                throw new ServiceException("文件内容与扩展名不匹配，不支持的文件类型");
            }
        }

        uploadFile(file, fileInfo, markPath);

        fileInfo.setSource(fileSource().name());// 设置文件来源
        fileInfo.setId(UUIDUtils.getUUID());
        getFileDao().insertSelective(fileInfo);// 将文件信息保存到数据库
        logger.info("上传文件：{}", fileInfo);
        return fileInfo;
    }

    /**
     * 文件来源
     *
     * @return
     */
    protected abstract FileSource fileSource();

    /**
     * 上传文件
     *
     * @param file
     * @param fileInfo
     */
    protected abstract void uploadFile(MultipartFile file, SysFileInfo fileInfo) throws Exception;

    /**
     * 上传文件
     *
     * @param file
     * @param fileInfo
     */
    protected abstract void uploadFile(MultipartFile file, SysFileInfo fileInfo, String markPath) throws Exception;

    @Override
    public void delete(SysFileInfo fileInfo) {
        deleteFile(fileInfo);
        getFileDao().deleteByPrimaryKey(fileInfo.getId());
        logger.info("删除文件：{}", fileInfo);
    }

    /**
     * 删除文件资源
     *
     * @param fileInfo
     * @return
     */
    protected abstract boolean deleteFile(SysFileInfo fileInfo);

}
