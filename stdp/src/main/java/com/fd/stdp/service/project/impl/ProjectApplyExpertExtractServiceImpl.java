package com.fd.stdp.service.project.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.fd.stdp.beans.project.*;
import com.fd.stdp.beans.project.vo.ProjectApplyExpertExtractVo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.constant.ProjectDeclarationFlowConstants;
import com.fd.stdp.dao.project.*;
import com.fd.stdp.service.project.ProjectApplyExpertExtractService;
import com.fd.stdp.service.project.ProjectApplyInfoService;
import com.fd.stdp.util.AssertUtil;
import com.fd.stdp.util.expertExtract.ExpertApi;
import com.fd.stdp.util.expertExtract.beans.fegn.*;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.constant.CommonConstant;

/**
 * <AUTHOR>
 * @Description: 专家抽取任务Service业务层处理
 * @date 2025-03-18
 */
@Service
@Transactional(readOnly = true)
public class ProjectApplyExpertExtractServiceImpl extends BaseServiceImpl<ProjectApplyExpertExtractMapper, ProjectApplyExpertExtract> implements ProjectApplyExpertExtractService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectApplyExpertExtractServiceImpl.class);
    @Autowired
    private ProjectApplyExpertExtractMapper projectApplyExpertExtractMapper;

    @Autowired
    ExpertApi expertApi;

    @Autowired
    private ProjectApplyExtractProMapper extractProMapper;

    @Autowired
    private ProjectApplyInfoMapper projectApplyInfoMapper;

    @Autowired
    private ProjectApplyExpertExtractConditionsMapper expertExtractConditionsMapper;

    @Autowired
    private ProjectApplyExpertExtractExcludeMapper expertExtractExcludeMapper;

    @Autowired
    private ProjectApplyExpertMumberMapper projectApplyExpertMumberMapper;

    @Autowired
    private ProjectApplyInfoService projectApplyInfoService;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新专家抽取任务
     *@param projectApplyExpertExtract 专家抽取任务对象
     *@return String 专家抽取任务ID
     *@Author: zhangYu
     */
    public String saveOrUpdateProjectApplyExpertExtract(ProjectApplyExpertExtract projectApplyExpertExtract) {
        if (projectApplyExpertExtract == null) {
            throw new ServiceException("数据异常");
        }
        if (!StringUtils.hasText(projectApplyExpertExtract.getId())) {
            //新增
            projectApplyExpertExtract.setId(UUIDUtils.getUUID());
            projectApplyExpertExtractMapper.insertSelective(projectApplyExpertExtract);
        } else {
            //避免页面传入修改
            projectApplyExpertExtract.setYn(null);
            projectApplyExpertExtractMapper.updateByPrimaryKeySelective(projectApplyExpertExtract);
        }
        return projectApplyExpertExtract.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除专家抽取任务
     *@param id void 专家抽取任务ID
     *@Author: zhangYu
     */
    public void deleteProjectApplyExpertExtract(List<String> ids) {
        for (String id : ids) {
            ProjectApplyExpertExtract projectApplyExpertExtract = projectApplyExpertExtractMapper.selectByPrimaryKey(id);
            if (projectApplyExpertExtract == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            ProjectApplyExpertExtract temprojectApplyExpertExtract = new ProjectApplyExpertExtract();
            temprojectApplyExpertExtract.setYn(CommonConstant.FLAG_NO);
            temprojectApplyExpertExtract.setId(projectApplyExpertExtract.getId());
            projectApplyExpertExtractMapper.updateByPrimaryKeySelective(temprojectApplyExpertExtract);
        }
    }

    /**
     * @param id
     * @return ProjectApplyExpertExtract
     * @Description: 查询专家抽取任务详情
     * @Author: zhangYu
     */
    @Override
    public ProjectApplyExpertExtractVo findById(String id) {
        ProjectApplyExpertExtract expertExtract = projectApplyExpertExtractMapper.selectByPrimaryKey(id);
        AssertUtil.notNull(expertExtract, "数据异常");
        ProjectApplyExpertExtractVo vo = new ProjectApplyExpertExtractVo();
        BeanUtils.copyProperties(expertExtract, vo);

        //查询项目信息
        List<String> idList = Arrays.asList(vo.getApplyId().split(","));
        List<ProjectApplyInfoVo> infoList = new ArrayList<>();
        for (String proId : idList) {
            ProjectApplyInfoVo infoVo = projectApplyInfoMapper.findInfoById(proId);
            infoList.add(infoVo);
        }
        vo.setProjectApplyInfoVoList(infoList);

        //查询抽取条件
        List<ProjectApplyExpertExtractConditions> conditions = expertExtractConditionsMapper.findByExtractId(id);
        if (!CollectionUtils.isEmpty(conditions)) {
            vo.setProjectApplyExpertExtractConditionsList(conditions);
        }

        //查询排除条件
        List<ProjectApplyExpertExtractExclude> exclude = expertExtractExcludeMapper.findByExtractId(id);
        if (!CollectionUtils.isEmpty(exclude)) {
            vo.setProjectApplyExpertExtractExcludeList(exclude);
        }

        //抽取到的专家
        RestFindBusiRandom findBusiRandom = new RestFindBusiRandom();
        findBusiRandom.setId(expertExtract.getExpertTaskId());
        BusiRandomVo busiRandomVo = expertApi.findByRandomId(findBusiRandom);
        if (busiRandomVo != null && (CommonConstant.FLAG_II.equals(busiRandomVo.getStatus()) || CommonConstant.FLAG_III.equals(busiRandomVo.getStatus()))
                && busiRandomVo.getRandomExpertVos().size() > 0) {
            vo.setBusiRandomExpertList(busiRandomVo.getRandomExpertVos());
        }
        return vo;
    }


    /**
     * @param vo
     * @return PageInfo<ProjectApplyExpertExtract>
     * @Description: 分页查询专家抽取任务
     * @Author: zhangYu
     */
    @Override
    @Transactional(readOnly = false)
    public List<ProjectApplyExpertExtractVo> findPageByQuery(ProjectApplyExpertExtractVo vo) {
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<ProjectApplyExpertExtractVo> voList = projectApplyExpertExtractMapper.findPageByQuery(vo);
        if (!CollectionUtils.isEmpty(voList)) {
            for (ProjectApplyExpertExtractVo extractVo : voList) {
                RestFindBusiRandom findBusiRandom = new RestFindBusiRandom();
                findBusiRandom.setId(extractVo.getExpertTaskId());
                BusiRandomVo busiRandomVo = expertApi.findByRandomId(findBusiRandom);
                //如果不是抽取中，更新抽取任务状态
                if (busiRandomVo != null && busiRandomVo.getStatus() != 0) {
                    ProjectApplyExpertExtract p = new ProjectApplyExpertExtract();
                    p.setId(extractVo.getId());
                    p.setStatus(busiRandomVo.getStatus());
                    p.setStatusTxt(busiRandomVo.getStatusTxt());
                    projectApplyExpertExtractMapper.updateByPrimaryKeySelective(p);
                }
            }
        }
        return voList;
    }

    /**
     * @param restBusiRandom
     * @return ProjectApplyExpertExtract
     * @Description: 抽取专家
     * @Author: zhangYu
     */
    @Transactional(readOnly = false)
    @Override
    public ProjectApplyExpertExtract extract(RestBusiRandom restBusiRandom) {
        if (CollectionUtils.isEmpty(restBusiRandom.getProjectIdList())) {
            throw new ServiceException("请选择项目");
        }

        List<RestBusiRandomCondition> randomConditions = new ArrayList<>();
        // 抽取条件
        List<ProjectApplyExpertExtractConditions> conditionsList = restBusiRandom.getExtractList();
        if (!CollectionUtils.isEmpty(conditionsList)) {
            conditionsList.forEach(condition -> {
                RestBusiRandomCondition unified = new RestBusiRandomCondition();
                BeanUtils.copyProperties(condition, unified);
                randomConditions.add(unified);
            });
        }
        // 排除条件
        List<ProjectApplyExpertExtractExclude> excludeList = restBusiRandom.getExcludeList();
        if (!CollectionUtils.isEmpty(excludeList)) {
            excludeList.forEach(exclude -> {
                RestBusiRandomCondition unified = new RestBusiRandomCondition();
                BeanUtils.copyProperties(exclude, unified);
                randomConditions.add(unified);
            });
        }

        restBusiRandom.setRandomConditions(randomConditions);
        BusiRandomVo extract = expertApi.extract(restBusiRandom);

        System.out.println("抽取返回" + extract);

        //封装抽取任务对象
        ProjectApplyExpertExtract projectApplyExpertExtract = new ProjectApplyExpertExtract();
        projectApplyExpertExtract.setId(UUIDUtils.getUUID());
        projectApplyExpertExtract.setApplyId(String.join(",", restBusiRandom.getProjectIdList()));
        projectApplyExpertExtract.setExpertTaskNo(extract.getTaskNo());
        projectApplyExpertExtract.setExpertTaskId(extract.getId());
        projectApplyExpertExtract.setStatus(CommonConstant.FLAG_NO);
        projectApplyExpertExtract.setStatusTxt("抽取中");
        projectApplyExpertExtract.setTaskName(restBusiRandom.getTaskName());
        projectApplyExpertExtract.setReviewStartDate(restBusiRandom.getReviewStartDate());
        projectApplyExpertExtract.setReviewEndDate(restBusiRandom.getReviewEndDate());
        projectApplyExpertExtract.setDetails(restBusiRandom.getDetails());
        Optional<Integer> leadNum = restBusiRandom.getRandomConditions().stream().filter(item -> item.getPeopleType() == 1).map(RestBusiRandomCondition::getAmount).findFirst();
        projectApplyExpertExtract.setLeadNum(leadNum.orElse(0));
        Optional<Integer> crewNum = restBusiRandom.getRandomConditions().stream().filter(item -> item.getPeopleType() == 2).map(RestBusiRandomCondition::getAmount).findFirst();
        projectApplyExpertExtract.setCrewNum(crewNum.orElse(0));
        ProjectApplyExpertExtract expertExtract = projectApplyExpertExtractMapper.selectByApplyId(restBusiRandom.getApplyId());
        if (expertExtract != null) {
            projectApplyExpertExtractMapper.updateByPrimaryKeySelective(projectApplyExpertExtract);
        } else {
            projectApplyExpertExtractMapper.insertSelective(projectApplyExpertExtract);
        }

        //添加中间表
        for (String s : restBusiRandom.getProjectIdList()) {
            ProjectApplyExtractPro p = new ProjectApplyExtractPro();
            p.setId(UUIDUtils.getUUID());
            p.setProjectApplyInfoId(s);
            p.setProjectApplyExpertExtractId(projectApplyExpertExtract.getId());
            extractProMapper.insertSelective(p);

            //设置项目专家抽取状态
            ProjectApplyInfo info = new ProjectApplyInfo();
            info.setId(s);
            info.setExtractExpertStatus(1);
            projectApplyInfoMapper.updateByPrimaryKeySelective(info);
        }

        // 抽取条件
        if (!CollectionUtils.isEmpty(conditionsList)) {
            for (ProjectApplyExpertExtractConditions projectApplyExpertExtractConditions : conditionsList) {
                ProjectApplyExpertExtractConditions conditions = new ProjectApplyExpertExtractConditions();
                BeanUtils.copyProperties(projectApplyExpertExtractConditions, conditions);
                conditions.setId(UUIDUtils.getUUID());
                conditions.setApplyId(projectApplyExpertExtract.getId());
                expertExtractConditionsMapper.insertSelective(conditions);
            }
        }
        // 排除条件
        if (!CollectionUtils.isEmpty(excludeList)) {
            for (ProjectApplyExpertExtractExclude projectApplyExpertExtractExclude : excludeList) {
                ProjectApplyExpertExtractExclude exclude = new ProjectApplyExpertExtractExclude();
                BeanUtils.copyProperties(projectApplyExpertExtractExclude, exclude);
                exclude.setId(UUIDUtils.getUUID());
                exclude.setApplyId(projectApplyExpertExtract.getId());
                expertExtractExcludeMapper.insertSelective(exclude);
            }
        }

        return projectApplyExpertExtract;
    }


    /**
     * @param applyId
     * @return ProjectApplyExpertExtract
     * @Description: 根据申请ID查询专家抽取任务详情
     * @Author: zhangYu
     */
    @Override
    public ProjectApplyExpertExtract findByApplyId(String applyId) {
        return projectApplyExpertExtractMapper.selectByApplyId(applyId);
    }

    /**
     * @param restBusiRandom
     * @return ProjectApplyExpertExtract
     * @Description: 添加专家抽取条件
     * @Author: zhangYu
     */
    @Override
    @Transactional(readOnly = false)
    public BusiRandomVo addCondition(RestBusiRandom restBusiRandom) {

        List<RestBusiRandomCondition> randomConditions = new ArrayList<>();
        // 抽取条件
        List<ProjectApplyExpertExtractConditions> conditionsList = restBusiRandom.getExtractList();
        if (!CollectionUtils.isEmpty(conditionsList)) {
            conditionsList.forEach(condition -> {
                RestBusiRandomCondition unified = new RestBusiRandomCondition();
                BeanUtils.copyProperties(condition, unified);
                randomConditions.add(unified);
            });
        }
        // 排除条件
        List<ProjectApplyExpertExtractExclude> excludeList = restBusiRandom.getExcludeList();
        if (!CollectionUtils.isEmpty(excludeList)) {
            excludeList.forEach(exclude -> {
                RestBusiRandomCondition unified = new RestBusiRandomCondition();
                BeanUtils.copyProperties(exclude, unified);
                randomConditions.add(unified);
            });
        }

        restBusiRandom.setRandomConditions(randomConditions);

        BusiRandomVo vo = expertApi.addCondition(restBusiRandom);
        if (vo != null) {
            //更新抽取任务状态为抽取中
            ProjectApplyExpertExtract projectApplyExpertExtract = projectApplyExpertExtractMapper.selectByPrimaryKey(restBusiRandom.getId());
            AssertUtil.notNull(projectApplyExpertExtract,"非法请求");
            ProjectApplyExpertExtract p = new ProjectApplyExpertExtract();
            p.setId(projectApplyExpertExtract.getId());
            p.setStatus(CommonConstant.FLAG_NO);
            p.setStatusTxt("抽取中");
            projectApplyExpertExtractMapper.updateByPrimaryKeySelective(p);
        } else {
            throw new ServiceException("数据异常");
        }

        //设置项目专家抽取状态
        for (String s : Arrays.asList(",",restBusiRandom.getApplyId())) {
            ProjectApplyInfo info = new ProjectApplyInfo();
            info.setId(s);
            info.setExtractExpertStatus(1);
            projectApplyInfoMapper.updateByPrimaryKeySelective(info);
        }

        // 抽取条件
        if (!CollectionUtils.isEmpty(conditionsList)) {
            for (ProjectApplyExpertExtractConditions projectApplyExpertExtractConditions : conditionsList) {
                ProjectApplyExpertExtractConditions conditions = new ProjectApplyExpertExtractConditions();
                BeanUtils.copyProperties(projectApplyExpertExtractConditions, conditions);
                conditions.setId(UUIDUtils.getUUID());
                conditions.setApplyId(restBusiRandom.getId());
                expertExtractConditionsMapper.insertSelective(conditions);
            }
        }
        // 排除条件
        if (!CollectionUtils.isEmpty(excludeList)) {
            for (ProjectApplyExpertExtractExclude projectApplyExpertExtractExclude : excludeList) {
                ProjectApplyExpertExtractExclude exclude = new ProjectApplyExpertExtractExclude();
                BeanUtils.copyProperties(projectApplyExpertExtractExclude, exclude);
                exclude.setId(UUIDUtils.getUUID());
                exclude.setApplyId(restBusiRandom.getId());
                expertExtractExcludeMapper.insertSelective(exclude);
            }
        }

        return vo;
    }


    /**
     * 查询工作单位（回避条件）
     *
     * @param busiRandom
     * @return
     */
    @Override
    public List<String> findRecusalUnitListByQuery(BusiRandomVo busiRandom) {
        List<String> unitList = expertApi.findRecusalUnitListByQuery(busiRandom);
        return unitList;
    }

    @Override
    public List<String> findRecusalExportListByQuery(BusiRandomVo vo) {
        List<String> export = expertApi.findRecusalExpertListByQuery(vo);
        return export;
    }

    @Override
    public List<SysDictItem> findDictItemByDictType(BusiRandomExpert vo) {
        List<SysDictItem> list = expertApi.findDictItemByDictType(vo);
        return list;
    }

    @Override
    public List<TreeSelect> selectDictItemByDictType(BusiRandomExpert vo) {
        List<TreeSelect> treeSelects = expertApi.findChildrenByDomainCode(vo);
        return treeSelects;
    }

    @Override
    public Boolean changeExpertStatus(BusiRandomExpert vo) {
        Boolean aBoolean = expertApi.changeExpertStatus(vo);
        return aBoolean;
    }


    /**
     * @param id
     * @return BusiRandomVo
     * @Description: 查询专家抽取任务详情
     * @Author: zhangYu
     */
    @Override
    @Transactional(readOnly = false)
    public BusiRandomVo findRandomInfoById(String id) {
        ProjectApplyExpertExtract expertExtract = projectApplyExpertExtractMapper.selectByPrimaryKey(id);
        if (expertExtract != null) {
            RestFindBusiRandom findBusiRandom = new RestFindBusiRandom();
            findBusiRandom.setId(expertExtract.getExpertTaskId());
            BusiRandomVo busiRandomVo = expertApi.findByRandomId(findBusiRandom);
            //如果不是抽取中，更新抽取任务状态
            if (busiRandomVo != null && busiRandomVo.getStatus() != 0) {
                ProjectApplyExpertExtract p = new ProjectApplyExpertExtract();
                p.setId(expertExtract.getId());
                p.setStatus(busiRandomVo.getStatus());
                p.setStatusTxt(busiRandomVo.getStatusTxt());
                projectApplyExpertExtractMapper.updateByPrimaryKeySelective(p);
            }
            return busiRandomVo;
        } else {
            throw new ServiceException("未查询到数据");
        }
    }

    /**
     * 确认选择专家
     *
     * @param id
     */
    @Override
    @Transactional(readOnly = false)
    public void confirmExpert(String id, String userName, String RoleName) {
        ProjectApplyExpertExtract expertExtract = projectApplyExpertExtractMapper.selectByPrimaryKey(id);
        AssertUtil.notNull(expertExtract,"非法请求");

        //抽取到的专家
        RestFindBusiRandom findBusiRandom = new RestFindBusiRandom();
        findBusiRandom.setId(expertExtract.getExpertTaskId());
        BusiRandomVo busiRandomVo = expertApi.findByRandomId(findBusiRandom);
        List<BusiRandomExpert> expertList = new ArrayList<>();
        if (busiRandomVo != null && (CommonConstant.FLAG_II.equals(busiRandomVo.getStatus()) || CommonConstant.FLAG_III.equals(busiRandomVo.getStatus()))
                && busiRandomVo.getRandomExpertVos().size() > 0) {
            expertList.addAll(busiRandomVo.getRandomExpertVos());
        }
        if (CollectionUtils.isEmpty(expertList)){
            throw new ServiceException("未查询到抽取专家");
        }

        //查询项目信息
        List<String> idList = Arrays.asList(expertExtract.getApplyId().split(","));
        for (String proId : idList) {
            ProjectApplyInfoVo infoVo = projectApplyInfoMapper.findInfoById(proId);
            if (infoVo == null){
                continue;
            }
            // 保存项目专家
            for (BusiRandomExpert busiRandomExpert : expertList) {
                ProjectApplyExpertMumber expertMumber = new ProjectApplyExpertMumber();
                expertMumber.setId(UUIDUtils.getUUID());
                expertMumber.setApplyId(proId);
                expertMumber.setTaskId(id);
                expertMumber.setName(busiRandomExpert.getExpertName());
                expertMumber.setIdCardNumber(busiRandomExpert.getIdCard());
                expertMumber.setSex(busiRandomExpert.getSex() != null ? busiRandomExpert.getSex().toString() : "");
                expertMumber.setTitleName(busiRandomExpert.getMajorGradeTxt() != null ? busiRandomExpert.getMajorGradeTxt() : "");
                expertMumber.setPhone(busiRandomExpert.getPhone());
                expertMumber.setUnitName(busiRandomExpert.getWorkUnit() != null ? busiRandomExpert.getWorkUnit() : "");
                expertMumber.setProField(busiRandomExpert.getFieldName() != null ? busiRandomExpert.getFieldName() : "");
                expertMumber.setStatus(busiRandomExpert.getStatus().toString());
                expertMumber.setExpertType(busiRandomExpert.getPeopleType().toString());
                projectApplyExpertMumberMapper.insertSelective(expertMumber);
            }

            //推动流程
            ProjectApplyInfoVo applyVo = new ProjectApplyInfoVo();
            applyVo.setId(proId);
            applyVo.setActNodeKey(ProjectDeclarationFlowConstants.CHOOSE_EXPERT_TASK_CODE);
            applyVo.setFlowResult(CommonConstant.FLAG_YES.toString());
            applyVo.setAdvice("");
            projectApplyInfoService.applyAudit(applyVo, userName, RoleName);

            //更新项目专家抽取任务状态
            ProjectApplyInfo info = new ProjectApplyInfo();
            info.setId(proId);
            info.setExtractExpertStatus(CommonConstant.FLAG_II);
            projectApplyInfoMapper.updateByPrimaryKeySelective(info);
        }



        expertExtract.setAttr1(CommonConstant.FLAG_YES.toString());
        projectApplyExpertExtractMapper.updateByPrimaryKeySelective(expertExtract);
    }
}
