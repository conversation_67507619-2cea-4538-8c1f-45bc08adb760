package com.fd.stdp.service.sys.impl;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;

import javax.servlet.http.HttpServletResponse;

import com.aliyun.oss.model.PutObjectResult;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import com.fd.stdp.beans.sys.SysFileInfo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.dao.sys.SysFileInfoMapper;
import com.fd.stdp.enums.FileSource;
import com.fd.stdp.util.Base64Converter;
import com.fd.stdp.util.FileUtil;

/**
 * 阿里云存储文件
 * 
 * <AUTHOR>
 *
 */
@Service("aliyunFileServiceImpl")
public class AliyunFileServiceImpl extends AbstractFileService {
    public static final Logger logger = LoggerFactory.getLogger(AliyunFileServiceImpl.class);
    @Autowired
    private SysFileInfoMapper fileDao;

    @Override
    protected SysFileInfoMapper getFileDao() {
        return fileDao;
    }

    @Override
    protected FileSource fileSource() {
        return FileSource.ALIYUN;
    }

    @Autowired
    private OSS ossClient;

    @Value("${file.aliyun.bucketName}")
    private String bucketName;
    @Value("${file.aliyun.domain}")
    private String domain;
    @Value("${file.aliyun.savefolder}")
    private String savefolder;

    @Override
    protected void uploadFile(MultipartFile file, SysFileInfo fileInfo) throws Exception {
        String md5 = fileInfo.getName() + FileUtil.fileMd5(file.getInputStream());
        int index = fileInfo.getName().lastIndexOf(".");
        // 文件扩展名
        String fileSuffix = fileInfo.getName().substring(index);
        PutObjectResult result = ossClient.putObject(bucketName, savefolder + md5 + fileSuffix, file.getInputStream());
        String keyBase64 = Base64Converter.encode(savefolder + md5 + fileSuffix);
        fileInfo.setUrl(ossClient.generatePresignedUrl(bucketName, savefolder + md5 + fileSuffix, new Date(System.currentTimeMillis() + 100L *365*24*3600*1000)).toString().replaceAll("\\+", "%2b").replaceFirst("http", "https"));
        fileInfo.setPath(savefolder + md5 + fileSuffix);
    }

    @Override
    protected boolean deleteFile(SysFileInfo fileInfo) {
        ossClient.deleteObject(bucketName, fileInfo.getPath());
        return true;
    }

    @Override
    public void down(String key, HttpServletResponse response) {
        if (StringUtils.isEmpty(key)) {
            throw new ServiceException("非法请求");
        }
        OSSObject ossObject = null;
        int buffer = 1024 * 10;
        byte data[] = new byte[buffer];
        int lastIn = key.lastIndexOf('/');
        String fileName = lastIn < 0 ? key : key.substring(lastIn, key.length());
        try {
            ossObject = ossClient.getObject(bucketName, key);
            InputStream inputStream = ossObject.getObjectContent();
            // 设置excel的文件名称
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            response.setContentType("multipart/octet-stream");
            OutputStream out = response.getOutputStream();
            int read;
            while ((read = inputStream.read(data)) != -1) {
                out.write(data, 0, read);
            }
            out.flush();
            out.close();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (ossObject != null) {
                try {
                    ossObject.close();
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
    }

    /**
     * @Description:
     * @param file
     * @param fileInfo
     * @param type
     * @throws Exception
     * @see com.fd.stdp.service.sys.impl.AbstractFileService#uploadFile(org.springframework.web.multipart.MultipartFile,
     *      com.fd.stdp.beans.sys.SysFileInfo, java.lang.String)
     * @Author: szx
     */
    @Override
    protected void uploadFile(MultipartFile file, SysFileInfo fileInfo, String type) throws Exception {
        // TODO Auto-generated method stub

    }

	@Override
	public boolean copyFile(String source, String taraget) {
		// TODO Auto-generated method stub
		return false;
	}

}
