package com.fd.stdp.service.basic.impl;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.afterturn.easypoi.word.WordExportUtil;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonExpertRecommend;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertRecommendVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicPersonExpertRecommendMapper;
import com.fd.stdp.service.basic.BasicPersonExpertRecommendService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import javax.servlet.http.HttpServletResponse;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 专家推荐表
 *@Author: wangsh
 *@Date: 2022-06-23 15:48:02
 */
public class BasicPersonExpertRecommendServiceImpl extends BaseServiceImpl<BasicPersonExpertRecommendMapper, BasicPersonExpertRecommend> implements BasicPersonExpertRecommendService{

	public static final Logger logger = LoggerFactory.getLogger(BasicPersonExpertRecommendServiceImpl.class);
	
	@Autowired
	private BasicPersonExpertRecommendMapper basicPersonExpertRecommendMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新专家推荐表
	 *@param basicPersonExpertRecommend 专家推荐表对象
	 *@return String 专家推荐表ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateBasicPersonExpertRecommend(BasicPersonExpertRecommendVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			basicPersonExpertRecommendMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			basicPersonExpertRecommendMapper.updateByPrimaryKeySelective(vo);
		}

		// 附件
		basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除专家推荐表
	 *@param id void 专家推荐表ID
	 *@Author: wangsh
	 */
	public void deleteBasicPersonExpertRecommend(String id) {
		//TODO 做判断后方能执行删除
		BasicPersonExpertRecommend basicPersonExpertRecommend=basicPersonExpertRecommendMapper.selectByPrimaryKey(id);
		if(basicPersonExpertRecommend==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		BasicPersonExpertRecommend tembasicPersonExpertRecommend=new BasicPersonExpertRecommend();
		tembasicPersonExpertRecommend.setYn(CommonConstant.FLAG_NO);
		tembasicPersonExpertRecommend.setId(basicPersonExpertRecommend.getId());
		basicPersonExpertRecommendMapper.updateByPrimaryKeySelective(tembasicPersonExpertRecommend);
	}

    /**
     * @Description: 批量删除专家推荐表
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiBasicPersonExpertRecommend(List<String> ids) {
		ids.stream().forEach(id-> this.deleteBasicPersonExpertRecommend(id));
	}

	@Override
	/**
	 *@Description: 查询专家推荐表详情
	 *@param id
	 *@return BasicPersonExpertRecommend
	 *@Author: wangsh
	 */
	public BasicPersonExpertRecommend findById(String id) {
		BasicPersonExpertRecommend recommend = basicPersonExpertRecommendMapper.selectByPrimaryKey(id);
		BasicPersonExpertRecommendVo vo = new BasicPersonExpertRecommendVo();
		BeanUtils.copyProperties(recommend, vo);
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询专家推荐表
	 *@param basicPersonExpertRecommendVo
	 *@return PageInfo<BasicPersonExpertRecommend>
	 *@Author: wangsh
	 */
	public PageInfo<BasicPersonExpertRecommend> findPageByQuery(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo) {
		PageHelper.startPage(basicPersonExpertRecommendVo.getPageNum(),basicPersonExpertRecommendVo.getPageSize());
		Example example=new Example(BasicPersonExpertRecommend.class);
		Criteria criteria=getCriteria(basicPersonExpertRecommendVo, example);
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
		List<BasicPersonExpertRecommend> basicPersonExpertRecommendList=basicPersonExpertRecommendMapper.selectByExample(example);
		return new PageInfo<BasicPersonExpertRecommend>(basicPersonExpertRecommendList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitBasicPersonExpertRecommend(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo) {
		String id = this.saveOrUpdateBasicPersonExpertRecommend(basicPersonExpertRecommendVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_NORMAL_AUDIT, basicPersonExpertRecommendVo, this.mapper,
				StringUtils.isNotBlank(basicPersonExpertRecommendVo.getAuditAdvice())?basicPersonExpertRecommendVo.getAuditAdvice():"提交专家推荐表");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditBasicPersonExpertRecommend(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo) {
		flowCommonService.doFlowStepAudit(basicPersonExpertRecommendVo, this.mapper
				, StringUtils.isNotBlank(basicPersonExpertRecommendVo.getAuditAdvice()) ? basicPersonExpertRecommendVo.getAuditAdvice() : "专家推荐表审核通过"
				, FlowStatusEnum.END.getCode());
		return basicPersonExpertRecommendVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackBasicPersonExpertRecommend(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo) {
		flowCommonService.doFlowStepSendBack(basicPersonExpertRecommendVo, this.mapper
				, StringUtils.isNotBlank(basicPersonExpertRecommendVo.getAuditAdvice())?basicPersonExpertRecommendVo.getAuditAdvice():"专家推荐表退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseBasicPersonExpertRecommend(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo) {
		flowCommonService.doCompleteTask(basicPersonExpertRecommendVo, this.mapper
				, "专家推荐表任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<BasicPersonExpertRecommend> todoList(BasicPersonExpertRecommendVo vo) {

		Example example = new Example(BasicPersonExpertRecommend.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<BasicPersonExpertRecommend> finishedList(BasicPersonExpertRecommendVo vo) {
		Example example = new Example(BasicPersonExpertRecommend.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<BasicPersonExpertRecommend> endList(BasicPersonExpertRecommendVo vo) {
        Example example=new Example(BasicPersonExpertRecommend.class);
        example.orderBy("createTime").desc();
    	Criteria criteria=getCriteria(vo, example);
    	return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
    }

	@Override
	public void exportOne(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo, HttpServletResponse response) {
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Map<String, Object> map = new HashMap<String, Object>();
		OutputStream out = null;
		try {
			BasicPersonExpertRecommend basicPersonExpertRecommend = new BasicPersonExpertRecommend();
			BeanUtils.copyProperties(basicPersonExpertRecommendVo, basicPersonExpertRecommend);
			Field[] fields = BasicPersonExpertRecommend.class.getDeclaredFields();
			for (Field field:fields) {
				field.setAccessible(true);
				field.getName();
				map.put(field.getName(), "");
				if(null != field.get(basicPersonExpertRecommend)){
					if(field.getType() == String.class){
						map.put(field.getName(), field.get(basicPersonExpertRecommend));
					} else if(field.getType() == Date.class){
						Date date = (Date) field.get(basicPersonExpertRecommend);
						map.put(field.getName(), dateFormat.format(date));
					}
				}
			}

			String filename = "专家委员会系统内专家推荐表";
			XWPFDocument doc = WordExportUtil.exportWord07("src/main/resources/word/421"+filename+"模板.docx", map);
			response.setCharacterEncoding("UTF-8");
			response.setHeader("content-Type", "multipart/form-data");
			response.setHeader("Set-Cookie", "fileDownload=true; path=/");
			response.setHeader("Content-Disposition",
					"attachment;filename=" + URLEncoder.encode(filename, "UTF-8") + ".docx");
			out = response.getOutputStream();
			doc.write(out);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
					logger.error("输出流关闭失败!", e);
				}
			}
		}
	}

	@Override
	public void exportPageByQuery(BasicPersonExpertRecommendVo basicPersonExpertRecommendVo, HttpServletResponse response) {

	}

	private Criteria getCriteria(BasicPersonExpertRecommendVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(StringUtils.isNotBlank(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}
		if(StringUtils.isNotBlank(vo.getUnitName())){
			criteria.andLike("unitName", "%" + vo.getUnitName()+ "%");
		}
		if(vo.getIsSystem() != null){
			criteria.andEqualTo("isSystem", vo.getIsSystem());
		}
		return criteria;
	}
}
