package com.fd.stdp.service.project.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectApplyTeams;
import com.fd.stdp.beans.project.vo.ProjectApplyTeamsVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectApplyTeamsMapper;
import com.fd.stdp.service.project.ProjectApplyTeamsService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 项目团队Service业务层处理
 * @date 2021-11-26
 */
@Service
@Transactional(readOnly = true)
public class ProjectApplyTeamsServiceImpl extends BaseServiceImpl<ProjectApplyTeamsMapper, ProjectApplyTeams> implements ProjectApplyTeamsService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectApplyTeamsServiceImpl.class);
    @Autowired
    private ProjectApplyTeamsMapper projectApplyTeamsMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新项目团队
     *@param projectApplyTeams 项目团队对象
     *@return String 项目团队ID
     *@Author: yujianfei
     */
    public String saveOrUpdateProjectApplyTeams(ProjectApplyTeams projectApplyTeams) {
        if (projectApplyTeams == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(projectApplyTeams.getId())) {
            //新增
            projectApplyTeams.setId(UUIDUtils.getUUID());
            projectApplyTeamsMapper.insertSelective(projectApplyTeams);
        } else {
            //避免页面传入修改
            projectApplyTeams.setYn(null);
            projectApplyTeamsMapper.updateByPrimaryKeySelective(projectApplyTeams);
        }
        return projectApplyTeams.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除项目团队
     *@param id void 项目团队ID
     *@Author: yujianfei
     */
    public void deleteProjectApplyTeams(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            ProjectApplyTeams projectApplyTeams = projectApplyTeamsMapper.selectByPrimaryKey(id);
            if (projectApplyTeams == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            ProjectApplyTeams temprojectApplyTeams = new ProjectApplyTeams();
            temprojectApplyTeams.setYn(CommonConstant.FLAG_NO);
            temprojectApplyTeams.setId(projectApplyTeams.getId());
            projectApplyTeamsMapper.updateByPrimaryKeySelective(temprojectApplyTeams);
        }
    }

    /**
     * @param id
     * @return ProjectApplyTeams
     * @Description: 查询项目团队详情
     * @Author: yujianfei
     */
    @Override
    public ProjectApplyTeams findById(String id) {
        return projectApplyTeamsMapper.selectByPrimaryKey(id);
    }


    /**
     * @param projectApplyTeamsVo
     * @return PageInfo<ProjectApplyTeams>
     * @Description: 分页查询项目团队
     * @Author: yujianfei
     */
    @Override
    public PageInfo<ProjectApplyTeams> findPageByQuery(ProjectApplyTeamsVo projectApplyTeamsVo) {
        PageHelper.startPage(projectApplyTeamsVo.getPageNum(), projectApplyTeamsVo.getPageSize());
        Example example = new Example(ProjectApplyTeams.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(projectApplyTeamsVo.getName())){
        //	criteria.andEqualTo(projectApplyTeamsVo.getName());
        //}
        List<ProjectApplyTeams> projectApplyTeamsList = projectApplyTeamsMapper.selectByExample(example);
        return new PageInfo<ProjectApplyTeams>(projectApplyTeamsList);
    }
}
