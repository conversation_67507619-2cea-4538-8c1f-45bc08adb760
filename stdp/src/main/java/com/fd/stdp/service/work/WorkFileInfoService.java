package com.fd.stdp.service.work;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.work.WorkFileInfo;
import com.fd.stdp.beans.work.vo.WorkFileInfoVo;

/**
 * 工作附件信息Service接口
 *
 * <AUTHOR>
 * @date 2021-11-12
 */
public interface WorkFileInfoService {
    /**
     * @param workFileInfo 工作附件信息对象
     * @return String 工作附件信息ID
     * @Description: 保存或更新工作附件信息
     * @Author: yujianfei
     */
    String saveOrUpdateWorkFileInfo(WorkFileInfo workFileInfo);

    /**
     * @param ids void 工作附件信息ID
     * @Description: 删除工作附件信息
     * @Author: yujianfei
     */
    void deleteWorkFileInfo(List<String> ids);

    /**
     * @param id
     * @return WorkFileInfo
     * @Description: 查询工作附件信息详情
     * @Author: yujianfei
     */
    WorkFileInfo findById(String id);

    /**
     * @param workFileInfoVo
     * @return PageInfo<WorkFileInfo>
     * @Description: 分页查询工作附件信息
     * @Author: yujianfei
     */
    PageInfo<WorkFileInfo> findPageByQuery(WorkFileInfoVo workFileInfoVo);
}
