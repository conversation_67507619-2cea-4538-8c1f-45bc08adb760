package com.fd.stdp.service.project;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectApplyTeams;
import com.fd.stdp.beans.project.vo.ProjectApplyTeamsVo;

/**
 * 项目团队Service接口
 *
 * <AUTHOR>
 * @date 2021-11-26
 */
public interface ProjectApplyTeamsService {
    /**
     * @param projectApplyTeams 项目团队对象
     * @return String 项目团队ID
     * @Description: 保存或更新项目团队
     * @Author: yujianfei
     */
    String saveOrUpdateProjectApplyTeams(ProjectApplyTeams projectApplyTeams);

    /**
     * @param ids void 项目团队ID
     * @Description: 删除项目团队
     * @Author: yujianfei
     */
    void deleteProjectApplyTeams(List<String> ids);

    /**
     * @param id
     * @return ProjectApplyTeams
     * @Description: 查询项目团队详情
     * @Author: yujianfei
     */
    ProjectApplyTeams findById(String id);

    /**
     * @param projectApplyTeamsVo
     * @return PageInfo<ProjectApplyTeams>
     * @Description: 分页查询项目团队
     * @Author: yujianfei
     */
    PageInfo<ProjectApplyTeams> findPageByQuery(ProjectApplyTeamsVo projectApplyTeamsVo);
}
