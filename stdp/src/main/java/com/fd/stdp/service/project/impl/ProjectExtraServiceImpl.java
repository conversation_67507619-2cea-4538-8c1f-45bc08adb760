package com.fd.stdp.service.project.impl;

import java.util.List;

import com.fd.stdp.beans.project.ProjectApplyCooperationUnit;
import com.fd.stdp.beans.project.ProjectApplyTeams;
import com.fd.stdp.dao.project.ProjectApplyCooperationUnitMapper;
import com.fd.stdp.dao.project.ProjectApplyCostMapper;
import com.fd.stdp.dao.project.ProjectApplyTeamsMapper;
import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectExtra;
import com.fd.stdp.beans.project.vo.ProjectExtraVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectExtraMapper;
import com.fd.stdp.service.project.ProjectExtraService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 外部项目
 *@Author: wangsh
 *@Date: 2022-03-28 19:32:44
 */
public class ProjectExtraServiceImpl extends BaseServiceImpl<ProjectExtraMapper, ProjectExtra> implements ProjectExtraService{

	public static final Logger logger = LoggerFactory.getLogger(ProjectExtraServiceImpl.class);
	
	@Autowired
	private ProjectExtraMapper projectExtraMapper;
	@Autowired
	private FlowCommonService flowCommonService;

	@Autowired
	private BasicFileAppendixService basicFileAppendixService;

	@Autowired
	private ProjectApplyCooperationUnitMapper projectApplyCooperationUnitMapper;

	@Autowired
	private ProjectApplyTeamsMapper projectApplyTeamsMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新外部项目
	 *@param projectExtra 外部项目对象
	 *@return String 外部项目ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateProjectExtra(ProjectExtraVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			projectExtraMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			projectExtraMapper.updateByPrimaryKeySelective(vo);
		}

		// 合作单位
		if(vo.getUnitList() != null) {
			updateList(vo, vo.getUnitList(), projectApplyCooperationUnitMapper, "setApplyId");
		}

		// 项目团队
		if(vo.getTeamsList() != null) {
			updateList(vo, vo.getTeamsList(), projectApplyTeamsMapper, "setApplyId");
		}

		// 附件
		if(vo.getFiles()!=null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除外部项目
	 *@param id void 外部项目ID
	 *@Author: wangsh
	 */
	public void deleteProjectExtra(String id) {
		//TODO 做判断后方能执行删除
		ProjectExtra projectExtra=projectExtraMapper.selectByPrimaryKey(id);
		if(projectExtra==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		ProjectExtra temprojectExtra=new ProjectExtra();
		temprojectExtra.setYn(CommonConstant.FLAG_NO);
		temprojectExtra.setId(projectExtra.getId());
		projectExtraMapper.updateByPrimaryKeySelective(temprojectExtra);
	}

    /**
     * @Description: 批量删除外部项目
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiProjectExtra(List<String> ids) {
		ids.stream().forEach(id-> this.deleteProjectExtra(id));
	}

	@Override
	/**
	 *@Description: 查询外部项目详情
	 *@param id
	 *@return ProjectExtra
	 *@Author: wangsh
	 */
	public ProjectExtra findById(String id) {
		ProjectExtra extra = projectExtraMapper.selectByPrimaryKey(id);
		ProjectExtraVo vo = new ProjectExtraVo();
		BeanUtils.copyProperties(extra, vo);

		Example example = new Example(ProjectApplyTeams.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
		example.orderBy("createTime").asc();
		vo.setTeamsList(projectApplyTeamsMapper.selectByExample(example));

		example = new Example(ProjectApplyCooperationUnit.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("applyId", id);
		example.orderBy("createTime").asc();
		vo.setUnitList(projectApplyCooperationUnitMapper.selectByExample(example));

		vo.setFiles(basicFileAppendixService.findByFormId(vo.getId()));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询外部项目
	 *@param projectExtraVo
	 *@return PageInfo<ProjectExtra>
	 *@Author: wangsh
	 */
	public PageInfo<ProjectExtra> findPageByQuery(ProjectExtraVo projectExtraVo) {
		PageHelper.startPage(projectExtraVo.getPageNum(),projectExtraVo.getPageSize());
		Example example=new Example(ProjectExtra.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(projectExtraVo.getName())){
		//	criteria.andEqualTo(projectExtraVo.getName());
		//}
		List<ProjectExtra> projectExtraList=projectExtraMapper.selectByExample(example);
		return new PageInfo<ProjectExtra>(projectExtraList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitProjectExtra(ProjectExtraVo projectExtraVo) {
		String id = this.saveOrUpdateProjectExtra(projectExtraVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, projectExtraVo, this.mapper,
				StringUtils.isNotBlank(projectExtraVo.getAuditAdvice())?projectExtraVo.getAuditAdvice():"提交外部项目");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditProjectExtra(ProjectExtraVo projectExtraVo) {
		flowCommonService.doFlowStepAudit(projectExtraVo, this.mapper
				, StringUtils.isNotBlank(projectExtraVo.getAuditAdvice()) ? projectExtraVo.getAuditAdvice() : "外部项目审核通过"
				, FlowStatusEnum.END.getCode());
		return projectExtraVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackProjectExtra(ProjectExtraVo projectExtraVo) {
		flowCommonService.doFlowStepSendBack(projectExtraVo, this.mapper
				, StringUtils.isNotBlank(projectExtraVo.getAuditAdvice())?projectExtraVo.getAuditAdvice():"外部项目退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseProjectExtra(ProjectExtraVo projectExtraVo) {
		flowCommonService.doCompleteTask(projectExtraVo, this.mapper
				, "外部项目任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<ProjectExtra> todoList(ProjectExtraVo vo) {

		Example example = new Example(ProjectExtra.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<ProjectExtra> finishedList(ProjectExtraVo vo) {
		Example example = new Example(ProjectExtra.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<ProjectExtra> endList(ProjectExtraVo vo) {
        Example example=new Example(ProjectExtra.class);
        example.orderBy("createTime").desc();
    	Criteria criteria=getCriteria(vo, example);
    	return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
    }

	private Criteria getCriteria(ProjectExtraVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
		return criteria;
	}
}
