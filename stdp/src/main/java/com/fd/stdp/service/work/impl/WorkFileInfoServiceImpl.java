package com.fd.stdp.service.work.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.work.WorkFileInfo;
import com.fd.stdp.beans.work.vo.WorkFileInfoVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.work.WorkFileInfoMapper;
import com.fd.stdp.service.work.WorkFileInfoService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 工作附件信息Service业务层处理
 * @date 2021-11-12
 */
@Service
@Transactional(readOnly = true)
public class WorkFileInfoServiceImpl extends BaseServiceImpl<WorkFileInfoMapper, WorkFileInfo> implements WorkFileInfoService {

    private static final Logger logger = LoggerFactory.getLogger(WorkFileInfoServiceImpl.class);
    @Autowired
    private WorkFileInfoMapper workFileInfoMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新工作附件信息
     *@param workFileInfo 工作附件信息对象
     *@return String 工作附件信息ID
     *@Author: yujianfei
     */
    public String saveOrUpdateWorkFileInfo(WorkFileInfo workFileInfo) {
        if (workFileInfo == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(workFileInfo.getId())) {
            //新增
            workFileInfo.setId(UUIDUtils.getUUID());
            workFileInfoMapper.insertSelective(workFileInfo);
        } else {
            //避免页面传入修改
            workFileInfo.setYn(null);
            workFileInfoMapper.updateByPrimaryKeySelective(workFileInfo);
        }
        return workFileInfo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除工作附件信息
     *@param id void 工作附件信息ID
     *@Author: yujianfei
     */
    public void deleteWorkFileInfo(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            WorkFileInfo workFileInfo = workFileInfoMapper.selectByPrimaryKey(id);
            if (workFileInfo == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            WorkFileInfo temworkFileInfo = new WorkFileInfo();
            temworkFileInfo.setYn(CommonConstant.FLAG_NO);
            temworkFileInfo.setId(workFileInfo.getId());
            workFileInfoMapper.updateByPrimaryKeySelective(temworkFileInfo);
        }
    }

    /**
     * @param id
     * @return WorkFileInfo
     * @Description: 查询工作附件信息详情
     * @Author: yujianfei
     */
    @Override
    public WorkFileInfo findById(String id) {
        return workFileInfoMapper.selectByPrimaryKey(id);
    }


    /**
     * @param workFileInfoVo
     * @return PageInfo<WorkFileInfo>
     * @Description: 分页查询工作附件信息
     * @Author: yujianfei
     */
    @Override
    public PageInfo<WorkFileInfo> findPageByQuery(WorkFileInfoVo workFileInfoVo) {
        PageHelper.startPage(workFileInfoVo.getPageNum(), workFileInfoVo.getPageSize());
        Example example = new Example(WorkFileInfo.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(workFileInfoVo.getName())){
        //	criteria.andEqualTo(workFileInfoVo.getName());
        //}
        List<WorkFileInfo> workFileInfoList = workFileInfoMapper.selectByExample(example);
        return new PageInfo<WorkFileInfo>(workFileInfoList);
    }
}
