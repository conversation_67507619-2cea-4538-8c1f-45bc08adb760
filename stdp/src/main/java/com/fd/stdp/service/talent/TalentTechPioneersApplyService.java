package com.fd.stdp.service.talent;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTechPioneersApply;
import com.fd.stdp.beans.talent.vo.TalentTechPioneersApplyVo;
/**
 *@Description: 科技尖兵申请书
 *@Author: wangsh
 *@Date: 2022-01-11 10:48:36
 */
public interface TalentTechPioneersApplyService {

	/**
	 *@Description: 保存或更新科技尖兵申请书
	 *@param talentTechPioneersApply 科技尖兵申请书对象
	 *@return String 科技尖兵申请书ID
	 *@Author: wangsh
	 */
	String saveOrUpdateTalentTechPioneersApply(TalentTechPioneersApplyVo vo);
	
	/**
	 *@Description: 删除科技尖兵申请书
	 *@param id void 科技尖兵申请书ID
	 *@Author: wangsh
	 */
	void deleteTalentTechPioneersApply(String id);

	/**
	 * @Description: 批量删除科技尖兵申请书
     * @param ids
     */
    void deleteMultiTalentTechPioneersApply(List<String> ids);

	/**
	 *@Description: 查询科技尖兵申请书详情
	 *@param id
	 *@return TalentTechPioneersApply
	 *@Author: wangsh
	 */
	TalentTechPioneersApply findById(String id);

	/**
	 *@Description: 分页查询科技尖兵申请书
	 *@param talentTechPioneersApplyVo
	 *@return PageInfo<TalentTechPioneersApply>
	 *@Author: wangsh
	 */
	PageInfo<TalentTechPioneersApply> findPageByQuery(TalentTechPioneersApplyVo talentTechPioneersApplyVo);

    String submitTalentTechPioneersApply(TalentTechPioneersApplyVo talentTechPioneersApply);

	String auditTalentTechPioneersApply(TalentTechPioneersApplyVo talentTechPioneersApply);

	String sendBackTalentTechPioneersApply(TalentTechPioneersApplyVo talentTechPioneersApply);

	PageInfo<TalentTechPioneersApply> todoList(TalentTechPioneersApplyVo talentTechPioneersApplyVo);

	PageInfo<TalentTechPioneersApply> finishedList(TalentTechPioneersApplyVo talentTechPioneersApplyVo);

	PageInfo<TalentTechPioneersApply> endList(TalentTechPioneersApplyVo talentTechPioneersApplyVo);
}
