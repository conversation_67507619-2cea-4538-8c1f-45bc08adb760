package com.fd.stdp.service.basic.impl;

import java.io.IOException;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.fd.stdp.beans.basic.BasicPersonReport;
import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.basic.statistics.PersonStatistics;
import com.fd.stdp.beans.basic.vo.BasicPersonReportVo;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.project.ProjectContractApply;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.beans.sys.BasicAreacode;
import com.fd.stdp.beans.sys.SysUser;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.constant.ProcessConstants;
import com.fd.stdp.dao.basic.BasicManageOrgMapper;
import com.fd.stdp.dao.basic.BasicPersonReportMapper;
import com.fd.stdp.dao.basic.BasicScienceOrgMapper;
import com.fd.stdp.dao.sys.BasicAreacodeMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.sys.SysUserService;
import com.fd.stdp.service.user.SysUserUtilService;
import com.fd.stdp.util.EasyExcelUtils;
import com.fd.stdp.util.ExcelUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPerson;
import com.fd.stdp.beans.basic.vo.BasicPersonVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicPersonMapper;
import com.fd.stdp.service.basic.BasicPersonService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.servlet.http.HttpServletResponse;

import static com.fd.stdp.common.BaseController.*;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 机构人员库
 *@Author: wangsh
 *@Date: 2022-01-07 10:53:24
 */
public class BasicPersonServiceImpl extends BaseServiceImpl<BasicPersonMapper, BasicPerson> implements BasicPersonService{

	public static final Logger logger = LoggerFactory.getLogger(BasicPersonServiceImpl.class);

	@Autowired
	private BasicPersonMapper basicPersonMapper;
	@Autowired
	private BasicAreacodeMapper basicAreacodeMapper;
	@Autowired
	private BasicScienceOrgMapper basicScienceOrgMapper;
	@Autowired
	private BasicManageOrgMapper basicManageOrgMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private FlowApiService flowApiService;
	@Autowired
	private BasicPersonReportMapper basicPersonReportMapper;

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新机构人员库
	 *@param basicPerson 机构人员库对象
	 *@return String 机构人员库ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateBasicPerson(BasicPersonVo basicPerson) {
		return saveOrUpdateBasicPerson(basicPerson, true);
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新机构人员库
	 *@param basicPerson 机构人员库对象 isStartFlow 是否尝试启动流程
	 *@return String 机构人员库ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateBasicPerson(BasicPersonVo vo, boolean isStartFlow) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}

		List<BasicPerson> old = findByIdentityId(vo.getIdCardNumber());
		if(!CollectionUtils.isEmpty(old) && (old.size() > 1 || !old.get(0).getId().equals(vo.getId()))){
			throw new ServiceException("系统内存在相同的身份证号,请确认或联系管理员 " + vo.getIdCardNumber());
		}

		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			basicPersonMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			basicPersonMapper.updateByPrimaryKeySelective(vo);
		}
		/*if(isStartFlow) {
			flowCommonService.doFlowStart(FlowableConstant.FLOW_SINGLE_PERSON, basicPerson, this.mapper, "开始机构人员库申请流程");
		}*/
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除机构人员库
	 *@param id void 机构人员库ID
	 *@Author: wangsh
	 */
	public void deleteBasicPerson(String id) {
		//TODO 做判断后方能执行删除
		BasicPerson basicPerson=basicPersonMapper.selectByPrimaryKey(id);
		if(basicPerson==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		BasicPerson tembasicPerson=new BasicPerson();
		tembasicPerson.setYn(CommonConstant.FLAG_NO);
		tembasicPerson.setId(basicPerson.getId());
		basicPersonMapper.updateByPrimaryKeySelective(tembasicPerson);
	}

	@Override
	/**
	 *@Description: 查询机构人员库详情
	 *@param id
	 *@return BasicPerson
	 *@Author: wangsh
	 */
	public BasicPerson findById(String id) {
		BasicPerson basicPerson = basicPersonMapper.selectByPrimaryKey(id);
		BasicPersonVo vo = new BasicPersonVo();
		BeanUtils.copyProperties(basicPerson, vo);
		// 流程
		if(StringUtils.isNotBlank(vo.getFlowStatus())) {
			vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		}
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询机构人员库
	 *@param basicPersonVo
	 *@return PageInfo<BasicPerson>
	 *@Author: wangsh
	 */
	public PageInfo<BasicPerson> findPageByQuery(BasicPersonVo basicPersonVo) {
		SysUser user = getLoginUser();
		String areaCode = user.getAreaCode();
		if(areaCode == null){
			throw new ServiceException("未找到机构行政区划，请前往机构管理补全信息");
		}
		String role = getUserRoleList().get(0).getRoleCode();
		while (areaCode.endsWith("0")){
			areaCode = areaCode.substring(0, areaCode.length()-1);
		}

		PageHelper.startPage(basicPersonVo.getPageNum(),basicPersonVo.getPageSize());
		Example example=new Example(BasicPerson.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		if((role+"@ROLE").contains(AssigneeConstant.ORG_HEAD_ROLE) || (role+"@ROLE").contains(AssigneeConstant.ORG_ADMIN_ROLE)){
			criteria.andEqualTo("orgId", getCurrentScienceOrgId());
		}

		//查询条件
		if(StringUtils.isNotBlank(basicPersonVo.getAreaCode())){
			criteria.andEqualTo("areaCode", basicPersonVo.getAreaCode());
		}
		if(StringUtils.isNotBlank(basicPersonVo.getName())){
			criteria.andLike("name", "%" + basicPersonVo.getName() + "%");
		}
		if(StringUtils.isNotBlank(basicPersonVo.getUnitName())){
			criteria.andLike("unitName", "%" + basicPersonVo.getUnitName() + "%");
		}
		if(StringUtils.isNotBlank(basicPersonVo.getPhone())){
			criteria.andLike("phone", "%" + basicPersonVo.getPhone() + "%");
		}
		if(StringUtils.isNotBlank(basicPersonVo.getEducation())){
			criteria.andEqualTo("education", basicPersonVo.getEducation());
		}
		if(StringUtils.isNotBlank(basicPersonVo.getDegree())){
			criteria.andEqualTo("degree", basicPersonVo.getDegree());
		}
		if(StringUtils.isNotBlank(basicPersonVo.getProField())){
			criteria.andEqualTo("proField", basicPersonVo.getProField());
		}
		if(StringUtils.isNotBlank(basicPersonVo.getTitleLevel())){
			criteria.andEqualTo("titleLevel", basicPersonVo.getTitleLevel());
		}
		if(basicPersonVo.getIsSystem() != null){
			criteria.andEqualTo("isSystem", basicPersonVo.getIsSystem());
		}
		if(StringUtils.equals("0", basicPersonVo.getIsExpert())){
			criteria.andCondition("(id not in (select b.person_id from basic_person_expert b where b.yn = 1 and b.flow_status = 999 and b.PERSON_ID is not null))");
		}
		if(basicPersonVo.getIsInternatinoalPro() != null){
			criteria.andEqualTo("isInternatinoalPro", basicPersonVo.getIsInternatinoalPro());
		}
		if(basicPersonVo.getIsNationPro() != null){
			criteria.andEqualTo("isNationPro", basicPersonVo.getIsNationPro());
		}
		if(basicPersonVo.getIsProvincePro() != null){
			criteria.andEqualTo("isProvincePro", basicPersonVo.getIsProvincePro());
		}
		if(basicPersonVo.getIsInternatinoalReviewPro() != null){
			criteria.andEqualTo("isInternatinoalReviewPro", basicPersonVo.getIsInternatinoalReviewPro());
		}
		if(basicPersonVo.getIsNationReviewPro() != null){
			criteria.andEqualTo("isNationReviewPro", basicPersonVo.getIsNationReviewPro());
		}
		if(basicPersonVo.getIsProvinceReviewPro() != null){
			criteria.andEqualTo("isProvinceReviewPro", basicPersonVo.getIsProvinceReviewPro());
		}
		if(basicPersonVo.getIsProvinceSubjectLeader() != null){
			criteria.andEqualTo("isProvinceSubjectLeader", basicPersonVo.getIsProvinceSubjectLeader());
		}
		if(basicPersonVo.getIsNationTalent() != null){
			criteria.andEqualTo("isNationTalent", basicPersonVo.getIsNationTalent());
		}
		if(basicPersonVo.getIsProvinceTalent() != null){
			criteria.andEqualTo("isProvinceTalent", basicPersonVo.getIsProvinceTalent());
		}
		if(basicPersonVo.getIsCityTalent() != null){
			criteria.andEqualTo("isCityTalent", basicPersonVo.getIsCityTalent());
		}
		if(basicPersonVo.getIsDoctorTutor() != null){
			criteria.andEqualTo("isDoctorTutor", basicPersonVo.getIsDoctorTutor());
		}
		if(basicPersonVo.getIsMasterTutor() != null){
			criteria.andEqualTo("isMasterTutor", basicPersonVo.getIsMasterTutor());
		}
		if(StringUtils.isNotBlank(basicPersonVo.getOnDuty())){
			criteria.andEqualTo("onDuty", basicPersonVo.getOnDuty());
		}

		example.orderBy("createTime").desc();
		List<BasicPerson> basicPersonList=basicPersonMapper.selectByExample(example);
		return new PageInfo<BasicPerson>(basicPersonList);
	}

	@Override
	public PageInfo<BasicPerson> findPageByQueryGlobal(BasicPersonVo basicPersonVo) {
		Example example=new Example(BasicPerson.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		if(!StringUtils.isEmpty(basicPersonVo.getName())){
			criteria.andLike("name", "%" + basicPersonVo.getName() + "%");
		}
		if(!StringUtils.isEmpty(basicPersonVo.getUnitName())){
			criteria.andLike("unitName", "%" + basicPersonVo.getUnitName() + "%");
		}
		if(basicPersonVo.getIsSystem() != null){
			criteria.andEqualTo("isSystem", basicPersonVo.getIsSystem());
		}
		if(StringUtils.equals("0", basicPersonVo.getIsExpert())){
			criteria.andCondition("(id not in (select b.person_id from basic_person_expert b where b.yn = 1 and b.flow_status = 999))");
		}

		example.orderBy("createTime").desc();
		PageHelper.startPage(basicPersonVo.getPageNum(),basicPersonVo.getPageSize());
		List<BasicPerson> basicPersonList=basicPersonMapper.selectByExample(example);
		basicPersonList.forEach(b->{
			if(StringUtils.isNotBlank(b.getIdCardNumber()) && b.getIdCardNumber().length() > 5){
				b.setIdCardNumber(b.getIdCardNumber().substring(0, 5) + "****" + b.getIdCardNumber().substring(b.getIdCardNumber().length()-3));
			}
		});
		return new PageInfo<BasicPerson>(basicPersonList);
	}

	static DateFormat df1 = new SimpleDateFormat("yyyy年MM月");
	static DateFormat df2 = new SimpleDateFormat("yyyy年MM月dd日");
	static DateFormat df3 = new SimpleDateFormat("yyyy-MM");
	static DateFormat df6 = new SimpleDateFormat("yyyy-MM-dd");
	static DateFormat df4 = new SimpleDateFormat("yyyy/MM");
	static DateFormat df5 = new SimpleDateFormat("yyyy/MM/日");
	static DateFormat[] dfs = {df1, df2, df3, df4, df5, df6};
	private Date datePrase(String dateStr){
		for (DateFormat df:dfs) {
			try {
				return df.parse(dateStr);
			} catch (Exception e) {

			}
		}
		try {
			int day = Integer.parseInt(dateStr);
			Calendar calendar = new GregorianCalendar(1900,0,1);
			Date d = calendar.getTime();
			Date date= DateUtils.addDays(d,day);
			return date;
		} catch (Exception e) {

		}
		return new Date();
	}
	private String datePraseStr(String dateStr){
		return dateStr.replaceAll("年", "-").replaceAll("月",  "");
	}

	/**
	 * 附库导入
	 * @param file
	 * @return
	 */
	@Override
	@Transactional(readOnly = false)
	public String upload(MultipartFile file) {
		List<BasicAreacode> areacodes = basicAreacodeMapper.findAll();
		String orgName = getCurrentOrgName();
		String orgCode = getLoginUser().getAreaCode();
		String orgId = getCurrentScienceOrgId();
		List<List<String>> fileValues = ExcelUtils.praseExcelToList(file);
		fileValues.remove(0);
		fileValues.stream().filter(l->l.size() > 0).forEach(
			list -> {
				BasicPersonVo vo = new BasicPersonVo();
				int index = 1;
				vo.setIsSystem(0);
				vo.setName(list.get(index++));
				vo.setSex(list.get(index++));
				vo.setIdCardNumber(list.get(index++));
				vo.setProvince(list.get(index++));
				vo.setArea(list.get(index++));
				areacodes.stream().filter(a->StringUtils.equals(a.getAreaName(), vo.getArea())).forEach(a->{
					vo.setAreaCode(a.getAreaCode());
				});
				if(vo.getAreaCode() == null) {
					areacodes.stream().filter(a->StringUtils.equals(a.getAreaName(), vo.getArea() + "市")).forEach(a->{
						vo.setAreaCode(a.getAreaCode());
					});
				}
				vo.setBirthday(datePrase(list.get(index++)));
				vo.setUnitName(list.get(index++));
				vo.setDeptName(list.get(index++));
				vo.setPost(list.get(index++));
				vo.setPhone(list.get(index++));
				vo.setEducation(list.get(index++));
				vo.setDegree(list.get(index++));
				vo.setProField(list.get(index++));
				vo.setProDirection(list.get(index++));
				vo.setTitleName(list.get(index++));
				vo.setTitleLevel(list.get(index++));
				vo.setTitleCertificateNumber(list.get(index++));
				vo.setIsDoctorTutor(StringUtils.equals("是",list.get(index++))?1:0);
				vo.setIsMasterTutor(StringUtils.equals("是",list.get(index++))?1:0);
				vo.setProCertificateName(list.get(index++));
				vo.setProCertificateNumber(list.get(index++));
				vo.setProTime(datePrase(list.get(index++)));
				vo.setAddress(list.get(index++));
				vo.setMail(list.get(index++));
				vo.setMainResearchExperience(list.get(index++));
				vo.setAwards(list.get(index++));
				vo.setSocialWorks(list.get(index++));
				vo.setBeExpert(list.get(index++));
//				vo.setMark(list.get(index++));
				vo.setOrgName(orgName);
				vo.setOrgCode(orgCode);
				vo.setOrgId(orgId);

//				BasicScienceOrg basicScienceOrg = new BasicScienceOrg();
//				basicScienceOrg.setOrgName(vo.getUnitName());
//				basicScienceOrg.setYn(CommonConstant.FLAG_YES);
//				if(!CollectionUtils.isEmpty(basicScienceOrgMapper.select(basicScienceOrg))){
//					throw new ServiceException(String.format("人员“%s”所属机构《%s》，该单位在系统中存在，请联系所在机构管理员添加主库人员", vo.getName(), vo.getUnitName()));
//				}

				this.saveOrUpdateBasicPerson(vo);
			}
		);

		return "";
	}

	/**
	 * 主库导入
	 * @param file
	 */
	@Override
	@Transactional(readOnly = false)
	public void uploadSystem(MultipartFile file) {
		List<BasicAreacode> areacodes = basicAreacodeMapper.findAll();
		DateFormat df = new SimpleDateFormat("yyyy年MM月dd日");
		String orgName = getCurrentOrgName();
		String orgCode = getLoginUser().getAreaCode();
		String orgId = getCurrentScienceOrgId();
		List<List<String>> fileValues = ExcelUtils.praseExcelToList(file);
		List<BasicPersonVo> datas = new ArrayList<>();
		fileValues.remove(0);
		fileValues.stream().filter(l->l.size() > 0).forEach(
				list -> {
					BasicPersonVo vo = new BasicPersonVo();
					int index = 1;
					vo.setIsSystem(1);
					vo.setProvince(list.get(index++));
					vo.setArea(list.get(index++));
					vo.setCity(list.get(index++));
					vo.setDistrict(list.get(index++));
					areacodes.stream().filter(a->StringUtils.equals(a.getAreaName(), vo.getDistrict())).forEach(a->{
						vo.setAreaCode(a.getAreaCode());
					});
					if(vo.getAreaCode() == null) {
						areacodes.stream().filter(a->StringUtils.equals(a.getAreaName(), vo.getCity() + "市")).forEach(a->{
							vo.setAreaCode(a.getAreaCode());
						});
					}
					vo.setUnitName(list.get(index++));
					vo.setName(list.get(index++));
					vo.setSex(list.get(index++));
					vo.setIdCardNumber(list.get(index++));
					vo.setPhone(list.get(index++));
					vo.setEducation(list.get(index++));
					vo.setEducationCertificateNumber(list.get(index++));
					vo.setDegree(list.get(index++));
					vo.setDegreeCertificateNumber(list.get(index++));
					vo.setProField(list.get(index++));
					vo.setProDirection(list.get(index++));
					vo.setStartFieldTime(datePrase(list.get(index++)));
					vo.setTitleName(list.get(index++));
					vo.setTitleLevel(list.get(index++));
					vo.setTitleCertificateNumber(list.get(index++));
					vo.setDeptName(list.get(index++));
					vo.setPost(list.get(index++));
					vo.setIsInternatinoalPro(StringUtils.equals("是", list.get(index++).trim())?1:0);
					vo.setInInternatinoalName(list.get(index++));
					vo.setInInternatinoalTime(datePraseStr(list.get(index++)));
					vo.setIsNationPro(StringUtils.equals("是", list.get(index++).trim())?1:0);
					vo.setInNationName(list.get(index++));
					vo.setInNationTime(datePraseStr(list.get(index++)));
					vo.setIsProvincePro(StringUtils.equals("是", list.get(index++).trim())?1:0);
					vo.setInProvinceName(list.get(index++));
					vo.setInProvinceTime(datePraseStr(list.get(index++)));
					vo.setIsInternatinoalReviewPro(StringUtils.equals("是", list.get(index++).trim())?1:0);
					vo.setInInternatinoalReviewName(list.get(index++));
					vo.setInInternatinoalReviewTime(datePraseStr(list.get(index++)));
					vo.setIsNationReviewPro(StringUtils.equals("是", list.get(index++).trim())?1:0);
					vo.setInNationReviewName(list.get(index++));
					vo.setInNationReviewTime(datePraseStr(list.get(index++)));
					vo.setIsProvinceReviewPro(StringUtils.equals("是", list.get(index++).trim())?1:0);
					vo.setInProvinceReviewName(list.get(index++));
					vo.setInProvinceReviewTime(datePraseStr(list.get(index++)));
					vo.setIsProvinceSubjectLeader(StringUtils.equals("是", list.get(index++).trim())?1:0);
					vo.setInProvinceSubjectName(list.get(index++));
					vo.setInProvinceSubjectTime(datePraseStr(list.get(index++)));
					vo.setIsNationTalent(StringUtils.equals("是", list.get(index++).trim())?1:0);
					vo.setInNationTalentName(list.get(index++));
					vo.setInNationTalentTime(datePraseStr(list.get(index++)));
					vo.setIsProvinceTalent(StringUtils.equals("是", list.get(index++).trim())?1:0);
					vo.setInProvinceTalentName(list.get(index++));
					vo.setInProvinceSubjectTime(datePraseStr(list.get(index++)));
					vo.setIsCityTalent(StringUtils.equals("是", list.get(index++).trim())?1:0);
					vo.setInCityTalentName(list.get(index++));
					vo.setInCityTalentTime(datePraseStr(list.get(index++)));
					vo.setIsDoctorTutor(StringUtils.equals("是", list.get(index++).trim())?1:0);
					vo.setIsMasterTutor(StringUtils.equals("是", list.get(index++).trim())?1:0);
					vo.setOnDuty(list.get(index++));
					vo.setIsOnDuty(StringUtils.equals("在岗", vo.getOnDuty())?1:0);
					vo.setInUnitTime(datePrase(list.get(index++)));
					vo.setAddress(list.get(index++));
					vo.setMail(list.get(index++));
					vo.setBirthday(datePrase(list.get(index++)));
					vo.setMainResearchExperience(list.get(index++));
					vo.setAwards(list.get(index++));
					vo.setSocialWorks(list.get(index++));
					vo.setBeExpert(list.get(index++));
					vo.setOrgName(orgName);
					vo.setOrgCode(orgCode);
					vo.setOrgId(orgId);
					datas.add(vo);
				}
		);
		datas.stream().forEach(b->{
			saveOrUpdateBasicPerson(b);
		});
		System.out.println("");
	}

	@Override
	public PageInfo<BasicPerson> findPageByQueryMain(BasicPersonVo basicPersonVo) {
		Example example = new Example(BasicPerson.class);
		Criteria criteria = getCriteria(basicPersonVo, example);
		if(StringUtils.isNotBlank(basicPersonVo.getOrgId())){
			criteria.andEqualTo("orgId", basicPersonVo.getOrgId());
		}
		PageHelper.startPage(basicPersonVo.getPageNum(),basicPersonVo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}


	@Override
	@Transactional(readOnly = false)
	public String submitBasicPerson(BasicPersonVo vo) {
		/*if(FlowStatusEnum.ORG_AUDIT.getCode().equals(vo.getFlowStatus())){
			// 用身份证号进行信息更新
			BasicPerson old = findByIdentityId(vo.getIdCardNumber());
			if(old != null && !old.getId().equals(vo.getId())){
				this.deleteBasicPerson(vo.getId());
				vo.setId(old.getId());
			}
			vo.setIsSave(CommonConstant.FLAG_NO);
			return auditBasicPerson(vo);
		} else {
			// 提交到单位审核
			vo.setFlowStatus(FlowStatusEnum.ORG_AUDIT.getCode());
			return this.saveOrUpdateBasicPerson(vo);
		}*/
		this.saveOrUpdateBasicPerson(vo);
		//flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, vo, this.mapper,
		//		StringUtils.isNotBlank(vo.getAuditAdvice())?vo.getAuditAdvice():"提交新增机构人员申请");

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String auditBasicPerson(BasicPersonVo vo) {

		String id = vo.getId();
		if(StringUtils.equals(vo.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())){
			vo.setIsSave(1);
		}
		vo.setFlowStatus(FlowStatusEnum.END.getCode());
		this.mapper.updateByPrimaryKeySelective(vo);

		String taskId = flowApiService.getTaskId(vo.getId());
		if(taskId == null) {
			throw  new ServiceException("未找到流程");
		}
		Map<String, Object> map = new HashMap<String, Object>();
		FlowTaskVo flowTaskVo = flowCommonService.createFlowTaskVo(taskId, vo.getId(), vo.getAuditAdvice(), null, null);
		map.put("ISPASS", 1);
		flowTaskVo.setValues(map);
		flowApiService.completeTask(flowTaskVo);
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackBasicPerson(BasicPersonVo vo) {
		/*vo.getIds().stream().forEach(id-> {
			BasicPersonVo basicPersonVo = new BasicPersonVo();
			basicPersonVo.setId(id);
			basicPersonVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			this.saveOrUpdateBasicPerson(vo);
		});
		return "";*/
		String id = vo.getId();
		flowCommonService.doFlowStepSendBack(vo, this.mapper
				, StringUtils.isNotBlank(vo.getAuditAdvice())?vo.getAuditAdvice():"新增机构人员申请退回"
				, false
		);
		return  id;
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteMultiBasicPerson(List<String> ids) {
		ids.stream().forEach(id-> this.deleteBasicPerson(id));
	}

	@Override
	public Object statistics(BasicPersonVo basicPersonVo) {
		Example example=new Example(BasicPerson.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		Integer totalCount = this.mapper.selectCountByExample(example);

		HashMap<String, PersonStatistics> map = new HashMap<String, PersonStatistics>();

		for(int i= 1;totalCount > 0; i++){
			totalCount-=pageSize;
			PageHelper.startPage(i,pageSize);
			List<BasicPerson> basicPersonList=basicPersonMapper.selectByExample(example);
			doStatistic(map, basicPersonList);
		}
		return map;
	}

	private final int pageSize = 10000;
	private final int yearNow = Calendar.getInstance().get(Calendar.YEAR);
	private final int dayNow = Calendar.getInstance().get(Calendar.DAY_OF_YEAR);

	public void doStatistic(HashMap<String, PersonStatistics> map, List<BasicPerson> personList){
		personList.stream().forEach(
				b -> {
					PersonStatistics personStatistics;
					String key = b.getArea() + "#" + b.getProField();
					if(map.containsKey(key)){
						personStatistics = map.get(key);
					} else {
						personStatistics = new PersonStatistics();
						map.put(key, personStatistics);
					}

					// 年龄
					Calendar calendar = Calendar.getInstance();
					calendar.setTime(b.getBirthday());
					int birthYear = calendar.get(Calendar.YEAR);
					int birthYearDay = calendar.get(Calendar.DAY_OF_YEAR);
					if(birthYearDay > dayNow){
						birthYear += 1;
					}
					int age = yearNow - birthYear;
					if(age < 30){
						personStatistics.addAge30();
					} else if (age <= 45) {
						personStatistics.addAge3045();
					} else {
						personStatistics.addAge45();
					}

					// 学历
					switch (b.getEducation()){
						case "研究生":
							personStatistics.addPostgraduate();
							break;
						case "本科":
							personStatistics.addUndergraduate();
							break;
					}

					// 学位
					switch (b.getDegree()){
						case  "博士":
							personStatistics.addDoctor();
							break;
						case  "硕士":
							personStatistics.addMaster();
							break;
						case  "学士":
							personStatistics.addBachelor();
							break;
					}

					// 职称
					if(b.getTitleLevel().contains("正高")){
						personStatistics.addZg();
					} else if (b.getTitleLevel().contains("高级")) {
						personStatistics.addAdvanced();
					} else if ("中级".equals((b.getTitleLevel()))) {
						personStatistics.addInter();
					}

					// 博士生 硕士生导师
					if(b.getIsDoctorTutor() == 1){
						personStatistics.addDoctorTutor();
					}
					if(b.getIsMasterTutor() == 1){
						personStatistics.addMasterTutor();
					}

					// todo 专家委员会人数

					// 从业年限
					int workYear = calendar.get(Calendar.YEAR);
					int workYearDay = calendar.get(Calendar.DAY_OF_YEAR);
					if(workYearDay > dayNow){
						workYear += 1;
					}
					int worky = yearNow - workYear;
					if(worky > 30){
						personStatistics.addWork30();
					} else if (worky >= 20) {
						personStatistics.addWork2030();
					} else if (worky >= 10) {
						personStatistics.addWork1020();
					}

					if(b.getIsInternatinoalPro() == 1) {
						personStatistics.addWorldStandard();
					}
					if(b.getIsNationPro() == 1) {
						personStatistics.addNationStandard();
					}
					if(b.getIsProvincePro() == 1) {
						personStatistics.addProvinceStandard();
					}

					if(b.getIsInternatinoalReviewPro() == 1) {
						personStatistics.addWorldReview();
					}
					if(b.getIsNationReviewPro() == 1) {
						personStatistics.addNationReview();
					}
					if(b.getIsProvinceReviewPro() == 1) {
						personStatistics.addProvinceReview();
					}

					if(b.getIsProvinceSubjectLeader() == 1) {
						personStatistics.addSubjectLeader();
					}

					if(b.getIsNationTalent() == 1) {
						personStatistics.addNation();
					}
					if(b.getIsProvinceTalent() == 1) {
						personStatistics.addProvince();
					}
					if(b.getIsCityTalent() == 1) {
						personStatistics.addCity();
					}

				}
		);
	}

	@Override
	public List<BasicPerson> findByIdentityId(String id) {
		// 查询指定身份证号的人员已校对信息
		if(StringUtils.isBlank(id)){
			return null;
		}
		Example example = new Example(BasicPerson.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("idCardNumber", id).andEqualTo("isSystem", 1)
				;//.andEqualTo("isSave", CommonConstant.FLAG_NO);
		return this.mapper.selectByExample(example);
	}

	@Override
	public Object export(BasicPersonVo basicPersonVo, HttpServletResponse response) {
		basicPersonVo.setPageSize(Integer.MAX_VALUE);
		basicPersonVo.setPageNum(1);

		List list = findPageByQuery(basicPersonVo).getList();

		String model;
		if(basicPersonVo.getIsSystem() == 1) {
			model = "word/机构人员主库导出模板2022-06-23.xlsx";
		} else {
			model = "word/机构人员副库导出模板2022-06-23.xlsx";
		}
		ClassPathResource classPathResource = new ClassPathResource(model);
		InputStream inputStream = null;
		try {
			inputStream = classPathResource.getInputStream();
		} catch (IOException e) {
			throw new ServiceException("加载文件失败");
		}
		EasyExcelUtils.exportTemplateFill(null, list, "Sheet1", "机构人员信息.xlsx", response, inputStream, true);
		return null;
	}

	@Override
	public PageInfo<BasicPerson> todoList(BasicPersonVo vo) {
		Example example = new Example(ProjectContractApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<BasicPerson> finishedList(BasicPersonVo vo) {
		Example example = new Example(ProjectContractApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
	@Transactional(readOnly = false)
	public void record() {
		String orgName = getCurrentOrgName();
		Calendar calendar = Calendar.getInstance();
		String year = String.valueOf(calendar.get(Calendar.YEAR));
		basicPersonReportMapper.clearByOrgNameAndYear(orgName, year);

		Example example = new Example(BasicPerson.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("orgName", orgName);
		BasicPersonVo basicPersonVo = new BasicPersonVo();
		basicPersonVo.setPageNum(1);
		basicPersonVo.setPageSize(999999);
		PageInfo<BasicPerson> pageInfo = findPageByQuery(basicPersonVo);
		pageInfo.getList().forEach(p -> {
			BasicPersonReport b = new BasicPersonReport();
			BeanUtils.copyProperties(p, b);
			b.setId(UUIDUtils.getUUID());
			b.setYearNo(year);
			b.setOrgName(orgName);
			b.setReportTime(calendar.getTime());
			b.setPersonId(p.getId());
			basicPersonReportMapper.insertSelective(b);
		});
	}

	@Override
	public PageInfo<BasicPersonReport> findRecordPageByQuery(BasicPersonReportVo vo) {
		Example example = new Example(BasicPersonReport.class);
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		if(StringUtils.isBlank(vo.getOrgName())){
			criteria.andEqualTo("orgName", vo.getOrgName());
		}
		if(StringUtils.isBlank(vo.getYearNo())){
			criteria.andEqualTo("yearNo", vo.getYearNo());
		}
		PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
		return new PageInfo<>(basicPersonReportMapper.selectByExample(example));
	}

	@Override
	public BasicPersonReport findByRecordId(String id) {
		Example example = new Example(BasicPersonReport.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("personId", id);
		return basicPersonReportMapper.selectOneByExample(example);
	}

	private Criteria getCriteria(BasicPersonVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}
		return criteria;
	}
}
