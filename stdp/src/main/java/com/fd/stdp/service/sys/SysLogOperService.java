package com.fd.stdp.service.sys;

import java.util.List;

import com.fd.stdp.beans.sys.SysLogOper;
import com.fd.stdp.beans.sys.vo.SysLogOperExportVo;
import com.fd.stdp.beans.sys.vo.SysLogOperVo;
import com.github.pagehelper.PageInfo;

/**
 * @Description: 用户操作日志表
 * @Author: szx
 * @Date: 2020-07-07 17:51:45
 */
public interface SysLogOperService {

    /**
     * @Description: 保存或更新用户操作日志表
     * @param sysLogOper
     *            用户操作日志表对象
     * @return String 用户操作日志表ID
     * @Author: szx
     */
    String saveOrUpdateSysLogOper(SysLogOper sysLogOper);

    /**
     * @Description: 删除用户操作日志表
     * @param id
     *            void 用户操作日志表ID
     * @Author: szx
     */
    void deleteSysLogOper(String id);

    /**
     * @Description: 查询用户操作日志表详情
     * @param id
     * @return SysLogOper
     * @Author: szx
     */
    SysLogOper findById(String id);

    /**
     * @Description: 分页查询用户操作日志表
     * @param sysLogOperVo
     * @return PageInfo<SysLogOper>
     * @Author: szx
     */
    PageInfo<SysLogOper> findPageByQuery(SysLogOperVo sysLogOperVo);

    /**
     * @Description:返回导出日志数据
     * @param sysLogOperVo
     * @return List<SysLogOperExportVo>
     * @Author: szx
     */
    List<SysLogOperExportVo> export(SysLogOperVo sysLogOperVo);
}
