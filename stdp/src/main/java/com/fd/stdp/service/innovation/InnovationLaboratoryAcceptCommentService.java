package com.fd.stdp.service.innovation;

import java.util.List;

import com.fd.stdp.beans.basic.vo.BasicGradeLinkedVo;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationLaboratoryAcceptComment;
import com.fd.stdp.beans.innovation.vo.InnovationLaboratoryAcceptCommentVo;
/**
 *@Description: 重点实验室验收评审/变更评审/中期评估
 *@Author: wangsh
 *@Date: 2022-02-20 13:11:44
 */
public interface InnovationLaboratoryAcceptCommentService {

	/**
	 *@Description: 保存或更新重点实验室验收评审/变更评审/中期评估
	 *@param innovationLaboratoryAcceptComment 重点实验室验收评审/变更评审/中期评估对象
	 *@return String 重点实验室验收评审/变更评审/中期评估ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationLaboratoryAcceptComment(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptComment);
	
	/**
	 *@Description: 删除重点实验室验收评审/变更评审/中期评估
	 *@param id void 重点实验室验收评审/变更评审/中期评估ID
	 *@Author: wangsh
	 */
	void deleteInnovationLaboratoryAcceptComment(String id);

	/**
	 * @Description: 批量删除重点实验室验收评审/变更评审/中期评估
	 * @param ids
	 */
    void deleteMultiInnovationLaboratoryAcceptComment(List<String> ids);

	/**
	 *@Description: 查询重点实验室验收评审/变更评审/中期评估详情
	 *@param id
	 *@return InnovationLaboratoryAcceptComment
	 *@Author: wangsh
	 */
	InnovationLaboratoryAcceptComment findById(String id);

	/**
	 *@Description: 分页查询重点实验室验收评审/变更评审/中期评估
	 *@param innovationLaboratoryAcceptCommentVo
	 *@return PageInfo<InnovationLaboratoryAcceptComment>
	 *@Author: wangsh
	 */
	PageInfo<InnovationLaboratoryAcceptComment> findPageByQuery(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo);
	
	
	/**
	 * 提交
	 * @param innovationLaboratoryAcceptCommentVo
	 * @return
	 */
    String submitInnovationLaboratoryAcceptComment(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo);

	/**
	 * 审核
	 * @param innovationLaboratoryAcceptCommentVo
	 * @return
	 */
	String auditInnovationLaboratoryAcceptComment(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo);

	/**
	 * 退回
	 * @param innovationLaboratoryAcceptCommentVo
	 * @return
	 */
	String sendBackInnovationLaboratoryAcceptComment(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo);

	/**
	 * 任务书下达
	 * @param innovationLaboratoryAcceptCommentVo
	 * @return
	 */
	String releaseInnovationLaboratoryAcceptComment(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnovationLaboratoryAcceptComment> todoList(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationLaboratoryAcceptComment> finishedList(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationLaboratoryAcceptComment> endList(InnovationLaboratoryAcceptCommentVo innovationLaboratoryAcceptCommentVo);

	/**
	 * 专家评审提交
	 * @param vo
	 * @return
	 */
    String expertSubmitInnovationLaboratoryAcceptComment(BasicGradeLinkedVo vo);
}
