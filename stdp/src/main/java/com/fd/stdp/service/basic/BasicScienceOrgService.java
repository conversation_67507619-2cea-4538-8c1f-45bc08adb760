package com.fd.stdp.service.basic;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.basic.vo.BasicScienceOrgVo;

/**
 * 机构基础信息Service接口
 *
 * <AUTHOR>
 * @date 2021-11-09
 */
public interface BasicScienceOrgService {
    /**
     * @param basicScienceOrgVo 机构基础信息对象
     * @return String 机构基础信息ID
     * @Description: 保存或更新机构基础信息
     * @Author: yujianfei
     */
    String saveOrUpdateBasicScienceOrg(BasicScienceOrgVo basicScienceOrgVo);

    /**
     * @param ids void 机构基础信息ID
     * @Description: 删除机构基础信息
     * @Author: yujianfei
     */
    void deleteBasicScienceOrg(List<String> ids);

    /**
     * @param id
     * @return BasicScienceOrg
     * @Description: 查询机构基础信息详情
     * @Author: yujianfei
     */
    BasicScienceOrgVo findById(String id);

    /**
     * @param basicScienceOrgVo
     * @return PageInfo<BasicScienceOrg>
     * @Description: 分页查询机构基础信息
     * @Author: yujianfei
     */
    PageInfo<BasicScienceOrg> findPageByQuery(BasicScienceOrgVo basicScienceOrgVo);

    /**
     * @return List<BasicScienceOrg>
     * @Description: 查询所有机构列表（只返回机构名称和机构代码）
     * @Author: zgx
     */
    List<BasicScienceOrg> findAllOrgList();

    /**
     * @param basicScienceOrgVo 查询条件
     * @return PageInfo<BasicScienceOrg>
     * @Description: 分页查询机构列表（只返回机构名称和机构代码）
     * @Author: zgx
     */
    PageInfo<BasicScienceOrg> findOrgListByPage(BasicScienceOrgVo basicScienceOrgVo);
}
