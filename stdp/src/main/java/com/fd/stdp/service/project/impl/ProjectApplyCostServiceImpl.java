package com.fd.stdp.service.project.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.project.ProjectApplyCost;
import com.fd.stdp.beans.project.vo.ProjectApplyCostVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.project.ProjectApplyCostMapper;
import com.fd.stdp.service.project.ProjectApplyCostService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * <AUTHOR>
 * @Description: 项目科研经费预算Service业务层处理
 * @date 2021-11-26
 */
@Service
@Transactional(readOnly = true)
public class ProjectApplyCostServiceImpl extends BaseServiceImpl<ProjectApplyCostMapper, ProjectApplyCost> implements ProjectApplyCostService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectApplyCostServiceImpl.class);
    @Autowired
    private ProjectApplyCostMapper projectApplyCostMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新项目科研经费预算
     *@param projectApplyCost 项目科研经费预算对象
     *@return String 项目科研经费预算ID
     *@Author: yujianfei
     */
    public String saveOrUpdateProjectApplyCost(ProjectApplyCost projectApplyCost) {
        if (projectApplyCost == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(projectApplyCost.getId())) {
            //新增
            projectApplyCost.setId(UUIDUtils.getUUID());
            projectApplyCostMapper.insertSelective(projectApplyCost);
        } else {
            //避免页面传入修改
            projectApplyCost.setYn(null);
            projectApplyCostMapper.updateByPrimaryKeySelective(projectApplyCost);
        }
        return projectApplyCost.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除项目科研经费预算
     *@param id void 项目科研经费预算ID
     *@Author: yujianfei
     */
    public void deleteProjectApplyCost(List<String> ids) {
        for (String id : ids) {
            //TODO 做判断后方能执行删除
            ProjectApplyCost projectApplyCost = projectApplyCostMapper.selectByPrimaryKey(id);
            if (projectApplyCost == null) {
                throw new ServiceException("非法请求");
            }
            //逻辑删除
            ProjectApplyCost temprojectApplyCost = new ProjectApplyCost();
            temprojectApplyCost.setYn(CommonConstant.FLAG_NO);
            temprojectApplyCost.setId(projectApplyCost.getId());
            projectApplyCostMapper.updateByPrimaryKeySelective(temprojectApplyCost);
        }
    }

    /**
     * @param id
     * @return ProjectApplyCost
     * @Description: 查询项目科研经费预算详情
     * @Author: yujianfei
     */
    @Override
    public ProjectApplyCost findById(String id) {
        return projectApplyCostMapper.selectByPrimaryKey(id);
    }


    /**
     * @param projectApplyCostVo
     * @return PageInfo<ProjectApplyCost>
     * @Description: 分页查询项目科研经费预算
     * @Author: yujianfei
     */
    @Override
    public PageInfo<ProjectApplyCost> findPageByQuery(ProjectApplyCostVo projectApplyCostVo) {
        PageHelper.startPage(projectApplyCostVo.getPageNum(), projectApplyCostVo.getPageSize());
        Example example = new Example(ProjectApplyCost.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
        //if(!StringUtils.isEmpty(projectApplyCostVo.getName())){
        //	criteria.andEqualTo(projectApplyCostVo.getName());
        //}
        List<ProjectApplyCost> projectApplyCostList = projectApplyCostMapper.selectByExample(example);
        return new PageInfo<ProjectApplyCost>(projectApplyCostList);
    }
}
