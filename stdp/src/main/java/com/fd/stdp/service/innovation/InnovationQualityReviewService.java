package com.fd.stdp.service.innovation;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationQualityReview;
import com.fd.stdp.beans.innovation.vo.InnovationQualityReviewVo;
/**
 *@Description: 省质检中心年度总结
 *@Author: wangsh
 *@Date: 2022-03-04 09:23:43
 */
public interface InnovationQualityReviewService {

	/**
	 *@Description: 保存或更新省质检中心年度总结
	 *@param innovationQualityReview 省质检中心年度总结对象
	 *@return String 省质检中心年度总结ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnovationQualityReview(InnovationQualityReviewVo innovationQualityReview);
	
	/**
	 *@Description: 删除省质检中心年度总结
	 *@param id void 省质检中心年度总结ID
	 *@Author: wangsh
	 */
	void deleteInnovationQualityReview(String id);

	/**
	 * @Description: 批量删除省质检中心年度总结
	 * @param ids
	 */
    void deleteMultiInnovationQualityReview(List<String> ids);

	/**
	 *@Description: 查询省质检中心年度总结详情
	 *@param id
	 *@return InnovationQualityReview
	 *@Author: wangsh
	 */
	InnovationQualityReview findById(String id);

	/**
	 *@Description: 分页查询省质检中心年度总结
	 *@param innovationQualityReviewVo
	 *@return PageInfo<InnovationQualityReview>
	 *@Author: wangsh
	 */
	PageInfo<InnovationQualityReview> findPageByQuery(InnovationQualityReviewVo innovationQualityReviewVo);
	
	
	/**
	 * 提交
	 * @param innovationQualityReviewVo
	 * @return
	 */
    String submitInnovationQualityReview(InnovationQualityReviewVo innovationQualityReviewVo);

	/**
	 * 审核
	 * @param innovationQualityReviewVo
	 * @return
	 */
	String auditInnovationQualityReview(InnovationQualityReviewVo innovationQualityReviewVo);

	/**
	 * 退回
	 * @param innovationQualityReviewVo
	 * @return
	 */
	String sendBackInnovationQualityReview(InnovationQualityReviewVo innovationQualityReviewVo);

	/**
	 * 任务书下达
	 * @param innovationQualityReviewVo
	 * @return
	 */
	String releaseInnovationQualityReview(InnovationQualityReviewVo innovationQualityReviewVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnovationQualityReview> todoList(InnovationQualityReviewVo innovationQualityReviewVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnovationQualityReview> finishedList(InnovationQualityReviewVo innovationQualityReviewVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnovationQualityReview> endList(InnovationQualityReviewVo innovationQualityReviewVo);
}
