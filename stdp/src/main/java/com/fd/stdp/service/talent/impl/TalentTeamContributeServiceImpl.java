package com.fd.stdp.service.talent.impl;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.fd.stdp.beans.basic.BasicRecommend;
import com.fd.stdp.beans.basic.vo.BasicPersonLinkedVo;
import com.fd.stdp.beans.sys.SysDictItem;
import com.fd.stdp.beans.talent.TalentTeamContributePlan;
import com.fd.stdp.beans.talent.TalentTeamContributeTarget;
import com.fd.stdp.beans.talent.vo.TalentTeamContributeChangeVo;
import com.fd.stdp.beans.talent.vo.TalentTeamExamineVo;
import com.fd.stdp.common.BaseEntity;
import com.fd.stdp.dao.basic.BasicRecommendMapper;
import com.fd.stdp.dao.talent.TalentTeamContributePlanMapper;
import com.fd.stdp.dao.talent.TalentTeamContributeTargetMapper;
import com.fd.stdp.service.basic.BasicPersonLinkedService;
import com.fd.stdp.service.basic.BasicRecommendService;
import com.fd.stdp.service.sys.SysDictItemService;
import com.fd.stdp.service.talent.TalentTeamContributeChangeService;
import com.fd.stdp.service.talent.TalentTeamExamineService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.talent.TalentTeamContribute;
import com.fd.stdp.beans.talent.vo.TalentTeamContributeVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.talent.TalentTeamContributeMapper;
import com.fd.stdp.service.talent.TalentTeamContributeService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 创新团队建设任务书
 *@Author: wangsh
 *@Date: 2022-02-14 10:22:12
 */
public class TalentTeamContributeServiceImpl extends BaseServiceImpl<TalentTeamContributeMapper, TalentTeamContribute> implements TalentTeamContributeService{

	public static final Logger logger = LoggerFactory.getLogger(TalentTeamContributeServiceImpl.class);
	private static final String NUMBER = "NUMBER";
	private static final String LEADER = "LEADER";
	private static final String EXPERT = "EXPERT";
	
	@Autowired
	private TalentTeamContributeMapper talentTeamContributeMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private TalentTeamContributePlanMapper talentTeamContributePlanMapper;
	@Autowired
	private TalentTeamContributeTargetMapper talentTeamContributeTargetMapper;
	@Autowired
	private BasicPersonLinkedService basicPersonLinkedService;
	@Autowired
	private BasicRecommendMapper basicRecommendMapper;

	@Autowired
	private TalentTeamContributeChangeService talentTeamContributeChangeService;
	@Autowired
	private TalentTeamExamineService talentTeamExamineService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新创新团队建设任务书
	 *@param talentTeamContribute 创新团队建设任务书对象
	 *@return String 创新团队建设任务书ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTalentTeamContribute(TalentTeamContributeVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			talentTeamContributeMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			talentTeamContributeMapper.updateByPrimaryKeySelective(vo);
		}

		if(StringUtils.isBlank(vo.getContributeTimeEnd()) || StringUtils.isBlank(vo.getContributeTime())) {
			Example example = new Example(BasicRecommend.class);
			example.createCriteria().andEqualTo("yn", 1).andEqualTo("recommandType", "TEAM_CONTRACT_RECOMMAND");
			List<BasicRecommend> recommends = basicRecommendMapper.selectByExample(example);
			if (recommends.size() > 0) {
				DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
				vo.setContributeTime(df.format(recommends.get(0).getBuildStartTime()));
				vo.setContributeTimeEnd(df.format(recommends.get(0).getBuildEndTime()));
			}
		}

		// 团队成员
		List<BasicPersonLinkedVo> list = new ArrayList();
		if(!CollectionUtils.isEmpty(vo.getTeamNumbers())){
			vo.getTeamNumbers().stream().forEach(p->p.setPersonType(NUMBER));
			basicPersonLinkedService.clearAndUpdateBasicPersonLinked(vo.getId(), vo.getTeamNumbers(), NUMBER);
		}
		// 合作专家
		if(!CollectionUtils.isEmpty(vo.getTeamExperts())){
			vo.getTeamExperts().stream().forEach(p->p.setPersonType(EXPERT));
			basicPersonLinkedService.clearAndUpdateBasicPersonLinked(vo.getId(), vo.getTeamExperts(), EXPERT);
		}

		if(!CollectionUtils.isEmpty(vo.getContractPlans())){
			updateList(vo, vo.getContractPlans(), talentTeamContributePlanMapper,"setContractId");
		}

		if(!CollectionUtils.isEmpty(vo.getContractTargets())){
			updateList(vo, vo.getContractTargets(), talentTeamContributeTargetMapper,"setContractId");
		}

		// 开启流程
		// flowCommonService.doFlowStart(FlowableConstant.FLOW_TECH_ACHIEVEMENT, vo, this.mapper, "开始创新团队建设任务书流程");

		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除创新团队建设任务书
	 *@param id void 创新团队建设任务书ID
	 *@Author: wangsh
	 */
	public void deleteTalentTeamContribute(String id) {
		//TODO 做判断后方能执行删除
		TalentTeamContribute talentTeamContribute=talentTeamContributeMapper.selectByPrimaryKey(id);
		if(talentTeamContribute==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TalentTeamContribute temtalentTeamContribute=new TalentTeamContribute();
		temtalentTeamContribute.setYn(CommonConstant.FLAG_NO);
		temtalentTeamContribute.setId(talentTeamContribute.getId());
		talentTeamContributeMapper.updateByPrimaryKeySelective(temtalentTeamContribute);
	}

    /**
     * @Description: 批量删除创新团队建设任务书
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiTalentTeamContribute(List<String> ids) {
		ids.stream().forEach(id-> this.deleteTalentTeamContribute(id));
	}

	@Override
	/**
	 *@Description: 查询创新团队建设任务书详情
	 *@param id
	 *@return TalentTeamContribute
	 *@Author: wangsh
	 */
	public TalentTeamContribute findById(String id) {
		TalentTeamContribute talentTeamContribute = talentTeamContributeMapper.selectByPrimaryKey(id);
		TalentTeamContributeVo vo = new TalentTeamContributeVo();
		BeanUtils.copyProperties(talentTeamContribute, vo);

		Example example = new Example(TalentTeamContributePlan.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("contractId", id);
		vo.setContractPlans(talentTeamContributePlanMapper.selectByExample(example));

		example = new Example(TalentTeamContributeTarget.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("contractId", id);
		vo.setContractTargets(talentTeamContributeTargetMapper.selectByExample(example));

		List<BasicPersonLinkedVo> bv = basicPersonLinkedService.findByFormId(id);
		vo.setTeamExperts(bv.stream().filter(b->EXPERT.equals(b.getPersonType())).collect(Collectors.toList()));
		vo.setTeamNumbers(bv.stream().filter(b->NUMBER.equals(b.getPersonType())).collect(Collectors.toList()));

		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询创新团队建设任务书
	 *@param talentTeamContributeVo
	 *@return PageInfo<TalentTeamContribute>
	 *@Author: wangsh
	 */
	public PageInfo<TalentTeamContribute> findPageByQuery(TalentTeamContributeVo talentTeamContributeVo) {
		PageHelper.startPage(talentTeamContributeVo.getPageNum(),talentTeamContributeVo.getPageSize());
		Example example=new Example(TalentTeamContribute.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		criteria.andEqualTo("flowStatus",FlowStatusEnum.END.getCode());
		//查询条件
		//if(!StringUtils.isEmpty(talentTeamContributeVo.getName())){
		//	criteria.andEqualTo(talentTeamContributeVo.getName());
		//}
		List<TalentTeamContribute> talentTeamContributeList=talentTeamContributeMapper.selectByExample(example);
		return new PageInfo<TalentTeamContribute>(talentTeamContributeList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitTalentTeamContribute(TalentTeamContributeVo talentTeamContributeVo) {
		String id = this.saveOrUpdateTalentTeamContribute(talentTeamContributeVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, talentTeamContributeVo, this.mapper,
				StringUtils.isNotBlank(talentTeamContributeVo.getAuditAdvice())?talentTeamContributeVo.getAuditAdvice():"提交创新团队建设任务书");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditTalentTeamContribute(TalentTeamContributeVo talentTeamContributeVo) {
		TalentTeamContributeVo dvo = (TalentTeamContributeVo) findById(talentTeamContributeVo.getId());
		if(StringUtils.equals(dvo.getFlowStatus(), FlowStatusEnum.PROVINCE_AUDIT.getCode())){
			// 在变更与考核登记中添加一条记录
			TalentTeamContributeChangeVo talentTeamContributeChangeVo = new TalentTeamContributeChangeVo();
			BeanUtils.copyProperties(dvo, talentTeamContributeChangeVo);
			talentTeamContributeChangeVo.setId(null);
			talentTeamContributeChangeVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			talentTeamContributeChangeVo.setFlowUser(AssigneeConstant.ORG_HEAD_ROLE);
			talentTeamContributeChangeVo.setContractId(dvo.getId());
			talentTeamContributeChangeService.saveOrUpdateTalentTeamContributeChangeNoFlow(talentTeamContributeChangeVo);

			TalentTeamExamineVo talentTeamExamineVo = new TalentTeamExamineVo();
			BeanUtils.copyProperties(dvo, talentTeamExamineVo);
			talentTeamExamineVo.setId(null);
			talentTeamExamineVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			talentTeamExamineVo.setFlowUser(AssigneeConstant.ORG_HEAD_ROLE);
			talentTeamExamineVo.setContractId(dvo.getId());
			talentTeamExamineService.saveOrUpdateTalentTeamExamineNoFlow(talentTeamExamineVo);
		}

		flowCommonService.doFlowStepAudit(talentTeamContributeVo, this.mapper
				, StringUtils.isNotBlank(talentTeamContributeVo.getAuditAdvice()) ? talentTeamContributeVo.getAuditAdvice() : "创新团队建设任务书审核通过"
				, FlowStatusEnum.END.getCode());
		return talentTeamContributeVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackTalentTeamContribute(TalentTeamContributeVo talentTeamContributeVo) {
		flowCommonService.doFlowStepSendBack(talentTeamContributeVo, this.mapper
				, StringUtils.isNotBlank(talentTeamContributeVo.getAuditAdvice())?talentTeamContributeVo.getAuditAdvice():"创新团队建设任务书退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseTalentTeamContribute(TalentTeamContributeVo talentTeamContributeVo) {
		flowCommonService.doCompleteTask(talentTeamContributeVo, this.mapper
				, "创新团队建设任务书任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<TalentTeamContribute> todoList(TalentTeamContributeVo vo) {

		Example example = new Example(TalentTeamContribute.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<TalentTeamContribute> finishedList(TalentTeamContributeVo vo) {
		Example example = new Example(TalentTeamContribute.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	private Criteria getCriteria(TalentTeamContributeVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("teamLeader", "%" + vo.getName()+ "%");
		}
		return criteria;
	}
}
