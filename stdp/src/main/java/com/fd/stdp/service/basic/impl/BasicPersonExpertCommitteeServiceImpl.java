package com.fd.stdp.service.basic.impl;

import java.util.Date;
import java.util.List;

import com.fd.stdp.beans.basic.BasicRecommend;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.dao.basic.BasicRecommendMapper;
import com.fd.stdp.enums.FlowStatusEnum;
import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.service.user.SysUserUtilService;
import io.swagger.annotations.OAuth2Definition;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicPersonExpertCommittee;
import com.fd.stdp.beans.basic.vo.BasicPersonExpertCommitteeVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicPersonExpertCommitteeMapper;
import com.fd.stdp.service.basic.BasicPersonExpertCommitteeService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 专家委员会库
 *@Author: wangsh
 *@Date: 2022-01-07 13:20:37
 */
public class BasicPersonExpertCommitteeServiceImpl extends BaseServiceImpl<BasicPersonExpertCommitteeMapper, BasicPersonExpertCommittee> implements BasicPersonExpertCommitteeService{

	public static final Logger logger = LoggerFactory.getLogger(BasicPersonExpertCommitteeServiceImpl.class);
	
	@Autowired
	private BasicPersonExpertCommitteeMapper basicPersonExpertCommitteeMapper;
	@Autowired
	private BasicRecommendMapper basicRecommendMapper;
	@Autowired
	SysUserUtilService sysUserUtilService;

	@Autowired
	private FlowCommonService flowCommonService;
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新专家委员会库
	 *@param basicPersonExpertCommittee 专家委员会库对象
	 *@return String 专家委员会库ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateBasicPersonExpertCommittee(BasicPersonExpertCommittee basicPersonExpertCommittee) {
		if(basicPersonExpertCommittee==null){
			throw new ServiceException("数据异常");
		}
		if(!inRecommendTime()){
			throw new ServiceException("不在开放时间段内");
		}
		if(StringUtils.isBlank(basicPersonExpertCommittee.getPersonId())){
			throw new ServiceException("请选择专家");
		}

		// 用人员id进行去重过滤
		Example example = new Example(BasicPersonExpertCommittee.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("personId", basicPersonExpertCommittee.getPersonId())
				.andEqualTo("orgName", basicPersonExpertCommittee.getOrgName());
		BasicPersonExpertCommittee old = this.mapper.selectOneByExample(example);
		if(old != null){
			basicPersonExpertCommittee.setId(old.getId());
			throw  new ServiceException("专家已存在");
		}

		if(StringUtils.isEmpty(basicPersonExpertCommittee.getId())){
			//新增
			basicPersonExpertCommittee.setId(UUIDUtils.getUUID());
			basicPersonExpertCommittee.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			basicPersonExpertCommittee.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			basicPersonExpertCommitteeMapper.insertSelective(basicPersonExpertCommittee);
		}else{
			//避免页面传入修改
			basicPersonExpertCommittee.setYn(null);
			basicPersonExpertCommitteeMapper.updateByPrimaryKeySelective(basicPersonExpertCommittee);
		}

		// flowCommonService.doFlowStart(FlowableConstant.FLOW_TECH_ACHIEVEMENT, basicPersonExpertCommittee, this.mapper, "开始专家委员会申请流程");

		return basicPersonExpertCommittee.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除专家委员会库
	 *@param id void 专家委员会库ID
	 *@Author: wangsh
	 */
	public void deleteBasicPersonExpertCommittee(String id) {
		//TODO 做判断后方能执行删除
		BasicPersonExpertCommittee basicPersonExpertCommittee=basicPersonExpertCommitteeMapper.selectByPrimaryKey(id);
		if(basicPersonExpertCommittee==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		BasicPersonExpertCommittee tembasicPersonExpertCommittee=new BasicPersonExpertCommittee();
		tembasicPersonExpertCommittee.setYn(CommonConstant.FLAG_NO);
		tembasicPersonExpertCommittee.setId(basicPersonExpertCommittee.getId());
		basicPersonExpertCommitteeMapper.updateByPrimaryKeySelective(tembasicPersonExpertCommittee);
	}

	@Override
	/**
	 *@Description: 查询专家委员会库详情
	 *@param id
	 *@return BasicPersonExpertCommittee
	 *@Author: wangsh
	 */
	public BasicPersonExpertCommittee findById(String id) {
		BasicPersonExpertCommittee basicPersonExpertCommittee = basicPersonExpertCommitteeMapper.selectByPrimaryKey(id);
		BasicPersonExpertCommitteeVo vo = new BasicPersonExpertCommitteeVo();
		BeanUtils.copyProperties(basicPersonExpertCommittee, vo);
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询专家委员会库
	 *@param basicPersonExpertCommitteeVo
	 *@return PageInfo<BasicPersonExpertCommittee>
	 *@Author: wangsh
	 */
	public PageInfo<BasicPersonExpertCommittee> findPageByQuery(BasicPersonExpertCommitteeVo basicPersonExpertCommitteeVo) {
		PageHelper.startPage(basicPersonExpertCommitteeVo.getPageNum(),basicPersonExpertCommitteeVo.getPageSize());
		Example example=new Example(BasicPersonExpertCommittee.class);
		Criteria criteria=getCriteria(basicPersonExpertCommitteeVo, example);
		criteria.andEqualTo("flowStatus",FlowStatusEnum.END.getCode());
		//查询条件
		//if(!StringUtils.isEmpty(basicPersonExpertCommitteeVo.getName())){
		//	criteria.andEqualTo(basicPersonExpertCommitteeVo.getName());
		//}
		List<BasicPersonExpertCommittee> basicPersonExpertCommitteeList=basicPersonExpertCommitteeMapper.selectByExample(example);
		return new PageInfo<BasicPersonExpertCommittee>(basicPersonExpertCommitteeList);
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteMultiBasicPersonExpertCommittee(List<String> ids) {
		ids.stream().forEach(id->this.deleteBasicPersonExpertCommittee(id));
	}

	@Override
	@Transactional(readOnly = false)
	public String submitBasicPersonExpertCommittee(BasicPersonExpertCommitteeVo vo) {
		this.saveOrUpdateBasicPersonExpertCommittee(vo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, vo, this.mapper,
				org.apache.commons.lang3.StringUtils.isNotBlank(vo.getAuditAdvice())?vo.getAuditAdvice():"提交专家委员会推荐申请");
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String auditBasicPersonExpertCommittee(BasicPersonExpertCommitteeVo vo) {
		if (StringUtils.equals(FlowStatusEnum.PROVINCE_AUDIT.getCode(), vo.getFlowStatus())) {
			// 审核通过，设置状态值为1
			vo.setIsCommitteeExpert("1");
			this.mapper.updateByPrimaryKeySelective(vo);
		}
		flowCommonService.doFlowStepAudit(vo, this.mapper
				, org.apache.commons.lang3.StringUtils.isNotBlank(vo.getAuditAdvice())?vo.getAuditAdvice():"通过专家委员会推荐申请"
				, FlowStatusEnum.END.getCode());
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackBasicPersonExpertCommittee(BasicPersonExpertCommitteeVo vo) {
		flowCommonService.doFlowStepSendBack(vo, this.mapper
				, org.apache.commons.lang3.StringUtils.isNotBlank(vo.getAuditAdvice())?vo.getAuditAdvice():"专家推荐申请退回"
				, false
		);
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String rejectBasicPersonExpertCommittee(BasicPersonExpertCommitteeVo vo) {
		this.deleteBasicPersonExpertCommittee(vo.getId());
		return vo.getId();
	}

	@Override
	public PageInfo<BasicPersonExpertCommittee> todoList(BasicPersonExpertCommitteeVo vo) {

		Example example = new Example(BasicPersonExpertCommittee.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<BasicPersonExpertCommittee> finishedList(BasicPersonExpertCommitteeVo vo) {
		Example example = new Example(BasicPersonExpertCommittee.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}

	@Override
	public Boolean inRecommendTime() {
		Example example = new Example(BasicRecommend.class);
		example.createCriteria()
				.andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("recommandType", "EXPERT_COMMITTEE_RECOMMAND")
				.andLessThan("startTime", new Date())
				.andGreaterThan("endTime", new Date());
		return basicRecommendMapper.selectOneByExample(example)==null?false:true;
	}

	private Criteria getCriteria(BasicPersonExpertCommittee vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getUnitName())){
			criteria.andLike("unitName", "%" + vo.getUnitName()+ "%");
		}
		return criteria;
	}
}
