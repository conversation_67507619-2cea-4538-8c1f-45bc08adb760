package com.fd.stdp.service.reward.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.reward.RewordPerson;
import com.fd.stdp.beans.reward.vo.RewordPersonVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.reward.RewordPersonMapper;
import com.fd.stdp.service.reward.RewordPersonService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 科技成果关联完成人员
 *@Author: wangsh
 *@Date: 2022-07-05 16:59:18
 */
public class RewordPersonServiceImpl extends BaseServiceImpl<RewordPersonMapper, RewordPerson> implements RewordPersonService{

	public static final Logger logger = LoggerFactory.getLogger(RewordPersonServiceImpl.class);
	
	@Autowired
	private RewordPersonMapper rewordPersonMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新科技成果关联完成人员
	 *@param rewordPerson 科技成果关联完成人员对象
	 *@return String 科技成果关联完成人员ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateRewordPerson(RewordPersonVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			rewordPersonMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			rewordPersonMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除科技成果关联完成人员
	 *@param id void 科技成果关联完成人员ID
	 *@Author: wangsh
	 */
	public void deleteRewordPerson(String id) {
		//TODO 做判断后方能执行删除
		RewordPerson rewordPerson=rewordPersonMapper.selectByPrimaryKey(id);
		if(rewordPerson==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		RewordPerson temrewordPerson=new RewordPerson();
		temrewordPerson.setYn(CommonConstant.FLAG_NO);
		temrewordPerson.setId(rewordPerson.getId());
		rewordPersonMapper.updateByPrimaryKeySelective(temrewordPerson);
	}

    /**
     * @Description: 批量删除科技成果关联完成人员
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiRewordPerson(List<String> ids) {
		ids.stream().forEach(id-> this.deleteRewordPerson(id));
	}

	@Override
	/**
	 *@Description: 查询科技成果关联完成人员详情
	 *@param id
	 *@return RewordPerson
	 *@Author: wangsh
	 */
	public RewordPerson findById(String id) {
		return rewordPersonMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询科技成果关联完成人员
	 *@param rewordPersonVo
	 *@return PageInfo<RewordPerson>
	 *@Author: wangsh
	 */
	public PageInfo<RewordPerson> findPageByQuery(RewordPersonVo rewordPersonVo) {
		PageHelper.startPage(rewordPersonVo.getPageNum(),rewordPersonVo.getPageSize());
		Example example=new Example(RewordPerson.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(rewordPersonVo.getName())){
		//	criteria.andEqualTo(rewordPersonVo.getName());
		//}
		List<RewordPerson> rewordPersonList=rewordPersonMapper.selectByExample(example);
		return new PageInfo<RewordPerson>(rewordPersonList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitRewordPerson(RewordPersonVo rewordPersonVo) {
		String id = this.saveOrUpdateRewordPerson(rewordPersonVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, rewordPersonVo, this.mapper,
				StringUtils.isNotBlank(rewordPersonVo.getAuditAdvice())?rewordPersonVo.getAuditAdvice():"提交科技成果关联完成人员");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditRewordPerson(RewordPersonVo rewordPersonVo) {
		flowCommonService.doFlowStepAudit(rewordPersonVo, this.mapper
				, StringUtils.isNotBlank(rewordPersonVo.getAuditAdvice()) ? rewordPersonVo.getAuditAdvice() : "科技成果关联完成人员审核通过"
				, FlowStatusEnum.END.getCode());
		return rewordPersonVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackRewordPerson(RewordPersonVo rewordPersonVo) {
		flowCommonService.doFlowStepSendBack(rewordPersonVo, this.mapper
				, StringUtils.isNotBlank(rewordPersonVo.getAuditAdvice())?rewordPersonVo.getAuditAdvice():"科技成果关联完成人员退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseRewordPerson(RewordPersonVo rewordPersonVo) {
		flowCommonService.doCompleteTask(rewordPersonVo, this.mapper
				, "科技成果关联完成人员任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<RewordPerson> todoList(RewordPersonVo vo) {

		Example example = new Example(RewordPerson.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<RewordPerson> finishedList(RewordPersonVo vo) {
		Example example = new Example(RewordPerson.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<RewordPerson> endList(RewordPersonVo vo) {
        Example example=new Example(RewordPerson.class);
        example.orderBy("createTime").desc();
    	Criteria criteria=getCriteria(vo, example);
    	return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
    }

	private Criteria getCriteria(RewordPersonVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
		return criteria;
	}
}
