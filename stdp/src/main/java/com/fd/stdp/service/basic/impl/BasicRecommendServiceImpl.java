package com.fd.stdp.service.basic.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicRecommend;
import com.fd.stdp.beans.basic.vo.BasicRecommendVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.basic.BasicRecommendMapper;
import com.fd.stdp.service.basic.BasicRecommendService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 窗口表
 *@Author: wangsh
 *@Date: 2022-02-15 15:20:33
 */
public class BasicRecommendServiceImpl extends BaseServiceImpl<BasicRecommendMapper, BasicRecommend> implements BasicRecommendService{

	public static final Logger logger = LoggerFactory.getLogger(BasicRecommendServiceImpl.class);
	
	@Autowired
	private BasicRecommendMapper basicRecommendMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新窗口表
	 *@param basicRecommend 窗口表对象
	 *@return String 窗口表ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateBasicRecommend(BasicRecommendVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			// 校验
			Example example = new Example(BasicRecommend.class);
			example.createCriteria().andEqualTo("yn", 1).andEqualTo("recommandType", vo.getRecommandType());
			if(!CollectionUtils.isEmpty(this.mapper.selectByExample(example))){
				throw new ServiceException("已存在相同的推荐类型");
			}

			//新增
			vo.setId(UUIDUtils.getUUID());
			basicRecommendMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			basicRecommendMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除窗口表
	 *@param id void 窗口表ID
	 *@Author: wangsh
	 */
	public void deleteBasicRecommend(String id) {
		//TODO 做判断后方能执行删除
		BasicRecommend basicRecommend=basicRecommendMapper.selectByPrimaryKey(id);
		if(basicRecommend==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		BasicRecommend tembasicRecommend=new BasicRecommend();
		tembasicRecommend.setYn(CommonConstant.FLAG_NO);
		tembasicRecommend.setId(basicRecommend.getId());
		basicRecommendMapper.updateByPrimaryKeySelective(tembasicRecommend);
	}

    /**
     * @Description: 批量删除窗口表
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiBasicRecommend(List<String> ids) {
		ids.stream().forEach(id-> this.deleteBasicRecommend(id));
	}

	@Override
	/**
	 *@Description: 查询窗口表详情
	 *@param id
	 *@return BasicRecommend
	 *@Author: wangsh
	 */
	public BasicRecommend findById(String id) {
		return basicRecommendMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询窗口表
	 *@param basicRecommendVo
	 *@return PageInfo<BasicRecommend>
	 *@Author: wangsh
	 */
	public PageInfo<BasicRecommend> findPageByQuery(BasicRecommendVo basicRecommendVo) {
		PageHelper.startPage(basicRecommendVo.getPageNum(),basicRecommendVo.getPageSize());
		Example example=new Example(BasicRecommend.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(basicRecommendVo.getName())){
		//	criteria.andEqualTo(basicRecommendVo.getName());
		//}
		List<BasicRecommend> basicRecommendList=basicRecommendMapper.selectByExample(example);
		return new PageInfo<BasicRecommend>(basicRecommendList);
	}


	@Override
	public BasicRecommend findByRecommandType(String type) {
		Example example = new Example(BasicRecommend.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("recommandType", type);
		BasicRecommend basicRecommend = this.mapper.selectOneByExample(example);
		BasicRecommendVo vo = new BasicRecommendVo();
		BeanUtils.copyProperties(basicRecommend, vo);
		return vo;
	}

	private Criteria getCriteria(BasicRecommend vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
		return criteria;
	}
}
