package com.fd.stdp.service.user.impl;

import java.util.List;
import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.flowable.FlowTaskVo;
import com.fd.stdp.beans.user.UserLeave;
import com.fd.stdp.beans.user.vo.UserLeaveVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.user.UserLeaveMapper;
import com.fd.stdp.service.flowable.FlowApiService;
import com.fd.stdp.service.user.UserLeaveService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * @Description: 员工请假Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-10-25
 */
@Service
@Transactional(readOnly = true)
public class UserLeaveServiceImpl extends BaseServiceImpl<UserLeaveMapper, UserLeave> implements UserLeaveService {

	private static final Logger logger = LoggerFactory.getLogger(UserLeaveServiceImpl.class);
	@Autowired
	private UserLeaveMapper userLeaveMapper;

	@Autowired
	private FlowApiService flowApiService;

	@Override
	@Transactional(readOnly = false)
	/**
	 * @Description: 保存或更新员工请假
	 * @param userLeave 员工请假对象
	 * @return String 员工请假ID
	 * @Author: linqiang
	 */
	public String saveOrUpdateUserLeave(UserLeave userLeave) {
		if (userLeave == null) {
			throw new ServiceException("数据异常");
		}
		if (StringUtils.isEmpty(userLeave.getId())) {
			// 新增
			userLeave.setId(UUIDUtils.getUUID());
			userLeaveMapper.insertSelective(userLeave);
		} else {
			// 避免页面传入修改
			userLeave.setYn(null);
			userLeaveMapper.updateByPrimaryKeySelective(userLeave);
		}
		FlowTaskVo flowTaskVo = new FlowTaskVo();
		flowTaskVo.setBusinessKey(userLeave.getId());
		flowTaskVo.setUserId(userLeave.getUserId());
		flowTaskVo.setUserName(userLeave.getUserName());
//		flowTaskVo.setAssignee("ADMIN@ROLE");
		flowTaskVo.setProcessInstanceKey("FLOWABLE_LEAVE");
		flowApiService.startProcessInstanceByKey(flowTaskVo);
		return userLeave.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 * @Description: 删除员工请假
	 * @param id void 员工请假ID
	 * @Author: linqiang
	 */
	public void deleteUserLeave(List<String> ids) {
		for (String id : ids) {
			// TODO 做判断后方能执行删除
			UserLeave userLeave = userLeaveMapper.selectByPrimaryKey(id);
			if (userLeave == null) {
				throw new ServiceException("非法请求");
			}
			// 逻辑删除
			UserLeave temuserLeave = new UserLeave();
			temuserLeave.setYn(CommonConstant.FLAG_NO);
			temuserLeave.setId(userLeave.getId());
			userLeaveMapper.updateByPrimaryKeySelective(temuserLeave);
		}
	}

	/**
	 * @Description: 查询员工请假详情
	 * @param id
	 * @return UserLeave
	 * @Author: linqiang
	 */
	@Override
	public UserLeave findById(String id) {
		return userLeaveMapper.selectByPrimaryKey(id);
	}

	/**
	 * @Description: 分页查询员工请假
	 * @param userLeaveVo
	 * @return PageInfo<UserLeave>
	 * @Author: linqiang
	 */
	@Override
	public PageInfo<UserLeave> findPageByQuery(UserLeaveVo userLeaveVo) {
		PageHelper.startPage(userLeaveVo.getPageNum(), userLeaveVo.getPageSize());
		Example example = new Example(UserLeave.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("yn", CommonConstant.FLAG_YES);
		// 查询条件
		// if(!StringUtils.isEmpty(userLeaveVo.getName())){
		// criteria.andEqualTo(userLeaveVo.getName());
		// }
		List<UserLeave> userLeaveList = userLeaveMapper.selectByExample(example);
		return new PageInfo<UserLeave>(userLeaveList);
	}
}
