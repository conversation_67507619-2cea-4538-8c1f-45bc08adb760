package com.fd.stdp.service.audit;

import java.util.List;

import com.fd.stdp.beans.audit.InnerAuditInformationCollection;
import com.fd.stdp.beans.audit.vo.InnerAuditEquippedInformationVo;
import com.fd.stdp.beans.audit.vo.InnerAuditInformationCollectionVo;
import com.github.pagehelper.PageInfo;

/**
 *@Description: 浙江省内部审计人员信息采集表
 *@Author: sef
 *@Date: 2022-06-06 13:55:59
 */
public interface InnerAuditInformationCollectionsService {

	/**
	 *@Description: 保存或更新浙江省内部审计人员信息采集表
	 *@param innerAuditInformationCollection 浙江省内部审计人员信息采集表对象
	 *@return String 浙江省内部审计人员信息采集表ID
	 *@Author: sef
	 */
	String saveOrUpdateInnerAuditInformationCollection(InnerAuditInformationCollectionVo innerAuditInformationCollection);
	
	/**
	 *@Description: 删除浙江省内部审计人员信息采集表
	 *@param id void 浙江省内部审计人员信息采集表ID
	 *@Author: sef
	 */
	void deleteInnerAuditInformationCollection(String id);

	/**
	 * @Description: 批量删除浙江省内部审计人员信息采集表
	 * @param ids
	 */
    void deleteMultiInnerAuditInformationCollection(List<String> ids);

	/**
	 *@Description: 查询浙江省内部审计人员信息采集表详情
	 *@param id
	 *@return InnerAuditInformationCollection
	 *@Author: sef
	 */
	InnerAuditInformationCollection findById(String id);

	/**
	 *@Description: 分页查询浙江省内部审计人员信息采集表
	 *@param innerAuditInformationCollectionVo
	 *@return PageInfo<InnerAuditInformationCollection>
	 *@Author: sef
	 */
	PageInfo<InnerAuditInformationCollection> findPageByQuery(InnerAuditInformationCollectionVo innerAuditInformationCollectionVo);
	
	
	/**
	 * 提交
	 * @param innerAuditInformationCollectionVo
	 * @return
	 */
    String submitInnerAuditInformationCollection(InnerAuditInformationCollectionVo innerAuditInformationCollectionVo);

	/**
	 * 审核
	 * @param innerAuditInformationCollectionVo
	 * @return
	 */
	String auditInnerAuditInformationCollection(InnerAuditInformationCollectionVo innerAuditInformationCollectionVo);

	/**
	 * 退回
	 * @param innerAuditInformationCollectionVo
	 * @return
	 */
	String sendBackInnerAuditInformationCollection(InnerAuditInformationCollectionVo innerAuditInformationCollectionVo);

	/**
	 * 任务书下达
	 * @param innerAuditInformationCollectionVo
	 * @return
	 */
	String releaseInnerAuditInformationCollection(InnerAuditInformationCollectionVo innerAuditInformationCollectionVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnerAuditInformationCollection> todoList(InnerAuditInformationCollectionVo innerAuditInformationCollectionVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnerAuditInformationCollection> finishedList(InnerAuditInformationCollectionVo innerAuditInformationCollectionVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnerAuditInformationCollection> endList(InnerAuditInformationCollectionVo innerAuditInformationCollectionVo);

    void saveBatchInnerAuditWorkContacts(List<InnerAuditInformationCollectionVo> contactList);

	void removeInnerAuditInformationCollection(String id);

	PageInfo findList(InnerAuditInformationCollectionVo vo);

	List exportInnerAuditInformationCollectionVo(InnerAuditInformationCollectionVo vo);

	List exportInnerAuditInformationCollectionVoAllUnit(InnerAuditInformationCollectionVo vo);
}
