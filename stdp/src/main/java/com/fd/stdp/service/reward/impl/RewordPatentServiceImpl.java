package com.fd.stdp.service.reward.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.reward.RewordPatent;
import com.fd.stdp.beans.reward.vo.RewordPatentVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.reward.RewordPatentMapper;
import com.fd.stdp.service.reward.RewordPatentService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 科技成果关联专利
 *@Author: wangsh
 *@Date: 2022-07-05 16:58:49
 */
public class RewordPatentServiceImpl extends BaseServiceImpl<RewordPatentMapper, RewordPatent> implements RewordPatentService{

	public static final Logger logger = LoggerFactory.getLogger(RewordPatentServiceImpl.class);
	
	@Autowired
	private RewordPatentMapper rewordPatentMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新科技成果关联专利
	 *@param rewordPatent 科技成果关联专利对象
	 *@return String 科技成果关联专利ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateRewordPatent(RewordPatentVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			rewordPatentMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			rewordPatentMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除科技成果关联专利
	 *@param id void 科技成果关联专利ID
	 *@Author: wangsh
	 */
	public void deleteRewordPatent(String id) {
		//TODO 做判断后方能执行删除
		RewordPatent rewordPatent=rewordPatentMapper.selectByPrimaryKey(id);
		if(rewordPatent==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		RewordPatent temrewordPatent=new RewordPatent();
		temrewordPatent.setYn(CommonConstant.FLAG_NO);
		temrewordPatent.setId(rewordPatent.getId());
		rewordPatentMapper.updateByPrimaryKeySelective(temrewordPatent);
	}

    /**
     * @Description: 批量删除科技成果关联专利
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiRewordPatent(List<String> ids) {
		ids.stream().forEach(id-> this.deleteRewordPatent(id));
	}

	@Override
	/**
	 *@Description: 查询科技成果关联专利详情
	 *@param id
	 *@return RewordPatent
	 *@Author: wangsh
	 */
	public RewordPatent findById(String id) {
		return rewordPatentMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询科技成果关联专利
	 *@param rewordPatentVo
	 *@return PageInfo<RewordPatent>
	 *@Author: wangsh
	 */
	public PageInfo<RewordPatent> findPageByQuery(RewordPatentVo rewordPatentVo) {
		PageHelper.startPage(rewordPatentVo.getPageNum(),rewordPatentVo.getPageSize());
		Example example=new Example(RewordPatent.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(rewordPatentVo.getName())){
		//	criteria.andEqualTo(rewordPatentVo.getName());
		//}
		List<RewordPatent> rewordPatentList=rewordPatentMapper.selectByExample(example);
		return new PageInfo<RewordPatent>(rewordPatentList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitRewordPatent(RewordPatentVo rewordPatentVo) {
		String id = this.saveOrUpdateRewordPatent(rewordPatentVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, rewordPatentVo, this.mapper,
				StringUtils.isNotBlank(rewordPatentVo.getAuditAdvice())?rewordPatentVo.getAuditAdvice():"提交科技成果关联专利");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditRewordPatent(RewordPatentVo rewordPatentVo) {
		flowCommonService.doFlowStepAudit(rewordPatentVo, this.mapper
				, StringUtils.isNotBlank(rewordPatentVo.getAuditAdvice()) ? rewordPatentVo.getAuditAdvice() : "科技成果关联专利审核通过"
				, FlowStatusEnum.END.getCode());
		return rewordPatentVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackRewordPatent(RewordPatentVo rewordPatentVo) {
		flowCommonService.doFlowStepSendBack(rewordPatentVo, this.mapper
				, StringUtils.isNotBlank(rewordPatentVo.getAuditAdvice())?rewordPatentVo.getAuditAdvice():"科技成果关联专利退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseRewordPatent(RewordPatentVo rewordPatentVo) {
		flowCommonService.doCompleteTask(rewordPatentVo, this.mapper
				, "科技成果关联专利任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<RewordPatent> todoList(RewordPatentVo vo) {

		Example example = new Example(RewordPatent.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<RewordPatent> finishedList(RewordPatentVo vo) {
		Example example = new Example(RewordPatent.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<RewordPatent> endList(RewordPatentVo vo) {
        Example example=new Example(RewordPatent.class);
        example.orderBy("createTime").desc();
    	Criteria criteria=getCriteria(vo, example);
    	return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
    }

	private Criteria getCriteria(RewordPatentVo vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
		return criteria;
	}
}
