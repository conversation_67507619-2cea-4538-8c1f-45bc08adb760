package com.fd.stdp.service.audit.impl;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import com.fd.stdp.beans.audit.InnerAuditOpen;
import com.fd.stdp.beans.audit.InnerAuditOrg;
import com.fd.stdp.beans.audit.vo.AuditStatistics;
import com.fd.stdp.beans.basic.BasicManageOrg;
import com.fd.stdp.beans.basic.BasicRecommend;
import com.fd.stdp.beans.basic.BasicScienceOrg;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.dao.audit.InnerAuditOpenMapper;
import com.fd.stdp.dao.audit.InnerAuditOrgMapper;
import com.fd.stdp.dao.basic.BasicManageOrgMapper;
import com.fd.stdp.dao.basic.BasicScienceOrgMapper;
import com.fd.stdp.service.user.SysUserUtilService;
import com.fd.stdp.util.EasyExcelUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.audit.InnerAuditFirst;
import com.fd.stdp.beans.audit.vo.InnerAuditFirstVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.audit.InnerAuditFirstMapper;
import com.fd.stdp.service.audit.InnerAuditFirstService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import javax.servlet.http.HttpServletResponse;

import static com.fd.stdp.common.BaseController.getCurrentOrgName;
import static com.fd.stdp.common.BaseController.getLoginUser;
import static com.fd.stdp.service.audit.impl.InnerAuditUtil.*;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 内审统计表1
 *@Author: wangsh
 *@Date: 2022-02-22 16:49:05
 */
public class InnerAuditFirstServiceImpl extends BaseServiceImpl<InnerAuditFirstMapper, InnerAuditFirst> implements InnerAuditFirstService{

	public static final Logger logger = LoggerFactory.getLogger(InnerAuditFirstServiceImpl.class);
	
	@Autowired
	private InnerAuditFirstMapper innerAuditFirstMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private InnerAuditOpenMapper innerAuditOpenMapper;
	@Autowired
	private InnerAuditOrgMapper innerAuditOrgMapper;

	@Autowired
	private SysUserUtilService sysUserUtilService;
	@Autowired
	private BasicScienceOrgMapper basicScienceOrgMapper;
	@Autowired
	private BasicManageOrgMapper basicManageOrgMapper;



	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新内审统计表1
	 *@param innerAuditFirst 内审统计表1对象
	 *@return String 内审统计表1ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnerAuditFirst(InnerAuditFirstVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}

		String targetUnit = null;
		OrgTypeEnum orgTypeEnum = getFillType(getCurrentOrgName());
		switch (orgTypeEnum){
			case PROVINCE:
				if(StringUtils.isEmpty(vo.getId())) {
					// 省局新增
					vo.setContent("内审统01表,内审统02表,内审工作总结");
					vo.setFlowStatus("2");
				}
				if(StringUtils.isBlank(vo.getUnitTypeFill())) {
					switch (getFillType(vo.getUnitName())) {
						case PROVINCE_GO_EM:
							// 企业
							vo.setUnitTypeFill(QIYE);
							break;
						case PROVINCE_GO:
							// 事业单位
							vo.setUnitTypeFill(SHIYEDANWEI);
							break;
						default:
							// 机关
							vo.setUnitTypeFill(JIGUAN);
							break;
					}
				}
				if(StringUtils.isBlank(vo.getTargetUnit())) {
					targetUnit = getTargetUnitType(vo.getUnitName());
				}
				break;
			default:
				if(StringUtils.isEmpty(vo.getId())) {
					InnerAuditOpen innerAuditOpen = isAuditOpen(vo.getOrgName());
					if (innerAuditOpen == null) {
						throw new ServiceException("获取开放窗口信息失败");
					}
					targetUnit = getTargetUnitType(getCurrentOrgName());
					vo.setYears(innerAuditOpen.getYears());
					vo.setTypes(innerAuditOpen.getTypes());
					vo.setStartTime(innerAuditOpen.getStartTime());
					vo.setEndTime(innerAuditOpen.getEndTime());
					vo.setContent(innerAuditOpen.getContent());
					vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
				}
				if(StringUtils.isBlank(vo.getUnitTypeFill())) {
					switch (getFillType(getCurrentOrgName())) {
						case PROVINCE_GO_EM:
							// 企业
							vo.setUnitTypeFill(QIYE);
							break;
						case PROVINCE_GO:
							// 事业单位
							vo.setUnitTypeFill(SHIYEDANWEI);
							break;
						default:
							// 机关
							vo.setUnitTypeFill(JIGUAN);
							break;
					}
				}
		}
		if(targetUnit != null &&
				(StringUtils.isBlank(vo.getTargetUnit()) || (orgTypeEnum == OrgTypeEnum.PROVINCE && StringUtils.equals("2", vo.getFlowStatus())))
		){
			vo.setTargetUnit(targetUnit);
		}
		try {
			// 补充单位地址信息
			Example example = new Example(BasicScienceOrg.class);
			example.createCriteria().andEqualTo("orgName", vo.getUnitName());
			List<BasicScienceOrg> list = basicScienceOrgMapper.selectByExample(example);
			if (list.size() > 0) {
				vo.setAddress(list.get(0).getAddress());
				vo.setOrgShortName(list.get(0).getOrgShortName());
			} else {
				example = new Example(BasicManageOrg.class);
				example.createCriteria().andEqualTo("orgName", vo.getUnitName());
				List<BasicManageOrg> list2 = basicManageOrgMapper.selectByExample(example);
				if (list2.size() > 0) {
					vo.setAddress(list2.get(0).getAddress());
				}
			}
			/*Integer operType = getOperType(getCurrentOrgName());
			if (operType == 2) {
				if(StringUtils.isEmpty(vo.getId())) {
					vo.setContent("内审统01表,内审统02表,内审工作总结");
					vo.setFlowStatus("2");
					vo.setTargetUnit(SHENGJU);
					vo.setTargetUnit(getTargetUnitType(vo.getUnitName()));
					if (StringUtils.equals(SHENGJUZHISHUDANWEI, vo.getTargetUnit())) {
						if (StringUtils.equals(FANGYUAN, vo.getUnitName())) {
							vo.setUnitTypeFill(QIYE);
						} else {
							vo.setUnitTypeFill(SHIYEDANWEI);
						}
						vo.setFillType(FillTypeEnum.PROVINCE_GOVEMENT.type);
					} else {
						vo.setUnitTypeFill(JIGUAN);
						if (StringUtils.equals(vo.getUnitName(), getCurrentOrgName())) {
							vo.setFillType(FillTypeEnum.PROVINCE.type);
						} else {
							vo.setFillType(FillTypeEnum.PROVINCE_GOVEMENT.type);
						}
					}
				}
			} else {
				InnerAuditOpen innerAuditOpen = isAuditOpen(vo.getOrgName());
				if (innerAuditOpen == null) {
					throw new ServiceException("获取开放窗口信息失败");
				}
				vo.setYears(innerAuditOpen.getYears());
				vo.setTypes(innerAuditOpen.getTypes());
				vo.setTargetUnit(getTargetUnitType(getCurrentOrgName()));
				vo.setStartTime(innerAuditOpen.getStartTime());
				vo.setEndTime(innerAuditOpen.getEndTime());
				vo.setContent(innerAuditOpen.getContent());
				vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());

				if (StringUtils.equals(SHENGJUZHISHUDANWEI, vo.getTargetUnit())) {
					if (StringUtils.equals(FANGYUAN, getCurrentOrgName())) {
						vo.setUnitTypeFill(QIYE);
					} else {
						vo.setUnitTypeFill(SHIYEDANWEI);
					}
					vo.setFillType(FillTypeEnum.PROVINCE_GOVEMENT.type);
				} else {
					vo.setUnitTypeFill(JIGUAN);
					vo.setFillType(FillTypeEnum.CITY.type);;
				}

				// 补充单位地址信息
				Example example = new Example(BasicScienceOrg.class);
				example.createCriteria().andEqualTo("orgName", vo.getUnitName());
				List<BasicScienceOrg> list = basicScienceOrgMapper.selectByExample(example);
				if (list.size() > 0) {
					vo.setAddress(list.get(0).getAddress());
					vo.setOrgShortName(list.get(0).getOrgShortName());
				} else {
					example = new Example(BasicManageOrg.class);
					example.createCriteria().andEqualTo("orgName", vo.getUnitName());
					List<BasicManageOrg> list2 = basicManageOrgMapper.selectByExample(example);
					if (list2.size() > 0) {
						vo.setAddress(list2.get(0).getAddress());
					}
				}
			}*/
		}catch (Exception e){
			e.printStackTrace();
			logger.error("补充单位信息失败");
		}

		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setId(UUIDUtils.getUUID());
			innerAuditFirstMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innerAuditFirstMapper.updateByPrimaryKeySelective(vo);
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除内审统计表1
	 *@param id void 内审统计表1ID
	 *@Author: wangsh
	 */
	public void deleteInnerAuditFirst(String id) {
		//TODO 做判断后方能执行删除
		InnerAuditFirst innerAuditFirst=innerAuditFirstMapper.selectByPrimaryKey(id);
		if(innerAuditFirst==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnerAuditFirst teminnerAuditFirst=new InnerAuditFirst();
		teminnerAuditFirst.setYn(CommonConstant.FLAG_NO);
		teminnerAuditFirst.setId(innerAuditFirst.getId());
		innerAuditFirstMapper.updateByPrimaryKeySelective(teminnerAuditFirst);
	}

    /**
     * @Description: 批量删除内审统计表1
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnerAuditFirst(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnerAuditFirst(id));
	}

	@Override
	/**
	 *@Description: 查询内审统计表1详情
	 *@param id
	 *@return InnerAuditFirst
	 *@Author: wangsh
	 */
	public InnerAuditFirst findById(String id) {
		if(StringUtils.isBlank(id)){
			InnerAuditFirst auditFirst = findOldVal();
			return auditFirst==null?new InnerAuditFirst():auditFirst;
		}
		return innerAuditFirstMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询内审统计表1
	 *@param innerAuditFirstVo
	 *@return PageInfo<InnerAuditFirst>
	 *@Author: wangsh
	 */
	public PageInfo<InnerAuditFirst> findPageByQuery(InnerAuditFirstVo innerAuditFirstVo) {
		PageHelper.startPage(innerAuditFirstVo.getPageNum(),innerAuditFirstVo.getPageSize());
		Example example=new Example(InnerAuditFirst.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innerAuditFirstVo.getName())){
		//	criteria.andEqualTo(innerAuditFirstVo.getName());
		//}
		List<InnerAuditFirst> innerAuditFirstList=innerAuditFirstMapper.selectByExample(example);
		return new PageInfo<InnerAuditFirst>(innerAuditFirstList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnerAuditFirst(InnerAuditFirstVo innerAuditFirstVo) {
		String id = this.saveOrUpdateInnerAuditFirst(innerAuditFirstVo);
		Integer operType = getOperType(getCurrentOrgName());
		if(operType == 2) {
			innerAuditFirstVo.setFlowStatus(FlowStatusEnum.END.getCode());
		} else {
			innerAuditFirstVo.setFlowStatus(FlowStatusEnum.PROVINCE_AUDIT.getCode());
		}
		innerAuditFirstVo.setYn(null);
		innerAuditFirstMapper.updateByPrimaryKeySelective(innerAuditFirstVo);
		// flowCommonService.doFlowStart(FlowableConstant.FLOW_INNER_AUDIT, innerAuditFirstVo, this.mapper, "填报内审统计表");
		// flowCommonService.doCompleteTask(innerAuditFirstVo, this.mapper, "提交内审统计表", FlowStatusEnum.PROVINCE_AUDIT.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnerAuditFirst(InnerAuditFirstVo innerAuditFirstVo) {
		flowCommonService.doFlowStepAudit(innerAuditFirstVo, this.mapper
				, StringUtils.isNotBlank(innerAuditFirstVo.getAuditAdvice()) ? innerAuditFirstVo.getAuditAdvice() : "内审统计表1审核通过"
				, FlowStatusEnum.END.getCode());
		return innerAuditFirstVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnerAuditFirst(InnerAuditFirstVo innerAuditFirstVo) {
		innerAuditFirstVo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
		innerAuditFirstVo.setYn(null);
		innerAuditFirstMapper.updateByPrimaryKeySelective(innerAuditFirstVo);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnerAuditFirst(InnerAuditFirstVo innerAuditFirstVo) {
		flowCommonService.doCompleteTask(innerAuditFirstVo, this.mapper
				, "内审统计表1任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnerAuditFirst> todoList(InnerAuditFirstVo vo) {

		Example example = new Example(InnerAuditFirst.class);
		Criteria criteria = example.createCriteria();
		Integer operType = getOperType(getCurrentOrgName());
		if(operType == 2) {
			criteria.andEqualTo("yn", CommonConstant.FLAG_YES)
					.andCondition("(flow_status = 5 or flow_status = 2)");
		} else {
			criteria.andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("orgName", vo.getOrgName())
					.andCondition("(flow_status = 1)");
		}
		if(StringUtils.isNotBlank(vo.getUnitUscc())){
			criteria.andLike("unitUscc", "%" + vo.getUnitUscc() + "%");
		}
		if(StringUtils.isNotBlank(vo.getUnitName())){
			criteria.andLike("unitName", "%" + vo.getUnitName() + "%");
		}
		if(StringUtils.isNotBlank(vo.getContent())){
			criteria.andLike("content", "%" + vo.getContent() + "%");
		}
		if(StringUtils.isNotBlank(vo.getYears())){
			criteria.andIn("years", Arrays.asList(vo.getYears().split(",")));
		}
		if(StringUtils.isNotBlank(vo.getTypes())){
			criteria.andIn("types", Arrays.asList(vo.getTypes().split(",")));
		}
		example.orderBy("createTime").desc();
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}

	@Override
	public PageInfo<InnerAuditFirst> finishedList(InnerAuditFirstVo vo) {
		Example example = new Example(InnerAuditFirst.class);
		Criteria criteria = example.createCriteria();
		Integer operType = getOperType(getCurrentOrgName());
		if(operType == 2) {
			criteria.andEqualTo("yn", CommonConstant.FLAG_YES).andCondition("(flow_status = 999)");
		} else {
			criteria.andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("orgName", vo.getOrgName())
					.andCondition("(flow_status > 1)");
		}
		if(StringUtils.isNotBlank(vo.getUnitUscc())){
			criteria.andLike("unitUscc", "%" + vo.getUnitUscc() + "%");
		}

		if(StringUtils.isNotBlank(vo.getUnitName())){
			criteria.andLike("unitName", "%" + vo.getUnitName() + "%");
		}
		if(StringUtils.isNotBlank(vo.getContent())){
			criteria.andLike("content", "%" + vo.getContent() + "%");
		}
		if(StringUtils.isNotBlank(vo.getYears())){
			criteria.andIn("years", Arrays.asList(vo.getYears().split(",")));
		}
		if(StringUtils.isNotBlank(vo.getTypes())){
			criteria.andIn("types", Arrays.asList(vo.getTypes().split(",")));
		}
		example.orderBy("createTime").desc();
		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(this.mapper.selectByExample(example));
	}
	
	@Override
    public PageInfo<InnerAuditFirst> endList(InnerAuditFirstVo vo) {
        Example example = new Example(InnerAuditFirst.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	@Override
	public InnerAuditOpen isAuditOpen(String s) {

		boolean isAuditOrg = false;
		Example example = new Example(InnerAuditOrg.class);
		example.createCriteria()
				.andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("orgName", s);
		if(!CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))) {
			isAuditOrg = true;
		}

		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		Date zero = calendar.getTime();

		example = new Example(InnerAuditOpen.class);
		Criteria criteria = example.createCriteria()
				.andEqualTo("yn", CommonConstant.FLAG_YES)
				.andEqualTo("status", "1")
				.andGreaterThanOrEqualTo("endTime", zero)
				.andLessThanOrEqualTo("startTime", zero);
		if(isAuditOrg){
			criteria.andLike("targetUnit", "%"+SHENGJUZHISHUDANWEI+"%");
		} else {
			Integer operType = getOperType(s);
			if(operType == 2){
				//criteria.andLike("targetUnit", "%省局%");
				return null;
			}else if(operType == 1){
				criteria.andLike("targetUnit", "%"+SHIJU+"%");
			} else if (operType == 0){
				criteria.andLike("targetUnit", "%"+QUXIANJU+"%");
			}
		}
		List<InnerAuditOpen> auditOpenList = innerAuditOpenMapper.selectByExample(example);
		if(auditOpenList.size() > 0){
			for (int i = 1; i < auditOpenList.size(); i++) {
				InnerAuditOpen auditOpen = auditOpenList.get(i);
				auditOpenList.get(0).setContent(auditOpenList.get(0).getContent() + "," + auditOpen.getContent());
			}
			return auditOpenList.get(0);
		}
		return null;
	}

	private String getTargetUnitType(String unitname) {

		boolean isAuditOrg = false;
		Example example = new Example(InnerAuditOrg.class);
		example.createCriteria()
				.andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("orgName", unitname);
		if(!CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))) {
			isAuditOrg = true;
		}

		if(isAuditOrg){
			return SHENGJUZHISHUDANWEI;
		} else {
			BasicManageOrg basicManageOrg = new BasicManageOrg();
			basicManageOrg.setOrgName(unitname);
			basicManageOrg.setYn(CommonConstant.FLAG_YES);
			List<BasicManageOrg> manageOrgs = basicManageOrgMapper.select(basicManageOrg);
			if(!CollectionUtils.isEmpty(manageOrgs)){
				BasicManageOrg b = manageOrgs.get(0);
				int orgType = sysUserUtilService.getOperTypeByAreaCode(b.getAreaCode());
				if(2 == orgType){
					return SHENGJU;
				} else if(1== orgType){
					return SHIJU;
				}
				return QUXIANJU;
			}

			Integer operType = getOperType(unitname);
			if(operType == 2){
				return SHENGJU;
			}else if(operType == 1){
				return SHIJU;
			} else if (operType == 0){
				return QUXIANJU;
			}
		}
		return null;
	}

	@Override
	public Object statistics(InnerAuditFirstVo innerAuditFirstVo) {
		return statisticsFormat(this.mapper.statistics(innerAuditFirstVo));
	}

	private List<AuditStatistics> statisticsFormat(List<InnerAuditFirstVo> statistics) {
		List<AuditStatistics> list = new ArrayList<>();

		try {
			formatInvoke(list, statistics, "unitNumber", "030000");
			formatInvoke(list, statistics, "hasTotalAudit", "030001");
			formatInvoke(list, statistics, "hasInnerAudit", "030100");
			formatInvoke(list, statistics, "hasIndependInnerAudit", "030101");
			formatInvoke(list, statistics, "actualNumber", "030200");
			formatInvoke(list, statistics, "fullTimeNumber", "030210");
			formatInvoke(list, statistics, "educationNumber", "030220");
			formatInvoke(list, statistics, "masterNumber", "030221");
			formatInvoke(list, statistics, "undergraduateNumber", "030222");
			formatInvoke(list, statistics, "specialtyNumber", "030223");
			formatInvoke(list, statistics, "titleNumber", "030230");
			formatInvoke(list, statistics, "primaryTitleNumber", "030231");
			formatInvoke(list, statistics, "midTitleNumber", "030232");
			formatInvoke(list, statistics, "highTitleNumber", "030233");
			formatInvoke(list, statistics, "noTitleNumber", "030234");
			formatInvoke(list, statistics, "ageNumber", "030240");
			formatInvoke(list, statistics, "down30TitleNumber", "030241");
			formatInvoke(list, statistics, "between3050TitleNumber", "030242");
			formatInvoke(list, statistics, "up50Number", "030243");
			formatInvoke(list, statistics, "knowledgeNumber", "030250");
			formatInvoke(list, statistics, "auditNumber", "030251");
			formatInvoke(list, statistics, "accountingNumber", "030252");
			formatInvoke(list, statistics, "economicsNumber", "030253");
			formatInvoke(list, statistics, "lawNumber", "030254");
			formatInvoke(list, statistics, "managerNumber", "030255");
			formatInvoke(list, statistics, "informationNumber", "030256");
			formatInvoke(list, statistics, "emgineeringNumber", "030257");
			formatInvoke(list, statistics, "otherNumber", "030258");
			formatInvoke(list, statistics, "ciaNumber", "030260");

			formatInvoke(list, statistics, "auditItem", "030300");
			formatInvoke(list, statistics, "majorNationalPolicies", "030301");
			formatInvoke(list, statistics, "financialRevenue", "030302");
			formatInvoke(list, statistics, "fixedAssets", "030303");
			formatInvoke(list, statistics, "internalControl", "030304");
			formatInvoke(list, statistics, "economicResponsibility", "030305");
			formatInvoke(list, statistics, "informationSystem", "030306");
			formatInvoke(list, statistics, "overseas", "030307");
			formatInvoke(list, statistics, "others1", "030308");
			formatInvoke(list, statistics, "entrustedOutsourcing", "030310");

			formatInvoke(list, statistics, "internalWorkload", "030400");
			formatInvoke(list, statistics, "amountProblems", "030500");
			formatInvoke(list, statistics, "amountPerformanceProblems", "030510");
			formatInvoke(list, statistics, "amountComplianceIssue", "030520");
			formatInvoke(list, statistics, "accounting", "030521");
			formatInvoke(list, statistics, "illegalFunds", "030522");
			formatInvoke(list, statistics, "interceptionFunds", "030523");
			formatInvoke(list, statistics, "lossWaste", "030524");
			formatInvoke(list, statistics, "misappropriationFunds", "030525");
			formatInvoke(list, statistics, "taxEvasion", "030526");
			formatInvoke(list, statistics, "illegalIncome", "030527");
			formatInvoke(list, statistics, "others3", "030528");

			formatInvoke(list, statistics, "auditProblemNumber", "030600");
			formatInvoke(list, statistics, "amountQuestionsNumber", "030610");
			formatInvoke(list, statistics, "noneAmountQuestionsNumber", "030620");
			formatInvoke(list, statistics, "nationalPolicies", "030621");
			formatInvoke(list, statistics, "developmentPlanning", "030622");
			formatInvoke(list, statistics, "internalControlRisk", "030623");
			formatInvoke(list, statistics, "others4", "030624");
			formatInvoke(list, statistics, "rectificationProblemFound", "030700");
			formatInvoke(list, statistics, "adjustAccounting", "030701");
			formatInvoke(list, statistics, "recoveryFunds", "030702");
			formatInvoke(list, statistics, "retrieveLoss", "030703");
			formatInvoke(list, statistics, "returnCapital", "030704");
			formatInvoke(list, statistics, "supplementaryTax", "030705");
			formatInvoke(list, statistics, "others5", "030706");

			formatInvoke(list, statistics, "rectificationProblemNoneFound", "030800");
			formatInvoke(list, statistics, "newlyFormulated", "030801");
			formatInvoke(list, statistics, "reviseImprove", "030802");
			formatInvoke(list, statistics, "optimizeBusiness", "030803");
			formatInvoke(list, statistics, "others6", "030804");
			formatInvoke(list, statistics, "recommendationsDiscipline", "030900");
			formatInvoke(list, statistics, "partyDiscipline", "030901");
			formatInvoke(list, statistics, "administrativeSanction", "030902");
			formatInvoke(list, statistics, "internalDisciplinary", "030903");
			formatInvoke(list, statistics, "judicialAuthorities", "031000");
			formatInvoke(list, statistics, "personInvolved", "031001");
		} catch (Exception e){
			e.printStackTrace();
			throw new ServiceException("无效字段");
		}

		return list;
	}

	private void formatInvoke(List list, List<InnerAuditFirstVo> statistics, String param, String code) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
		Method method = InnerAuditFirstVo.class.getMethod("get" + param.substring(0, 1).toUpperCase() + param.substring(1));
		BigDecimal qy = new BigDecimal(0),jg = new BigDecimal(0),sy = new BigDecimal(0),el = new BigDecimal(0);
		for (int i = 0; i < statistics.size(); i++) {
			InnerAuditFirstVo vo = statistics.get(i);
			if(vo.getUnitType() == null){
				continue;
			}
			Object o = method.invoke(vo);
			BigDecimal v;
			if (o == null){
				v = new BigDecimal(0);
			} else {
				if (o instanceof String) {
					v = new BigDecimal((String) o);
				} else if (o instanceof BigDecimal) {
					v = (BigDecimal) o;
				} else {
					v =  new BigDecimal((Integer) o);
				}
			}
			if (v!= null) {
				switch (vo.getUnitType()) {
					case QIYE:
						qy = v;
						break;
					case JIGUAN:
						jg = v;
						break;
					case SHIYEDANWEI:
						sy = v;
						break;
					default:
						el = v;
						break;
				}
			}
		}
		AuditStatistics auditStatistics = new AuditStatistics(qy, jg, sy, new BigDecimal(0), el);
		auditStatistics.setCode(code);
		list.add(auditStatistics);
	}

	@Override
	public void export(InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response) {
		InputStream inputStream = getInputStream("word/内审03导出.xlsx");
//		IFileLoader fileLoader = new FileLoaderImpl();
//		byte[] result = fileLoader.getFile("src/main/resources/word/内审03导出.xlsx");
//		result = Arrays.copyOf(result, result.length);
		// List list = this.mapper.statistics(innerAuditFirstVo);
		innerAuditFirstVo.setApplyTime(new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
		EasyExcelUtils.exportTemplateFill(innerAuditFirstVo, (List<Object>) this.statistics(innerAuditFirstVo), "Sheet1", "统计综合表.xlsx", response, inputStream, true);
	}

	@Override
	public List statistics11(InnerAuditFirstVo innerAuditFirstVo) {
		List<InnerAuditFirstVo> list = this.mapper.statistics11(innerAuditFirstVo);
		for (int i = 0; i < list.size(); i++) {
			if(list.get(i) == null){
				list.remove(i);
				i--;
				continue;
			}
			list.get(i).setListIndex(i+1);
		}
		return list;
	}

	@Override
	public List statistics12(InnerAuditFirstVo innerAuditFirstVo) {
//		Example example = new Example(InnerAuditFirst.class);
//		Criteria criteria = example.createCriteria();
//		criteria.andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
//		if(StringUtils.isNotBlank(innerAuditFirstVo.getOrgType())){
//			criteria.andLike("targetUnit", innerAuditFirstVo.getOrgType());
//		}
		innerAuditFirstVo.setContent("%内审统01表%");
		List<InnerAuditFirstVo> list = this.mapper.statistics12(innerAuditFirstVo); //.selectByExample(example);
		for (int i = 0; i < list.size(); i++) {
			if(list.get(i) == null){
				list.remove(i);
				i--;
				continue;
			}
			list.get(i).setListIndex(i+1);
		}
		return list;
	}

	@Override
	public List statistics21(InnerAuditFirstVo innerAuditFirstVo) {
		List<InnerAuditFirstVo> list = this.mapper.statistics21(innerAuditFirstVo);
		for (int i = 0; i < list.size(); i++) {
			if(list.get(i) == null){
				list.remove(i);
				i--;
				continue;
				// list.add(i, new InnerAuditFirstVo());
			}
			list.get(i).setListIndex(i+1);
		}
		return list;
	}

	@Override
	public List statistics22(InnerAuditFirstVo innerAuditFirstVo) {
//		Example example = new Example(InnerAuditFirst.class);
//		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
//		List<InnerAuditFirst> list = this.mapper.selectByExample(example);
//		for (int i = 0; i < list.size(); i++) {
//			if(list.get(i) == null){
//				list.remove(i);
//				i--;
//				continue;
//			}
//			list.get(i).setListIndex(i+1);
//		}
		innerAuditFirstVo.setContent("%内审统02表%");
		List<InnerAuditFirstVo> list = this.mapper.statistics12(innerAuditFirstVo); //.selectByExample(example);
		for (int i = 0; i < list.size(); i++) {
			if(list.get(i) == null){
				list.remove(i);
				i--;
				continue;
			}
			list.get(i).setListIndex(i+1);
		}
		return list;
	}

	@Override
	public void export11(InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response) {
		InputStream inputStream = getInputStream("word/表1-1导出.xlsx");
//		IFileLoader fileLoader = new FileLoaderImpl();
//		byte[] result = fileLoader.getFile("src/main/resources/word/表1-1导出.xlsx");
//		result = Arrays.copyOf(result, result.length);
		List list = statistics11(innerAuditFirstVo);
		innerAuditFirstVo.setApplyTime(new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
		EasyExcelUtils.exportTemplateFill(null, list, "Sheet1", "表1-1.xlsx", response, inputStream, false);
	}

	@Override
	public void export12(InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response) {
		InputStream inputStream = getInputStream("word/表1-2导出.xlsx");
		List list = statistics12(innerAuditFirstVo);
		innerAuditFirstVo.setApplyTime(new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
		EasyExcelUtils.exportTemplateFill(null, list, "Sheet1", "表1-2.xlsx", response, inputStream, true);
	}

	private InputStream getInputStream(String s) {
		ClassPathResource classPathResource = new ClassPathResource(s);
		InputStream inputStream = null;
		try {
			inputStream = classPathResource.getInputStream();
		} catch (IOException e) {
			throw new ServiceException("加载文件失败");
		}
		return inputStream;
	}

	@Override
	public void export21(InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response) {
		InputStream inputStream = getInputStream("word/表2-1导出.xlsx");
		List list = statistics21(innerAuditFirstVo);
		innerAuditFirstVo.setApplyTime(new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
		EasyExcelUtils.exportTemplateFill(null, list, "Sheet1", "表2-1.xlsx", response, inputStream, false);
	}

	@Override
	public void export22(InnerAuditFirstVo innerAuditFirstVo, HttpServletResponse response) {
		InputStream inputStream = getInputStream("word/表2-2导出.xlsx");
		List list = statistics22(innerAuditFirstVo);
		innerAuditFirstVo.setApplyTime(new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
		EasyExcelUtils.exportTemplateFill(null, list, "Sheet1", "表2-2.xlsx", response, inputStream, true);
	}

	@Override
	public InnerAuditFirst findOldVal() {
		Example example = new Example(InnerAuditFirst.class);
		example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("unitName", getCurrentOrgName());
		example.orderBy("createTime").desc();
		try {
			InnerAuditFirst innerAuditFirst = this.mapper.selectOneByExample(example);
			if(innerAuditFirst != null){
				innerAuditFirst.setFlowStatus(null);
				innerAuditFirst.setContent(null);
				innerAuditFirst.setYears(null);
				innerAuditFirst.setId(null);
			}
			return innerAuditFirst;
		} catch (Exception e){

		}
		return new InnerAuditFirst();
	}

	private Criteria getCriteria(InnerAuditFirst vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES).andEqualTo("orgName", vo.getOrgName())
				.andCondition("(FLOW_STATUS is null or FLOW_STATUS = 1)");
		//查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
		return criteria;
	}

	private Integer getOperType(String orgName){
		LoginUser loginUser = getLoginUser();
		Integer operType = sysUserUtilService.getUserOperType(loginUser);
		Example example = new Example(InnerAuditOrg.class);
		example.createCriteria().andEqualTo("orgName", orgName);
		if(CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))){
			return operType;
		}
		return 0;
	}

	private boolean getIsProOrg(String org){
		Example example = new Example(InnerAuditOrg.class);
		example.createCriteria().andLike("orgName", "%" + org + "%");
		if(CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))){
			return false;
		}
		return true;
	}

	// 0 省局 1 直属单位 2市局 3 县局
	private OrgTypeEnum getFillType(String org){
		if(getIsProOrg(org)){
			if(FANGYUAN.equals(org)){
				return OrgTypeEnum.PROVINCE_GO_EM;
			}
			return OrgTypeEnum.PROVINCE_GO;
		}
		int orgType = getOperType(org);
		if(2 == orgType){
			return OrgTypeEnum.PROVINCE;
		} else if(1== orgType){
			return OrgTypeEnum.CITY;
		}
		return OrgTypeEnum.COUNTRY;
	}

}
