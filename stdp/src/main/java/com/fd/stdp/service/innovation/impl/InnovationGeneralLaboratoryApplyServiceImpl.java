package com.fd.stdp.service.innovation.impl;

import java.util.List;

import com.fd.stdp.service.basic.BasicFileAppendixService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.innovation.InnovationGeneralLaboratoryApply;
import com.fd.stdp.beans.innovation.vo.InnovationGeneralLaboratoryApplyVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.innovation.InnovationGeneralLaboratoryApplyMapper;
import com.fd.stdp.service.innovation.InnovationGeneralLaboratoryApplyService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.constant.FlowableConstant;
import com.fd.stdp.enums.FlowStatusEnum;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 总局重点实验室
 *@Author: wangsh
 *@Date: 2022-03-07 19:45:57
 */
public class InnovationGeneralLaboratoryApplyServiceImpl extends BaseServiceImpl<InnovationGeneralLaboratoryApplyMapper, InnovationGeneralLaboratoryApply> implements InnovationGeneralLaboratoryApplyService{

	public static final Logger logger = LoggerFactory.getLogger(InnovationGeneralLaboratoryApplyServiceImpl.class);
	
	@Autowired
	private InnovationGeneralLaboratoryApplyMapper innovationGeneralLaboratoryApplyMapper;
	@Autowired
	private FlowCommonService flowCommonService;
	@Autowired
	private BasicFileAppendixService basicFileAppendixService;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新总局重点实验室
	 *@param innovationGeneralLaboratoryApply 总局重点实验室对象
	 *@return String 总局重点实验室ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateInnovationGeneralLaboratoryApply(InnovationGeneralLaboratoryApplyVo vo) {
		if(vo==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(vo.getId())){
			//新增
			vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
			vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
			vo.setId(UUIDUtils.getUUID());
			innovationGeneralLaboratoryApplyMapper.insertSelective(vo);
		}else{
			//避免页面传入修改
			vo.setYn(null);
			innovationGeneralLaboratoryApplyMapper.updateByPrimaryKeySelective(vo);
		}

		/**
		 * 附件
		 */
		if(vo.getFiles() != null){
			basicFileAppendixService.clearAndUpdateBasicFileAppendix(vo.getId(), vo.getFiles());
		}
		return vo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除总局重点实验室
	 *@param id void 总局重点实验室ID
	 *@Author: wangsh
	 */
	public void deleteInnovationGeneralLaboratoryApply(String id) {
		//TODO 做判断后方能执行删除
		InnovationGeneralLaboratoryApply innovationGeneralLaboratoryApply=innovationGeneralLaboratoryApplyMapper.selectByPrimaryKey(id);
		if(innovationGeneralLaboratoryApply==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		InnovationGeneralLaboratoryApply teminnovationGeneralLaboratoryApply=new InnovationGeneralLaboratoryApply();
		teminnovationGeneralLaboratoryApply.setYn(CommonConstant.FLAG_NO);
		teminnovationGeneralLaboratoryApply.setId(innovationGeneralLaboratoryApply.getId());
		innovationGeneralLaboratoryApplyMapper.updateByPrimaryKeySelective(teminnovationGeneralLaboratoryApply);
	}

    /**
     * @Description: 批量删除总局重点实验室
     * @param ids
     */
	@Override
	@Transactional(readOnly = false)
	public void deleteMultiInnovationGeneralLaboratoryApply(List<String> ids) {
		ids.stream().forEach(id-> this.deleteInnovationGeneralLaboratoryApply(id));
	}

	@Override
	/**
	 *@Description: 查询总局重点实验室详情
	 *@param id
	 *@return InnovationGeneralLaboratoryApply
	 *@Author: wangsh
	 */
	public InnovationGeneralLaboratoryApply findById(String id) {
		InnovationGeneralLaboratoryApply apply = innovationGeneralLaboratoryApplyMapper.selectByPrimaryKey(id);
		InnovationGeneralLaboratoryApplyVo vo = new InnovationGeneralLaboratoryApplyVo();
		BeanUtils.copyProperties(apply, vo);
		vo.setFiles(basicFileAppendixService.findByFormId(id));
		vo.setFlowTaskDto(flowCommonService.findTaskDto(vo));
		return vo;
	}

	@Override
	/**
	 *@Description: 分页查询总局重点实验室
	 *@param innovationGeneralLaboratoryApplyVo
	 *@return PageInfo<InnovationGeneralLaboratoryApply>
	 *@Author: wangsh
	 */
	public PageInfo<InnovationGeneralLaboratoryApply> findPageByQuery(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApplyVo) {
		PageHelper.startPage(innovationGeneralLaboratoryApplyVo.getPageNum(),innovationGeneralLaboratoryApplyVo.getPageSize());
		Example example=new Example(InnovationGeneralLaboratoryApply.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(innovationGeneralLaboratoryApplyVo.getName())){
		//	criteria.andEqualTo(innovationGeneralLaboratoryApplyVo.getName());
		//}
		List<InnovationGeneralLaboratoryApply> innovationGeneralLaboratoryApplyList=innovationGeneralLaboratoryApplyMapper.selectByExample(example);
		return new PageInfo<InnovationGeneralLaboratoryApply>(innovationGeneralLaboratoryApplyList);
	}

	@Override
	@Transactional(readOnly = false)
	public String submitInnovationGeneralLaboratoryApply(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApplyVo) {
		String id = this.saveOrUpdateInnovationGeneralLaboratoryApply(innovationGeneralLaboratoryApplyVo);
		flowCommonService.doFlowStepSubmit(FlowableConstant.FLOW_TECH_ACHIEVEMENT, innovationGeneralLaboratoryApplyVo, this.mapper,
				StringUtils.isNotBlank(innovationGeneralLaboratoryApplyVo.getAuditAdvice())?innovationGeneralLaboratoryApplyVo.getAuditAdvice():"提交总局重点实验室");
		return id;
	}

	@Override
	@Transactional(readOnly = false)
	public String auditInnovationGeneralLaboratoryApply(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApplyVo) {
		flowCommonService.doFlowStepAudit(innovationGeneralLaboratoryApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationGeneralLaboratoryApplyVo.getAuditAdvice()) ? innovationGeneralLaboratoryApplyVo.getAuditAdvice() : "总局重点实验室审核通过"
				, FlowStatusEnum.END.getCode());
		return innovationGeneralLaboratoryApplyVo.getId();
	}

	@Override
	@Transactional(readOnly = false)
	public String sendBackInnovationGeneralLaboratoryApply(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApplyVo) {
		flowCommonService.doFlowStepSendBack(innovationGeneralLaboratoryApplyVo, this.mapper
				, StringUtils.isNotBlank(innovationGeneralLaboratoryApplyVo.getAuditAdvice())?innovationGeneralLaboratoryApplyVo.getAuditAdvice():"总局重点实验室退回"
				, false
		);
		return null;
	}

	@Override
	@Transactional(readOnly = false)
	public String releaseInnovationGeneralLaboratoryApply(InnovationGeneralLaboratoryApplyVo innovationGeneralLaboratoryApplyVo) {
		flowCommonService.doCompleteTask(innovationGeneralLaboratoryApplyVo, this.mapper
				, "总局重点实验室任务书下达完成"
				, FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
		return null;
	}


	@Override
	public PageInfo<InnovationGeneralLaboratoryApply> todoList(InnovationGeneralLaboratoryApplyVo vo) {

		Example example = new Example(InnovationGeneralLaboratoryApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
	}

	@Override
	public PageInfo<InnovationGeneralLaboratoryApply> finishedList(InnovationGeneralLaboratoryApplyVo vo) {
		Example example = new Example(InnovationGeneralLaboratoryApply.class);
		Criteria criteria = getCriteria(vo, example);
		example.orderBy("createTime").desc();
//		PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
		return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
	}
	
	@Override
    public PageInfo<InnovationGeneralLaboratoryApply> endList(InnovationGeneralLaboratoryApplyVo vo) {
        Example example = new Example(InnovationGeneralLaboratoryApply.class);
        Criteria criteria = getCriteria(vo, example);
        criteria.andEqualTo("flowStatus", FlowStatusEnum.END.getCode());
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        return new PageInfo<>(this.mapper.selectByExample(example));
    }

	private Criteria getCriteria(InnovationGeneralLaboratoryApply vo, Example example) {
		Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
		//查询条件
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getLaboratory())){
			criteria.andLike("laboratory", "%" + vo.getLaboratory()+ "%");
		}
		if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getFormType())){
			criteria.andEqualTo("formType", vo.getFormType());
		}
		return criteria;
	}
}
