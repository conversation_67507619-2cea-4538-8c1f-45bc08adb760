package com.fd.stdp.service.audit.impl;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.fd.stdp.beans.audit.InnerAuditEquippedInformation;
import com.fd.stdp.beans.audit.InnerAuditOrg;
import com.fd.stdp.beans.audit.InnerAuditWorkContacts;
import com.fd.stdp.beans.audit.vo.*;
import com.fd.stdp.beans.sys.vo.LoginUser;
import com.fd.stdp.constant.InnerAuditBasicConstant;
import com.fd.stdp.dao.audit.InnerAuditOrgMapper;
import com.fd.stdp.service.user.SysUserUtilService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.audit.InnerAuditEquippedInformationMapper;
import com.fd.stdp.service.audit.InnerAuditEquippedInformationsService;
import com.fd.stdp.constant.CommonConstant;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import com.fd.stdp.service.flowable.FlowCommonService;
import com.fd.stdp.constant.AssigneeConstant;
import com.fd.stdp.enums.FlowStatusEnum;

import static com.fd.stdp.common.BaseController.getCurrentOrgName;
import static com.fd.stdp.common.BaseController.getLoginUser;
import static com.fd.stdp.service.audit.impl.InnerAuditUtil.FANGYUAN;
import static com.fd.stdp.util.AppUserUtil.getCurrentRealName;
import static com.fd.stdp.util.AppUserUtil.getCurrentUserName;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 浙江省内部审计总审计师配备情况表
 *@Author: sef
 *@Date: 2022-06-06 13:56:29
 */
public class InnerAuditEquippedInformationsServiceImpl extends BaseServiceImpl<InnerAuditEquippedInformationMapper, InnerAuditEquippedInformation> implements InnerAuditEquippedInformationsService {

    public static final Logger logger = LoggerFactory.getLogger(InnerAuditEquippedInformationsServiceImpl.class);

    @Autowired
    private InnerAuditEquippedInformationMapper innerAuditEquippedInformationMapper;
    @Autowired
    private FlowCommonService flowCommonService;

    @Autowired
    private SysUserUtilService sysUserUtilService;

    @Autowired
    private InnerAuditOrgMapper innerAuditOrgMapper;

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 保存或更新浙江省内部审计总审计师配备情况表
     *@param innerAuditEquippedInformation 浙江省内部审计总审计师配备情况表对象
     *@return String 浙江省内部审计总审计师配备情况表ID
     *@Author: sef
     */
    public String saveOrUpdateInnerAuditEquippedInformation(InnerAuditEquippedInformationVo vo) {
        if (vo == null) {
            throw new ServiceException("数据异常");
        }
        if (StringUtils.isEmpty(vo.getId())) {
            //新增
            vo.setId(UUIDUtils.getUUID());
            vo.setFillState(InnerAuditBasicConstant.FILL_STATE_ESCALATED);
            innerAuditEquippedInformationMapper.insertSelective(vo);
        } else {
            //避免页面传入修改
            vo.setYn(null);
            innerAuditEquippedInformationMapper.updateByPrimaryKeySelective(vo);
        }
        return vo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    /**
     *@Description: 删除浙江省内部审计总审计师配备情况表
     *@param id void 浙江省内部审计总审计师配备情况表ID
     *@Author: sef
     */
    public void deleteInnerAuditEquippedInformation(String id) {
        //TODO 做判断后方能执行删除
        InnerAuditEquippedInformation innerAuditEquippedInformation = innerAuditEquippedInformationMapper.selectByPrimaryKey(id);
        if (innerAuditEquippedInformation == null) {
            throw new ServiceException("非法请求");
        }
        //逻辑删除
        InnerAuditEquippedInformation teminnerAuditEquippedInformation = new InnerAuditEquippedInformation();
        teminnerAuditEquippedInformation.setYn(CommonConstant.FLAG_NO);
        teminnerAuditEquippedInformation.setId(innerAuditEquippedInformation.getId());
        innerAuditEquippedInformationMapper.updateByPrimaryKeySelective(teminnerAuditEquippedInformation);
    }

    /**
     * @param ids
     * @Description: 批量删除浙江省内部审计总审计师配备情况表
     */
    @Override
    @Transactional(readOnly = false)
    public void deleteMultiInnerAuditEquippedInformation(List<String> ids) {
        ids.stream().forEach(id -> this.deleteInnerAuditEquippedInformation(id));
    }

    @Override
    /**
     *@Description: 查询浙江省内部审计总审计师配备情况表详情
     *@param id
     *@return InnerAuditEquippedInformation
     *@Author: sef
     */
    public InnerAuditEquippedInformation findById(String id) {
        return innerAuditEquippedInformationMapper.selectByPrimaryKey(id);
    }

    @Override
    /**
     *@Description: 分页查询浙江省内部审计总审计师配备情况表
     *@param innerAuditEquippedInformationVo
     *@return PageInfo<InnerAuditEquippedInformation>
     *@Author: sef
     */
    public PageInfo<InnerAuditEquippedInformation> findPageByQuery(InnerAuditEquippedInformationVo innerAuditEquippedInformationVo) {
        PageHelper.startPage(innerAuditEquippedInformationVo.getPageNum(), innerAuditEquippedInformationVo.getPageSize());
        Example example = new Example(InnerAuditEquippedInformation.class);
        example.setOrderByClause(" ORDER_VAL");
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("yn", CommonConstant.FLAG_YES);

        if (StringUtils.isEmpty(innerAuditEquippedInformationVo.getUnitName())) {
            InnerAuditUtil.OrgTypeEnum orgTypeEnum = getFillType(getCurrentOrgName());
            switch (orgTypeEnum) {
                case PROVINCE:
                    break;
                default:
                    criteria.andEqualTo("unitName", getCurrentOrgName());
            }
        } else {
            criteria.andLike("unitName", '%' + innerAuditEquippedInformationVo.getUnitName() + '%');
        }

        //查询条件
        if (!StringUtils.isEmpty(innerAuditEquippedInformationVo.getName())) {
            criteria.andLike("name", '%' + innerAuditEquippedInformationVo.getName() + '%');
        }
        if (!StringUtils.isEmpty(innerAuditEquippedInformationVo.getOrgName())) {
            criteria.andLike("orgName", '%' + innerAuditEquippedInformationVo.getOrgName() + '%');
        }
        if (!StringUtils.isEmpty(innerAuditEquippedInformationVo.getUnitUscc())) {
            criteria.andEqualTo("unitUscc", innerAuditEquippedInformationVo.getUnitUscc());
        }
        if (!StringUtils.isEmpty(innerAuditEquippedInformationVo.getAuditAdvice())) {
            criteria.andLike("auditAdvice", '%' + innerAuditEquippedInformationVo.getAuditAdvice() + '%');
        }
        if (!StringUtils.isEmpty(innerAuditEquippedInformationVo.getLevel())) {
            criteria.andLike("level", '%' + innerAuditEquippedInformationVo.getLevel() + '%');
        }
        if (!ObjectUtils.isEmpty(innerAuditEquippedInformationVo.getFillState())) {
            criteria.andEqualTo("fillState", innerAuditEquippedInformationVo.getFillState());
        }
        List<InnerAuditEquippedInformation> innerAuditEquippedInformationList = innerAuditEquippedInformationMapper.selectByExample(example);
        return new PageInfo<>(innerAuditEquippedInformationList);
    }

    public boolean setUpdateFlag(String roleCode, Integer fillState) {
        Integer updateFlag = CommonConstant.FLAG_NO;
        if ("AUDIT_PROVINCE".equals(roleCode)) {
            if (InnerAuditBasicConstant.FILL_STATE_PROVINCIAL_BUREAU_MODIFICATION == fillState) {
                updateFlag = CommonConstant.FLAG_YES;
            }
        } else {
            if (InnerAuditBasicConstant.FILL_STATE_ESCALATED == fillState) {
                updateFlag = CommonConstant.FLAG_YES;
            }
        }
        return updateFlag == 1;
    }

    @Override
    @Transactional(readOnly = false)
    public String submitInnerAuditEquippedInformation(InnerAuditEquippedInformationVo innerAuditEquippedInformationVo) {
        List<InnerAuditEquippedInformationVo> infomationList = innerAuditEquippedInformationVo.getEquippedList();
        infomationList.stream().forEach(item -> item.setFillState(InnerAuditBasicConstant.FILL_STATE_PROVINCIAL_BUREAU_MODIFICATION));
        this.saveBatchInnerAuditWorkContacts(infomationList);
        return "success";
    }

    @Override
    @Transactional(readOnly = false)
    public String auditInnerAuditEquippedInformation(InnerAuditEquippedInformationVo innerAuditEquippedInformationVo) {
        flowCommonService.doFlowStepAudit(innerAuditEquippedInformationVo, this.mapper
                , StringUtils.isNotBlank(innerAuditEquippedInformationVo.getAuditAdvice()) ? innerAuditEquippedInformationVo.getAuditAdvice() : "浙江省内部审计总审计师配备情况表审核通过"
                , FlowStatusEnum.END.getCode());
        return innerAuditEquippedInformationVo.getId();
    }

    @Override
    @Transactional(readOnly = false)
    public String sendBackInnerAuditEquippedInformation(InnerAuditEquippedInformationVo vo) {
        if (StringUtils.isNotBlank(vo.getUnitName())) {
            innerAuditEquippedInformationMapper.sendBack(vo.getUnitName());
            return "success";
        }
        return "缺少单位名称！";
    }

    @Override
    @Transactional(readOnly = false)
    public String releaseInnerAuditEquippedInformation(InnerAuditEquippedInformationVo innerAuditEquippedInformationVo) {
        flowCommonService.doCompleteTask(innerAuditEquippedInformationVo, this.mapper
                , "浙江省内部审计总审计师配备情况表任务书下达完成"
                , FlowStatusEnum.END.getCode(), AssigneeConstant.DEPT_PROVINCE_ROLE);
        return null;
    }


    @Override
    public PageInfo<InnerAuditEquippedInformation> todoList(InnerAuditEquippedInformationVo vo) {

        Example example = new Example(InnerAuditEquippedInformation.class);
        Criteria criteria = getCriteria(vo, example);
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        return new PageInfo<>(flowCommonService.todoList(vo, this.mapper, example, criteria));
    }

    @Override
    public PageInfo<InnerAuditEquippedInformation> finishedList(InnerAuditEquippedInformationVo vo) {
        Example example = new Example(InnerAuditEquippedInformation.class);
        Criteria criteria = getCriteria(vo, example);
        example.orderBy("createTime").desc();
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        return new PageInfo<>(flowCommonService.finishedList(vo, this.mapper, example, criteria));
    }

    @Override
    public PageInfo<InnerAuditEquippedInformation> endList(InnerAuditEquippedInformationVo vo) {
        Example example = new Example(InnerAuditEquippedInformation.class);
        example.orderBy("createTime").desc();
        Criteria criteria = getCriteria(vo, example);
        return new PageInfo<>(flowCommonService.endList(vo, this.mapper, example, criteria));
    }

    @Override
    @Transactional(readOnly = false)
    public void saveBatchInnerAuditWorkContacts(List<InnerAuditEquippedInformationVo> contactList) {
        if (CollectionUtils.isEmpty(contactList)) {
            throw new ServiceException("请添加内容后重试");
        }
        // 已存在(单位)
        List<InnerAuditEquippedInformationVo> collect = contactList.stream().filter(item ->
                org.springframework.util.StringUtils.hasText(org.apache.commons.lang.ObjectUtils.toString(innerAuditEquippedInformationMapper.findByUnitIsOrNotExist(item), ""))).collect(Collectors.toList());

        // 批量删除 逻辑删除
        if (!collect.isEmpty()) {
            innerAuditEquippedInformationMapper.deleteByUnit(collect.get(0).getUnitName());
        }
        InnerAuditEquippedInformationVo old = collect.get(0);
        contactList.stream().forEach(item -> {
                    if (StringUtils.isBlank(item.getOrgName())) {
                        item.setOrgName(getCurrentOrgName());
                        item.setOrgCode(getLoginUser().getAreaCode());
                    }
                    item.setId(UUIDUtils.getUUID());
                    item.setFillState(old.getFillState() == null ? InnerAuditBasicConstant.FILL_STATE_ESCALATED : old.getFillState());
                    item.setFillTime(old.getFillTime() == null ? new Date() : old.getFillTime());
                    item.setUnitName(old.getUnitName() == null ? getCurrentOrgName() : old.getUnitName());
                    innerAuditEquippedInformationMapper.insert(item);
                }
        );
    }

    @Override
    public PageInfo findList(InnerAuditEquippedInformationVo vo) {
        InnerAuditUtil.OrgTypeEnum orgTypeEnum = getFillType(getCurrentOrgName());
        Boolean provinceFlag = new Boolean(false);
        switch (orgTypeEnum) {
            case PROVINCE:
                provinceFlag = true;
                break;
        }
        if (provinceFlag) {
            PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
            PageInfo pageInfo = new PageInfo(innerAuditEquippedInformationMapper.getProvinceList(vo));
            return pageInfo;
        }
        PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<InnerAuditEquippedInformation> innerAuditEquippedInformations = innerAuditEquippedInformationMapper.selectUnitName('%' + getCurrentOrgName() + '%');
        return new PageInfo(innerAuditEquippedInformations);
    }

    @Override
    public List<InnerAuditEquippedInformationExportVo> exportInnerAuditEquippedInformation(InnerAuditEquippedInformationVo vo) {
        List<InnerAuditEquippedInformation> innerAuditEquippedInformations = innerAuditEquippedInformationMapper.selectUnitName('%' + getCurrentOrgName() + '%');
        List<InnerAuditEquippedInformationExportVo> list = new ArrayList<>();
        AtomicInteger orderValCn = new AtomicInteger();
        innerAuditEquippedInformations.stream().forEach(item -> {
            InnerAuditEquippedInformationExportVo innerAuditEquippedInformationVo = new InnerAuditEquippedInformationExportVo();
            BeanUtils.copyProperties(item, innerAuditEquippedInformationVo);
            innerAuditEquippedInformationVo.setIsIndependentCn(innerAuditEquippedInformationVo.getIsIndependent() == 1 ? "是" : "否");
            innerAuditEquippedInformationVo.setOrderVal(orderValCn.incrementAndGet());
            list.add(innerAuditEquippedInformationVo);
        });
        return list;
    }

    @Override
    public List exportInnerAuditEquippedInformationAllUnit(InnerAuditEquippedInformationVo vo) {
        List<InnerAuditEquippedInformation> innerAudit = innerAuditEquippedInformationMapper.selectSubmit();
        List<InnerAuditEquippedInformationExportVo> list = new ArrayList<>();
        // 多个单位导出一起时序号重编
        AtomicInteger orderValCn = new AtomicInteger();
        innerAudit.stream().forEach(item -> {
            InnerAuditEquippedInformationExportVo innerAuditVo = new InnerAuditEquippedInformationExportVo();
            BeanUtils.copyProperties(item, innerAuditVo);
            innerAuditVo.setOrderVal(orderValCn.incrementAndGet());
            list.add(innerAuditVo);
        });
        return list;
    }

    private Criteria getCriteria(InnerAuditEquippedInformationVo vo, Example example) {
        Criteria criteria = example.createCriteria().andEqualTo("yn", CommonConstant.FLAG_YES);
        //查询条件
		/*if(!org.apache.commons.lang3.StringUtils.isEmpty(vo.getName())){
			criteria.andLike("name", "%" + vo.getName()+ "%");
		}*/
        return criteria;
    }


    private Integer getOperType(String orgName) {
        LoginUser loginUser = getLoginUser();
        Integer operType = sysUserUtilService.getUserOperType(loginUser);
        Example example = new Example(InnerAuditOrg.class);
        example.createCriteria().andEqualTo("orgName", orgName);
        if (CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))) {
            return operType;
        }
        return 0;
    }

    private boolean getIsProOrg(String org) {
        Example example = new Example(InnerAuditOrg.class);
        example.createCriteria().andLike("orgName", "%" + org + "%");
        if (CollectionUtils.isEmpty(innerAuditOrgMapper.selectByExample(example))) {
            return false;
        }
        return true;
    }

    // 0 省局 1 直属单位 2市局 3 县局
    private InnerAuditUtil.OrgTypeEnum getFillType(String org) {
        if (getIsProOrg(org)) {
            if (FANGYUAN.equals(org)) {
                return InnerAuditUtil.OrgTypeEnum.PROVINCE_GO_EM;
            }
            return InnerAuditUtil.OrgTypeEnum.PROVINCE_GO;
        }
        int orgType = getOperType(org);
        if (2 == orgType) {
            return InnerAuditUtil.OrgTypeEnum.PROVINCE;
        } else if (1 == orgType) {
            return InnerAuditUtil.OrgTypeEnum.CITY;
        }
        return InnerAuditUtil.OrgTypeEnum.COUNTRY;
    }
}
