package com.fd.stdp.service.sys;

import java.util.List;

import com.fd.stdp.beans.sys.SysElement;
import com.fd.stdp.beans.sys.vo.SysElementVo;
import com.fd.stdp.common.BaseService;
import com.github.pagehelper.PageInfo;

public interface ElementService extends BaseService<SysElement> {

	public List<SysElement> getAuthorityElementByUserId(String userId);

	public List<SysElement> getAuthorityElementByUserId(String userId, String menuId, Integer pcFlag, Integer appFlag);

	public List<SysElement> selectListAll();

	public void insertSelective(SysElement entity);

	public int updateSelectiveById(SysElementVo vo);

	public void delete(String id, boolean isAdmin);

	public List<SysElement> selectByExample(Object example);

	public void addSysElement(SysElementVo vo);

	/**
	 * 根据用户名得到用户下所有的资源
	 * 
	 * @Description: TODO
	 * @param userName
	 * @return List<SysElement>
	 * @Author: linqiang
	 */
	public List<SysElement> getPermissionByUsername(String userName);

	/**
	 * 分页查询权限
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param name
	 * @param code
	 * @return
	 */
	public PageInfo<SysElement> findPageList(Integer pageNum, Integer pageSize, String name, String code);

	/**
	 * 查询菜单下所有元素(分页)
	 * 
	 * @param sysElement
	 * @return
	 */
	public PageInfo<SysElement> findElementByMenuId(SysElementVo sysElementVo);

}
