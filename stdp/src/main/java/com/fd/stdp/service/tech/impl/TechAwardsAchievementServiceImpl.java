package com.fd.stdp.service.tech.impl;

import java.util.List;

import org.springframework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.tech.TechAwardsAchievement;
import com.fd.stdp.beans.tech.vo.TechAwardsAchievementVo;
import com.fd.stdp.common.exception.ServiceException;
import com.fd.stdp.common.BaseServiceImpl;
import com.fd.stdp.util.UUIDUtils;
import com.fd.stdp.dao.tech.TechAwardsAchievementMapper;
import com.fd.stdp.service.tech.TechAwardsAchievementService;
import com.fd.stdp.constant.CommonConstant;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
@Transactional(readOnly = true)
/**
 *@Description: 奖项成果关联表
 *@Author: wangsh
 *@Date: 2022-01-04 14:00:43
 */
public class TechAwardsAchievementServiceImpl extends BaseServiceImpl<TechAwardsAchievementMapper, TechAwardsAchievement> implements TechAwardsAchievementService{

	public static final Logger logger = LoggerFactory.getLogger(TechAwardsAchievementServiceImpl.class);
	
	@Autowired
	private TechAwardsAchievementMapper techAwardsAchievementMapper;
	
	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 保存或更新奖项成果关联表
	 *@param techAwardsAchievement 奖项成果关联表对象
	 *@return String 奖项成果关联表ID
	 *@Author: wangsh
	 */
	public String saveOrUpdateTechAwardsAchievement(TechAwardsAchievement techAwardsAchievement) {
		if(techAwardsAchievement==null){
			throw new ServiceException("数据异常");
		}
		if(StringUtils.isEmpty(techAwardsAchievement.getId())){
			//新增
			techAwardsAchievement.setId(UUIDUtils.getUUID());
			techAwardsAchievementMapper.insertSelective(techAwardsAchievement);
		}else{
			//避免页面传入修改
			techAwardsAchievement.setYn(null);
			techAwardsAchievementMapper.updateByPrimaryKeySelective(techAwardsAchievement);
		}
		return techAwardsAchievement.getId();
	}

	@Override
	@Transactional(readOnly = false)
	/**
	 *@Description: 删除奖项成果关联表
	 *@param id void 奖项成果关联表ID
	 *@Author: wangsh
	 */
	public void deleteTechAwardsAchievement(String id) {
		//TODO 做判断后方能执行删除
		TechAwardsAchievement techAwardsAchievement=techAwardsAchievementMapper.selectByPrimaryKey(id);
		if(techAwardsAchievement==null){
			throw new ServiceException("非法请求");
		}
		//逻辑删除
		TechAwardsAchievement temtechAwardsAchievement=new TechAwardsAchievement();
		temtechAwardsAchievement.setYn(CommonConstant.FLAG_NO);
		temtechAwardsAchievement.setId(techAwardsAchievement.getId());
		techAwardsAchievementMapper.updateByPrimaryKeySelective(temtechAwardsAchievement);
	}

	@Override
	/**
	 *@Description: 查询奖项成果关联表详情
	 *@param id
	 *@return TechAwardsAchievement
	 *@Author: wangsh
	 */
	public TechAwardsAchievement findById(String id) {
		return techAwardsAchievementMapper.selectByPrimaryKey(id);
	}

	@Override
	/**
	 *@Description: 分页查询奖项成果关联表
	 *@param techAwardsAchievementVo
	 *@return PageInfo<TechAwardsAchievement>
	 *@Author: wangsh
	 */
	public PageInfo<TechAwardsAchievement> findPageByQuery(TechAwardsAchievementVo techAwardsAchievementVo) {
		PageHelper.startPage(techAwardsAchievementVo.getPageNum(),techAwardsAchievementVo.getPageSize());
		Example example=new Example(TechAwardsAchievement.class);
		Criteria criteria=example.createCriteria();
		criteria.andEqualTo("yn",CommonConstant.FLAG_YES);
		//查询条件
		//if(!StringUtils.isEmpty(techAwardsAchievementVo.getName())){
		//	criteria.andEqualTo(techAwardsAchievementVo.getName());
		//}
		List<TechAwardsAchievement> techAwardsAchievementList=techAwardsAchievementMapper.selectByExample(example);
		return new PageInfo<TechAwardsAchievement>(techAwardsAchievementList);
	}

	@Override
	public void clearByAwardId(String awardId) {
		this.mapper.clearByAwardId(awardId);
	}

}
