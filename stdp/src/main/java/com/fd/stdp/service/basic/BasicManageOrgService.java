package com.fd.stdp.service.basic;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.basic.BasicManageOrg;
import com.fd.stdp.beans.basic.vo.BasicManageOrgVo;

/**
 * 监管单位Service接口
 *
 * <AUTHOR>
 * @date 2021-11-08
 */
public interface BasicManageOrgService {
    /**
     * @param basicManageOrg 监管单位对象
     * @return String 监管单位ID
     * @Description: 保存或更新监管单位
     * @Author: yujianfei
     */
    String saveOrUpdateBasicManageOrg(BasicManageOrgVo basicManageOrgVo);

    /**
     * @param ids void 监管单位ID
     * @Description: 删除监管单位
     * @Author: yujianfei
     */
    void deleteBasicManageOrg(List<String> ids);

    /**
     * @param id
     * @return BasicManageOrg
     * @Description: 查询监管单位详情
     * @Author: yujianfei
     */
    BasicManageOrg findById(String id);

    /**
     * @param basicManageOrgVo
     * @return PageInfo<BasicManageOrg>
     * @Description: 分页查询监管单位
     * @Author: yujianfei
     */
    PageInfo<BasicManageOrgVo> findPageByQuery(BasicManageOrgVo basicManageOrgVo);

    /**
     * @return RestApiResponse<?>
     * @Description: 查询所有市级监管单位
     * @Author: yujianfei
     */
    List<BasicManageOrg> findAllCity();

    /**
     * @return RestApiResponse<?>
     * @Description: 查询所有县级监管单位
     * @Author: yujianfei
     */
    List<BasicManageOrg> findAllCounty();
}
