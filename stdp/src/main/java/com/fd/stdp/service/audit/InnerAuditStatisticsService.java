package com.fd.stdp.service.audit;

import java.util.List;

import com.fd.stdp.beans.basic.BasicManageOrg;
import com.github.pagehelper.PageInfo;
import com.fd.stdp.beans.audit.InnerAuditStatistics;
import com.fd.stdp.beans.audit.vo.InnerAuditStatisticsVo;

import javax.servlet.http.HttpServletResponse;

/**
 *@Description: 内审统计表2
 *@Author: wangsh
 *@Date: 2022-02-22 17:30:52
 */
public interface InnerAuditStatisticsService {

	/**
	 *@Description: 保存或更新内审统计表2
	 *@param innerAuditStatistics 内审统计表2对象
	 *@return String 内审统计表2ID
	 *@Author: wangsh
	 */
	String saveOrUpdateInnerAuditStatistics(InnerAuditStatisticsVo innerAuditStatistics);
	
	/**
	 *@Description: 删除内审统计表2
	 *@param id void 内审统计表2ID
	 *@Author: wangsh
	 */
	void deleteInnerAuditStatistics(String id);

	/**
	 * @Description: 批量删除内审统计表2
	 * @param ids
	 */
    void deleteMultiInnerAuditStatistics(List<String> ids);

	/**
	 *@Description: 查询内审统计表2详情
	 *@param id
	 *@return InnerAuditStatistics
	 *@Author: wangsh
	 */
	InnerAuditStatistics findById(String id);

	/**
	 *@Description: 分页查询内审统计表2
	 *@param innerAuditStatisticsVo
	 *@return PageInfo<InnerAuditStatistics>
	 *@Author: wangsh
	 */
	PageInfo<InnerAuditStatistics> findPageByQuery(InnerAuditStatisticsVo innerAuditStatisticsVo);

	/**
	 *@Description: 分页查询内审统计表2 处室统计用
	 *@param innerAuditStatisticsVo
	 *@return PageInfo<InnerAuditStatistics>
	 *@Author: wangsh
	 */
	PageInfo<InnerAuditStatistics> findOfficePageByQuery(InnerAuditStatisticsVo innerAuditStatisticsVo);
	
	/**
	 * 提交
	 * @param innerAuditStatisticsVo
	 * @return
	 */
    String submitInnerAuditStatistics(InnerAuditStatisticsVo innerAuditStatisticsVo);

	/**
	 * 审核
	 * @param innerAuditStatisticsVo
	 * @return
	 */
	String auditInnerAuditStatistics(InnerAuditStatisticsVo innerAuditStatisticsVo);

	/**
	 * 退回
	 * @param innerAuditStatisticsVo
	 * @return
	 */
	String sendBackInnerAuditStatistics(InnerAuditStatisticsVo innerAuditStatisticsVo);

	/**
	 * 任务书下达
	 * @param innerAuditStatisticsVo
	 * @return
	 */
	String releaseInnerAuditStatistics(InnerAuditStatisticsVo innerAuditStatisticsVo);
	
	/**
	 * 待办列表
	 */
    PageInfo<InnerAuditStatistics> todoList(InnerAuditStatisticsVo innerAuditStatisticsVo);

	/**
	 * 已办列表
	 */
	PageInfo<InnerAuditStatistics> finishedList(InnerAuditStatisticsVo innerAuditStatisticsVo);

	/**
	 * 已完成列表
	 */
	PageInfo<InnerAuditStatistics> endList(InnerAuditStatisticsVo innerAuditStatisticsVo);

    PageInfo<BasicManageOrg> applyOrg(InnerAuditStatisticsVo innerAuditStatisticsVo);

    void export(InnerAuditStatisticsVo innerAuditStatisticsVo, HttpServletResponse response);

    PageInfo applyDepartment(InnerAuditStatisticsVo innerAuditStatisticsVo);
}
