package com.fd.stdp.feign;

import com.fd.stdp.beans.sys.OssUser;
import com.fd.stdp.controller.sys.rest.YthResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@Component
@FeignClient(name = "ythoauth-api", url = "${ythoauth.url}")
public interface YthoauthApi {

    // /**
    //  * 获取已登录用户信息
    //  *
    //  * @param est 设备唯一标识
    //  * @param appName 应用名称
    //  * @return 返回OssUser对象，包含已登录用户信息
    //  */
    // @GetMapping("/ythOssGetLonginUser")
    // OssUser ythOssGetLonginUser(@RequestParam("est") String est, @RequestParam("appName") String appName);


    /**
     * 一体化验证用户
     *
     * @param map 包含登录所需参数的Map对象
     * @return 包含验证结果的YthResponse对象
     */
    @PostMapping(value = "/wsite/service/sso/verify", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    YthResponse<String> ythOssLogin(@RequestBody Map<String, ?> map);

    @PostMapping("/SSOLogin")
    OssUser ythOssPostLoginUser(@RequestBody Map<String, ?> map);
}
