package com.fd.stdp.feign;

import com.fd.stdp.util.expertExtract.beans.fegn.SsoExpert;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "review-api", url = "${review.url}")
public interface ReviewApi {

    @PostMapping("/sso/getUserInfo")
    ReviewLogin getUserInfo(@RequestBody SsoExpert ssoExpert);

}
