<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fd.stdp.dao.project.ProjectApplyInfoMapper">
    <resultMap id="CommonBaseResultMap" type="com.fd.stdp.common.BaseEntity">
        <result column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="YN" property="yn" jdbcType="INTEGER"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="CREATE_USER_NICKNAME" property="createUserNickname" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NIKENAME" property="updateUserNikename" jdbcType="VARCHAR"/>
    </resultMap>
    <!-- 项目基本信息 -->
    <resultMap id="BaseResultMap" type="com.fd.stdp.beans.project.ProjectApplyInfo" extends="CommonBaseResultMap">
        <!--
          WARNING - @mbg.generated
        -->
        <!-- 自筹经费 -->
        <result column="SELF_RAISED_FUNDS" property="selfRaisedFunds" jdbcType="DECIMAL"/>
        <!-- 项目负责人证件号码 -->
        <result column="LEADER_CARD_NO" property="leaderCardNo" jdbcType="VARCHAR"/>
        <!-- 国际标准 -->
        <result column="INTERNATIONAL_STANDARD" property="internationalStandard" jdbcType="BIGINT"/>
        <!-- 主要技术指标和经济、社会效益 -->
        <result column="TECHNICAL_INDICATOES" property="technicalIndicatoes" jdbcType="VARCHAR"/>
        <!-- 当前流程操作人 -->
        <result column="FLOW_USER" property="flowUser" jdbcType="VARCHAR"/>
        <!-- 其他 -->
        <result column="OTHERS" property="others" jdbcType="VARCHAR"/>
        <!-- 设备配置理由及说明 -->
        <result column="DEVICE_CONFIG_REASON" property="deviceConfigReason" jdbcType="VARCHAR"/>
        <!-- 当前流程操作人姓名 -->
        <result column="FLOW_USER_NAME" property="flowUserName" jdbcType="VARCHAR"/>
        <!-- 项目团队硕士人数 -->
        <result column="APPLY_TEAM_DEGREE_MASTER" property="applyTeamDegreeMaster" jdbcType="VARCHAR"/>
        <!-- 项目类别 -->
        <result column="PROJECT_TYPE_CODE" property="projectTypeCode" jdbcType="VARCHAR"/>
        <!-- 研究内容/技术关键/创新点 -->
        <result column="RESEARCH_CONTENTS" property="researchContents" jdbcType="VARCHAR"/>
        <!-- 论文 -->
        <result column="PAPER" property="paper" jdbcType="BIGINT"/>
        <!-- 标准物质 -->
        <result column="STANDARD_MATERIAL" property="standardMaterial" jdbcType="BIGINT"/>
        <!-- 申报单位联系电话 -->
        <result column="APPLY_UNIT_LINK_TEL" property="applyUnitLinkTel" jdbcType="VARCHAR"/>
        <!-- 项目负责人出生日期 -->
        <result column="LEADER_BIRTHDAY" property="leaderBirthday" jdbcType="TIMESTAMP"/>
        <!-- 预期水平 -->
        <result column="EXPECTED_LEVEL" property="expectedLevel" jdbcType="VARCHAR"/>
        <!-- 研究目标 -->
        <result column="RESEARCH_TARGET" property="researchTarget" jdbcType="VARCHAR"/>
        <!-- 推荐单位名称 -->
        <result column="RECOMMEND_NAME" property="recommendName" jdbcType="VARCHAR"/>
        <!-- 计量基标准装置 -->
        <result column="MEASURE_STANDARD_SERVICE" property="measureStandardService" jdbcType="BIGINT"/>
        <!-- 项目名称 -->
        <result column="PROJECT_NAME" property="projectName" jdbcType="VARCHAR"/>
        <!-- 当前流程状态 -->
        <result column="FLOW_STATUS" property="flowStatus" jdbcType="VARCHAR"/>
        <!-- 新工艺 -->
        <result column="NEW_PROCESS" property="newProcess" jdbcType="BIGINT"/>
        <!-- 项目团队其他学位人数 -->
        <result column="APPLY_TEAM_DEGREE_OTHER" property="applyTeamDegreeOther" jdbcType="VARCHAR"/>
        <!-- 新产品 -->
        <result column="NEW_PRODUCT" property="newProduct" jdbcType="BIGINT"/>
        <!-- 项目负责人姓名 -->
        <result column="LEADER_NAME" property="leaderName" jdbcType="VARCHAR"/>
        <!-- 国家标准 -->
        <result column="NATIONAL_STANDARD" property="nationalStandard" jdbcType="BIGINT"/>
        <!-- 项目团队总人数 -->
        <result column="APPLY_TEAM_NUM" property="applyTeamNum" jdbcType="VARCHAR"/>
        <!-- 行业标准 -->
        <result column="INDUSTRY_STANDARD" property="industryStandard" jdbcType="BIGINT"/>
        <!-- 终止时间 -->
        <result column="END_DATE" property="endDate" jdbcType="TIMESTAMP"/>
        <!-- 专利 -->
        <result column="PATENT" property="patent" jdbcType="BIGINT"/>
        <!-- 项目团队正高人数 -->
        <result column="APPLY_TEAM_TITLE_HIGH" property="applyTeamTitleHigh" jdbcType="VARCHAR"/>
        <!-- 检测技术能力方面 -->
        <result column="DETECTION_TECHNOLOGY" property="detectionTechnology" jdbcType="VARCHAR"/>
        <!-- 市监领域 -->
        <result column="AREA_CODE" property="areaCode" jdbcType="VARCHAR"/>
        <!-- 推荐单位联系电话 -->
        <result column="RECOMMEND_LINK_TEL" property="recommendLinkTel" jdbcType="VARCHAR"/>
        <!-- 推荐单位联系人 -->
        <result column="RECOMMEND_LINK_MAN" property="recommendLinkMan" jdbcType="VARCHAR"/>
        <!-- 发明专利 -->
        <result column="INVENTION_PATENT" property="inventionPatent" jdbcType="BIGINT"/>
        <!-- 项目负责人性别 -->
        <result column="LEADER_GENDER" property="leaderGender" jdbcType="VARCHAR"/>
        <!-- 标准样品 -->
        <result column="STANDARD_SAMPLE" property="standardSample" jdbcType="BIGINT"/>
        <!-- 项目负责人证件类型 -->
        <result column="LEADER_DOCUMENT_TYPE" property="leaderDocumentType" jdbcType="VARCHAR"/>
        <!-- 项目负责人办公电话 -->
        <result column="LEADER_TEL" property="leaderTel" jdbcType="VARCHAR"/>
        <!-- 项目团队副高人数 -->
        <result column="APPLY_TEAM_TITLE_HIGH_SECOND" property="applyTeamTitleHighSecond" jdbcType="VARCHAR"/>
        <!-- 起始时间 -->
        <result column="STRAT_DATE" property="stratDate" jdbcType="TIMESTAMP"/>
        <!-- 所属专业 -->
        <result column="PRO_TEXT" property="proText" jdbcType="VARCHAR"/>
        <!-- 项目年度 -->
        <result column="YEAR_NO" property="yearNo" jdbcType="BIGINT"/>
        <!-- 项目负责人职称 -->
        <result column="LEADER_TITLE" property="leaderTitle" jdbcType="VARCHAR"/>
        <!-- 新装置 -->
        <result column="NEW_DEVICE" property="newDevice" jdbcType="BIGINT"/>
        <!-- 项目团队学士人数 -->
        <result column="APPLY_TEAM_DEGREE_BACHELOR" property="applyTeamDegreeBachelor" jdbcType="VARCHAR"/>
        <!-- 项目子类别 -->
        <result column="PROJECT_SECOND_TYPE_CODE" property="projectSecondTypeCode" jdbcType="VARCHAR"/>
        <!-- 其他经费 -->
        <result column="OTHER_FUNDS" property="otherFunds" jdbcType="DECIMAL"/>
        <!-- 研究报告 -->
        <result column="RESEARCH_REPORT" property="researchReport" jdbcType="BIGINT"/>
        <!-- 论著 -->
        <result column="WORKS" property="works" jdbcType="BIGINT"/>
        <!-- 项目团队博士人数 -->
        <result column="APPLY_TEAM_DEGREE_DR" property="applyTeamDegreeDr" jdbcType="VARCHAR"/>
        <!-- 项目负责人年龄 -->
        <result column="LEADER_AGE" property="leaderAge" jdbcType="VARCHAR"/>
        <!-- 项目负责人专业 -->
        <result column="LEADER_PROFESSIONAL" property="leaderProfessional" jdbcType="VARCHAR"/>
        <!-- 实用新型专利 -->
        <result column="NEW_MODEL_PATENT" property="newModelPatent" jdbcType="BIGINT"/>
        <!-- 申报编号 -->
        <result column="PROJECT_NUMBER" property="projectNumber" jdbcType="VARCHAR"/>
        <!-- 申报单位通讯地址 -->
        <result column="APPLY_UNIT_ADDRESS" property="applyUnitAddress" jdbcType="VARCHAR"/>
        <!-- 申报单位联系人 -->
        <result column="APPLY_UNIT_LINK_MAN" property="applyUnitLinkMan" jdbcType="VARCHAR"/>
        <!-- 项目负责人学位 -->
        <result column="LEADER_DEGREE" property="leaderDegree" jdbcType="VARCHAR"/>
        <!-- 所属专业 -->
        <result column="PRO_CODE" property="proCode" jdbcType="VARCHAR"/>
        <!-- 地方标准 -->
        <result column="LOCAL_STANDARD" property="localStandard" jdbcType="BIGINT"/>
        <!-- 市监领域 -->
        <result column="AREA_TEXT" property="areaText" jdbcType="VARCHAR"/>
        <!-- 申报单位邮政编码 -->
        <result column="APPLY_UNIT_POST_CODE" property="applyUnitPostCode" jdbcType="VARCHAR"/>
        <!-- 支撑市场监管方面 -->
        <result column="MARKET_REGULATION" property="marketRegulation" jdbcType="VARCHAR"/>
        <!-- 企业标准 -->
        <result column="ENTERPRISE_STANDARD" property="enterpriseStandard" jdbcType="BIGINT"/>
        <!-- 项目团队其他职称人数 -->
        <result column="APPLY_TEAM_TITLE_OTHER" property="applyTeamTitleOther" jdbcType="VARCHAR"/>
        <!-- 计量技术规范 -->
        <result column="TECHNICAL_SPECIFICATION" property="technicalSpecification" jdbcType="BIGINT"/>
        <!-- 服务产业方面 -->
        <result column="SERVICE_INDUSTRY" property="serviceIndustry" jdbcType="VARCHAR"/>
        <!-- 项目团队中级人数 -->
        <result column="APPLY_TEAM_TITLE_MIDDLE" property="applyTeamTitleMiddle" jdbcType="VARCHAR"/>
        <!-- 新方法 -->
        <result column="NEW_METHOD" property="newMethod" jdbcType="BIGINT"/>
        <!-- 项目负责人移动电话 -->
        <result column="LEADER_MOBILE" property="leaderMobile" jdbcType="VARCHAR"/>
        <!-- 项目负责人E-mail -->
        <result column="LEADER_EMAIL" property="leaderEmail" jdbcType="VARCHAR"/>
        <!-- 技术标准 -->
        <result column="TECHNICAL_STANDARD" property="technicalStandard" jdbcType="BIGINT"/>
        <!-- 专家评审平均分 -->
        <result column="AVERAGE" property="average" jdbcType="DECIMAL"/>
        <!-- 申报单位名称 -->
        <result column="APPLY_UNIT_NAME" property="applyUnitName" jdbcType="VARCHAR"/>
        <!-- 项目子类别 -->
        <result column="PROJECT_SECOND_TYPE_TEXT" property="projectSecondTypeText" jdbcType="VARCHAR"/>
        <!-- 对应指南ID -->
        <result column="GUID_ID" property="guidId" jdbcType="VARCHAR"/>
        <!-- 提交时间 -->
        <result column="SUBMIT_DATE" property="submitDate" jdbcType="TIMESTAMP"/>
        <!-- 财政经费 -->
        <result column="FISCAL_FUNDS" property="fiscalFunds" jdbcType="DECIMAL"/>
        <!-- 总经费 -->
        <result column="TOTAL_FUNDS" property="totalFunds" jdbcType="DECIMAL"/>
        <!-- 申报单位统一社会信用代码 -->
        <result column="APPLY_UNIT_USCC" property="applyUnitUscc" jdbcType="VARCHAR"/>
        <!-- 计量检定规程 -->
        <result column="VERIFICATION_REGULATION" property="verificationRegulation" jdbcType="BIGINT"/>
        <!-- 计算机软件著作权 -->
        <result column="SOFTWARE_COPYRIGHT" property="softwareCopyright" jdbcType="BIGINT"/>
        <!-- 团体标准 -->
        <result column="GROUP_STANDARD" property="groupStandard" jdbcType="BIGINT"/>
        <!-- 对应指南 -->
        <result column="GUID_NAME" property="guidName" jdbcType="VARCHAR"/>
        <!-- 项目类别 -->
        <result column="PROJECT_TYPE_TEXT" property="projectTypeText" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="findPageByQuery" resultType="com.fd.stdp.beans.project.ProjectApplyInfo">
        SELECT i.* FROM project_apply_info i
        left join project_apply_cooperation_unit u on i.id = u.APPLY_ID and u.yn = 1
        where i.yn = 1
        <if test="vo.projectName != null and vo.projectName != ''">
            <bind name="searchProjectName" value="'%' + vo.projectName + '%'"/>
            AND i.PROJECT_NAME LIKE #{searchProjectName}
        </if>
        <if test="vo.projectTypeText != null and vo.projectTypeText != ''">
            <bind name="searchProjectTypeText" value="'%' + vo.projectTypeText + '%'"/>
            AND i.PROJECT_TYPE_TEXT LIKE #{searchProjectTypeText}
        </if>
        <if test="vo.projectSecondTypeText != null and vo.projectSecondTypeText != ''">
            <bind name="searchProjectSecondTypeText" value="'%' + vo.projectSecondTypeText + '%'"/>
            AND i.PROJECT_SECOND_TYPE_TEXT LIKE #{searchProjectSecondTypeText}
        </if>
        <if test="vo.yearNo != null">
            AND i.YEAR_NO = #{vo.yearNo}
        </if>
        <if test="vo.applyUnitName != null and vo.applyUnitName != ''">
            <bind name="searchApplyUnitName" value="'%' + vo.applyUnitName + '%'"/>
            AND i.APPLY_UNIT_NAME LIKE #{searchApplyUnitName}
        </if>
        <if test="vo.leaderName != null and vo.leaderName != ''">
            <bind name="searchLeaderName" value="'%' + vo.leaderName + '%'"/>
            AND i.LEADER_NAME LIKE #{searchLeaderName}
        </if>
        <if test="vo.totalFunds != null">
            AND i.TOTAL_FUNDS = #{vo.totalFunds}
        </if>
        <if test="vo.FISCAL_FUNDS != null">
            AND i.FISCAL_FUNDS = #{vo.FISCAL_FUNDS}
        </if>
        <if test="vo.proCode != null">
            AND i.PRO_CODE = #{vo.proCode}
        </if>
    </select>
    <select id="queryExpertTodoList" resultType="com.fd.stdp.beans.project.ProjectApplyInfo">
        SELECT i.* FROM project_apply_info i
        inner join project_apply_experts e on i.id = e.apply_id and e.yn = 1
        <!-- inner join project_apply_expert_mumber m on i.id = m.apply_id and m.yn = 1
        left join sys_user su on su.id = e.user_id and su.yn = 1 -->
        where i.yn = 1 and (e.is_submit is null or e.is_submit = 0) and e.user_id = #{vo.attr4}
        AND I.FLOW_STATUS = 7
        <if test="vo.projectName != null and vo.projectName != ''">
            <bind name="searchProjectName" value="'%' + vo.projectName + '%'"/>
            AND i.PROJECT_NAME LIKE #{searchProjectName}
        </if>
        <if test="vo.projectTypeText != null and vo.projectTypeText != ''">
            <bind name="searchProjectTypeText" value="'%' + vo.projectTypeText + '%'"/>
            AND i.PROJECT_TYPE_TEXT LIKE #{searchProjectTypeText}
        </if>
        <if test="vo.projectSecondTypeText != null and vo.projectSecondTypeText != ''">
            <bind name="searchProjectSecondTypeText" value="'%' + vo.projectSecondTypeText + '%'"/>
            AND i.PROJECT_SECOND_TYPE_TEXT LIKE #{searchProjectSecondTypeText}
        </if>
        <if test="vo.yearNo != null">
            AND i.YEAR_NO = #{vo.yearNo}
        </if>
        <if test="vo.applyUnitName != null and vo.applyUnitName != ''">
            <bind name="searchApplyUnitName" value="'%' + vo.applyUnitName + '%'"/>
            AND i.APPLY_UNIT_NAME LIKE #{searchApplyUnitName}
        </if>
        <if test="vo.leaderName != null and vo.leaderName != ''">
            <bind name="searchLeaderName" value="'%' + vo.leaderName + '%'"/>
            AND i.LEADER_NAME LIKE #{searchLeaderName}
        </if>
    </select>

    <select id="queryExpertFinishedList" resultType="com.fd.stdp.beans.project.ProjectApplyInfo">
        SELECT i.* FROM project_apply_info i
        inner join project_apply_experts e on i.id = e.apply_id and e.yn = 1
        where i.yn = 1 and e.is_submit = 1 and e.user_id = #{vo.attr4}
        <if test="vo.projectName != null and vo.projectName != ''">
            <bind name="searchProjectName" value="'%' + vo.projectName + '%'"/>
            AND i.PROJECT_NAME LIKE #{searchProjectName}
        </if>
        <if test="vo.projectTypeText != null and vo.projectTypeText != ''">
            <bind name="searchProjectTypeText" value="'%' + vo.projectTypeText + '%'"/>
            AND i.PROJECT_TYPE_TEXT LIKE #{searchProjectTypeText}
        </if>
        <if test="vo.projectSecondTypeText != null and vo.projectSecondTypeText != ''">
            <bind name="searchProjectSecondTypeText" value="'%' + vo.projectSecondTypeText + '%'"/>
            AND i.PROJECT_SECOND_TYPE_TEXT LIKE #{searchProjectSecondTypeText}
        </if>
        <if test="vo.yearNo != null">
            AND i.YEAR_NO = #{vo.yearNo}
        </if>
        <if test="vo.applyUnitName != null and vo.applyUnitName != ''">
            <bind name="searchApplyUnitName" value="'%' + vo.applyUnitName + '%'"/>
            AND i.APPLY_UNIT_NAME LIKE #{searchApplyUnitName}
        </if>
        <if test="vo.leaderName != null and vo.leaderName != ''">
            <bind name="searchLeaderName" value="'%' + vo.leaderName + '%'"/>
            AND i.LEADER_NAME LIKE #{searchLeaderName}
        </if>
    </select>

    <!--    项目申报代办列表查询-->
    <select id="findTodoList" resultType="com.fd.stdp.beans.project.vo.ProjectApplyInfoVo">
        SELECT *
        FROM project_apply_info
        WHERE YN = 1
        AND ACT_NODE_KEY = #{vo.actNodeKey}
        <if test="vo.extractIdentification != null and vo.extractIdentification != ''">
            AND EXTRACT_EXPERT_STATUS IS NULL
        </if>
        <if test="vo.userAreaCode != null and vo.userAreaCode != ''">
            AND APPLY_UNIT_POST_CODE LIKE CONCAT( #{vo.userAreaCode} ,'%')
        </if>
        <if test="vo.userUscc != null and vo.userUscc != ''">
            AND APPLY_UNIT_USCC = #{vo.userUscc}
        </if>
        <if test="vo.leaderName != null and vo.leaderName != ''">
            <bind name="leaderName" value="'%' + vo.leaderName + '%'"/>
            AND LEADER_NAME LIKE CONCAT('%', #{vo.leaderName} ,'%')
        </if>
        <if test="vo.proCode != null and vo.proCode != ''">
            AND PRO_CODE = #{vo.proCode}
        </if>
        <if test="vo.projectName != null and vo.projectName != ''">
            <bind name="projectName" value="'%' + vo.projectName + '%'"/>
            AND PROJECT_NAME LIKE CONCAT('%', #{vo.projectName} ,'%')
        </if>
        <if test="vo.projectTypeCode != null and vo.projectTypeCode != ''">
            AND PROJECT_TYPE_CODE = #{vo.projectTypeCode}
        </if>
        <if test="vo.projectSecondTypeCode != null and vo.projectSecondTypeCode != ''">
            AND PROJECT_SECOND_TYPE_CODE = #{vo.projectSecondTypeCode}
        </if>
        <if test="vo.submitDateStart != null">
            AND SUBMIT_DATE &gt;= #{vo.submitDateStart}
        </if>
        <if test="vo.submitDateEnd != null">
            AND SUBMIT_DATE &lt;= #{vo.submitDateEnd}
        </if>
        <if test="vo.applyUnitName != null and vo.applyUnitName != ''">
            <bind name="applyUnitName" value="'%' + vo.applyUnitName + '%'"/>
            AND APPLY_UNIT_NAME LIKE CONCAT('%', #{vo.applyUnitName} ,'%')
        </if>
        order by SUBMIT_DATE DESC
    </select>

    <!--项目代办列表查询-->
    <select id="todoNew" resultType="com.fd.stdp.beans.project.vo.ProjectApplyInfoVo">
        SELECT *
        FROM PROJECT_APPLY_INFO
        WHERE YN = 1
        <!-- 关键修改：确保列表非空才生成IN子句 -->
        <if test="proNodeList != null and proNodeList.size() > 0">
            AND ACT_NODE_KEY IN
            <foreach collection="proNodeList" item="taskCode" open="(" separator="," close=")">
                #{taskCode}
            </foreach>
        </if>
        <if test="vo.userAreaCode != null and vo.userAreaCode != ''">
            AND APPLY_UNIT_POST_CODE LIKE CONCAT( #{vo.userAreaCode} ,'%')
        </if>
        <if test="vo.userUscc != null and vo.userUscc != ''">
            AND APPLY_UNIT_USCC = #{vo.userUscc}
        </if>
    </select>

    <!--等待专家审核列表查询 -->
    <select id="awaitingExpertReview" resultType="com.fd.stdp.beans.project.vo.ProjectApplyInfoVo">
        SELECT
        p.*,
        em.REVIEW_STATUS AS reviewStatus
        FROM
        project_apply_info p
        JOIN
        project_apply_expert_mumber em
        ON
        p.ID = em.APPLY_ID
        AND em.YN = 1
        <if test="vo.expertName != null and vo.expertName != ''">
            AND em.NAME = #{vo.expertName}
        </if>
        <if test="vo.expertPhone != null and vo.expertPhone != ''">
            AND em.PHONE = #{vo.expertPhone}
        </if>
        WHERE
        p.ID IN (
        SELECT APPLY_ID
        FROM project_apply_expert_mumber
        WHERE yn = 1
        <if test="vo.expertName != null and vo.expertName != ''">
            AND NAME = #{vo.expertName}
        </if>
        <if test="vo.expertPhone != null and vo.expertPhone != ''">
            AND PHONE = #{vo.expertPhone}
        </if>
        )
        AND p.YN = 1
        AND p.ACT_NODE_KEY = 'expertReviewTask'
        <if test="vo.leaderName != null and vo.leaderName != ''">
            <bind name="leaderName" value="'%' + vo.leaderName + '%'"/>
            AND p.LEADER_NAME LIKE #{leaderName}
        </if>
        <if test="vo.proCode != null and vo.proCode != ''">
            AND p.PRO_CODE = #{vo.proCode}
        </if>
        <if test="vo.projectName != null and vo.projectName != ''">
            <bind name="projectName" value="'%' + vo.projectName + '%'"/>
            AND p.PROJECT_NAME LIKE #{projectName}
        </if>
        <if test="vo.projectTypeCode != null and vo.projectTypeCode != ''">
            AND p.PROJECT_TYPE_CODE = #{vo.projectTypeCode}
        </if>
        <if test="vo.projectSecondTypeCode != null and vo.projectSecondTypeCode != ''">
            AND p.PROJECT_SECOND_TYPE_CODE = #{vo.projectSecondTypeCode}
        </if>
        <if test="vo.submitDateStart != null">
            AND p.SUBMIT_DATE &gt;= #{vo.submitDateStart}
        </if>
        <if test="vo.submitDateEnd != null">
            AND p.SUBMIT_DATE &lt;= #{vo.submitDateEnd}
        </if>
        <if test="vo.applyUnitName != null and vo.applyUnitName != ''">
            <bind name="applyUnitName" value="'%' + vo.applyUnitName + '%'"/>
            AND p.APPLY_UNIT_NAME LIKE #{applyUnitName}
        </if>
        ORDER BY
        p.SUBMIT_DATE DESC
    </select>

    <!--专家审核过的列表查询-->
    <select id="expertReviewedList" resultType="com.fd.stdp.beans.project.vo.ProjectApplyInfoVo">
        SELECT *
        from project_apply_info
        where ID in (
        SELECT APPLY_ID FROM project_apply_expert_score where USER_ID = #{vo.userId} and yn = 1
        )
        and YN = 1
        <if test="vo.leaderName != null and vo.leaderName != ''">
            <bind name="leaderName" value="'%' + vo.leaderName + '%'"/>
            AND LEADER_NAME LIKE #{leaderName}
        </if>
        <if test="vo.proCode != null and vo.proCode != ''">
            AND PRO_CODE = #{vo.proCode}
        </if>
        <if test="vo.projectName != null and vo.projectName != ''">
            <bind name="projectName" value="'%' + vo.projectName + '%'"/>
            AND PROJECT_NAME LIKE #{projectName}
        </if>
        <if test="vo.projectTypeCode != null and vo.projectTypeCode != ''">
            AND PROJECT_TYPE_CODE = #{vo.projectTypeCode}
        </if>
        <if test="vo.projectSecondTypeCode != null and vo.projectSecondTypeCode != ''">
            AND PROJECT_SECOND_TYPE_CODE = #{vo.projectSecondTypeCode}
        </if>
        <if test="vo.submitDateStart != null">
            AND SUBMIT_DATE &gt;= #{vo.submitDateStart}
        </if>
        <if test="vo.submitDateEnd != null">
            AND SUBMIT_DATE &lt;= #{vo.submitDateEnd}
        </if>
        <if test="vo.applyUnitName != null and vo.applyUnitName != ''">
            <bind name="applyUnitName" value="'%' + vo.applyUnitName + '%'"/>
            AND APPLY_UNIT_NAME LIKE #{applyUnitName}
        </if>
        ORDER BY SUBMIT_DATE DESC
    </select>

    <!--历史申报项目查询-->
    <select id="findHistoryDeclarationPro" resultType="com.fd.stdp.beans.project.vo.ProjectApplyInfoVo">
        SELECT *,( SELECT NAME FROM project_apply_teams t WHERE p.id = t.APPLY_ID AND yn = 1 AND IS_LEADER = 0 LIMIT 1 )
        proLeader
        FROM
        project_apply_info p
        WHERE
        MARK = 1
        AND yn = 1
        <if test="vo.userUscc != null and vo.userUscc != ''">
            AND p.APPLY_UNIT_USCC = #{vo.userUscc}
        </if>
        <if test="vo.userId != null and vo.userId != ''">
            AND p.USER_ID = #{vo.userId}
        </if>
        <if test="vo.proLeader != null and vo.proLeader != ''">
            <bind name="proLeader" value="'%' + vo.proLeader + '%'"/>
            AND p.id IN (
            SELECT
            APPLY_ID
            FROM
            project_apply_teams
            WHERE
            yn = 1
            AND IS_LEADER = 0
            AND NAME like #{proLeader})
        </if>
        <if test="vo.projectNumber != null and vo.projectNumber != ''">
            <bind name="projectNumber" value="'%' + vo.projectNumber + '%'"/>
            AND p.PROJECT_NUMBER LIKE #{projectNumber}
        </if>
        <if test="vo.projectName != null and vo.projectName != ''">
            <bind name="projectName" value="'%' + vo.projectName + '%'"/>
            AND p.PROJECT_NAME LIKE #{projectName}
        </if>
        <if test="vo.projectTypeCode != null and vo.projectTypeCode != ''">
            AND p.PROJECT_TYPE_CODE = #{vo.projectTypeCode}
        </if>
        <if test="vo.applyUnitName != null and vo.applyUnitName != ''">
            <bind name="applyUnitName" value="'%' + vo.applyUnitName + '%'"/>
            AND p.APPLY_UNIT_NAME LIKE #{applyUnitName}
        </if>
        <if test="vo.isCommit != null">
            AND p.IS_COMMIT = #{vo.isCommit}
        </if>
        ORDER BY p.CREATE_TIME DESC
    </select>

    <!--统计历史项目提交数量-->
    <select id="countCommitNumber" resultType="java.lang.Integer">
        SELECT count(1)
        FROM
        project_apply_info p
        WHERE
        MARK = 1
        AND yn = 1
        AND p.IS_COMMIT = 1
        <if test="vo.userUscc != null and vo.userUscc != ''">
            AND p.APPLY_UNIT_USCC = #{vo.userUscc}
        </if>
        <if test="vo.userId != null and vo.userId != ''">
            AND p.USER_ID = #{vo.userId}
        </if>
        <if test="vo.proLeader != null and vo.proLeader != ''">
            <bind name="proLeader" value="'%' + vo.proLeader + '%'"/>
            AND p.id IN (
            SELECT
            APPLY_ID
            FROM
            project_apply_teams
            WHERE
            yn = 1
            AND IS_LEADER = 0
            AND NAME like #{proLeader})
        </if>
        <if test="vo.projectNumber != null and vo.projectNumber != ''">
            <bind name="projectNumber" value="'%' + vo.projectNumber + '%'"/>
            AND p.PROJECT_NUMBER LIKE #{projectNumber}
        </if>
        <if test="vo.projectName != null and vo.projectName != ''">
            <bind name="projectName" value="'%' + vo.projectName + '%'"/>
            AND p.PROJECT_NAME LIKE #{projectName}
        </if>
        <if test="vo.projectTypeCode != null and vo.projectTypeCode != ''">
            AND p.PROJECT_TYPE_CODE = #{vo.projectTypeCode}
        </if>
        <if test="vo.applyUnitName != null and vo.applyUnitName != ''">
            <bind name="applyUnitName" value="'%' + vo.applyUnitName + '%'"/>
            AND p.APPLY_UNIT_NAME LIKE #{applyUnitName}
        </if>
    </select>

    <!-- 导出储备项目信息 - 自定义SQL查询 -->
    <select id="findSelfReserveProForExport" resultType="com.fd.stdp.beans.project.vo.ProjectApplyInfoVo">
        SELECT
            p.PROJECT_NAME as projectName,
            p.PROJECT_FIELD_SECOND_NAME as projectFieldSecondName,
            p.PROJECT_FIELD_THIRD_NAME as projectFieldThirdName,
            p.PROJECT_TYPE_TEXT as projectTypeText,
            p.PROJECT_SECOND_TYPE_TEXT as projectSecondTypeText,
            p.LEADER_NAME as leaderName,
            p.TOTAL_FUNDS as totalFunds,
            p.FISCAL_FUNDS as fiscalFunds,
            -- 关联basic_areacode表，将行政区划代码转换为名称
            a.AREA_NAME AS applyUnitAreaName,  -- 替代原APPLY_UNIT_POST_CODE的显示
            p.APPLY_UNIT_NAME as applyUnitName,
            -- 关联PROJECT_APPLY_COOPERATION_UNIT表（一对多，用GROUP_CONCAT聚合）
            GROUP_CONCAT(DISTINCT c.UNIT_NAME ORDER BY c.ID SEPARATOR ',') AS cooperationUnitNames,
            -- 关联project_apply_recommend_unit表（一对多，用GROUP_CONCAT聚合）
            GROUP_CONCAT(DISTINCT r.RECOMMEND_NAME ORDER BY r.ID SEPARATOR ',') AS recommendNames,
            -- 通过子查询获取项目负责人信息（IS_LEADER=0）
            leader_info.LEADER_TITLE as leaderTitle,
            leader_info.LEADER_DEGREE as leaderDegree,
            p.act_node_key as actNodeKey,
            p.act_node_name as actNodeName
        FROM
            project_apply_info p
        -- 关联行政区划表（1对1，用LEFT JOIN确保主表记录不丢失）
        LEFT JOIN
            basic_areacode a ON p.APPLY_UNIT_POST_CODE = a.AREA_CODE
        -- 关联合作单位表（1对多，LEFT JOIN保留无合作单位的项目）
        LEFT JOIN
            PROJECT_APPLY_COOPERATION_UNIT c ON p.ID = c.APPLY_ID AND c.yn = 1  -- 假设yn=1表示有效记录
        -- 关联推荐单位表（1对多，LEFT JOIN保留无推荐单位的项目）
        LEFT JOIN
            project_apply_recommend_unit r ON p.ID = r.APPLY_ID AND r.yn = 1  -- 假设yn=1表示有效记录
        -- 通过子查询关联项目负责人信息，只查询IS_LEADER=0的记录
        LEFT JOIN (
            SELECT
                APPLY_ID,
                TITLE AS LEADER_TITLE,
                DEGREE AS LEADER_DEGREE
            FROM
                project_apply_teams
            WHERE
                yn = 1
                AND IS_LEADER = 0  -- 只查询项目负责人
        ) leader_info ON p.ID = leader_info.APPLY_ID
        WHERE
            p.MARK IS NULL
            AND p.yn = 1
            AND P.CREATE_TIME > '2025-07-18 00:00:00'
            <if test="vo.userId != null and vo.userId != ''">
                AND p.USER_ID = #{vo.userId}
            </if>
            <if test="vo.projectName != null and vo.projectName != ''">
                <bind name="projectName" value="'%' + vo.projectName + '%'"/>
                AND p.PROJECT_NAME LIKE #{projectName}
            </if>
            <if test="vo.leaderName != null and vo.leaderName != ''">
                <bind name="leaderName" value="'%' + vo.leaderName + '%'"/>
                AND p.LEADER_NAME LIKE #{leaderName}
            </if>
            <if test="vo.applyUnitName != null and vo.applyUnitName != ''">
                <bind name="applyUnitName" value="'%' + vo.applyUnitName + '%'"/>
                AND p.APPLY_UNIT_NAME LIKE #{applyUnitName}
            </if>
            <if test="vo.projectTypeCode != null and vo.projectTypeCode != ''">
                AND p.PROJECT_TYPE_CODE = #{vo.projectTypeCode}
            </if>
        GROUP BY
            p.ID
        ORDER BY
            p.CREATE_TIME DESC
    </select>
</mapper>
