package com.fd.stdp.dao.project;

import com.fd.stdp.beans.project.ProjectApplyInfo;
import com.fd.stdp.beans.project.vo.ProjectApplyInfoVo;
import com.fd.stdp.common.mybatis.BaseInfoMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description: 项目基本信息Mapper接口
 * @Author: yujian<PERSON>i
 * @Date: 2021-11-26
 */
@Mapper
public interface ProjectApplyInfoMapper extends BaseInfoMapper<ProjectApplyInfo> {

    List<ProjectApplyInfo> findPageByQuery(@Param("vo") ProjectApplyInfoVo projectApplyInfoVo);

    List queryExpertTodoList(@Param("vo") Object projectApplyInfoVo);

    List queryExpertFinishedList(@Param("vo") Object vo);

    /**
     * 项目申报代办列表查询
     *
     * @param vo
     * @return
     */
    List<ProjectApplyInfoVo> findTodoList(@Param("vo") ProjectApplyInfoVo vo);

    /**
     * 项目代办列表查询
     *
     * @param vo
     * @return
     */
    List<ProjectApplyInfoVo> todoNew(@Param("vo") ProjectApplyInfoVo vo, @Param("proNodeList") List<String> proNodeList);

    @Select("select * from project_apply_info where id = #{proId}")
    ProjectApplyInfoVo findInfoById(String proId);

    /**
     * 等待专家审核列表查询
     *
     * @param applyVo
     * @return
     */
    List<ProjectApplyInfoVo> awaitingExpertReview(@Param("vo") ProjectApplyInfoVo applyVo);

    /**
     * 专家审核过的列表查询
     *
     * @param vo
     * @return
     */
    List<ProjectApplyInfoVo> expertReviewedList(@Param("vo") ProjectApplyInfoVo vo);

    /**
     * 历史申报项目查询
     *
     * @param vo
     * @return
     */
    List<ProjectApplyInfoVo> findHistoryDeclarationPro(@Param("vo") ProjectApplyInfoVo vo);

    /**
     * 统计历史项目提交数量
     *
     * @param vo
     * @return
     */
    int countCommitNumber(@Param("vo") ProjectApplyInfoVo vo);

    /**
     * 导出储备项目信息 - 自定义SQL查询
     *
     * @param vo
     * @return
     */
    List<ProjectApplyInfoVo> findSelfReserveProForExport(@Param("vo") ProjectApplyInfoVo vo);
}
