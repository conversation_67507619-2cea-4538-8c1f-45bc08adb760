package com.fd.stdp.beans.innovation;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fd.stdp.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import javax.persistence.*;
import java.math.BigDecimal;

/**
 *@Description: 重点实验室培育申报评审
 *@Author: wangsh
 *@Date: 2022-03-11 15:43:25
 */
@Table(name = "INNOVATION_LABORATORY_APPLY_COMMENT")
@ApiModel(value = "com.fd.stdp.beans.innovation.InnovationLaboratoryApplyComment", description = "重点实验室培育申报评审")
public class InnovationLaboratoryApplyComment extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @Column(name = "APPLY_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="申请id")
    private String applyId;
    
    @Column(name = "USER_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="专家id")
    private String userId;
    
    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构code")
    private String orgCode;
    
    @Column(name = "ORG_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构名称")
    private String orgName;
    
    @Column(name = "USER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String userName;
    
    @Column(name = "EXPERT_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="专家类型")
    private String expertType;
    
    @Column(name = "LABORATORY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="实验室名称")
    private String laboratory;
    
    @Column(name = "SCORE1")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="分值1")
    private BigDecimal score1;
    
    @Column(name = "SCORE2")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="分值2")
    private BigDecimal score2;
    
    @Column(name = "SCORE3")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="分值3")
    private BigDecimal score3;
    
    @Column(name = "SCORE4")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="分值4")
    private BigDecimal score4;
    
    @Column(name = "SCORE5")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="分值5")
    private BigDecimal score5;
    
    @Column(name = "SCORE6")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="分值6")
    private BigDecimal score6;
    
    @Column(name = "SCORE7")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="分值7")
    private BigDecimal score7;
    
    @Column(name = "SCORE8")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="分值8")
    private BigDecimal score8;
    
    @Column(name = "SCORE9")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="分值9")
    private BigDecimal score9;
    
    @Column(name = "SCORE10")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="分值10")
    private BigDecimal score10;
    
    @Column(name = "SCORE11")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="分值11")
    private BigDecimal score11;
    
    @Column(name = "SCORE12")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="分值12")
    private BigDecimal score12;
    
    @Column(name = "RESULT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String result;
    
    @Column(name = "OPINION")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="评审意见")
    private String opinion;
    
    @Column(name = "NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String name;
    
    
    /**
     * GET 申请id
     * @return applyId
     */
    public String getApplyId() {
        return applyId;
    }

    /**
     * SET 申请id
     * @param applyId
     */
    public void setApplyId(String applyId) {
        this.applyId = applyId == null ? null : applyId.trim();
    }
    /**
     * GET 专家id
     * @return userId
     */
    public String getUserId() {
        return userId;
    }

    /**
     * SET 专家id
     * @param userId
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }
    /**
     * GET 机构code
     * @return orgCode
     */
    public String getOrgCode() {
        return orgCode;
    }

    /**
     * SET 机构code
     * @param orgCode
     */
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }
    /**
     * GET 机构名称
     * @return orgName
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * SET 机构名称
     * @param orgName
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }
    /**
     * GET 
     * @return userName
     */
    public String getUserName() {
        return userName;
    }

    /**
     * SET 
     * @param userName
     */
    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }
    /**
     * GET 专家类型
     * @return expertType
     */
    public String getExpertType() {
        return expertType;
    }

    /**
     * SET 专家类型
     * @param expertType
     */
    public void setExpertType(String expertType) {
        this.expertType = expertType == null ? null : expertType.trim();
    }
    /**
     * GET 实验室名称
     * @return laboratory
     */
    public String getLaboratory() {
        return laboratory;
    }

    /**
     * SET 实验室名称
     * @param laboratory
     */
    public void setLaboratory(String laboratory) {
        this.laboratory = laboratory == null ? null : laboratory.trim();
    }
    /**
     * GET 分值1
     * @return score1
     */
    public BigDecimal getScore1() {
        return score1;
    }

    /**
     * SET 分值1
     * @param score1
     */
    public void setScore1(BigDecimal score1) {
        this.score1 = score1;
    }
    /**
     * GET 分值2
     * @return score2
     */
    public BigDecimal getScore2() {
        return score2;
    }

    /**
     * SET 分值2
     * @param score2
     */
    public void setScore2(BigDecimal score2) {
        this.score2 = score2;
    }
    /**
     * GET 分值3
     * @return score3
     */
    public BigDecimal getScore3() {
        return score3;
    }

    /**
     * SET 分值3
     * @param score3
     */
    public void setScore3(BigDecimal score3) {
        this.score3 = score3;
    }
    /**
     * GET 分值4
     * @return score4
     */
    public BigDecimal getScore4() {
        return score4;
    }

    /**
     * SET 分值4
     * @param score4
     */
    public void setScore4(BigDecimal score4) {
        this.score4 = score4;
    }
    /**
     * GET 分值5
     * @return score5
     */
    public BigDecimal getScore5() {
        return score5;
    }

    /**
     * SET 分值5
     * @param score5
     */
    public void setScore5(BigDecimal score5) {
        this.score5 = score5;
    }
    /**
     * GET 分值6
     * @return score6
     */
    public BigDecimal getScore6() {
        return score6;
    }

    /**
     * SET 分值6
     * @param score6
     */
    public void setScore6(BigDecimal score6) {
        this.score6 = score6;
    }
    /**
     * GET 分值7
     * @return score7
     */
    public BigDecimal getScore7() {
        return score7;
    }

    /**
     * SET 分值7
     * @param score7
     */
    public void setScore7(BigDecimal score7) {
        this.score7 = score7;
    }
    /**
     * GET 分值8
     * @return score8
     */
    public BigDecimal getScore8() {
        return score8;
    }

    /**
     * SET 分值8
     * @param score8
     */
    public void setScore8(BigDecimal score8) {
        this.score8 = score8;
    }
    /**
     * GET 分值9
     * @return score9
     */
    public BigDecimal getScore9() {
        return score9;
    }

    /**
     * SET 分值9
     * @param score9
     */
    public void setScore9(BigDecimal score9) {
        this.score9 = score9;
    }
    /**
     * GET 分值10
     * @return score10
     */
    public BigDecimal getScore10() {
        return score10;
    }

    /**
     * SET 分值10
     * @param score10
     */
    public void setScore10(BigDecimal score10) {
        this.score10 = score10;
    }
    /**
     * GET 分值11
     * @return score11
     */
    public BigDecimal getScore11() {
        return score11;
    }

    /**
     * SET 分值11
     * @param score11
     */
    public void setScore11(BigDecimal score11) {
        this.score11 = score11;
    }
    /**
     * GET 分值12
     * @return score12
     */
    public BigDecimal getScore12() {
        return score12;
    }

    /**
     * SET 分值12
     * @param score12
     */
    public void setScore12(BigDecimal score12) {
        this.score12 = score12;
    }
    /**
     * GET 
     * @return result
     */
    public String getResult() {
        return result;
    }

    /**
     * SET 
     * @param result
     */
    public void setResult(String result) {
        this.result = result == null ? null : result.trim();
    }
    /**
     * GET 评审意见
     * @return opinion
     */
    public String getOpinion() {
        return opinion;
    }

    /**
     * SET 评审意见
     * @param opinion
     */
    public void setOpinion(String opinion) {
        this.opinion = opinion == null ? null : opinion.trim();
    }
    /**
     * GET 
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * SET 
     * @param name
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}