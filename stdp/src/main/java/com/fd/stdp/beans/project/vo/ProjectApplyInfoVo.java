package com.fd.stdp.beans.project.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fd.stdp.beans.basic.vo.BasicFileAppendixVo;
import com.fd.stdp.beans.flowable.FlowTaskDto;
import com.fd.stdp.beans.project.*;
import com.fd.stdp.enums.FlowStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.BeanUtils;

import javax.persistence.Basic;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * @Description: 项目基本信息Vo
 * @Author: yujianfei
 * @Date: 2021-11-26
 */
@ApiModel(value = "com.fd.stdp.beans.project.ProjectApplyInfoVo", description = "ProjectApplyInfoVo")
public class ProjectApplyInfoVo extends ProjectApplyInfo {

    /**
     * 附件信息
     */
    private List<BasicFileAppendixVo> files;

    /**
     * 计划进度
     */
    private List<ProjectApplyProgress> progressList;

    /**
     * 设备购置预算明细
     */
    private List<ProjectApplyDevices> devicesList;

    /**
     * 科研经费
     */
    private ProjectApplyCost projectApplyCost;

    /**
     * 财政补助经费
     */
    private ProjectApplyCost projectAllowanceCost;

    /**
     * 财政补助经费
     */
    private ProjectApplyCost projectSelfCost;

    /**
     * 项目团队
     */
    private List<ProjectApplyTeams> teamsList;

    /**
     * 合作单位
     */
    private List<ProjectApplyCooperationUnit> unitList;

    /**
     * 专家列表
     */
    private List<ProjectApplyExpertMumberVo> experts;

    /**
     * 提交专家打分结果
     */
    private ProjectApplyExperts expertsReview;

    /**
     * 专家打分结果列表
     */
    private List<ProjectApplyExperts> expertsReviews;

    /**
     * 流程(审核)意见
     */
    private String auditAdvice;

    /**
     * 是否通过
     */
    private String isPass;

    /**
     * todo
     */
    private Integer codeType;

    /**
     * 专家评分
     */
    private String expertPointText;

    /**
     * 是否变更立项子类别
     */
    private String secondTypeChangedText;

    @ApiModelProperty(value = "科研仪器及设备信息")
    private List<ProjectApplyResearchEquipment> researchEquipmentList;

    @ApiModelProperty(value = "装备项目-项目资金安排")
    private List<ProjectApplyInfoZbFundsPlan> fundsPlanList;

    @ApiModelProperty(value = "重大项目-参与单位列表")
    private List<ProjectApplyRecommendUnit> recommendList;

    @ApiModelProperty(value = "重大项目-人才共享列表")
    private List<ProjectApplyTalentShare> talentShareList;

    /**
     * 审核备注
     */
    private String advice;


    /**
     * 候选人
     */
    private String assignerUser;


    @ApiModelProperty(value = "跳转流程key")
    private String targetKey;


    /**
     * 候选组
     */
    private String assignerGroup;

    public String acceptAreaLevel;

    public String acceptAreaCode;

    public String flowResult;

    @ApiModelProperty(value = "当前用户areaCode")
    private String userAreaCode;

    @ApiModelProperty(value = "当前用户Uscc")
    private String userUscc;

    @ApiModelProperty(value = "抽取专家列表查询，只查还没抽的")
    private String extractIdentification;

    @ApiModelProperty(value = "专家名称")
    private String expertName;

    @ApiModelProperty(value = "专家手机号码")
    private String expertPhone;

    @ApiModelProperty(value = "当前专家用户评审结果")
    private ProjectApplyExpertScoreVo nowExpertScore;

    @ApiModelProperty(value = "项目评审状态")
    private int reviewStatus;

    @ApiModelProperty(value = "项目团队负责人名称")
    private String proLeader;

    public ProjectApplyCost getProjectAllowanceCost() {
        return projectAllowanceCost;
    }

    public void setProjectAllowanceCost(ProjectApplyCost projectAllowanceCost) {
        this.projectAllowanceCost = projectAllowanceCost;
    }

    public List<ProjectApplyProgress> getProgressList() {
        return progressList;
    }

    public void setProgressList(List<ProjectApplyProgress> progressList) {
        this.progressList = progressList;
    }

    public List<ProjectApplyDevices> getDevicesList() {
        return devicesList;
    }

    public void setDevicesList(List<ProjectApplyDevices> devicesList) {
        this.devicesList = devicesList;
    }

    public ProjectApplyCost getProjectApplyCost() {
        return projectApplyCost;
    }

    public void setProjectApplyCost(ProjectApplyCost projectApplyCost) {
        this.projectApplyCost = projectApplyCost;
    }

    public List<ProjectApplyTeams> getTeamsList() {
        return teamsList;
    }

    public void setTeamsList(List<ProjectApplyTeams> teamsList) {
        this.teamsList = teamsList;
    }

    public List<ProjectApplyCooperationUnit> getUnitList() {
        return unitList;
    }

    public void setUnitList(List<ProjectApplyCooperationUnit> unitList) {
        this.unitList = unitList;
    }

    public List<ProjectApplyExpertMumberVo> getExperts() {
        return experts;
    }

    public void setExperts(List<ProjectApplyExpertMumberVo> experts) {
        this.experts = experts;
    }

    public Integer getCodeType() {
        return codeType;
    }

    public void setCodeType(Integer codeType) {
        this.codeType = codeType;
    }

    public List<BasicFileAppendixVo> getFiles() {
        return files;
    }

    public void setFiles(List<BasicFileAppendixVo> files) {
        this.files = files;
    }

    public String getAuditAdvice() {
        return auditAdvice;
    }

    public void setAuditAdvice(String auditAdvice) {
        this.auditAdvice = auditAdvice;
    }

    public List<ProjectApplyExperts> getExpertsReviews() {
        return expertsReviews;
    }

    public void setExpertsReviews(List<ProjectApplyExperts> expertsReviews) {
        this.expertsReviews = expertsReviews;
    }

    public String getIsPass() {
        return isPass;
    }

    public void setIsPass(String isPass) {
        this.isPass = isPass;
    }

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private Integer pageNum = 1;
    private Integer pageSize = 20;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 搜索条件 todo finish end query
     */
    String queryType;

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    /**
     * 转换为任务书对象
     * @return
     */
    public ProjectContractApplyVo toProjectContractApplyVo() {
        ProjectContractApplyVo vo = new ProjectContractApplyVo();
        BeanUtils.copyProperties(this, vo);
        vo.setId(null);
        vo.setApplyId(getId());
        vo.setFlowUser(FlowStatusEnum.WAIT_APPLY.getRole());
        vo.setFlowStatus(FlowStatusEnum.WAIT_APPLY.getCode());
        /*vo.setContractType(getProjectTypeText());
        vo.setContractTypeCode(getProjectTypeCode());
        vo.setApplyId(getId());
        vo.setProjectTypeCode(getProjectSecondTypeCode());
        vo.setProjectTypeText(getProjectSecondTypeText());
        vo.setInventionCount(getPatent());
        vo.setInventionPatent(getInventionPatent());
        vo.setUtilityModelAuth(getNewModelPatent());

        vo.setProjectLevelText(getProjectTypeText());
        vo.setProjectLevelCode(getProjectTypeCode());
        vo.setProText(getProText());
        vo.setProCode(getProCode());*/
        return vo;
    }

    /**
     * 流程对象
     */
    private FlowTaskDto flowTaskDto;

    public FlowTaskDto getFlowTaskDto() {
        return flowTaskDto;
    }

    public void setFlowTaskDto(FlowTaskDto flowTaskDto) {
        this.flowTaskDto = flowTaskDto;
    }

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date submitDateStart;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date submitDateEnd;

    public Date getSubmitDateStart() {
        return submitDateStart;
    }

    public void setSubmitDateStart(Date submitDateStart) {
        this.submitDateStart = submitDateStart;
    }

    public Date getSubmitDateEnd() {
        return submitDateEnd;
    }

    public void setSubmitDateEnd(Date submitDateEnd) {
        this.submitDateEnd = submitDateEnd;
    }

    private List<String> ids = new ArrayList<>();

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    private static final DateFormat leaderBirthdayExportDf = new SimpleDateFormat("yyyy-MM-dd");
    public String getLeaderBirthdayExport() {
        return getLeaderBirthday()==null?"":leaderBirthdayExportDf.format(getLeaderBirthday());
    }

    public String getExpertPointText() {
        return expertPointText;
    }

    public void setExpertPointText(String expertPointText) {
        this.expertPointText = expertPointText;
    }

    public String getSecondTypeChangedText() {
        return secondTypeChangedText;
    }

    public void setSecondTypeChangedText(String secondTypeChangedText) {
        this.secondTypeChangedText = secondTypeChangedText;
    }

    /**
     * 专家意见
     */
    private String expertAdvice1;
    private String expertAdvice2;
    private String expertAdvice3;

    public String getExpertAdvice1() {
        return expertAdvice1;
    }

    public void setExpertAdvice1(String expertAdvice1) {
        this.expertAdvice1 = expertAdvice1;
    }

    public String getExpertAdvice2() {
        return expertAdvice2;
    }

    public void setExpertAdvice2(String expertAdvice2) {
        this.expertAdvice2 = expertAdvice2;
    }

    public String getExpertAdvice3() {
        return expertAdvice3;
    }

    public void setExpertAdvice3(String expertAdvice3) {
        this.expertAdvice3 = expertAdvice3;
    }

    public ProjectApplyCost getProjectSelfCost() {
        return projectSelfCost;
    }

    public void setProjectSelfCost(ProjectApplyCost projectSelfCost) {
        this.projectSelfCost = projectSelfCost;
    }

    public List<ProjectApplyResearchEquipment> getResearchEquipmentList() {
        return researchEquipmentList;
    }

    public void setResearchEquipmentList(List<ProjectApplyResearchEquipment> researchEquipmentList) {
        this.researchEquipmentList = researchEquipmentList;
    }

    public List<ProjectApplyInfoZbFundsPlan> getFundsPlanList() {
        return fundsPlanList;
    }

    public void setFundsPlanList(List<ProjectApplyInfoZbFundsPlan> fundsPlanList) {
        this.fundsPlanList = fundsPlanList;
    }

    public String getAdvice() {
        return advice;
    }

    public void setAdvice(String advice) {
        this.advice = advice;
    }

    public String getAssignerUser() {
        return assignerUser;
    }

    public void setAssignerUser(String assignerUser) {
        this.assignerUser = assignerUser;
    }

    public String getAssignerGroup() {
        return assignerGroup;
    }

    public void setAssignerGroup(String assignerGroup) {
        this.assignerGroup = assignerGroup;
    }

    public String getAcceptAreaLevel() {
        return acceptAreaLevel;
    }

    public void setAcceptAreaLevel(String acceptAreaLevel) {
        this.acceptAreaLevel = acceptAreaLevel;
    }

    public String getAcceptAreaCode() {
        return acceptAreaCode;
    }

    public void setAcceptAreaCode(String acceptAreaCode) {
        this.acceptAreaCode = acceptAreaCode;
    }

    public String getTargetKey() {
        return targetKey;
    }

    public void setTargetKey(String targetKey) {
        this.targetKey = targetKey;
    }

    public String getFlowResult() {
        return flowResult;
    }

    public void setFlowResult(String flowResult) {
        this.flowResult = flowResult;
    }

    public String getUserAreaCode() {
        return userAreaCode;
    }

    public void setUserAreaCode(String userAreaCode) {
        this.userAreaCode = userAreaCode;
    }

    public String getUserUscc() {
        return userUscc;
    }

    public void setUserUscc(String userUscc) {
        this.userUscc = userUscc;
    }

    public List<ProjectApplyRecommendUnit> getRecommendList() {
        return recommendList;
    }

    public void setRecommendList(List<ProjectApplyRecommendUnit> recommendList) {
        this.recommendList = recommendList;
    }

    public List<ProjectApplyTalentShare> getTalentShareList() {
        return talentShareList;
    }

    public void setTalentShareList(List<ProjectApplyTalentShare> talentShareList) {
        this.talentShareList = talentShareList;
    }

    public String getExtractIdentification() {
        return extractIdentification;
    }

    public void setExtractIdentification(String extractIdentification) {
        this.extractIdentification = extractIdentification;
    }

    public String getExpertName() {
        return expertName;
    }

    public void setExpertName(String expertName) {
        this.expertName = expertName;
    }

    public String getExpertPhone() {
        return expertPhone;
    }

    public void setExpertPhone(String expertPhone) {
        this.expertPhone = expertPhone;
    }

    public ProjectApplyExpertScoreVo getNowExpertScore() {
        return nowExpertScore;
    }

    public void setNowExpertScore(ProjectApplyExpertScoreVo nowExpertScore) {
        this.nowExpertScore = nowExpertScore;
    }

    public int getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(int reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getProLeader() {
        return proLeader;
    }

    public void setProLeader(String proLeader) {
        this.proLeader = proLeader;
    }

    // 导出相关字段
    /**
     * 合作单位名称（多个用逗号分隔）
     */
    private String cooperationUnitNames;

    /**
     * 推荐单位名称（多个用逗号分隔）
     */
    private String recommendNames;

    /**
     * 项目负责人职称
     */
    private String leaderTitle;

    /**
     * 项目负责人学历
     */
    private String leaderDegree;

    /**
     * 申报单位所在地区名称
     */
    private String applyUnitAreaName;

    public String getCooperationUnitNames() {
        return cooperationUnitNames;
    }

    public void setCooperationUnitNames(String cooperationUnitNames) {
        this.cooperationUnitNames = cooperationUnitNames;
    }

    public String getRecommendNames() {
        return recommendNames;
    }

    public void setRecommendNames(String recommendNames) {
        this.recommendNames = recommendNames;
    }

    public String getLeaderTitle() {
        return leaderTitle;
    }

    public void setLeaderTitle(String leaderTitle) {
        this.leaderTitle = leaderTitle;
    }

    public String getLeaderDegree() {
        return leaderDegree;
    }

    public void setLeaderDegree(String leaderDegree) {
        this.leaderDegree = leaderDegree;
    }

    public String getApplyUnitAreaName() {
        return applyUnitAreaName;
    }

    public void setApplyUnitAreaName(String applyUnitAreaName) {
        this.applyUnitAreaName = applyUnitAreaName;
    }
}
