package com.fd.stdp.beans.innovation;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fd.stdp.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import javax.persistence.*;
import java.math.BigDecimal;

/**
 *@Description: 其他载体
 *@Author: wangsh
 *@Date: 2022-03-07 19:46:34
 */
@Table(name = "INNOVATION_OTHER_INNOVATION_APPLY")
@ApiModel(value = "com.fd.stdp.beans.innovation.InnovationOtherInnovationApply", description = "其他载体")
public class InnovationOtherInnovationApply extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构code")
    private String orgCode;
    
    @Column(name = "ORG_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构名称")
    private String orgName;
    
    @Column(name = "CENTER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="名称")
    private String centerName;
    
    @Column(name = "RUNTIME_DATE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="批复时间")
    private String runtimeDate;
    
    @Column(name = "APPLY_UNIT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="依托单位")
    private String applyUnitName;
    
    @Column(name = "INDUSTRY_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="行业领域")
    private String industryType;
    
    @Column(name = "GOAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="主攻方向和技术")
    private String goal;
    
    @Column(name = "LEADER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="负责人")
    private String leaderName;
    
    @Column(name = "TEAM_NUMBER_COUNT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="相关人数")
    private String teamNumberCount;
    
    @Column(name = "ZHONG_JI_PRESENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="本科及中级职称以上人员占比")
    private String zhongJiPresent;
    
    @Column(name = "RESEARCH_AREA_YEAR")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="相关场地面积年度")
    private BigDecimal researchAreaYear;
    
    @Column(name = "RESEARCH_AREA_TOTAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="相关场地面积累计")
    private BigDecimal researchAreaTotal;
    
    @Column(name = "DEVICE_VALUE_YEAR")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="相关设备原值年度")
    private BigDecimal deviceValueYear;
    
    @Column(name = "DEVICE_VALUE_TOTAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="相关设备原值累计")
    private BigDecimal deviceValueTotal;
    
    @Column(name = "EQUIP_VALUE_YEAR")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="相关装备数量年度")
    private BigDecimal equipValueYear;
    
    @Column(name = "EQUIP_VALUE_TOTAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="相关装备数量累计")
    private BigDecimal equipValueTotal;
    
    @Column(name = "FUND_TOTAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="上年度投入")
    private BigDecimal fundTotal;
    
    @Column(name = "OTHER_FIELDS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="其他字段内容")
    private String otherFields;
    
    @Column(name = "MARK_FIELDS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="备用字段内容")
    private String markFields;
    
    @Column(name = "FLOW_STATUS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowStatus;
    
    @Column(name = "FLOW_USER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowUser;
    
    @Column(name = "FLOW_USER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowUserName;
    
    
    /**
     * GET 机构code
     * @return orgCode
     */
    public String getOrgCode() {
        return orgCode;
    }

    /**
     * SET 机构code
     * @param orgCode
     */
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }
    /**
     * GET 机构名称
     * @return orgName
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * SET 机构名称
     * @param orgName
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }
    /**
     * GET 名称
     * @return centerName
     */
    public String getCenterName() {
        return centerName;
    }

    /**
     * SET 名称
     * @param centerName
     */
    public void setCenterName(String centerName) {
        this.centerName = centerName == null ? null : centerName.trim();
    }
    /**
     * GET 批复时间
     * @return runtimeDate
     */
    public String getRuntimeDate() {
        return runtimeDate;
    }

    /**
     * SET 批复时间
     * @param runtimeDate
     */
    public void setRuntimeDate(String runtimeDate) {
        this.runtimeDate = runtimeDate == null ? null : runtimeDate.trim();
    }
    /**
     * GET 依托单位
     * @return applyUnitName
     */
    public String getApplyUnitName() {
        return applyUnitName;
    }

    /**
     * SET 依托单位
     * @param applyUnitName
     */
    public void setApplyUnitName(String applyUnitName) {
        this.applyUnitName = applyUnitName == null ? null : applyUnitName.trim();
    }
    /**
     * GET 行业领域
     * @return industryType
     */
    public String getIndustryType() {
        return industryType;
    }

    /**
     * SET 行业领域
     * @param industryType
     */
    public void setIndustryType(String industryType) {
        this.industryType = industryType == null ? null : industryType.trim();
    }
    /**
     * GET 主攻方向和技术
     * @return goal
     */
    public String getGoal() {
        return goal;
    }

    /**
     * SET 主攻方向和技术
     * @param goal
     */
    public void setGoal(String goal) {
        this.goal = goal == null ? null : goal.trim();
    }
    /**
     * GET 负责人
     * @return leaderName
     */
    public String getLeaderName() {
        return leaderName;
    }

    /**
     * SET 负责人
     * @param leaderName
     */
    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName == null ? null : leaderName.trim();
    }
    /**
     * GET 相关人数
     * @return teamNumberCount
     */
    public String getTeamNumberCount() {
        return teamNumberCount;
    }

    /**
     * SET 相关人数
     * @param teamNumberCount
     */
    public void setTeamNumberCount(String teamNumberCount) {
        this.teamNumberCount = teamNumberCount == null ? null : teamNumberCount.trim();
    }
    /**
     * GET 本科及中级职称以上人员占比
     * @return zhongJiPresent
     */
    public String getZhongJiPresent() {
        return zhongJiPresent;
    }

    /**
     * SET 本科及中级职称以上人员占比
     * @param zhongJiPresent
     */
    public void setZhongJiPresent(String zhongJiPresent) {
        this.zhongJiPresent = zhongJiPresent == null ? null : zhongJiPresent.trim();
    }
    /**
     * GET 相关场地面积年度
     * @return researchAreaYear
     */
    public BigDecimal getResearchAreaYear() {
        return researchAreaYear;
    }

    /**
     * SET 相关场地面积年度
     * @param researchAreaYear
     */
    public void setResearchAreaYear(BigDecimal researchAreaYear) {
        this.researchAreaYear = researchAreaYear;
    }
    /**
     * GET 相关场地面积累计
     * @return researchAreaTotal
     */
    public BigDecimal getResearchAreaTotal() {
        return researchAreaTotal;
    }

    /**
     * SET 相关场地面积累计
     * @param researchAreaTotal
     */
    public void setResearchAreaTotal(BigDecimal researchAreaTotal) {
        this.researchAreaTotal = researchAreaTotal;
    }
    /**
     * GET 相关设备原值年度
     * @return deviceValueYear
     */
    public BigDecimal getDeviceValueYear() {
        return deviceValueYear;
    }

    /**
     * SET 相关设备原值年度
     * @param deviceValueYear
     */
    public void setDeviceValueYear(BigDecimal deviceValueYear) {
        this.deviceValueYear = deviceValueYear;
    }
    /**
     * GET 相关设备原值累计
     * @return deviceValueTotal
     */
    public BigDecimal getDeviceValueTotal() {
        return deviceValueTotal;
    }

    /**
     * SET 相关设备原值累计
     * @param deviceValueTotal
     */
    public void setDeviceValueTotal(BigDecimal deviceValueTotal) {
        this.deviceValueTotal = deviceValueTotal;
    }
    /**
     * GET 相关装备数量年度
     * @return equipValueYear
     */
    public BigDecimal getEquipValueYear() {
        return equipValueYear;
    }

    /**
     * SET 相关装备数量年度
     * @param equipValueYear
     */
    public void setEquipValueYear(BigDecimal equipValueYear) {
        this.equipValueYear = equipValueYear;
    }
    /**
     * GET 相关装备数量累计
     * @return equipValueTotal
     */
    public BigDecimal getEquipValueTotal() {
        return equipValueTotal;
    }

    /**
     * SET 相关装备数量累计
     * @param equipValueTotal
     */
    public void setEquipValueTotal(BigDecimal equipValueTotal) {
        this.equipValueTotal = equipValueTotal;
    }
    /**
     * GET 上年度投入
     * @return fundTotal
     */
    public BigDecimal getFundTotal() {
        return fundTotal;
    }

    /**
     * SET 上年度投入
     * @param fundTotal
     */
    public void setFundTotal(BigDecimal fundTotal) {
        this.fundTotal = fundTotal;
    }
    /**
     * GET 其他字段内容
     * @return otherFields
     */
    public String getOtherFields() {
        return otherFields;
    }

    /**
     * SET 其他字段内容
     * @param otherFields
     */
    public void setOtherFields(String otherFields) {
        this.otherFields = otherFields == null ? null : otherFields.trim();
    }
    /**
     * GET 备用字段内容
     * @return markFields
     */
    public String getMarkFields() {
        return markFields;
    }

    /**
     * SET 备用字段内容
     * @param markFields
     */
    public void setMarkFields(String markFields) {
        this.markFields = markFields == null ? null : markFields.trim();
    }
    /**
     * GET 
     * @return flowStatus
     */
    public String getFlowStatus() {
        return flowStatus;
    }

    /**
     * SET 
     * @param flowStatus
     */
    public void setFlowStatus(String flowStatus) {
        this.flowStatus = flowStatus == null ? null : flowStatus.trim();
    }
    /**
     * GET 
     * @return flowUser
     */
    public String getFlowUser() {
        return flowUser;
    }

    /**
     * SET 
     * @param flowUser
     */
    public void setFlowUser(String flowUser) {
        this.flowUser = flowUser == null ? null : flowUser.trim();
    }
    /**
     * GET 
     * @return flowUserName
     */
    public String getFlowUserName() {
        return flowUserName;
    }

    /**
     * SET 
     * @param flowUserName
     */
    public void setFlowUserName(String flowUserName) {
        this.flowUserName = flowUserName == null ? null : flowUserName.trim();
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}