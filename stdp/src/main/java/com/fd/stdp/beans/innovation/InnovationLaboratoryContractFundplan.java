package com.fd.stdp.beans.innovation;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fd.stdp.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import javax.persistence.*;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 *@Description: 重点实验室任务书经费计划
 *@Author: wangsh
 *@Date: 2022-02-09 16:46:17
 */
@Table(name = "INNOVATION_LABORATORY_CONTRACT_FUNDPLAN")
@ApiModel(value = "com.fd.stdp.beans.innovation.InnovationLaboratoryContractFundplan", description = "重点实验室任务书经费计划")
public class InnovationLaboratoryContractFundplan extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @Column(name = "CONTRACT_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="任务书id")
    private String contractId;
    
    @Column(name = "FUND")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="金额（万元）")
    private BigDecimal fund;
    
    @Column(name = "FUNDING_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @ApiModelProperty(value="拨款时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fundingTime;
    
    @Column(name = "FUND_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="计划类型")
    private String fundType;
    
    
    /**
     * GET 任务书id
     * @return contractId
     */
    public String getContractId() {
        return contractId;
    }

    /**
     * SET 任务书id
     * @param contractId
     */
    public void setContractId(String contractId) {
        this.contractId = contractId == null ? null : contractId.trim();
    }
    /**
     * GET 金额（万元）
     * @return fund
     */
    public BigDecimal getFund() {
        return fund;
    }

    /**
     * SET 金额（万元）
     * @param fund
     */
    public void setFund(BigDecimal fund) {
        this.fund = fund;
    }
    /**
     * GET 拨款时间
     * @return fundingTime
     */
    public Date getFundingTime() {
        return fundingTime;
    }

    /**
     * SET 拨款时间
     * @param fundingTime
     */
    public void setFundingTime(Date fundingTime) {
        this.fundingTime = fundingTime;
    }
    /**
     * GET 计划类型
     * @return fundType
     */
    public String getFundType() {
        return fundType;
    }

    /**
     * SET 计划类型
     * @param fundType
     */
    public void setFundType(String fundType) {
        this.fundType = fundType == null ? null : fundType.trim();
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}