package com.fd.stdp.beans.innovation;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fd.stdp.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import javax.persistence.*;
import java.math.BigDecimal;

/**
 *@Description: 省工程研究中心
 *@Author: wangsh
 *@Date: 2022-03-07 19:46:26
 */
@Table(name = "INNOVATION_PRIVINCE_ENGINE_APPLY")
@ApiModel(value = "com.fd.stdp.beans.innovation.InnovationPrivinceEngineApply", description = "省工程研究中心")
public class InnovationPrivinceEngineApply extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构code")
    private String orgCode;
    
    @Column(name = "ORG_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构名称")
    private String orgName;
    
    @Column(name = "CENTER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="工程研究中心名称")
    private String centerName;
    
    @Column(name = "RUNTIME_DATE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="批复时间")
    private String runtimeDate;
    
    @Column(name = "APPLY_UNIT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="依托单位")
    private String applyUnitName;
    
    @Column(name = "LEADER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="研究中心主任")
    private String leaderName;
    
    @Column(name = "INDUSTRY_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="行业领域")
    private String industryType;
    
    @Column(name = "FUND_TOTAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="科技经费支出")
    private BigDecimal fundTotal;
    
    @Column(name = "FUND_ORIGINAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="其中：固定资产购建费")
    private BigDecimal fundOriginal;
    
    @Column(name = "FUND_RESEARCH")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="其中：研究开发费")
    private BigDecimal fundResearch;
    
    @Column(name = "PERSON_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="总人数")
    private String personTotal;
    
    @Column(name = "PERSON_RESEARCH")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="研发人员数")
    private String personResearch;
    
    @Column(name = "PERSON_SUBJECT_LEADER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="学术与技术带头人数")
    private String personSubjectLeader;
    
    @Column(name = "PERSON_ACADEMICIAN")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="其中：院士人数")
    private String personAcademician;
    
    @Column(name = "PERSON_NATIONAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="其中：国家级人才")
    private String personNational;
    
    @Column(name = "PERSON_PROVINCE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="其中：省级人才数")
    private String personProvince;
    
    @Column(name = "DEVICE_VALUE_YEAR")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="科研仪器设备原值年度")
    private BigDecimal deviceValueYear;
    
    @Column(name = "DEVICE_VALUE_TOTAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="科研仪器设备原值累计")
    private BigDecimal deviceValueTotal;
    
    @Column(name = "RESEARCH_AREA_YEAR")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="科研场所面积年度")
    private BigDecimal researchAreaYear;
    
    @Column(name = "RESEARCH_AREA_TOTAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="科研场所面积累计")
    private BigDecimal researchAreaTotal;
    
    @Column(name = "PROJACT_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 全部在研项目数")
    private String projactTotal;
    
    @Column(name = "PROJACT_NATIONAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 其中国家级项目数")
    private String projactNational;
    
    @Column(name = "PROJACT_PROVINCE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 其中省部级项目数")
    private String projactProvince;
    
    @Column(name = "PROJACT_IMPORT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 新增对外合作项目数")
    private String projactImport;
    
    @Column(name = "THREE_YEAR_INCOME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 近三年总收入")
    private String threeYearIncome;
    
    @Column(name = "INVENTION_PATENT_AUTH")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 获得发明专利授权的数量")
    private String inventionPatentAuth;
    
    @Column(name = "UTILITY_MODEL_AUTH")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 获得实用新型专利授权的数量")
    private String utilityModelAuth;
    
    @Column(name = "OUTWORD_AUTH")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 获得外观设计专利授权的数量")
    private String outwordAuth;
    
    @Column(name = "NATIONAL_AWARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 获得的奖励数 国家级")
    private String nationalAward;
    
    @Column(name = "PROVINCE_AWARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 获得的奖励数 省级")
    private String provinceAward;
    
    @Column(name = "SOCIAL_AWARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 获得的奖励数 社会科学技术")
    private String socialAward;
    
    @Column(name = "SCI_PAPER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 国际权威期刊论文（TOP或SCI/SSCI、EI）")
    private String sciPaper;
    
    @Column(name = "CORE_PAPER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 国家核心期刊论文")
    private String corePaper;
    
    @Column(name = "NARMAL_PAPER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 专著（含软件著作）")
    private String narmalPaper;
    
    @Column(name = "AWARD_SELL_COUNT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 成果转化项数")
    private String awardSellCount;
    
    @Column(name = "AWARD_SELL_PRICE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 对行业直接经济效益")
    private String awardSellPrice;
    
    @Column(name = "AWARD_STANDARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="形成国家与行业标准数")
    private String awardStandard;
    
    @Column(name = "NATIONAL_STANDARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="国家标准")
    private String nationalStandard;
    
    @Column(name = "INDUSTRY_STANDARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="行业标准")
    private String industryStandard;
    
    @Column(name = "LOCAL_STANDARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="地方标准")
    private String localStandard;
    
    @Column(name = "GROUP_STANDARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="团体标准")
    private String groupStandard;
    
    @Column(name = "FLOW_STATUS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowStatus;
    
    @Column(name = "FLOW_USER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowUser;
    
    @Column(name = "FLOW_USER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowUserName;
    
    
    /**
     * GET 机构code
     * @return orgCode
     */
    public String getOrgCode() {
        return orgCode;
    }

    /**
     * SET 机构code
     * @param orgCode
     */
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }
    /**
     * GET 机构名称
     * @return orgName
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * SET 机构名称
     * @param orgName
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }
    /**
     * GET 工程研究中心名称
     * @return centerName
     */
    public String getCenterName() {
        return centerName;
    }

    /**
     * SET 工程研究中心名称
     * @param centerName
     */
    public void setCenterName(String centerName) {
        this.centerName = centerName == null ? null : centerName.trim();
    }
    /**
     * GET 批复时间
     * @return runtimeDate
     */
    public String getRuntimeDate() {
        return runtimeDate;
    }

    /**
     * SET 批复时间
     * @param runtimeDate
     */
    public void setRuntimeDate(String runtimeDate) {
        this.runtimeDate = runtimeDate == null ? null : runtimeDate.trim();
    }
    /**
     * GET 依托单位
     * @return applyUnitName
     */
    public String getApplyUnitName() {
        return applyUnitName;
    }

    /**
     * SET 依托单位
     * @param applyUnitName
     */
    public void setApplyUnitName(String applyUnitName) {
        this.applyUnitName = applyUnitName == null ? null : applyUnitName.trim();
    }
    /**
     * GET 研究中心主任
     * @return leaderName
     */
    public String getLeaderName() {
        return leaderName;
    }

    /**
     * SET 研究中心主任
     * @param leaderName
     */
    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName == null ? null : leaderName.trim();
    }
    /**
     * GET 行业领域
     * @return industryType
     */
    public String getIndustryType() {
        return industryType;
    }

    /**
     * SET 行业领域
     * @param industryType
     */
    public void setIndustryType(String industryType) {
        this.industryType = industryType == null ? null : industryType.trim();
    }
    /**
     * GET 科技经费支出
     * @return fundTotal
     */
    public BigDecimal getFundTotal() {
        return fundTotal;
    }

    /**
     * SET 科技经费支出
     * @param fundTotal
     */
    public void setFundTotal(BigDecimal fundTotal) {
        this.fundTotal = fundTotal;
    }
    /**
     * GET 其中：固定资产购建费
     * @return fundOriginal
     */
    public BigDecimal getFundOriginal() {
        return fundOriginal;
    }

    /**
     * SET 其中：固定资产购建费
     * @param fundOriginal
     */
    public void setFundOriginal(BigDecimal fundOriginal) {
        this.fundOriginal = fundOriginal;
    }
    /**
     * GET 其中：研究开发费
     * @return fundResearch
     */
    public BigDecimal getFundResearch() {
        return fundResearch;
    }

    /**
     * SET 其中：研究开发费
     * @param fundResearch
     */
    public void setFundResearch(BigDecimal fundResearch) {
        this.fundResearch = fundResearch;
    }
    /**
     * GET 总人数
     * @return personTotal
     */
    public String getPersonTotal() {
        return personTotal;
    }

    /**
     * SET 总人数
     * @param personTotal
     */
    public void setPersonTotal(String personTotal) {
        this.personTotal = personTotal == null ? null : personTotal.trim();
    }
    /**
     * GET 研发人员数
     * @return personResearch
     */
    public String getPersonResearch() {
        return personResearch;
    }

    /**
     * SET 研发人员数
     * @param personResearch
     */
    public void setPersonResearch(String personResearch) {
        this.personResearch = personResearch == null ? null : personResearch.trim();
    }
    /**
     * GET 学术与技术带头人数
     * @return personSubjectLeader
     */
    public String getPersonSubjectLeader() {
        return personSubjectLeader;
    }

    /**
     * SET 学术与技术带头人数
     * @param personSubjectLeader
     */
    public void setPersonSubjectLeader(String personSubjectLeader) {
        this.personSubjectLeader = personSubjectLeader == null ? null : personSubjectLeader.trim();
    }
    /**
     * GET 其中：院士人数
     * @return personAcademician
     */
    public String getPersonAcademician() {
        return personAcademician;
    }

    /**
     * SET 其中：院士人数
     * @param personAcademician
     */
    public void setPersonAcademician(String personAcademician) {
        this.personAcademician = personAcademician == null ? null : personAcademician.trim();
    }
    /**
     * GET 其中：国家级人才
     * @return personNational
     */
    public String getPersonNational() {
        return personNational;
    }

    /**
     * SET 其中：国家级人才
     * @param personNational
     */
    public void setPersonNational(String personNational) {
        this.personNational = personNational == null ? null : personNational.trim();
    }
    /**
     * GET 其中：省级人才数
     * @return personProvince
     */
    public String getPersonProvince() {
        return personProvince;
    }

    /**
     * SET 其中：省级人才数
     * @param personProvince
     */
    public void setPersonProvince(String personProvince) {
        this.personProvince = personProvince == null ? null : personProvince.trim();
    }
    /**
     * GET 科研仪器设备原值年度
     * @return deviceValueYear
     */
    public BigDecimal getDeviceValueYear() {
        return deviceValueYear;
    }

    /**
     * SET 科研仪器设备原值年度
     * @param deviceValueYear
     */
    public void setDeviceValueYear(BigDecimal deviceValueYear) {
        this.deviceValueYear = deviceValueYear;
    }
    /**
     * GET 科研仪器设备原值累计
     * @return deviceValueTotal
     */
    public BigDecimal getDeviceValueTotal() {
        return deviceValueTotal;
    }

    /**
     * SET 科研仪器设备原值累计
     * @param deviceValueTotal
     */
    public void setDeviceValueTotal(BigDecimal deviceValueTotal) {
        this.deviceValueTotal = deviceValueTotal;
    }
    /**
     * GET 科研场所面积年度
     * @return researchAreaYear
     */
    public BigDecimal getResearchAreaYear() {
        return researchAreaYear;
    }

    /**
     * SET 科研场所面积年度
     * @param researchAreaYear
     */
    public void setResearchAreaYear(BigDecimal researchAreaYear) {
        this.researchAreaYear = researchAreaYear;
    }
    /**
     * GET 科研场所面积累计
     * @return researchAreaTotal
     */
    public BigDecimal getResearchAreaTotal() {
        return researchAreaTotal;
    }

    /**
     * SET 科研场所面积累计
     * @param researchAreaTotal
     */
    public void setResearchAreaTotal(BigDecimal researchAreaTotal) {
        this.researchAreaTotal = researchAreaTotal;
    }
    /**
     * GET  全部在研项目数
     * @return projactTotal
     */
    public String getProjactTotal() {
        return projactTotal;
    }

    /**
     * SET  全部在研项目数
     * @param projactTotal
     */
    public void setProjactTotal(String projactTotal) {
        this.projactTotal = projactTotal == null ? null : projactTotal.trim();
    }
    /**
     * GET  其中国家级项目数
     * @return projactNational
     */
    public String getProjactNational() {
        return projactNational;
    }

    /**
     * SET  其中国家级项目数
     * @param projactNational
     */
    public void setProjactNational(String projactNational) {
        this.projactNational = projactNational == null ? null : projactNational.trim();
    }
    /**
     * GET  其中省部级项目数
     * @return projactProvince
     */
    public String getProjactProvince() {
        return projactProvince;
    }

    /**
     * SET  其中省部级项目数
     * @param projactProvince
     */
    public void setProjactProvince(String projactProvince) {
        this.projactProvince = projactProvince == null ? null : projactProvince.trim();
    }
    /**
     * GET  新增对外合作项目数
     * @return projactImport
     */
    public String getProjactImport() {
        return projactImport;
    }

    /**
     * SET  新增对外合作项目数
     * @param projactImport
     */
    public void setProjactImport(String projactImport) {
        this.projactImport = projactImport == null ? null : projactImport.trim();
    }
    /**
     * GET  近三年总收入
     * @return threeYearIncome
     */
    public String getThreeYearIncome() {
        return threeYearIncome;
    }

    /**
     * SET  近三年总收入
     * @param threeYearIncome
     */
    public void setThreeYearIncome(String threeYearIncome) {
        this.threeYearIncome = threeYearIncome == null ? null : threeYearIncome.trim();
    }
    /**
     * GET  获得发明专利授权的数量
     * @return inventionPatentAuth
     */
    public String getInventionPatentAuth() {
        return inventionPatentAuth;
    }

    /**
     * SET  获得发明专利授权的数量
     * @param inventionPatentAuth
     */
    public void setInventionPatentAuth(String inventionPatentAuth) {
        this.inventionPatentAuth = inventionPatentAuth == null ? null : inventionPatentAuth.trim();
    }
    /**
     * GET  获得实用新型专利授权的数量
     * @return utilityModelAuth
     */
    public String getUtilityModelAuth() {
        return utilityModelAuth;
    }

    /**
     * SET  获得实用新型专利授权的数量
     * @param utilityModelAuth
     */
    public void setUtilityModelAuth(String utilityModelAuth) {
        this.utilityModelAuth = utilityModelAuth == null ? null : utilityModelAuth.trim();
    }
    /**
     * GET  获得外观设计专利授权的数量
     * @return outwordAuth
     */
    public String getOutwordAuth() {
        return outwordAuth;
    }

    /**
     * SET  获得外观设计专利授权的数量
     * @param outwordAuth
     */
    public void setOutwordAuth(String outwordAuth) {
        this.outwordAuth = outwordAuth == null ? null : outwordAuth.trim();
    }
    /**
     * GET  获得的奖励数 国家级
     * @return nationalAward
     */
    public String getNationalAward() {
        return nationalAward;
    }

    /**
     * SET  获得的奖励数 国家级
     * @param nationalAward
     */
    public void setNationalAward(String nationalAward) {
        this.nationalAward = nationalAward == null ? null : nationalAward.trim();
    }
    /**
     * GET  获得的奖励数 省级
     * @return provinceAward
     */
    public String getProvinceAward() {
        return provinceAward;
    }

    /**
     * SET  获得的奖励数 省级
     * @param provinceAward
     */
    public void setProvinceAward(String provinceAward) {
        this.provinceAward = provinceAward == null ? null : provinceAward.trim();
    }
    /**
     * GET  获得的奖励数 社会科学技术
     * @return socialAward
     */
    public String getSocialAward() {
        return socialAward;
    }

    /**
     * SET  获得的奖励数 社会科学技术
     * @param socialAward
     */
    public void setSocialAward(String socialAward) {
        this.socialAward = socialAward == null ? null : socialAward.trim();
    }
    /**
     * GET  国际权威期刊论文（TOP或SCI/SSCI、EI）
     * @return sciPaper
     */
    public String getSciPaper() {
        return sciPaper;
    }

    /**
     * SET  国际权威期刊论文（TOP或SCI/SSCI、EI）
     * @param sciPaper
     */
    public void setSciPaper(String sciPaper) {
        this.sciPaper = sciPaper == null ? null : sciPaper.trim();
    }
    /**
     * GET  国家核心期刊论文
     * @return corePaper
     */
    public String getCorePaper() {
        return corePaper;
    }

    /**
     * SET  国家核心期刊论文
     * @param corePaper
     */
    public void setCorePaper(String corePaper) {
        this.corePaper = corePaper == null ? null : corePaper.trim();
    }
    /**
     * GET  专著（含软件著作）
     * @return narmalPaper
     */
    public String getNarmalPaper() {
        return narmalPaper;
    }

    /**
     * SET  专著（含软件著作）
     * @param narmalPaper
     */
    public void setNarmalPaper(String narmalPaper) {
        this.narmalPaper = narmalPaper == null ? null : narmalPaper.trim();
    }
    /**
     * GET  成果转化项数
     * @return awardSellCount
     */
    public String getAwardSellCount() {
        return awardSellCount;
    }

    /**
     * SET  成果转化项数
     * @param awardSellCount
     */
    public void setAwardSellCount(String awardSellCount) {
        this.awardSellCount = awardSellCount == null ? null : awardSellCount.trim();
    }
    /**
     * GET  对行业直接经济效益
     * @return awardSellPrice
     */
    public String getAwardSellPrice() {
        return awardSellPrice;
    }

    /**
     * SET  对行业直接经济效益
     * @param awardSellPrice
     */
    public void setAwardSellPrice(String awardSellPrice) {
        this.awardSellPrice = awardSellPrice == null ? null : awardSellPrice.trim();
    }
    /**
     * GET 形成国家与行业标准数
     * @return awardStandard
     */
    public String getAwardStandard() {
        return awardStandard;
    }

    /**
     * SET 形成国家与行业标准数
     * @param awardStandard
     */
    public void setAwardStandard(String awardStandard) {
        this.awardStandard = awardStandard == null ? null : awardStandard.trim();
    }
    /**
     * GET 国家标准
     * @return nationalStandard
     */
    public String getNationalStandard() {
        return nationalStandard;
    }

    /**
     * SET 国家标准
     * @param nationalStandard
     */
    public void setNationalStandard(String nationalStandard) {
        this.nationalStandard = nationalStandard == null ? null : nationalStandard.trim();
    }
    /**
     * GET 行业标准
     * @return industryStandard
     */
    public String getIndustryStandard() {
        return industryStandard;
    }

    /**
     * SET 行业标准
     * @param industryStandard
     */
    public void setIndustryStandard(String industryStandard) {
        this.industryStandard = industryStandard == null ? null : industryStandard.trim();
    }
    /**
     * GET 地方标准
     * @return localStandard
     */
    public String getLocalStandard() {
        return localStandard;
    }

    /**
     * SET 地方标准
     * @param localStandard
     */
    public void setLocalStandard(String localStandard) {
        this.localStandard = localStandard == null ? null : localStandard.trim();
    }
    /**
     * GET 团体标准
     * @return groupStandard
     */
    public String getGroupStandard() {
        return groupStandard;
    }

    /**
     * SET 团体标准
     * @param groupStandard
     */
    public void setGroupStandard(String groupStandard) {
        this.groupStandard = groupStandard == null ? null : groupStandard.trim();
    }
    /**
     * GET 
     * @return flowStatus
     */
    public String getFlowStatus() {
        return flowStatus;
    }

    /**
     * SET 
     * @param flowStatus
     */
    public void setFlowStatus(String flowStatus) {
        this.flowStatus = flowStatus == null ? null : flowStatus.trim();
    }
    /**
     * GET 
     * @return flowUser
     */
    public String getFlowUser() {
        return flowUser;
    }

    /**
     * SET 
     * @param flowUser
     */
    public void setFlowUser(String flowUser) {
        this.flowUser = flowUser == null ? null : flowUser.trim();
    }
    /**
     * GET 
     * @return flowUserName
     */
    public String getFlowUserName() {
        return flowUserName;
    }

    /**
     * SET 
     * @param flowUserName
     */
    public void setFlowUserName(String flowUserName) {
        this.flowUserName = flowUserName == null ? null : flowUserName.trim();
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}