package com.fd.stdp.beans.innovation;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fd.stdp.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import javax.persistence.*;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 *@Description: 重点实验室培育申报任务书
 *@Author: wangsh
 *@Date: 2022-05-11 15:07:05
 */
@Table(name = "INNOVATION_LABORATORY_CONTRACT")
@ApiModel(value = "com.fd.stdp.beans.innovation.InnovationLaboratoryContract", description = "重点实验室培育申报任务书")
public class InnovationLaboratoryContract extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构code")
    private String orgCode;
    
    @Column(name = "ORG_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构名称")
    private String orgName;
    
    @Column(name = "APPLY_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String applyId;
    
    @Column(name = "LABORATORY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="实验室名称")
    private String laboratory;
    
    @Column(name = "ENTRUST_UNIT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="下达单位(甲方)")
    private String entrustUnitName;
    
    @Column(name = "MANAGE_UNIT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="主管市局(乙方)")
    private String manageUnitName;
    
    @Column(name = "APPLY_UNIT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="依托单位(丙方)")
    private String applyUnitName;
    
    @Column(name = "END_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @ApiModelProperty(value="任务书期限")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM")
    private Date endTime;

    @Column(name = "RESEARCH_TYPE_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="研究领域")
    private String researchTypeCode;
    
    @Column(name = "RESEARCH_TYPE_TEXT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="研究领域")
    private String researchTypeText;
    
    @Column(name = "RESEARCH_TYPE_TEXT_ELSE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="研究领域其它")
    private String researchTypeTextElse;
    
    @Column(name = "RESEARCH_DIRECTION")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="研究方向")
    private String researchDirection;
    
    @Column(name = "LEADER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="重点实验室主任")
    private String leaderName;
    
    @Column(name = "LEADER_UNIT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="单位")
    private String leaderUnitName;
    
    @Column(name = "LEADER_BIRTHDAY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="出生年月")
    private String leaderBirthday;
    
    @Column(name = "LEADER_SEX")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="性别")
    private String leaderSex;
    
    @Column(name = "LEADER_TITAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="职称")
    private String leaderTital;
    
    @Column(name = "LEADER_DEGREE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="学历学位")
    private String leaderDegree;
    
    @Column(name = "LEADER_TEL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="联系电话")
    private String leaderTel;
    
    @Column(name = "LEADER_EMAIL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="电子邮箱")
    private String leaderEmail;
    
    @Column(name = "APPLY_UNIT_USCC")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="统一社会信用代码/组织机构代码")
    private String applyUnitUscc;
    
    @Column(name = "APPLY_UNIT_LEADER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="单位负责人")
    private String applyUnitLeaderName;
    
    @Column(name = "APPLY_UNIT_LEADER_TITAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="职务/职称")
    private String applyUnitLeaderTital;
    
    @Column(name = "APPLY_UNIT_LINKED_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="联系人")
    private String applyUnitLinkedName;
    
    @Column(name = "APPLY_UNIT_LINKED_TITAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="职务/职称")
    private String applyUnitLinkedTital;
    
    @Column(name = "APPLY_UNIT_TEL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="联系电话")
    private String applyUnitTel;
    
    @Column(name = "APPLY_UNIT_EMAIL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="电子邮箱")
    private String applyUnitEmail;
    
    @Column(name = "APPLY_UNIT_POSTCODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="邮    编")
    private String applyUnitPostcode;
    
    @Column(name = "APPLY_UNIT_ADDRESS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="通讯地址")
    private String applyUnitAddress;
    
    @Column(name = "CONTENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="国内外概况")
    private String content;
    
    @Column(name = "WORK_BASIS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="现有研究工作基础、水平等")
    private String workBasis;
    
    @Column(name = "EXPERIMENTAL_CONDITION")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="已具备的实验条件")
    private String experimentalCondition;
    
    @Column(name = "RESEARCH_TARGET")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="重点实验室研究方向和主要研究内容")
    private String researchTarget;
    
    @Column(name = "WORK_PLANNING")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="主要工作规划、预期目标和水平")
    private String workPlanning;
    
    @Column(name = "TEAM_ABILITY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="科研队伍状况及人才培养能力")
    private String teamAbility;
    
    @Column(name = "TOTAL_FUNDS")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="责任期投入总经费")
    private BigDecimal totalFunds;
    
    @Column(name = "PROVINCE_INPUT")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="甲方投入 万元")
    private BigDecimal provinceInput;
    
    @Column(name = "CITY_INPUT")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="市县、部门配套     万元")
    private BigDecimal cityInput;
    
    @Column(name = "SELEF_RAISED")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="丙方自筹     万元")
    private BigDecimal selefRaised;
    
    @Column(name = "PROVINCE_FUND_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="甲方拨款合计")
    private String provinceFundTotal;
    
    @Column(name = "PROVINCE_FUND_TIME1")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="甲方拨款时间1")
    private String provinceFundTime1;
    
    @Column(name = "PROVINCE_FUND_TIME2")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="甲方拨款时间2")
    private String provinceFundTime2;
    
    @Column(name = "PROVINCE_FUND_TIME3")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="甲方拨款时间3")
    private String provinceFundTime3;
    
    @Column(name = "PROVINCE_FUND_MONEY1")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="甲方拨款金额1")
    private String provinceFundMoney1;
    
    @Column(name = "PROVINCE_FUND_MONEY2")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="甲方拨款金额2")
    private String provinceFundMoney2;
    
    @Column(name = "PROVINCE_FUND_MONEY3")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="甲方拨款金额3")
    private String provinceFundMoney3;
    
    @Column(name = "CITY_FUND_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="乙方拨款合计")
    private String cityFundTotal;
    
    @Column(name = "CITY_FUND_TIME1")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="乙方拨款时间1")
    private String cityFundTime1;
    
    @Column(name = "CITY_FUND_TIME2")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="乙方拨款时间2")
    private String cityFundTime2;
    
    @Column(name = "CITY_FUND_TIME3")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="乙方拨款时间3")
    private String cityFundTime3;
    
    @Column(name = "CITY_FUND_MONEY1")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="乙方拨款金额1")
    private String cityFundMoney1;
    
    @Column(name = "CITY_FUND_MONEY2")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="乙方拨款金额2")
    private String cityFundMoney2;
    
    @Column(name = "CITY_FUND_MONEY3")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="乙方拨款金额3")
    private String cityFundMoney3;
    
    @Column(name = "SELF_FUND_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="丙方拨款合计")
    private String selfFundTotal;
    
    @Column(name = "SELF_FUND_TIME1")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="丙方拨款时间1")
    private String selfFundTime1;
    
    @Column(name = "SELF_FUND_TIME2")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="丙方拨款时间2")
    private String selfFundTime2;
    
    @Column(name = "SELF_FUND_TIME3")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="丙方拨款时间3")
    private String selfFundTime3;
    
    @Column(name = "SELF_FUND_MONEY1")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="丙方拨款金额1")
    private String selfFundMoney1;
    
    @Column(name = "SELF_FUND_MONEY2")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="丙方拨款金额2")
    private String selfFundMoney2;
    
    @Column(name = "SELF_FUND_MONEY3")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="丙方拨款金额3")
    private String selfFundMoney3;
    
    @Column(name = "APPLY_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="申请类型：培育申报/直接命名申报/培育评估/验收申请")
    private String applyType;
    
    @Column(name = "RESULT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String result;
    
    @Column(name = "OPINION")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String opinion;
    
    @Column(name = "FORM_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String formType;
    
    @Column(name = "FLOW_STATUS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowStatus;
    
    @Column(name = "FLOW_USER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowUser;
    
    @Column(name = "FLOW_USER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowUserName;
    
    @Column(name = "START_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @ApiModelProperty(value="")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM")
    private Date startTime;


    /**
     * GET 机构code
     * @return orgCode
     */
    public String getOrgCode() {
        return orgCode;
    }

    /**
     * SET 机构code
     * @param orgCode
     */
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }
    /**
     * GET 机构名称
     * @return orgName
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * SET 机构名称
     * @param orgName
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }
    /**
     * GET 
     * @return applyId
     */
    public String getApplyId() {
        return applyId;
    }

    /**
     * SET 
     * @param applyId
     */
    public void setApplyId(String applyId) {
        this.applyId = applyId == null ? null : applyId.trim();
    }
    /**
     * GET 实验室名称
     * @return laboratory
     */
    public String getLaboratory() {
        return laboratory;
    }

    /**
     * SET 实验室名称
     * @param laboratory
     */
    public void setLaboratory(String laboratory) {
        this.laboratory = laboratory == null ? null : laboratory.trim();
    }
    /**
     * GET 下达单位(甲方)
     * @return entrustUnitName
     */
    public String getEntrustUnitName() {
        return entrustUnitName;
    }

    /**
     * SET 下达单位(甲方)
     * @param entrustUnitName
     */
    public void setEntrustUnitName(String entrustUnitName) {
        this.entrustUnitName = entrustUnitName == null ? null : entrustUnitName.trim();
    }
    /**
     * GET 主管市局(乙方)
     * @return manageUnitName
     */
    public String getManageUnitName() {
        return manageUnitName;
    }

    /**
     * SET 主管市局(乙方)
     * @param manageUnitName
     */
    public void setManageUnitName(String manageUnitName) {
        this.manageUnitName = manageUnitName == null ? null : manageUnitName.trim();
    }
    /**
     * GET 依托单位(丙方)
     * @return applyUnitName
     */
    public String getApplyUnitName() {
        return applyUnitName;
    }

    /**
     * SET 依托单位(丙方)
     * @param applyUnitName
     */
    public void setApplyUnitName(String applyUnitName) {
        this.applyUnitName = applyUnitName == null ? null : applyUnitName.trim();
    }
    /**
     * GET 任务书期限
     * @return endTime
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * SET 任务书期限
     * @param endTime
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    /**
     * GET 研究领域
     * @return researchTypeCode
     */
    public String getResearchTypeCode() {
        return researchTypeCode;
    }

    /**
     * SET 研究领域
     * @param researchTypeCode
     */
    public void setResearchTypeCode(String researchTypeCode) {
        this.researchTypeCode = researchTypeCode == null ? null : researchTypeCode.trim();
    }
    /**
     * GET 研究领域
     * @return researchTypeText
     */
    public String getResearchTypeText() {
        return researchTypeText;
    }

    /**
     * SET 研究领域
     * @param researchTypeText
     */
    public void setResearchTypeText(String researchTypeText) {
        this.researchTypeText = researchTypeText == null ? null : researchTypeText.trim();
    }
    /**
     * GET 研究领域其它
     * @return researchTypeTextElse
     */
    public String getResearchTypeTextElse() {
        return researchTypeTextElse;
    }

    /**
     * SET 研究领域其它
     * @param researchTypeTextElse
     */
    public void setResearchTypeTextElse(String researchTypeTextElse) {
        this.researchTypeTextElse = researchTypeTextElse == null ? null : researchTypeTextElse.trim();
    }
    /**
     * GET 研究方向
     * @return researchDirection
     */
    public String getResearchDirection() {
        return researchDirection;
    }

    /**
     * SET 研究方向
     * @param researchDirection
     */
    public void setResearchDirection(String researchDirection) {
        this.researchDirection = researchDirection == null ? null : researchDirection.trim();
    }
    /**
     * GET 重点实验室主任
     * @return leaderName
     */
    public String getLeaderName() {
        return leaderName;
    }

    /**
     * SET 重点实验室主任
     * @param leaderName
     */
    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName == null ? null : leaderName.trim();
    }
    /**
     * GET 单位
     * @return leaderUnitName
     */
    public String getLeaderUnitName() {
        return leaderUnitName;
    }

    /**
     * SET 单位
     * @param leaderUnitName
     */
    public void setLeaderUnitName(String leaderUnitName) {
        this.leaderUnitName = leaderUnitName == null ? null : leaderUnitName.trim();
    }
    /**
     * GET 出生年月
     * @return leaderBirthday
     */
    public String getLeaderBirthday() {
        return leaderBirthday;
    }

    /**
     * SET 出生年月
     * @param leaderBirthday
     */
    public void setLeaderBirthday(String leaderBirthday) {
        this.leaderBirthday = leaderBirthday == null ? null : leaderBirthday.trim();
    }
    /**
     * GET 性别
     * @return leaderSex
     */
    public String getLeaderSex() {
        return leaderSex;
    }

    /**
     * SET 性别
     * @param leaderSex
     */
    public void setLeaderSex(String leaderSex) {
        this.leaderSex = leaderSex == null ? null : leaderSex.trim();
    }
    /**
     * GET 职称
     * @return leaderTital
     */
    public String getLeaderTital() {
        return leaderTital;
    }

    /**
     * SET 职称
     * @param leaderTital
     */
    public void setLeaderTital(String leaderTital) {
        this.leaderTital = leaderTital == null ? null : leaderTital.trim();
    }
    /**
     * GET 学历学位
     * @return leaderDegree
     */
    public String getLeaderDegree() {
        return leaderDegree;
    }

    /**
     * SET 学历学位
     * @param leaderDegree
     */
    public void setLeaderDegree(String leaderDegree) {
        this.leaderDegree = leaderDegree == null ? null : leaderDegree.trim();
    }
    /**
     * GET 联系电话
     * @return leaderTel
     */
    public String getLeaderTel() {
        return leaderTel;
    }

    /**
     * SET 联系电话
     * @param leaderTel
     */
    public void setLeaderTel(String leaderTel) {
        this.leaderTel = leaderTel == null ? null : leaderTel.trim();
    }
    /**
     * GET 电子邮箱
     * @return leaderEmail
     */
    public String getLeaderEmail() {
        return leaderEmail;
    }

    /**
     * SET 电子邮箱
     * @param leaderEmail
     */
    public void setLeaderEmail(String leaderEmail) {
        this.leaderEmail = leaderEmail == null ? null : leaderEmail.trim();
    }
    /**
     * GET 统一社会信用代码/组织机构代码
     * @return applyUnitUscc
     */
    public String getApplyUnitUscc() {
        return applyUnitUscc;
    }

    /**
     * SET 统一社会信用代码/组织机构代码
     * @param applyUnitUscc
     */
    public void setApplyUnitUscc(String applyUnitUscc) {
        this.applyUnitUscc = applyUnitUscc == null ? null : applyUnitUscc.trim();
    }
    /**
     * GET 单位负责人
     * @return applyUnitLeaderName
     */
    public String getApplyUnitLeaderName() {
        return applyUnitLeaderName;
    }

    /**
     * SET 单位负责人
     * @param applyUnitLeaderName
     */
    public void setApplyUnitLeaderName(String applyUnitLeaderName) {
        this.applyUnitLeaderName = applyUnitLeaderName == null ? null : applyUnitLeaderName.trim();
    }
    /**
     * GET 职务/职称
     * @return applyUnitLeaderTital
     */
    public String getApplyUnitLeaderTital() {
        return applyUnitLeaderTital;
    }

    /**
     * SET 职务/职称
     * @param applyUnitLeaderTital
     */
    public void setApplyUnitLeaderTital(String applyUnitLeaderTital) {
        this.applyUnitLeaderTital = applyUnitLeaderTital == null ? null : applyUnitLeaderTital.trim();
    }
    /**
     * GET 联系人
     * @return applyUnitLinkedName
     */
    public String getApplyUnitLinkedName() {
        return applyUnitLinkedName;
    }

    /**
     * SET 联系人
     * @param applyUnitLinkedName
     */
    public void setApplyUnitLinkedName(String applyUnitLinkedName) {
        this.applyUnitLinkedName = applyUnitLinkedName == null ? null : applyUnitLinkedName.trim();
    }
    /**
     * GET 职务/职称
     * @return applyUnitLinkedTital
     */
    public String getApplyUnitLinkedTital() {
        return applyUnitLinkedTital;
    }

    /**
     * SET 职务/职称
     * @param applyUnitLinkedTital
     */
    public void setApplyUnitLinkedTital(String applyUnitLinkedTital) {
        this.applyUnitLinkedTital = applyUnitLinkedTital == null ? null : applyUnitLinkedTital.trim();
    }
    /**
     * GET 联系电话
     * @return applyUnitTel
     */
    public String getApplyUnitTel() {
        return applyUnitTel;
    }

    /**
     * SET 联系电话
     * @param applyUnitTel
     */
    public void setApplyUnitTel(String applyUnitTel) {
        this.applyUnitTel = applyUnitTel == null ? null : applyUnitTel.trim();
    }
    /**
     * GET 电子邮箱
     * @return applyUnitEmail
     */
    public String getApplyUnitEmail() {
        return applyUnitEmail;
    }

    /**
     * SET 电子邮箱
     * @param applyUnitEmail
     */
    public void setApplyUnitEmail(String applyUnitEmail) {
        this.applyUnitEmail = applyUnitEmail == null ? null : applyUnitEmail.trim();
    }
    /**
     * GET 邮    编
     * @return applyUnitPostcode
     */
    public String getApplyUnitPostcode() {
        return applyUnitPostcode;
    }

    /**
     * SET 邮    编
     * @param applyUnitPostcode
     */
    public void setApplyUnitPostcode(String applyUnitPostcode) {
        this.applyUnitPostcode = applyUnitPostcode == null ? null : applyUnitPostcode.trim();
    }
    /**
     * GET 通讯地址
     * @return applyUnitAddress
     */
    public String getApplyUnitAddress() {
        return applyUnitAddress;
    }

    /**
     * SET 通讯地址
     * @param applyUnitAddress
     */
    public void setApplyUnitAddress(String applyUnitAddress) {
        this.applyUnitAddress = applyUnitAddress == null ? null : applyUnitAddress.trim();
    }
    /**
     * GET 国内外概况
     * @return content
     */
    public String getContent() {
        return content;
    }

    /**
     * SET 国内外概况
     * @param content
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }
    /**
     * GET 现有研究工作基础、水平等
     * @return workBasis
     */
    public String getWorkBasis() {
        return workBasis;
    }

    /**
     * SET 现有研究工作基础、水平等
     * @param workBasis
     */
    public void setWorkBasis(String workBasis) {
        this.workBasis = workBasis == null ? null : workBasis.trim();
    }
    /**
     * GET 已具备的实验条件
     * @return experimentalCondition
     */
    public String getExperimentalCondition() {
        return experimentalCondition;
    }

    /**
     * SET 已具备的实验条件
     * @param experimentalCondition
     */
    public void setExperimentalCondition(String experimentalCondition) {
        this.experimentalCondition = experimentalCondition == null ? null : experimentalCondition.trim();
    }
    /**
     * GET 重点实验室研究方向和主要研究内容
     * @return researchTarget
     */
    public String getResearchTarget() {
        return researchTarget;
    }

    /**
     * SET 重点实验室研究方向和主要研究内容
     * @param researchTarget
     */
    public void setResearchTarget(String researchTarget) {
        this.researchTarget = researchTarget == null ? null : researchTarget.trim();
    }
    /**
     * GET 主要工作规划、预期目标和水平
     * @return workPlanning
     */
    public String getWorkPlanning() {
        return workPlanning;
    }

    /**
     * SET 主要工作规划、预期目标和水平
     * @param workPlanning
     */
    public void setWorkPlanning(String workPlanning) {
        this.workPlanning = workPlanning == null ? null : workPlanning.trim();
    }
    /**
     * GET 科研队伍状况及人才培养能力
     * @return teamAbility
     */
    public String getTeamAbility() {
        return teamAbility;
    }

    /**
     * SET 科研队伍状况及人才培养能力
     * @param teamAbility
     */
    public void setTeamAbility(String teamAbility) {
        this.teamAbility = teamAbility == null ? null : teamAbility.trim();
    }
    /**
     * GET 责任期投入总经费
     * @return totalFunds
     */
    public BigDecimal getTotalFunds() {
        return totalFunds;
    }

    /**
     * SET 责任期投入总经费
     * @param totalFunds
     */
    public void setTotalFunds(BigDecimal totalFunds) {
        this.totalFunds = totalFunds;
    }
    /**
     * GET 甲方投入 万元
     * @return provinceInput
     */
    public BigDecimal getProvinceInput() {
        return provinceInput;
    }

    /**
     * SET 甲方投入 万元
     * @param provinceInput
     */
    public void setProvinceInput(BigDecimal provinceInput) {
        this.provinceInput = provinceInput;
    }
    /**
     * GET 市县、部门配套     万元
     * @return cityInput
     */
    public BigDecimal getCityInput() {
        return cityInput;
    }

    /**
     * SET 市县、部门配套     万元
     * @param cityInput
     */
    public void setCityInput(BigDecimal cityInput) {
        this.cityInput = cityInput;
    }
    /**
     * GET 丙方自筹     万元
     * @return selefRaised
     */
    public BigDecimal getSelefRaised() {
        return selefRaised;
    }

    /**
     * SET 丙方自筹     万元
     * @param selefRaised
     */
    public void setSelefRaised(BigDecimal selefRaised) {
        this.selefRaised = selefRaised;
    }
    /**
     * GET 甲方拨款合计
     * @return provinceFundTotal
     */
    public String getProvinceFundTotal() {
        return provinceFundTotal;
    }

    /**
     * SET 甲方拨款合计
     * @param provinceFundTotal
     */
    public void setProvinceFundTotal(String provinceFundTotal) {
        this.provinceFundTotal = provinceFundTotal == null ? null : provinceFundTotal.trim();
    }
    /**
     * GET 甲方拨款时间1
     * @return provinceFundTime1
     */
    public String getProvinceFundTime1() {
        return provinceFundTime1;
    }

    /**
     * SET 甲方拨款时间1
     * @param provinceFundTime1
     */
    public void setProvinceFundTime1(String provinceFundTime1) {
        this.provinceFundTime1 = provinceFundTime1 == null ? null : provinceFundTime1.trim();
    }
    /**
     * GET 甲方拨款时间2
     * @return provinceFundTime2
     */
    public String getProvinceFundTime2() {
        return provinceFundTime2;
    }

    /**
     * SET 甲方拨款时间2
     * @param provinceFundTime2
     */
    public void setProvinceFundTime2(String provinceFundTime2) {
        this.provinceFundTime2 = provinceFundTime2 == null ? null : provinceFundTime2.trim();
    }
    /**
     * GET 甲方拨款时间3
     * @return provinceFundTime3
     */
    public String getProvinceFundTime3() {
        return provinceFundTime3;
    }

    /**
     * SET 甲方拨款时间3
     * @param provinceFundTime3
     */
    public void setProvinceFundTime3(String provinceFundTime3) {
        this.provinceFundTime3 = provinceFundTime3 == null ? null : provinceFundTime3.trim();
    }
    /**
     * GET 甲方拨款金额1
     * @return provinceFundMoney1
     */
    public String getProvinceFundMoney1() {
        return provinceFundMoney1;
    }

    /**
     * SET 甲方拨款金额1
     * @param provinceFundMoney1
     */
    public void setProvinceFundMoney1(String provinceFundMoney1) {
        this.provinceFundMoney1 = provinceFundMoney1 == null ? null : provinceFundMoney1.trim();
    }
    /**
     * GET 甲方拨款金额2
     * @return provinceFundMoney2
     */
    public String getProvinceFundMoney2() {
        return provinceFundMoney2;
    }

    /**
     * SET 甲方拨款金额2
     * @param provinceFundMoney2
     */
    public void setProvinceFundMoney2(String provinceFundMoney2) {
        this.provinceFundMoney2 = provinceFundMoney2 == null ? null : provinceFundMoney2.trim();
    }
    /**
     * GET 甲方拨款金额3
     * @return provinceFundMoney3
     */
    public String getProvinceFundMoney3() {
        return provinceFundMoney3;
    }

    /**
     * SET 甲方拨款金额3
     * @param provinceFundMoney3
     */
    public void setProvinceFundMoney3(String provinceFundMoney3) {
        this.provinceFundMoney3 = provinceFundMoney3 == null ? null : provinceFundMoney3.trim();
    }
    /**
     * GET 乙方拨款合计
     * @return cityFundTotal
     */
    public String getCityFundTotal() {
        return cityFundTotal;
    }

    /**
     * SET 乙方拨款合计
     * @param cityFundTotal
     */
    public void setCityFundTotal(String cityFundTotal) {
        this.cityFundTotal = cityFundTotal == null ? null : cityFundTotal.trim();
    }
    /**
     * GET 乙方拨款时间1
     * @return cityFundTime1
     */
    public String getCityFundTime1() {
        return cityFundTime1;
    }

    /**
     * SET 乙方拨款时间1
     * @param cityFundTime1
     */
    public void setCityFundTime1(String cityFundTime1) {
        this.cityFundTime1 = cityFundTime1 == null ? null : cityFundTime1.trim();
    }
    /**
     * GET 乙方拨款时间2
     * @return cityFundTime2
     */
    public String getCityFundTime2() {
        return cityFundTime2;
    }

    /**
     * SET 乙方拨款时间2
     * @param cityFundTime2
     */
    public void setCityFundTime2(String cityFundTime2) {
        this.cityFundTime2 = cityFundTime2 == null ? null : cityFundTime2.trim();
    }
    /**
     * GET 乙方拨款时间3
     * @return cityFundTime3
     */
    public String getCityFundTime3() {
        return cityFundTime3;
    }

    /**
     * SET 乙方拨款时间3
     * @param cityFundTime3
     */
    public void setCityFundTime3(String cityFundTime3) {
        this.cityFundTime3 = cityFundTime3 == null ? null : cityFundTime3.trim();
    }
    /**
     * GET 乙方拨款金额1
     * @return cityFundMoney1
     */
    public String getCityFundMoney1() {
        return cityFundMoney1;
    }

    /**
     * SET 乙方拨款金额1
     * @param cityFundMoney1
     */
    public void setCityFundMoney1(String cityFundMoney1) {
        this.cityFundMoney1 = cityFundMoney1 == null ? null : cityFundMoney1.trim();
    }
    /**
     * GET 乙方拨款金额2
     * @return cityFundMoney2
     */
    public String getCityFundMoney2() {
        return cityFundMoney2;
    }

    /**
     * SET 乙方拨款金额2
     * @param cityFundMoney2
     */
    public void setCityFundMoney2(String cityFundMoney2) {
        this.cityFundMoney2 = cityFundMoney2 == null ? null : cityFundMoney2.trim();
    }
    /**
     * GET 乙方拨款金额3
     * @return cityFundMoney3
     */
    public String getCityFundMoney3() {
        return cityFundMoney3;
    }

    /**
     * SET 乙方拨款金额3
     * @param cityFundMoney3
     */
    public void setCityFundMoney3(String cityFundMoney3) {
        this.cityFundMoney3 = cityFundMoney3 == null ? null : cityFundMoney3.trim();
    }
    /**
     * GET 丙方拨款合计
     * @return selfFundTotal
     */
    public String getSelfFundTotal() {
        return selfFundTotal;
    }

    /**
     * SET 丙方拨款合计
     * @param selfFundTotal
     */
    public void setSelfFundTotal(String selfFundTotal) {
        this.selfFundTotal = selfFundTotal == null ? null : selfFundTotal.trim();
    }
    /**
     * GET 丙方拨款时间1
     * @return selfFundTime1
     */
    public String getSelfFundTime1() {
        return selfFundTime1;
    }

    /**
     * SET 丙方拨款时间1
     * @param selfFundTime1
     */
    public void setSelfFundTime1(String selfFundTime1) {
        this.selfFundTime1 = selfFundTime1 == null ? null : selfFundTime1.trim();
    }
    /**
     * GET 丙方拨款时间2
     * @return selfFundTime2
     */
    public String getSelfFundTime2() {
        return selfFundTime2;
    }

    /**
     * SET 丙方拨款时间2
     * @param selfFundTime2
     */
    public void setSelfFundTime2(String selfFundTime2) {
        this.selfFundTime2 = selfFundTime2 == null ? null : selfFundTime2.trim();
    }
    /**
     * GET 丙方拨款时间3
     * @return selfFundTime3
     */
    public String getSelfFundTime3() {
        return selfFundTime3;
    }

    /**
     * SET 丙方拨款时间3
     * @param selfFundTime3
     */
    public void setSelfFundTime3(String selfFundTime3) {
        this.selfFundTime3 = selfFundTime3 == null ? null : selfFundTime3.trim();
    }
    /**
     * GET 丙方拨款金额1
     * @return selfFundMoney1
     */
    public String getSelfFundMoney1() {
        return selfFundMoney1;
    }

    /**
     * SET 丙方拨款金额1
     * @param selfFundMoney1
     */
    public void setSelfFundMoney1(String selfFundMoney1) {
        this.selfFundMoney1 = selfFundMoney1 == null ? null : selfFundMoney1.trim();
    }
    /**
     * GET 丙方拨款金额2
     * @return selfFundMoney2
     */
    public String getSelfFundMoney2() {
        return selfFundMoney2;
    }

    /**
     * SET 丙方拨款金额2
     * @param selfFundMoney2
     */
    public void setSelfFundMoney2(String selfFundMoney2) {
        this.selfFundMoney2 = selfFundMoney2 == null ? null : selfFundMoney2.trim();
    }
    /**
     * GET 丙方拨款金额3
     * @return selfFundMoney3
     */
    public String getSelfFundMoney3() {
        return selfFundMoney3;
    }

    /**
     * SET 丙方拨款金额3
     * @param selfFundMoney3
     */
    public void setSelfFundMoney3(String selfFundMoney3) {
        this.selfFundMoney3 = selfFundMoney3 == null ? null : selfFundMoney3.trim();
    }
    /**
     * GET 申请类型：培育申报/直接命名申报/培育评估/验收申请
     * @return applyType
     */
    public String getApplyType() {
        return applyType;
    }

    /**
     * SET 申请类型：培育申报/直接命名申报/培育评估/验收申请
     * @param applyType
     */
    public void setApplyType(String applyType) {
        this.applyType = applyType == null ? null : applyType.trim();
    }
    /**
     * GET 
     * @return result
     */
    public String getResult() {
        return result;
    }

    /**
     * SET 
     * @param result
     */
    public void setResult(String result) {
        this.result = result == null ? null : result.trim();
    }
    /**
     * GET 
     * @return opinion
     */
    public String getOpinion() {
        return opinion;
    }

    /**
     * SET 
     * @param opinion
     */
    public void setOpinion(String opinion) {
        this.opinion = opinion == null ? null : opinion.trim();
    }
    /**
     * GET 
     * @return formType
     */
    public String getFormType() {
        return formType;
    }

    /**
     * SET 
     * @param formType
     */
    public void setFormType(String formType) {
        this.formType = formType == null ? null : formType.trim();
    }
    /**
     * GET 
     * @return flowStatus
     */
    public String getFlowStatus() {
        return flowStatus;
    }

    /**
     * SET 
     * @param flowStatus
     */
    public void setFlowStatus(String flowStatus) {
        this.flowStatus = flowStatus == null ? null : flowStatus.trim();
    }
    /**
     * GET 
     * @return flowUser
     */
    public String getFlowUser() {
        return flowUser;
    }

    /**
     * SET 
     * @param flowUser
     */
    public void setFlowUser(String flowUser) {
        this.flowUser = flowUser == null ? null : flowUser.trim();
    }
    /**
     * GET 
     * @return flowUserName
     */
    public String getFlowUserName() {
        return flowUserName;
    }

    /**
     * SET 
     * @param flowUserName
     */
    public void setFlowUserName(String flowUserName) {
        this.flowUserName = flowUserName == null ? null : flowUserName.trim();
    }
    /**
     * GET
     * @return startTime
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * SET
     * @param startTime
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}