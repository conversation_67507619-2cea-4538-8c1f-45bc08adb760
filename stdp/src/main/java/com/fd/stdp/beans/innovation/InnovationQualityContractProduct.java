package com.fd.stdp.beans.innovation;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fd.stdp.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import javax.persistence.*;
import java.math.BigDecimal;

/**
 *@Description: 省质检中心筹建任务书产品
 *@Author: wangsh
 *@Date: 2022-04-12 14:42:31
 */
@Table(name = "INNOVATION_QUALITY_CONTRACT_PRODUCT")
@ApiModel(value = "com.fd.stdp.beans.innovation.InnovationQualityContractProduct", description = "省质检中心筹建任务书产品")
public class InnovationQualityContractProduct extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构code")
    private String orgCode;
    
    @Column(name = "ORG_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构名称")
    private String orgName;
    
    @Column(name = "CONTRACT_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="ID主键")
    private String contractId;
    
    @Column(name = "PRODUCT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="产品名称")
    private String productName;
    
    @Column(name = "STANDARD_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="标准编号、名称")
    private String standardName;
    
    @Column(name = "ITEM")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="检测项目")
    private String item;
    
    @Column(name = "MARK")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="备注")
    private String mark;
    
    @Column(name = "SORT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="排序")
    private String sort;
    
    
    /**
     * GET 机构code
     * @return orgCode
     */
    public String getOrgCode() {
        return orgCode;
    }

    /**
     * SET 机构code
     * @param orgCode
     */
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }
    /**
     * GET 机构名称
     * @return orgName
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * SET 机构名称
     * @param orgName
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }
    /**
     * GET ID主键
     * @return contractId
     */
    public String getContractId() {
        return contractId;
    }

    /**
     * SET ID主键
     * @param contractId
     */
    public void setContractId(String contractId) {
        this.contractId = contractId == null ? null : contractId.trim();
    }
    /**
     * GET 产品名称
     * @return productName
     */
    public String getProductName() {
        return productName;
    }

    /**
     * SET 产品名称
     * @param productName
     */
    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }
    /**
     * GET 标准编号、名称
     * @return standardName
     */
    public String getStandardName() {
        return standardName;
    }

    /**
     * SET 标准编号、名称
     * @param standardName
     */
    public void setStandardName(String standardName) {
        this.standardName = standardName == null ? null : standardName.trim();
    }
    /**
     * GET 检测项目
     * @return item
     */
    public String getItem() {
        return item;
    }

    /**
     * SET 检测项目
     * @param item
     */
    public void setItem(String item) {
        this.item = item == null ? null : item.trim();
    }
    /**
     * GET 备注
     * @return mark
     */
    public String getMark() {
        return mark;
    }

    /**
     * SET 备注
     * @param mark
     */
    public void setMark(String mark) {
        this.mark = mark == null ? null : mark.trim();
    }
    /**
     * GET 排序
     * @return sort
     */
    public String getSort() {
        return sort;
    }

    /**
     * SET 排序
     * @param sort
     */
    public void setSort(String sort) {
        this.sort = sort == null ? null : sort.trim();
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}