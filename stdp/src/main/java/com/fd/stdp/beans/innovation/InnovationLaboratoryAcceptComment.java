package com.fd.stdp.beans.innovation;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fd.stdp.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import javax.persistence.*;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 *@Description: 重点实验室验收评审/变更评审/中期评估
 *@Author: wangsh
 *@Date: 2022-02-10 15:29:07
 */
@Table(name = "INNOVATION_LABORATORY_ACCEPT_COMMENT")
@ApiModel(value = "com.fd.stdp.beans.innovation.InnovationLaboratoryAcceptComment", description = "重点实验室验收评审/变更评审/中期评估")
public class InnovationLaboratoryAcceptComment extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @Column(name = "APPLY_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="ID主键")
    private String applyId;
    
    @Column(name = "EXPERT_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="专家id")
    private String expertId;
    
    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构code")
    private String orgCode;
    
    @Column(name = "ORG_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构名称")
    private String orgName;
    
    @Column(name = "LABORATORY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="实验室名称")
    private String laboratory;
    
    @Column(name = "APPLY_UNIT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="依托单位")
    private String applyUnitName;
    
    @Column(name = "WORK_CONTENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="任务书预期目标及完成情况 / 变更事项及理由 / 任务书完成情况及说明")
    private String workContent;
    
    @Column(name = "IS_PASS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="是否推荐通过验收")
    private String isPass;
    
    @Column(name = "COMMAND")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="专家意见")
    private String command;
    
    @Column(name = "START_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @ApiModelProperty(value="日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    
    
    /**
     * GET ID主键
     * @return applyId
     */
    public String getApplyId() {
        return applyId;
    }

    /**
     * SET ID主键
     * @param applyId
     */
    public void setApplyId(String applyId) {
        this.applyId = applyId == null ? null : applyId.trim();
    }
    /**
     * GET 专家id
     * @return expertId
     */
    public String getExpertId() {
        return expertId;
    }

    /**
     * SET 专家id
     * @param expertId
     */
    public void setExpertId(String expertId) {
        this.expertId = expertId == null ? null : expertId.trim();
    }
    /**
     * GET 机构code
     * @return orgCode
     */
    public String getOrgCode() {
        return orgCode;
    }

    /**
     * SET 机构code
     * @param orgCode
     */
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }
    /**
     * GET 机构名称
     * @return orgName
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * SET 机构名称
     * @param orgName
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }
    /**
     * GET 实验室名称
     * @return laboratory
     */
    public String getLaboratory() {
        return laboratory;
    }

    /**
     * SET 实验室名称
     * @param laboratory
     */
    public void setLaboratory(String laboratory) {
        this.laboratory = laboratory == null ? null : laboratory.trim();
    }
    /**
     * GET 依托单位
     * @return applyUnitName
     */
    public String getApplyUnitName() {
        return applyUnitName;
    }

    /**
     * SET 依托单位
     * @param applyUnitName
     */
    public void setApplyUnitName(String applyUnitName) {
        this.applyUnitName = applyUnitName == null ? null : applyUnitName.trim();
    }
    /**
     * GET 任务书预期目标及完成情况 / 变更事项及理由 / 任务书完成情况及说明
     * @return workContent
     */
    public String getWorkContent() {
        return workContent;
    }

    /**
     * SET 任务书预期目标及完成情况 / 变更事项及理由 / 任务书完成情况及说明
     * @param workContent
     */
    public void setWorkContent(String workContent) {
        this.workContent = workContent == null ? null : workContent.trim();
    }
    /**
     * GET 是否推荐通过验收
     * @return isPass
     */
    public String getIsPass() {
        return isPass;
    }

    /**
     * SET 是否推荐通过验收
     * @param isPass
     */
    public void setIsPass(String isPass) {
        this.isPass = isPass == null ? null : isPass.trim();
    }
    /**
     * GET 专家意见
     * @return command
     */
    public String getCommand() {
        return command;
    }

    /**
     * SET 专家意见
     * @param command
     */
    public void setCommand(String command) {
        this.command = command == null ? null : command.trim();
    }
    /**
     * GET 日期
     * @return startTime
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * SET 日期
     * @param startTime
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}