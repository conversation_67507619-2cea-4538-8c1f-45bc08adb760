package com.fd.stdp.beans.innovation;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fd.stdp.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import javax.persistence.*;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 *@Description: 省质检中心验收申请
 *@Author: wangsh
 *@Date: 2022-03-08 14:52:42
 */
@Table(name = "INNOVATION_QUALITY_ACCEPT_APPLY")
@ApiModel(value = "com.fd.stdp.beans.innovation.InnovationQualityAcceptApply", description = "省质检中心验收申请")
public class InnovationQualityAcceptApply extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构code")
    private String orgCode;
    
    @Column(name = "ORG_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构名称")
    private String orgName;
    
    @Column(name = "APPLY_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="申报id")
    private String applyId;
    
    @Column(name = "CONTRACT_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="任务书id")
    private String contractId;
    
    @Column(name = "APPLY_UNIT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="承建单位名称")
    private String applyUnitName;
    
    @Column(name = "ZIP_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="邮编")
    private String zipCode;
    
    @Column(name = "LINKED_PERSON")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="联系人")
    private String linkedPerson;
    
    @Column(name = "FAX")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="传真")
    private String fax;
    
    @Column(name = "APPLY_CENTER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="申报省质检中心名称")
    private String applyCenterName;
    
    @Column(name = "PASS_DATE")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @ApiModelProperty(value="批准筹建日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date passDate;
    
    @Column(name = "LEGAL_UNIT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="依托法人单位")
    private String legalUnitName;
    
    @Column(name = "ADDRESS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="地址")
    private String address;
    
    @Column(name = "LEADER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="承建单位负责人")
    private String leaderName;
    
    @Column(name = "TEL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="联系电话及传真")
    private String tel;
    
    @Column(name = "EMAIL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="电子邮箱")
    private String email;
    
    @Column(name = "PHONE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="手机号码")
    private String phone;
    
    @Column(name = "CENTER_PERSON_COUNT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="中心人员总数")
    private String centerPersonCount;
    
    @Column(name = "TECH_PERSON_COUNT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="技术人员总数")
    private String techPersonCount;
    
    @Column(name = "CENTER_OBJECT_VALUE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="中心固定资产总值")
    private String centerObjectValue;
    
    @Column(name = "CENTER_DEVICE_VALUE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="中心设备总值")
    private String centerDeviceValue;
    
    @Column(name = "CENTER_HOUCE_AREA")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="中心房屋总面积")
    private String centerHouceArea;
    
    @Column(name = "CENTER_LIB_AREA")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="中心实验室总面积")
    private String centerLibArea;
    
    @Column(name = "TEST_RANGE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="目前已具备的检验能力范围")
    private String testRange = "省质检中心名称对应领域内包括     种产品（或对应    个检验项目），目前检验能力覆盖    种产品（或具有  个项目的检验能力），占   %。";
    
    @Column(name = "TEST_RANGE_PRODUCT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="产品")
    private String testRangeProduct;
    
    @Column(name = "TEST_RANGE_ITEM")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="项目")
    private String testRangeItem;
    
    @Column(name = "TEST_RANGE_PRODUCT2")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="产品")
    private String testRangeProduct2;
    
    @Column(name = "TEST_RANGE_ITEM2")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="项目")
    private String testRangeItem2;
    
    @Column(name = "TEST_RANGE_PERCENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="%")
    private String testRangePercent;
    
    @Column(name = "IMPPORT_ITEM")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="按国际及国外先进标准进行检验的关键项目情况")
    private String impportItem = "能按国际及国外先进标准进行检验的关键项目共   项，占全部关键检验项目的   %。";
    
    @Column(name = "IMPPORT_ITEM_COUNT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="项")
    private String impportItemCount;
    
    @Column(name = "IMPPORT_ITEM_PERCENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="%")
    private String impportItemPercent;
    
    @Column(name = "DEVICE_COUNT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="台")
    private String deviceCount;
    
    @Column(name = "DEVICE_PRICE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="万元")
    private String devicePrice;
    
    @Column(name = "LIB_ENVIRONMENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="实验室环境条件及硬件设施状况")
    private String libEnvironment;
    
    @Column(name = "PROJECT_NATIONAL")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value="承担国家级项目")
    private Integer projectNational;
    
    @Column(name = "PROJACT_PROVINCE")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value="承担省部级项目")
    private Integer projactProvince;
    
    @Column(name = "PROJECT_CITY")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value="承担市厅级项目")
    private Integer projectCity;
    
    @Column(name = "PROJECT_OTHER")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value="承担其他项目")
    private Integer projectOther;
    
    @Column(name = "PROJECT_NATIONAL_INNER")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value="国家级项目")
    private Integer projectNationalInner;
    
    @Column(name = "PROJACT_PROVINCE_INNER")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value="省部级项目")
    private Integer projactProvinceInner;
    
    @Column(name = "PROJECT_CITY_INNER")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value="市厅级项目")
    private Integer projectCityInner;
    
    @Column(name = "PROJECT_OTHER_INNER")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value="其他项目")
    private Integer projectOtherInner;
    
    @Column(name = "NATIONAL_STANDARD")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value="国家标准")
    private Integer nationalStandard;
    
    @Column(name = "INDUSTRY_STANDARD")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value="行业标准")
    private Integer industryStandard;
    
    @Column(name = "OTHER_STANDARD")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value="其它标准")
    private Integer otherStandard;
    
    @Column(name = "TALENT_EXPERT_INFO")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="本专业领域知名专家、学术技术带头人引进和培养情况")
    private String talentExpertInfo;
    
    @Column(name = "TALENT_PRO_INFO")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="专业技术人员引进及培养情况")
    private String talentProInfo;
    
    @Column(name = "TALENT_EDUCATION_INFO")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="技术培训、继续教育情况及取得的成效")
    private String talentEducationInfo;
    
    @Column(name = "TALENT_COMMINICATE_INFO")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="国内、省内技术交流情况，本单位在相关学术组织中所发挥的作用及影响力")
    private String talentComminicateInfo;
    
    @Column(name = "TALENT_DEGREE_INFO")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="专业技术人员的学历和职称结构")
    private String talentDegreeInfo;
    
    @Column(name = "MANAGER_INFO")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="目前的运行管理体制")
    private String managerInfo;
    
    @Column(name = "INTERNATIONAL_INFO")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="信息化建设情况")
    private String internationalInfo;
    
    @Column(name = "COUNTRY_SUPPORT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="地方政府对筹建工作的支持")
    private String countrySupport;
    
    @Column(name = "COUNTRY_SUPPORT_INFO")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="落实情况")
    private String countrySupportInfo;
    
    @Column(name = "FUND_CONTRIBUTE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="筹建资金投入及构成")
    private String fundContribute;
    
    @Column(name = "NEXT_PLAN")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="后续建设发展计划")
    private String nextPlan;
    
    @Column(name = "FINAL_LEVEL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="筹建完成后达到的总体水平")
    private String finalLevel;
    
    @Column(name = "RECENT_AWARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="近期取得的主要科研成果")
    private String recentAward;
    
    @Column(name = "FLOW_STATUS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="信息流程状态 待填报 机构审核 县级审核 市级审核 省级审核 完成")
    private String flowStatus;
    
    @Column(name = "FLOW_USER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowUser;
    
    @Column(name = "FLOW_USER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowUserName;
    
    
    /**
     * GET 机构code
     * @return orgCode
     */
    public String getOrgCode() {
        return orgCode;
    }

    /**
     * SET 机构code
     * @param orgCode
     */
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }
    /**
     * GET 机构名称
     * @return orgName
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * SET 机构名称
     * @param orgName
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }
    /**
     * GET 申报id
     * @return applyId
     */
    public String getApplyId() {
        return applyId;
    }

    /**
     * SET 申报id
     * @param applyId
     */
    public void setApplyId(String applyId) {
        this.applyId = applyId == null ? null : applyId.trim();
    }
    /**
     * GET 任务书id
     * @return contractId
     */
    public String getContractId() {
        return contractId;
    }

    /**
     * SET 任务书id
     * @param contractId
     */
    public void setContractId(String contractId) {
        this.contractId = contractId == null ? null : contractId.trim();
    }
    /**
     * GET 承建单位名称
     * @return applyUnitName
     */
    public String getApplyUnitName() {
        return applyUnitName;
    }

    /**
     * SET 承建单位名称
     * @param applyUnitName
     */
    public void setApplyUnitName(String applyUnitName) {
        this.applyUnitName = applyUnitName == null ? null : applyUnitName.trim();
    }
    /**
     * GET 邮编
     * @return zipCode
     */
    public String getZipCode() {
        return zipCode;
    }

    /**
     * SET 邮编
     * @param zipCode
     */
    public void setZipCode(String zipCode) {
        this.zipCode = zipCode == null ? null : zipCode.trim();
    }
    /**
     * GET 联系人
     * @return linkedPerson
     */
    public String getLinkedPerson() {
        return linkedPerson;
    }

    /**
     * SET 联系人
     * @param linkedPerson
     */
    public void setLinkedPerson(String linkedPerson) {
        this.linkedPerson = linkedPerson == null ? null : linkedPerson.trim();
    }
    /**
     * GET 传真
     * @return fax
     */
    public String getFax() {
        return fax;
    }

    /**
     * SET 传真
     * @param fax
     */
    public void setFax(String fax) {
        this.fax = fax == null ? null : fax.trim();
    }
    /**
     * GET 申报省质检中心名称
     * @return applyCenterName
     */
    public String getApplyCenterName() {
        return applyCenterName;
    }

    /**
     * SET 申报省质检中心名称
     * @param applyCenterName
     */
    public void setApplyCenterName(String applyCenterName) {
        this.applyCenterName = applyCenterName == null ? null : applyCenterName.trim();
    }
    /**
     * GET 批准筹建日期
     * @return passDate
     */
    public Date getPassDate() {
        return passDate;
    }

    /**
     * SET 批准筹建日期
     * @param passDate
     */
    public void setPassDate(Date passDate) {
        this.passDate = passDate;
    }
    /**
     * GET 依托法人单位
     * @return legalUnitName
     */
    public String getLegalUnitName() {
        return legalUnitName;
    }

    /**
     * SET 依托法人单位
     * @param legalUnitName
     */
    public void setLegalUnitName(String legalUnitName) {
        this.legalUnitName = legalUnitName == null ? null : legalUnitName.trim();
    }
    /**
     * GET 地址
     * @return address
     */
    public String getAddress() {
        return address;
    }

    /**
     * SET 地址
     * @param address
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }
    /**
     * GET 承建单位负责人
     * @return leaderName
     */
    public String getLeaderName() {
        return leaderName;
    }

    /**
     * SET 承建单位负责人
     * @param leaderName
     */
    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName == null ? null : leaderName.trim();
    }
    /**
     * GET 联系电话及传真
     * @return tel
     */
    public String getTel() {
        return tel;
    }

    /**
     * SET 联系电话及传真
     * @param tel
     */
    public void setTel(String tel) {
        this.tel = tel == null ? null : tel.trim();
    }
    /**
     * GET 电子邮箱
     * @return email
     */
    public String getEmail() {
        return email;
    }

    /**
     * SET 电子邮箱
     * @param email
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }
    /**
     * GET 手机号码
     * @return phone
     */
    public String getPhone() {
        return phone;
    }

    /**
     * SET 手机号码
     * @param phone
     */
    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }
    /**
     * GET 中心人员总数
     * @return centerPersonCount
     */
    public String getCenterPersonCount() {
        return centerPersonCount;
    }

    /**
     * SET 中心人员总数
     * @param centerPersonCount
     */
    public void setCenterPersonCount(String centerPersonCount) {
        this.centerPersonCount = centerPersonCount == null ? null : centerPersonCount.trim();
    }
    /**
     * GET 技术人员总数
     * @return techPersonCount
     */
    public String getTechPersonCount() {
        return techPersonCount;
    }

    /**
     * SET 技术人员总数
     * @param techPersonCount
     */
    public void setTechPersonCount(String techPersonCount) {
        this.techPersonCount = techPersonCount == null ? null : techPersonCount.trim();
    }
    /**
     * GET 中心固定资产总值
     * @return centerObjectValue
     */
    public String getCenterObjectValue() {
        return centerObjectValue;
    }

    /**
     * SET 中心固定资产总值
     * @param centerObjectValue
     */
    public void setCenterObjectValue(String centerObjectValue) {
        this.centerObjectValue = centerObjectValue == null ? null : centerObjectValue.trim();
    }
    /**
     * GET 中心设备总值
     * @return centerDeviceValue
     */
    public String getCenterDeviceValue() {
        return centerDeviceValue;
    }

    /**
     * SET 中心设备总值
     * @param centerDeviceValue
     */
    public void setCenterDeviceValue(String centerDeviceValue) {
        this.centerDeviceValue = centerDeviceValue == null ? null : centerDeviceValue.trim();
    }
    /**
     * GET 中心房屋总面积
     * @return centerHouceArea
     */
    public String getCenterHouceArea() {
        return centerHouceArea;
    }

    /**
     * SET 中心房屋总面积
     * @param centerHouceArea
     */
    public void setCenterHouceArea(String centerHouceArea) {
        this.centerHouceArea = centerHouceArea == null ? null : centerHouceArea.trim();
    }
    /**
     * GET 中心实验室总面积
     * @return centerLibArea
     */
    public String getCenterLibArea() {
        return centerLibArea;
    }

    /**
     * SET 中心实验室总面积
     * @param centerLibArea
     */
    public void setCenterLibArea(String centerLibArea) {
        this.centerLibArea = centerLibArea == null ? null : centerLibArea.trim();
    }
    /**
     * GET 目前已具备的检验能力范围
     * @return testRange
     */
    public String getTestRange() {
        return testRange;
    }

    /**
     * SET 目前已具备的检验能力范围
     * @param testRange
     */
    public void setTestRange(String testRange) {
        this.testRange = testRange == null ? null : testRange.trim();
    }
    /**
     * GET 产品
     * @return testRangeProduct
     */
    public String getTestRangeProduct() {
        return testRangeProduct;
    }

    /**
     * SET 产品
     * @param testRangeProduct
     */
    public void setTestRangeProduct(String testRangeProduct) {
        this.testRangeProduct = testRangeProduct == null ? null : testRangeProduct.trim();
    }
    /**
     * GET 项目
     * @return testRangeItem
     */
    public String getTestRangeItem() {
        return testRangeItem;
    }

    /**
     * SET 项目
     * @param testRangeItem
     */
    public void setTestRangeItem(String testRangeItem) {
        this.testRangeItem = testRangeItem == null ? null : testRangeItem.trim();
    }
    /**
     * GET 产品
     * @return testRangeProduct2
     */
    public String getTestRangeProduct2() {
        return testRangeProduct2;
    }

    /**
     * SET 产品
     * @param testRangeProduct2
     */
    public void setTestRangeProduct2(String testRangeProduct2) {
        this.testRangeProduct2 = testRangeProduct2 == null ? null : testRangeProduct2.trim();
    }
    /**
     * GET 项目
     * @return testRangeItem2
     */
    public String getTestRangeItem2() {
        return testRangeItem2;
    }

    /**
     * SET 项目
     * @param testRangeItem2
     */
    public void setTestRangeItem2(String testRangeItem2) {
        this.testRangeItem2 = testRangeItem2 == null ? null : testRangeItem2.trim();
    }
    /**
     * GET %
     * @return testRangePercent
     */
    public String getTestRangePercent() {
        return testRangePercent;
    }

    /**
     * SET %
     * @param testRangePercent
     */
    public void setTestRangePercent(String testRangePercent) {
        this.testRangePercent = testRangePercent == null ? null : testRangePercent.trim();
    }
    /**
     * GET 按国际及国外先进标准进行检验的关键项目情况
     * @return impportItem
     */
    public String getImpportItem() {
        return impportItem;
    }

    /**
     * SET 按国际及国外先进标准进行检验的关键项目情况
     * @param impportItem
     */
    public void setImpportItem(String impportItem) {
        this.impportItem = impportItem == null ? null : impportItem.trim();
    }
    /**
     * GET 项
     * @return impportItemCount
     */
    public String getImpportItemCount() {
        return impportItemCount;
    }

    /**
     * SET 项
     * @param impportItemCount
     */
    public void setImpportItemCount(String impportItemCount) {
        this.impportItemCount = impportItemCount == null ? null : impportItemCount.trim();
    }
    /**
     * GET %
     * @return impportItemPercent
     */
    public String getImpportItemPercent() {
        return impportItemPercent;
    }

    /**
     * SET %
     * @param impportItemPercent
     */
    public void setImpportItemPercent(String impportItemPercent) {
        this.impportItemPercent = impportItemPercent == null ? null : impportItemPercent.trim();
    }
    /**
     * GET 台
     * @return deviceCount
     */
    public String getDeviceCount() {
        return deviceCount;
    }

    /**
     * SET 台
     * @param deviceCount
     */
    public void setDeviceCount(String deviceCount) {
        this.deviceCount = deviceCount == null ? null : deviceCount.trim();
    }
    /**
     * GET 万元
     * @return devicePrice
     */
    public String getDevicePrice() {
        return devicePrice;
    }

    /**
     * SET 万元
     * @param devicePrice
     */
    public void setDevicePrice(String devicePrice) {
        this.devicePrice = devicePrice == null ? null : devicePrice.trim();
    }
    /**
     * GET 实验室环境条件及硬件设施状况
     * @return libEnvironment
     */
    public String getLibEnvironment() {
        return libEnvironment;
    }

    /**
     * SET 实验室环境条件及硬件设施状况
     * @param libEnvironment
     */
    public void setLibEnvironment(String libEnvironment) {
        this.libEnvironment = libEnvironment == null ? null : libEnvironment.trim();
    }
    /**
     * GET 承担国家级项目
     * @return projectNational
     */
    public Integer getProjectNational() {
        return projectNational;
    }

    /**
     * SET 承担国家级项目
     * @param projectNational
     */
    public void setProjectNational(Integer projectNational) {
        this.projectNational = projectNational;
    }
    /**
     * GET 承担省部级项目
     * @return projactProvince
     */
    public Integer getProjactProvince() {
        return projactProvince;
    }

    /**
     * SET 承担省部级项目
     * @param projactProvince
     */
    public void setProjactProvince(Integer projactProvince) {
        this.projactProvince = projactProvince;
    }
    /**
     * GET 承担市厅级项目
     * @return projectCity
     */
    public Integer getProjectCity() {
        return projectCity;
    }

    /**
     * SET 承担市厅级项目
     * @param projectCity
     */
    public void setProjectCity(Integer projectCity) {
        this.projectCity = projectCity;
    }
    /**
     * GET 承担其他项目
     * @return projectOther
     */
    public Integer getProjectOther() {
        return projectOther;
    }

    /**
     * SET 承担其他项目
     * @param projectOther
     */
    public void setProjectOther(Integer projectOther) {
        this.projectOther = projectOther;
    }
    /**
     * GET 国家级项目
     * @return projectNationalInner
     */
    public Integer getProjectNationalInner() {
        return projectNationalInner;
    }

    /**
     * SET 国家级项目
     * @param projectNationalInner
     */
    public void setProjectNationalInner(Integer projectNationalInner) {
        this.projectNationalInner = projectNationalInner;
    }
    /**
     * GET 省部级项目
     * @return projactProvinceInner
     */
    public Integer getProjactProvinceInner() {
        return projactProvinceInner;
    }

    /**
     * SET 省部级项目
     * @param projactProvinceInner
     */
    public void setProjactProvinceInner(Integer projactProvinceInner) {
        this.projactProvinceInner = projactProvinceInner;
    }
    /**
     * GET 市厅级项目
     * @return projectCityInner
     */
    public Integer getProjectCityInner() {
        return projectCityInner;
    }

    /**
     * SET 市厅级项目
     * @param projectCityInner
     */
    public void setProjectCityInner(Integer projectCityInner) {
        this.projectCityInner = projectCityInner;
    }
    /**
     * GET 其他项目
     * @return projectOtherInner
     */
    public Integer getProjectOtherInner() {
        return projectOtherInner;
    }

    /**
     * SET 其他项目
     * @param projectOtherInner
     */
    public void setProjectOtherInner(Integer projectOtherInner) {
        this.projectOtherInner = projectOtherInner;
    }
    /**
     * GET 国家标准
     * @return nationalStandard
     */
    public Integer getNationalStandard() {
        return nationalStandard;
    }

    /**
     * SET 国家标准
     * @param nationalStandard
     */
    public void setNationalStandard(Integer nationalStandard) {
        this.nationalStandard = nationalStandard;
    }
    /**
     * GET 行业标准
     * @return industryStandard
     */
    public Integer getIndustryStandard() {
        return industryStandard;
    }

    /**
     * SET 行业标准
     * @param industryStandard
     */
    public void setIndustryStandard(Integer industryStandard) {
        this.industryStandard = industryStandard;
    }
    /**
     * GET 其它标准
     * @return otherStandard
     */
    public Integer getOtherStandard() {
        return otherStandard;
    }

    /**
     * SET 其它标准
     * @param otherStandard
     */
    public void setOtherStandard(Integer otherStandard) {
        this.otherStandard = otherStandard;
    }
    /**
     * GET 本专业领域知名专家、学术技术带头人引进和培养情况
     * @return talentExpertInfo
     */
    public String getTalentExpertInfo() {
        return talentExpertInfo;
    }

    /**
     * SET 本专业领域知名专家、学术技术带头人引进和培养情况
     * @param talentExpertInfo
     */
    public void setTalentExpertInfo(String talentExpertInfo) {
        this.talentExpertInfo = talentExpertInfo == null ? null : talentExpertInfo.trim();
    }
    /**
     * GET 专业技术人员引进及培养情况
     * @return talentProInfo
     */
    public String getTalentProInfo() {
        return talentProInfo;
    }

    /**
     * SET 专业技术人员引进及培养情况
     * @param talentProInfo
     */
    public void setTalentProInfo(String talentProInfo) {
        this.talentProInfo = talentProInfo == null ? null : talentProInfo.trim();
    }
    /**
     * GET 技术培训、继续教育情况及取得的成效
     * @return talentEducationInfo
     */
    public String getTalentEducationInfo() {
        return talentEducationInfo;
    }

    /**
     * SET 技术培训、继续教育情况及取得的成效
     * @param talentEducationInfo
     */
    public void setTalentEducationInfo(String talentEducationInfo) {
        this.talentEducationInfo = talentEducationInfo == null ? null : talentEducationInfo.trim();
    }
    /**
     * GET 国内、省内技术交流情况，本单位在相关学术组织中所发挥的作用及影响力
     * @return talentComminicateInfo
     */
    public String getTalentComminicateInfo() {
        return talentComminicateInfo;
    }

    /**
     * SET 国内、省内技术交流情况，本单位在相关学术组织中所发挥的作用及影响力
     * @param talentComminicateInfo
     */
    public void setTalentComminicateInfo(String talentComminicateInfo) {
        this.talentComminicateInfo = talentComminicateInfo == null ? null : talentComminicateInfo.trim();
    }
    /**
     * GET 专业技术人员的学历和职称结构
     * @return talentDegreeInfo
     */
    public String getTalentDegreeInfo() {
        return talentDegreeInfo;
    }

    /**
     * SET 专业技术人员的学历和职称结构
     * @param talentDegreeInfo
     */
    public void setTalentDegreeInfo(String talentDegreeInfo) {
        this.talentDegreeInfo = talentDegreeInfo == null ? null : talentDegreeInfo.trim();
    }
    /**
     * GET 目前的运行管理体制
     * @return managerInfo
     */
    public String getManagerInfo() {
        return managerInfo;
    }

    /**
     * SET 目前的运行管理体制
     * @param managerInfo
     */
    public void setManagerInfo(String managerInfo) {
        this.managerInfo = managerInfo == null ? null : managerInfo.trim();
    }
    /**
     * GET 信息化建设情况
     * @return internationalInfo
     */
    public String getInternationalInfo() {
        return internationalInfo;
    }

    /**
     * SET 信息化建设情况
     * @param internationalInfo
     */
    public void setInternationalInfo(String internationalInfo) {
        this.internationalInfo = internationalInfo == null ? null : internationalInfo.trim();
    }
    /**
     * GET 地方政府对筹建工作的支持
     * @return countrySupport
     */
    public String getCountrySupport() {
        return countrySupport;
    }

    /**
     * SET 地方政府对筹建工作的支持
     * @param countrySupport
     */
    public void setCountrySupport(String countrySupport) {
        this.countrySupport = countrySupport == null ? null : countrySupport.trim();
    }
    /**
     * GET 落实情况
     * @return countrySupportInfo
     */
    public String getCountrySupportInfo() {
        return countrySupportInfo;
    }

    /**
     * SET 落实情况
     * @param countrySupportInfo
     */
    public void setCountrySupportInfo(String countrySupportInfo) {
        this.countrySupportInfo = countrySupportInfo == null ? null : countrySupportInfo.trim();
    }
    /**
     * GET 筹建资金投入及构成
     * @return fundContribute
     */
    public String getFundContribute() {
        return fundContribute;
    }

    /**
     * SET 筹建资金投入及构成
     * @param fundContribute
     */
    public void setFundContribute(String fundContribute) {
        this.fundContribute = fundContribute == null ? null : fundContribute.trim();
    }
    /**
     * GET 后续建设发展计划
     * @return nextPlan
     */
    public String getNextPlan() {
        return nextPlan;
    }

    /**
     * SET 后续建设发展计划
     * @param nextPlan
     */
    public void setNextPlan(String nextPlan) {
        this.nextPlan = nextPlan == null ? null : nextPlan.trim();
    }
    /**
     * GET 筹建完成后达到的总体水平
     * @return finalLevel
     */
    public String getFinalLevel() {
        return finalLevel;
    }

    /**
     * SET 筹建完成后达到的总体水平
     * @param finalLevel
     */
    public void setFinalLevel(String finalLevel) {
        this.finalLevel = finalLevel == null ? null : finalLevel.trim();
    }
    /**
     * GET 近期取得的主要科研成果
     * @return recentAward
     */
    public String getRecentAward() {
        return recentAward;
    }

    /**
     * SET 近期取得的主要科研成果
     * @param recentAward
     */
    public void setRecentAward(String recentAward) {
        this.recentAward = recentAward == null ? null : recentAward.trim();
    }
    /**
     * GET 信息流程状态 待填报 机构审核 县级审核 市级审核 省级审核 完成
     * @return flowStatus
     */
    public String getFlowStatus() {
        return flowStatus;
    }

    /**
     * SET 信息流程状态 待填报 机构审核 县级审核 市级审核 省级审核 完成
     * @param flowStatus
     */
    public void setFlowStatus(String flowStatus) {
        this.flowStatus = flowStatus == null ? null : flowStatus.trim();
    }
    /**
     * GET 
     * @return flowUser
     */
    public String getFlowUser() {
        return flowUser;
    }

    /**
     * SET 
     * @param flowUser
     */
    public void setFlowUser(String flowUser) {
        this.flowUser = flowUser == null ? null : flowUser.trim();
    }
    /**
     * GET 
     * @return flowUserName
     */
    public String getFlowUserName() {
        return flowUserName;
    }

    /**
     * SET 
     * @param flowUserName
     */
    public void setFlowUserName(String flowUserName) {
        this.flowUserName = flowUserName == null ? null : flowUserName.trim();
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}