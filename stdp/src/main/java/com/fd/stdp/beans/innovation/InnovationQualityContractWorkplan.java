package com.fd.stdp.beans.innovation;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fd.stdp.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import javax.persistence.*;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 *@Description: 省质检中心筹建任务书工作进度
 *@Author: wangsh
 *@Date: 2022-03-04 14:58:35
 */
@Table(name = "INNOVATION_QUALITY_CONTRACT_WORKPLAN")
@ApiModel(value = "com.fd.stdp.beans.innovation.InnovationQualityContractWorkplan", description = "省质检中心筹建任务书工作进度")
public class InnovationQualityContractWorkplan extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构code")
    private String orgCode;
    
    @Column(name = "ORG_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构名称")
    private String orgName;
    
    @Column(name = "CONTRACT_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="ID主键")
    private String contractId;
    
    @Column(name = "START_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @ApiModelProperty(value="开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date startTime;
    
    @Column(name = "END_TIME")
    @ColumnType(jdbcType = JdbcType.TIMESTAMP)
    @ApiModelProperty(value="结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date endTime;
    
    @Column(name = "MAIN_WORK")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="主要工作")
    private String mainWork;
    
    @Column(name = "PLAN_TARGET")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="阶段性目标")
    private String planTarget;
    
    @Column(name = "MARK")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="备注")
    private String mark;
    
    
    /**
     * GET 机构code
     * @return orgCode
     */
    public String getOrgCode() {
        return orgCode;
    }

    /**
     * SET 机构code
     * @param orgCode
     */
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }
    /**
     * GET 机构名称
     * @return orgName
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * SET 机构名称
     * @param orgName
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }
    /**
     * GET ID主键
     * @return contractId
     */
    public String getContractId() {
        return contractId;
    }

    /**
     * SET ID主键
     * @param contractId
     */
    public void setContractId(String contractId) {
        this.contractId = contractId == null ? null : contractId.trim();
    }
    /**
     * GET 开始时间
     * @return startTime
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * SET 开始时间
     * @param startTime
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    /**
     * GET 结束时间
     * @return endTime
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * SET 结束时间
     * @param endTime
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    /**
     * GET 主要工作
     * @return mainWork
     */
    public String getMainWork() {
        return mainWork;
    }

    /**
     * SET 主要工作
     * @param mainWork
     */
    public void setMainWork(String mainWork) {
        this.mainWork = mainWork == null ? null : mainWork.trim();
    }
    /**
     * GET 阶段性目标
     * @return planTarget
     */
    public String getPlanTarget() {
        return planTarget;
    }

    /**
     * SET 阶段性目标
     * @param planTarget
     */
    public void setPlanTarget(String planTarget) {
        this.planTarget = planTarget == null ? null : planTarget.trim();
    }
    /**
     * GET 备注
     * @return mark
     */
    public String getMark() {
        return mark;
    }

    /**
     * SET 备注
     * @param mark
     */
    public void setMark(String mark) {
        this.mark = mark == null ? null : mark.trim();
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}