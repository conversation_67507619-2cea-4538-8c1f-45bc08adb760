package com.fd.stdp.beans.innovation;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fd.stdp.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import javax.persistence.*;
import java.math.BigDecimal;

/**
 *@Description: 重点实验室培育申报任务书经费预算
 *@Author: wangsh
 *@Date: 2022-03-30 13:57:45
 */
@Table(name = "INNOVATION_LABORATORY_CONTRACT_COST")
@ApiModel(value = "com.fd.stdp.beans.innovation.InnovationLaboratoryContractCost", description = "重点实验室培育申报任务书经费预算")
public class InnovationLaboratoryContractCost extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @Column(name = "CONTRACT_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="任务书id")
    private String contractId;
    
    @Column(name = "SUBJECT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="经费开支科目")
    private String subjectName;
    
    @Column(name = "TOTAL_FUND")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="总金额（万元）")
    private BigDecimal totalFund;
    
    @Column(name = "FIRST_FUND")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="甲方金额（万元）")
    private BigDecimal firstFund;
    
    @Column(name = "SECOND_FUND")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="乙方金额（万元）")
    private BigDecimal secondFund;
    
    @Column(name = "THIRD_FUND")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="丙方金额（万元）")
    private BigDecimal thirdFund;
    
    @Column(name = "SORT")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value="排序")
    private Integer sort;
    
    
    /**
     * GET 任务书id
     * @return contractId
     */
    public String getContractId() {
        return contractId;
    }

    /**
     * SET 任务书id
     * @param contractId
     */
    public void setContractId(String contractId) {
        this.contractId = contractId == null ? null : contractId.trim();
    }
    /**
     * GET 经费开支科目
     * @return subjectName
     */
    public String getSubjectName() {
        return subjectName;
    }

    /**
     * SET 经费开支科目
     * @param subjectName
     */
    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName == null ? null : subjectName.trim();
    }
    /**
     * GET 总金额（万元）
     * @return totalFund
     */
    public BigDecimal getTotalFund() {
        return totalFund;
    }

    /**
     * SET 总金额（万元）
     * @param totalFund
     */
    public void setTotalFund(BigDecimal totalFund) {
        this.totalFund = totalFund;
    }
    /**
     * GET 甲方金额（万元）
     * @return firstFund
     */
    public BigDecimal getFirstFund() {
        return firstFund;
    }

    /**
     * SET 甲方金额（万元）
     * @param firstFund
     */
    public void setFirstFund(BigDecimal firstFund) {
        this.firstFund = firstFund;
    }
    /**
     * GET 乙方金额（万元）
     * @return secondFund
     */
    public BigDecimal getSecondFund() {
        return secondFund;
    }

    /**
     * SET 乙方金额（万元）
     * @param secondFund
     */
    public void setSecondFund(BigDecimal secondFund) {
        this.secondFund = secondFund;
    }
    /**
     * GET 丙方金额（万元）
     * @return thirdFund
     */
    public BigDecimal getThirdFund() {
        return thirdFund;
    }

    /**
     * SET 丙方金额（万元）
     * @param thirdFund
     */
    public void setThirdFund(BigDecimal thirdFund) {
        this.thirdFund = thirdFund;
    }
    /**
     * GET 排序
     * @return sort
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * SET 排序
     * @param sort
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}