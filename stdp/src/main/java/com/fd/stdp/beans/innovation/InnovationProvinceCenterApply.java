package com.fd.stdp.beans.innovation;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fd.stdp.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import javax.persistence.*;
import java.math.BigDecimal;

/**
 *@Description: 省质检中心
 *@Author: wangsh
 *@Date: 2022-03-22 15:20:01
 */
@Table(name = "INNOVATION_PROVINCE_CENTER_APPLY")
@ApiModel(value = "com.fd.stdp.beans.innovation.InnovationProvinceCenterApply", description = "省质检中心")
public class InnovationProvinceCenterApply extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构code")
    private String orgCode;
    
    @Column(name = "ORG_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构名称")
    private String orgName;
    
    @Column(name = "CENTER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="省级质检中心名称")
    private String centerName;
    
    @Column(name = "CONTRIBUTE_PROGRESS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="建设进度")
    private String contributeProgress;
    
    @Column(name = "RUNTIME_DATE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="成立时间")
    private String runtimeDate;
    
    @Column(name = "LOCATION_AREA")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="所在地市")
    private String locationArea;
    
    @Column(name = "APPLY_UNIT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="依托单位")
    private String applyUnitName;
    
    @Column(name = "ADDRESS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="通讯地址")
    private String address;
    
    @Column(name = "LEADER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="中心主任")
    private String leaderName;
    
    @Column(name = "DEVICE_VALUE_ADD")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="新增仪器设备原值")
    private BigDecimal deviceValueAdd;
    
    @Column(name = "DEVICE_VALUE_TOTAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="累计仪器设备原值")
    private BigDecimal deviceValueTotal;
    
    @Column(name = "DEVICE_HIGH_ADD")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="50W以上设备台数新增")
    private BigDecimal deviceHighAdd;
    
    @Column(name = "DEVICE_HIGH_TOTAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="50W以上设备台数累计")
    private BigDecimal deviceHighTotal;
    
    @Column(name = "CAM_PRODUCT_ADD")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="获得CMA资质的具有全项检测能力的产品数量新增")
    private BigDecimal camProductAdd;
    
    @Column(name = "CAM_PRODUCT_TOTAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="获得CMA资质的具有全项检测能力的产品数量累计")
    private BigDecimal camProductTotal;
    
    @Column(name = "CAM_PARAM_ADD")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="获得CMA资质的检测参数数量新增")
    private BigDecimal camParamAdd;
    
    @Column(name = "CAM_PARAM_TOTAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="获得CMA资质的检测参数数量累计")
    private BigDecimal camParamTotal;
    
    @Column(name = "CAM_STANDARD_ADD")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="获得CMA资质的国际或国外标准数量新增")
    private BigDecimal camStandardAdd;
    
    @Column(name = "CAM_STANDARD_TOTAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="获得CMA资质的国际或国外标准数量累计")
    private BigDecimal camStandardTotal;
    
    @Column(name = "LIB_AREA_ADD")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="实验室面积新增")
    private BigDecimal libAreaAdd;
    
    @Column(name = "LIB_AREA_TOTAL")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="实验室面积累计")
    private BigDecimal libAreaTotal;
    
    @Column(name = "ORIGIN_PERSON_ADD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="技术人员总数新增")
    private String originPersonAdd;
    
    @Column(name = "ORIGIN_PERSON_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="技术人员总数累计")
    private String originPersonTotal;
    
    @Column(name = "ZHENG_GAO_ADD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="正高级职称人数新增")
    private String zhengGaoAdd;
    
    @Column(name = "ZHENG_GAO_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="正高级职称人数累计")
    private String zhengGaoTotal;
    
    @Column(name = "FU_GAO_ADD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="副高级职称人数新增")
    private String fuGaoAdd;
    
    @Column(name = "FU_GAO_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="副高级职称人数累计")
    private String fuGaoTotal;
    
    @Column(name = "ZHONG_JI_ADD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="中级职称人数新增")
    private String zhongJiAdd;
    
    @Column(name = "ZHONG_JI_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="中级职称人数累计")
    private String zhongJiTotal;
    
    @Column(name = "DOCTOR_ADD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="博士学历人数新增")
    private String doctorAdd;
    
    @Column(name = "DOCTOR_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="博士学历人数累计")
    private String doctorTotal;
    
    @Column(name = "MASTER_ADD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="硕士学历人数新增")
    private String masterAdd;
    
    @Column(name = "MASTER_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="硕士学历人数累计")
    private String masterTotal;
    
    @Column(name = "NATIONAL_COMMITTEE_INTER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="作为秘书处参与国家/行业标准化技术委员会的数量")
    private String nationalCommitteeInter;
    
    @Column(name = "NATIONAL_COMMITTEE_HODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="作为主任委员/副主任委员参与国家/行业标准化技术委员会的数量")
    private String nationalCommitteeHode;
    
    @Column(name = "NATIONAL_COMMITTEE_JOIN")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="作为委员参与国家/行业标准化技术委员会的数量")
    private String nationalCommitteeJoin;
    
    @Column(name = "LOCAL_COMMITTEE_INTER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="作为秘书处参与地方标准化技术委员会的数量")
    private String localCommitteeInter;
    
    @Column(name = "TEAM_COMMITTEE_INTER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="作为秘书处参与学会/团体标准化委员会的数量")
    private String teamCommitteeInter;
    
    @Column(name = "INVENTION_PATENT_AUTH")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年获得发明专利授权的数量")
    private String inventionPatentAuth;
    
    @Column(name = "UTILITY_MODEL_AUTH")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年获得实用新型专利授权的数量")
    private String utilityModelAuth;
    
    @Column(name = "OUTWORD_AUTH")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年获得外观设计专利授权的数量")
    private String outwordAuth;
    
    @Column(name = "SOFTWARE_COPYRIGHT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年获得软件著作权授权的数量")
    private String softwareCopyright;
    
    @Column(name = "FUND_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年科研经费投入金额")
    private String fundTotal;
    
    @Column(name = "NATIONAL_PROJACT_ADD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年新增国家级科研项目立项数新增")
    private String nationalProjactAdd;
    
    @Column(name = "NATIONAL_PROJACT_INTER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年新增国家级科研项目立项数参与")
    private String nationalProjactInter;
    
    @Column(name = "PROVINCE_PROJACT_ADD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年新增省部级科研项目立项数新增")
    private String provinceProjactAdd;
    
    @Column(name = "PROVINCE_PROJACT_INTER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年新增省部级科研项目立项数参与")
    private String provinceProjactInter;
    
    @Column(name = "CITY_PROJACT_ADD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年新增主持地市级科研项目立项数")
    private String cityProjactAdd;
    
    @Column(name = "CITY_PROJACT_INNER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String cityProjactInner;
    
    @Column(name = "INTERNATIONAL_STANDARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年新增主持或参与发布国际标准制修订数")
    private String internationalStandard;
    
    @Column(name = "NATIONAL_STANDARD_ADD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年新增发布国家/行业标准制修订数主持")
    private String nationalStandardAdd;
    
    @Column(name = "NATIONAL_STANDARD_ENTER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年新增发布国家/行业标准制修订数参与")
    private String nationalStandardEnter;
    
    @Column(name = "COUNTRY_STANDARD_ADD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年新增发布地方/团体标准制修订数主持")
    private String countryStandardAdd;
    
    @Column(name = "COUNTRY_STANDARD_ENTER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年新增发布地方/团体标准制修订数参与")
    private String countryStandardEnter;
    
    @Column(name = "NATIONAL_STANDARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年主持或参与的标准制修订数 国家标准")
    private String nationalStandard;
    
    @Column(name = "INDUSTRY_STANDARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年主持或参与的标准制修订数 行业标准")
    private String industryStandard;
    
    @Column(name = "LOCAL_STANDARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年主持或参与的标准制修订数 地方标准")
    private String localStandard;
    
    @Column(name = "GROUP_STANDARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年主持或参与的标准制修订数 团体标准")
    private String groupStandard;
    
    @Column(name = "SCI_PAPER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年发表的论文数SCI")
    private String sciPaper;
    
    @Column(name = "CORE_PAPER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年发表的论文数核心")
    private String corePaper;
    
    @Column(name = "NARMAL_PAPER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年发表的论文数一般")
    private String narmalPaper;
    
    @Column(name = "NATIONAL_AWARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年获得的奖励数 国家级")
    private String nationalAward;
    
    @Column(name = "PROVINCE_AWARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年获得的奖励数 省级")
    private String provinceAward;
    
    @Column(name = "SOCIAL_AWARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年获得的奖励数 社会科学技术")
    private String socialAward;
    
    @Column(name = "AWARD_SELL_COUNT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年成果转化情况 转化项目数")
    private String awardSellCount;
    
    @Column(name = "AWARD_SELL_PRICE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年成果转化情况 转化金额（万元）")
    private String awardSellPrice;
    
    @Column(name = "TEST_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 检验业务状况 当年检验总批次")
    private String testTotal;
    
    @Column(name = "TEST_PRICE_TOTAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 检验业务状况 当年检验业务总收入（万元）")
    private String testPriceTotal;
    
    @Column(name = "TEST_NATIONAL_COUNT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 检验业务状况 承担当年国家市场监管总局下达的产品质量监督任务数量")
    private String testNationalCount;
    
    @Column(name = "TEST_PROVINCE_COUNT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 检验业务状况 承担当年省级市场监管局下达的产品质量监督任务数量")
    private String testProvinceCount;
    
    @Column(name = "TEST_CITY_COUNT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 检验业务状况 承担当年省级（不含）以下市场监管部门下达的产品质量监督任务数量")
    private String testCityCount;
    
    @Column(name = "TEST_INDUSTRY_COUNT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 检验业务状况 承担当年行业部委下达的产品质量监督任务数量")
    private String testIndustryCount;
    
    @Column(name = "OPEN_LIBORATORY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value=" 当年服务中小微企业 开放实验室（批次）")
    private String openLiboratory;
    
    @Column(name = "COMMINICATION_COUNT")
    @ColumnType(jdbcType = JdbcType.DECIMAL)
    @ApiModelProperty(value="减免委托检测费（万元）")
    private BigDecimal comminicationCount;
    
    @Column(name = "SERVICE_COUNT")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value=" 当年服务中小微企业的数量")
    private Integer serviceCount;
    
    @Column(name = "FLOW_STATUS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowStatus;
    
    @Column(name = "FLOW_USER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowUser;
    
    @Column(name = "FLOW_USER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowUserName;
    
    
    /**
     * GET 机构code
     * @return orgCode
     */
    public String getOrgCode() {
        return orgCode;
    }

    /**
     * SET 机构code
     * @param orgCode
     */
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }
    /**
     * GET 机构名称
     * @return orgName
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * SET 机构名称
     * @param orgName
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }
    /**
     * GET 省级质检中心名称
     * @return centerName
     */
    public String getCenterName() {
        return centerName;
    }

    /**
     * SET 省级质检中心名称
     * @param centerName
     */
    public void setCenterName(String centerName) {
        this.centerName = centerName == null ? null : centerName.trim();
    }
    /**
     * GET 建设进度
     * @return contributeProgress
     */
    public String getContributeProgress() {
        return contributeProgress;
    }

    /**
     * SET 建设进度
     * @param contributeProgress
     */
    public void setContributeProgress(String contributeProgress) {
        this.contributeProgress = contributeProgress == null ? null : contributeProgress.trim();
    }
    /**
     * GET 成立时间
     * @return runtimeDate
     */
    public String getRuntimeDate() {
        return runtimeDate;
    }

    /**
     * SET 成立时间
     * @param runtimeDate
     */
    public void setRuntimeDate(String runtimeDate) {
        this.runtimeDate = runtimeDate == null ? null : runtimeDate.trim();
    }
    /**
     * GET 所在地市
     * @return locationArea
     */
    public String getLocationArea() {
        return locationArea;
    }

    /**
     * SET 所在地市
     * @param locationArea
     */
    public void setLocationArea(String locationArea) {
        this.locationArea = locationArea == null ? null : locationArea.trim();
    }
    /**
     * GET 依托单位
     * @return applyUnitName
     */
    public String getApplyUnitName() {
        return applyUnitName;
    }

    /**
     * SET 依托单位
     * @param applyUnitName
     */
    public void setApplyUnitName(String applyUnitName) {
        this.applyUnitName = applyUnitName == null ? null : applyUnitName.trim();
    }
    /**
     * GET 通讯地址
     * @return address
     */
    public String getAddress() {
        return address;
    }

    /**
     * SET 通讯地址
     * @param address
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }
    /**
     * GET 中心主任
     * @return leaderName
     */
    public String getLeaderName() {
        return leaderName;
    }

    /**
     * SET 中心主任
     * @param leaderName
     */
    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName == null ? null : leaderName.trim();
    }
    /**
     * GET 新增仪器设备原值
     * @return deviceValueAdd
     */
    public BigDecimal getDeviceValueAdd() {
        return deviceValueAdd;
    }

    /**
     * SET 新增仪器设备原值
     * @param deviceValueAdd
     */
    public void setDeviceValueAdd(BigDecimal deviceValueAdd) {
        this.deviceValueAdd = deviceValueAdd;
    }
    /**
     * GET 累计仪器设备原值
     * @return deviceValueTotal
     */
    public BigDecimal getDeviceValueTotal() {
        return deviceValueTotal;
    }

    /**
     * SET 累计仪器设备原值
     * @param deviceValueTotal
     */
    public void setDeviceValueTotal(BigDecimal deviceValueTotal) {
        this.deviceValueTotal = deviceValueTotal;
    }
    /**
     * GET 50W以上设备台数新增
     * @return deviceHighAdd
     */
    public BigDecimal getDeviceHighAdd() {
        return deviceHighAdd;
    }

    /**
     * SET 50W以上设备台数新增
     * @param deviceHighAdd
     */
    public void setDeviceHighAdd(BigDecimal deviceHighAdd) {
        this.deviceHighAdd = deviceHighAdd;
    }
    /**
     * GET 50W以上设备台数累计
     * @return deviceHighTotal
     */
    public BigDecimal getDeviceHighTotal() {
        return deviceHighTotal;
    }

    /**
     * SET 50W以上设备台数累计
     * @param deviceHighTotal
     */
    public void setDeviceHighTotal(BigDecimal deviceHighTotal) {
        this.deviceHighTotal = deviceHighTotal;
    }
    /**
     * GET 获得CMA资质的具有全项检测能力的产品数量新增
     * @return camProductAdd
     */
    public BigDecimal getCamProductAdd() {
        return camProductAdd;
    }

    /**
     * SET 获得CMA资质的具有全项检测能力的产品数量新增
     * @param camProductAdd
     */
    public void setCamProductAdd(BigDecimal camProductAdd) {
        this.camProductAdd = camProductAdd;
    }
    /**
     * GET 获得CMA资质的具有全项检测能力的产品数量累计
     * @return camProductTotal
     */
    public BigDecimal getCamProductTotal() {
        return camProductTotal;
    }

    /**
     * SET 获得CMA资质的具有全项检测能力的产品数量累计
     * @param camProductTotal
     */
    public void setCamProductTotal(BigDecimal camProductTotal) {
        this.camProductTotal = camProductTotal;
    }
    /**
     * GET 获得CMA资质的检测参数数量新增
     * @return camParamAdd
     */
    public BigDecimal getCamParamAdd() {
        return camParamAdd;
    }

    /**
     * SET 获得CMA资质的检测参数数量新增
     * @param camParamAdd
     */
    public void setCamParamAdd(BigDecimal camParamAdd) {
        this.camParamAdd = camParamAdd;
    }
    /**
     * GET 获得CMA资质的检测参数数量累计
     * @return camParamTotal
     */
    public BigDecimal getCamParamTotal() {
        return camParamTotal;
    }

    /**
     * SET 获得CMA资质的检测参数数量累计
     * @param camParamTotal
     */
    public void setCamParamTotal(BigDecimal camParamTotal) {
        this.camParamTotal = camParamTotal;
    }
    /**
     * GET 获得CMA资质的国际或国外标准数量新增
     * @return camStandardAdd
     */
    public BigDecimal getCamStandardAdd() {
        return camStandardAdd;
    }

    /**
     * SET 获得CMA资质的国际或国外标准数量新增
     * @param camStandardAdd
     */
    public void setCamStandardAdd(BigDecimal camStandardAdd) {
        this.camStandardAdd = camStandardAdd;
    }
    /**
     * GET 获得CMA资质的国际或国外标准数量累计
     * @return camStandardTotal
     */
    public BigDecimal getCamStandardTotal() {
        return camStandardTotal;
    }

    /**
     * SET 获得CMA资质的国际或国外标准数量累计
     * @param camStandardTotal
     */
    public void setCamStandardTotal(BigDecimal camStandardTotal) {
        this.camStandardTotal = camStandardTotal;
    }
    /**
     * GET 实验室面积新增
     * @return libAreaAdd
     */
    public BigDecimal getLibAreaAdd() {
        return libAreaAdd;
    }

    /**
     * SET 实验室面积新增
     * @param libAreaAdd
     */
    public void setLibAreaAdd(BigDecimal libAreaAdd) {
        this.libAreaAdd = libAreaAdd;
    }
    /**
     * GET 实验室面积累计
     * @return libAreaTotal
     */
    public BigDecimal getLibAreaTotal() {
        return libAreaTotal;
    }

    /**
     * SET 实验室面积累计
     * @param libAreaTotal
     */
    public void setLibAreaTotal(BigDecimal libAreaTotal) {
        this.libAreaTotal = libAreaTotal;
    }
    /**
     * GET 技术人员总数新增
     * @return originPersonAdd
     */
    public String getOriginPersonAdd() {
        return originPersonAdd;
    }

    /**
     * SET 技术人员总数新增
     * @param originPersonAdd
     */
    public void setOriginPersonAdd(String originPersonAdd) {
        this.originPersonAdd = originPersonAdd == null ? null : originPersonAdd.trim();
    }
    /**
     * GET 技术人员总数累计
     * @return originPersonTotal
     */
    public String getOriginPersonTotal() {
        return originPersonTotal;
    }

    /**
     * SET 技术人员总数累计
     * @param originPersonTotal
     */
    public void setOriginPersonTotal(String originPersonTotal) {
        this.originPersonTotal = originPersonTotal == null ? null : originPersonTotal.trim();
    }
    /**
     * GET 正高级职称人数新增
     * @return zhengGaoAdd
     */
    public String getZhengGaoAdd() {
        return zhengGaoAdd;
    }

    /**
     * SET 正高级职称人数新增
     * @param zhengGaoAdd
     */
    public void setZhengGaoAdd(String zhengGaoAdd) {
        this.zhengGaoAdd = zhengGaoAdd == null ? null : zhengGaoAdd.trim();
    }
    /**
     * GET 正高级职称人数累计
     * @return zhengGaoTotal
     */
    public String getZhengGaoTotal() {
        return zhengGaoTotal;
    }

    /**
     * SET 正高级职称人数累计
     * @param zhengGaoTotal
     */
    public void setZhengGaoTotal(String zhengGaoTotal) {
        this.zhengGaoTotal = zhengGaoTotal == null ? null : zhengGaoTotal.trim();
    }
    /**
     * GET 副高级职称人数新增
     * @return fuGaoAdd
     */
    public String getFuGaoAdd() {
        return fuGaoAdd;
    }

    /**
     * SET 副高级职称人数新增
     * @param fuGaoAdd
     */
    public void setFuGaoAdd(String fuGaoAdd) {
        this.fuGaoAdd = fuGaoAdd == null ? null : fuGaoAdd.trim();
    }
    /**
     * GET 副高级职称人数累计
     * @return fuGaoTotal
     */
    public String getFuGaoTotal() {
        return fuGaoTotal;
    }

    /**
     * SET 副高级职称人数累计
     * @param fuGaoTotal
     */
    public void setFuGaoTotal(String fuGaoTotal) {
        this.fuGaoTotal = fuGaoTotal == null ? null : fuGaoTotal.trim();
    }
    /**
     * GET 中级职称人数新增
     * @return zhongJiAdd
     */
    public String getZhongJiAdd() {
        return zhongJiAdd;
    }

    /**
     * SET 中级职称人数新增
     * @param zhongJiAdd
     */
    public void setZhongJiAdd(String zhongJiAdd) {
        this.zhongJiAdd = zhongJiAdd == null ? null : zhongJiAdd.trim();
    }
    /**
     * GET 中级职称人数累计
     * @return zhongJiTotal
     */
    public String getZhongJiTotal() {
        return zhongJiTotal;
    }

    /**
     * SET 中级职称人数累计
     * @param zhongJiTotal
     */
    public void setZhongJiTotal(String zhongJiTotal) {
        this.zhongJiTotal = zhongJiTotal == null ? null : zhongJiTotal.trim();
    }
    /**
     * GET 博士学历人数新增
     * @return doctorAdd
     */
    public String getDoctorAdd() {
        return doctorAdd;
    }

    /**
     * SET 博士学历人数新增
     * @param doctorAdd
     */
    public void setDoctorAdd(String doctorAdd) {
        this.doctorAdd = doctorAdd == null ? null : doctorAdd.trim();
    }
    /**
     * GET 博士学历人数累计
     * @return doctorTotal
     */
    public String getDoctorTotal() {
        return doctorTotal;
    }

    /**
     * SET 博士学历人数累计
     * @param doctorTotal
     */
    public void setDoctorTotal(String doctorTotal) {
        this.doctorTotal = doctorTotal == null ? null : doctorTotal.trim();
    }
    /**
     * GET 硕士学历人数新增
     * @return masterAdd
     */
    public String getMasterAdd() {
        return masterAdd;
    }

    /**
     * SET 硕士学历人数新增
     * @param masterAdd
     */
    public void setMasterAdd(String masterAdd) {
        this.masterAdd = masterAdd == null ? null : masterAdd.trim();
    }
    /**
     * GET 硕士学历人数累计
     * @return masterTotal
     */
    public String getMasterTotal() {
        return masterTotal;
    }

    /**
     * SET 硕士学历人数累计
     * @param masterTotal
     */
    public void setMasterTotal(String masterTotal) {
        this.masterTotal = masterTotal == null ? null : masterTotal.trim();
    }
    /**
     * GET 作为秘书处参与国家/行业标准化技术委员会的数量
     * @return nationalCommitteeInter
     */
    public String getNationalCommitteeInter() {
        return nationalCommitteeInter;
    }

    /**
     * SET 作为秘书处参与国家/行业标准化技术委员会的数量
     * @param nationalCommitteeInter
     */
    public void setNationalCommitteeInter(String nationalCommitteeInter) {
        this.nationalCommitteeInter = nationalCommitteeInter == null ? null : nationalCommitteeInter.trim();
    }
    /**
     * GET 作为主任委员/副主任委员参与国家/行业标准化技术委员会的数量
     * @return nationalCommitteeHode
     */
    public String getNationalCommitteeHode() {
        return nationalCommitteeHode;
    }

    /**
     * SET 作为主任委员/副主任委员参与国家/行业标准化技术委员会的数量
     * @param nationalCommitteeHode
     */
    public void setNationalCommitteeHode(String nationalCommitteeHode) {
        this.nationalCommitteeHode = nationalCommitteeHode == null ? null : nationalCommitteeHode.trim();
    }
    /**
     * GET 作为委员参与国家/行业标准化技术委员会的数量
     * @return nationalCommitteeJoin
     */
    public String getNationalCommitteeJoin() {
        return nationalCommitteeJoin;
    }

    /**
     * SET 作为委员参与国家/行业标准化技术委员会的数量
     * @param nationalCommitteeJoin
     */
    public void setNationalCommitteeJoin(String nationalCommitteeJoin) {
        this.nationalCommitteeJoin = nationalCommitteeJoin == null ? null : nationalCommitteeJoin.trim();
    }
    /**
     * GET 作为秘书处参与地方标准化技术委员会的数量
     * @return localCommitteeInter
     */
    public String getLocalCommitteeInter() {
        return localCommitteeInter;
    }

    /**
     * SET 作为秘书处参与地方标准化技术委员会的数量
     * @param localCommitteeInter
     */
    public void setLocalCommitteeInter(String localCommitteeInter) {
        this.localCommitteeInter = localCommitteeInter == null ? null : localCommitteeInter.trim();
    }
    /**
     * GET 作为秘书处参与学会/团体标准化委员会的数量
     * @return teamCommitteeInter
     */
    public String getTeamCommitteeInter() {
        return teamCommitteeInter;
    }

    /**
     * SET 作为秘书处参与学会/团体标准化委员会的数量
     * @param teamCommitteeInter
     */
    public void setTeamCommitteeInter(String teamCommitteeInter) {
        this.teamCommitteeInter = teamCommitteeInter == null ? null : teamCommitteeInter.trim();
    }
    /**
     * GET  当年获得发明专利授权的数量
     * @return inventionPatentAuth
     */
    public String getInventionPatentAuth() {
        return inventionPatentAuth;
    }

    /**
     * SET  当年获得发明专利授权的数量
     * @param inventionPatentAuth
     */
    public void setInventionPatentAuth(String inventionPatentAuth) {
        this.inventionPatentAuth = inventionPatentAuth == null ? null : inventionPatentAuth.trim();
    }
    /**
     * GET  当年获得实用新型专利授权的数量
     * @return utilityModelAuth
     */
    public String getUtilityModelAuth() {
        return utilityModelAuth;
    }

    /**
     * SET  当年获得实用新型专利授权的数量
     * @param utilityModelAuth
     */
    public void setUtilityModelAuth(String utilityModelAuth) {
        this.utilityModelAuth = utilityModelAuth == null ? null : utilityModelAuth.trim();
    }
    /**
     * GET  当年获得外观设计专利授权的数量
     * @return outwordAuth
     */
    public String getOutwordAuth() {
        return outwordAuth;
    }

    /**
     * SET  当年获得外观设计专利授权的数量
     * @param outwordAuth
     */
    public void setOutwordAuth(String outwordAuth) {
        this.outwordAuth = outwordAuth == null ? null : outwordAuth.trim();
    }
    /**
     * GET  当年获得软件著作权授权的数量
     * @return softwareCopyright
     */
    public String getSoftwareCopyright() {
        return softwareCopyright;
    }

    /**
     * SET  当年获得软件著作权授权的数量
     * @param softwareCopyright
     */
    public void setSoftwareCopyright(String softwareCopyright) {
        this.softwareCopyright = softwareCopyright == null ? null : softwareCopyright.trim();
    }
    /**
     * GET  当年科研经费投入金额
     * @return fundTotal
     */
    public String getFundTotal() {
        return fundTotal;
    }

    /**
     * SET  当年科研经费投入金额
     * @param fundTotal
     */
    public void setFundTotal(String fundTotal) {
        this.fundTotal = fundTotal == null ? null : fundTotal.trim();
    }
    /**
     * GET  当年新增国家级科研项目立项数新增
     * @return nationalProjactAdd
     */
    public String getNationalProjactAdd() {
        return nationalProjactAdd;
    }

    /**
     * SET  当年新增国家级科研项目立项数新增
     * @param nationalProjactAdd
     */
    public void setNationalProjactAdd(String nationalProjactAdd) {
        this.nationalProjactAdd = nationalProjactAdd == null ? null : nationalProjactAdd.trim();
    }
    /**
     * GET  当年新增国家级科研项目立项数参与
     * @return nationalProjactInter
     */
    public String getNationalProjactInter() {
        return nationalProjactInter;
    }

    /**
     * SET  当年新增国家级科研项目立项数参与
     * @param nationalProjactInter
     */
    public void setNationalProjactInter(String nationalProjactInter) {
        this.nationalProjactInter = nationalProjactInter == null ? null : nationalProjactInter.trim();
    }
    /**
     * GET  当年新增省部级科研项目立项数新增
     * @return provinceProjactAdd
     */
    public String getProvinceProjactAdd() {
        return provinceProjactAdd;
    }

    /**
     * SET  当年新增省部级科研项目立项数新增
     * @param provinceProjactAdd
     */
    public void setProvinceProjactAdd(String provinceProjactAdd) {
        this.provinceProjactAdd = provinceProjactAdd == null ? null : provinceProjactAdd.trim();
    }
    /**
     * GET  当年新增省部级科研项目立项数参与
     * @return provinceProjactInter
     */
    public String getProvinceProjactInter() {
        return provinceProjactInter;
    }

    /**
     * SET  当年新增省部级科研项目立项数参与
     * @param provinceProjactInter
     */
    public void setProvinceProjactInter(String provinceProjactInter) {
        this.provinceProjactInter = provinceProjactInter == null ? null : provinceProjactInter.trim();
    }
    /**
     * GET  当年新增主持地市级科研项目立项数
     * @return cityProjactAdd
     */
    public String getCityProjactAdd() {
        return cityProjactAdd;
    }

    /**
     * SET  当年新增主持地市级科研项目立项数
     * @param cityProjactAdd
     */
    public void setCityProjactAdd(String cityProjactAdd) {
        this.cityProjactAdd = cityProjactAdd == null ? null : cityProjactAdd.trim();
    }
    /**
     * GET 
     * @return cityProjactInner
     */
    public String getCityProjactInner() {
        return cityProjactInner;
    }

    /**
     * SET 
     * @param cityProjactInner
     */
    public void setCityProjactInner(String cityProjactInner) {
        this.cityProjactInner = cityProjactInner == null ? null : cityProjactInner.trim();
    }
    /**
     * GET  当年新增主持或参与发布国际标准制修订数
     * @return internationalStandard
     */
    public String getInternationalStandard() {
        return internationalStandard;
    }

    /**
     * SET  当年新增主持或参与发布国际标准制修订数
     * @param internationalStandard
     */
    public void setInternationalStandard(String internationalStandard) {
        this.internationalStandard = internationalStandard == null ? null : internationalStandard.trim();
    }
    /**
     * GET  当年新增发布国家/行业标准制修订数主持
     * @return nationalStandardAdd
     */
    public String getNationalStandardAdd() {
        return nationalStandardAdd;
    }

    /**
     * SET  当年新增发布国家/行业标准制修订数主持
     * @param nationalStandardAdd
     */
    public void setNationalStandardAdd(String nationalStandardAdd) {
        this.nationalStandardAdd = nationalStandardAdd == null ? null : nationalStandardAdd.trim();
    }
    /**
     * GET  当年新增发布国家/行业标准制修订数参与
     * @return nationalStandardEnter
     */
    public String getNationalStandardEnter() {
        return nationalStandardEnter;
    }

    /**
     * SET  当年新增发布国家/行业标准制修订数参与
     * @param nationalStandardEnter
     */
    public void setNationalStandardEnter(String nationalStandardEnter) {
        this.nationalStandardEnter = nationalStandardEnter == null ? null : nationalStandardEnter.trim();
    }
    /**
     * GET  当年新增发布地方/团体标准制修订数主持
     * @return countryStandardAdd
     */
    public String getCountryStandardAdd() {
        return countryStandardAdd;
    }

    /**
     * SET  当年新增发布地方/团体标准制修订数主持
     * @param countryStandardAdd
     */
    public void setCountryStandardAdd(String countryStandardAdd) {
        this.countryStandardAdd = countryStandardAdd == null ? null : countryStandardAdd.trim();
    }
    /**
     * GET  当年新增发布地方/团体标准制修订数参与
     * @return countryStandardEnter
     */
    public String getCountryStandardEnter() {
        return countryStandardEnter;
    }

    /**
     * SET  当年新增发布地方/团体标准制修订数参与
     * @param countryStandardEnter
     */
    public void setCountryStandardEnter(String countryStandardEnter) {
        this.countryStandardEnter = countryStandardEnter == null ? null : countryStandardEnter.trim();
    }
    /**
     * GET  当年主持或参与的标准制修订数 国家标准
     * @return nationalStandard
     */
    public String getNationalStandard() {
        return nationalStandard;
    }

    /**
     * SET  当年主持或参与的标准制修订数 国家标准
     * @param nationalStandard
     */
    public void setNationalStandard(String nationalStandard) {
        this.nationalStandard = nationalStandard == null ? null : nationalStandard.trim();
    }
    /**
     * GET  当年主持或参与的标准制修订数 行业标准
     * @return industryStandard
     */
    public String getIndustryStandard() {
        return industryStandard;
    }

    /**
     * SET  当年主持或参与的标准制修订数 行业标准
     * @param industryStandard
     */
    public void setIndustryStandard(String industryStandard) {
        this.industryStandard = industryStandard == null ? null : industryStandard.trim();
    }
    /**
     * GET  当年主持或参与的标准制修订数 地方标准
     * @return localStandard
     */
    public String getLocalStandard() {
        return localStandard;
    }

    /**
     * SET  当年主持或参与的标准制修订数 地方标准
     * @param localStandard
     */
    public void setLocalStandard(String localStandard) {
        this.localStandard = localStandard == null ? null : localStandard.trim();
    }
    /**
     * GET  当年主持或参与的标准制修订数 团体标准
     * @return groupStandard
     */
    public String getGroupStandard() {
        return groupStandard;
    }

    /**
     * SET  当年主持或参与的标准制修订数 团体标准
     * @param groupStandard
     */
    public void setGroupStandard(String groupStandard) {
        this.groupStandard = groupStandard == null ? null : groupStandard.trim();
    }
    /**
     * GET  当年发表的论文数SCI
     * @return sciPaper
     */
    public String getSciPaper() {
        return sciPaper;
    }

    /**
     * SET  当年发表的论文数SCI
     * @param sciPaper
     */
    public void setSciPaper(String sciPaper) {
        this.sciPaper = sciPaper == null ? null : sciPaper.trim();
    }
    /**
     * GET  当年发表的论文数核心
     * @return corePaper
     */
    public String getCorePaper() {
        return corePaper;
    }

    /**
     * SET  当年发表的论文数核心
     * @param corePaper
     */
    public void setCorePaper(String corePaper) {
        this.corePaper = corePaper == null ? null : corePaper.trim();
    }
    /**
     * GET  当年发表的论文数一般
     * @return narmalPaper
     */
    public String getNarmalPaper() {
        return narmalPaper;
    }

    /**
     * SET  当年发表的论文数一般
     * @param narmalPaper
     */
    public void setNarmalPaper(String narmalPaper) {
        this.narmalPaper = narmalPaper == null ? null : narmalPaper.trim();
    }
    /**
     * GET  当年获得的奖励数 国家级
     * @return nationalAward
     */
    public String getNationalAward() {
        return nationalAward;
    }

    /**
     * SET  当年获得的奖励数 国家级
     * @param nationalAward
     */
    public void setNationalAward(String nationalAward) {
        this.nationalAward = nationalAward == null ? null : nationalAward.trim();
    }
    /**
     * GET  当年获得的奖励数 省级
     * @return provinceAward
     */
    public String getProvinceAward() {
        return provinceAward;
    }

    /**
     * SET  当年获得的奖励数 省级
     * @param provinceAward
     */
    public void setProvinceAward(String provinceAward) {
        this.provinceAward = provinceAward == null ? null : provinceAward.trim();
    }
    /**
     * GET  当年获得的奖励数 社会科学技术
     * @return socialAward
     */
    public String getSocialAward() {
        return socialAward;
    }

    /**
     * SET  当年获得的奖励数 社会科学技术
     * @param socialAward
     */
    public void setSocialAward(String socialAward) {
        this.socialAward = socialAward == null ? null : socialAward.trim();
    }
    /**
     * GET  当年成果转化情况 转化项目数
     * @return awardSellCount
     */
    public String getAwardSellCount() {
        return awardSellCount;
    }

    /**
     * SET  当年成果转化情况 转化项目数
     * @param awardSellCount
     */
    public void setAwardSellCount(String awardSellCount) {
        this.awardSellCount = awardSellCount == null ? null : awardSellCount.trim();
    }
    /**
     * GET  当年成果转化情况 转化金额（万元）
     * @return awardSellPrice
     */
    public String getAwardSellPrice() {
        return awardSellPrice;
    }

    /**
     * SET  当年成果转化情况 转化金额（万元）
     * @param awardSellPrice
     */
    public void setAwardSellPrice(String awardSellPrice) {
        this.awardSellPrice = awardSellPrice == null ? null : awardSellPrice.trim();
    }
    /**
     * GET  检验业务状况 当年检验总批次
     * @return testTotal
     */
    public String getTestTotal() {
        return testTotal;
    }

    /**
     * SET  检验业务状况 当年检验总批次
     * @param testTotal
     */
    public void setTestTotal(String testTotal) {
        this.testTotal = testTotal == null ? null : testTotal.trim();
    }
    /**
     * GET  检验业务状况 当年检验业务总收入（万元）
     * @return testPriceTotal
     */
    public String getTestPriceTotal() {
        return testPriceTotal;
    }

    /**
     * SET  检验业务状况 当年检验业务总收入（万元）
     * @param testPriceTotal
     */
    public void setTestPriceTotal(String testPriceTotal) {
        this.testPriceTotal = testPriceTotal == null ? null : testPriceTotal.trim();
    }
    /**
     * GET  检验业务状况 承担当年国家市场监管总局下达的产品质量监督任务数量
     * @return testNationalCount
     */
    public String getTestNationalCount() {
        return testNationalCount;
    }

    /**
     * SET  检验业务状况 承担当年国家市场监管总局下达的产品质量监督任务数量
     * @param testNationalCount
     */
    public void setTestNationalCount(String testNationalCount) {
        this.testNationalCount = testNationalCount == null ? null : testNationalCount.trim();
    }
    /**
     * GET  检验业务状况 承担当年省级市场监管局下达的产品质量监督任务数量
     * @return testProvinceCount
     */
    public String getTestProvinceCount() {
        return testProvinceCount;
    }

    /**
     * SET  检验业务状况 承担当年省级市场监管局下达的产品质量监督任务数量
     * @param testProvinceCount
     */
    public void setTestProvinceCount(String testProvinceCount) {
        this.testProvinceCount = testProvinceCount == null ? null : testProvinceCount.trim();
    }
    /**
     * GET  检验业务状况 承担当年省级（不含）以下市场监管部门下达的产品质量监督任务数量
     * @return testCityCount
     */
    public String getTestCityCount() {
        return testCityCount;
    }

    /**
     * SET  检验业务状况 承担当年省级（不含）以下市场监管部门下达的产品质量监督任务数量
     * @param testCityCount
     */
    public void setTestCityCount(String testCityCount) {
        this.testCityCount = testCityCount == null ? null : testCityCount.trim();
    }
    /**
     * GET  检验业务状况 承担当年行业部委下达的产品质量监督任务数量
     * @return testIndustryCount
     */
    public String getTestIndustryCount() {
        return testIndustryCount;
    }

    /**
     * SET  检验业务状况 承担当年行业部委下达的产品质量监督任务数量
     * @param testIndustryCount
     */
    public void setTestIndustryCount(String testIndustryCount) {
        this.testIndustryCount = testIndustryCount == null ? null : testIndustryCount.trim();
    }
    /**
     * GET  当年服务中小微企业 开放实验室（批次）
     * @return openLiboratory
     */
    public String getOpenLiboratory() {
        return openLiboratory;
    }

    /**
     * SET  当年服务中小微企业 开放实验室（批次）
     * @param openLiboratory
     */
    public void setOpenLiboratory(String openLiboratory) {
        this.openLiboratory = openLiboratory == null ? null : openLiboratory.trim();
    }
    /**
     * GET 减免委托检测费（万元）
     * @return comminicationCount
     */
    public BigDecimal getComminicationCount() {
        return comminicationCount;
    }

    /**
     * SET 减免委托检测费（万元）
     * @param comminicationCount
     */
    public void setComminicationCount(BigDecimal comminicationCount) {
        this.comminicationCount = comminicationCount;
    }
    /**
     * GET  当年服务中小微企业的数量
     * @return serviceCount
     */
    public Integer getServiceCount() {
        return serviceCount;
    }

    /**
     * SET  当年服务中小微企业的数量
     * @param serviceCount
     */
    public void setServiceCount(Integer serviceCount) {
        this.serviceCount = serviceCount;
    }
    /**
     * GET 
     * @return flowStatus
     */
    public String getFlowStatus() {
        return flowStatus;
    }

    /**
     * SET 
     * @param flowStatus
     */
    public void setFlowStatus(String flowStatus) {
        this.flowStatus = flowStatus == null ? null : flowStatus.trim();
    }
    /**
     * GET 
     * @return flowUser
     */
    public String getFlowUser() {
        return flowUser;
    }

    /**
     * SET 
     * @param flowUser
     */
    public void setFlowUser(String flowUser) {
        this.flowUser = flowUser == null ? null : flowUser.trim();
    }
    /**
     * GET 
     * @return flowUserName
     */
    public String getFlowUserName() {
        return flowUserName;
    }

    /**
     * SET 
     * @param flowUserName
     */
    public void setFlowUserName(String flowUserName) {
        this.flowUserName = flowUserName == null ? null : flowUserName.trim();
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}