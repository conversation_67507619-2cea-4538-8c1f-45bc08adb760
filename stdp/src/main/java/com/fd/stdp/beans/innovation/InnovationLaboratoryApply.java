package com.fd.stdp.beans.innovation;

import com.lzhpo.sensitive.SensitiveStrategy;
import com.lzhpo.sensitive.annocation.Sensitive;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fd.stdp.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.annotation.ColumnType;
import javax.persistence.*;
import java.math.BigDecimal;

/**
 *@Description: 省局重点实验室培育申报
 *@Author: wangsh
 *@Date: 2022-03-13 14:19:37
 */
@Table(name = "INNOVATION_LABORATORY_APPLY")
@ApiModel(value = "com.fd.stdp.beans.innovation.InnovationLaboratoryApply", description = "省局重点实验室培育申报")
public class InnovationLaboratoryApply extends BaseEntity {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
    @Column(name = "ORG_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构code")
    private String orgCode;
    
    @Column(name = "ORG_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="机构名称")
    private String orgName;
    
    @Column(name = "CONTRACT_ID")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="任务书id")
    private String contractId;
    
    @Column(name = "LABORATORY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="实验室名称")
    private String laboratory;
    
    @Column(name = "INDUSTRY_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="所属行业")
    private String industryType;
    
    @Column(name = "RESEARCH_TYPE_CODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="研究领域")
    private String researchTypeCode;
    
    @Column(name = "RESEARCH_TYPE_TEXT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="研究领域")
    private String researchTypeText;
    
    @Column(name = "RESEARCH_TYPE_TEXT_ELSE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="研究领域其它文本")
    private String researchTypeTextElse;
    
    @Column(name = "RESEARCH_DIRECTION")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="研究方向")
    private String researchDirection;
    
    @Column(name = "IS_CITY_LABORATORY")
    @ColumnType(jdbcType = JdbcType.INTEGER)
    @ApiModelProperty(value="已获批市级以上重点实验室 1：是 0：否")
    private Integer isCityLaboratory;
    
    @Column(name = "CITY_LABORATORY_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="获批市级以上重点实验室 名称")
    private String cityLaboratoryName;
    
    @Column(name = "LEADER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="重点实验室主任")
    @Sensitive(strategy = SensitiveStrategy.CHINESE_NAME)
    private String leaderName;
    
    @Column(name = "LEADER_UNIT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="单位")
    private String leaderUnitName;
    
    @Column(name = "LEADER_BIRTHDAY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="出生年月")
    private String leaderBirthday;
    
    @Column(name = "LEADER_SEX")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="性别")
    private String leaderSex;
    
    @Column(name = "LEADER_TITAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="职称")
    private String leaderTital;
    
    @Column(name = "LEADER_DEGREE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="学历学位")
    private String leaderDegree;
    
    @Column(name = "LEADER_TEL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="联系电话")
    @Sensitive(strategy = SensitiveStrategy.MOBILE_PHONE)
    private String leaderTel;
    
    @Column(name = "LEADER_EMAIL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="电子邮箱")
    @Sensitive(strategy = SensitiveStrategy.EMAIL)
    private String leaderEmail;
    
    @Column(name = "APPLY_UNIT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="申报单位名称")
    private String applyUnitName;
    
    @Column(name = "APPLY_UNIT_USCC")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="统一社会信用代码/组织机构代码")
    private String applyUnitUscc;
    
    @Column(name = "APPLY_UNIT_LEADER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="单位负责人")
    @Sensitive(strategy = SensitiveStrategy.CHINESE_NAME)
    private String applyUnitLeaderName;
    
    @Column(name = "APPLY_UNIT_LEADER_TITAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="职务/职称")
    private String applyUnitLeaderTital;
    
    @Column(name = "APPLY_UNIT_LINKED_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="联系人")
    @Sensitive(strategy = SensitiveStrategy.CHINESE_NAME)
    private String applyUnitLinkedName;
    
    @Column(name = "APPLY_UNIT_LINKED_TITAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="职务/职称")
    private String applyUnitLinkedTital;
    
    @Column(name = "APPLY_UNIT_TEL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="联系电话")
    @Sensitive(strategy = SensitiveStrategy.MOBILE_PHONE)
    private String applyUnitTel;
    
    @Column(name = "APPLY_UNIT_EMAIL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="电子邮箱")
    @Sensitive(strategy = SensitiveStrategy.EMAIL)
    private String applyUnitEmail;
    
    @Column(name = "APPLY_UNIT_POSTCODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="邮    编")
    private String applyUnitPostcode;
    
    @Column(name = "APPLY_UNIT_ADDRESS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="通讯地址")
    @Sensitive(strategy = SensitiveStrategy.ADDRESS)
    private String applyUnitAddress;
    
    @Column(name = "RECOMMEND_UNIT_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="推荐单位名称")
    private String recommendUnitName;
    
    @Column(name = "RECOMMEND_UNIT_LINKED_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="联系人")
    @Sensitive(strategy = SensitiveStrategy.CHINESE_NAME)
    private String recommendUnitLinkedName;
    
    @Column(name = "RECOMMEND_UNIT_LINKED_TITAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="职务/职称")
    private String recommendUnitLinkedTital;
    
    @Column(name = "RECOMMEND_UNIT_TEL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @Sensitive(strategy = SensitiveStrategy.MOBILE_PHONE)
    @ApiModelProperty(value="联系电话")
    private String recommendUnitTel;
    
    @Column(name = "RECOMMEND_UNIT_FAX")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="联系电话")
    @Sensitive(strategy = SensitiveStrategy.MOBILE_PHONE)
    private String recommendUnitFax;
    
    @Column(name = "RECOMMEND_UNIT_EMAIL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="电子邮箱")
    @Sensitive(strategy = SensitiveStrategy.EMAIL)
    private String recommendUnitEmail;
    
    @Column(name = "RECOMMEND_UNIT_POSTCODE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="邮    编")
    private String recommendUnitPostcode;
    
    @Column(name = "RECOMMEND_UNIT_ADDRESS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="通讯地址")
    @Sensitive(strategy = SensitiveStrategy.ADDRESS)
    private String recommendUnitAddress;
    
    @Column(name = "GOAL")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="重点实验室建设的目的和意义")
    private String goal;
    
    @Column(name = "CONTENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="国内外概况")
    private String content;
    
    @Column(name = "WORK_BASIS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="现有研究工作基础、水平等")
    private String workBasis;
    
    @Column(name = "RESEARCH_TARGET")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="重点实验室研究方向和主要研究内容 重点实验室建设任务书的主要内容及指标")
    private String researchTarget;
    
    @Column(name = "WORK_PLANNING")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="主要工作规划、预期目标和水平")
    private String workPlanning;
    
    @Column(name = "EXPERIMENTAL_CONDITION")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="已具备的实验条件")
    private String experimentalCondition;
    
    @Column(name = "TEAM_ABILITY")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="科研队伍状况及人才培养能力")
    private String teamAbility;
    
    @Column(name = "MAIN_INFO")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="重点实验室基本情况")
    private String mainInfo;
    
    @Column(name = "RESEARCH_AWARD")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="重点实验室建设期间研究工作进展")
    private String researchAward;
    
    @Column(name = "RESEARCH_PLAN")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="重点实验室建设计划执行情况概述")
    private String researchPlan;
    
    @Column(name = "RESEARCH_SUBJECT_CONTENT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="学科建设、人才培养与国内外学术交流情况")
    private String researchSubjectContent;
    
    @Column(name = "LABORATORY_MANAGER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="重点实验室组织结构与管理制度、模式")
    private String laboratoryManager;
    
    @Column(name = "LABORATORY_NEXT_PLAN")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="重点实验室中长期工作设想")
    private String laboratoryNextPlan;
    
    @Column(name = "LABORATORY_SUPPORT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="依托单位给予的支持情况")
    private String laboratorySupport;
    
    @Column(name = "MEET_CONDITION_TEXT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="符合申请直接命名的条件")
    private String meetConditionText;
    
    @Column(name = "MEET_CONDITION_VALUE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="符合申请直接命名的条件 详细情况")
    private String meetConditionValue;
    
    @Column(name = "YEAR_NO")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="工作报告年度")
    private String yearNo;
    
    @Column(name = "MARKET_SUPPORT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="科研成果转化及对市场监管支撑情况")
    private String marketSupport;
    
    @Column(name = "CITY_SUPPORT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="该年度市局和所在地市给予的支持情况")
    private String citySupport;
    
    @Column(name = "FUND_STATUS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="该年度运行与开放经费及使用情况")
    private String fundStatus;
    
    @Column(name = "RESULT")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String result;
    
    @Column(name = "OPINION")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String opinion;
    
    @Column(name = "FORM_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String formType;
    
    @Column(name = "APPLY_TYPE")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="申请类型：培育申报/直接命名申报/培育评估/验收申请/中期评估/年度总结")
    private String applyType;
    
    @Column(name = "FLOW_STATUS")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowStatus;
    
    @Column(name = "FLOW_USER")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowUser;
    
    @Column(name = "FLOW_USER_NAME")
    @ColumnType(jdbcType = JdbcType.VARCHAR)
    @ApiModelProperty(value="")
    private String flowUserName;
    
    
    /**
     * GET 机构code
     * @return orgCode
     */
    public String getOrgCode() {
        return orgCode;
    }

    /**
     * SET 机构code
     * @param orgCode
     */
    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }
    /**
     * GET 机构名称
     * @return orgName
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * SET 机构名称
     * @param orgName
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }
    /**
     * GET 任务书id
     * @return contractId
     */
    public String getContractId() {
        return contractId;
    }

    /**
     * SET 任务书id
     * @param contractId
     */
    public void setContractId(String contractId) {
        this.contractId = contractId == null ? null : contractId.trim();
    }
    /**
     * GET 实验室名称
     * @return laboratory
     */
    public String getLaboratory() {
        return laboratory;
    }

    /**
     * SET 实验室名称
     * @param laboratory
     */
    public void setLaboratory(String laboratory) {
        this.laboratory = laboratory == null ? null : laboratory.trim();
    }
    /**
     * GET 所属行业
     * @return industryType
     */
    public String getIndustryType() {
        return industryType;
    }

    /**
     * SET 所属行业
     * @param industryType
     */
    public void setIndustryType(String industryType) {
        this.industryType = industryType == null ? null : industryType.trim();
    }
    /**
     * GET 研究领域
     * @return researchTypeCode
     */
    public String getResearchTypeCode() {
        return researchTypeCode;
    }

    /**
     * SET 研究领域
     * @param researchTypeCode
     */
    public void setResearchTypeCode(String researchTypeCode) {
        this.researchTypeCode = researchTypeCode == null ? null : researchTypeCode.trim();
    }
    /**
     * GET 研究领域
     * @return researchTypeText
     */
    public String getResearchTypeText() {
        return researchTypeText;
    }

    /**
     * SET 研究领域
     * @param researchTypeText
     */
    public void setResearchTypeText(String researchTypeText) {
        this.researchTypeText = researchTypeText == null ? null : researchTypeText.trim();
    }
    /**
     * GET 研究领域其它文本
     * @return researchTypeTextElse
     */
    public String getResearchTypeTextElse() {
        return researchTypeTextElse;
    }

    /**
     * SET 研究领域其它文本
     * @param researchTypeTextElse
     */
    public void setResearchTypeTextElse(String researchTypeTextElse) {
        this.researchTypeTextElse = researchTypeTextElse == null ? null : researchTypeTextElse.trim();
    }
    /**
     * GET 研究方向
     * @return researchDirection
     */
    public String getResearchDirection() {
        return researchDirection;
    }

    /**
     * SET 研究方向
     * @param researchDirection
     */
    public void setResearchDirection(String researchDirection) {
        this.researchDirection = researchDirection == null ? null : researchDirection.trim();
    }
    /**
     * GET 已获批市级以上重点实验室 1：是 0：否
     * @return isCityLaboratory
     */
    public Integer getIsCityLaboratory() {
        return isCityLaboratory;
    }

    /**
     * SET 已获批市级以上重点实验室 1：是 0：否
     * @param isCityLaboratory
     */
    public void setIsCityLaboratory(Integer isCityLaboratory) {
        this.isCityLaboratory = isCityLaboratory;
    }
    /**
     * GET 获批市级以上重点实验室 名称
     * @return cityLaboratoryName
     */
    public String getCityLaboratoryName() {
        return cityLaboratoryName;
    }

    /**
     * SET 获批市级以上重点实验室 名称
     * @param cityLaboratoryName
     */
    public void setCityLaboratoryName(String cityLaboratoryName) {
        this.cityLaboratoryName = cityLaboratoryName == null ? null : cityLaboratoryName.trim();
    }
    /**
     * GET 重点实验室主任
     * @return leaderName
     */
    public String getLeaderName() {
        return leaderName;
    }

    /**
     * SET 重点实验室主任
     * @param leaderName
     */
    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName == null ? null : leaderName.trim();
    }
    /**
     * GET 单位
     * @return leaderUnitName
     */
    public String getLeaderUnitName() {
        return leaderUnitName;
    }

    /**
     * SET 单位
     * @param leaderUnitName
     */
    public void setLeaderUnitName(String leaderUnitName) {
        this.leaderUnitName = leaderUnitName == null ? null : leaderUnitName.trim();
    }
    /**
     * GET 出生年月
     * @return leaderBirthday
     */
    public String getLeaderBirthday() {
        return leaderBirthday;
    }

    /**
     * SET 出生年月
     * @param leaderBirthday
     */
    public void setLeaderBirthday(String leaderBirthday) {
        this.leaderBirthday = leaderBirthday == null ? null : leaderBirthday.trim();
    }
    /**
     * GET 性别
     * @return leaderSex
     */
    public String getLeaderSex() {
        return leaderSex;
    }

    /**
     * SET 性别
     * @param leaderSex
     */
    public void setLeaderSex(String leaderSex) {
        this.leaderSex = leaderSex == null ? null : leaderSex.trim();
    }
    /**
     * GET 职称
     * @return leaderTital
     */
    public String getLeaderTital() {
        return leaderTital;
    }

    /**
     * SET 职称
     * @param leaderTital
     */
    public void setLeaderTital(String leaderTital) {
        this.leaderTital = leaderTital == null ? null : leaderTital.trim();
    }
    /**
     * GET 学历学位
     * @return leaderDegree
     */
    public String getLeaderDegree() {
        return leaderDegree;
    }

    /**
     * SET 学历学位
     * @param leaderDegree
     */
    public void setLeaderDegree(String leaderDegree) {
        this.leaderDegree = leaderDegree == null ? null : leaderDegree.trim();
    }
    /**
     * GET 联系电话
     * @return leaderTel
     */
    public String getLeaderTel() {
        return leaderTel;
    }

    /**
     * SET 联系电话
     * @param leaderTel
     */
    public void setLeaderTel(String leaderTel) {
        this.leaderTel = leaderTel == null ? null : leaderTel.trim();
    }
    /**
     * GET 电子邮箱
     * @return leaderEmail
     */
    public String getLeaderEmail() {
        return leaderEmail;
    }

    /**
     * SET 电子邮箱
     * @param leaderEmail
     */
    public void setLeaderEmail(String leaderEmail) {
        this.leaderEmail = leaderEmail == null ? null : leaderEmail.trim();
    }
    /**
     * GET 申报单位名称
     * @return applyUnitName
     */
    public String getApplyUnitName() {
        return applyUnitName;
    }

    /**
     * SET 申报单位名称
     * @param applyUnitName
     */
    public void setApplyUnitName(String applyUnitName) {
        this.applyUnitName = applyUnitName == null ? null : applyUnitName.trim();
    }
    /**
     * GET 统一社会信用代码/组织机构代码
     * @return applyUnitUscc
     */
    public String getApplyUnitUscc() {
        return applyUnitUscc;
    }

    /**
     * SET 统一社会信用代码/组织机构代码
     * @param applyUnitUscc
     */
    public void setApplyUnitUscc(String applyUnitUscc) {
        this.applyUnitUscc = applyUnitUscc == null ? null : applyUnitUscc.trim();
    }
    /**
     * GET 单位负责人
     * @return applyUnitLeaderName
     */
    public String getApplyUnitLeaderName() {
        return applyUnitLeaderName;
    }

    /**
     * SET 单位负责人
     * @param applyUnitLeaderName
     */
    public void setApplyUnitLeaderName(String applyUnitLeaderName) {
        this.applyUnitLeaderName = applyUnitLeaderName == null ? null : applyUnitLeaderName.trim();
    }
    /**
     * GET 职务/职称
     * @return applyUnitLeaderTital
     */
    public String getApplyUnitLeaderTital() {
        return applyUnitLeaderTital;
    }

    /**
     * SET 职务/职称
     * @param applyUnitLeaderTital
     */
    public void setApplyUnitLeaderTital(String applyUnitLeaderTital) {
        this.applyUnitLeaderTital = applyUnitLeaderTital == null ? null : applyUnitLeaderTital.trim();
    }
    /**
     * GET 联系人
     * @return applyUnitLinkedName
     */
    public String getApplyUnitLinkedName() {
        return applyUnitLinkedName;
    }

    /**
     * SET 联系人
     * @param applyUnitLinkedName
     */
    public void setApplyUnitLinkedName(String applyUnitLinkedName) {
        this.applyUnitLinkedName = applyUnitLinkedName == null ? null : applyUnitLinkedName.trim();
    }
    /**
     * GET 职务/职称
     * @return applyUnitLinkedTital
     */
    public String getApplyUnitLinkedTital() {
        return applyUnitLinkedTital;
    }

    /**
     * SET 职务/职称
     * @param applyUnitLinkedTital
     */
    public void setApplyUnitLinkedTital(String applyUnitLinkedTital) {
        this.applyUnitLinkedTital = applyUnitLinkedTital == null ? null : applyUnitLinkedTital.trim();
    }
    /**
     * GET 联系电话
     * @return applyUnitTel
     */
    public String getApplyUnitTel() {
        return applyUnitTel;
    }

    /**
     * SET 联系电话
     * @param applyUnitTel
     */
    public void setApplyUnitTel(String applyUnitTel) {
        this.applyUnitTel = applyUnitTel == null ? null : applyUnitTel.trim();
    }
    /**
     * GET 电子邮箱
     * @return applyUnitEmail
     */
    public String getApplyUnitEmail() {
        return applyUnitEmail;
    }

    /**
     * SET 电子邮箱
     * @param applyUnitEmail
     */
    public void setApplyUnitEmail(String applyUnitEmail) {
        this.applyUnitEmail = applyUnitEmail == null ? null : applyUnitEmail.trim();
    }
    /**
     * GET 邮    编
     * @return applyUnitPostcode
     */
    public String getApplyUnitPostcode() {
        return applyUnitPostcode;
    }

    /**
     * SET 邮    编
     * @param applyUnitPostcode
     */
    public void setApplyUnitPostcode(String applyUnitPostcode) {
        this.applyUnitPostcode = applyUnitPostcode == null ? null : applyUnitPostcode.trim();
    }
    /**
     * GET 通讯地址
     * @return applyUnitAddress
     */
    public String getApplyUnitAddress() {
        return applyUnitAddress;
    }

    /**
     * SET 通讯地址
     * @param applyUnitAddress
     */
    public void setApplyUnitAddress(String applyUnitAddress) {
        this.applyUnitAddress = applyUnitAddress == null ? null : applyUnitAddress.trim();
    }
    /**
     * GET 推荐单位名称
     * @return recommendUnitName
     */
    public String getRecommendUnitName() {
        return recommendUnitName;
    }

    /**
     * SET 推荐单位名称
     * @param recommendUnitName
     */
    public void setRecommendUnitName(String recommendUnitName) {
        this.recommendUnitName = recommendUnitName == null ? null : recommendUnitName.trim();
    }
    /**
     * GET 联系人
     * @return recommendUnitLinkedName
     */
    public String getRecommendUnitLinkedName() {
        return recommendUnitLinkedName;
    }

    /**
     * SET 联系人
     * @param recommendUnitLinkedName
     */
    public void setRecommendUnitLinkedName(String recommendUnitLinkedName) {
        this.recommendUnitLinkedName = recommendUnitLinkedName == null ? null : recommendUnitLinkedName.trim();
    }
    /**
     * GET 职务/职称
     * @return recommendUnitLinkedTital
     */
    public String getRecommendUnitLinkedTital() {
        return recommendUnitLinkedTital;
    }

    /**
     * SET 职务/职称
     * @param recommendUnitLinkedTital
     */
    public void setRecommendUnitLinkedTital(String recommendUnitLinkedTital) {
        this.recommendUnitLinkedTital = recommendUnitLinkedTital == null ? null : recommendUnitLinkedTital.trim();
    }
    /**
     * GET 联系电话
     * @return recommendUnitTel
     */
    public String getRecommendUnitTel() {
        return recommendUnitTel;
    }

    /**
     * SET 联系电话
     * @param recommendUnitTel
     */
    public void setRecommendUnitTel(String recommendUnitTel) {
        this.recommendUnitTel = recommendUnitTel == null ? null : recommendUnitTel.trim();
    }
    /**
     * GET 联系电话
     * @return recommendUnitFax
     */
    public String getRecommendUnitFax() {
        return recommendUnitFax;
    }

    /**
     * SET 联系电话
     * @param recommendUnitFax
     */
    public void setRecommendUnitFax(String recommendUnitFax) {
        this.recommendUnitFax = recommendUnitFax == null ? null : recommendUnitFax.trim();
    }
    /**
     * GET 电子邮箱
     * @return recommendUnitEmail
     */
    public String getRecommendUnitEmail() {
        return recommendUnitEmail;
    }

    /**
     * SET 电子邮箱
     * @param recommendUnitEmail
     */
    public void setRecommendUnitEmail(String recommendUnitEmail) {
        this.recommendUnitEmail = recommendUnitEmail == null ? null : recommendUnitEmail.trim();
    }
    /**
     * GET 邮    编
     * @return recommendUnitPostcode
     */
    public String getRecommendUnitPostcode() {
        return recommendUnitPostcode;
    }

    /**
     * SET 邮    编
     * @param recommendUnitPostcode
     */
    public void setRecommendUnitPostcode(String recommendUnitPostcode) {
        this.recommendUnitPostcode = recommendUnitPostcode == null ? null : recommendUnitPostcode.trim();
    }
    /**
     * GET 通讯地址
     * @return recommendUnitAddress
     */
    public String getRecommendUnitAddress() {
        return recommendUnitAddress;
    }

    /**
     * SET 通讯地址
     * @param recommendUnitAddress
     */
    public void setRecommendUnitAddress(String recommendUnitAddress) {
        this.recommendUnitAddress = recommendUnitAddress == null ? null : recommendUnitAddress.trim();
    }
    /**
     * GET 重点实验室建设的目的和意义
     * @return goal
     */
    public String getGoal() {
        return goal;
    }

    /**
     * SET 重点实验室建设的目的和意义
     * @param goal
     */
    public void setGoal(String goal) {
        this.goal = goal == null ? null : goal.trim();
    }
    /**
     * GET 国内外概况
     * @return content
     */
    public String getContent() {
        return content;
    }

    /**
     * SET 国内外概况
     * @param content
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }
    /**
     * GET 现有研究工作基础、水平等
     * @return workBasis
     */
    public String getWorkBasis() {
        return workBasis;
    }

    /**
     * SET 现有研究工作基础、水平等
     * @param workBasis
     */
    public void setWorkBasis(String workBasis) {
        this.workBasis = workBasis == null ? null : workBasis.trim();
    }
    /**
     * GET 重点实验室研究方向和主要研究内容 重点实验室建设任务书的主要内容及指标
     * @return researchTarget
     */
    public String getResearchTarget() {
        return researchTarget;
    }

    /**
     * SET 重点实验室研究方向和主要研究内容 重点实验室建设任务书的主要内容及指标
     * @param researchTarget
     */
    public void setResearchTarget(String researchTarget) {
        this.researchTarget = researchTarget == null ? null : researchTarget.trim();
    }
    /**
     * GET 主要工作规划、预期目标和水平
     * @return workPlanning
     */
    public String getWorkPlanning() {
        return workPlanning;
    }

    /**
     * SET 主要工作规划、预期目标和水平
     * @param workPlanning
     */
    public void setWorkPlanning(String workPlanning) {
        this.workPlanning = workPlanning == null ? null : workPlanning.trim();
    }
    /**
     * GET 已具备的实验条件
     * @return experimentalCondition
     */
    public String getExperimentalCondition() {
        return experimentalCondition;
    }

    /**
     * SET 已具备的实验条件
     * @param experimentalCondition
     */
    public void setExperimentalCondition(String experimentalCondition) {
        this.experimentalCondition = experimentalCondition == null ? null : experimentalCondition.trim();
    }
    /**
     * GET 科研队伍状况及人才培养能力
     * @return teamAbility
     */
    public String getTeamAbility() {
        return teamAbility;
    }

    /**
     * SET 科研队伍状况及人才培养能力
     * @param teamAbility
     */
    public void setTeamAbility(String teamAbility) {
        this.teamAbility = teamAbility == null ? null : teamAbility.trim();
    }
    /**
     * GET 重点实验室基本情况
     * @return mainInfo
     */
    public String getMainInfo() {
        return mainInfo;
    }

    /**
     * SET 重点实验室基本情况
     * @param mainInfo
     */
    public void setMainInfo(String mainInfo) {
        this.mainInfo = mainInfo == null ? null : mainInfo.trim();
    }
    /**
     * GET 重点实验室建设期间研究工作进展
     * @return researchAward
     */
    public String getResearchAward() {
        return researchAward;
    }

    /**
     * SET 重点实验室建设期间研究工作进展
     * @param researchAward
     */
    public void setResearchAward(String researchAward) {
        this.researchAward = researchAward == null ? null : researchAward.trim();
    }
    /**
     * GET 重点实验室建设计划执行情况概述
     * @return researchPlan
     */
    public String getResearchPlan() {
        return researchPlan;
    }

    /**
     * SET 重点实验室建设计划执行情况概述
     * @param researchPlan
     */
    public void setResearchPlan(String researchPlan) {
        this.researchPlan = researchPlan == null ? null : researchPlan.trim();
    }
    /**
     * GET 学科建设、人才培养与国内外学术交流情况
     * @return researchSubjectContent
     */
    public String getResearchSubjectContent() {
        return researchSubjectContent;
    }

    /**
     * SET 学科建设、人才培养与国内外学术交流情况
     * @param researchSubjectContent
     */
    public void setResearchSubjectContent(String researchSubjectContent) {
        this.researchSubjectContent = researchSubjectContent == null ? null : researchSubjectContent.trim();
    }
    /**
     * GET 重点实验室组织结构与管理制度、模式
     * @return laboratoryManager
     */
    public String getLaboratoryManager() {
        return laboratoryManager;
    }

    /**
     * SET 重点实验室组织结构与管理制度、模式
     * @param laboratoryManager
     */
    public void setLaboratoryManager(String laboratoryManager) {
        this.laboratoryManager = laboratoryManager == null ? null : laboratoryManager.trim();
    }
    /**
     * GET 重点实验室中长期工作设想
     * @return laboratoryNextPlan
     */
    public String getLaboratoryNextPlan() {
        return laboratoryNextPlan;
    }

    /**
     * SET 重点实验室中长期工作设想
     * @param laboratoryNextPlan
     */
    public void setLaboratoryNextPlan(String laboratoryNextPlan) {
        this.laboratoryNextPlan = laboratoryNextPlan == null ? null : laboratoryNextPlan.trim();
    }
    /**
     * GET 依托单位给予的支持情况
     * @return laboratorySupport
     */
    public String getLaboratorySupport() {
        return laboratorySupport;
    }

    /**
     * SET 依托单位给予的支持情况
     * @param laboratorySupport
     */
    public void setLaboratorySupport(String laboratorySupport) {
        this.laboratorySupport = laboratorySupport == null ? null : laboratorySupport.trim();
    }
    /**
     * GET 符合申请直接命名的条件
     * @return meetConditionText
     */
    public String getMeetConditionText() {
        return meetConditionText;
    }

    /**
     * SET 符合申请直接命名的条件
     * @param meetConditionText
     */
    public void setMeetConditionText(String meetConditionText) {
        this.meetConditionText = meetConditionText == null ? null : meetConditionText.trim();
    }
    /**
     * GET 符合申请直接命名的条件 详细情况
     * @return meetConditionValue
     */
    public String getMeetConditionValue() {
        return meetConditionValue;
    }

    /**
     * SET 符合申请直接命名的条件 详细情况
     * @param meetConditionValue
     */
    public void setMeetConditionValue(String meetConditionValue) {
        this.meetConditionValue = meetConditionValue == null ? null : meetConditionValue.trim();
    }
    /**
     * GET 工作报告年度
     * @return yearNo
     */
    public String getYearNo() {
        return yearNo;
    }

    /**
     * SET 工作报告年度
     * @param yearNo
     */
    public void setYearNo(String yearNo) {
        this.yearNo = yearNo == null ? null : yearNo.trim();
    }
    /**
     * GET 科研成果转化及对市场监管支撑情况
     * @return marketSupport
     */
    public String getMarketSupport() {
        return marketSupport;
    }

    /**
     * SET 科研成果转化及对市场监管支撑情况
     * @param marketSupport
     */
    public void setMarketSupport(String marketSupport) {
        this.marketSupport = marketSupport == null ? null : marketSupport.trim();
    }
    /**
     * GET 该年度市局和所在地市给予的支持情况
     * @return citySupport
     */
    public String getCitySupport() {
        return citySupport;
    }

    /**
     * SET 该年度市局和所在地市给予的支持情况
     * @param citySupport
     */
    public void setCitySupport(String citySupport) {
        this.citySupport = citySupport == null ? null : citySupport.trim();
    }
    /**
     * GET 该年度运行与开放经费及使用情况
     * @return fundStatus
     */
    public String getFundStatus() {
        return fundStatus;
    }

    /**
     * SET 该年度运行与开放经费及使用情况
     * @param fundStatus
     */
    public void setFundStatus(String fundStatus) {
        this.fundStatus = fundStatus == null ? null : fundStatus.trim();
    }
    /**
     * GET 
     * @return result
     */
    public String getResult() {
        return result;
    }

    /**
     * SET 
     * @param result
     */
    public void setResult(String result) {
        this.result = result == null ? null : result.trim();
    }
    /**
     * GET 
     * @return opinion
     */
    public String getOpinion() {
        return opinion;
    }

    /**
     * SET 
     * @param opinion
     */
    public void setOpinion(String opinion) {
        this.opinion = opinion == null ? null : opinion.trim();
    }
    /**
     * GET 
     * @return formType
     */
    public String getFormType() {
        return formType;
    }

    /**
     * SET 
     * @param formType
     */
    public void setFormType(String formType) {
        this.formType = formType == null ? null : formType.trim();
    }
    /**
     * GET 申请类型：培育申报/直接命名申报/培育评估/验收申请/中期评估/年度总结
     * @return applyType
     */
    public String getApplyType() {
        return applyType;
    }

    /**
     * SET 申请类型：培育申报/直接命名申报/培育评估/验收申请/中期评估/年度总结
     * @param applyType
     */
    public void setApplyType(String applyType) {
        this.applyType = applyType == null ? null : applyType.trim();
    }
    /**
     * GET 
     * @return flowStatus
     */
    public String getFlowStatus() {
        return flowStatus;
    }

    /**
     * SET 
     * @param flowStatus
     */
    public void setFlowStatus(String flowStatus) {
        this.flowStatus = flowStatus == null ? null : flowStatus.trim();
    }
    /**
     * GET 
     * @return flowUser
     */
    public String getFlowUser() {
        return flowUser;
    }

    /**
     * SET 
     * @param flowUser
     */
    public void setFlowUser(String flowUser) {
        this.flowUser = flowUser == null ? null : flowUser.trim();
    }
    /**
     * GET 
     * @return flowUserName
     */
    public String getFlowUserName() {
        return flowUserName;
    }

    /**
     * SET 
     * @param flowUserName
     */
    public void setFlowUserName(String flowUserName) {
        this.flowUserName = flowUserName == null ? null : flowUserName.trim();
    }
    @Override
	public String toString() {
	    return  ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
	}
}