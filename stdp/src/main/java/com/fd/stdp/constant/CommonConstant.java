package com.fd.stdp.constant;

public class CommonConstant {
	public final static Integer FLAG_III = 3;
	public final static Integer FLAG_II = 2;
	public final static Integer FLAG_YES = 1;
	public final static Integer FLAG_NO = 0;
	public final static String ROOT = "0";
	public final static String ROLE_CODE_ADMIN = "adminstrator";

	public static final String TYPE_ADMIN = "ADMIN";


	 /** 菜单类型（目录） */
    public static final String TYPE_DIR = "M";

    /** 菜单类型（菜单） */
    public static final String TYPE_MENU = "C";

    /** 菜单类型（按钮） */
    public static final String TYPE_BUTTON = "F";

    /** 是否菜单外链（是） */
    public static final Integer YES_FRAME = 0;

    /** 是否菜单外链（否） */
    public static final Integer NO_FRAME = 1;

    /** Layout组件标识 */
    public final static String LAYOUT = "Layout";

    /** ParentView组件标识 */
    public final static String PARENT_VIEW = "ParentView";

	public static final String DESC = "desc";
	public static final String ASC = "asc";

	public static final String ASSIGNEE = "USER@";
	public static final String ASSIGNROLE = "ROLE@";

	/**
	 * 独立单点登录配置标识
	 */
	public static final String SSOTICKENT = "ssotickent={ssotickent}";
	/**
	 * 最小密码长度
	 */
	public static final Integer MINSIZE = 8;
	/**
	 * 最大密码长度
	 */
	public static final Integer MAXSIZE = 30;

	/**
	 * 系统日志序列
	 */
	public static final String SYS_LOG_QUEUE = "SYS_LOG_QUEUE";
	/**
	 * 聊天消息redis key
	 */
	public final static String REDIS_MESSAGE_INFO = "project:message";
	/**
	 * 区域redis key
	 */
	public final static String REDIS_AREA_INFO = "project:area:parent_";
	/**
	 * 区号redis key
	 */
	public final static String REDIS_COUNTRY_ALL = "project:country:all";

	/**
	 * 验证码redis key
	 */
	public final static String REDIS_VERIFI_CODE = "project:verifi:code_";
	/**
	 * 验证码过期时间 5分钟
	 */
	public final static Integer REDIS_VERIFI_CODE_TIME = 300;

	/**
	 * 权限资源类型
	 */
	public final static String RESOURCE_TYPE_MENU = "menu";
	public final static String RESOURCE_TYPE_BTN = "button";

	/**
	 * 用户类型 管理员
	 */
	public final static String USER_TYPE_ADMIN = "ADMIN";

	/**
	 * 用户类型 个人
	 */
	public final static String USER_TYPE_PERSONAL = "PERSONAL";

	/**
	 * 用户类型 企业
	 */
	public final static String USER_TYPE_COMPANY = "COMPANY";

	/**
	 * 用户类型注册用户
	 */
	public final static String USER_TYPE_REGISTER = "REGISTER";

	// 工作流生成XML和PNG目录
	public static final String FILEACTIVITI = "uploadFiles/activitiFile/";
	public static final String FILEACTIVITI＿ = "uploadFiles/activitiFile";

	/**
	 * 审批判断参数名 层级省市县
	 */
	public static final String APPROVAL_LEVEL = "LEVEL";
	/**
	 * 审批判断参数名 结果
	 */
	public static final String APPROVAL_RESULT = "RESULT";

	/**
	 * 字典管理 1代表公共字典
	 */
	public static final Integer DICT_IS_OPEN = 1;
	/**
	 * 字典管理 0代表不是公共字典
	 */
	public static final Integer DICT_NOT_OPEN = 0;

	/**
	 * 超级管理员角色类型 linqiang
	 */
	public static final String SUPER_ADMIN_ROLE = "SUPERADMIN";
	/**
	 * 专家角色
	 */
	public static final String EXPERT_ROLE = "EXPERT";

	/**
	 * 检验标注管理 标准状态 1代表正常
	 */
	public static final Integer BASIC_STANDARD_STATUS_NORMAL = 1;

	/**
	 * 检验标注管理 标准状态 1代表正常
	 */
	public static final String BASIC_STANDARD_STATUSTXT_NORMAL = "正常";
	/**
	 * 检验标注管理 标准状态 0代表废止
	 */
	public static final Integer BASIC_STANDARD_STATUS_STOP = 0;
	/**
	 * 检验标注管理 标准状态 0代表废止
	 */
	public static final String BASIC_STANDARD_STATUSTXT_STOP = "废止";
	/**
	 * 超级管理用户类型 ADMIN
	 */
	public static final String USER_TYPE_MANAGER_ADMIN = "ADMIN";
	/**
	 * 用户登录名长度限制
	 */
	public static final Integer USER_NAME_LENGTH = 32;
	/**
	 * 用户密码长度限制
	 */
	public static final Integer USER_PASSWORD_LENGTH = 32;
	/**
	 * 用户真实姓名
	 */
	public static final Integer USER_REAL_NAME_LENGTH = 32;

	/**
	 * 流程名 任务登记与下达
	 */
	public static final String WORK_FLOW_MISSION_ASSIGN = "WORK_FLOW_MISSION_ASSIGN";

	/**
	 * 流程任务唯一标识
	 */
	public static final String TASK_UNIQUEID = "TASK_UNIQUEID";
	/**
	 * 平台类型：用户默认密码
	 */
	public static final String DEFAULT_PASSWORD = "zscq@123";

	public static final String FILE_SEPARATE_SIGN = "#FSP#";

	/**
	 * 浙里检数据接口
	 */
	public static final String ZLJ_POST_URL = "http://gl.zjsis.cn/rest/datasync/scienceStatisc";
}
