package com.fd.stdp.constant;

/**
 * Token的Key常量
 *
 * <AUTHOR>
 */
public class TokenConstants {
    /**
     * 令牌自定义标识
     */
    public static final String AUTHENTICATION = "Authorization";

    /**
     * 令牌前缀
     */
    public static final String PREFIX = "Bearer ";

    /**
     * 用户标识
     */
    public static final String USER_KEY = "user_key";
    /**
     * 用户ID字段
     */
    public static final String DETAILS_USER_ID = "user_id";

    /**
     * 用户名字段
     */
    public static final String DETAILS_USERNAME = "username";

    /**
     * 用户别名字段
     */
    public static final String DETAILS_USERNICKNAME = "usernickname";

    /**
     * 用户类型
     */
    public static final String DETAILS_USERTYPE = "usertype";

    /**
     * JWT令牌秘钥
     * 数据库字段加密秘钥
     */
    public final static String SECRET = "(Jing:)_$^11244^%$_(Hui:)_@@++--(Shu:)_+++))(Zhi:)";

    /**
     * 全局数据库加密sm4密码
     * 必需16位
     */
    public final static String SM4_DATA_SECRET = "(Jing:)_$^11244^";

    /**
     * google 验证标识
     */
    public final static String GOOGLE_AUTHENTICATOR_FLAG = "GOOGLE_AUTHENTICATOR_FLAG";
    /**
     * 显示在app上的标题
     */
    public final static String GOOGLE_AUTH_TITLE = "模板管理系统";

    /**
     * 页面数据解密私钥
     */
    public final static String PRIVATE_KEY = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALSrelzlqbNX+ilqerEO7s/MjpZMx+oXPTRTbDJxGZRdT5r+LY7RICq4GxKkOQTxqgbxts0RSHVshK/rDuJfbVDpvwG2w2aIErKRLaklMD6FeOyr7kRlOjnFANKhg05sanZZ/NcWL3TbPct0PZqx4KbeY1vh/Saj+4AOXheqVJKvAgMBAAECgYEAr0Tzjg+XE88zdt4/bGlKL/kIErMl9ejZjVuoyT6DTDVBtuECnVwvUoIMcYczcaJoChiP+Fzd6FyAVJE6fU2KI6TDh6XsIF/NBe3OqHkruSVXjO0CDXyXLRpYkxcSYP9ab37vwTFKbGLNhMGubAObH62p3Kq9EWk9qja/XsVtJvECQQDj/3WUvsv1ZGi+OPm6n6U2GbWPBiS3EFyJ9gTLsmR9lQcERChdSCBKv/21aHRFVALo9aW/9cm0AY8Xp3jJQ70dAkEAytv3EnwFo6SgMLEHrc6z/mUsaojgs9aauuT/O/72Dc7okZlnXAt9c//73FetDC5wLJHqt52868rdcwv+VFBhOwJARqgmckBmyNejNkBF9DYBZzC0eWx3+FuTshWSCuZwChMjJ4lP0QjHKSMypwOF9O5CMJvgEAtFxQkupS/7WLQraQJAbM0ZcRKPu7s6J6NraO8qXtFIx0LuscXyazcTJcyTj8hfuGe8pgOLn8G2BfIIYDYv99ezOhQiFZ1StLfECtiprQJBALRFbM+Hde1Hv682ESUrf4Y5e8QwRmaI+A3oYms59a8GQ1pv6ix2cQdOlXavCiqFijiA5ydFWAZeRU7XMrx5Bqg=";
    // 对应页面公钥 MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC0q3pc5amzV/opanqxDu7PzI6WTMfqFz00U2wycRmUXU+a/i2O0SAquBsSpDkE8aoG8bbNEUh1bISv6w7iX21Q6b8BtsNmiBKykS2pJTA+hXjsq+5EZTo5xQDSoYNObGp2WfzXFi902z3LdD2aseCm3mNb4f0mo/uADl4XqlSSrwIDAQAB
    /**
     * 服务数据加密传输到页面 公钥
     */
    public final static String PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDk1qgE1FUGombwROjdtaXK1LDhOPCX2IYu+1jfMZUM3JsoBXz0rkM/NUMbmvSVtiN9JU53DRr0DXgxpaLDjmlmvf54hXrMi/lnBaiA8P89G0TSwKQzC1BwBTVKobzLmtNdXEMaMw44pV6uVQGXTLCJ9eYTqwl8FojeoD5cO0/+dwIDAQAB";
    // 对应页面私钥  MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAOTWqATUVQaiZvBE6N21pcrUsOE48JfYhi77WN8xlQzcmygFfPSuQz81Qxua9JW2I30lTncNGvQNeDGlosOOaWa9/niFesyL+WcFqIDw/z0bRNLApDMLUHAFNUqhvMua011cQxozDjilXq5VAZdMsIn15hOrCXwWiN6gPlw7T/53AgMBAAECgYAggvrGDOCzm5kiY7ft3+PZKFSk+vD7sdCGlkEvHJ0/gYwtqHHzVVrKfSe2oVJEfucV3ZoDQgJlUBCWhSDVxcLliCwrIEc1gsvz9Adg6G4zJ/9nvjKKLYywRkF7P1jKi3FO159upk4P4M2UsUwEXXY7ICrrQXFm2EQEtCybVMDW6QJBAPMgZOWt1P1gqvJWEkbjmTcuT/3ULNg6hlJqu//OyaVbdKzD7jaTKsSOmUYhlwMIFee5XVFaUg1R/Mj3E2ijX/MCQQDw9JYZpX2sopytazTZXikRdzcwngd2OwHzMJMsgA+I3qXEIzIY4+mk8eoIZPIWZGN+KHBlDH/o955WRDDyrExtAkBM+j638dfz+X1miRX0V++U5xbwmRMxyyXoRl8L2V9C+bfxU3/Cy8nIvhWNfOkhTpgQ7EG9lklQalsNDh2ZDdE9AkEAoN0ZbIvQiDWT1i8xfEDhuBhYDywhnSwt1BbE2ruwI1dJYdWBd56I+KbM8Me723kv9yS2TK/4oSbXXY5vA0RGTQJAboZS7XmF9wmyIAGSABvR722jfbY3OjpDVBkaEFWdCxA77iLcmC98g0ZpC3V/avVi4brGhjms132PMv3q/UXNMw==

    public final static String SERVER_PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAOTWqATUVQaiZvBE6N21pcrUsOE48JfYhi77WN8xlQzcmygFfPSuQz81Qxua9JW2I30lTncNGvQNeDGlosOOaWa9/niFesyL+WcFqIDw/z0bRNLApDMLUHAFNUqhvMua011cQxozDjilXq5VAZdMsIn15hOrCXwWiN6gPlw7T/53AgMBAAECgYAggvrGDOCzm5kiY7ft3+PZKFSk+vD7sdCGlkEvHJ0/gYwtqHHzVVrKfSe2oVJEfucV3ZoDQgJlUBCWhSDVxcLliCwrIEc1gsvz9Adg6G4zJ/9nvjKKLYywRkF7P1jKi3FO159upk4P4M2UsUwEXXY7ICrrQXFm2EQEtCybVMDW6QJBAPMgZOWt1P1gqvJWEkbjmTcuT/3ULNg6hlJqu//OyaVbdKzD7jaTKsSOmUYhlwMIFee5XVFaUg1R/Mj3E2ijX/MCQQDw9JYZpX2sopytazTZXikRdzcwngd2OwHzMJMsgA+I3qXEIzIY4+mk8eoIZPIWZGN+KHBlDH/o955WRDDyrExtAkBM+j638dfz+X1miRX0V++U5xbwmRMxyyXoRl8L2V9C+bfxU3/Cy8nIvhWNfOkhTpgQ7EG9lklQalsNDh2ZDdE9AkEAoN0ZbIvQiDWT1i8xfEDhuBhYDywhnSwt1BbE2ruwI1dJYdWBd56I+KbM8Me723kv9yS2TK/4oSbXXY5vA0RGTQJAboZS7XmF9wmyIAGSABvR722jfbY3OjpDVBkaEFWdCxA77iLcmC98g0ZpC3V/avVi4brGhjms132PMv3q/UXNMw==";
}
