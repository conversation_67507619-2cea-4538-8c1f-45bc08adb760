package com.fd.stdp.constant;

/**
 * 文件常量
 * 
 * <AUTHOR>
 * @date 2019年9月19日
 */
public class FileConstant {
	/**
	 * 文件后缀 .zip
	 */
	public static final String FILE_SUFFIX_ZIP = ".zip";
	/**
	 * 文件后缀 .docx
	 */
	public static final String FILE_SUFFIX_DOCX = ".docx";
	/**
	 * 文件后缀 .doc
	 */
	public static final String FILE_SUFFIX_DOC = ".doc";
	/**
	 * 文件后缀 .xml
	 */
	public static final String FILE_SUFFIX_XML = ".xml";
	/**
	 * 文件后缀 .html
	 */
	public static final String FILE_SUFFIX_HTML = ".html";
	/**
	 * 文件后缀 .pdf
	 */
	public static final String FILE_SUFFIX_PDF = ".pdf";

	/**
	 * 专利奖 设计类模板路径
	 */
	public static final String PATENT_AWARD_DESIGN_TEMP_PATH = "baseDesign";

	/**
	 * 专利奖 运用效益(二) 模板路径
	 */
	public static final String PATENT_AWARD_BENEFIT_TEMP_PATH = "benefit";
	/**
	 * 专利奖 获奖情况 模板路径
	 */
	public static final String PATENT_AWARD_PRIZE_WINNING_TEMP_PATH = "prizeWinning";

	/**
	 * 专利奖 设计类PDF模板路径
	 */
	public static final String PATENT_AWARD_DESIGN_PDF_TEMP_PATH = "baseDesignTempPDF";
	/**
	 * 专利奖 发明、实用类PDF模板路径
	 */
	public static final String PATENT_AWARD_INVENTION_PDF_TEMP_PATH = "baseInventionTempPDF";

	/**
	 * 专利奖 运用效益(二) PDF模板路径
	 */
	public static final String PATENT_AWARD_BENEFIT_PDF_TEMP_PATH = "benefitTempPDF";

	/**
	 * 专利奖 运用效益(二) HTML模板路径
	 */
	public static final String PATENT_AWARD_BENEFIT_HTML_TEMP_PATH = "benefitTempHtml";
	/**
	 * 专利奖 获奖情况的PDF模板路径
	 */
	public static final String PATENT_AWARD_PRIZE_WINNING_PDF_TEMP_PATH = "winPrizeTempPDF";

	/**
	 * 专利申请优先审查 请求书模板路径
	 */
	public static final String PRIORITY_PATENT_TEMP_PATH = "ipmPriorityPatent";
	
	/**
	 * 专利申请优先审查  审核通过模板名称
	 */
	public static final String PRIORITY_EXPORT_PASS = "tempExportPass";
	/**
	 * 复审/无效宣告案件 请求书模板路径
	 */
	public static final String PRIORITY_REVIEW_TEMP_PATH = "reviewApplyInfo202002";
	/**
	 * 专利奖 发明、实用 类模板路径
	 */
	public static final String PATENT_AWARD_INVENTION_TEMP_PATH = "baseInvention";

	/**
	 * 产业规划类型导航项目 (html页面1)
	 */
	public static final String NAVIGATION_PROJECT_INDUSTRIAL_TEMP_HTMLONE_PATH = "navigationIndustrialOne";

	/**
	 * 产业规划类型导航项目 (html页面2)
	 */
	public static final String NAVIGATION_PROJECT_INDUSTRIAL_TEMP_HTMLTWO_PATH = "navigationIndustrialTwo";

	/**
	 * 产业规划类型导航项目 模板路径
	 */
	public static final String NAVIGATION_PROJECT_INDUSTRIAL_TEMP_PATH = "navigationIndustrial";

	public static final String NAVIGATION_PROJECT_INDUSTRIAL_TEMP_PATH_ONE = "navigationIndustrialOne";

	public static final String NAVIGATION_PROJECT_INDUSTRIAL_TEMP_PATH_TWO = "navigationIndustrialTwo";

	public static final String NAVIGATION_PROJECT_INDUSTRIAL_TEMP_PATH_THREE = "navigationIndustrialThree";

	/**
	 * 导航底部模板
	 */
	public static final String NAVIGATION_PROJECT_BOTTOM_PATH = "navigationbottom";

	/**
	 * 企业运营类型类型导航项目 模板路径
	 */
	public static final String NAVIGATION_PROJECT_COMPANY_TEMP_PATH = "navigationCompany";

	public static final String NAVIGATION_PROJECT_COMPANY_TEMP_PATH_ONE = "navigationCompanyOne";

	public static final String NAVIGATION_PROJECT_COMPANY_TEMP_PATH_TWO = "navigationCompanyTwo";

	public static final String NAVIGATION_PROJECT_COMPANY_TEMP_PATH_THREE = "navigationCompanyThree";

	/**
	 * 战略项目 产业规划类型导航项目 (html页面1)
	 */
	public static final String STRATEGIC_PROJECT_TEMP_HTMLONE_PATH = "strategicProjectOne";

	/**
	 * 战略项目 产业规划类型导航项目 (html页面2)
	 */
	public static final String STRATEGIC_PROJECT_TEMP_HTMLTWO_PATH = "strategicProjectTwo";

	/**
	 * 战略项目 产业规划类型导航项目 模板路径
	 */
	public static final String STRATEGIC_PROJECT_TEMP_PATH = "strategicProject";
	/**
	 * 专利优先审查文件夹 位置常量
	 */
	public static final String FILE_PRIORITY_PATH = "priorityCase/priorityFile";
	/**
	 * 专利优先审查文件夹 位置常量
	 */
	public static final String FILE_PRIORITY_CASE = "priorityCase";
	/**
	 * 专利优先审查文件夹 位置常量
	 */
	public static final String FILE_PRIORITY_PRINT_PATH = "printCase/printCaseFile";
	/**
	 * 专利优先审查文件夹 位置常量 合成pdf时，存放临时word转pdf的文件
	 */
	public static final String FILE_PRIORITY_PDF_PATH = "printPdfCase/pdfCaseFile";
	/**
	 * 专利优先审查文件夹 位置常量 合成的pdf
	 */
	public static final String FILE_PRIORITY_MERGER_PDF_PATH = "printPdfCase/mergePdf";

	/**
	 * 专利优先审查 现有技术或者现有设计信息材料类型
	 */
	public static final String PRIORITY_FILE_TYPE_TECHNOLOGY = "FILE_TYPE_EXIST_TECHNOLOGY";
	/**
	 * 专利优先审查  其他文件类型
	 */
	public static final String PRIORITY_FILE_TYPE_OTHER = "FILE_TYPE_OTHER";
	
	
	/**
	 * 复审/无效宣告案件文件夹 位置常量(压缩时的文件夹)
	 */
	public static final String FILE_REVIEW_PATH = "reviewCase/reviewFile";
	/**
	 * 复审/无效宣告案件文件夹 PDF格式文件位置
	 */
	public static final String FILE_REVIEW_PDF_PATH = "reviewCase/reviewPdfFile";
	/**
	 * 复审/无效宣告案件文件夹 PDF格式文件位置
	 */
	public static final String FILE_REVIEW_MERGER_PDF_PATH = "reviewCase/mergePdf";

	/**
	 * 预警项目 请求书模板路径
	 */
	public static final String STRATEGIC_WARN_PROJECT_TEMP = "warnProjectTemp";

	/**
	 * 预警项目 上半部分模板
	 */
	public static final String STRATEGIC_WARN_HEAD = "warnProjectHead";

	/**
	 * 预警项目 下半部分模板
	 */
	public static final String STRATEGIC_WARN_TAIL = "warnProjectTail";

	/**
	 * 预警项目 立项背景附件类型
	 */
	public static final String STRATEGIC_WARN_PROJECT = "FILE_TYPE_PROJECT";

	/**
	 * 预警项目 研究基础附件类型
	 */
	public static final String STRATEGIC_WARN_RESEARCH = "FILE_TYPE_RESEARCH";

	/**
	 * 预警项目 研究内容及预期成果附件类型
	 */
	public static final String STRATEGIC_WARN_RESULT = "FILE_TYPE_RESULT";
	
	/**
	 * 预警项目 完整文档模板
	 */
	public static final String STRATEGIC_WARN_TEMP = "strategicWarnTemp";
	
	/**
	 * 预警项目 头部（新模板）
	 */
	public static final String STRATEGIC_HEAD_TEMP = "strategicHeadTemp";
	
	/**
	 * 预警项目 尾部（新模板）
	 */
	public static final String STRATEGIC_TAIL_TEMP = "strategicTailTemp";
	/**
	 *	 预警合同模板-头部
	 */
	public static final String STRATEGIC_CONTRACT_HEAD_TEMP = "straContractHeadTemp";
	
	/**
	 *	 预警合同模板-尾部
	 */
	public static final String STRATEGIC_CONTRACT_TAIL_TEMP = "straContractTailTemp";
}
