package com.fd.stdp.constant;


/**
 * 系统流程 常量类
 * @Date: 2021-12-10
 * @Author: yu<PERSON><PERSON><PERSON><PERSON>
 */
public class FlowableConstant {

    /**
     * 一般审核流程
     */
    public static final String  FLOW_NORMAL_AUDIT = "FLOW_NORMAL_AUDIT";

    /**
     * 科技处科研项目需求征集
     */
    public static final String  SYSTEM_FLOWABLE_STDP_DEMAND_APPLY = "SYSTEM_FLOWABLE_STDP_DEMAND_APPLY";

    /**
     * 项目申报
     */
    public static final String  FLOW_PROJECT_APPLY = "FLOW_PROJECT_APPLY";

    /**
     * 任务书申报
     */
    public static final String  FLOW_CONTRACT_PROCESS = "FLOW_CONTRACT_PROCESS";

    /**
     * 项目任务书变更
     */
    public static final String  FLOW_PROJECT_CONTRACT_CHANGE = "FLOW_PROJECT_CONTRACT_CHANGE";

    /**
     * 项目验收
     */
    public static final String FLOW_PROJECT_ACCEPT = "FLOW_PROJECT_ACCEPT";

    /**
     * 学科带头人申报流程
     */
    public static final String FLOW_SUBJECR_LEADER_APPLY = "FLOW_SUBJECR_LEADER_APPLY";

    /**
     * 科技成果
     */
    public static final String  FLOW_TECH_ACHIEVEMENT = "FLOW_TECH_ACHIEVEMENT";

    /**
     * 报奖备案
     */
    public static final String  FLOW_TECH_AWARD_APPLY = "FLOW_TECH_AWARD_APPLY";

    /**
     * 新增单个机构人员
     */
    public static final String  FLOW_SINGLE_PERSON = "FLOW_SINGLE_PERSON";

    /**
     * 创新载体申报
     */
    public static final String  FLOW_INNOVATION_APPLY = "FLOW_INNOVATION_APPLY";

    /**
     * 创新载体任务书
     */
    public static final String  FLOW_INNOVATION_CONTRACT = "FLOW_INNOVATION_CONTRACT";

    /**
     * 创新载体验收
     */
    public static final String FLOW_INNOVATION_LABORATORY_ACCEPT = "FLOW_INNOVATION_LABORATORY_ACCEPT";

    /**
     * 省质检中心验收
     */
    public static final String FLOW_QUALITY_ACCEPTAPPLY = "FLOW_QUALITY_ACCEPTAPPLY";

    /**
     * 创新团队申报流程
     */
    public static final String FLOW_TALENT_TEAM_APPLY = "FLOW_TALENT_TEAM_APPLY";
    /**
     * 创新团队任务书变更流程
     */
    public static final String FLOW_TALENT_TEAM_CONTRACT_CHANGE = "FLOW_TALENT_TEAM_CONTRACT_CHANGE";

    /**
     * 内审流程
     */
    public static final String FLOW_INNER_AUDIT = "FLOW_INNER_AUDIT";

    /**
     * 简单审核流程
     */
    public static final String FLOW_NOTICE_APPLY = "FLOW_NOTICE_APPLY";

    /**
     * 科普工作审核流程
     */
    public static final String FLOW_WORK_REPORT = "FLOW_WORK_REPORT";


    /**
     * 科技项目申报流程
     */
    public static final String TECK_APPLY_FLOW = "teck_apply_flow";
}
