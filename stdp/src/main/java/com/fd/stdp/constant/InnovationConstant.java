package com.fd.stdp.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 创新载体 常量类
 * @date 2022/4/19 10:31
 */
public class InnovationConstant {

    /**
     * 省局重点实验室
      */
    public static final String INNOVATION_LABORATORY = "INNOVATION_LABORATORY";
    public static final String INNOVATION_LABORATORY_TEXT = "省局重点实验室";

    /**
     * 省质检中心
     */
    public static final String INNOVATION_QUALITY = "INNOVATION_QUALITY";
    public static final String INNOVATION_QUALITY_TEXT = "省质检中心";

    /**
     * 总局重点实验室
     */
    public static final String GENERAL_LABORATORY = "GENERAL_LABORATORY";
    public static final String GENERAL_LABORATORY_TEXT = "总局重点实验室";

    /**
     * 总局技术创新中心
     */
    public static final String GENERAL_INNOVATION = "GENERAL_INNOVATION";
    public static final String GENERAL_INNOVATION_TEXT = "总局技术创新中心";

    /**
     * 省重点实验室
     */
    public static final String PROVINCE_LABORATORY = "PROVINCE_LABORATORY";
    public static final String PROVINCE_LABORATORY_TEXT = "省重点实验室";

    /**
     * 省级工程研究中心
     */
    public static final String PROVINCE_ENGINE = "PROVINCE_ENGINE";
    public static final String PROVINCE_ENGINE_TEXT = "省级工程研究中心";

    /**
     * 其它创新载体
     */
    public static final String OTHER_INNOVATION = "OTHER_INNOVATION";
    public static final String OTHER_INNOVATION_TEXT = "其它创新载体";

    /**
     * 国家质检中心
     */
    public static final String NATIONAL_CENTER = "NATIONAL_CENTER";
    public static final String NATIONAL_CENTER_TEXT = "国家质检中心";

    /**
     * 省质检中心
     */
    public static final String PROVINCE_CENTER = "PROVINCE_CENTER";
    public static final String PROVINCE_CENTER_TEXT = "省质检中心";
}
