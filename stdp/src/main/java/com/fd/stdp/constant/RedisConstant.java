package com.fd.stdp.constant;

public class RedisConstant {

    public static final String SYS_REDIS = "stdp:tuomin:";

    /**
     * 验证码redis前缀
     */
    public static final String VALID_CODE_KEY = "stdp:valid:code:";

    /**
     * 单点登录tickent redis前缀信息
     */
    public static final String SSOTICKENT="stdp:sso:tickent:";
    /**
     * 过期时间 5s 验证码到登录用时不超过5s
     */
    public static final Integer REDIS_EXPIRE_VALID = 5;

    /**
     * 字典类型查询字典条目的RedisKey
     */
    public static final String DICT_TYPE_CODE = "stdp:dictionary:TYPE_CODE_";
    /**
     * 过期时间 1 天
     */
    public static final Integer REDIS_EXPIRE_ONE_DAY = 86400;
    /**
     * 过期时间 10分钟
     */
    public static final Integer REDIS_EXPIRE_TEN_MIN = 600;
    /**
     * 过期时间 1分钟
     */
    public static final Integer REDIS_EXPIRE_ONE_MIN = 60;
    /**
     * 过期时间10秒
     */
    public static final Integer REDIS_EXPIRE_TEN_SECOND = 10;

    /**
     * 省行政区划rediskey
     */
    public static final String SYS_ARECODE_PROVINCE_CATCH = "stdp:sys:areaCode:ARECODE_PROVINCE";

    /**
     * 市 行政区划rediskey
     */
    public static final String SYS_ARECODE_CITY_CATCH = "stdp:sys:areaCode:ARECODE_CITY";

    /**
     * 县 行政区划rediskey
     */
    public static final String SYS_ARECODE_COUNTY_CATCH = "stdp:sys:areaCode:ARECODE_COUNTY";

    /**
     * 全国行政区划
     */
    public static final String SYS_ARECODE_COUNTRY = "stdp:sys:areaCode:COUNTRY";

    /**
     * 浙里检数据
     */
    public static final String ZJL_INFO = "stdp:sys:zlj:INFO";

}
