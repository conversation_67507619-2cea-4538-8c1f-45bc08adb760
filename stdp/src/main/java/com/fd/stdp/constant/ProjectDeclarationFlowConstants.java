package com.fd.stdp.constant;

/**
 * @Description: 项目申报流程常量类
 * @Author: zhangYu
 */
public class ProjectDeclarationFlowConstants {

    /**
     * 项目申报流程前缀
     */
    public static final String FLOW_PRO_PREFIX = "PRO_PREFIX:";

    /**
     * 发起申请(项目填报)
     */
    public static final String APPLY_TASK_CODE = "principalTask";
    public static final String APPLY_TASK_NAME = "项目填报";
    public static final String PRINCIPAL_TASK_ROLE = "ORG_HEAD";


    /**
     *单位审核
     *
     */
    public static final String ORG_TASK_CODE = "organizationTask";
    public static final String ORG_TASK_NAME = "单位审核";
    public static final String ORG_TASK_ROLE = "ORG_ADMIN";

    /**
     *县级经办人审核
     *
     */
    public static final String COUNTY_CHECK_TASK_CODE = "countyCheckTask";
    public static final String COUNTY_CHECK_TASK_NAME = "县级经办人审核";
    public static final String COUNTY_CHECK_TASK_ROLE = "COUNTY_CHECK_CODE";

    /**
     *县级分管领导审批
     *
     */
    public static final String COUNTY_APPROVAL_TASK_CODE = "countyApprovalTask";
    public static final String COUNTY_APPROVAL_TASK_NAME = "县级分管领导审批";
    public static final String COUNTY_APPROVAL_TASK_ROLE = "COUNTY_APPROVAL_CODE";

    /**
     *市级经办人审核
     *
     */
    public static final String CITY_CHECK_TASK_CODE = "cityCheckTask";
    public static final String CITY_CHECK_TASK_NAME = "市级经办人审核";
    public static final String CITY_CHECK_TASK_ROLE = "CITY_CHECK_CODE";

    /**
     *市级分管领导审批
     *
     */
    public static final String CITY_APPROVAL_TASK_CODE = "cityApprovalTask";
    public static final String CITY_APPROVAL_TASK_NAME = "市级分管领导审批";
    public static final String CITY_APPROVAL_TASK_ROLE = "CITY_APPROVAL_CODE";

    /**
     *省局经办人审核
     *
     */
    public static final String PROVINCE_CHECK_TASK_CODE = "provinceCheckTask";
    public static final String PROVINCE_CHECK_TASK_NAME = "省局经办人审核";
    public static final String PROVINCE_CHECK_TASK_ROLE = "PROVINCE_CHECK_CODE";

    /**
     *处长审核
     *
     */
    public static final String DIRECTOR_AUDIT_TASK_CODE = "directorAuditTask";
    public static final String DIRECTOR_AUDIT_TASK_NAME = "处长审核";
    public static final String DIRECTOR_AUDIT_TASK_ROLE = "DIRECTOR_HEAD_CODE";

    /**
     *选择专家
     *
     */
    public static final String CHOOSE_EXPERT_TASK_CODE = "chooseExpertTask";
    public static final String CHOOSE_EXPERT_TASK_NAME = "选择专家";


    /**
     *专家评审
     *
     */
    public static final String EXPERT_REVIEW_TASK_CODE = "expertReviewTask";
    public static final String EXPERT_REVIEW_TASK_NAME = "专家评审";
    public static final String EXPERT_REVIEW_TASK_ROLE = "EXPERT";

    /**
     *处室评议
     *
     */
    public static final String DEPARTMENT_COMMENT_TASK_CODE = "departmentCommentTask";
    public static final String DEPARTMENT_COMMENT_TASK_NAME = "处室评议";


    /**
     *省局分管领导审批
     *
     */
    public static final String PROVINCE_APPROVAL_TASK_CODE = "provinceApprovalTask";
    public static final String PROVINCE_APPROVAL_TASK_NAME = "省局分管领导审批";
    public static final String PROVINCE_APPROVAL_TASK_ROLE = "PROVINCE_APPROVAL_CODE";

    /**
     * 不通过
     *
     */
    public static final String NOT_END = "NOT_END";
    public static final String NOT_END_NAME = "不通过";

    /**
     * 结束
     *
     */
    public static final String END = "END";
    public static final String END_NAME = "结束";

}
