package com.fd.stdp.constant;

/**
 * 常量类
 * 
 * @date 2019年12月2日
 */
public class Constant {

	 /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";
	/**
	 * 通用标记 YES
	 */
	public static final Integer FLAG_YES = 1;

	/**
	 * 通用标记 NO
	 */
	public static final Integer FLAG_NO = 0;

	/**
	 * 专利类型 外观设计
	 */
	public static final String PATENT_TYPE_APPDE = "PATENT_TYPE_APPDE";

	/**
	 * 专利质量评价材料
	 */
	public static final String PATENT_FILE_TYPE_QUALITY_NAME = "专利质量评价材料";

	/**
	 * 专利质量评价材料
	 */
	public static final String PATENT_FILE_TYPE_QUALITY_CODE = "FILE_TYPE_QUALITY";

	/**
	 * 设计水平评价材料
	 */
	public static final String PATENT_FILE_TYPE_DESIGN_LEVEL_CODE = "FILE_TYPE_DESIGN_LEVEL";

	/**
	 * 设计水平评价材料
	 */
	public static final String PATENT_FILE_TYPE_DESIGN_LEVEL_NAME = "设计水平评价材料";
	/**
	 * 运用效益评价材料（一）
	 */
	public static final String PATENT_FILE_TYPE_UTIL_BENEFIT_NAME = "运用效益评价材料（一）";
	/**
	 * 运用效益评价材料（一）
	 */
	public static final String PATENT_FILE_TYPE_UTIL_BENEFIT_CODE = "FILE_TYPE_UTIL_BENEFIT";

	/**
	 * 专利许可情况材料
	 */
	public static final String PATENT_FILE_TYPE_PATENT_LICENSE_NAME = "专利许可情况材料";
	/**
	 * 专利许可情况材料
	 */
	public static final String PATENT_FILE_TYPE_PATENT_LICENSE_CODE = "FILE_TYPE_PATENT_LICENSE";

	/**
	 * 专利质押融资情况材料
	 */
	public static final String PATENT_FILE_TYPE_PATENT_FINANCING_NAME = "专利质押融资情况材料";
	/**
	 * 专利质押融资情况材料
	 */
	public static final String PATENT_FILE_TYPE_PATENT_FINANCING_CODE = "FILE_TYPE_PATENT_FINANCING";

	/**
	 * 专利出资情况材料
	 */
	public static final String PATENT_FILE_TYPE_PATENT_CONTRIBUTION_NAME = "专利出资情况材料";
	/**
	 * 专利出资情况材料
	 */
	public static final String PATENT_FILE_TYPE_PATENT_CONTRIBUTION_CODE = "FILE_TYPE_PATENT_CONTRIBUTION";

	/**
	 * 保护管理水平评价材料
	 */
	public static final String PATENT_FILE_TYPE_PROTECT_MANAGE_LEVEL_NAME = "保护管理水平评价材料";

	/**
	 * 保护管理水平评价材料
	 */
	public static final String PATENT_FILE_TYPE_PROTECT_MANAGE_LEVEL_CODE = "FILE_TYPE_PROTECT_MANAGE_LEVEL";

	/**
	 * 推荐意见书（推荐单位审核上传）
	 */
	public static final String PATENT_FILE_TYPE_RECOMMEND_LETTER_CODE = "FILE_TYPE_RECOMMEND_LETTER";
	/**
	 * 推荐意见书（推荐单位审核上传）
	 */
	public static final String PATENT_FILE_TYPE_RECOMMEND_LETTER_NAME = "推荐意见书（推荐单位审核上传）";

	/**
	 * 推荐公示文件（所在单位审核上传）
	 */
	public static final String PATENT_FILE_TYPE_RECOMMEND_PUBLICITY_CODE = "FILE_TYPE_RECOMMEND_PUBLICITY";

	/**
	 * 推荐公示文件（所在单位审核上传）
	 */
	public static final String PATENT_FILE_TYPE_RECOMMEND_PUBLICITY_NAME = "推荐公示文件（所在单位审核上传）";

	/**
	 * 专利证书及授权文本（或经无效程序重新确认后的专利文本）
	 */
	public static final String PATENT_FILE_TYPE_AUTH_TXT_CODE = "FILE_TYPE_AUTH_TXT";
	/**
	 * 专利证书及授权文本（或经无效程序重新确认后的专利文本）
	 */
	public static final String PATENT_FILE_TYPE_AUTH_TXT_NAME = "专利证书及授权文本（或经无效程序重新确认后的专利文本）";

	/**
	 * 专利权人身份证明（营业执照、事业单位法人证书或个人身份证）
	 */
	public static final String PATENT_FILE_TYPE_IDENTITY_CERT_CODE = "FILE_TYPE_IDENTITY_CERT";
	/**
	 * 专利权人身份证明（营业执照、事业单位法人证书或个人身份证）
	 */
	public static final String PATENT_FILE_TYPE_IDENTITY_CERT_NAME = "专利权人身份证明（营业执照、事业单位法人证书或个人身份证）";

	/**
	 * 获奖证书、图片、照片
	 */
	public static final String PATENT_FILE_TYPE_AWARD_CERT_CODE = "FILE_TYPE_AWARD_CERT";

	/**
	 * 获奖证书、图片、照片
	 */
	public static final String PATENT_FILE_TYPE_AWARD_CERT_NAME = "获奖证书、图片、照片";

	/**
	 * 项目应用证明
	 */
	public static final String PATENT_FILE_TYPE_PROJECT_CERT_CODE = "FILE_TYPE_PROJECT_CERT";

	/**
	 * 项目应用证明
	 */
	public static final String PATENT_FILE_TYPE_PROJECT_CERT_NAME = "项目应用证明";

	/**
	 * 专利权评价或专利检索报告
	 */
	public static final String PATENT_FILE_TYPE_EVALUATE_CODE = "FILE_TYPE_EVALUATE";
	/**
	 * 专利权评价或专利检索报告
	 */
	public static final String PATENT_FILE_TYPE_EVALUATE_NAME = "专利权评价或专利检索报告";

	/**
	 * 其他证明
	 */
	public static final String PATENT_FILE_TYPE_OTHER_CERT_CODE = "FILE_TYPE_OTHER_CERT";
	/**
	 * 其他证明
	 */
	public static final String PATENT_FILE_TYPE_OTHER_CERT_NAME = "其他证明";

	/**
	 * 专利优先审查功能模块:专利优先审查请求书
	 */
	public static final String FILE_TYPE_PRIORITY_REQUEST_CODE = "FILE_TYPE_PRIORITY_REQUEST";
	/**
	 * 专利优先审查请求书name
	 */
	public static final String FILE_TYPE_PRIORITY_REQUEST_NAME = "专利优先审查请求书";

	/**
	 * 专利优先审查功能模块:专利阶段相关凭证
	 */
	public static final String FILE_TYPE_PATENT_RELATED_CODE = "FILE_TYPE_PATENT_RELATED";
	/**
	 * 专利阶段相关凭证name
	 */
	public static final String FILE_TYPE_PATENT_RELATED_NAME = "专利阶段相关凭证";

	/**
	 * 专利优先审查功能模块: 申请人相关证明材料
	 */
	public static final String FILE_TYPE_APPLICAN_RELATED_CODE = "FILE_TYPE_APPLICAN_RELATED";
	/**
	 * 申请人相关证明材料name
	 */
	public static final String FILE_TYPE_APPLICAN_RELATED_NAME = "申请人相关证明材料";

	/**
	 * 专利优先审查: 第三方委托书
	 */
	public static final String FILE_TYPE_THIRD_PARTY_CODE = "FILE_TYPE_THIRD_PARTY";
	/**
	 * 第三方委托书name
	 */
	public static final String FILE_TYPE_THIRD_PARTY_NAME = "第三方委托书";
	/**
	 * 专利优先审查功能模块:现有技术或者现有设计信息材料
	 */
	public static final String FILE_TYPE_EXIST_TECHNOLOGY_CODE = "FILE_TYPE_EXIST_TECHNOLOGY";
	/**
	 * 现有技术或者现有设计信息材料name
	 */
	public static final String FILE_TYPE_EXIST_TECHNOLOGY_NAME = "现有技术或者现有设计信息材料";

	/**
	 * 专利优先审查功能模块:相关证明文件
	 */
	public static final String FILE_TYPE_RELATED_PROOF_CODE = "FILE_TYPE_RELATED_PROOF";
	/**
	 * 相关证明文件name
	 */
	public static final String FILE_TYPE_RELATED_PROOF_NAME = "相关证明文件";

	/**
	 * 专利优先审查功能模块：其他证明
	 */
	public static final String FILE_TYPE_OTHER_CODE = "FILE_TYPE_OTHER";
	/**
	 * 专利优先审查功能模块：其他证明 name
	 */
	public static final String FILE_TYPE_OTHER_NAME = "相关证明文件";

	/**
	 * 专利优先审查功能模块：非专利文献 code
	 */
	public static final String FILE_TYPE_NON_PATENT_CODE = "FILE_TYPE_NON_PATENT_DOCUMENT";
	/**
	 * 专利优先审查功能模块：非专利文献 name
	 */
	public static final String FILE_TYPE_NON_PATENT_NAME = "非专利文献";

	/**
	 * 类型CODE（项目负责人）
	 */
	public static final String PROJECT_MEMBER_TYPE_PROJECT_MAIN_CODE = "MEMBER_PROJECT_MAIN";

	/**
	 * 类型CODE（立项阶段叫研究人员；合同、验收、公示叫项目组成员）
	 */
	public static final String PROJECT_MEMBER_TYPE_PROJECT_RESEARCH_CODE = "MEMBER_PROJECT_RESEARCH";

	/**
	 * 类型CODE(牵头单位)
	 */
	public static final String PROJECT_COMPANY_TYPE_LEAD_CODE = "LEAD_COMPANY";

	/**
	 * 类型CODE(丙方)
	 */
	public static final String PROJECT_COMPANY_TYPE_THREE_CODE = "THREE_COMPANY";

	/**
	 * 类型CODE(参加单位)
	 */
	public static final String PROJECT_COMPANY_TYPE_JOIN_CODE = "JOIN_COMPANY";

	/**
	 * 类型CODE(产业规划类导航项目)
	 */
	public static final String PROJECT_NAVIGATION_TYPE_INDUSTRIAL_PLANNING_CODE = "INDUSTRIAL_PLANNING";

	/**
	 * 类型CODE(企业运营类导航项目)
	 */
	public static final String PROJECT_NAVIGATION_TYPE_ENTERPRISE_OPERATION_CODE = "ENTERPRISE_OPERATION";

	/**
<<<<<<< HEAD
	 * 预警项目 类型CODE(产业规划类导航项目)
=======
	 * 类型CODE(浙江省专利导航项目)
	 */
	public static final String PROJECT_NAVIGATION_TYPE_PATENT_NAVIGATION = "PATENT_NAVIGATION";

	/**
	 * 战略项目 类型CODE(产业规划类导航项目)
>>>>>>> aedee7fe031b740335a35ba681e772fc95cd22bb
	 */
	public static final String STRATEGIC_PROJECT_INDUSTRIAL_PLANNING_CODE = "INDUSTRIAL_PLANNING";

	/**
	 * 预警项目类型CODE(企业运营类导航项目)
	 */
	public static final String STRATEGIC_PROJECT__ENTERPRISE_OPERATION_CODE = "ENTERPRISE_OPERATION";

	/**
	 * 预警项目(牵头单位)
	 */
	public static final String STRATEGIC_COMPANY_TYPE_LEAD_CODE = "LEAD_COMPANY";

	/**
	 * 预警项目(参加单位)
	 */
	public static final String STRATEGIC_COMPANY_TYPE_JOIN_CODE = "JOIN_COMPANY";

	/**
	 * 预警项目（项目负责人）
	 */
	public static final String STRATEGIC_MEMBER_PROJECT_MAIN_CODE = "MEMBER_PROJECT_MAIN";

	/**
	 * 预警项目（主要研究人员）
	 */
	public static final String STRATEGIC_MEMBER_PROJECT_RESEARCH_CODE = "MEMBER_PROJECT_RESEARCH";
	/**
	 * 复审/无效宣告申请中优先请求理由1
	 */
	public static final String REVIEW_APPLY_PRIORITY_REASON_ONE = "涉及节能环保、新一代信息技术、生物、高端装备制造、新能源、新材料、新能源汽车、智能制造等国家重点发展产业。";
	/**
	 * 复审/无效宣告申请中优先请求理由2
	 */
	public static final String REVIEW_APPLY_PRIORITY_REASON_TWO = "涉及各省级和设区的市级人民政府重点鼓励的产业。";

	/**
	 * 复审/无效宣告申请中优先请求理由3
	 */
	public static final String REVIEW_APPLY_PRIORITY_REASON_THREE = "涉及互联网、大数据、云计算等领域且技术或者产品更新速度快。";
	/**
	 * 复审/无效宣告申请中优先请求理由4
	 */
	public static final String REVIEW_APPLY_PRIORITY_REASON_FOUR = "专利申请人已经做好实施准备或者已经开始实施，或者有证据证明他人正在实施其发明创造。";
	/**
	 * 复审/无效宣告申请中优先请求理由5
	 */
	public static final String REVIEW_APPLY_PRIORITY_REASON_FIVE = "就相同主题首次在中国提出专利申请又向其他国家或地区提出申请的该中国首次申请。";

	/**
	 * 复审/无效宣告申请中优先请求理由6 (这个最后还有其他判断条件，等前台完善完毕在进行处理)
	 */
	public static final String REVIEW_APPLY_PRIORITY_REASON_SIX = "PCT途径，国际申请号";
	/**
	 * 复审/无效宣告申请中优先请求理由7
	 */
	public static final String REVIEW_APPLY_PRIORITY_REASON_SEVEN = "巴黎公约途径";
	/**
	 * 复审/无效宣告申请中优先请求理由8
	 */
	public static final String REVIEW_APPLY_PRIORITY_REASON_EIGHT = "其他对国家利益或者公共利益具有重大意义需要优先审查。";

	/**
	 * 复审/无效宣告申请中优先请求理由10
	 */
	public static final String REVIEW_APPLY_PRIORITY_REASON_TEN = "无效宣告案件涉及的专利发生侵权纠纷，当事人已请求地方知识产权局处理、向人民法院起诉或者请求仲裁调解组织仲裁调解。";
	/**
	 * 复审/无效宣告申请中优先请求理由11
	 */
	public static final String REVIEW_APPLY_PRIORITY_REASON_ELEVEN = "无效宣告案件涉及的专利对国家利益或者公共利益具有重大意义。";
	/**
	 * 复审/无效宣告申请中优先请求理由 CDOE
	 */
	public static final String REASON_OEN = "REASON_ONE";
	public static final String REASON_TWO = "REASON_TWO";
	public static final String REASON_THREE = "REASON_THREE";
	public static final String REASON_FOUR = "REASON_FOUR";
	public static final String REASON_FIVE = "REASON_FIVE";
	public static final String REASON_SIX = "REASON_SIX";
	public static final String REASON_SEVEN = "REASON_SEVEN";
	public static final String REASON_EIGHT = "REASON_EIGHT";
	public static final String REASON_NINE = "REASON_NINE";
	public static final String REASON_TEN = "REASON_TEN";
	public static final String REASON_ELEVEN = "REASON_ELEVEN";

	/**
	 * 导航项目，生成PDF时水印内容
	 */
	public static final String STRATEGIC_NAVIGATION_PROJECT_CONTENT = "浙江省市场监督管理局";

	/**
	 * 复审/无效宣告案件功能模块:专利优先审查请求书
	 */
	public static final String FILE_TYPE_PRIORITY_REQUEST_REVIEW_CODE = "FILE_TYPE_PRIORITY_REVIEW_REQUEST";
	/**
	 * 复审/无效宣告案件请求书name
	 */
	public static final String FILE_TYPE_PRIORITY_REQUEST_REVIEW_NAME = "专利优先审查请求书";

	/**
	 * 复审/无效宣告案件模块:专利阶段相关凭证
	 */
	public static final String FILE_TYPE_PATENT_RELATED_REVIEW_CODE = "FILE_TYPE_PATENT_REVIEW_RELATED";
	/**
	 * 复审/无效宣告案件 阶段相关凭证name
	 */
	public static final String FILE_TYPE_PATENT_RELATED_REVIEW_NAME = "专利阶段相关凭证";

	/**
	 * 复审/无效宣告案件功能模块: 申请人相关证明材料
	 */
	public static final String FILE_TYPE_APPLICAN_RELATED_REVIEW_CODE = "FILE_TYPE_APPLICAN_REVIEW_RELATED";
	/**
	 * 复审/无效宣告案件申请人相关证明材料name
	 */
	public static final String FILE_TYPE_APPLICAN_RELATED_REVIEW_NAME = "申请人相关证明材料";

	/**
	 * 复审/无效宣告案件: 第三方委托书
	 */
	public static final String FILE_TYPE_THIRD_PARTY_REVIEW_CODE = "FILE_TYPE_THIRD_REVIEW_PARTY";
	/**
	 * 复审/无效宣告案件：第三方委托书name
	 */
	public static final String FILE_TYPE_THIRD_PARTY_REVIEW_NAME = "第三方委托书";
	/**
	 * 复审/无效宣告案件功能模块:现有技术或者现有设计信息材料
	 */
	public static final String FILE_TYPE_EXIST_TECHNOLOGY_REVIEW_CODE = "FILE_TYPE_EXIST_REVIEW_TECHNOLOGY";
	/**
	 * 复审/无效宣告案件:现有技术或者现有设计信息材料name
	 */
	public static final String FILE_TYPE_EXIST_TECHNOLOGY_REVIEW_NAME = "现有技术或者现有设计信息材料";

	/**
	 * 复审/无效宣告案件:相关证明文件
	 */
	public static final String FILE_TYPE_RELATED_PROOF_REVIEW_CODE = "FILE_TYPE_RELATED_REVIEW_PROOF";
	/**
	 * 复审/无效宣告案件:相关证明文件name
	 */
	public static final String FILE_TYPE_RELATED_PROOF_REVIEW_NAME = "相关证明文件";

	/**
	 * 复审/无效宣告案件功能模块：其他证明
	 */
	public static final String FILE_TYPE_OTHER_REVIEW_CODE = "FILE_TYPE_REVIEW_OTHER";
	/**
	 * 复审/无效宣告案件功能模块：其他证明 name
	 */
	public static final String FILE_TYPE_OTHER_REVIEW_NAME = "相关证明文件";

	/**
	 * 复审/无效宣告案件 审核通过code
	 */
	public static final String REVIEW_AGREE = "AGREE";
	/**
	 * 复审/无效宣告案件 审核通过
	 */
	public static final String REVIEW_AGREE_VALUE = "审核通过";
	/**
	 * 复审/无效宣告案件 审核退回code
	 */
	public static final String REVIEW_REFUSE = "REFUSE";
	/**
	 * 复审/无效宣告案件 审核退回
	 */
	public static final String REVIEW_REFUSE_VALUE = "审核退回";

	/**
	 * 专利优先审查 审核通过code
	 */
	public static final String PRIORITY_AGREE = "AGREE";
	/**
	 * 专利优先审查 审核通过value
	 */
	public static final String PRIORITY_AGREE_VALUE = "审核通过";
	/**
	 * 专利优先审查 审核退回code
	 */
	public static final String PRIORITY_REFUSE = "REFUSE";
	/**
	 * 专利优先审查 审核退回
	 */
	public static final String PRIORITY_REFUSE_VALUE = "审核退回";

	/**
	 * 专利优先审查 流程节点标识符 u1
	 */
	public static final String PRIORITY_ACTIVITY_U1 = "u1";
	/**
	 * 专利优先审查 流程节点标识符 d
	 */
	public static final String PRIORITY_ACTIVITY_D = "d";
	/**
	 * 专利优先审查 黑名单标识
	 */
	public static final String PRIORITY_BLACK_CODE = "BLACK";
	/**
	 * 专利优先审查 黑名单标识 中文值
	 */
	public static final String PRIORITY_BLACK_VALUE = "黑名单";
	/**
	 * 专利优先审查 白名单标识
	 */
	public static final String PRIORITY_WHITE_CODE = "WHITE";
	/**
	 * 专利优先审查 白名单标识 中文值
	 */
	public static final String PRIORITY_WHITE_VALUE = "白名单";
	/**
	 * 专利优先审查 普通名单标识
	 */
	public static final String PRIORITY_ORDINARY_CODE = "ORDINARY";

	/**
	 * 复审/无效宣告案件 白名单标识
	 */
	public static final String WHITE_CODE = "WHITE";
	/**
	 * 复审/无效宣告案件 黑名单标识
	 */
	public static final String BLACK_CODE = "BLACK";

	/**
	 * sys复审/无效宣告案件 普通用户标code
	 */
	public static final String ORDINARY_CODE = "ORDINARY";
	/**
	 * sys复审/无效宣告案件 普通用户标识Value
	 */
	public static final String ORDINARY_CODE_VALUE = "普通用户";
	/**
	 * 复审/无效宣告案件 流程结束标识符
	 */
	public static final String REVIEW_ACTIVITY_END = "d";
	/**
	 * 复审/无效宣告案件 流程节点名称
	 */
	public static final String REVIEW_STAY = "待";
	/**
	 * 是否已打印标识符：YES:已打印
	 */
	public static final String IS_PRINT_YES = "YES";
	/**
	 * 是否已打印标识符：NO:未打印
	 */
	public static final String IS_PRINT_NO = "NO";
	/**
	 * 是否已打印标识说明值：已打印
	 */
	public static final String IS_PRINT_VALUE_YES = "已打印";
	/**
	 * 是否已打印标识说明值：未打印
	 */
	public static final String IS_PRINT_VALUE_NO = "未打印";

	/**
	 * 复审/无效案件中 是否回寄标识符：YES_BACK代表"已回寄"
	 */
	public static final String REVIEW_SEND_BACK_STATUS_YES = "YES_BACK";
	/**
	 * 复审/无效案件中 是否回寄标识符：NO_BACK代表"未回寄"
	 */
	public static final String REVIEW_SEND_BACK_STATUS_NO = "NO_BACK";
	/**
	 * 复审/无效案件中 已优先CODE
	 */
	public static final String HAVE_PRIORITY_CODE = "FIRST";
	/**
	 * 复审/无效案件 已授权CODE
	 */
	public static final String HAVE_AUTHORIZE_CODE = "AUTH";
	/**
	 * 复审/无效案件中 寄送信息标识符：SEND代表寄送
	 */
	public static final String REVIEW_SEND_MODE_SEND = "SEND";
	/**
	 * 复审/无效案件中 寄送信息标识符：BACK代表回寄
	 */
	public static final String REVIEW_SEND_MODE_BACK = "BACK";
	/**
	 * 专利申请优先审查 当日申请名额数量配置参数
	 */
	public static final String PRIORITY_TO_CONFIGURE = "PRIORITY_APPLY_NUMBER";

	/**
	 * 复审/无效案件 当日申请名额数量配置参数
	 */
	public static final String REVIEW_TO_CONFIGURE = "REVIEW_APPLY_NUMBER";

	/**
	 * 专利优先审查驳回理由CODE ，“MONTH_FULL”代表：1.本月名额已满
	 */
	public static final String PRIORITY_REJECT_REASON_FIRST = "MONTH_FULL";

	/**
	 * 专利优先审查驳回理由CODE ，“QUOTA_FULL”代表：2.名额已满
	 */
	public static final String PRIORITY_REJECT_REASON_SECOND = "QUOTA_FULL";

	/**
	 * 专利优先审查驳回理由CODE ，“APPLY_SAME_DAY”代表：3.同日申请
	 */
	public static final String PRIORITY_REJECT_REASON_THIRD = "APPLY_SAME_DAY";

	/**
	 * 专利优先审查驳回理由CODE ，“SPECIAL_FIELD”代表：4.特殊领域
	 */
	public static final String PRIORITY_REJECT_REASON_FOURTH = "SPECIAL_FIELD";

	/**
	 * 专利优先审查驳回理由CODE ，“SCAN_FILE_NOT_CLEAR”代表：5.扫描件不清晰
	 */
	public static final String PRIORITY_REJECT_REASON_FIFTH = "SCAN_FILE_NOT_CLEAR";

	/**
	 * 专利优先审查驳回理由CODE ，“INCOMPLETE_DOCUMENT”代表：6.文件不齐全
	 */
	public static final String PRIORITY_REJECT_REASON_SIXTH = "INCOMPLETE_DOCUMENT";

	/**
	 * 专利优先审申请处理时限，申请人提交后第二天开始算，7个工作日需处理完成
	 */
	public static final int PRIORITY_WORKDAY = 7;

	/**
	 * 复审/无效优先审申请处理时限，申请人提交后第二天开始算，7个工作日需处理完成
	 */
	public static final int REVIEW_WORKDAY = 7;

	/**
	 * 用户类型 省局处领导 FZY
	 */
	public static final String USER_TYPE_UTA_PBLEADER = "UTA_PBLEADER";

	/**
	 * 用户类型 省局经办人 FZY
	 */
	public static final String USER_TYPE_UTA_PBMANAGER = "ROLE@UTA_PBMANAGER";

	/**
	 * 预警项目，生成PDF时水印内容
	 */
	public static final String STRATEGIC_WATER_CONTENT = "2020年度浙江省知识产权预警分析项目";

	/**
	 * 预警项目，市级审批时查询使用
	 */
	public static final String STRATEGIC_TYPE_CITY = "CITY";

	/**
	 * 预警项目，省局经办人二次审查时查询使用
	 */
	public static final String STRATEGIC_TYPE_SC = "SC";
	/**
	 * 预警项目，经办人上传专家意见文件类型code
	 */
	public static final String STRATEGIC_FILE_TYPE_EXPERT = "FILE_EXPERT_OPINION";

	/**
	 * 预警项目，立项背景证明材料code
	 */
	public static final String FILE_TYPE_PROJECT = "FILE_TYPE_PROJECT";

	/**
	 * 预警项目，研究基础证明材料code
	 */
	public static final String FILE_TYPE_RESARCH = "FILE_TYPE_RESARCH";
	/**
	 * 预警项目，其他证明材料code
	 */
	public static final String FILE_TYPE_STRATEGIC_RECOMMEND_LETTER = "FILE_TYPE_STRATEGIC_RECOMMEND_LETTER";

	/**
	 * 专利优先审查 已优先
	 */
	public static final String PRIORITY_ALREADY_FIRST_VALUE = "已优先";

	/**
	 * 专利优先审查 已授权
	 */
	public static final String PRIORITY_ALREADY_AUTH_VALUE = "已授权";

	/**
	 * 专利优先审查 是否存在同日申请 code 1 代表"是"
	 */
	public static final Integer IS_SAME_DATE = 1;

	/**
	 * 专利优先审查 是否代理申请 code 1 代表"是"
	 */
	public static final Integer AGENT_STATUS_ONE = 1;

	/**
	 * 企业用户
	 */
	public static final String USER_TYPE_UTR_COMPANY = "UTR_COMPANY";

	/**
	 * 专利优先审查 同一个月申请数量
	 */
	public static final int PRIORITY_SAME_MONTH_APPLY_NUM = 2;
	
	/**
	 *    专利优先审查 同一个月内用户申请数量字典标识
	 */
	public static final String PRI_APPLY_NUM = "PRI_APPLY_NUM"; 
	
	/**
	 *    复审/无效优先审查 同一个月内用户申请数量字典标识
	 */
	public static final String REV_APPLY_NUM = "REV_APPLY_NUM";

	/**
	 * 县局考核
	 */
	public static final String INTERGRATE_COUNTY = "县局考核";

	/**
	 * 市局考核
	 */
	public static final String INTERGRATE_CITY = "市局考核";

	/**
	 * 待提交
	 */
	public static final String FLOW_STATUS_DTJ = "待提交";

	/**
	 * 待省局审核
	 */
	public static final String FLOW_STATUS_DPSH = "待省局审核";

	/**
	 * 待市局审核
	 */
	public static final String FLOW_STATUS_DCSH = "待市局审核";

	/**
	 * 审核已完成
	 */
	public static final String FLOW_STATUS_YES = "审核已完成";

	/**
	 * TWO
	 */
	public static final Integer FLAG_TWO = 2;

	/**
	 * THREE
	 */
	public static final Integer FLAG_THREE = 3;

	/**
	 * 已完成
	 */
	public static final String FINISH = "已完成";

	/**
	 * 保存
	 */
	public static final String SAVE = "保存";

	/**
	 * 无
	 */
	public static final String WU = "无";

	/**
	 * 待专家审核
	 */
	public static final String FLOW_STATUS_DZJSH = "待专家审核";

	/**
	 * 0000
	 */
	public static final String ZERO_ZERO_ZERO_ZERO = "0000";

	/**
	 * 待申请单位审核
	 */
	public static final String FLOW_STATUS_DSQDWSH = "待申请单位审核";

	/**
	 * 机构管理员
	 */
	public static final String ORG_ADMIN = "ORG_ADMIN";

	/**
	 * 个人用户类型
	 */
	public static final String ROLE_PERSONAL = "ROLE_PERSONAL";

	/**
	 * 审计处室
	 */
	public static final String PRICINCE_OFFICE = "PRICINCE_OFFICE";
}
