package com.fd.stdp.constant;



/**
 * 系统流程 常量类
 * @Date: 2021-12-10
 * @Author: yu<PERSON><PERSON><PERSON>i
 */
public class AssigneeConstant {

    /**
     * 机构管理员
     */
    public static final String ORG_ADMIN_ROLE = "ORG_ADMIN@ROLE";

    /**
     * 项目负责人
     */
    public static final String ORG_HEAD_ROLE = "ORG_HEAD@ROLE";

    /**
     * 管理员
     */
    public static final String SUPERADMIN_ROLE = "SUPERADMIN@ROLE";

    /**
     * 县级科技主管部门
     */
    public static final String DEPT_COUNTY_ROLE = "DEPT_COUNTY@ROLE";

    /**
     * 市级科技主管部门
     */
    public static final String DEPT_CITY_ROLE = "DEPT_CITY@ROLE";

    /**
     * 省级科技主管部门
     */
    public static final String DEPT_PROVINCE_ROLE = "DEPT_PROVINCE@ROLE";
    /**
     * 省级科技主管部门
     */
    public static final String TEC_ORG_AGENT = "TEC_ORG_AGENT@ROLE";

    /**
     * 专家
     */
    public static final String EXPERT_ROLE = "EXPERT@ROLE";

    /**
     * 个人用户
     */
    public static final String ROLE_PERSONAL_ROLE = "ROLE_PERSONAL@ROLE";

}
