package com.fd.stdp.handler;


import com.fd.stdp.common.RestApiResponse;
import com.fd.stdp.util.IpUtils;
import com.fd.stdp.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Set;

/**
 * @Description 白名单拦截器
 * @Date 2024/1/22 13:58
 * <AUTHOR>
 **/
@Slf4j
public class WhiteInterceptor implements HandlerInterceptor {

    private Set<String> whiteList;

    public WhiteInterceptor(Set<String> whiteList) {
        this.whiteList = whiteList;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String ipAddr = IpUtils.getIpAddr(request);
        log.error("IP{}", ipAddr);
        if (StringUtils.isBlank(ipAddr)) {
            log.error("请求失败,IP获取失败：IP{}");
            RestApiResponse rest = RestApiResponse.error("IP获取失败");
            ResponseUtil.responseJson(response, HttpStatus.OK.value(), rest);
            return false;
        }
        if (CollectionUtils.isEmpty(whiteList)) {
            log.error("请求失败,请设置白名单IP：{}", ipAddr);
            RestApiResponse rest = RestApiResponse.error("请设置白名单");
            ResponseUtil.responseJson(response, HttpStatus.OK.value(), rest);
            return false;
        } else {
            String[] split = ipAddr.split(",");
            for (int i = 0; i < split.length; i++) {
                String s = split[i];
                if (StringUtils.isNotBlank(s)) {
                    if (whiteList.contains(s.trim())) {
                        return true;
                    }
                }
            }
        }
        log.error("请求失败,IP请申请白名单：IP{}", ipAddr);
        RestApiResponse rest = RestApiResponse.error("请申请白名单");
        ResponseUtil.responseJson(response, HttpStatus.OK.value(), rest);
        return false;
    }
}
